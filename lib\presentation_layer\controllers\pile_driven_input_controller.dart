import 'package:riverpod_annotation/riverpod_annotation.dart';

//presentation layer
import '../../domain_layer/column_scheme_data.dart';
import '../../domain_layer/pile_driven_input.dart';
import '../screen/homescreen.dart';

// domain layer

part 'pile_driven_input_controller.g.dart';

@riverpod
class PileDrivenInputController extends _$PileDrivenInputController {
  @override
  FutureOr<PileDrivenInput> build() async {
    // print('Build: Column Scheme Input Global');
    PileDrivenInput pileDrivenInput =
        await ref
            .read(appDatabaseControllerProvider.notifier)
            .queryPileDrivenInput();
    final data1 = ref.watch(columnSchemeDataControllerProvider);
    final data2 = ref.watch(pileDrivenInputGlobalControllerProvider);
    return data1.when(
      data: (colData) async {
        return data2.when(
          data: (inputGlobal) async {
            final selectedColumn = colData.firstWhere(
              (scheme) => scheme.isSelected,
              orElse: () => ColumnSchemeData(),
            );
            
            if (pileDrivenInput.useSelectColLoad) {
              if (inputGlobal.colLoadFactor == 0) {
                inputGlobal = inputGlobal.copyWith(colLoadFactor: 1.0);
                await ref
                    .read(pileDrivenInputGlobalControllerProvider.notifier)
                    .updateTable(colLoadFactor: 1.0);
              }
              pileDrivenInput = pileDrivenInput.copyWith(
                slsLoad: selectedColumn.slsLoad / inputGlobal.colLoadFactor,
                ulsLoad: selectedColumn.ulsLoad / inputGlobal.colLoadFactor,
              );
            }
            return pileDrivenInput;
          },
          error: (error, stackTrace) => PileDrivenInput(),
          loading: () => PileDrivenInput(),
        );
      },
      error: (error, stackTrace) => PileDrivenInput(),
      loading: () => PileDrivenInput(),
    );
  }

  Future<void> updateTable({
    double? sptNValue,
    double? fos,
    double? maxPileLength,
    double? slsLoad,
    double? ulsLoad,
    bool? useSelectColLoad,
    String? id,
  }) async {
    final x = await future;
    PileDrivenInput newState = x.copyWith(
      sptNValue: sptNValue ?? x.sptNValue,
      fos: fos ?? x.fos,
      maxPileLength: maxPileLength ?? x.maxPileLength,
      slsLoad: slsLoad ?? x.slsLoad,
      ulsLoad: ulsLoad ?? x.ulsLoad,
      useSelectColLoad: useSelectColLoad ?? x.useSelectColLoad,
      id: id ?? x.id,
    );
    // Update the state only if there are changes
    state = AsyncData(newState);
  }
}
