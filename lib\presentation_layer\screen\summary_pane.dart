import 'dart:convert';

import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';

//below for printing
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:printing/printing.dart';
import 'package:structify/domain_layer/beam_scheme_data.dart';
import 'package:structify/domain_layer/column_scheme_data.dart';
import 'package:structify/domain_layer/slab_scheme_data.dart';
import 'package:structify/presentation_layer/small_elements/chart/chart_transfer_beam_scheme.dart';
import 'package:structify/presentation_layer/small_elements/sketch/draw_column_scheme_circle.dart';
import 'package:structify/presentation_layer/small_elements/sketch/draw_column_scheme_square.dart';
import 'package:structify/presentation_layer/small_elements/sketch/draw_footing_scheme_sketch.dart';
import 'package:structify/presentation_layer/small_elements/sketch/draw_pile_driven_scheme.dart';
import 'package:structify/presentation_layer/small_elements/sketch/draw_slab_scheme_sketch.dart';

// import 'package:flutter/services.dart';

//domain layer
import '../../domain_layer/data_struct/str_force_struct.dart';
import '../../domain_layer/footing_scheme_data.dart';
import '../../domain_layer/pile_driven_data.dart';
import '../../domain_layer/pile_end_bearing_bored_data.dart';
import '../../domain_layer/pile_frictional_bored_data.dart';
import '../../domain_layer/pile_socketed_data.dart';
import '../../domain_layer/preferences.dart';

// presentation layer
// import '../presentation_layer/homescreen.dart';
import '../../misc/custom_func.dart';
import '../small_elements/sketch/draw_beam_scheme_sketch.dart';
import '../small_elements/sketch/draw_footing_scheme_plan_sketch.dart';
import '../small_elements/sketch/draw_pile_end_bearing_bored_scheme.dart';
import '../small_elements/sketch/draw_pile_fricitonal_bored_scheme.dart';
import '../small_elements/sketch/draw_pile_socketed_scheme.dart';
import '../small_elements/sketch/draw_transfer_beam_loading.dart';
import 'homescreen.dart';
// import '../small_elements/custom_stateful_double_input.dart';
// import '../small_elements/custom_stateful_text_input.dart';

class SummaryPane extends ConsumerWidget {
  SummaryPane({super.key});

  final GlobalKey _globalKey = GlobalKey();
  final ScrollController _scrollController = ScrollController();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final ColorScheme colorScheme = Theme.of(context).colorScheme;
    final TextTheme textTheme = Theme.of(context).textTheme;

    late final SlabSchemeData selectedSlabScheme;
    late final int selectedSlabSchemeIndex;
    late final BeamSchemeData selectedBeamScheme;
    late final int selectedBeamSchemeIndex;
    late final ColumnSchemeData selectedColumnScheme;
    late final int selectedColumnSchemeIndex;
    late final PileFrictionalBoredData selectedPileFrictionalBoredScheme;
    late final PileEndBearingBoredData selectedPileEndBearingBoredScheme;
    late final PileSocketedData selectedPileSocketedScheme;
    late final int selectedPileFrictionalBoredSchemeIndex;
    late final int selectedPileEndBearingBoredSchemeIndex;
    late final PileDrivenData selectedPileDrivenScheme;
    late final int selectedPileDrivenSchemeIndex;
    late final FootingSchemeData selectedFootingScheme;
    late final int selectedFootingSchemeIndex;
    late final double sdlBeam, sdlSlab;
    late final double llBeam, llSlab;
    late final double ulsLoad, capSquare, capCircle;
    late final String columnSqaureRebar, columnCircleRebar;
    late List<String> unit;

    final int containerOpacity = 175;
    final int textOpacity = 225;

    final titleIdBgColor = colorScheme.secondaryContainer.withAlpha(
      containerOpacity,
    );
    final titleIdTextStyle = textTheme.labelLarge!.copyWith(
      color: colorScheme.onSecondaryContainer.withAlpha(textOpacity),
    );

    final data1 = ref.watch(globalDataControllerProvider);
    final data2 = ref.watch(loadingTablesControllerProvider);
    final data3 = ref.watch(slabSchemeDataControllerProvider);
    final data4 = ref.watch(beamSchemeDataControllerProvider);
    final data5 = ref.watch(columnSchemeDataControllerProvider);
    final data6 = ref.watch(transferBeamSchemeDataControllerProvider);
    final data7 = ref.watch(transferBeamSchemeInputGlobalControllerProvider);
    final data8 = ref.watch(pileFrictionalBoredDataControllerProvider);
    final data9 = ref.watch(pileSocketedDataControllerProvider);
    final data10 = ref.watch(pileDrivenDataControllerProvider);
    final data11 = ref.watch(footingSchemeDataControllerProvider);
    final data12 = ref.watch(pileEndBearingBoredDataControllerProvider);
    ref.watch(appWatcherProvider);

    return data1.when(
      data: (globalData) {
        return data2.when(
          data: (loadingTables) {
            return data3.when(
              data: (slabSchemeData) {
                return data4.when(
                  data: (beamSchemeData) {
                    return data5.when(
                      data: (columnSchemeData) {
                        return data6.when(
                          data: (transferBeamSchemeData) {
                            return data7.when(
                              data: (transferBeamSchemeInputGlobal) {
                                return data8.when(
                                  data: (pileFrictionalBoredData) {
                                    return data9.when(
                                      data: (pileSocketedData) {
                                        return data10.when(
                                          data: (pileDrivenData) {
                                            return data11.when(
                                              data: (footingSchemeData) {
                                                return data12.when(
                                                  data: (
                                                    pileEndBearingBoredData,
                                                  ) {
                                                    // todo: get selected slab scheme
                                                    try {
                                                      selectedSlabScheme =
                                                          slabSchemeData.firstWhere(
                                                            (scheme) =>
                                                                scheme
                                                                    .isSelected,
                                                          );
                                                      selectedSlabSchemeIndex =
                                                          slabSchemeData.indexOf(
                                                            selectedSlabScheme,
                                                          );

                                                      sdlSlab =
                                                          globalData
                                                                  .finishUnitWeight *
                                                              selectedSlabScheme
                                                                  .finish /
                                                              1000 +
                                                          selectedSlabScheme
                                                              .service;
                                                      llSlab =
                                                          selectedSlabScheme
                                                              .liveLoad;
                                                    } catch (e) {
                                                      selectedSlabScheme =
                                                          SlabSchemeData(
                                                            calsLog: 'Error',
                                                          );
                                                      selectedSlabSchemeIndex =
                                                          -1;
                                                      sdlSlab = 0;
                                                      llSlab = 0;
                                                    }

                                                    // todo: get selected beam scheme
                                                    try {
                                                      selectedBeamScheme =
                                                          beamSchemeData.firstWhere(
                                                            (scheme) =>
                                                                scheme
                                                                    .isSelected,
                                                          );
                                                      selectedBeamSchemeIndex =
                                                          beamSchemeData.indexOf(
                                                            selectedBeamScheme,
                                                          );
                                                      sdlBeam =
                                                          globalData
                                                                  .finishUnitWeight *
                                                              selectedBeamScheme
                                                                  .finish /
                                                              1000 +
                                                          selectedBeamScheme
                                                              .service;
                                                      llBeam =
                                                          selectedBeamScheme
                                                              .liveLoad;
                                                    } catch (e) {
                                                      selectedBeamScheme =
                                                          BeamSchemeData(
                                                            calsLog: 'Error',
                                                          );
                                                      selectedBeamSchemeIndex =
                                                          -1;
                                                      sdlBeam = 0;
                                                      llBeam = 0;
                                                    }

                                                    // todo: get selected column scheme
                                                    try {
                                                      selectedColumnScheme =
                                                          columnSchemeData
                                                              .firstWhere(
                                                                (scheme) =>
                                                                    scheme
                                                                        .isSelected,
                                                              );
                                                      selectedColumnSchemeIndex =
                                                          columnSchemeData.indexOf(
                                                            selectedColumnScheme,
                                                          );
                                                      ulsLoad =
                                                          selectedColumnScheme
                                                              .ulsLoad;
                                                      capSquare =
                                                          selectedColumnScheme
                                                              .axialCapacitySquare;
                                                      capCircle =
                                                          selectedColumnScheme
                                                              .axialCapacityCircle;
                                                      columnSqaureRebar =
                                                          selectedColumnScheme
                                                              .mainBarSquare;
                                                      columnCircleRebar =
                                                          selectedColumnScheme
                                                              .mainBarCircle;
                                                    } catch (e) {
                                                      selectedColumnScheme =
                                                          ColumnSchemeData(
                                                            calsLog:
                                                                'No RC Column Design Selected',
                                                          );
                                                      selectedColumnSchemeIndex =
                                                          -1;
                                                      ulsLoad = -1;
                                                      capSquare = -1;
                                                      capCircle = -1;
                                                      columnSqaureRebar =
                                                          'No RC Column Design Selected';
                                                      columnCircleRebar =
                                                          'No RC Column Design Selected';
                                                    }
                                                    // todo: get current transfer beam scheme
                                                    final List<FlSpot> beamMd =
                                                        [];
                                                    final List<FlSpot> beamVd =
                                                        [];
                                                    final Map<String, dynamic>
                                                    y = jsonDecode(
                                                      transferBeamSchemeData
                                                          .beamForce,
                                                    );
                                                    y.map((key, value) {
                                                      y[key] =
                                                          StrForce.fromJson(
                                                            value,
                                                          );
                                                      return MapEntry(
                                                        key,
                                                        value,
                                                      );
                                                    });

                                                    for (StrForce beamForce
                                                        in y.values) {
                                                      beamMd.add(
                                                        FlSpot(
                                                          roundTo(
                                                            beamForce.x,
                                                            1,
                                                          ),
                                                          roundTo(
                                                            beamForce.Md,
                                                            0,
                                                          ),
                                                        ),
                                                      );
                                                      beamVd.add(
                                                        FlSpot(
                                                          roundTo(
                                                            beamForce.x,
                                                            1,
                                                          ),
                                                          roundTo(
                                                            beamForce.Vd,
                                                            0,
                                                          ),
                                                        ),
                                                      );
                                                    }

                                                    switch (globalData.unit) {
                                                      case 'metrics':
                                                        unit =
                                                            PreferredUnit
                                                                .metrics;
                                                        break;
                                                      case 'imperial':
                                                        unit =
                                                            PreferredUnit
                                                                .imperial;
                                                        break;
                                                      default:
                                                        unit =
                                                            PreferredUnit
                                                                .metrics;
                                                    }
                                                    // todo: get selected end bearing bored pile scheme
                                                    try {
                                                      selectedPileEndBearingBoredScheme =
                                                          pileEndBearingBoredData
                                                              .firstWhere(
                                                                (scheme) =>
                                                                    scheme
                                                                        .isSelected,
                                                              );
                                                      selectedPileEndBearingBoredSchemeIndex =
                                                          pileEndBearingBoredData
                                                              .indexOf(
                                                                selectedPileEndBearingBoredScheme,
                                                              );
                                                    } catch (e) {
                                                      selectedPileEndBearingBoredScheme =
                                                          PileEndBearingBoredData(
                                                            calsLog:
                                                                'No Pile Design Selected',
                                                          );
                                                      selectedPileEndBearingBoredSchemeIndex =
                                                          -1;
                                                    }

                                                    // todo: get selected frictional bored pile scheme
                                                    try {
                                                      selectedPileFrictionalBoredScheme =
                                                          pileFrictionalBoredData
                                                              .firstWhere(
                                                                (scheme) =>
                                                                    scheme
                                                                        .isSelected,
                                                              );
                                                      selectedPileFrictionalBoredSchemeIndex =
                                                          pileFrictionalBoredData
                                                              .indexOf(
                                                                selectedPileFrictionalBoredScheme,
                                                              );
                                                    } catch (e) {
                                                      selectedPileFrictionalBoredScheme =
                                                          PileFrictionalBoredData(
                                                            calsLog:
                                                                'No Pile Design Selected',
                                                          );
                                                      selectedPileFrictionalBoredSchemeIndex =
                                                          -1;
                                                    }

                                                    // todo: get selected socketed pile scheme
                                                    selectedPileSocketedScheme =
                                                        pileSocketedData;

                                                    // todo: get selected driven pile scheme
                                                    try {
                                                      selectedPileDrivenScheme =
                                                          pileDrivenData.firstWhere(
                                                            (scheme) =>
                                                                scheme
                                                                    .isSelected,
                                                          );
                                                      selectedPileDrivenSchemeIndex =
                                                          pileDrivenData.indexOf(
                                                            selectedPileDrivenScheme,
                                                          );
                                                    } catch (e) {
                                                      selectedPileDrivenScheme =
                                                          PileDrivenData(
                                                            calsLog:
                                                                'No Pile Design Selected',
                                                          );
                                                      selectedPileDrivenSchemeIndex =
                                                          -1;
                                                    }

                                                    // todo: get selected footing scheme
                                                    try {
                                                      selectedFootingScheme =
                                                          footingSchemeData
                                                              .firstWhere(
                                                                (scheme) =>
                                                                    scheme
                                                                        .isSelected,
                                                              );
                                                      selectedFootingSchemeIndex =
                                                          footingSchemeData.indexOf(
                                                            selectedFootingScheme,
                                                          );
                                                    } catch (e) {
                                                      selectedFootingScheme =
                                                          FootingSchemeData(
                                                            calsLog:
                                                                'No Footing Design Selected',
                                                          );
                                                      selectedFootingSchemeIndex =
                                                          -1;
                                                    }

                                                    return Scaffold(
                                                      // appBar: AppBar(
                                                      //   title: Text(
                                                      //     'Loading Calculator',
                                                      //     style: textTheme.titleLarge!.copyWith(
                                                      //       color: colorScheme.onPrimary,
                                                      //     ),
                                                      //   ),
                                                      //   backgroundColor: colorScheme.secondaryContainer,
                                                      //   leading: IconButton(
                                                      //     icon: Icon(
                                                      //       Icons.arrow_back,
                                                      //       color: colorScheme.onPrimary,
                                                      //     ), // Customize the icon
                                                      //     onPressed: () {
                                                      //       // Custom action for the back button
                                                      //       Navigator.pop(context); // Go back to the previous screen
                                                      //     },
                                                      //   ),
                                                      //   actions: [
                                                      //     IconButton(
                                                      //       icon: Icon(
                                                      //         Icons.print,
                                                      //         color: colorScheme.onSecondaryContainer,
                                                      //       ),
                                                      //       onPressed: () {
                                                      //         _exportListToPdf(context, ref, rowsPerPage: 14);
                                                      //       },
                                                      //     ),
                                                      //     IconButton(
                                                      //       icon: Icon(
                                                      //         Icons.data_array_outlined,
                                                      //         color: colorScheme.onSecondaryContainer,
                                                      //       ), // Customize the icon
                                                      //       onPressed: () {
                                                      //         final db = AppDatabase(); //This should be a singleton
                                                      //         Navigator.of(context).push(
                                                      //           MaterialPageRoute(builder: (context) => DriftDbViewer(db)),
                                                      //         );
                                                      //       },
                                                      //     ),
                                                      //   ],
                                                      // ),
                                                      backgroundColor:
                                                          colorScheme.surface,
                                                      body: Padding(
                                                        padding:
                                                            const EdgeInsets.all(
                                                              8.0,
                                                            ),
                                                        child: Container(
                                                          decoration: BoxDecoration(
                                                            color: colorScheme
                                                                .secondaryContainer
                                                                .withAlpha(25),
                                                            borderRadius:
                                                                BorderRadius.circular(
                                                                  15.0,
                                                                ),
                                                            border: Border.all(
                                                              color: colorScheme
                                                                  .primary
                                                                  .withAlpha(
                                                                    100,
                                                                  ),
                                                              width: 1.5,
                                                            ),
                                                          ),
                                                          child: Padding(
                                                            padding:
                                                                const EdgeInsets.all(
                                                                  8.0,
                                                                ),
                                                            child: Scrollbar(
                                                              controller:
                                                                  _scrollController,
                                                              thumbVisibility:
                                                                  true,
                                                              thickness: 8.0,
                                                              child: ListView(
                                                                controller:
                                                                    _scrollController,
                                                                children: [
                                                                  // Padding(
                                                                  //   padding: const EdgeInsets.all(8.0),
                                                                  //   child: Row(
                                                                  //     mainAxisAlignment: MainAxisAlignment.end,
                                                                  //     children: [
                                                                  //       FunctionButton(
                                                                  //         labelIcon: Icon(Icons.print_outlined),
                                                                  //         labelText: 'Print',
                                                                  //         onTap: (isPressed) async {
                                                                  //           await _exportListToPdf(
                                                                  //             context,
                                                                  //             ref,
                                                                  //             rowsPerPage: 14,
                                                                  //           );
                                                                  //         },
                                                                  //       ),
                                                                  //       SizedBox(width: 5.0),
                                                                  //       FunctionButton(
                                                                  //         labelIcon: Icon(Icons.data_array_outlined),
                                                                  //         labelText: 'Data',

                                                                  //         onTap: (isPressed) async {
                                                                  //           final db =
                                                                  //               AppDatabase(); //This should be a singleton
                                                                  //           Navigator.of(context).push(
                                                                  //             MaterialPageRoute(
                                                                  //               builder: (context) => DriftDbViewer(db),
                                                                  //             ),
                                                                  //           );
                                                                  //         },
                                                                  //       ),
                                                                  //     ],
                                                                  //   ),
                                                                  // ),
                                                                  Align(
                                                                    alignment:
                                                                        Alignment
                                                                            .center,
                                                                    child: _buildTitle(
                                                                      colorScheme,
                                                                      textTheme,
                                                                      'Summary Pane',
                                                                      containerBgColor:
                                                                          colorScheme
                                                                              .primaryContainer,
                                                                      textColor:
                                                                          colorScheme
                                                                              .onPrimaryContainer,
                                                                      textStyle: textTheme
                                                                          .titleLarge!
                                                                          .copyWith(
                                                                            color:
                                                                                colorScheme.onPrimaryContainer,
                                                                          ),
                                                                      containerOpacity:
                                                                          175,
                                                                      textOpacity:
                                                                          225,
                                                                    ),
                                                                  ),

                                                                  // SizedBox(height: 15.0),
                                                                  Divider(),
                                                                  Align(
                                                                    alignment:
                                                                        Alignment
                                                                            .centerLeft,
                                                                    child: Row(
                                                                      children: [
                                                                        CircleAvatar(
                                                                          backgroundColor:
                                                                              titleIdBgColor,
                                                                          radius:
                                                                              14,
                                                                          child: Padding(
                                                                            padding: const EdgeInsets.all(
                                                                              5.0,
                                                                            ),
                                                                            child: Text(
                                                                              '3',
                                                                              style:
                                                                                  titleIdTextStyle,
                                                                            ),
                                                                          ),
                                                                        ),
                                                                        SizedBox(
                                                                          width:
                                                                              5.0,
                                                                        ),
                                                                        _buildTitle(
                                                                          colorScheme,
                                                                          textTheme,
                                                                          'Selected RC Slab Scheme',
                                                                          // containerBgColor:
                                                                          //     colorScheme.surfaceContainer,
                                                                          // textStyle: textTheme.titleMedium!
                                                                          //     .copyWith(
                                                                          //       color: colorScheme.onSurface,
                                                                          //     ),
                                                                          containerOpacity:
                                                                              containerOpacity,
                                                                          textOpacity:
                                                                              textOpacity,
                                                                        ),
                                                                      ],
                                                                    ),
                                                                  ),
                                                                  SizedBox(
                                                                    height: 5.0,
                                                                  ),
                                                                  DefaultTextStyle(
                                                                    style: textTheme
                                                                        .labelMedium!
                                                                        .copyWith(
                                                                          color:
                                                                              colorScheme.onSurface,
                                                                        ),
                                                                    child: Builder(
                                                                      builder: (
                                                                        context,
                                                                      ) {
                                                                        if (selectedSlabSchemeIndex >=
                                                                            0) {
                                                                          return Row(
                                                                            crossAxisAlignment:
                                                                                CrossAxisAlignment.start,
                                                                            children: [
                                                                              GestureDetector(
                                                                                onTap: () async {
                                                                                  await showDialog(
                                                                                    context:
                                                                                        context,
                                                                                    builder: (
                                                                                      context,
                                                                                    ) {
                                                                                      return Dialog(
                                                                                        backgroundColor:
                                                                                            colorScheme.surfaceContainer,
                                                                                        child: SizedBox(
                                                                                          width:
                                                                                              550,
                                                                                          height:
                                                                                              550,
                                                                                          child: DrawSlabSchemeSketch(
                                                                                            sketchWidth:
                                                                                                500,
                                                                                            sketchHeight:
                                                                                                500,
                                                                                            index:
                                                                                                selectedSlabSchemeIndex,
                                                                                            // fontSize: 9.0,
                                                                                          ),
                                                                                        ),
                                                                                      );
                                                                                    },
                                                                                  );
                                                                                },
                                                                                child: DrawSlabSchemeSketch(
                                                                                  sketchWidth:
                                                                                      150,
                                                                                  sketchHeight:
                                                                                      150,
                                                                                  index:
                                                                                      selectedSlabSchemeIndex,
                                                                                  fontSize:
                                                                                      9.0,
                                                                                ),
                                                                              ),
                                                                              SizedBox(
                                                                                width:
                                                                                    5.0,
                                                                              ),
                                                                              Flexible(
                                                                                child: DefaultTextStyle(
                                                                                  style: textTheme.labelMedium!.copyWith(
                                                                                    color:
                                                                                        colorScheme.onSurface,
                                                                                  ),
                                                                                  child: Column(
                                                                                    crossAxisAlignment:
                                                                                        CrossAxisAlignment.start,
                                                                                    children: [
                                                                                      // Text('Index:'),
                                                                                      Container(
                                                                                        decoration: BoxDecoration(
                                                                                          shape:
                                                                                              BoxShape.rectangle,
                                                                                          borderRadius: BorderRadius.circular(
                                                                                            5.0,
                                                                                          ),
                                                                                          border: Border.all(
                                                                                            color: colorScheme.primary.withAlpha(
                                                                                              150,
                                                                                            ),
                                                                                          ),
                                                                                        ),
                                                                                        child: Padding(
                                                                                          padding: const EdgeInsets.all(
                                                                                            4.0,
                                                                                          ),
                                                                                          child: Text(
                                                                                            'Index',
                                                                                          ),
                                                                                        ),
                                                                                      ),
                                                                                      Text(
                                                                                        'Usage:',
                                                                                      ),
                                                                                      Text(
                                                                                        'SDL [${unit[1]}]: ',
                                                                                      ),
                                                                                      Text(
                                                                                        'LL [${unit[1]}]:',
                                                                                      ),
                                                                                      Text(
                                                                                        'Span [${unit[3]}]:',
                                                                                      ),
                                                                                      Text(
                                                                                        'Depth [${unit[4]}]:',
                                                                                      ),
                                                                                      Text(
                                                                                        'Top Bar(C):',
                                                                                      ),
                                                                                      Text(
                                                                                        'Bottom Bar(T):',
                                                                                      ),
                                                                                      Text(
                                                                                        'Links:',
                                                                                      ),
                                                                                    ],
                                                                                  ),
                                                                                ),
                                                                              ),
                                                                              SizedBox(
                                                                                width:
                                                                                    5.0,
                                                                              ),
                                                                              Flexible(
                                                                                child: DefaultTextStyle(
                                                                                  style: textTheme.bodySmall!.copyWith(
                                                                                    color:
                                                                                        colorScheme.onSurface,
                                                                                  ),
                                                                                  child: Column(
                                                                                    crossAxisAlignment:
                                                                                        CrossAxisAlignment.start,
                                                                                    children: [
                                                                                      Container(
                                                                                        decoration: BoxDecoration(
                                                                                          shape:
                                                                                              BoxShape.circle,
                                                                                          border: Border.all(
                                                                                            color: colorScheme.primary.withAlpha(
                                                                                              150,
                                                                                            ),
                                                                                          ),
                                                                                        ),
                                                                                        child: Padding(
                                                                                          padding: const EdgeInsets.all(
                                                                                            4.0,
                                                                                          ),
                                                                                          child: Text(
                                                                                            '${selectedSlabSchemeIndex + 1}',
                                                                                          ),
                                                                                        ),
                                                                                      ),
                                                                                      Text(
                                                                                        selectedSlabScheme.usage,
                                                                                      ),
                                                                                      Text(
                                                                                        '$sdlSlab',
                                                                                      ),
                                                                                      Text(
                                                                                        '$llSlab',
                                                                                      ),
                                                                                      Text(
                                                                                        NumberFormat(
                                                                                          '0.0',
                                                                                        ).format(
                                                                                          selectedSlabScheme.span,
                                                                                        ),
                                                                                      ),
                                                                                      Text(
                                                                                        NumberFormat(
                                                                                          '0',
                                                                                        ).format(
                                                                                          selectedSlabScheme.strZone,
                                                                                        ),
                                                                                      ),
                                                                                      Text(
                                                                                        selectedSlabScheme.mainTopBar,
                                                                                      ),
                                                                                      Text(
                                                                                        selectedSlabScheme.mainBottomBar,
                                                                                      ),
                                                                                      Text(
                                                                                        selectedSlabScheme.mainLinks,
                                                                                      ),
                                                                                    ],
                                                                                  ),
                                                                                ),
                                                                              ),
                                                                            ],
                                                                          );
                                                                        } else {
                                                                          return Align(
                                                                            alignment:
                                                                                Alignment.topLeft,
                                                                            child: Container(
                                                                              decoration: BoxDecoration(
                                                                                color:
                                                                                    colorScheme.errorContainer,
                                                                                borderRadius: BorderRadius.circular(
                                                                                  5.0,
                                                                                ),
                                                                              ),
                                                                              child: Padding(
                                                                                padding: const EdgeInsets.all(
                                                                                  2.0,
                                                                                ),
                                                                                child: Text(
                                                                                  'Nothing selected',
                                                                                  style: textTheme.titleSmall!.copyWith(
                                                                                    color:
                                                                                        colorScheme.onErrorContainer,
                                                                                  ),
                                                                                ),
                                                                              ),
                                                                            ),
                                                                          );
                                                                        }
                                                                      },
                                                                    ),
                                                                  ),
                                                                  Divider(),
                                                                  Row(
                                                                    children: [
                                                                      CircleAvatar(
                                                                        backgroundColor:
                                                                            titleIdBgColor,
                                                                        radius:
                                                                            14,
                                                                        child: Padding(
                                                                          padding: const EdgeInsets.all(
                                                                            5.0,
                                                                          ),
                                                                          child: Text(
                                                                            '4',
                                                                            style:
                                                                                titleIdTextStyle,
                                                                          ),
                                                                        ),
                                                                      ),

                                                                      SizedBox(
                                                                        width:
                                                                            5.0,
                                                                      ),
                                                                      Align(
                                                                        alignment:
                                                                            Alignment.centerLeft,
                                                                        child: _buildTitle(
                                                                          colorScheme,
                                                                          textTheme,
                                                                          'Selected RC Beam Scheme',
                                                                          // containerBgColor:
                                                                          //     colorScheme.surfaceContainer,
                                                                          // textStyle: textTheme.titleMedium!
                                                                          //     .copyWith(
                                                                          //       color: colorScheme.onSurface,
                                                                          //     ),
                                                                          containerOpacity:
                                                                              175,
                                                                          textOpacity:
                                                                              225,
                                                                        ),
                                                                      ),
                                                                    ],
                                                                  ),
                                                                  SizedBox(
                                                                    height: 5.0,
                                                                  ),

                                                                  DefaultTextStyle(
                                                                    style: textTheme
                                                                        .labelMedium!
                                                                        .copyWith(
                                                                          color:
                                                                              colorScheme.onSurface,
                                                                        ),
                                                                    child: Builder(
                                                                      builder: (
                                                                        context,
                                                                      ) {
                                                                        if (selectedBeamSchemeIndex >=
                                                                            0) {
                                                                          return Row(
                                                                            crossAxisAlignment:
                                                                                CrossAxisAlignment.start,
                                                                            children: [
                                                                              GestureDetector(
                                                                                onTap: () async {
                                                                                  await showDialog(
                                                                                    context:
                                                                                        context,
                                                                                    builder: (
                                                                                      context,
                                                                                    ) {
                                                                                      return Dialog(
                                                                                        backgroundColor:
                                                                                            colorScheme.surfaceContainer,
                                                                                        child: SizedBox(
                                                                                          width:
                                                                                              550,
                                                                                          height:
                                                                                              550,
                                                                                          child: DrawBeamSchemeSketch(
                                                                                            sketchWidth:
                                                                                                500,
                                                                                            sketchHeight:
                                                                                                500,
                                                                                            index:
                                                                                                selectedBeamSchemeIndex,
                                                                                            // fontSize: 9.0,
                                                                                          ),
                                                                                        ),
                                                                                      );
                                                                                    },
                                                                                  );
                                                                                },
                                                                                child: DrawBeamSchemeSketch(
                                                                                  sketchWidth:
                                                                                      150,
                                                                                  sketchHeight:
                                                                                      150,
                                                                                  index:
                                                                                      selectedBeamSchemeIndex,
                                                                                  fontSize:
                                                                                      9.0,
                                                                                ),
                                                                              ),
                                                                              SizedBox(
                                                                                width:
                                                                                    5.0,
                                                                              ),
                                                                              Flexible(
                                                                                child: DefaultTextStyle(
                                                                                  style: textTheme.labelMedium!.copyWith(
                                                                                    color:
                                                                                        colorScheme.onSurface,
                                                                                  ),
                                                                                  child: Column(
                                                                                    crossAxisAlignment:
                                                                                        CrossAxisAlignment.start,
                                                                                    children: [
                                                                                      // Text('Index:'),
                                                                                      Container(
                                                                                        decoration: BoxDecoration(
                                                                                          shape:
                                                                                              BoxShape.rectangle,
                                                                                          borderRadius: BorderRadius.circular(
                                                                                            5.0,
                                                                                          ),
                                                                                          border: Border.all(
                                                                                            color: colorScheme.primary.withAlpha(
                                                                                              150,
                                                                                            ),
                                                                                          ),
                                                                                        ),
                                                                                        child: Padding(
                                                                                          padding: const EdgeInsets.all(
                                                                                            4.0,
                                                                                          ),
                                                                                          child: Text(
                                                                                            'Index',
                                                                                          ),
                                                                                        ),
                                                                                      ),
                                                                                      Text(
                                                                                        'Usage:',
                                                                                      ),
                                                                                      Text(
                                                                                        'SDL [${unit[1]}]: ',
                                                                                      ),
                                                                                      Text(
                                                                                        'LL [${unit[1]}]:',
                                                                                      ),
                                                                                      Text(
                                                                                        'Bays:',
                                                                                      ),
                                                                                      Text(
                                                                                        'Span [${unit[3]}]:',
                                                                                      ),
                                                                                      Text(
                                                                                        'Main Beam [${unit[4]}]:',
                                                                                      ),
                                                                                      Text(
                                                                                        'Sec Beam [${unit[4]}]:',
                                                                                      ),
                                                                                    ],
                                                                                  ),
                                                                                ),
                                                                              ),
                                                                              SizedBox(
                                                                                width:
                                                                                    5.0,
                                                                              ),
                                                                              Flexible(
                                                                                child: DefaultTextStyle(
                                                                                  style: textTheme.bodySmall!.copyWith(
                                                                                    color:
                                                                                        colorScheme.onSurface,
                                                                                  ),
                                                                                  child: Column(
                                                                                    crossAxisAlignment:
                                                                                        CrossAxisAlignment.start,
                                                                                    children: [
                                                                                      // Text(
                                                                                      //   '${selectedBeamSchemeIndex + 1}',
                                                                                      // ),
                                                                                      Container(
                                                                                        decoration: BoxDecoration(
                                                                                          shape:
                                                                                              BoxShape.circle,
                                                                                          border: Border.all(
                                                                                            color: colorScheme.primary.withAlpha(
                                                                                              150,
                                                                                            ),
                                                                                          ),
                                                                                        ),
                                                                                        child: Padding(
                                                                                          padding: const EdgeInsets.all(
                                                                                            4.0,
                                                                                          ),
                                                                                          child: Text(
                                                                                            '${selectedBeamSchemeIndex + 1}',
                                                                                          ),
                                                                                        ),
                                                                                      ),
                                                                                      Text(
                                                                                        selectedBeamScheme.usage,
                                                                                      ),
                                                                                      Text(
                                                                                        '$sdlBeam',
                                                                                      ),
                                                                                      Text(
                                                                                        '$llBeam',
                                                                                      ),
                                                                                      Text(
                                                                                        '${selectedBeamScheme.bays}',
                                                                                      ),
                                                                                      Text(
                                                                                        '${NumberFormat('0.0').format(selectedBeamScheme.shortSpan)}(S)x${NumberFormat('0.0').format(selectedBeamScheme.longSpan)}(L)',
                                                                                      ),
                                                                                      Text(
                                                                                        '${NumberFormat('0').format(selectedBeamScheme.mainWidth)}(W)x${NumberFormat('0').format(selectedBeamScheme.mainStrZone)}(D)',
                                                                                      ),
                                                                                      Text(
                                                                                        '${NumberFormat('0').format(selectedBeamScheme.secWidth)}(W)x${NumberFormat('0').format(selectedBeamScheme.secStrZone)}(D)',
                                                                                      ),
                                                                                    ],
                                                                                  ),
                                                                                ),
                                                                              ),
                                                                            ],
                                                                          );
                                                                        } else {
                                                                          return Align(
                                                                            alignment:
                                                                                Alignment.topLeft,
                                                                            child: Container(
                                                                              decoration: BoxDecoration(
                                                                                color:
                                                                                    colorScheme.errorContainer,
                                                                                borderRadius: BorderRadius.circular(
                                                                                  5.0,
                                                                                ),
                                                                              ),
                                                                              child: Padding(
                                                                                padding: const EdgeInsets.all(
                                                                                  2.0,
                                                                                ),
                                                                                child: Text(
                                                                                  'Nothing selected',
                                                                                  style: textTheme.titleSmall!.copyWith(
                                                                                    color:
                                                                                        colorScheme.onErrorContainer,
                                                                                  ),
                                                                                ),
                                                                              ),
                                                                            ),
                                                                          );
                                                                        }
                                                                      },
                                                                    ),
                                                                  ),

                                                                  Divider(),
                                                                  Row(
                                                                    children: [
                                                                      CircleAvatar(
                                                                        backgroundColor:
                                                                            titleIdBgColor,
                                                                        radius:
                                                                            14,
                                                                        child: Padding(
                                                                          padding: const EdgeInsets.all(
                                                                            5.0,
                                                                          ),
                                                                          child: Text(
                                                                            '5',
                                                                            style:
                                                                                titleIdTextStyle,
                                                                          ),
                                                                        ),
                                                                      ),

                                                                      SizedBox(
                                                                        width:
                                                                            5.0,
                                                                      ),
                                                                      Align(
                                                                        alignment:
                                                                            Alignment.centerLeft,
                                                                        child: _buildTitle(
                                                                          colorScheme,
                                                                          textTheme,
                                                                          'Selected RC Column Scheme',
                                                                          // containerBgColor:
                                                                          //     colorScheme.surfaceContainer,
                                                                          // textStyle: textTheme.titleMedium!
                                                                          //     .copyWith(
                                                                          //       color: colorScheme.onSurface,
                                                                          //     ),
                                                                          containerOpacity:
                                                                              175,
                                                                          textOpacity:
                                                                              225,
                                                                        ),
                                                                      ),
                                                                    ],
                                                                  ),
                                                                  SizedBox(
                                                                    height: 5.0,
                                                                  ),

                                                                  DefaultTextStyle(
                                                                    style: textTheme
                                                                        .labelMedium!
                                                                        .copyWith(
                                                                          color:
                                                                              colorScheme.onSurface,
                                                                        ),
                                                                    child: Builder(
                                                                      builder: (
                                                                        context,
                                                                      ) {
                                                                        if (selectedColumnSchemeIndex >=
                                                                            0) {
                                                                          return Row(
                                                                            crossAxisAlignment:
                                                                                CrossAxisAlignment.start,
                                                                            children: [
                                                                              GestureDetector(
                                                                                onTap: () async {
                                                                                  await showDialog(
                                                                                    context:
                                                                                        context,
                                                                                    builder: (
                                                                                      context,
                                                                                    ) {
                                                                                      return Dialog(
                                                                                        backgroundColor:
                                                                                            colorScheme.surfaceContainer,
                                                                                        child: SizedBox(
                                                                                          width:
                                                                                              550,
                                                                                          height:
                                                                                              550,
                                                                                          child: DrawColumnSchemeSquare(
                                                                                            sketchWidth:
                                                                                                500,
                                                                                            sketchHeight:
                                                                                                500,
                                                                                            index:
                                                                                                selectedColumnSchemeIndex,
                                                                                            // fontSize: 9.0,
                                                                                          ),
                                                                                        ),
                                                                                      );
                                                                                    },
                                                                                  );
                                                                                },
                                                                                child: DrawColumnSchemeSquare(
                                                                                  sketchWidth:
                                                                                      125,
                                                                                  sketchHeight:
                                                                                      125,
                                                                                  index:
                                                                                      selectedColumnSchemeIndex,
                                                                                  fontSize:
                                                                                      9.0,
                                                                                ),
                                                                              ),
                                                                              SizedBox(
                                                                                width:
                                                                                    5.0,
                                                                              ),
                                                                              GestureDetector(
                                                                                onTap: () async {
                                                                                  await showDialog(
                                                                                    context:
                                                                                        context,
                                                                                    builder: (
                                                                                      context,
                                                                                    ) {
                                                                                      return Dialog(
                                                                                        backgroundColor:
                                                                                            colorScheme.surfaceContainer,
                                                                                        child: SizedBox(
                                                                                          width:
                                                                                              550,
                                                                                          height:
                                                                                              550,
                                                                                          child: DrawColumnSchemeCircle(
                                                                                            sketchWidth:
                                                                                                500,
                                                                                            sketchHeight:
                                                                                                500,
                                                                                            index:
                                                                                                selectedColumnSchemeIndex,
                                                                                            // fontSize: 9.0,
                                                                                          ),
                                                                                        ),
                                                                                      );
                                                                                    },
                                                                                  );
                                                                                },
                                                                                child: DrawColumnSchemeCircle(
                                                                                  sketchWidth:
                                                                                      125,
                                                                                  sketchHeight:
                                                                                      125,
                                                                                  index:
                                                                                      selectedColumnSchemeIndex,
                                                                                  fontSize:
                                                                                      9.0,
                                                                                ),
                                                                              ),
                                                                              SizedBox(
                                                                                width:
                                                                                    5.0,
                                                                              ),
                                                                              Flexible(
                                                                                child: DefaultTextStyle(
                                                                                  style: textTheme.labelMedium!.copyWith(
                                                                                    color:
                                                                                        colorScheme.onSurface,
                                                                                  ),
                                                                                  child: Column(
                                                                                    crossAxisAlignment:
                                                                                        CrossAxisAlignment.start,
                                                                                    children: [
                                                                                      // Text('Index:'),
                                                                                      Container(
                                                                                        decoration: BoxDecoration(
                                                                                          shape:
                                                                                              BoxShape.rectangle,
                                                                                          borderRadius: BorderRadius.circular(
                                                                                            5.0,
                                                                                          ),
                                                                                          border: Border.all(
                                                                                            color: colorScheme.primary.withAlpha(
                                                                                              150,
                                                                                            ),
                                                                                          ),
                                                                                        ),
                                                                                        child: Padding(
                                                                                          padding: const EdgeInsets.all(
                                                                                            4.0,
                                                                                          ),
                                                                                          child: Text(
                                                                                            'Index',
                                                                                          ),
                                                                                        ),
                                                                                      ),
                                                                                      Text(
                                                                                        'Size[${unit[4]}]:',
                                                                                      ),
                                                                                      Text(
                                                                                        'fcu[${unit[5]}]:',
                                                                                      ),
                                                                                      Text(
                                                                                        'ULS Load [${unit[0]}]:',
                                                                                      ),
                                                                                      Text(
                                                                                        'Cap (Sq) [${unit[0]}]: ',
                                                                                      ),
                                                                                      Text(
                                                                                        'Cap (C) [${unit[0]}]: ',
                                                                                      ),
                                                                                      Text(
                                                                                        'Rebar (Sq):',
                                                                                      ),
                                                                                      Text(
                                                                                        'Rebar (C):',
                                                                                      ),
                                                                                    ],
                                                                                  ),
                                                                                ),
                                                                              ),
                                                                              SizedBox(
                                                                                width:
                                                                                    5.0,
                                                                              ),
                                                                              Flexible(
                                                                                child: DefaultTextStyle(
                                                                                  style: textTheme.bodySmall!.copyWith(
                                                                                    color:
                                                                                        colorScheme.onSurface,
                                                                                  ),
                                                                                  child: Column(
                                                                                    crossAxisAlignment:
                                                                                        CrossAxisAlignment.start,
                                                                                    children: [
                                                                                      // Text(
                                                                                      //   '${selectedColumnSchemeIndex + 1}',
                                                                                      // ),
                                                                                      Container(
                                                                                        decoration: BoxDecoration(
                                                                                          shape:
                                                                                              BoxShape.circle,
                                                                                          border: Border.all(
                                                                                            color: colorScheme.primary.withAlpha(
                                                                                              150,
                                                                                            ),
                                                                                          ),
                                                                                        ),
                                                                                        child: Padding(
                                                                                          padding: const EdgeInsets.all(
                                                                                            4.0,
                                                                                          ),
                                                                                          child: Text(
                                                                                            '${selectedColumnSchemeIndex + 1}',
                                                                                          ),
                                                                                        ),
                                                                                      ),
                                                                                      Text(
                                                                                        NumberFormat(
                                                                                          '0',
                                                                                        ).format(
                                                                                          selectedColumnScheme.size,
                                                                                        ),
                                                                                      ),
                                                                                      Text(
                                                                                        NumberFormat(
                                                                                          '0',
                                                                                        ).format(
                                                                                          selectedColumnScheme.fcu,
                                                                                        ),
                                                                                      ),
                                                                                      Text(
                                                                                        NumberFormat(
                                                                                          '0',
                                                                                        ).format(
                                                                                          selectedColumnScheme.ulsLoad,
                                                                                        ),
                                                                                      ),
                                                                                      Text(
                                                                                        NumberFormat(
                                                                                          '0',
                                                                                        ).format(
                                                                                          selectedColumnScheme.axialCapacitySquare,
                                                                                        ),
                                                                                      ),
                                                                                      Text(
                                                                                        NumberFormat(
                                                                                          '0',
                                                                                        ).format(
                                                                                          selectedColumnScheme.axialCapacityCircle,
                                                                                        ),
                                                                                      ),
                                                                                      Text(
                                                                                        selectedColumnScheme.mainBarSquare,
                                                                                      ),
                                                                                      Text(
                                                                                        selectedColumnScheme.mainBarCircle,
                                                                                      ),
                                                                                    ],
                                                                                  ),
                                                                                ),
                                                                              ),
                                                                            ],
                                                                          );
                                                                        } else {
                                                                          return Align(
                                                                            alignment:
                                                                                Alignment.topLeft,
                                                                            child: Container(
                                                                              decoration: BoxDecoration(
                                                                                color:
                                                                                    colorScheme.errorContainer,
                                                                                borderRadius: BorderRadius.circular(
                                                                                  5.0,
                                                                                ),
                                                                              ),
                                                                              child: Padding(
                                                                                padding: const EdgeInsets.all(
                                                                                  2.0,
                                                                                ),
                                                                                child: Text(
                                                                                  'Nothing selected',
                                                                                  style: textTheme.titleSmall!.copyWith(
                                                                                    color:
                                                                                        colorScheme.onErrorContainer,
                                                                                  ),
                                                                                ),
                                                                              ),
                                                                            ),
                                                                          );
                                                                        }
                                                                      },
                                                                    ),
                                                                  ),
                                                                  Divider(),
                                                                  Row(
                                                                    children: [
                                                                      CircleAvatar(
                                                                        backgroundColor:
                                                                            titleIdBgColor,
                                                                        radius:
                                                                            14,
                                                                        child: Padding(
                                                                          padding: const EdgeInsets.all(
                                                                            5.0,
                                                                          ),
                                                                          child: Text(
                                                                            '6',
                                                                            style:
                                                                                titleIdTextStyle,
                                                                          ),
                                                                        ),
                                                                      ),

                                                                      SizedBox(
                                                                        width:
                                                                            5.0,
                                                                      ),
                                                                      Align(
                                                                        alignment:
                                                                            Alignment.centerLeft,
                                                                        child: _buildTitle(
                                                                          colorScheme,
                                                                          textTheme,
                                                                          'Transfer RC Beam Scheme',
                                                                          containerOpacity:
                                                                              175,
                                                                          textOpacity:
                                                                              225,
                                                                        ),
                                                                      ),
                                                                    ],
                                                                  ),
                                                                  SizedBox(
                                                                    height: 5.0,
                                                                  ),
                                                                  // ClipRect(
                                                                  //   child:
                                                                  //       ChartTransferBeamScheme(
                                                                  //         Md: beamMd,
                                                                  //         Vd: beamVd,
                                                                  //       ),
                                                                  // ),
                                                                  DefaultTextStyle(
                                                                    style: textTheme
                                                                        .labelMedium!
                                                                        .copyWith(
                                                                          color:
                                                                              colorScheme.onSurface,
                                                                        ),
                                                                    child: GestureDetector(
                                                                      onTap: () async {
                                                                        await showDialog(
                                                                          context:
                                                                              context,
                                                                          builder: (
                                                                            context,
                                                                          ) {
                                                                            return Dialog(
                                                                              backgroundColor:
                                                                                  colorScheme.surfaceContainer,
                                                                              child: SizedBox(
                                                                                width:
                                                                                    550,
                                                                                height:
                                                                                    300,
                                                                                child: DrawTransferBeamLoading(
                                                                                  sketchWidth:
                                                                                      500,
                                                                                  sketchHeight:
                                                                                      250,
                                                                                ),
                                                                              ),
                                                                            );
                                                                          },
                                                                        );
                                                                      },
                                                                      child: LayoutBuilder(
                                                                        builder: (
                                                                          context,
                                                                          constraints,
                                                                        ) {
                                                                          return DrawTransferBeamLoading(
                                                                            sketchWidth:
                                                                                constraints.maxWidth *
                                                                                9 /
                                                                                10,
                                                                            sketchHeight:
                                                                                constraints.maxWidth *
                                                                                3 /
                                                                                10,
                                                                          );
                                                                        },
                                                                      ),
                                                                    ),
                                                                  ),
                                                                  SizedBox(
                                                                    height:
                                                                        10.0,
                                                                  ),
                                                                  DefaultTextStyle(
                                                                    style: textTheme
                                                                        .labelMedium!
                                                                        .copyWith(
                                                                          color:
                                                                              colorScheme.onSurface,
                                                                        ),
                                                                    child: Row(
                                                                      mainAxisAlignment:
                                                                          MainAxisAlignment
                                                                              .center,
                                                                      children: [
                                                                        Column(
                                                                          crossAxisAlignment:
                                                                              CrossAxisAlignment.start,
                                                                          children: [
                                                                            Builder(
                                                                              builder: (
                                                                                context,
                                                                              ) {
                                                                                if (!transferBeamSchemeData.mainTopBar.contains(
                                                                                      'Fail',
                                                                                    ) ||
                                                                                    !transferBeamSchemeData.mainBottomBar.contains(
                                                                                      'Fail',
                                                                                    ) ||
                                                                                    !transferBeamSchemeData.mainLinks.contains(
                                                                                      'Fail',
                                                                                    )) {
                                                                                  return Text(
                                                                                    'STATUS:',
                                                                                    style: textTheme.labelMedium!.copyWith(
                                                                                      color:
                                                                                          Colors.green[700],
                                                                                    ),
                                                                                  );
                                                                                } else {
                                                                                  return Text(
                                                                                    'STATUS:',
                                                                                    style: textTheme.labelMedium!.copyWith(
                                                                                      color:
                                                                                          colorScheme.error,
                                                                                    ),
                                                                                  );
                                                                                }
                                                                              },
                                                                            ),
                                                                            Column(
                                                                              crossAxisAlignment:
                                                                                  CrossAxisAlignment.start,

                                                                              children: [
                                                                                Text(
                                                                                  'Beam Size: ',
                                                                                ),
                                                                                Text(
                                                                                  'Width Limit:',
                                                                                ),
                                                                                Text(
                                                                                  'Preferred k-value:',
                                                                                ),
                                                                                Builder(
                                                                                  builder: (
                                                                                    context,
                                                                                  ) {
                                                                                    if (!transferBeamSchemeData.mainTopBar.contains(
                                                                                      'Fail',
                                                                                    )) {
                                                                                      return Text(
                                                                                        'Top Bar:',
                                                                                      );
                                                                                    } else {
                                                                                      return Text(
                                                                                        'Top Bar:',
                                                                                        style: textTheme.titleSmall!.copyWith(
                                                                                          color:
                                                                                              colorScheme.error,
                                                                                        ),
                                                                                      );
                                                                                    }
                                                                                  },
                                                                                ),
                                                                                Builder(
                                                                                  builder: (
                                                                                    context,
                                                                                  ) {
                                                                                    if (!transferBeamSchemeData.mainBottomBar.contains(
                                                                                      'Fail',
                                                                                    )) {
                                                                                      return Text(
                                                                                        'Bottom Bar:',
                                                                                      );
                                                                                    } else {
                                                                                      return Text(
                                                                                        'Bottom Bar:',
                                                                                        style: textTheme.titleSmall!.copyWith(
                                                                                          color:
                                                                                              colorScheme.error,
                                                                                        ),
                                                                                      );
                                                                                    }
                                                                                  },
                                                                                ),
                                                                                Builder(
                                                                                  builder: (
                                                                                    context,
                                                                                  ) {
                                                                                    if (!transferBeamSchemeData.mainLinks.contains(
                                                                                      'Fail',
                                                                                    )) {
                                                                                      return Text(
                                                                                        'Links:',
                                                                                      );
                                                                                    } else {
                                                                                      return Text(
                                                                                        'Links:',
                                                                                        style: textTheme.titleSmall!.copyWith(
                                                                                          color:
                                                                                              colorScheme.error,
                                                                                        ),
                                                                                      );
                                                                                    }
                                                                                  },
                                                                                ),
                                                                              ],
                                                                            ),
                                                                          ],
                                                                        ),
                                                                        SizedBox(
                                                                          width:
                                                                              10.0,
                                                                        ),
                                                                        Column(
                                                                          crossAxisAlignment:
                                                                              CrossAxisAlignment.start,

                                                                          children: [
                                                                            Builder(
                                                                              builder: (
                                                                                context,
                                                                              ) {
                                                                                if (!transferBeamSchemeData.mainTopBar.contains(
                                                                                      'Fail',
                                                                                    ) ||
                                                                                    !transferBeamSchemeData.mainBottomBar.contains(
                                                                                      'Fail',
                                                                                    ) ||
                                                                                    !transferBeamSchemeData.mainLinks.contains(
                                                                                      'Fail',
                                                                                    )) {
                                                                                  return Text(
                                                                                    'PASS',
                                                                                    style: textTheme.labelMedium!.copyWith(
                                                                                      color:
                                                                                          Colors.green[700],
                                                                                    ),
                                                                                  );
                                                                                } else {
                                                                                  return Text(
                                                                                    'FAIL',
                                                                                    style: textTheme.labelMedium!.copyWith(
                                                                                      color:
                                                                                          colorScheme.error,
                                                                                    ),
                                                                                  );
                                                                                }
                                                                              },
                                                                            ),
                                                                            Column(
                                                                              crossAxisAlignment:
                                                                                  CrossAxisAlignment.start,

                                                                              children: [
                                                                                Text(
                                                                                  '${NumberFormat('0').format(transferBeamSchemeData.mainWidth)}(W)x${NumberFormat('0').format(transferBeamSchemeData.strZone)}(D) [${unit[4]}]',
                                                                                ),
                                                                                Text(
                                                                                  '${transferBeamSchemeInputGlobal.maxWidth} [${unit[4]}]',
                                                                                ),
                                                                                Text(
                                                                                  '${transferBeamSchemeInputGlobal.mainKValue}',
                                                                                ),
                                                                                Builder(
                                                                                  builder: (
                                                                                    context,
                                                                                  ) {
                                                                                    if (!transferBeamSchemeData.mainTopBar.contains(
                                                                                      'Fail',
                                                                                    )) {
                                                                                      return Text(
                                                                                        transferBeamSchemeData.mainTopBar,
                                                                                      );
                                                                                    } else {
                                                                                      return Text(
                                                                                        transferBeamSchemeData.mainTopBar,
                                                                                        style: textTheme.titleSmall!.copyWith(
                                                                                          color:
                                                                                              colorScheme.error,
                                                                                        ),
                                                                                      );
                                                                                    }
                                                                                  },
                                                                                ),
                                                                                Builder(
                                                                                  builder: (
                                                                                    context,
                                                                                  ) {
                                                                                    if (!transferBeamSchemeData.mainBottomBar.contains(
                                                                                      'Fail',
                                                                                    )) {
                                                                                      return Text(
                                                                                        transferBeamSchemeData.mainBottomBar,
                                                                                      );
                                                                                    } else {
                                                                                      return Text(
                                                                                        transferBeamSchemeData.mainBottomBar,
                                                                                        style: textTheme.titleSmall!.copyWith(
                                                                                          color:
                                                                                              colorScheme.error,
                                                                                        ),
                                                                                      );
                                                                                    }
                                                                                  },
                                                                                ),
                                                                                Builder(
                                                                                  builder: (
                                                                                    context,
                                                                                  ) {
                                                                                    if (!transferBeamSchemeData.mainLinks.contains(
                                                                                      'Fail',
                                                                                    )) {
                                                                                      return Text(
                                                                                        transferBeamSchemeData.mainLinks,
                                                                                      );
                                                                                    } else {
                                                                                      return Text(
                                                                                        transferBeamSchemeData.mainLinks,
                                                                                        style: textTheme.titleSmall!.copyWith(
                                                                                          color:
                                                                                              colorScheme.error,
                                                                                        ),
                                                                                      );
                                                                                    }
                                                                                  },
                                                                                ),
                                                                              ],
                                                                            ),
                                                                          ],
                                                                        ),
                                                                      ],
                                                                    ),
                                                                  ),
                                                                  Divider(),
                                                                  Row(
                                                                    children: [
                                                                      CircleAvatar(
                                                                        backgroundColor:
                                                                            titleIdBgColor,
                                                                        radius:
                                                                            14,
                                                                        child: Padding(
                                                                          padding: const EdgeInsets.all(
                                                                            5.0,
                                                                          ),
                                                                          child: Text(
                                                                            '7',
                                                                            style:
                                                                                titleIdTextStyle,
                                                                          ),
                                                                        ),
                                                                      ),

                                                                      SizedBox(
                                                                        width:
                                                                            5.0,
                                                                      ),
                                                                      Align(
                                                                        alignment:
                                                                            Alignment.centerLeft,
                                                                        child: _buildTitle(
                                                                          colorScheme,
                                                                          textTheme,
                                                                          'Foundation Scheme',
                                                                          containerOpacity:
                                                                              175,
                                                                          textOpacity:
                                                                              225,
                                                                        ),
                                                                      ),
                                                                    ],
                                                                  ),
                                                                  SizedBox(
                                                                    height: 5.0,
                                                                  ),

                                                                  //* For foundation scheme, we will display all selected pile scheme
                                                                  //* and will only display 'no pile scheme selected' when
                                                                  //* this is no pile scheme selected
                                                                  DefaultTextStyle(
                                                                    style: textTheme
                                                                        .labelMedium!
                                                                        .copyWith(
                                                                          color:
                                                                              colorScheme.onSurface,
                                                                        ),
                                                                    child: Builder(
                                                                      builder: (
                                                                        context,
                                                                      ) {
                                                                        if (selectedPileEndBearingBoredSchemeIndex >=
                                                                            0) {
                                                                          return Column(
                                                                            crossAxisAlignment:
                                                                                CrossAxisAlignment.start,
                                                                            children: [
                                                                              Padding(
                                                                                padding: const EdgeInsets.fromLTRB(
                                                                                  0.0,
                                                                                  8,
                                                                                  0.0,
                                                                                  8,
                                                                                ),
                                                                                child: _buildTitle(
                                                                                  colorScheme,
                                                                                  textTheme,
                                                                                  'End Bearing Bored Pile Scheme',
                                                                                  containerOpacity:
                                                                                      175,
                                                                                  textOpacity:
                                                                                      225,
                                                                                  textStyle: textTheme.labelLarge!.copyWith(
                                                                                    color:
                                                                                        colorScheme.onTertiary,
                                                                                  ),
                                                                                  containerBgColor:
                                                                                      colorScheme.tertiary,
                                                                                ),
                                                                              ),
                                                                              SizedBox(
                                                                                height:
                                                                                    5.0,
                                                                              ),
                                                                              Row(
                                                                                crossAxisAlignment:
                                                                                    CrossAxisAlignment.start,
                                                                                children: [
                                                                                  GestureDetector(
                                                                                    onTap: () async {
                                                                                      await showDialog(
                                                                                        context:
                                                                                            context,
                                                                                        builder: (
                                                                                          context,
                                                                                        ) {
                                                                                          return Dialog(
                                                                                            backgroundColor:
                                                                                                colorScheme.surfaceContainer,
                                                                                            child: SizedBox(
                                                                                              width:
                                                                                                  550,
                                                                                              height:
                                                                                                  550,
                                                                                              child: DrawPileEndBearingBoredScheme(
                                                                                                sketchWidth:
                                                                                                    500,
                                                                                                sketchHeight:
                                                                                                    500,
                                                                                                index:
                                                                                                    selectedPileEndBearingBoredSchemeIndex,
                                                                                                // fontSize: 9.0,
                                                                                              ),
                                                                                            ),
                                                                                          );
                                                                                        },
                                                                                      );
                                                                                    },
                                                                                    child: DrawPileEndBearingBoredScheme(
                                                                                      sketchWidth:
                                                                                          125,
                                                                                      sketchHeight:
                                                                                          125,
                                                                                      index:
                                                                                          selectedPileEndBearingBoredSchemeIndex,
                                                                                      fontSize:
                                                                                          9.0,
                                                                                    ),
                                                                                  ),
                                                                                  SizedBox(
                                                                                    width:
                                                                                        5.0,
                                                                                  ),
                                                                                  Flexible(
                                                                                    child: DefaultTextStyle(
                                                                                      style: textTheme.labelMedium!.copyWith(
                                                                                        color:
                                                                                            colorScheme.onSurface,
                                                                                      ),
                                                                                      child: Column(
                                                                                        crossAxisAlignment:
                                                                                            CrossAxisAlignment.start,
                                                                                        children: [
                                                                                          // Text('Index:'),
                                                                                          Container(
                                                                                            decoration: BoxDecoration(
                                                                                              shape:
                                                                                                  BoxShape.rectangle,
                                                                                              borderRadius: BorderRadius.circular(
                                                                                                5.0,
                                                                                              ),
                                                                                              border: Border.all(
                                                                                                color: colorScheme.primary.withAlpha(
                                                                                                  150,
                                                                                                ),
                                                                                              ),
                                                                                            ),
                                                                                            child: Padding(
                                                                                              padding: const EdgeInsets.all(
                                                                                                4.0,
                                                                                              ),
                                                                                              child: Text(
                                                                                                'Index',
                                                                                              ),
                                                                                            ),
                                                                                          ),
                                                                                          // Text(
                                                                                          //   'SPT N-Value:',
                                                                                          // ),
                                                                                          // Text(
                                                                                          //   'Length[${unit[3]}]:',
                                                                                          // ),
                                                                                          Text(
                                                                                            'Diameter[${unit[4]}]:',
                                                                                          ),

                                                                                          Text(
                                                                                            'fcu[${unit[5]}]:',
                                                                                          ),
                                                                                          Text(
                                                                                            'SLS Load [${unit[0]}]:',
                                                                                          ),
                                                                                          Text(
                                                                                            'ULS Load [${unit[0]}]:',
                                                                                          ),
                                                                                          Text(
                                                                                            'Cap (SLS) [${unit[0]}]: ',
                                                                                          ),
                                                                                          Text(
                                                                                            'Cap (ULS) [${unit[0]}]: ',
                                                                                          ),
                                                                                          Text(
                                                                                            'Rebar: ',
                                                                                          ),
                                                                                        ],
                                                                                      ),
                                                                                    ),
                                                                                  ),
                                                                                  SizedBox(
                                                                                    width:
                                                                                        5.0,
                                                                                  ),
                                                                                  Flexible(
                                                                                    child: DefaultTextStyle(
                                                                                      style: textTheme.bodySmall!.copyWith(
                                                                                        color:
                                                                                            colorScheme.onSurface,
                                                                                      ),
                                                                                      child: Column(
                                                                                        crossAxisAlignment:
                                                                                            CrossAxisAlignment.start,
                                                                                        children: [
                                                                                          // Text(
                                                                                          //   '${selectedColumnSchemeIndex + 1}',
                                                                                          // ),
                                                                                          Container(
                                                                                            decoration: BoxDecoration(
                                                                                              shape:
                                                                                                  BoxShape.circle,
                                                                                              border: Border.all(
                                                                                                color: colorScheme.primary.withAlpha(
                                                                                                  150,
                                                                                                ),
                                                                                              ),
                                                                                            ),
                                                                                            child: Padding(
                                                                                              padding: const EdgeInsets.all(
                                                                                                4.0,
                                                                                              ),
                                                                                              child: Text(
                                                                                                '${selectedPileEndBearingBoredSchemeIndex + 1}',
                                                                                              ),
                                                                                            ),
                                                                                          ),
                                                                                          // Text(
                                                                                          //   NumberFormat(
                                                                                          //     '0',
                                                                                          //   ).format(
                                                                                          //     selectedPileEndBearingBoredScheme.sptNValue,
                                                                                          //   ),
                                                                                          // ),
                                                                                          // Text(
                                                                                          //   NumberFormat(
                                                                                          //     '0',
                                                                                          //   ).format(
                                                                                          //     selectedPileEndBearingBoredScheme.length,
                                                                                          //   ),
                                                                                          // ),
                                                                                          Text(
                                                                                            NumberFormat(
                                                                                              '0',
                                                                                            ).format(
                                                                                              selectedPileEndBearingBoredScheme.diameter,
                                                                                            ),
                                                                                          ),

                                                                                          Text(
                                                                                            NumberFormat(
                                                                                              '0',
                                                                                            ).format(
                                                                                              selectedPileEndBearingBoredScheme.fcu,
                                                                                            ),
                                                                                          ),
                                                                                          Text(
                                                                                            NumberFormat(
                                                                                              '0',
                                                                                            ).format(
                                                                                              selectedPileEndBearingBoredScheme.slsLoad,
                                                                                            ),
                                                                                          ),
                                                                                          Text(
                                                                                            NumberFormat(
                                                                                              '0',
                                                                                            ).format(
                                                                                              selectedPileEndBearingBoredScheme.ulsLoad,
                                                                                            ),
                                                                                          ),
                                                                                          Text(
                                                                                            NumberFormat(
                                                                                              '0',
                                                                                            ).format(
                                                                                              selectedPileEndBearingBoredScheme.totalGroundResistance,
                                                                                            ),
                                                                                          ),
                                                                                          Text(
                                                                                            NumberFormat(
                                                                                              '0',
                                                                                            ).format(
                                                                                              selectedPileEndBearingBoredScheme.strCapacity,
                                                                                            ),
                                                                                          ),
                                                                                          Text(
                                                                                            selectedPileEndBearingBoredScheme.rebar,
                                                                                          ),
                                                                                        ],
                                                                                      ),
                                                                                    ),
                                                                                  ),
                                                                                ],
                                                                              ),
                                                                            ],
                                                                          );
                                                                        } else {
                                                                          return SizedBox(
                                                                            height:
                                                                                0,
                                                                            width:
                                                                                0,
                                                                          );
                                                                        }
                                                                      },
                                                                    ),
                                                                  ),
                                                                  DefaultTextStyle(
                                                                    style: textTheme
                                                                        .labelMedium!
                                                                        .copyWith(
                                                                          color:
                                                                              colorScheme.onSurface,
                                                                        ),
                                                                    child: Builder(
                                                                      builder: (
                                                                        context,
                                                                      ) {
                                                                        if (selectedPileFrictionalBoredSchemeIndex >=
                                                                            0) {
                                                                          return Column(
                                                                            crossAxisAlignment:
                                                                                CrossAxisAlignment.start,
                                                                            children: [
                                                                              Padding(
                                                                                padding: const EdgeInsets.fromLTRB(
                                                                                  0.0,
                                                                                  8,
                                                                                  0.0,
                                                                                  8,
                                                                                ),
                                                                                child: _buildTitle(
                                                                                  colorScheme,
                                                                                  textTheme,
                                                                                  'Frictional Bored Pile Scheme',
                                                                                  containerOpacity:
                                                                                      175,
                                                                                  textOpacity:
                                                                                      225,
                                                                                  textStyle: textTheme.labelLarge!.copyWith(
                                                                                    color:
                                                                                        colorScheme.onTertiary,
                                                                                  ),
                                                                                  containerBgColor:
                                                                                      colorScheme.tertiary,
                                                                                ),
                                                                              ),
                                                                              SizedBox(
                                                                                height:
                                                                                    5.0,
                                                                              ),
                                                                              Row(
                                                                                crossAxisAlignment:
                                                                                    CrossAxisAlignment.start,
                                                                                children: [
                                                                                  GestureDetector(
                                                                                    onTap: () async {
                                                                                      await showDialog(
                                                                                        context:
                                                                                            context,
                                                                                        builder: (
                                                                                          context,
                                                                                        ) {
                                                                                          return Dialog(
                                                                                            backgroundColor:
                                                                                                colorScheme.surfaceContainer,
                                                                                            child: SizedBox(
                                                                                              width:
                                                                                                  550,
                                                                                              height:
                                                                                                  550,
                                                                                              child: DrawPileFrictionalBoredScheme(
                                                                                                sketchWidth:
                                                                                                    500,
                                                                                                sketchHeight:
                                                                                                    500,
                                                                                                index:
                                                                                                    selectedPileFrictionalBoredSchemeIndex,
                                                                                                // fontSize: 9.0,
                                                                                              ),
                                                                                            ),
                                                                                          );
                                                                                        },
                                                                                      );
                                                                                    },
                                                                                    child: DrawPileFrictionalBoredScheme(
                                                                                      sketchWidth:
                                                                                          125,
                                                                                      sketchHeight:
                                                                                          125,
                                                                                      index:
                                                                                          selectedPileFrictionalBoredSchemeIndex,
                                                                                      fontSize:
                                                                                          9.0,
                                                                                    ),
                                                                                  ),
                                                                                  SizedBox(
                                                                                    width:
                                                                                        5.0,
                                                                                  ),
                                                                                  Flexible(
                                                                                    child: DefaultTextStyle(
                                                                                      style: textTheme.labelMedium!.copyWith(
                                                                                        color:
                                                                                            colorScheme.onSurface,
                                                                                      ),
                                                                                      child: Column(
                                                                                        crossAxisAlignment:
                                                                                            CrossAxisAlignment.start,
                                                                                        children: [
                                                                                          // Text('Index:'),
                                                                                          Container(
                                                                                            decoration: BoxDecoration(
                                                                                              shape:
                                                                                                  BoxShape.rectangle,
                                                                                              borderRadius: BorderRadius.circular(
                                                                                                5.0,
                                                                                              ),
                                                                                              border: Border.all(
                                                                                                color: colorScheme.primary.withAlpha(
                                                                                                  150,
                                                                                                ),
                                                                                              ),
                                                                                            ),
                                                                                            child: Padding(
                                                                                              padding: const EdgeInsets.all(
                                                                                                4.0,
                                                                                              ),
                                                                                              child: Text(
                                                                                                'Index',
                                                                                              ),
                                                                                            ),
                                                                                          ),
                                                                                          Text(
                                                                                            'SPT N-Value:',
                                                                                          ),
                                                                                          Text(
                                                                                            'Length[${unit[3]}]:',
                                                                                          ),
                                                                                          Text(
                                                                                            'Diameter[${unit[4]}]:',
                                                                                          ),

                                                                                          Text(
                                                                                            'fcu[${unit[5]}]:',
                                                                                          ),
                                                                                          Text(
                                                                                            'SLS Load [${unit[0]}]:',
                                                                                          ),
                                                                                          Text(
                                                                                            'ULS Load [${unit[0]}]:',
                                                                                          ),
                                                                                          Text(
                                                                                            'Cap (SLS) [${unit[0]}]: ',
                                                                                          ),
                                                                                          Text(
                                                                                            'Cap (ULS) [${unit[0]}]: ',
                                                                                          ),
                                                                                          Text(
                                                                                            'Rebar: ',
                                                                                          ),
                                                                                        ],
                                                                                      ),
                                                                                    ),
                                                                                  ),
                                                                                  SizedBox(
                                                                                    width:
                                                                                        5.0,
                                                                                  ),
                                                                                  Flexible(
                                                                                    child: DefaultTextStyle(
                                                                                      style: textTheme.bodySmall!.copyWith(
                                                                                        color:
                                                                                            colorScheme.onSurface,
                                                                                      ),
                                                                                      child: Column(
                                                                                        crossAxisAlignment:
                                                                                            CrossAxisAlignment.start,
                                                                                        children: [
                                                                                          // Text(
                                                                                          //   '${selectedColumnSchemeIndex + 1}',
                                                                                          // ),
                                                                                          Container(
                                                                                            decoration: BoxDecoration(
                                                                                              shape:
                                                                                                  BoxShape.circle,
                                                                                              border: Border.all(
                                                                                                color: colorScheme.primary.withAlpha(
                                                                                                  150,
                                                                                                ),
                                                                                              ),
                                                                                            ),
                                                                                            child: Padding(
                                                                                              padding: const EdgeInsets.all(
                                                                                                4.0,
                                                                                              ),
                                                                                              child: Text(
                                                                                                '${selectedPileFrictionalBoredSchemeIndex + 1}',
                                                                                              ),
                                                                                            ),
                                                                                          ),
                                                                                          Text(
                                                                                            NumberFormat(
                                                                                              '0',
                                                                                            ).format(
                                                                                              selectedPileFrictionalBoredScheme.sptNValue,
                                                                                            ),
                                                                                          ),
                                                                                          Text(
                                                                                            NumberFormat(
                                                                                              '0',
                                                                                            ).format(
                                                                                              selectedPileFrictionalBoredScheme.length,
                                                                                            ),
                                                                                          ),
                                                                                          Text(
                                                                                            NumberFormat(
                                                                                              '0',
                                                                                            ).format(
                                                                                              selectedPileFrictionalBoredScheme.diameter,
                                                                                            ),
                                                                                          ),

                                                                                          Text(
                                                                                            NumberFormat(
                                                                                              '0',
                                                                                            ).format(
                                                                                              selectedPileFrictionalBoredScheme.fcu,
                                                                                            ),
                                                                                          ),
                                                                                          Text(
                                                                                            NumberFormat(
                                                                                              '0',
                                                                                            ).format(
                                                                                              selectedPileFrictionalBoredScheme.slsLoad,
                                                                                            ),
                                                                                          ),
                                                                                          Text(
                                                                                            NumberFormat(
                                                                                              '0',
                                                                                            ).format(
                                                                                              selectedPileFrictionalBoredScheme.ulsLoad,
                                                                                            ),
                                                                                          ),
                                                                                          Text(
                                                                                            NumberFormat(
                                                                                              '0',
                                                                                            ).format(
                                                                                              selectedPileFrictionalBoredScheme.totalGroundResistance,
                                                                                            ),
                                                                                          ),
                                                                                          Text(
                                                                                            NumberFormat(
                                                                                              '0',
                                                                                            ).format(
                                                                                              selectedPileFrictionalBoredScheme.strCapacity,
                                                                                            ),
                                                                                          ),
                                                                                          Text(
                                                                                            selectedPileFrictionalBoredScheme.rebar,
                                                                                          ),
                                                                                        ],
                                                                                      ),
                                                                                    ),
                                                                                  ),
                                                                                ],
                                                                              ),
                                                                            ],
                                                                          );
                                                                        } else {
                                                                          return SizedBox(
                                                                            height:
                                                                                0,
                                                                            width:
                                                                                0,
                                                                          );
                                                                        }
                                                                      },
                                                                    ),
                                                                  ),
                                                                  DefaultTextStyle(
                                                                    style: textTheme
                                                                        .labelMedium!
                                                                        .copyWith(
                                                                          color:
                                                                              colorScheme.onSurface,
                                                                        ),
                                                                    child: Builder(
                                                                      builder: (
                                                                        context,
                                                                      ) {
                                                                        if (selectedPileSocketedScheme
                                                                            .isSelected) {
                                                                          return Column(
                                                                            crossAxisAlignment:
                                                                                CrossAxisAlignment.start,
                                                                            children: [
                                                                              Padding(
                                                                                padding: const EdgeInsets.fromLTRB(
                                                                                  0.0,
                                                                                  8,
                                                                                  0.0,
                                                                                  8,
                                                                                ),
                                                                                child: _buildTitle(
                                                                                  colorScheme,
                                                                                  textTheme,
                                                                                  'Socketed Steel H Pile Scheme',
                                                                                  containerOpacity:
                                                                                      175,
                                                                                  textOpacity:
                                                                                      225,
                                                                                  textStyle: textTheme.labelLarge!.copyWith(
                                                                                    color:
                                                                                        colorScheme.onTertiary,
                                                                                  ),
                                                                                  containerBgColor:
                                                                                      colorScheme.tertiary,
                                                                                ),
                                                                              ),
                                                                              SizedBox(
                                                                                height:
                                                                                    5.0,
                                                                              ),
                                                                              Row(
                                                                                crossAxisAlignment:
                                                                                    CrossAxisAlignment.start,
                                                                                children: [
                                                                                  GestureDetector(
                                                                                    onTap: () async {
                                                                                      await showDialog(
                                                                                        context:
                                                                                            context,
                                                                                        builder: (
                                                                                          context,
                                                                                        ) {
                                                                                          return Dialog(
                                                                                            backgroundColor:
                                                                                                colorScheme.surfaceContainer,
                                                                                            child: SizedBox(
                                                                                              width:
                                                                                                  550,
                                                                                              height:
                                                                                                  550,
                                                                                              child: DrawPileSocketedScheme(
                                                                                                sketchWidth:
                                                                                                    500,
                                                                                                sketchHeight:
                                                                                                    500,
                                                                                              ),
                                                                                            ),
                                                                                          );
                                                                                        },
                                                                                      );
                                                                                    },
                                                                                    child: DrawPileSocketedScheme(
                                                                                      sketchWidth:
                                                                                          125,
                                                                                      sketchHeight:
                                                                                          125,
                                                                                      fontSize:
                                                                                          9.0,
                                                                                    ),
                                                                                  ),
                                                                                  SizedBox(
                                                                                    width:
                                                                                        5.0,
                                                                                  ),
                                                                                  Flexible(
                                                                                    child: DefaultTextStyle(
                                                                                      style: textTheme.labelMedium!.copyWith(
                                                                                        color:
                                                                                            colorScheme.onSurface,
                                                                                      ),
                                                                                      child: Column(
                                                                                        crossAxisAlignment:
                                                                                            CrossAxisAlignment.start,
                                                                                        children: [
                                                                                          Text(
                                                                                            'Section:',
                                                                                          ),
                                                                                          Text(
                                                                                            'Grade:',
                                                                                          ),
                                                                                          Text(
                                                                                            'Diameter[${unit[4]}]:',
                                                                                          ),
                                                                                          Text(
                                                                                            'Length[${unit[3]}]:',
                                                                                          ),
                                                                                          Text(
                                                                                            'fcu[${unit[5]}]:',
                                                                                          ),
                                                                                          Text(
                                                                                            'Cap (SLS) [${unit[0]}]:',
                                                                                          ),
                                                                                          Text(
                                                                                            'Cap (ULS) [${unit[0]}]:',
                                                                                          ),
                                                                                        ],
                                                                                      ),
                                                                                    ),
                                                                                  ),
                                                                                  SizedBox(
                                                                                    width:
                                                                                        5.0,
                                                                                  ),
                                                                                  Flexible(
                                                                                    child: DefaultTextStyle(
                                                                                      style: textTheme.bodySmall!.copyWith(
                                                                                        color:
                                                                                            colorScheme.onSurface,
                                                                                      ),
                                                                                      child: Column(
                                                                                        crossAxisAlignment:
                                                                                            CrossAxisAlignment.start,
                                                                                        children: [
                                                                                          Text(
                                                                                            'UBP 305x305x223',
                                                                                          ),
                                                                                          Text(
                                                                                            'S460',
                                                                                          ),
                                                                                          Text(
                                                                                            NumberFormat(
                                                                                              '0',
                                                                                            ).format(
                                                                                              610,
                                                                                            ),
                                                                                          ),
                                                                                          Text(
                                                                                            NumberFormat(
                                                                                              '0',
                                                                                            ).format(
                                                                                              11.5,
                                                                                            ),
                                                                                          ),
                                                                                          Text(
                                                                                            NumberFormat(
                                                                                              '0',
                                                                                            ).format(
                                                                                              45,
                                                                                            ),
                                                                                          ),
                                                                                          Text(
                                                                                            NumberFormat(
                                                                                              '0',
                                                                                            ).format(
                                                                                              6106,
                                                                                            ),
                                                                                          ),
                                                                                          Text(
                                                                                            NumberFormat(
                                                                                              '0',
                                                                                            ).format(
                                                                                              12212,
                                                                                            ),
                                                                                          ),
                                                                                        ],
                                                                                      ),
                                                                                    ),
                                                                                  ),
                                                                                ],
                                                                              ),
                                                                            ],
                                                                          );
                                                                        } else {
                                                                          return SizedBox(
                                                                            height:
                                                                                0,
                                                                            width:
                                                                                0,
                                                                          );
                                                                        }
                                                                      },
                                                                    ),
                                                                  ),
                                                                  DefaultTextStyle(
                                                                    style: textTheme
                                                                        .labelMedium!
                                                                        .copyWith(
                                                                          color:
                                                                              colorScheme.onSurface,
                                                                        ),
                                                                    child: Builder(
                                                                      builder: (
                                                                        context,
                                                                      ) {
                                                                        if (selectedPileDrivenSchemeIndex >=
                                                                            0) {
                                                                          return Column(
                                                                            crossAxisAlignment:
                                                                                CrossAxisAlignment.start,
                                                                            children: [
                                                                              Padding(
                                                                                padding: const EdgeInsets.fromLTRB(
                                                                                  0.0,
                                                                                  8,
                                                                                  0.0,
                                                                                  8,
                                                                                ),
                                                                                child: _buildTitle(
                                                                                  colorScheme,
                                                                                  textTheme,
                                                                                  'Driven Steel H Pile Scheme',
                                                                                  containerOpacity:
                                                                                      175,
                                                                                  textOpacity:
                                                                                      225,
                                                                                  textStyle: textTheme.labelLarge!.copyWith(
                                                                                    color:
                                                                                        colorScheme.onTertiary,
                                                                                  ),
                                                                                  containerBgColor:
                                                                                      colorScheme.tertiary,
                                                                                ),
                                                                              ),
                                                                              SizedBox(
                                                                                height:
                                                                                    5.0,
                                                                              ),
                                                                              Row(
                                                                                crossAxisAlignment:
                                                                                    CrossAxisAlignment.start,
                                                                                children: [
                                                                                  GestureDetector(
                                                                                    onTap: () async {
                                                                                      await showDialog(
                                                                                        context:
                                                                                            context,
                                                                                        builder: (
                                                                                          context,
                                                                                        ) {
                                                                                          return Dialog(
                                                                                            backgroundColor:
                                                                                                colorScheme.surfaceContainer,
                                                                                            child: SizedBox(
                                                                                              width:
                                                                                                  550,
                                                                                              height:
                                                                                                  550,
                                                                                              child: DrawPileDrivenScheme(
                                                                                                sketchWidth:
                                                                                                    500,
                                                                                                sketchHeight:
                                                                                                    500,
                                                                                                index:
                                                                                                    selectedPileDrivenSchemeIndex,
                                                                                              ),
                                                                                            ),
                                                                                          );
                                                                                        },
                                                                                      );
                                                                                    },
                                                                                    child: DrawPileDrivenScheme(
                                                                                      sketchWidth:
                                                                                          125,
                                                                                      sketchHeight:
                                                                                          125,
                                                                                      index:
                                                                                          selectedPileDrivenSchemeIndex,
                                                                                      fontSize:
                                                                                          9.0,
                                                                                    ),
                                                                                  ),
                                                                                  SizedBox(
                                                                                    width:
                                                                                        5.0,
                                                                                  ),
                                                                                  Flexible(
                                                                                    child: DefaultTextStyle(
                                                                                      style: textTheme.labelMedium!.copyWith(
                                                                                        color:
                                                                                            colorScheme.onSurface,
                                                                                      ),
                                                                                      child: Column(
                                                                                        crossAxisAlignment:
                                                                                            CrossAxisAlignment.start,
                                                                                        children: [
                                                                                          Text(
                                                                                            'SPT N-value:',
                                                                                          ),
                                                                                          Text(
                                                                                            'Length[${unit[3]}]:',
                                                                                          ),
                                                                                          Text(
                                                                                            'Section:',
                                                                                          ),
                                                                                          Text(
                                                                                            'Grade[${unit[5]}]:',
                                                                                          ),
                                                                                          Text(
                                                                                            'SLS Load [${unit[0]}]:',
                                                                                          ),
                                                                                          Text(
                                                                                            'ULS Load [${unit[0]}]:',
                                                                                          ),
                                                                                          Text(
                                                                                            'Cap (SLS) [${unit[0]}]:',
                                                                                          ),
                                                                                          Text(
                                                                                            'Cap (ULS) [${unit[0]}]:',
                                                                                          ),
                                                                                        ],
                                                                                      ),
                                                                                    ),
                                                                                  ),
                                                                                  SizedBox(
                                                                                    width:
                                                                                        5.0,
                                                                                  ),
                                                                                  Flexible(
                                                                                    child: DefaultTextStyle(
                                                                                      style: textTheme.bodySmall!.copyWith(
                                                                                        color:
                                                                                            colorScheme.onSurface,
                                                                                      ),
                                                                                      child: Column(
                                                                                        crossAxisAlignment:
                                                                                            CrossAxisAlignment.start,
                                                                                        children: [
                                                                                          Text(
                                                                                            NumberFormat(
                                                                                              '0',
                                                                                            ).format(
                                                                                              selectedPileDrivenScheme.sptNValue,
                                                                                            ),
                                                                                          ),
                                                                                          Text(
                                                                                            NumberFormat(
                                                                                              '0',
                                                                                            ).format(
                                                                                              selectedPileDrivenScheme.length,
                                                                                            ),
                                                                                          ),
                                                                                          Text(
                                                                                            'UBP 305x305x223',
                                                                                          ),
                                                                                          Text(
                                                                                            'S460',
                                                                                          ),
                                                                                          Text(
                                                                                            NumberFormat(
                                                                                              '0',
                                                                                            ).format(
                                                                                              selectedPileDrivenScheme.slsLoad,
                                                                                            ),
                                                                                          ),
                                                                                          Text(
                                                                                            NumberFormat(
                                                                                              '0',
                                                                                            ).format(
                                                                                              selectedPileDrivenScheme.ulsLoad,
                                                                                            ),
                                                                                          ),
                                                                                          Text(
                                                                                            NumberFormat(
                                                                                              '0',
                                                                                            ).format(
                                                                                              selectedPileDrivenScheme.totalGroundResistance,
                                                                                            ),
                                                                                          ),
                                                                                          Text(
                                                                                            NumberFormat(
                                                                                              '0',
                                                                                            ).format(
                                                                                              selectedPileDrivenScheme.strULSCapacity,
                                                                                            ),
                                                                                          ),
                                                                                        ],
                                                                                      ),
                                                                                    ),
                                                                                  ),
                                                                                ],
                                                                              ),
                                                                            ],
                                                                          );
                                                                        } else {
                                                                          return SizedBox(
                                                                            height:
                                                                                0,
                                                                            width:
                                                                                0,
                                                                          );
                                                                        }
                                                                      },
                                                                    ),
                                                                  ),
                                                                  DefaultTextStyle(
                                                                    style: textTheme
                                                                        .labelMedium!
                                                                        .copyWith(
                                                                          color:
                                                                              colorScheme.onSurface,
                                                                        ),
                                                                    child: Builder(
                                                                      builder: (
                                                                        context,
                                                                      ) {
                                                                        if (selectedFootingSchemeIndex >=
                                                                            0) {
                                                                          return Column(
                                                                            crossAxisAlignment:
                                                                                CrossAxisAlignment.start,
                                                                            children: [
                                                                              Padding(
                                                                                padding: const EdgeInsets.fromLTRB(
                                                                                  0.0,
                                                                                  8,
                                                                                  0.0,
                                                                                  8,
                                                                                ),
                                                                                child: _buildTitle(
                                                                                  colorScheme,
                                                                                  textTheme,
                                                                                  'Pad Footing Scheme',
                                                                                  containerOpacity:
                                                                                      175,
                                                                                  textOpacity:
                                                                                      225,
                                                                                  textStyle: textTheme.labelLarge!.copyWith(
                                                                                    color:
                                                                                        colorScheme.onTertiary,
                                                                                  ),
                                                                                  containerBgColor:
                                                                                      colorScheme.tertiary,
                                                                                ),
                                                                              ),
                                                                              SizedBox(
                                                                                height:
                                                                                    5.0,
                                                                              ),
                                                                              Row(
                                                                                crossAxisAlignment:
                                                                                    CrossAxisAlignment.start,
                                                                                children: [
                                                                                  GestureDetector(
                                                                                    onTap: () async {
                                                                                      await showDialog(
                                                                                        context:
                                                                                            context,
                                                                                        builder: (
                                                                                          context,
                                                                                        ) {
                                                                                          return Dialog(
                                                                                            backgroundColor:
                                                                                                colorScheme.surfaceContainer,
                                                                                            child: SizedBox(
                                                                                              width:
                                                                                                  550,
                                                                                              height:
                                                                                                  550,
                                                                                              child: DrawFootingSchemeSketch(
                                                                                                sketchWidth:
                                                                                                    500,
                                                                                                sketchHeight:
                                                                                                    500,
                                                                                                index:
                                                                                                    selectedFootingSchemeIndex,
                                                                                              ),
                                                                                            ),
                                                                                          );
                                                                                        },
                                                                                      );
                                                                                    },
                                                                                    child: DrawFootingSchemeSketch(
                                                                                      sketchWidth:
                                                                                          125,
                                                                                      sketchHeight:
                                                                                          125,
                                                                                      index:
                                                                                          selectedFootingSchemeIndex,
                                                                                      fontSize:
                                                                                          9.0,
                                                                                    ),
                                                                                  ),
                                                                                  SizedBox(
                                                                                    width:
                                                                                        5.0,
                                                                                  ),
                                                                                  GestureDetector(
                                                                                    onTap: () async {
                                                                                      await showDialog(
                                                                                        context:
                                                                                            context,
                                                                                        builder: (
                                                                                          context,
                                                                                        ) {
                                                                                          return Dialog(
                                                                                            backgroundColor:
                                                                                                colorScheme.surfaceContainer,
                                                                                            child: SizedBox(
                                                                                              width:
                                                                                                  550,
                                                                                              height:
                                                                                                  550,
                                                                                              child: DrawFootingSchemePlanSketch(
                                                                                                sketchWidth:
                                                                                                    500,
                                                                                                sketchHeight:
                                                                                                    500,
                                                                                                index:
                                                                                                    selectedFootingSchemeIndex,
                                                                                              ),
                                                                                            ),
                                                                                          );
                                                                                        },
                                                                                      );
                                                                                    },
                                                                                    child: DrawFootingSchemePlanSketch(
                                                                                      sketchWidth:
                                                                                          125,
                                                                                      sketchHeight:
                                                                                          125,
                                                                                      index:
                                                                                          selectedFootingSchemeIndex,
                                                                                      fontSize:
                                                                                          9.0,
                                                                                    ),
                                                                                  ),
                                                                                  SizedBox(
                                                                                    width:
                                                                                        5.0,
                                                                                  ),
                                                                                ],
                                                                              ),
                                                                              SizedBox(
                                                                                height:
                                                                                    5.0,
                                                                              ),
                                                                              Row(
                                                                                children: [
                                                                                  Flexible(
                                                                                    child: DefaultTextStyle(
                                                                                      style: textTheme.labelMedium!.copyWith(
                                                                                        color:
                                                                                            colorScheme.onSurface,
                                                                                      ),
                                                                                      child: Column(
                                                                                        crossAxisAlignment:
                                                                                            CrossAxisAlignment.start,
                                                                                        children: [
                                                                                          Text(
                                                                                            'Size:',
                                                                                          ),
                                                                                          Text(
                                                                                            'Column Size [${unit[4]}]:',
                                                                                          ),
                                                                                          Text(
                                                                                            'Ground Type:',
                                                                                          ),
                                                                                          Builder(
                                                                                            builder: (
                                                                                              context,
                                                                                            ) {
                                                                                              if (selectedFootingScheme.groundType ==
                                                                                                  'Soil') {
                                                                                                return Text(
                                                                                                  'SPT N-value:',
                                                                                                );
                                                                                              } else {
                                                                                                return Text(
                                                                                                  'Rock Capacity [${unit[1]}]:',
                                                                                                );
                                                                                              }
                                                                                            },
                                                                                          ),
                                                                                          Text(
                                                                                            'Footing Top Level [mPD]:',
                                                                                          ),
                                                                                          Text(
                                                                                            'Water Table Level [mPD]:',
                                                                                          ),
                                                                                          Text(
                                                                                            'SLS Load [${unit[0]}]:',
                                                                                          ),
                                                                                          Text(
                                                                                            'ULS Load [${unit[0]}]:',
                                                                                          ),
                                                                                          Text(
                                                                                            'Main Top Bar:',
                                                                                          ),
                                                                                          Text(
                                                                                            'Main Bottom Bar:',
                                                                                          ),
                                                                                          Text(
                                                                                            'Main Links:',
                                                                                          ),
                                                                                          Text(
                                                                                            'Punching Links:',
                                                                                          ),
                                                                                        ],
                                                                                      ),
                                                                                    ),
                                                                                  ),
                                                                                  SizedBox(
                                                                                    width:
                                                                                        10.0,
                                                                                  ),
                                                                                  Flexible(
                                                                                    child: DefaultTextStyle(
                                                                                      style: textTheme.bodySmall!.copyWith(
                                                                                        color:
                                                                                            colorScheme.onSurface,
                                                                                      ),
                                                                                      child: Column(
                                                                                        crossAxisAlignment:
                                                                                            CrossAxisAlignment.start,
                                                                                        children: [
                                                                                          Text(
                                                                                            '${NumberFormat('0').format(selectedFootingScheme.size)}(W)x${NumberFormat('0').format(selectedFootingScheme.size)}(L)x${NumberFormat('0').format(selectedFootingScheme.strZone)}(D) [${unit[4]}]',
                                                                                          ),
                                                                                          Text(
                                                                                            NumberFormat(
                                                                                              '0',
                                                                                            ).format(
                                                                                              selectedFootingScheme.columnSize,
                                                                                            ),
                                                                                          ),
                                                                                          Text(
                                                                                            '${selectedFootingScheme.groundType}',
                                                                                          ),
                                                                                          Builder(
                                                                                            builder: (
                                                                                              context,
                                                                                            ) {
                                                                                              if (selectedFootingScheme.groundType ==
                                                                                                  'Soil') {
                                                                                                return Text(
                                                                                                  NumberFormat(
                                                                                                    '0',
                                                                                                  ).format(
                                                                                                    selectedFootingScheme.soilNValue,
                                                                                                  ),
                                                                                                );
                                                                                              } else {
                                                                                                return Text(
                                                                                                  NumberFormat(
                                                                                                    '0',
                                                                                                  ).format(
                                                                                                    selectedFootingScheme.rockCapacity,
                                                                                                  ),
                                                                                                );
                                                                                              }
                                                                                            },
                                                                                          ),
                                                                                          Text(
                                                                                            NumberFormat(
                                                                                              '0.000',
                                                                                            ).format(
                                                                                              selectedFootingScheme.footingTopLevel,
                                                                                            ),
                                                                                          ),
                                                                                          Text(
                                                                                            NumberFormat(
                                                                                              '0.000',
                                                                                            ).format(
                                                                                              selectedFootingScheme.waterTableLevel,
                                                                                            ),
                                                                                          ),
                                                                                          Text(
                                                                                            NumberFormat(
                                                                                              '0',
                                                                                            ).format(
                                                                                              selectedFootingScheme.slsLoad,
                                                                                            ),
                                                                                          ),
                                                                                          Text(
                                                                                            NumberFormat(
                                                                                              '0',
                                                                                            ).format(
                                                                                              selectedFootingScheme.ulsLoad,
                                                                                            ),
                                                                                          ),
                                                                                          Text(
                                                                                            selectedFootingScheme.mainTopBar,
                                                                                          ),
                                                                                          Text(
                                                                                            selectedFootingScheme.mainBottomBar,
                                                                                          ),
                                                                                          Text(
                                                                                            selectedFootingScheme.mainLinks,
                                                                                          ),
                                                                                          Text(
                                                                                            selectedFootingScheme.punchingLinks,
                                                                                          ),
                                                                                        ],
                                                                                      ),
                                                                                    ),
                                                                                  ),
                                                                                ],
                                                                              ),
                                                                            ],
                                                                          );
                                                                        } else {
                                                                          return SizedBox(
                                                                            height:
                                                                                0,
                                                                            width:
                                                                                0,
                                                                          );
                                                                        }
                                                                      },
                                                                    ),
                                                                  ),

                                                                  //* If no any pile scheme selected, below will be displayed
                                                                  Builder(
                                                                    builder: (
                                                                      context,
                                                                    ) {
                                                                      if (selectedPileEndBearingBoredSchemeIndex <
                                                                              0 &&
                                                                          selectedPileFrictionalBoredSchemeIndex <
                                                                              0 &&
                                                                          !selectedPileSocketedScheme
                                                                              .isSelected &&
                                                                          selectedPileDrivenSchemeIndex <
                                                                              0 &&
                                                                          !selectedFootingScheme
                                                                              .isSelected) {
                                                                        return Align(
                                                                          alignment:
                                                                              Alignment.topLeft,
                                                                          child: Container(
                                                                            decoration: BoxDecoration(
                                                                              color:
                                                                                  colorScheme.errorContainer,
                                                                              borderRadius: BorderRadius.circular(
                                                                                5.0,
                                                                              ),
                                                                            ),
                                                                            child: Padding(
                                                                              padding: const EdgeInsets.all(
                                                                                2.0,
                                                                              ),
                                                                              child: Text(
                                                                                'Nothing selected',
                                                                                style: textTheme.titleSmall!.copyWith(
                                                                                  color:
                                                                                      colorScheme.onErrorContainer,
                                                                                ),
                                                                              ),
                                                                            ),
                                                                          ),
                                                                        );
                                                                      }
                                                                      return SizedBox(
                                                                        height:
                                                                            0,
                                                                        width:
                                                                            0,
                                                                      );
                                                                    },
                                                                  ),
                                                                ],
                                                              ),
                                                            ),
                                                          ),
                                                        ),
                                                      ),
                                                    );
                                                  },
                                                  error:
                                                      (error, stackTrace) =>
                                                          Text('Error: $error'),
                                                  loading:
                                                      () =>
                                                          CircularProgressIndicator(),
                                                );
                                              },
                                              error:
                                                  (error, stackTrace) =>
                                                      Text('Error: $error'),
                                              loading:
                                                  () =>
                                                      CircularProgressIndicator(),
                                            );
                                          },
                                          error:
                                              (error, stackTrace) =>
                                                  Text('Error: $error'),
                                          loading:
                                              () => CircularProgressIndicator(),
                                        );
                                      },
                                      error:
                                          (error, stackTrace) =>
                                              Text('Error: $error'),
                                      loading:
                                          () => CircularProgressIndicator(),
                                    );
                                  },
                                  error:
                                      (error, stackTrace) =>
                                          Text('Error: $error'),
                                  loading: () => CircularProgressIndicator(),
                                );
                              },
                              error:
                                  (error, stackTrace) => Text('Error: $error'),
                              loading: () => CircularProgressIndicator(),
                            );
                          },
                          error: (error, stackTrace) => Text('Error: $error'),
                          loading: () => CircularProgressIndicator(),
                        );
                      },
                      error: (error, stackTrace) => Text('Error: $error'),
                      loading: () => CircularProgressIndicator(),
                    );
                  },
                  error: (error, stackTrace) => Text('Error: $error'),
                  loading: () => CircularProgressIndicator(),
                );
              },
              error: (error, stackTrace) => Text('Error: $error'),
              loading: () => CircularProgressIndicator(),
            );
          },
          error: (error, stackTrace) => Text('Error: $error'),
          loading: () => CircularProgressIndicator(),
        );
      },
      error: (error, stackTrace) {
        return Text('Error: $error');
      },
      loading: () {
        return CircularProgressIndicator();
      },
    );
  }

  Container _buildTitle(
    ColorScheme colorScheme,
    TextTheme textTheme,
    String subtitle, {
    bool withBorder = false,
    Color? containerBgColor,
    Color? textColor,
    TextStyle? textStyle,
    int containerOpacity = 255,
    int textOpacity = 255,
  }) {
    // return Container(
    //   decoration: BoxDecoration(
    //     color: bgColor ?? colorScheme.surfaceContainer,
    //     borderRadius: BorderRadius.circular(5.0),
    //     // border: Border.all(color: colorScheme.outline, width: 1.0),
    //   ),
    //   child: Padding(
    //     padding: const EdgeInsets.all(3.0),
    //     child: Text(
    //       '${subtitle}',
    //       style:
    //           textStyle ??
    //           textTheme.titleSmall!.copyWith(
    //             color: textColor ?? colorScheme.onSurface,
    //           ),
    //     ),
    //   ),
    // );
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10.0),
        border:
            withBorder ? Border.all(color: Colors.grey.withAlpha(150)) : null,
        color:
            containerBgColor?.withAlpha(containerOpacity) ??
            colorScheme.secondaryContainer.withAlpha(containerOpacity),
      ),
      child: Padding(
        padding: const EdgeInsets.all(4.0),
        child: Text(
          subtitle,
          style:
              textStyle ??
              textTheme.titleSmall!.copyWith(
                color:
                    textColor?.withAlpha(textOpacity) ??
                    colorScheme.onSecondaryContainer.withAlpha(textOpacity),
              ),
        ),
      ),
    );
  }

  Future<void> _exportListToPdf(
    BuildContext context,
    WidgetRef ref, {
    int rowsPerPage = 10,
  }) async {
    final pdf = pw.Document();
    final loadingTables = ref.read(loadingTablesControllerProvider);
    loadingTables.when(
      data: (data) {
        int counter = 0;
        do {
          if (counter + rowsPerPage <= data.length) {
            pdf.addPage(
              _customPage(
                context,
                ref,
                startFrom: counter,
                rowsPerPage: rowsPerPage,
              ),
            );
          } else {
            pdf.addPage(
              _customPage(
                context,
                ref,
                startFrom: counter,
                rowsPerPage: data.length - counter,
              ),
            );
          }
          counter += rowsPerPage;
        } while (counter <= data.length);
      },
      error: (error, stackTrace) => {},
      loading: () {},
    );

    await Printing.layoutPdf(
      onLayout: (PdfPageFormat format) async => pdf.save(),
    );
  }
}

pw.Widget _customVerticalDivider() {
  return pw.Row(
    children: [
      pw.SizedBox(width: 15.0),
      pw.Container(width: 1.0, height: 30, color: PdfColors.grey400),
      pw.SizedBox(width: 15.0),
    ],
  );
}

pw.Widget _customDivider() {
  return pw.Divider(color: PdfColors.grey400);
}

pw.Page _customPage(
  BuildContext context,
  WidgetRef ref, {
  int startFrom = 0,
  int? rowsPerPage,
}) {
  final textStyle = Theme.of(context).textTheme.bodyMedium;
  return pw.Page(
    build: (pw.Context context) {
      final loadingTables = ref.read(loadingTablesControllerProvider);
      final globaldata = ref.read(globalDataControllerProvider);

      return loadingTables.when(
        data: (tables) {
          return globaldata.when(
            data: (data) {
              return pw.Center(
                child: pw.Column(
                  mainAxisAlignment: pw.MainAxisAlignment.start,
                  children: [
                    ...List.generate(rowsPerPage ?? tables.length - startFrom, (
                      index,
                    ) {
                      final double sdl =
                          tables[index + startFrom].service +
                          tables[index + startFrom].finish *
                              data.finishUnitWeight /
                              1000;
                      final double ll = tables[index + startFrom].liveLoad;
                      final double total = sdl + ll;
                      final double factoredTotal =
                          data.sdlFactor * sdl + data.llFactor * ll;
                      final String f1 = data.sdlFactor.toString();
                      final String f2 = data.llFactor.toString();
                      late List<String> unit;
                      switch (data.unit) {
                        case 'metrics':
                          unit = PreferredUnit.metrics;
                          break;
                        case 'imperial':
                          unit = PreferredUnit.imperial;
                          break;
                        default:
                          unit = PreferredUnit.metrics;
                      }
                      return pw.DefaultTextStyle(
                        style: pw.TextStyle(fontSize: textStyle!.fontSize),
                        child: pw.Column(
                          children: [
                            pw.Row(
                              children: [
                                pw.Text(
                                  '${index + startFrom + 1}',
                                  style: pw.TextStyle(
                                    fontSize: textStyle.fontSize,
                                    fontWeight: pw.FontWeight.bold,
                                  ),
                                ),

                                _customVerticalDivider(),

                                pw.Text(
                                  tables[index + startFrom].usage,
                                  style: pw.TextStyle(
                                    fontSize: textStyle.fontSize,
                                    fontWeight: pw.FontWeight.bold,
                                  ),
                                ),

                                _customVerticalDivider(),

                                pw.Column(
                                  crossAxisAlignment:
                                      pw.CrossAxisAlignment.start,

                                  children: [pw.Text('SLL: '), pw.Text('LL: ')],
                                ),
                                pw.SizedBox(width: 10.0),

                                pw.Column(
                                  crossAxisAlignment:
                                      pw.CrossAxisAlignment.start,

                                  children: [
                                    pw.Text(
                                      '${sdl.toStringAsFixed(2)} [${unit[1]}]',
                                    ),
                                    pw.Text(
                                      '${ll.toStringAsFixed(2)} [${unit[1]}]',
                                    ),
                                  ],
                                ),

                                _customVerticalDivider(),

                                pw.Column(
                                  crossAxisAlignment:
                                      pw.CrossAxisAlignment.start,
                                  children: [
                                    pw.Text('SDL+LL: '),
                                    pw.Text('${f1}SDL+${f2}LL: '),
                                  ],
                                ),
                                pw.SizedBox(width: 10.0),
                                pw.Column(
                                  crossAxisAlignment:
                                      pw.CrossAxisAlignment.start,
                                  children: [
                                    pw.Text(
                                      '${total.toStringAsFixed(2)} [${unit[1]}]',
                                    ),
                                    pw.Text(
                                      '${factoredTotal.toStringAsFixed(2)} [${unit[1]}]',
                                    ),
                                  ],
                                ),
                              ],
                            ),
                            _customDivider(),
                          ],
                        ),
                      );
                    }),
                  ],
                ),
              );
            },
            error: (error, stacktrace) => pw.Text('Error: $error'),
            loading: () => pw.Text('Loading'),
          );
        },
        error: (error, stackTrace) => pw.Text('Error: $error'),
        loading: () => pw.Text('loading'),
      );
    },
  );
}
