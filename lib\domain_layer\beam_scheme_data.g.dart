// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'beam_scheme_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_BeamSchemeData _$BeamSchemeDataFromJson(Map<String, dynamic> json) =>
    _BeamSchemeData(
      usage: json['usage'] as String? ?? '',
      finish: (json['finish'] as num?)?.toDouble() ?? 0.0,
      service: (json['service'] as num?)?.toDouble() ?? 0.0,
      liveLoad: (json['liveLoad'] as num?)?.toDouble() ?? 0.0,
      loadingTableId: json['loadingTableId'] as String? ?? '',
      shortSpan: (json['shortSpan'] as num?)?.toDouble() ?? 5.0,
      longSpan: (json['longSpan'] as num?)?.toDouble() ?? 12.0,
      bays: (json['bays'] as num?)?.toInt() ?? 2,
      mainStrZone: (json['mainStrZone'] as num?)?.toDouble() ?? 500.0,
      secStrZone: (json['secStrZone'] as num?)?.toDouble() ?? 500.0,
      fcu: (json['fcu'] as num?)?.toDouble() ?? 45.0,
      cover: (json['cover'] as num?)?.toDouble() ?? 40.0,
      mainWidth: (json['mainWidth'] as num?)?.toDouble() ?? 200.0,
      secWidth: (json['secWidth'] as num?)?.toDouble() ?? 200.0,
      mainTopBar: json['mainTopBar'] as String? ?? '',
      mainBottomBar: json['mainBottomBar'] as String? ?? '',
      mainLinks: json['mainLinks'] as String? ?? '',
      secTopBar: json['secTopBar'] as String? ?? '',
      secBottomBar: json['secBottomBar'] as String? ?? '',
      secLinks: json['secLinks'] as String? ?? '',
      beamSchemeId: json['beamSchemeId'] as String? ?? '',
      calsLog: json['calsLog'] as String? ?? '',
      isSelected: json['isSelected'] as bool? ?? false,
    );

Map<String, dynamic> _$BeamSchemeDataToJson(_BeamSchemeData instance) =>
    <String, dynamic>{
      'usage': instance.usage,
      'finish': instance.finish,
      'service': instance.service,
      'liveLoad': instance.liveLoad,
      'loadingTableId': instance.loadingTableId,
      'shortSpan': instance.shortSpan,
      'longSpan': instance.longSpan,
      'bays': instance.bays,
      'mainStrZone': instance.mainStrZone,
      'secStrZone': instance.secStrZone,
      'fcu': instance.fcu,
      'cover': instance.cover,
      'mainWidth': instance.mainWidth,
      'secWidth': instance.secWidth,
      'mainTopBar': instance.mainTopBar,
      'mainBottomBar': instance.mainBottomBar,
      'mainLinks': instance.mainLinks,
      'secTopBar': instance.secTopBar,
      'secBottomBar': instance.secBottomBar,
      'secLinks': instance.secLinks,
      'beamSchemeId': instance.beamSchemeId,
      'calsLog': instance.calsLog,
      'isSelected': instance.isSelected,
    };
