// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'footing_scheme_data_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$footingSchemeDataControllerHash() =>
    r'8450b43028d42f22b3950a308a0235f8a0494108';

/// See also [FootingSchemeDataController].
@ProviderFor(FootingSchemeDataController)
final footingSchemeDataControllerProvider = AutoDisposeAsyncNotifierProvider<
  FootingSchemeDataController,
  List<FootingSchemeData>
>.internal(
  FootingSchemeDataController.new,
  name: r'footingSchemeDataControllerProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$footingSchemeDataControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$FootingSchemeDataController =
    AutoDisposeAsyncNotifier<List<FootingSchemeData>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
