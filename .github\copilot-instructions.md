# Copilot Instructions for structify

## Project Overview
- This is a Flutter project for structural engineering calculations and visualizations.
- The codebase is organized by architectural layers: `application_layer`, `data_layer`, `domain_layer`, `presentation_layer`, and `misc` under `lib/`.
- The main UI logic and custom drawing widgets are in `presentation_layer/small_elements/`.
- Domain models and calculation logic are in `domain_layer/` and `data_layer/`.

## Key Patterns & Conventions
- **Riverpod** is used for state management. Providers are defined for each major data type (e.g., `steelTransferTrussSchemeInputControllerProvider`).
- Custom drawing is handled via `CustomPainter` subclasses (see `DrawSteelTransferTrussLoadingPainter`).
- Data flows from providers (domain/data layer) into UI widgets (presentation layer) using `ConsumerWidget` and `.when(data: ...)` pattern for async state.
- Calculations and logs are often extracted from domain objects (e.g., `scheme.calsLog`).
- Units and preferences are managed via global providers and enums (see `globalData.unit`, `PreferredUnit`).

## Build & Test Workflow
- Standard Flutter build/test commands apply:
  - Build: `flutter build <platform>`
  - Run: `flutter run`
  - Test: `flutter test`
- No custom build scripts or non-standard test runners detected.

## Integration Points
- External dependencies: Flutter, Riverpod, vector_math, and custom domain models.
- Platform-specific code in `android/`, `ios/`, `linux/`, `macos/`, `windows/` folders.
- Assets (fonts, XML, images) are in `assets/` and referenced in UI and domain logic.

## Examples of Important Patterns
- **Provider Usage:**
  ```dart
  final data = ref.watch(someProvider);
  return data.when(
    data: (value) => ...,
    error: (err, stack) => ...,
    loading: () => ...,
  );
  ```
- **CustomPainter for Drawing:**
  ```dart
  class MyPainter extends CustomPainter {
    @override
    void paint(Canvas canvas, Size size) {
      // Drawing logic
    }
    @override
    bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
  }
  ```
- **Global Data Access:**
  ```dart
  final unit = globalData.unit;
  ```

## Directory References
- `lib/domain_layer/`: Domain models, calculation logic
- `lib/presentation_layer/`: UI widgets, drawing logic
- `assets/`: Fonts, images, XML data

## Notes
- No project-specific agent rules or conventions found in existing docs.
- For new features, follow the layer separation and provider-based data flow.
- When adding new calculations, prefer placing logic in domain/data layer, not UI.

---

If any section is unclear or missing, please specify which part needs more detail or examples.
