import 'package:freezed_annotation/freezed_annotation.dart';
// import 'package:flutter/foundation.dart';
part 'beam_scheme_data.freezed.dart';
part 'beam_scheme_data.g.dart';

@freezed
abstract class BeamSchemeData with _$BeamSchemeData {
  const BeamSchemeData._();
  factory BeamSchemeData({
    @Default('') String usage,
    @Default(0.0) double finish,
    @Default(0.0) double service,
    @Default(0.0) double liveLoad,
    @Default('') String loadingTableId, 
    @Default(5.0) double shortSpan,
    @Default(12.0) double longSpan,
    @Default(2) int bays,
    @Default(500.0) double mainStrZone,
    @Default(500.0) double secStrZone,
    @Default(45.0) double fcu,
    @Default(40.0) double cover,
    @Default(200.0) double mainWidth,
    @Default(200.0) double secWidth,
    @Default('') String mainTopBar,
    @Default('') String mainBottomBar,
    @Default('') String mainLinks,
    @Default('') String secTopBar,
    @Default('') String secBottomBar,
    @Default('') String secLinks,
    @Default('') String beamSchemeId, //will be overriden  as soon as new instance created
    @Default('') String calsLog,
    @Default(false) bool isSelected,

  }) = _BeamSchemeData; 


  factory BeamSchemeData.fromJson(Map<String, Object?> json) =>
      _$BeamSchemeDataFromJson(json);
}
