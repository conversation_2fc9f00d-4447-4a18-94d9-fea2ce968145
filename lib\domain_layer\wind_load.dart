import 'dart:math';
import 'package:freezed_annotation/freezed_annotation.dart';
// import 'package:flutter/foundation.dart';
part 'wind_load.freezed.dart';
part 'wind_load.g.dart';

@freezed
abstract class WindLoad with _$WindLoad {
  const WindLoad._();
  factory WindLoad({
    @Default(0.0) double h, // (Will be from Global Input) Building Height
    @Default(0.0)
    double bTop, // (Will be from Global Input) building width at top
    @Default(0.0)
    double dTop, // (Will be from Global Input) building depth at top
    @Default(0.0) double sS, // (Will be from Global Input) size factor
    @Default('Concrete')
    String
    bldgType, // (Will be from Global Input) building depth at height considered
    @Default(0.0) double amplificationFactor, // (Will be from Global Input)
    @Default(0.0) double z, // height where pressure is considered
    @Default(0.0) double b, // width where pressure is considered
    @Default(0.0) double d, // depth where pressure is considered
    @Default('') String id,
  }) = _WindLoad;

  factory WindLoad.fromJson(Map<String, Object?> json) =>
      _$WindLoadFromJson(json);

  double get zE => h; //Effective Height (After addressing Shelter Effect)
  double get sTheta => 0.85; //directional factor
  double get sT => 1; // topographic factor
  double get qZo => 3.7 * pow(z / 500, 0.16);
  double get cF {
    if (h == 0 || bTop == 0 || dTop == 0) {
      return 0;
    }
    final double rH = min(zE / dTop, 12), rB = bTop / dTop;
    return 1.1 +
        ((0.055 * rH) /
            exp(
              pow(
                log(0.6 * rB * (1 - 0.011 * rH)).abs(),
                1.7 - 0.0013 * pow(rH, 2),
              ),
            ));
  }

  double get sQz {
    if (h == 0 || bTop == 0 || nX == 0 || xI == 0) {
      return 0;
    }
    return sQh - 1.2 * (sQh - pow(10 / h, 0.14)) * (1 - z / h);
  }

  double get sQh {
    if (h == 0 || bTop == 0 || nX == 0 || xI == 0) {
      return 0;
    }
    return 0.5 +
        sqrt(pow(sS - 0.5, 2) + 0.25 / (sqrt(bTop) * h * pow(nX, 2) * xI));
  }

  // Fundamental Period - based on ASCE 7-10
  // Tx = C_t * H^x
  double get tX {
    // Based on ASCE 7-10
    if (bldgType == 'Concrete') {
      return 0.0466 * pow(h, 0.9);
    } else if (bldgType == 'Steel') {
      return 0.0724 * pow(h, 0.8);
    } else {
      return 0.0466 * pow(h, 0.9);
    }
  }

  // Fundamental Frequency
  double get nX {
    if (tX == 0) {
      return 0;
    }
    return 1 / tX;
  }

  // damping ratio
  double get xI {
    // Based on ASCE 7-10
    if (bldgType == 'Concrete') {
      return 0.05;
    } else if (bldgType == 'Steel') {
      return 0.03;
    } else {
      return 0.05;
    }
  }

  double get qZ => qZo * sT * sTheta; //Wind Pressure at height Z
  double get qZFactored =>
      qZ * amplificationFactor; // Factored wind pressure at height Z
}
