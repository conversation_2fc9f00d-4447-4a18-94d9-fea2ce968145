import 'package:riverpod_annotation/riverpod_annotation.dart';

//presentation layer
import '../../domain_layer/steel_column_scheme_input_global.dart';
import '../screen/homescreen.dart';

// domain layer

part 'steel_column_scheme_input_global_controller.g.dart';

@riverpod
class SteelColumnSchemeInputGlobalController
    extends _$SteelColumnSchemeInputGlobalController {
  @override
  FutureOr<SteelColumnSchemeInputGlobal> build() async {
    // print('Build: Column Scheme Input Global');
    SteelColumnSchemeInputGlobal steelColumnSchemeGlobalInput =
        await ref
            .read(appDatabaseControllerProvider.notifier)
            .querySteelColumnSchemeInputGlobal();
    return steelColumnSchemeGlobalInput;
  }

  Future<void> updateTable({
    String? id,
    String? steelGrade,
    double? unbracedLength,
  }) async {
    final x = await future;
    SteelColumnSchemeInputGlobal newState = x.copyWith(
      id: id ?? x.id,
      steelGrade: steelGrade ?? x.steelGrade,
      unbracedLength: unbracedLength ?? x.unbracedLength,
    );
    // Update the state only if there are changes
    state = AsyncData(newState);
  }
}
