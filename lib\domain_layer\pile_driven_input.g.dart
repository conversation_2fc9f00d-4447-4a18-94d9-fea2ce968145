// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'pile_driven_input.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_PileDrivenInput _$PileDrivenInputFromJson(Map<String, dynamic> json) =>
    _PileDrivenInput(
      sptNValue: (json['sptNValue'] as num?)?.toDouble() ?? 50,
      fos: (json['fos'] as num?)?.toDouble() ?? 2,
      maxPileLength: (json['maxPileLength'] as num?)?.toDouble() ?? 30,
      slsLoad: (json['slsLoad'] as num?)?.toDouble() ?? 1000,
      ulsLoad: (json['ulsLoad'] as num?)?.toDouble() ?? 2000,
      useSelectColLoad: json['useSelectColLoad'] as bool? ?? false,
      id: json['id'] as String? ?? '1',
    );

Map<String, dynamic> _$PileDrivenInputToJson(_PileDrivenInput instance) =>
    <String, dynamic>{
      'sptNValue': instance.sptNValue,
      'fos': instance.fos,
      'maxPileLength': instance.maxPileLength,
      'slsLoad': instance.slsLoad,
      'ulsLoad': instance.ulsLoad,
      'useSelectColLoad': instance.useSelectColLoad,
      'id': instance.id,
    };
