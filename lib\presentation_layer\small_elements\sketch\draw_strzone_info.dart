import 'dart:math' as math;
import 'dart:typed_data';
import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:structify/domain_layer/steel_transfer_truss_scheme_input.dart';
import 'package:structify/domain_layer/steel_transfer_truss_scheme_input_global.dart';
import 'package:structify/misc/custom_func.dart';
import 'package:structify/presentation_layer/screen/homescreen.dart';
import 'package:vector_math/vector_math.dart' as vec;

import '../../../domain_layer/global_data.dart';
import '../../../domain_layer/preferences.dart';

class DrawStrZoneInfo extends ConsumerWidget {
  DrawStrZoneInfo({
    required this.sketchWidth,
    required this.sketchHeight,
    this.fontSize,
    super.key,
  });

  final double sketchWidth;
  final double sketchHeight;
  double? fontSize;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final ColorScheme colorScheme = Theme.of(context).colorScheme;
    final TextTheme textTheme = Theme.of(context).textTheme;
    final constraints = BoxConstraints(
      maxWidth: sketchWidth,
      maxHeight: sketchHeight,
      minHeight: 100,
      minWidth: 100,
    );
    return Center(
      child: Container(
        decoration: BoxDecoration(
          color: colorScheme.surfaceContainer.withAlpha(100),
          borderRadius: BorderRadius.circular(10.0),
          border: Border.all(color: Colors.black.withAlpha(100)),
        ),
        width:
            constraints.maxWidth == double.infinity
                ? 100
                : constraints.maxWidth,
        height:
            constraints.maxHeight == double.infinity
                ? 100
                : constraints.maxHeight,
        child: CustomPaint(
          painter: DrawStrZoneInfoPainter(
            boxConstraints: constraints,
            fontSize: fontSize,
            context: context,
          ),
        ),
      ),
    );
  }
}

class DrawStrZoneInfoPainter extends CustomPainter {
  DrawStrZoneInfoPainter({
    required this.boxConstraints,
    this.fontSize,
    required this.context,
  });

  final BoxConstraints boxConstraints;
  double? fontSize;
  final BuildContext context;

  @override
  void paint(Canvas canvas, Size size) {
    final ColorScheme colorScheme = Theme.of(context).colorScheme;
    final TextTheme textTheme = Theme.of(context).textTheme;
    late Offset startP1, endP1, startP2, endP2, startP3, endP3, startP4, endP4;
    late Offset offset1;
    Float32List points;
    fontSize = fontSize ?? math.min(12, math.min(size.width, size.height) / 8);
    double div = 20, dottedLineLength, dottedLineSpace;
    Paint steelMemberPaint =
        Paint()
          ..color = colorScheme.onSurface
          ..strokeCap = StrokeCap.round
          ..style = PaintingStyle.stroke
          ..strokeWidth = 1.0;

    Paint strPaint =
        Paint()
          ..color = colorScheme.onSurface
          ..strokeCap = StrokeCap.round
          ..style = PaintingStyle.stroke
          ..strokeWidth = 2.0;

    Paint dottedLine =
        Paint()
          ..color = colorScheme.onSurface.withAlpha(150)
          ..strokeCap = StrokeCap.round
          ..style = PaintingStyle.stroke
          ..strokeWidth = 1.0;
    // ******************
    //todo: draw str
    // ******************
    //* beam top
    startP1 = Offset(size.width / 20, size.height / 12);
    points = Float32List.fromList(
      _getPointsFromOffset(startP1, [
        Offset(size.width - 2 * size.width / 20, 0),
      ]),
    );
    canvas.drawRawPoints(PointMode.polygon, points, strPaint);
    startP3 = startP1;

    //* floor indicator
    points = Float32List.fromList(
      _getPointsFromOffset(startP1 + Offset(size.width / 20, 0), [
        Offset(-size.width / 40, -size.height / 40),
        Offset(size.width / 40 * 2, 0),
        Offset(-size.width / 40, size.height / 40),
      ]),
    );
    canvas.drawRawPoints(PointMode.polygon, points, steelMemberPaint);
    _drawText(
      canvas,
      minFromNum([size.width / 40, size.height / 40]),
      startP1,
      startP1 + Offset(size.width / 10, 0),
      minFromNum([size.width / 30, size.height / 30]),
      0,
      'Floor concerned',
      context,
    );

    //* beam bottom
    startP1 = startP1 + Offset(0, size.height / 20 * 4.0);
    points = Float32List.fromList(
      _getPointsFromOffset(startP1, [
        Offset(size.width - 2 * size.width / 20, 0),
      ]),
    );
    canvas.drawRawPoints(PointMode.polygon, points, strPaint);
    endP3 = startP1;
    startP4 = startP1;

    //* mark slab depth
    _drawDimLine(
      canvas,
      fontSize!,
      startP3,
      endP3,
      -size.width / 15,
      fontSize! / 2,
      'Str Zone',
      context,
      
    );

    // ******************
    // todo: draw service zone
    // ******************
    //* service
    startP1 = startP1 + Offset(0, size.height / 20 * 2);
    points = Float32List.fromList(
      _getPointsFromOffset(startP1, [
        Offset(size.width - 2 * size.width / 20, 0),
      ]),
    );
    canvas.drawRawPoints(PointMode.polygon, points, steelMemberPaint);
    endP4 = startP1;

    startP2 = startP1 + Offset(size.width / 20, 0);

    //* mark
    _drawDimLine(
      canvas,
      fontSize!,
      startP4,
      endP4,
      -size.width / 15,
      fontSize! / 2,
      'Service',
      context,
    );

    // ******************
    // todo: Finish
    // ******************
    //* finish top
    startP1 = startP1 + Offset(0, size.height / 20 * 11);
    points = Float32List.fromList(
      _getPointsFromOffset(startP1, [
        Offset(size.width - 2 * size.width / 20, 0),
      ]),
    );

    //* indicate clear
    startP4 = endP4;
    endP4 = startP1;
    _drawDimLine(
      canvas,
      fontSize!,
      startP4,
      endP4,
      -size.width / 15,
      fontSize! / 2,
      'Clear',
      context,
    );

    startP4 = startP1;
    endP2 = startP1 + Offset(size.width / 20, 0);
    canvas.drawRawPoints(PointMode.polygon, points, steelMemberPaint);

    //* lower floor top
    startP1 = startP1 + Offset(0, size.height / 20 / 2);
    points = Float32List.fromList(
      _getPointsFromOffset(startP1, [
        Offset(size.width - 2 * size.width / 20, 0),
      ]),
    );
    canvas.drawRawPoints(PointMode.polygon, points, strPaint);
    endP3 = startP1;
    endP4 = startP1;

    //* mark bottom chord
    _drawDimLine(
      canvas,
      fontSize!,
      startP4,
      endP4,
      -size.width / 15,
      fontSize! / 2,
      'Finish',
      context,
    );

    //* mark truess total depth
    _drawDimLine(
      canvas,
      fontSize!,
      startP3,
      endP3,
      -size.width / 15 * 2,
      fontSize! / 2,
      'Height',
      context,
    );
  }

  // void _rebarDesignation(
  //   List<int> rebarDia,
  //   int i,
  //   List<int> rebarSpacing,
  //   ui.Color fontColor,
  //   ui.Canvas canvas,
  //   ui.Offset endP3,
  // ) {
  //   TextSpan textSpan = TextSpan(
  //     text: 'T${rebarDia[i]}-${rebarSpacing[i]}',
  //     style: TextStyle(
  //       fontSize: fontSize,
  //       color: fontColor,
  //       fontWeight: FontWeight.bold,
  //     ),
  //   );

  //   TextPainter textPainter = TextPainter(
  //     text: textSpan,
  //     textAlign: TextAlign.center,
  //     textDirection: TextDirection.ltr,
  //   )..layout();

  //   textPainter.paint(canvas, endP3 + Offset(7, -7));
  // }

  void _drawDimLine(
    Canvas canvas,
    double fontSize,
    Offset start,
    Offset end,
    double offsetDistance,
    double dimExtension,
    String label,
    BuildContext context, {
    Color? fontColor,
  }) {
    double tempOffset = 10; // forthe dim line end
    late Offset tempEnd;
    late Offset tempStart;
    late Offset textCenter;

    late double angle;

    late List<String> unit;


    late final vec.Vector2 u;
    late final vec.Vector2 uUnit;
    late final vec.Vector2 vUnit;
    late final vec.Vector2 vUnit45;
    late final vec.Matrix2 m;

    final ColorScheme colorScheme = Theme.of(context).colorScheme;
    final TextTheme textTheme = Theme.of(context).textTheme;
    fontColor ??= colorScheme.onSurface.withAlpha(150);

    final Paint paint =
        Paint()
          ..color = fontColor
          ..strokeWidth = 1.0
          ..style = PaintingStyle.stroke
          ..strokeJoin = StrokeJoin.round;

    // line vector
    u = vec.Vector2(end.dx - start.dx, end.dy - start.dy);
    uUnit = u / u.length;

    // orthogonal vector
    vUnit = vec.Vector2(uUnit.y, uUnit.x); //rotate aniti-clockwise

    // Vector of parallel line with Offset
    Offset newStart = Offset(
      start.dx - vUnit.x * offsetDistance - uUnit.x * dimExtension,
      start.dy - vUnit.y * offsetDistance - uUnit.y * dimExtension,
    );
    Offset newEnd = Offset(
      end.dx - vUnit.x * offsetDistance + uUnit.x * dimExtension,
      end.dy - vUnit.y * offsetDistance + uUnit.y * dimExtension,
    );
    canvas.drawLine(newStart, newEnd, paint);

    // draw start crossing
    tempStart = Offset(
      (start.dx - vUnit.x * offsetDistance) - vUnit.x * dimExtension,
      (start.dy - vUnit.y * offsetDistance) - vUnit.y * dimExtension,
    );
    tempEnd = Offset(
      (start.dx - vUnit.x * offsetDistance) + vUnit.x * dimExtension,
      (start.dy - vUnit.y * offsetDistance) + vUnit.y * dimExtension,
    );
    canvas.drawLine(tempStart, tempEnd, paint);

    m = vec.Matrix2.rotation(-45 * math.pi / 180);
    vUnit45 = m * vUnit;
    tempStart = Offset(
      (start.dx - vUnit.x * offsetDistance) - vUnit45.x * dimExtension,
      (start.dy - vUnit.y * offsetDistance) - vUnit45.y * dimExtension,
    );
    tempEnd = Offset(
      (start.dx - vUnit.x * offsetDistance) + vUnit45.x * dimExtension,
      (start.dy - vUnit.y * offsetDistance) + vUnit45.y * dimExtension,
    );
    canvas.drawLine(tempStart, tempEnd, paint);

    // draw end crossing
    tempStart = Offset(
      (end.dx - vUnit.x * offsetDistance) - vUnit.x * dimExtension,
      (end.dy - vUnit.y * offsetDistance) - vUnit.y * dimExtension,
    );
    tempEnd = Offset(
      (end.dx - vUnit.x * offsetDistance) + vUnit.x * dimExtension,
      (end.dy - vUnit.y * offsetDistance) + vUnit.y * dimExtension,
    );
    canvas.drawLine(tempStart, tempEnd, paint);

    tempStart = Offset(
      (end.dx - vUnit.x * offsetDistance) - vUnit45.x * dimExtension,
      (end.dy - vUnit.y * offsetDistance) - vUnit45.y * dimExtension,
    );
    tempEnd = Offset(
      (end.dx - vUnit.x * offsetDistance) + vUnit45.x * dimExtension,
      (end.dy - vUnit.y * offsetDistance) + vUnit45.y * dimExtension,
    );
    canvas.drawLine(tempStart, tempEnd, paint);

    // indicate span length and beam size

    TextSpan textSpan = TextSpan(
      text: label,
      style: TextStyle(
        fontSize: fontSize,
        color: fontColor,
        fontWeight: FontWeight.bold,
      ),
    );

    TextPainter textPainter = TextPainter(
      text: textSpan,
      textAlign: TextAlign.center,
      textDirection: TextDirection.ltr,
    )..layout();

    textCenter = Offset(
      (start.dx + end.dx) / 2 -
          vUnit.x * (offsetDistance * 1.0 + 12) -
          uUnit.x * textPainter.width / 2,
      (start.dy + end.dy) / 2 -
          vUnit.y * (offsetDistance * 1.0 + 12) -
          uUnit.y * textPainter.width / 2,
    );
    angle = math.atan2(end.dy - start.dy, end.dx - start.dx) * 180 / math.pi;

    canvas.save();
    canvas.translate(textCenter.dx, textCenter.dy);
    canvas.rotate(angle * math.pi / 180);
    textPainter.paint(canvas, Offset.zero);
    canvas.restore();
  }

  void _drawText(
    Canvas canvas,
    double fontSize,
    Offset start,
    Offset end,
    double offsetDistance,
    double dimExtension,
    String label,
    BuildContext context, {
    Color? fontColor,
  }) {
    double tempOffset = 10; // forthe dim line end
    late Offset tempEnd;
    late Offset tempStart;
    late Offset textCenter;

    late double angle;

    late List<String> unit;

    fontColor ??=  Colors.red.withAlpha(150);

    late final vec.Vector2 u;
    late final vec.Vector2 uUnit;
    late final vec.Vector2 vUnit;
    late final vec.Vector2 vUnit45;
    late final vec.Matrix2 m;

    final ColorScheme colorScheme = Theme.of(context).colorScheme;
    final TextTheme textTheme = Theme.of(context).textTheme;

    // double dimExtension = offsetDistance.abs() / 2;

    final Paint paint =
        Paint()
          ..color = fontColor
          ..strokeWidth = 1.0
          ..style = PaintingStyle.stroke
          ..strokeJoin = StrokeJoin.round;

    // line vector
    u = vec.Vector2(end.dx - start.dx, end.dy - start.dy);
    uUnit = u / u.length;

    // orthogonal vector
    vUnit = vec.Vector2(uUnit.y, uUnit.x); //rotate aniti-clockwise

    // Vector of parallel line with Offset
    Offset newStart = Offset(
      start.dx - vUnit.x * offsetDistance - uUnit.x * dimExtension,
      start.dy - vUnit.y * offsetDistance - uUnit.y * dimExtension,
    );
    Offset newEnd = Offset(
      end.dx - vUnit.x * offsetDistance + uUnit.x * dimExtension,
      end.dy - vUnit.y * offsetDistance + uUnit.y * dimExtension,
    );

    // indicate span length and beam size

    TextSpan textSpan = TextSpan(
      text: label,
      style: TextStyle(
        fontSize: fontSize,
        color: fontColor,
        fontWeight: FontWeight.bold,
      ),
    );

    TextPainter textPainter = TextPainter(
      text: textSpan,
      textAlign: TextAlign.center,
      textDirection: TextDirection.ltr,
    )..layout();

    textCenter = Offset(
      (start.dx + end.dx) / 2 -
          vUnit.x * (offsetDistance * 1.0 + 12) -
          uUnit.x * textPainter.width / 2,
      (start.dy + end.dy) / 2 -
          vUnit.y * (offsetDistance * 1.0 + 12) -
          uUnit.y * textPainter.width / 2,
    );
    angle = math.atan2(end.dy - start.dy, end.dx - start.dx) * 180 / math.pi;

    canvas.save();
    canvas.translate(textCenter.dx, textCenter.dy);
    canvas.rotate(angle * math.pi / 180);
    textPainter.paint(canvas, Offset.zero);
    canvas.restore();
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    // TODO: implement shouldRepaint
    return false;
  }

  Float32List _getPointsFromOffset(Offset start, List<Offset> offsets) {
    List<Offset> points = [start];
    for (Offset offset in offsets) {
      start = start + offset;
      points.add(start);
    }
    return Float32List.fromList(points.expand((i) => [i.dx, i.dy]).toList());
  }
}
