// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'column_scheme_input_global.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$ColumnSchemeInputGlobal {

 int get columnSchemeInputGlobalId; double get cover; double get minColumnSize; double get sizeIncrement; int get iterationSteps; String get concreteGrade; double get minSteelRatio; double get maxSteelRatio; double get safetyFactor; double get minClearS; bool get useSlabSelected;
/// Create a copy of ColumnSchemeInputGlobal
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ColumnSchemeInputGlobalCopyWith<ColumnSchemeInputGlobal> get copyWith => _$ColumnSchemeInputGlobalCopyWithImpl<ColumnSchemeInputGlobal>(this as ColumnSchemeInputGlobal, _$identity);

  /// Serializes this ColumnSchemeInputGlobal to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ColumnSchemeInputGlobal&&(identical(other.columnSchemeInputGlobalId, columnSchemeInputGlobalId) || other.columnSchemeInputGlobalId == columnSchemeInputGlobalId)&&(identical(other.cover, cover) || other.cover == cover)&&(identical(other.minColumnSize, minColumnSize) || other.minColumnSize == minColumnSize)&&(identical(other.sizeIncrement, sizeIncrement) || other.sizeIncrement == sizeIncrement)&&(identical(other.iterationSteps, iterationSteps) || other.iterationSteps == iterationSteps)&&(identical(other.concreteGrade, concreteGrade) || other.concreteGrade == concreteGrade)&&(identical(other.minSteelRatio, minSteelRatio) || other.minSteelRatio == minSteelRatio)&&(identical(other.maxSteelRatio, maxSteelRatio) || other.maxSteelRatio == maxSteelRatio)&&(identical(other.safetyFactor, safetyFactor) || other.safetyFactor == safetyFactor)&&(identical(other.minClearS, minClearS) || other.minClearS == minClearS)&&(identical(other.useSlabSelected, useSlabSelected) || other.useSlabSelected == useSlabSelected));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,columnSchemeInputGlobalId,cover,minColumnSize,sizeIncrement,iterationSteps,concreteGrade,minSteelRatio,maxSteelRatio,safetyFactor,minClearS,useSlabSelected);

@override
String toString() {
  return 'ColumnSchemeInputGlobal(columnSchemeInputGlobalId: $columnSchemeInputGlobalId, cover: $cover, minColumnSize: $minColumnSize, sizeIncrement: $sizeIncrement, iterationSteps: $iterationSteps, concreteGrade: $concreteGrade, minSteelRatio: $minSteelRatio, maxSteelRatio: $maxSteelRatio, safetyFactor: $safetyFactor, minClearS: $minClearS, useSlabSelected: $useSlabSelected)';
}


}

/// @nodoc
abstract mixin class $ColumnSchemeInputGlobalCopyWith<$Res>  {
  factory $ColumnSchemeInputGlobalCopyWith(ColumnSchemeInputGlobal value, $Res Function(ColumnSchemeInputGlobal) _then) = _$ColumnSchemeInputGlobalCopyWithImpl;
@useResult
$Res call({
 int columnSchemeInputGlobalId, double cover, double minColumnSize, double sizeIncrement, int iterationSteps, String concreteGrade, double minSteelRatio, double maxSteelRatio, double safetyFactor, double minClearS, bool useSlabSelected
});




}
/// @nodoc
class _$ColumnSchemeInputGlobalCopyWithImpl<$Res>
    implements $ColumnSchemeInputGlobalCopyWith<$Res> {
  _$ColumnSchemeInputGlobalCopyWithImpl(this._self, this._then);

  final ColumnSchemeInputGlobal _self;
  final $Res Function(ColumnSchemeInputGlobal) _then;

/// Create a copy of ColumnSchemeInputGlobal
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? columnSchemeInputGlobalId = null,Object? cover = null,Object? minColumnSize = null,Object? sizeIncrement = null,Object? iterationSteps = null,Object? concreteGrade = null,Object? minSteelRatio = null,Object? maxSteelRatio = null,Object? safetyFactor = null,Object? minClearS = null,Object? useSlabSelected = null,}) {
  return _then(_self.copyWith(
columnSchemeInputGlobalId: null == columnSchemeInputGlobalId ? _self.columnSchemeInputGlobalId : columnSchemeInputGlobalId // ignore: cast_nullable_to_non_nullable
as int,cover: null == cover ? _self.cover : cover // ignore: cast_nullable_to_non_nullable
as double,minColumnSize: null == minColumnSize ? _self.minColumnSize : minColumnSize // ignore: cast_nullable_to_non_nullable
as double,sizeIncrement: null == sizeIncrement ? _self.sizeIncrement : sizeIncrement // ignore: cast_nullable_to_non_nullable
as double,iterationSteps: null == iterationSteps ? _self.iterationSteps : iterationSteps // ignore: cast_nullable_to_non_nullable
as int,concreteGrade: null == concreteGrade ? _self.concreteGrade : concreteGrade // ignore: cast_nullable_to_non_nullable
as String,minSteelRatio: null == minSteelRatio ? _self.minSteelRatio : minSteelRatio // ignore: cast_nullable_to_non_nullable
as double,maxSteelRatio: null == maxSteelRatio ? _self.maxSteelRatio : maxSteelRatio // ignore: cast_nullable_to_non_nullable
as double,safetyFactor: null == safetyFactor ? _self.safetyFactor : safetyFactor // ignore: cast_nullable_to_non_nullable
as double,minClearS: null == minClearS ? _self.minClearS : minClearS // ignore: cast_nullable_to_non_nullable
as double,useSlabSelected: null == useSlabSelected ? _self.useSlabSelected : useSlabSelected // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}

}


/// Adds pattern-matching-related methods to [ColumnSchemeInputGlobal].
extension ColumnSchemeInputGlobalPatterns on ColumnSchemeInputGlobal {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _ColumnSchemeInputGlobal value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _ColumnSchemeInputGlobal() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _ColumnSchemeInputGlobal value)  $default,){
final _that = this;
switch (_that) {
case _ColumnSchemeInputGlobal():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _ColumnSchemeInputGlobal value)?  $default,){
final _that = this;
switch (_that) {
case _ColumnSchemeInputGlobal() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( int columnSchemeInputGlobalId,  double cover,  double minColumnSize,  double sizeIncrement,  int iterationSteps,  String concreteGrade,  double minSteelRatio,  double maxSteelRatio,  double safetyFactor,  double minClearS,  bool useSlabSelected)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _ColumnSchemeInputGlobal() when $default != null:
return $default(_that.columnSchemeInputGlobalId,_that.cover,_that.minColumnSize,_that.sizeIncrement,_that.iterationSteps,_that.concreteGrade,_that.minSteelRatio,_that.maxSteelRatio,_that.safetyFactor,_that.minClearS,_that.useSlabSelected);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( int columnSchemeInputGlobalId,  double cover,  double minColumnSize,  double sizeIncrement,  int iterationSteps,  String concreteGrade,  double minSteelRatio,  double maxSteelRatio,  double safetyFactor,  double minClearS,  bool useSlabSelected)  $default,) {final _that = this;
switch (_that) {
case _ColumnSchemeInputGlobal():
return $default(_that.columnSchemeInputGlobalId,_that.cover,_that.minColumnSize,_that.sizeIncrement,_that.iterationSteps,_that.concreteGrade,_that.minSteelRatio,_that.maxSteelRatio,_that.safetyFactor,_that.minClearS,_that.useSlabSelected);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( int columnSchemeInputGlobalId,  double cover,  double minColumnSize,  double sizeIncrement,  int iterationSteps,  String concreteGrade,  double minSteelRatio,  double maxSteelRatio,  double safetyFactor,  double minClearS,  bool useSlabSelected)?  $default,) {final _that = this;
switch (_that) {
case _ColumnSchemeInputGlobal() when $default != null:
return $default(_that.columnSchemeInputGlobalId,_that.cover,_that.minColumnSize,_that.sizeIncrement,_that.iterationSteps,_that.concreteGrade,_that.minSteelRatio,_that.maxSteelRatio,_that.safetyFactor,_that.minClearS,_that.useSlabSelected);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _ColumnSchemeInputGlobal extends ColumnSchemeInputGlobal {
   _ColumnSchemeInputGlobal({this.columnSchemeInputGlobalId = 1, this.cover = 30, this.minColumnSize = 300, this.sizeIncrement = 100, this.iterationSteps = 10, this.concreteGrade = 'C45', this.minSteelRatio = 0.02, this.maxSteelRatio = 0.04, this.safetyFactor = 1.0, this.minClearS = 60, this.useSlabSelected = false}): super._();
  factory _ColumnSchemeInputGlobal.fromJson(Map<String, dynamic> json) => _$ColumnSchemeInputGlobalFromJson(json);

@override@JsonKey() final  int columnSchemeInputGlobalId;
@override@JsonKey() final  double cover;
@override@JsonKey() final  double minColumnSize;
@override@JsonKey() final  double sizeIncrement;
@override@JsonKey() final  int iterationSteps;
@override@JsonKey() final  String concreteGrade;
@override@JsonKey() final  double minSteelRatio;
@override@JsonKey() final  double maxSteelRatio;
@override@JsonKey() final  double safetyFactor;
@override@JsonKey() final  double minClearS;
@override@JsonKey() final  bool useSlabSelected;

/// Create a copy of ColumnSchemeInputGlobal
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ColumnSchemeInputGlobalCopyWith<_ColumnSchemeInputGlobal> get copyWith => __$ColumnSchemeInputGlobalCopyWithImpl<_ColumnSchemeInputGlobal>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ColumnSchemeInputGlobalToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ColumnSchemeInputGlobal&&(identical(other.columnSchemeInputGlobalId, columnSchemeInputGlobalId) || other.columnSchemeInputGlobalId == columnSchemeInputGlobalId)&&(identical(other.cover, cover) || other.cover == cover)&&(identical(other.minColumnSize, minColumnSize) || other.minColumnSize == minColumnSize)&&(identical(other.sizeIncrement, sizeIncrement) || other.sizeIncrement == sizeIncrement)&&(identical(other.iterationSteps, iterationSteps) || other.iterationSteps == iterationSteps)&&(identical(other.concreteGrade, concreteGrade) || other.concreteGrade == concreteGrade)&&(identical(other.minSteelRatio, minSteelRatio) || other.minSteelRatio == minSteelRatio)&&(identical(other.maxSteelRatio, maxSteelRatio) || other.maxSteelRatio == maxSteelRatio)&&(identical(other.safetyFactor, safetyFactor) || other.safetyFactor == safetyFactor)&&(identical(other.minClearS, minClearS) || other.minClearS == minClearS)&&(identical(other.useSlabSelected, useSlabSelected) || other.useSlabSelected == useSlabSelected));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,columnSchemeInputGlobalId,cover,minColumnSize,sizeIncrement,iterationSteps,concreteGrade,minSteelRatio,maxSteelRatio,safetyFactor,minClearS,useSlabSelected);

@override
String toString() {
  return 'ColumnSchemeInputGlobal(columnSchemeInputGlobalId: $columnSchemeInputGlobalId, cover: $cover, minColumnSize: $minColumnSize, sizeIncrement: $sizeIncrement, iterationSteps: $iterationSteps, concreteGrade: $concreteGrade, minSteelRatio: $minSteelRatio, maxSteelRatio: $maxSteelRatio, safetyFactor: $safetyFactor, minClearS: $minClearS, useSlabSelected: $useSlabSelected)';
}


}

/// @nodoc
abstract mixin class _$ColumnSchemeInputGlobalCopyWith<$Res> implements $ColumnSchemeInputGlobalCopyWith<$Res> {
  factory _$ColumnSchemeInputGlobalCopyWith(_ColumnSchemeInputGlobal value, $Res Function(_ColumnSchemeInputGlobal) _then) = __$ColumnSchemeInputGlobalCopyWithImpl;
@override @useResult
$Res call({
 int columnSchemeInputGlobalId, double cover, double minColumnSize, double sizeIncrement, int iterationSteps, String concreteGrade, double minSteelRatio, double maxSteelRatio, double safetyFactor, double minClearS, bool useSlabSelected
});




}
/// @nodoc
class __$ColumnSchemeInputGlobalCopyWithImpl<$Res>
    implements _$ColumnSchemeInputGlobalCopyWith<$Res> {
  __$ColumnSchemeInputGlobalCopyWithImpl(this._self, this._then);

  final _ColumnSchemeInputGlobal _self;
  final $Res Function(_ColumnSchemeInputGlobal) _then;

/// Create a copy of ColumnSchemeInputGlobal
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? columnSchemeInputGlobalId = null,Object? cover = null,Object? minColumnSize = null,Object? sizeIncrement = null,Object? iterationSteps = null,Object? concreteGrade = null,Object? minSteelRatio = null,Object? maxSteelRatio = null,Object? safetyFactor = null,Object? minClearS = null,Object? useSlabSelected = null,}) {
  return _then(_ColumnSchemeInputGlobal(
columnSchemeInputGlobalId: null == columnSchemeInputGlobalId ? _self.columnSchemeInputGlobalId : columnSchemeInputGlobalId // ignore: cast_nullable_to_non_nullable
as int,cover: null == cover ? _self.cover : cover // ignore: cast_nullable_to_non_nullable
as double,minColumnSize: null == minColumnSize ? _self.minColumnSize : minColumnSize // ignore: cast_nullable_to_non_nullable
as double,sizeIncrement: null == sizeIncrement ? _self.sizeIncrement : sizeIncrement // ignore: cast_nullable_to_non_nullable
as double,iterationSteps: null == iterationSteps ? _self.iterationSteps : iterationSteps // ignore: cast_nullable_to_non_nullable
as int,concreteGrade: null == concreteGrade ? _self.concreteGrade : concreteGrade // ignore: cast_nullable_to_non_nullable
as String,minSteelRatio: null == minSteelRatio ? _self.minSteelRatio : minSteelRatio // ignore: cast_nullable_to_non_nullable
as double,maxSteelRatio: null == maxSteelRatio ? _self.maxSteelRatio : maxSteelRatio // ignore: cast_nullable_to_non_nullable
as double,safetyFactor: null == safetyFactor ? _self.safetyFactor : safetyFactor // ignore: cast_nullable_to_non_nullable
as double,minClearS: null == minClearS ? _self.minClearS : minClearS // ignore: cast_nullable_to_non_nullable
as double,useSlabSelected: null == useSlabSelected ? _self.useSlabSelected : useSlabSelected // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}


}

// dart format on
