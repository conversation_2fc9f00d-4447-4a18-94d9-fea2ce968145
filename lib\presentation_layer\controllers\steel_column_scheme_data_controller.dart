import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:nanoid2/nanoid2.dart';
import 'dart:math';
import 'package:intl/intl.dart';
import 'package:structify/domain_layer/mixin/mixin_str_general_cals.dart';
import 'package:structify/domain_layer/steel_column_scheme_data.dart';

//presentation layer
import '../../domain_layer/global_data.dart';
import '../../domain_layer/loading_table.dart';
import '../../domain_layer/mixin/mixin_steel_str.dart';
import '../../domain_layer/preferences.dart';
import '../../domain_layer/steel_column_scheme_input.dart';
import '../../domain_layer/steel_column_scheme_input_global.dart';
import '../../misc/custom_func.dart';
import '../screen/homescreen.dart';

// domain layer
part 'steel_column_scheme_data_controller.g.dart';

@riverpod
class SteelColumnSchemeDataController extends _$SteelColumnSchemeDataController
    with StrGeneralCals, SteelStrHK {
  @override
  FutureOr<List<SteelColumnSchemeData>> build() async {
    // print('Build: Column Scheme Data');
    final columnSchemeDataList =
        await ref
            .read(appDatabaseControllerProvider.notifier)
            .querySteelColumnSchemeData();
    final data1 = ref.watch(globalDataControllerProvider);
    final data2 = ref.watch(loadingTablesControllerProvider);
    final data3 = ref.watch(steelColumnSchemeInputControllerProvider);
    final data4 = ref.watch(steelColumnSchemeInputGlobalControllerProvider);

    return data1.when(
      data: (globalData) {
        return data2.when(
          data: (loadingTables) {
            return data3.when(
              data: (inputs) {
                return data4.when(
                  data: (inputsGlobal) async {
                    return await bacthColumnScheming(
                      existingID:
                          columnSchemeDataList
                              .map((x) => x.steelColumnSchemeDataId)
                              .toSet(),
                    );
                  },
                  error: (error, stackTrace) => <SteelColumnSchemeData>[],
                  loading: () => <SteelColumnSchemeData>[],
                );
              },
              error: (error, stackTrace) => <SteelColumnSchemeData>[],
              loading: () => <SteelColumnSchemeData>[],
            );
          },
          error: (error, stackTrace) => <SteelColumnSchemeData>[],
          loading: () => <SteelColumnSchemeData>[],
        );
      },
      error: (error, stackTrace) => <SteelColumnSchemeData>[],
      loading: () => <SteelColumnSchemeData>[],
    );
  }

  Future<void> addColumnSchemeData(
    List<SteelColumnSchemeData> steelColumnSchemeData,
  ) async {
    final x = await future;
    state = AsyncData([...x, ...steelColumnSchemeData]);
  }

  Future<void> deleteTable(String id) async {
    final x = await future;
    x.removeWhere((item) => item.steelColumnSchemeDataId == id);
    state = AsyncData(x);
  }

  Future<void> deleteAllTable() async {
    state = AsyncData([]);
  }

  Future<String> _generateUniqueId({Set<String>? existingID}) async {
    late final Set<String> steelColumnSchemeDataId;
    if (existingID == null) {
      final x = await future;
      steelColumnSchemeDataId =
          x.map((item) => item.steelColumnSchemeDataId).toSet();
    } else {
      steelColumnSchemeDataId = existingID;
    }
    String newID = nanoid(alphabet: Alphabet.noDoppelgangerSafe);
    while (steelColumnSchemeDataId.contains(newID)) {
      newID = nanoid(alphabet: Alphabet.noDoppelgangerSafe);
    }
    return newID;
  }

  Future<List<SteelColumnSchemeData>> bacthColumnScheming({
    Set<String>? existingID,
  }) async {
    // await deleteAllTable();
    final tables = await ref.read(loadingTablesControllerProvider.future);
    final globalData = await ref.read(globalDataControllerProvider.future);
    final inputs = await ref.read(
      steelColumnSchemeInputControllerProvider.future,
    );
    final inputsGlobal = await ref.read(
      steelColumnSchemeInputGlobalControllerProvider.future,
    );

    final List<double> fsy;
    final RegExp regExp1 = RegExp(r'(?<=S)[\d]+');
    double sdl, ll, total, factoredTotal, axialCap = 0;
    bool status = false;
    List<SteelColumnSchemeData> steelColumnSchemeList = [];
    List<Map<String, dynamic>> steelSections = await getAllSteelISection();
    String steelSection = steelSections.first['name'];
    StringBuffer buffer = StringBuffer(); // record the cals

    fsy =
        inputsGlobal.steelGrade.split(',').map((e) {
          try {
            final x = regExp1.allMatches(e).first.group(0);
            return double.parse(x ?? '');
          } catch (e) {
            return 0.0;
          }
        }).toList();
    fsy.sort();
    //* get column loading
    sdl = inputs.fold(0, (double sum, input) {
      final tbl = tables.firstWhere((table) {
        return table.usage == input.usage;
      });
      sum +=
          (tbl.service +
              getPressureFromThick(tbl.finish, globalData.finishUnitWeight) +
              getPressureFromThick(
                input.slabThickness,
                globalData.rcUnitWeight,
              )) *
          input.loadWidth *
          input.loadLength *
          input.nosOfFloor;
      return sum;
    });
    ll = inputs.fold(0, (double sum, input) {
      final tbl = tables.firstWhere((table) {
        return table.usage == input.usage;
      });
      sum +=
          (tbl.liveLoad) *
          input.loadWidth *
          input.loadLength *
          input.nosOfFloor;
      return sum;
    });
    total = sdl + ll;
    factoredTotal = globalData.sdlFactor * sdl + globalData.llFactor * ll;

    //* loop:
    //* 1. Steel grades 
    //* 2. Available UC section
    for (int i = 0; i < fsy.length; i++) {
      status = false;
      for (int j = 0; j < steelSections.length; j++) {
        steelSection = steelSections[j]['name'];
        axialCap = await getAxialCapacitySteelUC(
          fsyGrade: fsy[i],
          unbracedLength: inputsGlobal.unbracedLength,
          sectionName: steelSection,
          steelSections: steelSections,
        );
        //* sections are sorted by depth then area, ascendingly
        //* adopt the section with min depth and A
        if (axialCap > factoredTotal) {
          status = true;
          break;
        }
      }
      if (status) {
        _recordCalsResult(
          buffer,
          inputs: inputs,
          inputsGlobal: inputsGlobal,
          globalData: globalData,
          loadingTables: tables,
          fsy: fsy[i],
          axialCap: axialCap,
          steelSection: steelSection,
        );
        steelColumnSchemeList.add(
          SteelColumnSchemeData(
            steelColumnSchemeDataId: await _generateUniqueId(
              existingID: existingID,
            ),
            sdl: roundTo(sdl, 0),
            ll: roundTo(ll, 0),
            slsLoad: roundTo(total, 0),
            ulsLoad: roundTo(factoredTotal, 0),
            fsy: roundTo(fsy[i], 0),
            unbracedLength: roundTo(inputsGlobal.unbracedLength, 2),
            axialCapacity: roundTo(axialCap, 0),
            steelSection: steelSection,
            calsLog: buffer.toString(),
          ),
        );
      }
    }
    state = AsyncData(steelColumnSchemeList);
    return steelColumnSchemeList;
  }

  void _recordCalsResult(
    StringBuffer buffer, {
    required List<SteelColumnSchemeInput> inputs,
    required SteelColumnSchemeInputGlobal inputsGlobal,
    required GlobalData globalData,
    required List<LoadingTable> loadingTables,
    required double fsy,
    required double axialCap,
    required String steelSection,
  }) {
    late final List<String> unit;
    late double sdl, ll, ulsLoad, slsLoad, loadArea;
    late LoadingTable loadingTable;
    double slsTotal = 0, ulsTotal = 0;
    switch (globalData.unit) {
      case 'metrics':
        unit = PreferredUnit.metrics;
        break;
      case 'imperial':
        unit = PreferredUnit.imperial;
        break;
      default:
        unit = PreferredUnit.metrics;
        break;
    }

    buffer.clear();

    for (int i = 0; i < inputs.length; i++) {
      loadingTable = loadingTables.firstWhere(
        (tbl) => tbl.usage == inputs[i].usage,
      );
      loadArea =
          inputs[i].loadLength * inputs[i].loadWidth * inputs[i].nosOfFloor;
      sdl =
          (inputs[i].slabThickness * globalData.rcUnitWeight * pow(10, -3) +
              loadingTable.finish * globalData.finishUnitWeight * pow(10, -3) +
              loadingTable.service) *
          loadArea;
      ll = loadingTable.liveLoad * loadArea;
      slsLoad = sdl + ll;
      ulsLoad = globalData.sdlFactor * sdl + globalData.llFactor * ll;
      slsTotal += slsLoad;
      ulsTotal += ulsLoad;
      buffer.write(
        'LoadArea (${i + 1}): ${NumberFormat('0').format(loadArea)} [${unit[7]}] | ',
      );
      buffer.write(
        'SDL (${i + 1}): ${NumberFormat('0').format(sdl)} [${unit[0]}] | ',
      );
      buffer.write(
        'LL (${i + 1}): ${NumberFormat('0').format(ll)} [${unit[0]}] | ',
      );
      buffer.write(
        'SLS Load (${i + 1}): ${NumberFormat('0').format(slsLoad)} [${unit[0]}] | ',
      );
      buffer.write(
        'ULS Load (${i + 1}): ${NumberFormat('0').format(ulsLoad)} [${unit[0]}]\n',
      );
    }
    buffer.write(
      'SLS Total: ${NumberFormat('0').format(slsTotal)} [${unit[0]}] | ',
    );
    buffer.write(
      'ULS Total: ${NumberFormat('0').format(ulsTotal)} [${unit[0]}]\n',
    );

    buffer.write('Fy: ${NumberFormat('0').format(fsy)} [${unit[5]}] | ');
    buffer.write(
      'Axial Capacity: ${NumberFormat('0').format(axialCap)} [${unit[0]}] | ',
    );

    buffer.write('Section Designation: $steelSection\n');

    if (axialCap > ulsTotal) {
      buffer.write('Steel Scolumn Scheme: pass\n');
    } else {
      buffer.write('Steel Scolumn Scheme: fail\n');
    }
  }
}
