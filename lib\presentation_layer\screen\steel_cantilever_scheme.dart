import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:printing/printing.dart';
import 'package:structify/data_layer/app_database.dart';
import 'package:drift_db_viewer/drift_db_viewer.dart';

//below for printing
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:structify/misc/custom_func.dart';

// import 'package:flutter/services.dart';

//domain layer
import '../../domain_layer/preferences.dart';

// presentation layer
import '../small_elements/dessign_assumption.dart';
import '../small_elements/button/function_button.dart';
import '../small_elements/steel_cantilever_truss_scheme_input_ui.dart';
import '../small_elements/steel_cantilever_truss_scheme_summary.dart';
import '../small_elements/steel_transfer_truss_scheme_input_ui.dart';
import '../small_elements/steel_transfer_truss_scheme_summary.dart';
import 'homescreen.dart';

class SteelCantileverScheme extends ConsumerWidget {
  SteelCantileverScheme({super.key});

  final GlobalKey _globalKey = GlobalKey();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final ColorScheme colorScheme = Theme.of(context).colorScheme;
    final TextTheme textTheme = Theme.of(context).textTheme;
    final globalData = ref.watch(globalDataControllerProvider);
    final steelCantileverTrussSchemeInput = ref.watch(
      steelCantileverTrussSchemeInputControllerProvider,
    );
    ref.watch(appWatcherProvider);
    return globalData.when(
      data: (globalData) {
        return steelCantileverTrussSchemeInput.when(
          data: (input) {
            return Scaffold(
              backgroundColor: colorScheme.surface,
              body: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        FunctionButton(
                          labelIcon: Icon(Icons.data_array_outlined),
                          labelText: 'Assumption',
                          onTap: (isPressed) async {
                            showAssumption(
                              context,
                              DesignAssumption(
                                isExpanded: true,
                                textStyle: textTheme.bodyMedium!.copyWith(
                                  color: colorScheme.onInverseSurface,
                                ),
                                titleStyle: textTheme.titleLarge!.copyWith(
                                  color: colorScheme.onInverseSurface,
                                ),
                                title: 'Assumption',
                                assumptions: [
                                  'HKCoP for Structural Use of Steel 2011 (HKCoPSUC2011)',
                                  'Each segment of the truss has end pinned condition (K=1.0) for both direction of buckling',
                                  'Axail Design: pc = pEpy / (\u03A6 + \u221A(\u03A6^2-pE*py)) (Appendix 8.4)',
                                  'Web design is not considered, but chord unbraced length is controlled by web member arrangement',
                                  'Compression controls the chord design, so chrods section size will be based on that of compression chord',
                                ],
                                tooltipText:
                                    '------if axial fails------\n-adjust the max unbraced length\n-adjust steel grade OR\n-if possible, adjust str zone (hence reduce str zone a bit from each of other floors)\n',
                              ),
                            );
                          },
                        ),
                        SizedBox(width: 5.0),
                        FunctionButton(
                          labelIcon: Icon(Icons.data_array_outlined),
                          labelText: 'Summary',
                          onTap: (isPressed) async {
                            showSummary(context);
                          },
                        ),
                        SizedBox(width: 5.0),
                        FunctionButton(
                          labelIcon: Icon(Icons.data_array_outlined),
                          labelText: 'Data',
                          onTap: (isPressed) async {
                            final db =
                                AppDatabase(); //This should be a singleton
                            Navigator.of(context).push(
                              MaterialPageRoute(
                                builder: (context) => DriftDbViewer(db),
                              ),
                            );
                          },
                        ),
                      ],
                    ),
                  ),

                  Flexible(
                    child: Row(
                      children: [
                        Flexible(
                          flex: 3,
                          child: SteelCantileverTrussSchemeInputUi(
                            isExpanded: true,
                          ),
                        ),
                        Flexible(
                          flex: 4,
                          child: SteelCantileverTrussSchemeSummary(
                            isExpanded: true,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            );
          },
          error: (error, stackTrace) => Text('Error: $error'),
          loading: () => CircularProgressIndicator(),
        );
      },
      error: (error, stackTrace) {
        return Text('Error: $error');
      },
      loading: () {
        return CircularProgressIndicator();
      },
    );
  }
}
