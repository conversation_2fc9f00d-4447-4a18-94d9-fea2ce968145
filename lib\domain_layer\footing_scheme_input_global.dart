import 'package:freezed_annotation/freezed_annotation.dart';
// import 'package:flutter/foundation.dart';
part 'footing_scheme_input_global.freezed.dart';
part 'footing_scheme_input_global.g.dart';

@freezed
abstract class FootingSchemeInputGlobal with _$FootingSchemeInputGlobal {
  const FootingSchemeInputGlobal._();
  factory FootingSchemeInputGlobal({
    @Default('1') String id,
    @Default(1.0) double  colLoadFactor,
  }) = _FootingSchemeInputGlobal; 

  factory FootingSchemeInputGlobal.fromJson(Map<String, Object?> json) =>
      _$FootingSchemeInputGlobalFromJson(json);
}
