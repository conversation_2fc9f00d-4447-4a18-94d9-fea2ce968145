// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'steel_column_scheme_data_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$steelColumnSchemeDataControllerHash() =>
    r'f17275d851e07cc27b2ab3decb26570b870f1e10';

/// See also [SteelColumnSchemeDataController].
@ProviderFor(SteelColumnSchemeDataController)
final steelColumnSchemeDataControllerProvider =
    AutoDisposeAsyncNotifierProvider<
      SteelColumnSchemeDataController,
      List<SteelColumnSchemeData>
    >.internal(
      SteelColumnSchemeDataController.new,
      name: r'steelColumnSchemeDataControllerProvider',
      debugGetCreateSourceHash:
          const bool.fromEnvironment('dart.vm.product')
              ? null
              : _$steelColumnSchemeDataControllerHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$SteelColumnSchemeDataController =
    AutoDisposeAsyncNotifier<List<SteelColumnSchemeData>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
