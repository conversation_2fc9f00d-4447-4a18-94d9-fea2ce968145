// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'column_scheme_data_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$columnSchemeDataControllerHash() =>
    r'5ea38ed17485981ebcabf2009fe67555c20671d0';

/// See also [ColumnSchemeDataController].
@ProviderFor(ColumnSchemeDataController)
final columnSchemeDataControllerProvider = AutoDisposeAsyncNotifierProvider<
  ColumnSchemeDataController,
  List<ColumnSchemeData>
>.internal(
  ColumnSchemeDataController.new,
  name: r'columnSchemeDataControllerProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$columnSchemeDataControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$ColumnSchemeDataController =
    AutoDisposeAsyncNotifier<List<ColumnSchemeData>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
