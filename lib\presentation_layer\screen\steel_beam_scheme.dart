import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:structify/data_layer/app_database.dart';
import 'package:drift_db_viewer/drift_db_viewer.dart';

//below for printing
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:printing/printing.dart';
// import 'dart:typed_data';
// import 'dart:ui';
import 'package:structify/presentation_layer/small_elements/steel_beam_scheme_input_ui.dart';
// import 'package:structify/presentation_layer/small_elements/loadcals_summary_ui.dart';

// import 'package:flutter/services.dart';

//domain layer
import '../../domain_layer/preferences.dart';

// presentation layer
// import '../presentation_layer/homescreen.dart';
import '../../misc/custom_func.dart';
import '../small_elements/dessign_assumption.dart';
import '../small_elements/button/function_button.dart';
import '../small_elements/steel_beam_scheme_summary.dart';
import 'homescreen.dart';
// import '../small_elements/custom_stateful_double_input.dart';
// import '../small_elements/custom_stateful_text_input.dart';
// import '../small_elements/loadcals_summary_ui.dart';

class SteelBeamSchemeCalculator extends ConsumerWidget {
  SteelBeamSchemeCalculator({super.key});

  final GlobalKey _globalKey = GlobalKey();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final ColorScheme colorScheme = Theme.of(context).colorScheme;
    final TextStyle titleStyle = Theme.of(context).textTheme.titleLarge!;
    final TextTheme textTheme = Theme.of(context).textTheme;
    final globalData = ref.watch(globalDataControllerProvider);
    ref.watch(appWatcherProvider);
    return globalData.when(
      data: (data) {
        return Scaffold(
          // appBar: AppBar(
          //   title: Text(
          //     'Beam Scheme Calculator',
          //     style: textTheme.titleLarge!.copyWith(
          //       color: colorScheme.onPrimary,
          //     ),
          //   ),
          //   backgroundColor: colorScheme.secondaryContainer,
          //   leading: IconButton(
          //     icon: Icon(
          //       Icons.arrow_back,
          //       color: colorScheme.onPrimary,
          //     ), // Customize the icon
          //     onPressed: () {
          //       // Custom action for the back button
          //       Navigator.pop(context); // Go back to the previous screen
          //     },
          //   ),
          //   actions: [
          //     IconButton(
          //       icon: Icon(
          //         Icons.print,
          //         color: colorScheme.onSecondaryContainer,
          //       ),
          //       onPressed: () {
          //         _exportListToPdf(context, ref, rowsPerPage: 11);
          //       },
          //     ),
          //     IconButton(
          //       icon: Icon(
          //         Icons.data_array_outlined,
          //         color: colorScheme.onSecondaryContainer,
          //       ), // Customize the icon
          //       onPressed: () {
          //         final db = AppDatabase(); //This should be a singleton
          //         Navigator.of(context).push(
          //           MaterialPageRoute(builder: (context) => DriftDbViewer(db)),
          //         );
          //       },
          //     ),
          //   ],
          // ),
          backgroundColor: colorScheme.surface,
          body: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Padding(
                padding: const EdgeInsets.all(8.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    FunctionButton(
                      labelIcon: Icon(Icons.print_outlined),
                      labelText: 'Assumption',
                      onTap: (isPressed) async {
                        showAssumption(
                          context,
                          DesignAssumption(
                            isExpanded: true,
                            textStyle: textTheme.bodyMedium!.copyWith(
                              color: colorScheme.onInverseSurface,
                            ),
                            titleStyle: textTheme.titleLarge!.copyWith(
                              color: colorScheme.onInverseSurface,
                            ),
                            title: 'Assumption',
                            assumptions: [
                              'HKCoP for Structural Use of Steel 2011 (HKCoPSUC2011)',
                              'Design based on flexural (ULS) and live load delfection (SLS)',
                              'All beams are simply-supported with concrete topping (fully-restrained so no lateral-torsional buckling)',
                              'Could adopt composite action factor >1.0 for additional flexural strength and stiffness (deflection SLS)',
                              'Flexural design: Low shear condition | Mc = min(pyS,1.2PyZ) (cl. *******)',
                              'Deflection design: consider live load only | Limit: L/250 | 2nd beam: 5wL^4 / 384EI  | main beam: point load deflection formulas',
                            ],
                            tooltipText:
                                '------if flexural fails------\n-adjust composite action factor OR\n-adjust steel grade OR\n-adjust grids\n'
                                '------If deflection fails------\n- same as above (need to pass both checks at the same time)',
                          ),
                        );
                      },
                    ),
                    SizedBox(width: 5.0),
                    FunctionButton(
                      labelIcon: Icon(Icons.print_outlined),
                      labelText: 'Summary',
                      onTap: (isPressed) async {
                        showSummary(context);
                      },
                    ),
                    SizedBox(width: 5.0),
                    FunctionButton(
                      labelIcon: Icon(Icons.print_outlined),
                      labelText: 'Print',

                      onTap: (isPressed) async {
                        _exportListToPdf(context, ref, rowsPerPage: 11);
                      },
                    ),
                    SizedBox(width: 5.0),
                    FunctionButton(
                      labelIcon: Icon(Icons.data_array_outlined),
                      labelText: 'Data',

                      onTap: (isPressed) async {
                        final db = AppDatabase(); //This should be a singleton
                        Navigator.of(context).push(
                          MaterialPageRoute(
                            builder: (context) => DriftDbViewer(db),
                          ),
                        );
                      },
                    ),
                  ],
                ),
              ),

              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Flexible(child: SteelBeamSchemeInputUi(isExpanded: true)),
                  Flexible(child: SteelBeamSchemeSummary(isExpanded: true)),
                ],
              ),
            ],
          ),
        );
      },
      error: (error, stackTrace) {
        return Text('Error: $error');
      },
      loading: () {
        return CircularProgressIndicator();
      },
    );
  }

  Future<void> _exportListToPdf(
    BuildContext context,
    WidgetRef ref, {
    int rowsPerPage = 10,
  }) async {
    final pdf = pw.Document();
    final schemes = ref.read(steelBeamSchemeDataControllerProvider);
    schemes.when(
      data: (data) {
        int counter = 0;
        do {
          if (counter + rowsPerPage <= data.length) {
            pdf.addPage(
              _customPage(
                context,
                ref,
                startFrom: counter,
                rowsPerPage: rowsPerPage,
                contentScale: 0.85,
              ),
            );
          } else {
            pdf.addPage(
              _customPage(
                context,
                ref,
                startFrom: counter,
                rowsPerPage: data.length - counter,
                contentScale: 0.85,
              ),
            );
          }
          counter += rowsPerPage;
        } while (counter <= data.length);
      },
      error: (error, stackTrace) => {},
      loading: () {},
    );

    await Printing.layoutPdf(
      onLayout: (PdfPageFormat format) async => pdf.save(),
    );
  }

  pw.Widget _customVerticalDivider() {
    return pw.Row(
      children: [
        pw.SizedBox(width: 15.0),
        pw.Container(width: 1.0, height: 30, color: PdfColors.grey400),
        pw.SizedBox(width: 15.0),
      ],
    );
  }

  pw.Widget _customDivider() {
    return pw.Divider(color: PdfColors.grey400);
  }

  pw.Page _customPage(
    BuildContext context,
    WidgetRef ref, {
    int startFrom = 0,
    int? rowsPerPage,
    double contentScale = 1.0,
  }) {
    final textStyle = Theme.of(context).textTheme.labelSmall;
    return pw.Page(
      margin: pw.EdgeInsets.fromLTRB(25, 35, 25, 35),
      pageFormat: PdfPageFormat.a4,
      orientation: pw.PageOrientation.portrait,
      build: (pw.Context context) {
        final schemes = ref.read(steelBeamSchemeDataControllerProvider);
        final globaldata = ref.read(globalDataControllerProvider);

        return schemes.when(
          data: (tables) {
            return globaldata.when(
              data: (data) {
                return pw.Center(
                  child: pw.Column(
                    mainAxisAlignment: pw.MainAxisAlignment.start,
                    children: [
                      pw.Align(
                        alignment: pw.Alignment.center,
                        child: pw.Text(
                          'Steel Beam Schemes Summary',
                          style: pw.TextStyle(
                            fontSize: 20,
                            fontWeight: pw.FontWeight.bold,
                          ),
                        ),
                      ),
                      pw.SizedBox(height: 10.0),
                      ...List.generate(rowsPerPage ?? tables.length - startFrom, (
                        index,
                      ) {
                        final bool mainBeamStatusBool =
                            tables[index].mainBeamSection != 'fail';
                        final bool secBeamStatusBool =
                            tables[index].secBeamSection != 'fail';

                        final double finish = tables[index + startFrom].finish;
                        final double sdl =
                            tables[index + startFrom].service +
                            tables[index + startFrom].finish *
                                data.finishUnitWeight /
                                1000;
                        final double ll = tables[index + startFrom].liveLoad;
                        late List<String> unit;
                        switch (data.unit) {
                          case 'metrics':
                            unit = PreferredUnit.metrics;
                            break;
                          case 'imperial':
                            unit = PreferredUnit.imperial;
                            break;
                          default:
                            unit = PreferredUnit.metrics;
                        }
                        return pw.Transform.scale(
                          scale: contentScale,
                          child: pw.DefaultTextStyle(
                            style: pw.TextStyle(fontSize: textStyle!.fontSize),
                            child: pw.Column(
                              children: [
                                pw.Row(
                                  children: [
                                    pw.Text(
                                      '${index + startFrom + 1}',
                                      style: pw.TextStyle(
                                        fontSize: textStyle.fontSize,
                                        fontWeight: pw.FontWeight.bold,
                                      ),
                                    ),

                                    _customVerticalDivider(),

                                    pw.Column(
                                      children: [
                                        pw.Text(
                                          tables[index + startFrom].usage,
                                          style: pw.TextStyle(
                                            fontSize: textStyle.fontSize,
                                            fontWeight: pw.FontWeight.bold,
                                          ),
                                        ),
                                        pw.Builder(
                                          builder: (context) {
                                            if (mainBeamStatusBool) {
                                              return pw.Text(
                                                'Main Beam: Pass',
                                                style: pw.TextStyle(
                                                  fontWeight:
                                                      pw.FontWeight.bold,
                                                  color: PdfColors.green,
                                                ),
                                              );
                                            } else {
                                              return pw.Text(
                                                'Main Beam: Fail',
                                                style: pw.TextStyle(
                                                  fontWeight:
                                                      pw.FontWeight.bold,
                                                  color: PdfColors.red,
                                                ),
                                              );
                                            }
                                          },
                                        ),
                                        pw.Builder(
                                          builder: (context) {
                                            if (secBeamStatusBool) {
                                              return pw.Text(
                                                'Sec Beam: Pass',
                                                style: pw.TextStyle(
                                                  fontWeight:
                                                      pw.FontWeight.bold,
                                                  color: PdfColors.green,
                                                ),
                                              );
                                            } else {
                                              return pw.Text(
                                                'Sec Beam: Fail',
                                                style: pw.TextStyle(
                                                  fontWeight:
                                                      pw.FontWeight.bold,
                                                  color: PdfColors.red,
                                                ),
                                              );
                                            }
                                          },
                                        ),
                                      ],
                                    ),

                                    _customVerticalDivider(),

                                    pw.Column(
                                      crossAxisAlignment:
                                          pw.CrossAxisAlignment.start,

                                      children: [
                                        pw.Text('Finish: '),
                                        pw.Text('SDL: '),
                                        pw.Text('LL: '),
                                      ],
                                    ),
                                    pw.SizedBox(width: 10.0),

                                    pw.Column(
                                      crossAxisAlignment:
                                          pw.CrossAxisAlignment.start,

                                      children: [
                                        pw.Text(
                                          '${NumberFormat('0.0').format(finish)} [${unit[4]}]',
                                        ),
                                        pw.Text(
                                          '${NumberFormat('0.0').format(sdl)} [${unit[1]}]',
                                        ),
                                        pw.Text(
                                          '${NumberFormat('0.0').format(ll)} [${unit[1]}]',
                                        ),
                                      ],
                                    ),

                                    _customVerticalDivider(),

                                    pw.Column(
                                      crossAxisAlignment:
                                          pw.CrossAxisAlignment.start,
                                      children: [
                                        pw.Text('Short Span: '),
                                        pw.Text('Long Span: '),
                                        pw.Text('Bays: '),
                                        pw.Text('Str Zone: '),
                                      ],
                                    ),
                                    pw.SizedBox(width: 10.0),
                                    pw.Column(
                                      crossAxisAlignment:
                                          pw.CrossAxisAlignment.start,
                                      children: [
                                        pw.Text(
                                          '${NumberFormat('0.0').format(tables[index + startFrom].shortSpan)} [${unit[3]}]',
                                        ),
                                        pw.Text(
                                          '${NumberFormat('0.0').format(tables[index + startFrom].longSpan)} [${unit[3]}]',
                                        ),
                                        pw.Text(
                                          '${NumberFormat('0.0').format(tables[index + startFrom].bays)} [${unit[3]}]',
                                        ),
                                        pw.Text(
                                          '${NumberFormat('0').format(tables[index + startFrom].strZone)} [${unit[4]}]',
                                        ),
                                      ],
                                    ),
                                    _customVerticalDivider(),
                                    pw.Column(
                                      crossAxisAlignment:
                                          pw.CrossAxisAlignment.start,
                                      children: [
                                        pw.Text(
                                          'Main Beam Section: ',
                                          style: pw.TextStyle(
                                            fontSize: textStyle.fontSize,
                                            fontWeight: pw.FontWeight.bold,
                                          ),
                                        ),
                                        pw.Text(
                                          '${tables[index + startFrom].mainBeamSection} ',
                                        ),
                                        pw.Text(
                                          'Sec Beam Section: ',
                                          style: pw.TextStyle(
                                            fontSize: textStyle.fontSize,
                                            fontWeight: pw.FontWeight.bold,
                                          ),
                                        ),
                                        pw.Text(
                                          '${tables[index + startFrom].secBeamSection} ',
                                        ),
                                      ],
                                    ),
                                    // pw.SizedBox(width: 10.0),
                                    // pw.Column(
                                    //   crossAxisAlignment:
                                    //       pw.CrossAxisAlignment.start,
                                    //   children: [
                                    //     pw.Text(
                                    //       '${tables[index + startFrom].mainBeamSection} ',
                                    //     ),
                                    //     pw.Text(
                                    //       '${tables[index + startFrom].secBeamSection} ',
                                    //     ),
                                    //   ],
                                    // ),
                                  ],
                                ),
                                _customDivider(),
                              ],
                            ),
                          ),
                        );
                      }),
                    ],
                  ),
                );
              },
              error: (error, stacktrace) => pw.Text('Error: $error'),
              loading: () => pw.Text('Loading'),
            );
          },
          error: (error, stackTrace) => pw.Text('Error: $error'),
          loading: () => pw.Text('loading'),
        );
      },
    );
  }
}
