import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:structify/data_layer/app_database.dart';
import 'package:drift_db_viewer/drift_db_viewer.dart';

//below for printing
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:printing/printing.dart';
import 'package:structify/misc/custom_func.dart';

// import 'package:flutter/services.dart';

//domain layer
import '../../domain_layer/preferences.dart';

// presentation layer
// import '../presentation_layer/homescreen.dart';
import '../small_elements/loadcals_input_ui.dart';
import '../small_elements/button/function_button.dart';
import '../small_elements/misc_strzone_summary.dart';
import '../small_elements/misc_strzone_table_ui.dart';
import 'homescreen.dart';
// import '../small_elements/custom_stateful_double_input.dart';
// import '../small_elements/custom_stateful_text_input.dart';
import '../small_elements/loadcals_summary.dart';

class StrZoneCalculator extends ConsumerWidget {
  StrZoneCalculator({super.key});

  final GlobalKey _globalKey = GlobalKey();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;
    final globalData = ref.watch(globalDataControllerProvider);
    ref.watch(appWatcherProvider);
    return globalData.when(
      data: (data) {
        return Scaffold(
          // appBar: AppBar(
          //   title: Text(
          //     'Loading Calculator',
          //     style: textTheme.titleLarge!.copyWith(
          //       color: colorScheme.onPrimary,
          //     ),
          //   ),
          //   backgroundColor: colorScheme.secondaryContainer,
          //   leading: IconButton(
          //     icon: Icon(
          //       Icons.arrow_back,
          //       color: colorScheme.onPrimary,
          //     ), // Customize the icon
          //     onPressed: () {
          //       // Custom action for the back button
          //       Navigator.pop(context); // Go back to the previous screen
          //     },
          //   ),
          //   actions: [
          //     IconButton(
          //       icon: Icon(
          //         Icons.print,
          //         color: colorScheme.onSecondaryContainer,
          //       ),
          //       onPressed: () {
          //         _exportListToPdf(context, ref, rowsPerPage: 14);
          //       },
          //     ),
          //     IconButton(
          //       icon: Icon(
          //         Icons.data_array_outlined,
          //         color: colorScheme.onSecondaryContainer,
          //       ), // Customize the icon
          //       onPressed: () {
          //         final db = AppDatabase(); //This should be a singleton
          //         Navigator.of(context).push(
          //           MaterialPageRoute(builder: (context) => DriftDbViewer(db)),
          //         );
          //       },
          //     ),
          //   ],
          // ),
          backgroundColor: colorScheme.surface,
          body: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Padding(
                padding: const EdgeInsets.all(8.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    FunctionButton(
                      labelIcon: Icon(Icons.print_outlined),
                      labelText: 'Summary',
                      onTap: (isPressed) async {
                        showSummary(context);
                      },
                    ),
                    SizedBox(width: 5.0), FunctionButton(
                      labelIcon: Icon(Icons.print_outlined),
                      labelText: 'Print',
                      onTap: (isPressed) async {
                        // await _exportListToPdf(context, ref, rowsPerPage: 14);
                      },
                    ),
                    SizedBox(width: 5.0),
                    FunctionButton(
                      labelIcon: Icon(Icons.data_array_outlined),
                      labelText: 'Data',

                      onTap: (isPressed) async {
                        final db = AppDatabase(); //This should be a singleton
                        Navigator.of(context).push(
                          MaterialPageRoute(
                            builder: (context) => DriftDbViewer(db),
                          ),
                        );
                      },
                    ),
                  ],
                ),
              ),
              StrZoneTableUi(isExpanded: true),
              StrZoneCalsSummary(isExpanded: true),
            ],
          ),
          floatingActionButton: FloatingActionButton(
            backgroundColor: colorScheme.secondaryContainer.withAlpha(150),
            child: Icon(
              Icons.add_circle_outline_outlined,
              color: colorScheme.onSecondaryContainer.withAlpha(150),
            ),
            onPressed: () {
              ref
                  .read(strZoneTablesControllerProvider.notifier)
                  .addEmptytable();
            },
          ),
        );
      },
      error: (error, stackTrace) {
        return Text('Error: $error');
      },
      loading: () {
        return CircularProgressIndicator();
      },
    );
  }

  Future<void> _exportListToPdf(
    BuildContext context,
    WidgetRef ref, {
    int rowsPerPage = 10,
  }) async {
    final pdf = pw.Document();
    final loadingTables = ref.read(loadingTablesControllerProvider);
    loadingTables.when(
      data: (data) {
        int counter = 0;
        do {
          if (counter + rowsPerPage <= data.length) {
            pdf.addPage(
              _customPage(
                context,
                ref,
                startFrom: counter,
                rowsPerPage: rowsPerPage,
              ),
            );
          } else {
            pdf.addPage(
              _customPage(
                context,
                ref,
                startFrom: counter,
                rowsPerPage: data.length - counter,
              ),
            );
          }
          counter += rowsPerPage;
        } while (counter <= data.length);
      },
      error: (error, stackTrace) => {},
      loading: () {},
    );

    await Printing.layoutPdf(
      onLayout: (PdfPageFormat format) async => pdf.save(),
    );
  }
}

pw.Widget _customVerticalDivider() {
  return pw.Row(
    children: [
      pw.SizedBox(width: 15.0),
      pw.Container(width: 1.0, height: 30, color: PdfColors.grey400),
      pw.SizedBox(width: 15.0),
    ],
  );
}

pw.Widget _customDivider() {
  return pw.Divider(color: PdfColors.grey400);
}

pw.Page _customPage(
  BuildContext context,
  WidgetRef ref, {
  int startFrom = 0,
  int? rowsPerPage,
}) {
  final textStyle = Theme.of(context).textTheme.bodyMedium;
  return pw.Page(
    margin: pw.EdgeInsets.fromLTRB(25, 35, 25, 35),
    pageFormat: PdfPageFormat.a4,
    orientation: pw.PageOrientation.portrait,
    build: (pw.Context context) {
      final loadingTables = ref.read(loadingTablesControllerProvider);
      final globaldata = ref.read(globalDataControllerProvider);

      return loadingTables.when(
        data: (tables) {
          return globaldata.when(
            data: (data) {
              return pw.Center(
                child: pw.Column(
                  mainAxisAlignment: pw.MainAxisAlignment.start,
                  children: [
                    pw.Align(
                      alignment: pw.Alignment.center,
                      child: pw.Text(
                        'Loading Summary',
                        style: pw.TextStyle(
                          fontSize: 20,
                          fontWeight: pw.FontWeight.bold,
                        ),
                      ),
                    ),
                    pw.SizedBox(height: 10.0),
                    ...List.generate(rowsPerPage ?? tables.length - startFrom, (
                      index,
                    ) {
                      final double sdl =
                          tables[index + startFrom].service +
                          tables[index + startFrom].finish *
                              data.finishUnitWeight /
                              1000;
                      final double ll = tables[index + startFrom].liveLoad;
                      final double total = sdl + ll;
                      final double factoredTotal =
                          data.sdlFactor * sdl + data.llFactor * ll;
                      final String f1 = data.sdlFactor.toString();
                      final String f2 = data.llFactor.toString();
                      late List<String> unit;
                      switch (data.unit) {
                        case 'metrics':
                          unit = PreferredUnit.metrics;
                          break;
                        case 'imperial':
                          unit = PreferredUnit.imperial;
                          break;
                        default:
                          unit = PreferredUnit.metrics;
                      }
                      return pw.DefaultTextStyle(
                        style: pw.TextStyle(fontSize: textStyle!.fontSize),
                        child: pw.Column(
                          children: [
                            pw.Row(
                              children: [
                                pw.Text(
                                  '${index + startFrom + 1}',
                                  style: pw.TextStyle(
                                    fontSize: textStyle.fontSize,
                                    fontWeight: pw.FontWeight.bold,
                                  ),
                                ),

                                _customVerticalDivider(),

                                pw.Text(
                                  tables[index + startFrom].usage,
                                  style: pw.TextStyle(
                                    fontSize: textStyle.fontSize,
                                    fontWeight: pw.FontWeight.bold,
                                  ),
                                ),

                                _customVerticalDivider(),

                                pw.Column(
                                  crossAxisAlignment:
                                      pw.CrossAxisAlignment.start,

                                  children: [pw.Text('SLL: '), pw.Text('LL: ')],
                                ),
                                pw.SizedBox(width: 10.0),

                                pw.Column(
                                  crossAxisAlignment:
                                      pw.CrossAxisAlignment.start,

                                  children: [
                                    pw.Text(
                                      '${sdl.toStringAsFixed(2)} [${unit[1]}]',
                                    ),
                                    pw.Text(
                                      '${ll.toStringAsFixed(2)} [${unit[1]}]',
                                    ),
                                  ],
                                ),

                                _customVerticalDivider(),

                                pw.Column(
                                  crossAxisAlignment:
                                      pw.CrossAxisAlignment.start,
                                  children: [
                                    pw.Text('SDL+LL: '),
                                    pw.Text('${f1}SDL+${f2}LL: '),
                                  ],
                                ),
                                pw.SizedBox(width: 10.0),
                                pw.Column(
                                  crossAxisAlignment:
                                      pw.CrossAxisAlignment.start,
                                  children: [
                                    pw.Text(
                                      '${total.toStringAsFixed(2)} [${unit[1]}]',
                                    ),
                                    pw.Text(
                                      '${factoredTotal.toStringAsFixed(2)} [${unit[1]}]',
                                    ),
                                  ],
                                ),
                              ],
                            ),
                            _customDivider(),
                          ],
                        ),
                      );
                    }),
                  ],
                ),
              );
            },
            error: (error, stacktrace) => pw.Text('Error: $error'),
            loading: () => pw.Text('Loading'),
          );
        },
        error: (error, stackTrace) => pw.Text('Error: $error'),
        loading: () => pw.Text('loading'),
      );
    },
  );
}
