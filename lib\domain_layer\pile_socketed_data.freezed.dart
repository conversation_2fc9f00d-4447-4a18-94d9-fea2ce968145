// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'pile_socketed_data.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$PileSocketedData {

 bool get isSelected; String get pileSocketedSchemeId;
/// Create a copy of PileSocketedData
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$PileSocketedDataCopyWith<PileSocketedData> get copyWith => _$PileSocketedDataCopyWithImpl<PileSocketedData>(this as PileSocketedData, _$identity);

  /// Serializes this PileSocketedData to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is PileSocketedData&&(identical(other.isSelected, isSelected) || other.isSelected == isSelected)&&(identical(other.pileSocketedSchemeId, pileSocketedSchemeId) || other.pileSocketedSchemeId == pileSocketedSchemeId));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,isSelected,pileSocketedSchemeId);

@override
String toString() {
  return 'PileSocketedData(isSelected: $isSelected, pileSocketedSchemeId: $pileSocketedSchemeId)';
}


}

/// @nodoc
abstract mixin class $PileSocketedDataCopyWith<$Res>  {
  factory $PileSocketedDataCopyWith(PileSocketedData value, $Res Function(PileSocketedData) _then) = _$PileSocketedDataCopyWithImpl;
@useResult
$Res call({
 bool isSelected, String pileSocketedSchemeId
});




}
/// @nodoc
class _$PileSocketedDataCopyWithImpl<$Res>
    implements $PileSocketedDataCopyWith<$Res> {
  _$PileSocketedDataCopyWithImpl(this._self, this._then);

  final PileSocketedData _self;
  final $Res Function(PileSocketedData) _then;

/// Create a copy of PileSocketedData
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? isSelected = null,Object? pileSocketedSchemeId = null,}) {
  return _then(_self.copyWith(
isSelected: null == isSelected ? _self.isSelected : isSelected // ignore: cast_nullable_to_non_nullable
as bool,pileSocketedSchemeId: null == pileSocketedSchemeId ? _self.pileSocketedSchemeId : pileSocketedSchemeId // ignore: cast_nullable_to_non_nullable
as String,
  ));
}

}


/// Adds pattern-matching-related methods to [PileSocketedData].
extension PileSocketedDataPatterns on PileSocketedData {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _PileSocketedData value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _PileSocketedData() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _PileSocketedData value)  $default,){
final _that = this;
switch (_that) {
case _PileSocketedData():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _PileSocketedData value)?  $default,){
final _that = this;
switch (_that) {
case _PileSocketedData() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( bool isSelected,  String pileSocketedSchemeId)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _PileSocketedData() when $default != null:
return $default(_that.isSelected,_that.pileSocketedSchemeId);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( bool isSelected,  String pileSocketedSchemeId)  $default,) {final _that = this;
switch (_that) {
case _PileSocketedData():
return $default(_that.isSelected,_that.pileSocketedSchemeId);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( bool isSelected,  String pileSocketedSchemeId)?  $default,) {final _that = this;
switch (_that) {
case _PileSocketedData() when $default != null:
return $default(_that.isSelected,_that.pileSocketedSchemeId);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _PileSocketedData extends PileSocketedData {
   _PileSocketedData({this.isSelected = false, this.pileSocketedSchemeId = '1'}): super._();
  factory _PileSocketedData.fromJson(Map<String, dynamic> json) => _$PileSocketedDataFromJson(json);

@override@JsonKey() final  bool isSelected;
@override@JsonKey() final  String pileSocketedSchemeId;

/// Create a copy of PileSocketedData
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$PileSocketedDataCopyWith<_PileSocketedData> get copyWith => __$PileSocketedDataCopyWithImpl<_PileSocketedData>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$PileSocketedDataToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _PileSocketedData&&(identical(other.isSelected, isSelected) || other.isSelected == isSelected)&&(identical(other.pileSocketedSchemeId, pileSocketedSchemeId) || other.pileSocketedSchemeId == pileSocketedSchemeId));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,isSelected,pileSocketedSchemeId);

@override
String toString() {
  return 'PileSocketedData(isSelected: $isSelected, pileSocketedSchemeId: $pileSocketedSchemeId)';
}


}

/// @nodoc
abstract mixin class _$PileSocketedDataCopyWith<$Res> implements $PileSocketedDataCopyWith<$Res> {
  factory _$PileSocketedDataCopyWith(_PileSocketedData value, $Res Function(_PileSocketedData) _then) = __$PileSocketedDataCopyWithImpl;
@override @useResult
$Res call({
 bool isSelected, String pileSocketedSchemeId
});




}
/// @nodoc
class __$PileSocketedDataCopyWithImpl<$Res>
    implements _$PileSocketedDataCopyWith<$Res> {
  __$PileSocketedDataCopyWithImpl(this._self, this._then);

  final _PileSocketedData _self;
  final $Res Function(_PileSocketedData) _then;

/// Create a copy of PileSocketedData
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? isSelected = null,Object? pileSocketedSchemeId = null,}) {
  return _then(_PileSocketedData(
isSelected: null == isSelected ? _self.isSelected : isSelected // ignore: cast_nullable_to_non_nullable
as bool,pileSocketedSchemeId: null == pileSocketedSchemeId ? _self.pileSocketedSchemeId : pileSocketedSchemeId // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

// dart format on
