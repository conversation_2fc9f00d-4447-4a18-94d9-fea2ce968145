// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'basement_wall_scheme_input.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_BasementWallSchemeInput _$BasementWallSchemeInputFromJson(
  Map<String, dynamic> json,
) => _BasementWallSchemeInput(
  id: json['id'] as String? ?? '1',
  fcu: (json['fcu'] as num?)?.toDouble() ?? 45.0,
  cover: (json['cover'] as num?)?.toDouble() ?? 75.0,
  mainKValue: (json['mainKValue'] as num?)?.toDouble() ?? 0.156,
  mainSteelRatio: (json['mainSteelRatio'] as num?)?.toDouble() ?? 0.04,
  minS: (json['minS'] as num?)?.toInt() ?? 100,
  maxS: (json['maxS'] as num?)?.toInt() ?? 300,
  maxDepth: (json['maxDepth'] as num?)?.toDouble() ?? 500,
  minDepth: (json['minDepth'] as num?)?.toDouble() ?? 150.0,
  maxLayers: (json['maxLayers'] as num?)?.toInt() ?? 2,
  wallTopLevel: (json['wallTopLevel'] as num?)?.toDouble() ?? 0.0,
  wallBottomLevel: (json['wallBottomLevel'] as num?)?.toDouble() ?? -3.0,
  soilTopLevel: (json['soilTopLevel'] as num?)?.toDouble() ?? 0.0,
  waterTopLevel: (json['waterTopLevel'] as num?)?.toDouble() ?? 0.0,
);

Map<String, dynamic> _$BasementWallSchemeInputToJson(
  _BasementWallSchemeInput instance,
) => <String, dynamic>{
  'id': instance.id,
  'fcu': instance.fcu,
  'cover': instance.cover,
  'mainKValue': instance.mainKValue,
  'mainSteelRatio': instance.mainSteelRatio,
  'minS': instance.minS,
  'maxS': instance.maxS,
  'maxDepth': instance.maxDepth,
  'minDepth': instance.minDepth,
  'maxLayers': instance.maxLayers,
  'wallTopLevel': instance.wallTopLevel,
  'wallBottomLevel': instance.wallBottomLevel,
  'soilTopLevel': instance.soilTopLevel,
  'waterTopLevel': instance.waterTopLevel,
};
