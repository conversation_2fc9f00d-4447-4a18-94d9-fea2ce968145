import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:structify/data_layer/app_database.dart';
import 'package:drift_db_viewer/drift_db_viewer.dart';

//below for printing
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:printing/printing.dart';
import 'package:structify/misc/custom_func.dart';

//domain layer
import '../../domain_layer/preferences.dart';

// presentation layer
import '../small_elements/dessign_assumption.dart';
import '../small_elements/fdn_footing_scheme_input_ui.dart';
import '../small_elements/fdn_footing_scheme_summary.dart';
import '../small_elements/button/function_button.dart';
import 'homescreen.dart';

class FootingScheme extends ConsumerWidget {
  FootingScheme({super.key});
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final ColorScheme colorScheme = Theme.of(context).colorScheme;
    final TextTheme textTheme = Theme.of(context).textTheme;
    final globalData = ref.watch(globalDataControllerProvider);
    ref.watch(appWatcherProvider);
    return globalData.when(
      data: (data) {
        return Scaffold(
          // appBar: AppBar(
          //   title: Text(
          //     'Loading Calculator',
          //     style: textTheme.titleLarge!.copyWith(
          //       color: colorScheme.onPrimary,
          //     ),
          //   ),
          //   backgroundColor: colorScheme.secondaryContainer,
          //   leading: IconButton(
          //     icon: Icon(
          //       Icons.arrow_back,
          //       color: colorScheme.onPrimary,
          //     ), // Customize the icon
          //     onPressed: () {
          //       // Custom action for the back button
          //       Navigator.pop(context); // Go back to the previous screen
          //     },
          //   ),
          //   actions: [
          //     IconButton(
          //       icon: Icon(
          //         Icons.print,
          //         color: colorScheme.onSecondaryContainer,
          //       ),
          //       onPressed: () {
          //         _exportListToPdf(context, ref, rowsPerPage: 14);
          //       },
          //     ),
          //     IconButton(
          //       icon: Icon(
          //         Icons.data_array_outlined,
          //         color: colorScheme.onSecondaryContainer,
          //       ), // Customize the icon
          //       onPressed: () {
          //         final db = AppDatabase(); //This should be a singleton
          //         Navigator.of(context).push(
          //           MaterialPageRoute(builder: (context) => DriftDbViewer(db)),
          //         );
          //       },
          //     ),
          //   ],
          // ),
          backgroundColor: colorScheme.surface,
          body: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Padding(
                padding: const EdgeInsets.all(8.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    FunctionButton(
                      labelIcon: Icon(Icons.print_outlined),
                      labelText: 'Assumption',
                      onTap: (isPressed) async {
                        showAssumption(
                          context,
                          DesignAssumption(
                            isExpanded: true,
                            textStyle: textTheme.bodyMedium!.copyWith(
                              color: colorScheme.onInverseSurface,
                            ),
                            titleStyle: textTheme.titleLarge!.copyWith(
                              color: colorScheme.onInverseSurface,
                            ),
                            title: 'Assumption',
                            assumptions: [
                              'HKCoP for Structural Use of Concrete 2013 (HKCoPSUC2013)',
                              'fy = 500 MPa',
                              'Flexural design: cl.*******(c) | k-limit: <10% moment redistribution',
                              'Shear design: cl.******* | vc: table 6.3, notes 1 to 3',
                              'Deflection Limit: L/d <= 7 (cantilever beam)',
                            ],
                            tooltipText:
                                '------if ground SLS load fails------\n-adjust footing top level/depth such that it sits on non-surmerged surface/rock OR\n-reject this scheme in comparison and consider pile scheme.\n'
                                '------if flexural fails------\n-adjust preferred k-value OR\n-adjust max footing depth OR\n-adjust max rebar layer OR\n-adjust concrete grade\n'
                                '------if shear fails------\n-adjust the min links spacing OR\n-adjust max footing depth\n'
                                '------If deflection fails------\n-add compression bar in detailed design stage OR\n-check actual deflection in detailed design stage OR\n-consider pre-stressed RC footing in detailed design stage',
                          ),
                        );
                      },
                    ),
                    SizedBox(width: 5.0),
                    FunctionButton(
                      labelIcon: Icon(Icons.print_outlined),
                      labelText: 'Summary',
                      onTap: (isPressed) async {
                        showSummary(context);
                      },
                    ),
                    SizedBox(width: 5.0),
                    FunctionButton(
                      labelIcon: Icon(Icons.print_outlined),
                      labelText: 'Print',
                      onTap: (isPressed) async {
                        await _exportListToPdf(context, ref, rowsPerPage: 13);
                      },
                    ),
                    SizedBox(width: 5.0),
                    FunctionButton(
                      labelIcon: Icon(Icons.data_array_outlined),
                      labelText: 'Data',
                      onTap: (isPressed) async {
                        final db = AppDatabase(); //This should be a singleton
                        Navigator.of(context).push(
                          MaterialPageRoute(
                            builder: (context) => DriftDbViewer(db),
                          ),
                        );
                      },
                    ),
                  ],
                ),
              ),

              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Flexible(flex:1,child: FootingSchemeInputUi(isExpanded: true)),
                  Flexible(flex: 2, child: FootingSchemeSummary(isExpanded: true)),
                ],
              ),
            ],
          ),
          // floatingActionButton: FloatingActionButton(
          //   backgroundColor: colorScheme.secondaryContainer.withAlpha(150),
          //   child: Icon(
          //     Icons.add_circle_outline_outlined,
          //     color: colorScheme.onSecondaryContainer.withAlpha(150),
          //   ),
          //   onPressed: () {
          //     ref
          //         .read(steelColumnSchemeInputControllerProvider.notifier)
          //         .addEmptytable();
          //   },
          // ),
        );
      },
      error: (error, stackTrace) {
        return Text('Error: $error');
      },
      loading: () {
        return CircularProgressIndicator();
      },
    );
  }

  Future<void> _exportListToPdf(
    BuildContext context,
    WidgetRef ref, {
    int rowsPerPage = 10,
  }) async {
    final pdf = pw.Document();
    final footingSchemes = ref.read(footingSchemeDataControllerProvider);
    footingSchemes.when(
      data: (data) {
        int counter = 0;
        do {
          if (counter + rowsPerPage <= data.length) {
            pdf.addPage(
              _customPage(
                context,
                ref,
                startFrom: counter,
                rowsPerPage: rowsPerPage,
              ),
            );
          } else {
            pdf.addPage(
              _customPage(
                context,
                ref,
                startFrom: counter,
                rowsPerPage: data.length - counter,
              ),
            );
          }
          counter += rowsPerPage;
        } while (counter <= data.length);
      },
      error: (error, stackTrace) => {},
      loading: () {},
    );

    await Printing.layoutPdf(
      onLayout: (PdfPageFormat format) async => pdf.save(),
    );
  }
}

pw.Page _customPage(
  BuildContext context,
  WidgetRef ref, {
  int startFrom = 0,
  int? rowsPerPage,
}) {
  // final textStyle = Theme.of(context).textTheme.bodySmall;
  return pw.Page(
    margin: pw.EdgeInsets.fromLTRB(25, 35, 25, 35),
    pageFormat: PdfPageFormat.a4,
    orientation: pw.PageOrientation.portrait,
    build: (pw.Context context) {
      final footingSchemes = ref.read(footingSchemeDataControllerProvider);
      final globaldata = ref.read(globalDataControllerProvider);
      NumberFormat f0 = NumberFormat('0');
      return footingSchemes.when(
        data: (tables) {
          return globaldata.when(
            data: (data) {
              final double titleFontSize = 8;
              final double normalFontSize = 8;
              return pw.Center(
                child: pw.Column(
                  mainAxisAlignment: pw.MainAxisAlignment.start,
                  children: [
                    ...List.generate(rowsPerPage ?? tables.length - startFrom, (
                      index,
                    ) {
                      late List<String> unit;
                      switch (data.unit) {
                        case 'metrics':
                          unit = PreferredUnit.metrics;
                          break;
                        case 'imperial':
                          unit = PreferredUnit.imperial;
                          break;
                        default:
                          unit = PreferredUnit.metrics;
                      }
                      return pw.DefaultTextStyle(
                        style: pw.TextStyle(fontSize: normalFontSize),
                        child: pw.Column(
                          children: [
                            pw.Align(
                              alignment: pw.Alignment.center,
                              child: pw.Text(
                                'Pad Footing Schemes Summary',
                                style: pw.TextStyle(
                                  fontSize: 20,
                                  fontWeight: pw.FontWeight.bold,
                                ),
                              ),
                            ),
                            pw.SizedBox(height: 10.0),
                            pw.Row(
                              children: [
                                pw.Text(
                                  '${index + startFrom + 1}',
                                  style: pw.TextStyle(
                                    fontSize: titleFontSize,
                                    fontWeight: pw.FontWeight.bold,
                                  ),
                                ),

                                _customVerticalDivider(),

                                pw.Column(
                                  crossAxisAlignment:
                                      pw.CrossAxisAlignment.start,
                                  children: [
                                    pw.Text(
                                      'Size:\n\n\n',
                                      style: pw.TextStyle(
                                        fontSize: titleFontSize,
                                        fontWeight: pw.FontWeight.bold,
                                      ),
                                    ),
                                    pw.Text(
                                      'fcu [${unit[5]}]:',
                                      style: pw.TextStyle(
                                        fontSize: titleFontSize,
                                        fontWeight: pw.FontWeight.bold,
                                      ),
                                    ),
                                    pw.Text(
                                      'Ground Type:',
                                      style: pw.TextStyle(
                                        fontSize: titleFontSize,
                                        fontWeight: pw.FontWeight.bold,
                                      ),
                                    ),
                                    pw.Builder(
                                      builder: (context) {
                                        if (tables[index].groundType ==
                                            'Soil') {
                                          return pw.Text(
                                            'Soil N Value:',
                                            style: pw.TextStyle(
                                              fontSize: titleFontSize,
                                              fontWeight: pw.FontWeight.bold,
                                            ),
                                          );
                                        } else {
                                          return pw.Text(
                                            'Safe Bearing Capacity:',
                                            style: pw.TextStyle(
                                              fontSize: titleFontSize,
                                              fontWeight: pw.FontWeight.bold,
                                            ),
                                          );
                                        }
                                      },
                                    ),
                                  ],
                                ),
                                pw.SizedBox(width: 5.0),
                                pw.Column(
                                  crossAxisAlignment:
                                      pw.CrossAxisAlignment.start,
                                  children: [
                                    pw.Text(
                                      '${tables[index + startFrom].size}(W)x\n${tables[index + startFrom].size}(L)x\n${tables[index + startFrom].strZone}(D)',
                                      style: pw.TextStyle(
                                        fontSize: titleFontSize,
                                      ),
                                    ),
                                    pw.Text(
                                      '${tables[index + startFrom].fcu}',
                                      style: pw.TextStyle(
                                        fontSize: titleFontSize,
                                      ),
                                    ),
                                    pw.Text(
                                      '${tables[index + startFrom].groundType}',
                                      style: pw.TextStyle(
                                        fontSize: titleFontSize,
                                      ),
                                    ),
                                    pw.Builder(
                                      builder: (context) {
                                        if (tables[index].groundType ==
                                            'Soil') {
                                          return pw.Text(
                                            '${NumberFormat('0').format(tables[index].soilNValue)}',
                                          );
                                        } else {
                                          return pw.Text(
                                            '${NumberFormat('0').format(tables[index].rockCapacity)}',
                                          );
                                        }
                                      },
                                    ),
                                  ],
                                ),
                                _customVerticalDivider(),
                                pw.Row(
                                  children: [
                                    pw.Column(
                                      crossAxisAlignment:
                                          pw.CrossAxisAlignment.start,

                                      children: [
                                        pw.Text(
                                          'Footing Top Level [mPD]: ',
                                          style: pw.TextStyle(
                                            fontSize: titleFontSize,
                                            fontWeight: pw.FontWeight.bold,
                                          ),
                                        ),
                                        pw.Text(
                                          'Water Table Level [mPD]: ',
                                          style: pw.TextStyle(
                                            fontSize: titleFontSize,
                                            fontWeight: pw.FontWeight.bold,
                                          ),
                                        ),
                                        pw.Text(
                                          'SLS Load [${unit[0]}]: ',
                                          style: pw.TextStyle(
                                            fontSize: titleFontSize,
                                            fontWeight: pw.FontWeight.bold,
                                          ),
                                        ),
                                        pw.Text(
                                          'ULS Load [${unit[0]}]: ',
                                          style: pw.TextStyle(
                                            fontSize: titleFontSize,
                                            fontWeight: pw.FontWeight.bold,
                                          ),
                                        ),
                                      ],
                                    ),
                                    pw.SizedBox(width: 5.0),

                                    pw.Column(
                                      crossAxisAlignment:
                                          pw.CrossAxisAlignment.start,

                                      children: [
                                        pw.Text(
                                          f0.format(
                                            tables[index + startFrom]
                                                .footingTopLevel,
                                          ),
                                        ),
                                        pw.Text(
                                          f0.format(
                                            tables[index + startFrom]
                                                .waterTableLevel,
                                          ),
                                        ),
                                        pw.Text(
                                          f0.format(
                                            tables[index + startFrom].slsLoad,
                                          ),
                                        ),
                                        pw.Text(
                                          f0.format(
                                            tables[index + startFrom].ulsLoad,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ],
                                ),
                                _customVerticalDivider(),
                                pw.Column(
                                  crossAxisAlignment:
                                      pw.CrossAxisAlignment.start,
                                  children: [
                                    pw.Text(
                                      'Top Bar (Compression):',
                                      style: pw.TextStyle(
                                        fontSize: titleFontSize,
                                        fontWeight: pw.FontWeight.bold,
                                      ),
                                    ),
                                    pw.Text(
                                      'Bottom Bar (Tension):',
                                      style: pw.TextStyle(
                                        fontSize: titleFontSize,
                                        fontWeight: pw.FontWeight.bold,
                                      ),
                                    ),
                                    pw.Text(
                                      'Links:',
                                      style: pw.TextStyle(
                                        fontSize: titleFontSize,
                                        fontWeight: pw.FontWeight.bold,
                                      ),
                                    ),
                                    pw.Text(
                                      'Punching Links:',
                                      style: pw.TextStyle(
                                        fontSize: titleFontSize,
                                        fontWeight: pw.FontWeight.bold,
                                      ),
                                    ),
                                  ],
                                ),
                                pw.SizedBox(width: 5.0),
                                pw.Column(
                                  crossAxisAlignment:
                                      pw.CrossAxisAlignment.start,
                                  children: [
                                    pw.Text(
                                      tables[index + startFrom].mainTopBar,
                                    ),
                                    pw.Text(
                                      tables[index + startFrom].mainBottomBar,
                                    ),
                                    pw.Text(
                                      tables[index + startFrom].mainLinks,
                                    ),
                                    pw.Text(
                                      tables[index + startFrom].punchingLinks,
                                    ),
                                  ],
                                ),
                              ],
                            ),
                            _customDivider(),
                          ],
                        ),
                      );
                    }),
                  ],
                ),
              );
            },
            error: (error, stacktrace) => pw.Text('Error: $error'),
            loading: () => pw.Text('Loading'),
          );
        },
        error: (error, stackTrace) => pw.Text('Error: $error'),
        loading: () => pw.Text('loading'),
      );
    },
  );
}

pw.Widget _customVerticalDivider() {
  return pw.Row(
    children: [
      pw.SizedBox(width: 15.0),
      pw.Container(width: 1.0, height: 60, color: PdfColors.grey400),
      pw.SizedBox(width: 15.0),
    ],
  );
}

pw.Widget _customDivider() {
  return pw.Divider(color: PdfColors.grey400);
}
