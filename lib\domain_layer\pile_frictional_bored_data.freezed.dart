// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'pile_frictional_bored_data.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$PileFrictionalBoredData {

 double get sptNValue; double get soilUnitWeight; double get kTan; double get fos; double get fcu; double get maxPileLength; double get maxPileDiameter; double get maxSteelRatio; double get slsLoad; double get ulsLoad; double get diameter; double get ratioOfBelloutDia; double get length; double get shaftCapacity; double get baseCapacity; double get totalGroundResistance; double get strCapacity; String get rebar;//will be overriden  as soon as new instance created
 double get steelRatio; bool get isSelected; String get calsLog;//will be overriden  as soon as new instance created
 String get pileFrictionalBoredSchemeId;
/// Create a copy of PileFrictionalBoredData
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$PileFrictionalBoredDataCopyWith<PileFrictionalBoredData> get copyWith => _$PileFrictionalBoredDataCopyWithImpl<PileFrictionalBoredData>(this as PileFrictionalBoredData, _$identity);

  /// Serializes this PileFrictionalBoredData to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is PileFrictionalBoredData&&(identical(other.sptNValue, sptNValue) || other.sptNValue == sptNValue)&&(identical(other.soilUnitWeight, soilUnitWeight) || other.soilUnitWeight == soilUnitWeight)&&(identical(other.kTan, kTan) || other.kTan == kTan)&&(identical(other.fos, fos) || other.fos == fos)&&(identical(other.fcu, fcu) || other.fcu == fcu)&&(identical(other.maxPileLength, maxPileLength) || other.maxPileLength == maxPileLength)&&(identical(other.maxPileDiameter, maxPileDiameter) || other.maxPileDiameter == maxPileDiameter)&&(identical(other.maxSteelRatio, maxSteelRatio) || other.maxSteelRatio == maxSteelRatio)&&(identical(other.slsLoad, slsLoad) || other.slsLoad == slsLoad)&&(identical(other.ulsLoad, ulsLoad) || other.ulsLoad == ulsLoad)&&(identical(other.diameter, diameter) || other.diameter == diameter)&&(identical(other.ratioOfBelloutDia, ratioOfBelloutDia) || other.ratioOfBelloutDia == ratioOfBelloutDia)&&(identical(other.length, length) || other.length == length)&&(identical(other.shaftCapacity, shaftCapacity) || other.shaftCapacity == shaftCapacity)&&(identical(other.baseCapacity, baseCapacity) || other.baseCapacity == baseCapacity)&&(identical(other.totalGroundResistance, totalGroundResistance) || other.totalGroundResistance == totalGroundResistance)&&(identical(other.strCapacity, strCapacity) || other.strCapacity == strCapacity)&&(identical(other.rebar, rebar) || other.rebar == rebar)&&(identical(other.steelRatio, steelRatio) || other.steelRatio == steelRatio)&&(identical(other.isSelected, isSelected) || other.isSelected == isSelected)&&(identical(other.calsLog, calsLog) || other.calsLog == calsLog)&&(identical(other.pileFrictionalBoredSchemeId, pileFrictionalBoredSchemeId) || other.pileFrictionalBoredSchemeId == pileFrictionalBoredSchemeId));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hashAll([runtimeType,sptNValue,soilUnitWeight,kTan,fos,fcu,maxPileLength,maxPileDiameter,maxSteelRatio,slsLoad,ulsLoad,diameter,ratioOfBelloutDia,length,shaftCapacity,baseCapacity,totalGroundResistance,strCapacity,rebar,steelRatio,isSelected,calsLog,pileFrictionalBoredSchemeId]);

@override
String toString() {
  return 'PileFrictionalBoredData(sptNValue: $sptNValue, soilUnitWeight: $soilUnitWeight, kTan: $kTan, fos: $fos, fcu: $fcu, maxPileLength: $maxPileLength, maxPileDiameter: $maxPileDiameter, maxSteelRatio: $maxSteelRatio, slsLoad: $slsLoad, ulsLoad: $ulsLoad, diameter: $diameter, ratioOfBelloutDia: $ratioOfBelloutDia, length: $length, shaftCapacity: $shaftCapacity, baseCapacity: $baseCapacity, totalGroundResistance: $totalGroundResistance, strCapacity: $strCapacity, rebar: $rebar, steelRatio: $steelRatio, isSelected: $isSelected, calsLog: $calsLog, pileFrictionalBoredSchemeId: $pileFrictionalBoredSchemeId)';
}


}

/// @nodoc
abstract mixin class $PileFrictionalBoredDataCopyWith<$Res>  {
  factory $PileFrictionalBoredDataCopyWith(PileFrictionalBoredData value, $Res Function(PileFrictionalBoredData) _then) = _$PileFrictionalBoredDataCopyWithImpl;
@useResult
$Res call({
 double sptNValue, double soilUnitWeight, double kTan, double fos, double fcu, double maxPileLength, double maxPileDiameter, double maxSteelRatio, double slsLoad, double ulsLoad, double diameter, double ratioOfBelloutDia, double length, double shaftCapacity, double baseCapacity, double totalGroundResistance, double strCapacity, String rebar, double steelRatio, bool isSelected, String calsLog, String pileFrictionalBoredSchemeId
});




}
/// @nodoc
class _$PileFrictionalBoredDataCopyWithImpl<$Res>
    implements $PileFrictionalBoredDataCopyWith<$Res> {
  _$PileFrictionalBoredDataCopyWithImpl(this._self, this._then);

  final PileFrictionalBoredData _self;
  final $Res Function(PileFrictionalBoredData) _then;

/// Create a copy of PileFrictionalBoredData
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? sptNValue = null,Object? soilUnitWeight = null,Object? kTan = null,Object? fos = null,Object? fcu = null,Object? maxPileLength = null,Object? maxPileDiameter = null,Object? maxSteelRatio = null,Object? slsLoad = null,Object? ulsLoad = null,Object? diameter = null,Object? ratioOfBelloutDia = null,Object? length = null,Object? shaftCapacity = null,Object? baseCapacity = null,Object? totalGroundResistance = null,Object? strCapacity = null,Object? rebar = null,Object? steelRatio = null,Object? isSelected = null,Object? calsLog = null,Object? pileFrictionalBoredSchemeId = null,}) {
  return _then(_self.copyWith(
sptNValue: null == sptNValue ? _self.sptNValue : sptNValue // ignore: cast_nullable_to_non_nullable
as double,soilUnitWeight: null == soilUnitWeight ? _self.soilUnitWeight : soilUnitWeight // ignore: cast_nullable_to_non_nullable
as double,kTan: null == kTan ? _self.kTan : kTan // ignore: cast_nullable_to_non_nullable
as double,fos: null == fos ? _self.fos : fos // ignore: cast_nullable_to_non_nullable
as double,fcu: null == fcu ? _self.fcu : fcu // ignore: cast_nullable_to_non_nullable
as double,maxPileLength: null == maxPileLength ? _self.maxPileLength : maxPileLength // ignore: cast_nullable_to_non_nullable
as double,maxPileDiameter: null == maxPileDiameter ? _self.maxPileDiameter : maxPileDiameter // ignore: cast_nullable_to_non_nullable
as double,maxSteelRatio: null == maxSteelRatio ? _self.maxSteelRatio : maxSteelRatio // ignore: cast_nullable_to_non_nullable
as double,slsLoad: null == slsLoad ? _self.slsLoad : slsLoad // ignore: cast_nullable_to_non_nullable
as double,ulsLoad: null == ulsLoad ? _self.ulsLoad : ulsLoad // ignore: cast_nullable_to_non_nullable
as double,diameter: null == diameter ? _self.diameter : diameter // ignore: cast_nullable_to_non_nullable
as double,ratioOfBelloutDia: null == ratioOfBelloutDia ? _self.ratioOfBelloutDia : ratioOfBelloutDia // ignore: cast_nullable_to_non_nullable
as double,length: null == length ? _self.length : length // ignore: cast_nullable_to_non_nullable
as double,shaftCapacity: null == shaftCapacity ? _self.shaftCapacity : shaftCapacity // ignore: cast_nullable_to_non_nullable
as double,baseCapacity: null == baseCapacity ? _self.baseCapacity : baseCapacity // ignore: cast_nullable_to_non_nullable
as double,totalGroundResistance: null == totalGroundResistance ? _self.totalGroundResistance : totalGroundResistance // ignore: cast_nullable_to_non_nullable
as double,strCapacity: null == strCapacity ? _self.strCapacity : strCapacity // ignore: cast_nullable_to_non_nullable
as double,rebar: null == rebar ? _self.rebar : rebar // ignore: cast_nullable_to_non_nullable
as String,steelRatio: null == steelRatio ? _self.steelRatio : steelRatio // ignore: cast_nullable_to_non_nullable
as double,isSelected: null == isSelected ? _self.isSelected : isSelected // ignore: cast_nullable_to_non_nullable
as bool,calsLog: null == calsLog ? _self.calsLog : calsLog // ignore: cast_nullable_to_non_nullable
as String,pileFrictionalBoredSchemeId: null == pileFrictionalBoredSchemeId ? _self.pileFrictionalBoredSchemeId : pileFrictionalBoredSchemeId // ignore: cast_nullable_to_non_nullable
as String,
  ));
}

}


/// Adds pattern-matching-related methods to [PileFrictionalBoredData].
extension PileFrictionalBoredDataPatterns on PileFrictionalBoredData {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _PileFrictionalBoredData value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _PileFrictionalBoredData() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _PileFrictionalBoredData value)  $default,){
final _that = this;
switch (_that) {
case _PileFrictionalBoredData():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _PileFrictionalBoredData value)?  $default,){
final _that = this;
switch (_that) {
case _PileFrictionalBoredData() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( double sptNValue,  double soilUnitWeight,  double kTan,  double fos,  double fcu,  double maxPileLength,  double maxPileDiameter,  double maxSteelRatio,  double slsLoad,  double ulsLoad,  double diameter,  double ratioOfBelloutDia,  double length,  double shaftCapacity,  double baseCapacity,  double totalGroundResistance,  double strCapacity,  String rebar,  double steelRatio,  bool isSelected,  String calsLog,  String pileFrictionalBoredSchemeId)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _PileFrictionalBoredData() when $default != null:
return $default(_that.sptNValue,_that.soilUnitWeight,_that.kTan,_that.fos,_that.fcu,_that.maxPileLength,_that.maxPileDiameter,_that.maxSteelRatio,_that.slsLoad,_that.ulsLoad,_that.diameter,_that.ratioOfBelloutDia,_that.length,_that.shaftCapacity,_that.baseCapacity,_that.totalGroundResistance,_that.strCapacity,_that.rebar,_that.steelRatio,_that.isSelected,_that.calsLog,_that.pileFrictionalBoredSchemeId);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( double sptNValue,  double soilUnitWeight,  double kTan,  double fos,  double fcu,  double maxPileLength,  double maxPileDiameter,  double maxSteelRatio,  double slsLoad,  double ulsLoad,  double diameter,  double ratioOfBelloutDia,  double length,  double shaftCapacity,  double baseCapacity,  double totalGroundResistance,  double strCapacity,  String rebar,  double steelRatio,  bool isSelected,  String calsLog,  String pileFrictionalBoredSchemeId)  $default,) {final _that = this;
switch (_that) {
case _PileFrictionalBoredData():
return $default(_that.sptNValue,_that.soilUnitWeight,_that.kTan,_that.fos,_that.fcu,_that.maxPileLength,_that.maxPileDiameter,_that.maxSteelRatio,_that.slsLoad,_that.ulsLoad,_that.diameter,_that.ratioOfBelloutDia,_that.length,_that.shaftCapacity,_that.baseCapacity,_that.totalGroundResistance,_that.strCapacity,_that.rebar,_that.steelRatio,_that.isSelected,_that.calsLog,_that.pileFrictionalBoredSchemeId);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( double sptNValue,  double soilUnitWeight,  double kTan,  double fos,  double fcu,  double maxPileLength,  double maxPileDiameter,  double maxSteelRatio,  double slsLoad,  double ulsLoad,  double diameter,  double ratioOfBelloutDia,  double length,  double shaftCapacity,  double baseCapacity,  double totalGroundResistance,  double strCapacity,  String rebar,  double steelRatio,  bool isSelected,  String calsLog,  String pileFrictionalBoredSchemeId)?  $default,) {final _that = this;
switch (_that) {
case _PileFrictionalBoredData() when $default != null:
return $default(_that.sptNValue,_that.soilUnitWeight,_that.kTan,_that.fos,_that.fcu,_that.maxPileLength,_that.maxPileDiameter,_that.maxSteelRatio,_that.slsLoad,_that.ulsLoad,_that.diameter,_that.ratioOfBelloutDia,_that.length,_that.shaftCapacity,_that.baseCapacity,_that.totalGroundResistance,_that.strCapacity,_that.rebar,_that.steelRatio,_that.isSelected,_that.calsLog,_that.pileFrictionalBoredSchemeId);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _PileFrictionalBoredData extends PileFrictionalBoredData {
   _PileFrictionalBoredData({this.sptNValue = 50, this.soilUnitWeight = 19, this.kTan = 0.25, this.fos = 3, this.fcu = 45, this.maxPileLength = 30, this.maxPileDiameter = 2000, this.maxSteelRatio = 0.04, this.slsLoad = 1000, this.ulsLoad = 2000, this.diameter = 0, this.ratioOfBelloutDia = 1.65, this.length = 0, this.shaftCapacity = 0, this.baseCapacity = 0, this.totalGroundResistance = 0, this.strCapacity = 0, this.rebar = '', this.steelRatio = 0, this.isSelected = false, this.calsLog = '', this.pileFrictionalBoredSchemeId = ''}): super._();
  factory _PileFrictionalBoredData.fromJson(Map<String, dynamic> json) => _$PileFrictionalBoredDataFromJson(json);

@override@JsonKey() final  double sptNValue;
@override@JsonKey() final  double soilUnitWeight;
@override@JsonKey() final  double kTan;
@override@JsonKey() final  double fos;
@override@JsonKey() final  double fcu;
@override@JsonKey() final  double maxPileLength;
@override@JsonKey() final  double maxPileDiameter;
@override@JsonKey() final  double maxSteelRatio;
@override@JsonKey() final  double slsLoad;
@override@JsonKey() final  double ulsLoad;
@override@JsonKey() final  double diameter;
@override@JsonKey() final  double ratioOfBelloutDia;
@override@JsonKey() final  double length;
@override@JsonKey() final  double shaftCapacity;
@override@JsonKey() final  double baseCapacity;
@override@JsonKey() final  double totalGroundResistance;
@override@JsonKey() final  double strCapacity;
@override@JsonKey() final  String rebar;
//will be overriden  as soon as new instance created
@override@JsonKey() final  double steelRatio;
@override@JsonKey() final  bool isSelected;
@override@JsonKey() final  String calsLog;
//will be overriden  as soon as new instance created
@override@JsonKey() final  String pileFrictionalBoredSchemeId;

/// Create a copy of PileFrictionalBoredData
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$PileFrictionalBoredDataCopyWith<_PileFrictionalBoredData> get copyWith => __$PileFrictionalBoredDataCopyWithImpl<_PileFrictionalBoredData>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$PileFrictionalBoredDataToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _PileFrictionalBoredData&&(identical(other.sptNValue, sptNValue) || other.sptNValue == sptNValue)&&(identical(other.soilUnitWeight, soilUnitWeight) || other.soilUnitWeight == soilUnitWeight)&&(identical(other.kTan, kTan) || other.kTan == kTan)&&(identical(other.fos, fos) || other.fos == fos)&&(identical(other.fcu, fcu) || other.fcu == fcu)&&(identical(other.maxPileLength, maxPileLength) || other.maxPileLength == maxPileLength)&&(identical(other.maxPileDiameter, maxPileDiameter) || other.maxPileDiameter == maxPileDiameter)&&(identical(other.maxSteelRatio, maxSteelRatio) || other.maxSteelRatio == maxSteelRatio)&&(identical(other.slsLoad, slsLoad) || other.slsLoad == slsLoad)&&(identical(other.ulsLoad, ulsLoad) || other.ulsLoad == ulsLoad)&&(identical(other.diameter, diameter) || other.diameter == diameter)&&(identical(other.ratioOfBelloutDia, ratioOfBelloutDia) || other.ratioOfBelloutDia == ratioOfBelloutDia)&&(identical(other.length, length) || other.length == length)&&(identical(other.shaftCapacity, shaftCapacity) || other.shaftCapacity == shaftCapacity)&&(identical(other.baseCapacity, baseCapacity) || other.baseCapacity == baseCapacity)&&(identical(other.totalGroundResistance, totalGroundResistance) || other.totalGroundResistance == totalGroundResistance)&&(identical(other.strCapacity, strCapacity) || other.strCapacity == strCapacity)&&(identical(other.rebar, rebar) || other.rebar == rebar)&&(identical(other.steelRatio, steelRatio) || other.steelRatio == steelRatio)&&(identical(other.isSelected, isSelected) || other.isSelected == isSelected)&&(identical(other.calsLog, calsLog) || other.calsLog == calsLog)&&(identical(other.pileFrictionalBoredSchemeId, pileFrictionalBoredSchemeId) || other.pileFrictionalBoredSchemeId == pileFrictionalBoredSchemeId));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hashAll([runtimeType,sptNValue,soilUnitWeight,kTan,fos,fcu,maxPileLength,maxPileDiameter,maxSteelRatio,slsLoad,ulsLoad,diameter,ratioOfBelloutDia,length,shaftCapacity,baseCapacity,totalGroundResistance,strCapacity,rebar,steelRatio,isSelected,calsLog,pileFrictionalBoredSchemeId]);

@override
String toString() {
  return 'PileFrictionalBoredData(sptNValue: $sptNValue, soilUnitWeight: $soilUnitWeight, kTan: $kTan, fos: $fos, fcu: $fcu, maxPileLength: $maxPileLength, maxPileDiameter: $maxPileDiameter, maxSteelRatio: $maxSteelRatio, slsLoad: $slsLoad, ulsLoad: $ulsLoad, diameter: $diameter, ratioOfBelloutDia: $ratioOfBelloutDia, length: $length, shaftCapacity: $shaftCapacity, baseCapacity: $baseCapacity, totalGroundResistance: $totalGroundResistance, strCapacity: $strCapacity, rebar: $rebar, steelRatio: $steelRatio, isSelected: $isSelected, calsLog: $calsLog, pileFrictionalBoredSchemeId: $pileFrictionalBoredSchemeId)';
}


}

/// @nodoc
abstract mixin class _$PileFrictionalBoredDataCopyWith<$Res> implements $PileFrictionalBoredDataCopyWith<$Res> {
  factory _$PileFrictionalBoredDataCopyWith(_PileFrictionalBoredData value, $Res Function(_PileFrictionalBoredData) _then) = __$PileFrictionalBoredDataCopyWithImpl;
@override @useResult
$Res call({
 double sptNValue, double soilUnitWeight, double kTan, double fos, double fcu, double maxPileLength, double maxPileDiameter, double maxSteelRatio, double slsLoad, double ulsLoad, double diameter, double ratioOfBelloutDia, double length, double shaftCapacity, double baseCapacity, double totalGroundResistance, double strCapacity, String rebar, double steelRatio, bool isSelected, String calsLog, String pileFrictionalBoredSchemeId
});




}
/// @nodoc
class __$PileFrictionalBoredDataCopyWithImpl<$Res>
    implements _$PileFrictionalBoredDataCopyWith<$Res> {
  __$PileFrictionalBoredDataCopyWithImpl(this._self, this._then);

  final _PileFrictionalBoredData _self;
  final $Res Function(_PileFrictionalBoredData) _then;

/// Create a copy of PileFrictionalBoredData
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? sptNValue = null,Object? soilUnitWeight = null,Object? kTan = null,Object? fos = null,Object? fcu = null,Object? maxPileLength = null,Object? maxPileDiameter = null,Object? maxSteelRatio = null,Object? slsLoad = null,Object? ulsLoad = null,Object? diameter = null,Object? ratioOfBelloutDia = null,Object? length = null,Object? shaftCapacity = null,Object? baseCapacity = null,Object? totalGroundResistance = null,Object? strCapacity = null,Object? rebar = null,Object? steelRatio = null,Object? isSelected = null,Object? calsLog = null,Object? pileFrictionalBoredSchemeId = null,}) {
  return _then(_PileFrictionalBoredData(
sptNValue: null == sptNValue ? _self.sptNValue : sptNValue // ignore: cast_nullable_to_non_nullable
as double,soilUnitWeight: null == soilUnitWeight ? _self.soilUnitWeight : soilUnitWeight // ignore: cast_nullable_to_non_nullable
as double,kTan: null == kTan ? _self.kTan : kTan // ignore: cast_nullable_to_non_nullable
as double,fos: null == fos ? _self.fos : fos // ignore: cast_nullable_to_non_nullable
as double,fcu: null == fcu ? _self.fcu : fcu // ignore: cast_nullable_to_non_nullable
as double,maxPileLength: null == maxPileLength ? _self.maxPileLength : maxPileLength // ignore: cast_nullable_to_non_nullable
as double,maxPileDiameter: null == maxPileDiameter ? _self.maxPileDiameter : maxPileDiameter // ignore: cast_nullable_to_non_nullable
as double,maxSteelRatio: null == maxSteelRatio ? _self.maxSteelRatio : maxSteelRatio // ignore: cast_nullable_to_non_nullable
as double,slsLoad: null == slsLoad ? _self.slsLoad : slsLoad // ignore: cast_nullable_to_non_nullable
as double,ulsLoad: null == ulsLoad ? _self.ulsLoad : ulsLoad // ignore: cast_nullable_to_non_nullable
as double,diameter: null == diameter ? _self.diameter : diameter // ignore: cast_nullable_to_non_nullable
as double,ratioOfBelloutDia: null == ratioOfBelloutDia ? _self.ratioOfBelloutDia : ratioOfBelloutDia // ignore: cast_nullable_to_non_nullable
as double,length: null == length ? _self.length : length // ignore: cast_nullable_to_non_nullable
as double,shaftCapacity: null == shaftCapacity ? _self.shaftCapacity : shaftCapacity // ignore: cast_nullable_to_non_nullable
as double,baseCapacity: null == baseCapacity ? _self.baseCapacity : baseCapacity // ignore: cast_nullable_to_non_nullable
as double,totalGroundResistance: null == totalGroundResistance ? _self.totalGroundResistance : totalGroundResistance // ignore: cast_nullable_to_non_nullable
as double,strCapacity: null == strCapacity ? _self.strCapacity : strCapacity // ignore: cast_nullable_to_non_nullable
as double,rebar: null == rebar ? _self.rebar : rebar // ignore: cast_nullable_to_non_nullable
as String,steelRatio: null == steelRatio ? _self.steelRatio : steelRatio // ignore: cast_nullable_to_non_nullable
as double,isSelected: null == isSelected ? _self.isSelected : isSelected // ignore: cast_nullable_to_non_nullable
as bool,calsLog: null == calsLog ? _self.calsLog : calsLog // ignore: cast_nullable_to_non_nullable
as String,pileFrictionalBoredSchemeId: null == pileFrictionalBoredSchemeId ? _self.pileFrictionalBoredSchemeId : pileFrictionalBoredSchemeId // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

// dart format on
