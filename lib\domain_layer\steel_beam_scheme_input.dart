import 'package:freezed_annotation/freezed_annotation.dart';
// import 'package:flutter/foundation.dart';
part 'steel_beam_scheme_input.freezed.dart';
part 'steel_beam_scheme_input.g.dart';

@freezed
abstract class SteelBeamSchemeInput with _$SteelBeamSchemeInput {
  const SteelBeamSchemeInput._();
  factory SteelBeamSchemeInput({
    @Default('1') String id,
    @Default(6.0) double shortSpan,
    @Default(10.0) double longSpan,
    @Default(2) int bays,
    @Default(500.0) double strZone,
    @Default(355.0) double fsy,
    @Default(1.0) double shortSpanIncreament,
    @Default(1.0) double longSpanIncreament,
    @Default(1) int baysIncreament,
    @Default(10) int iterationSteps,
    @Default('') String usage,
    @Default(130.0) double slabThickness,
    @Default(1.0) double compositeActionFactor,
  }) = _SteelBeamSchemeInput;

  factory SteelBeamSchemeInput.fromJson(Map<String, Object?> json) =>
      _$SteelBeamSchemeInputFromJson(json);
}
