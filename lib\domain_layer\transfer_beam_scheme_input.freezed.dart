// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'transfer_beam_scheme_input.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$TransferBeamSchemeInput {

 double get pointLoad; double get distA; bool get loadFromSelectedCol; String get transferBeamSchemeInputId;
/// Create a copy of TransferBeamSchemeInput
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$TransferBeamSchemeInputCopyWith<TransferBeamSchemeInput> get copyWith => _$TransferBeamSchemeInputCopyWithImpl<TransferBeamSchemeInput>(this as TransferBeamSchemeInput, _$identity);

  /// Serializes this TransferBeamSchemeInput to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is TransferBeamSchemeInput&&(identical(other.pointLoad, pointLoad) || other.pointLoad == pointLoad)&&(identical(other.distA, distA) || other.distA == distA)&&(identical(other.loadFromSelectedCol, loadFromSelectedCol) || other.loadFromSelectedCol == loadFromSelectedCol)&&(identical(other.transferBeamSchemeInputId, transferBeamSchemeInputId) || other.transferBeamSchemeInputId == transferBeamSchemeInputId));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,pointLoad,distA,loadFromSelectedCol,transferBeamSchemeInputId);

@override
String toString() {
  return 'TransferBeamSchemeInput(pointLoad: $pointLoad, distA: $distA, loadFromSelectedCol: $loadFromSelectedCol, transferBeamSchemeInputId: $transferBeamSchemeInputId)';
}


}

/// @nodoc
abstract mixin class $TransferBeamSchemeInputCopyWith<$Res>  {
  factory $TransferBeamSchemeInputCopyWith(TransferBeamSchemeInput value, $Res Function(TransferBeamSchemeInput) _then) = _$TransferBeamSchemeInputCopyWithImpl;
@useResult
$Res call({
 double pointLoad, double distA, bool loadFromSelectedCol, String transferBeamSchemeInputId
});




}
/// @nodoc
class _$TransferBeamSchemeInputCopyWithImpl<$Res>
    implements $TransferBeamSchemeInputCopyWith<$Res> {
  _$TransferBeamSchemeInputCopyWithImpl(this._self, this._then);

  final TransferBeamSchemeInput _self;
  final $Res Function(TransferBeamSchemeInput) _then;

/// Create a copy of TransferBeamSchemeInput
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? pointLoad = null,Object? distA = null,Object? loadFromSelectedCol = null,Object? transferBeamSchemeInputId = null,}) {
  return _then(_self.copyWith(
pointLoad: null == pointLoad ? _self.pointLoad : pointLoad // ignore: cast_nullable_to_non_nullable
as double,distA: null == distA ? _self.distA : distA // ignore: cast_nullable_to_non_nullable
as double,loadFromSelectedCol: null == loadFromSelectedCol ? _self.loadFromSelectedCol : loadFromSelectedCol // ignore: cast_nullable_to_non_nullable
as bool,transferBeamSchemeInputId: null == transferBeamSchemeInputId ? _self.transferBeamSchemeInputId : transferBeamSchemeInputId // ignore: cast_nullable_to_non_nullable
as String,
  ));
}

}


/// Adds pattern-matching-related methods to [TransferBeamSchemeInput].
extension TransferBeamSchemeInputPatterns on TransferBeamSchemeInput {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _TransferBeamSchemeInput value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _TransferBeamSchemeInput() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _TransferBeamSchemeInput value)  $default,){
final _that = this;
switch (_that) {
case _TransferBeamSchemeInput():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _TransferBeamSchemeInput value)?  $default,){
final _that = this;
switch (_that) {
case _TransferBeamSchemeInput() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( double pointLoad,  double distA,  bool loadFromSelectedCol,  String transferBeamSchemeInputId)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _TransferBeamSchemeInput() when $default != null:
return $default(_that.pointLoad,_that.distA,_that.loadFromSelectedCol,_that.transferBeamSchemeInputId);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( double pointLoad,  double distA,  bool loadFromSelectedCol,  String transferBeamSchemeInputId)  $default,) {final _that = this;
switch (_that) {
case _TransferBeamSchemeInput():
return $default(_that.pointLoad,_that.distA,_that.loadFromSelectedCol,_that.transferBeamSchemeInputId);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( double pointLoad,  double distA,  bool loadFromSelectedCol,  String transferBeamSchemeInputId)?  $default,) {final _that = this;
switch (_that) {
case _TransferBeamSchemeInput() when $default != null:
return $default(_that.pointLoad,_that.distA,_that.loadFromSelectedCol,_that.transferBeamSchemeInputId);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _TransferBeamSchemeInput extends TransferBeamSchemeInput {
   _TransferBeamSchemeInput({this.pointLoad = 0.0, this.distA = 3.0, this.loadFromSelectedCol = false, this.transferBeamSchemeInputId = ''}): super._();
  factory _TransferBeamSchemeInput.fromJson(Map<String, dynamic> json) => _$TransferBeamSchemeInputFromJson(json);

@override@JsonKey() final  double pointLoad;
@override@JsonKey() final  double distA;
@override@JsonKey() final  bool loadFromSelectedCol;
@override@JsonKey() final  String transferBeamSchemeInputId;

/// Create a copy of TransferBeamSchemeInput
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$TransferBeamSchemeInputCopyWith<_TransferBeamSchemeInput> get copyWith => __$TransferBeamSchemeInputCopyWithImpl<_TransferBeamSchemeInput>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$TransferBeamSchemeInputToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _TransferBeamSchemeInput&&(identical(other.pointLoad, pointLoad) || other.pointLoad == pointLoad)&&(identical(other.distA, distA) || other.distA == distA)&&(identical(other.loadFromSelectedCol, loadFromSelectedCol) || other.loadFromSelectedCol == loadFromSelectedCol)&&(identical(other.transferBeamSchemeInputId, transferBeamSchemeInputId) || other.transferBeamSchemeInputId == transferBeamSchemeInputId));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,pointLoad,distA,loadFromSelectedCol,transferBeamSchemeInputId);

@override
String toString() {
  return 'TransferBeamSchemeInput(pointLoad: $pointLoad, distA: $distA, loadFromSelectedCol: $loadFromSelectedCol, transferBeamSchemeInputId: $transferBeamSchemeInputId)';
}


}

/// @nodoc
abstract mixin class _$TransferBeamSchemeInputCopyWith<$Res> implements $TransferBeamSchemeInputCopyWith<$Res> {
  factory _$TransferBeamSchemeInputCopyWith(_TransferBeamSchemeInput value, $Res Function(_TransferBeamSchemeInput) _then) = __$TransferBeamSchemeInputCopyWithImpl;
@override @useResult
$Res call({
 double pointLoad, double distA, bool loadFromSelectedCol, String transferBeamSchemeInputId
});




}
/// @nodoc
class __$TransferBeamSchemeInputCopyWithImpl<$Res>
    implements _$TransferBeamSchemeInputCopyWith<$Res> {
  __$TransferBeamSchemeInputCopyWithImpl(this._self, this._then);

  final _TransferBeamSchemeInput _self;
  final $Res Function(_TransferBeamSchemeInput) _then;

/// Create a copy of TransferBeamSchemeInput
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? pointLoad = null,Object? distA = null,Object? loadFromSelectedCol = null,Object? transferBeamSchemeInputId = null,}) {
  return _then(_TransferBeamSchemeInput(
pointLoad: null == pointLoad ? _self.pointLoad : pointLoad // ignore: cast_nullable_to_non_nullable
as double,distA: null == distA ? _self.distA : distA // ignore: cast_nullable_to_non_nullable
as double,loadFromSelectedCol: null == loadFromSelectedCol ? _self.loadFromSelectedCol : loadFromSelectedCol // ignore: cast_nullable_to_non_nullable
as bool,transferBeamSchemeInputId: null == transferBeamSchemeInputId ? _self.transferBeamSchemeInputId : transferBeamSchemeInputId // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

// dart format on
