// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'app_database_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$appDatabaseControllerHash() =>
    r'64f8da432a78ca4c60eec55908f3d435a4e2c6d3';

/// See also [AppDatabaseController].
@ProviderFor(AppDatabaseController)
final appDatabaseControllerProvider = AutoDisposeAsyncNotifierProvider<
  AppDatabaseController,
  AppDatabase
>.internal(
  AppDatabaseController.new,
  name: r'appDatabaseControllerProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$appDatabaseControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$AppDatabaseController = AutoDisposeAsyncNotifier<AppDatabase>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
