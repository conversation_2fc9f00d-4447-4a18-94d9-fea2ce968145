import 'package:riverpod_annotation/riverpod_annotation.dart';

//presentation layer
import '../../domain_layer/slab_scheme_data.dart';
import '../../domain_layer/steel_beam_scheme_input.dart';
import '../screen/homescreen.dart';

// domain layer

part 'steel_beam_scheme_input_controller.g.dart';

@riverpod
class SteelBeamSchemeInputController extends _$SteelBeamSchemeInputController {
  @override
  FutureOr<SteelBeamSchemeInput> build() async {
    SteelBeamSchemeInput steelBeamSchemeInput =
        await ref
            .read(appDatabaseControllerProvider.notifier)
            .querySteelBeamSchemeInput();
    final data1 = ref.watch(loadingTablesControllerProvider);
    return data1.when(
      data: (loadingTables) async {
            //* Check if input use loading not exist in database
            final usages = loadingTables.map((x) => x.usage).toList();
            if (!usages.contains(steelBeamSchemeInput.usage)) {
              // if input's usage is deleted from loading table,
              // update input's usage to first usage then return again
              // to avoid error in its UI counterpart: usage dropdownlist
              // await updateTable(usage: usages[0]); // this won't work as infinite waiting results
              steelBeamSchemeInput = steelBeamSchemeInput.copyWith(
                usage: usages[0],
              );
            }
            return steelBeamSchemeInput;
          },
      error: (error, stackTrace) => SteelBeamSchemeInput(),
      loading: () => SteelBeamSchemeInput(),
    );
  }

  Future<void> updateTable({
    String? id,
    double? shortSpan,
    double? longSpan,
    int? bays,
    double? strZone,
    double? fsy,
    double? shortSpanIncreament,
    double? longSpanIncreament,
    int? baysIncreament,
    int? iterationSteps,
    String? usage,
    double? slabThickness,
    double? compositeActionFactor,
  }) async {
    final x = await future;
    // Create a list of updates
    final newState = x.copyWith(
      id: id ?? x.id,
      shortSpan: shortSpan ?? x.shortSpan,
      longSpan: longSpan ?? x.longSpan,
      bays: bays ?? x.bays,
      strZone: strZone ?? x.strZone,
      fsy: fsy ?? x.fsy,
      shortSpanIncreament: shortSpanIncreament ?? x.shortSpanIncreament,
      longSpanIncreament: longSpanIncreament ?? x.longSpanIncreament,
      baysIncreament: baysIncreament ?? x.baysIncreament,
      iterationSteps: iterationSteps ?? x.iterationSteps,
      usage: usage ?? x.usage,
      slabThickness: slabThickness ?? x.slabThickness,
      compositeActionFactor: compositeActionFactor ?? x.compositeActionFactor,
    );

    // Update the state only if there are changes
    state = AsyncData(newState);
  }
}
