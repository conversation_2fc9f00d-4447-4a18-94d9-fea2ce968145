// import 'dart:nativewrappers/_internal/vm/lib/math_patch.dart';
import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:structify/domain_layer/data_struct/recommend_load.dart';

//below for printing
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:printing/printing.dart';
// import 'dart:typed_data';
// import 'dart:ui';

//domain layer

// presentation layer
import '../../domain_layer/preferences.dart';
import '../screen/homescreen.dart';
// import '../small_elements/custom_stateful_double_input.dart';
// import '../small_elements/custom_stateful_text_input.dart';
// import '../small_elements/global_data_ui.dart';

//misc

import 'button/selection_button.dart';

class RecommendedLoadingSummary extends ConsumerStatefulWidget {
  const RecommendedLoadingSummary({
    this.isExpanded,
    this.maxHeight,
    this.minHeight,
    super.key,
  });

  final bool? isExpanded;
  final double? maxHeight;
  final double? minHeight;

  @override
  ConsumerState<ConsumerStatefulWidget> createState() =>
      _RecommendedLoadingSummaryState();
}

class _RecommendedLoadingSummaryState
    extends ConsumerState<RecommendedLoadingSummary>
    with WidgetsBindingObserver {
  late bool _isExpanded;
  late double _maxHeight;
  late double _minHeight;
  late ScrollController _scrollController;
  late final List<RecommendedLoad> recommendedLoading = [];

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void initState() {
    super.initState();

    WidgetsBinding.instance.addObserver(this);
    _isExpanded = widget.isExpanded ?? false;
    _maxHeight = widget.maxHeight ?? 300;
    _minHeight = widget.minHeight ?? 50;
    _scrollController = ScrollController();
  }

  @override
  void didChangeDependencies() {
    _updateHeights();
    super.didChangeDependencies();
    // Update heights when dependencies change, including after initState
  }

  @override
  void didChangeMetrics() {
    // This method is called when the metrics change (like resizing the window)
    setState(() {
      _updateHeights();
    });
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;
    final globalData = ref.watch(globalDataControllerProvider);
    final recommendedLoading = ref.watch(recommendedLoadControllerProvider);
    final TextStyle titleTextStyle = textTheme.titleMedium!.copyWith(
      color: colorScheme.onSurface,
    );
    final TextStyle bodyTextStyle = textTheme.bodyMedium!.copyWith(
      color: colorScheme.onSurface,
    );
    final TextStyle highlightText = textTheme.labelLarge!.copyWith(
      color: colorScheme.primary,
    );
    late final List<String> unit;
    return globalData.when(
      data: (data) {
        if (data.unit == 'metrics') {
          unit = PreferredUnit.metrics;
        } else {
          unit = PreferredUnit.imperial;
        }
        return recommendedLoading.when(
          data: (loading) {
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding: const EdgeInsets.fromLTRB(10, 0, 10, 0),
                  child: Divider(),
                ),
                Padding(
                  padding: const EdgeInsets.fromLTRB(10, 0, 0, 0),
                  child: Align(
                    alignment: Alignment.centerLeft,
                    child: Wrap(
                      crossAxisAlignment: WrapCrossAlignment.center,
                      children: [
                        IconButton(
                          icon: Icon(
                            _isExpanded ? Icons.expand_less : Icons.expand_more,
                          ),
                          color: colorScheme.onSurface,
                          onPressed: () {
                            setState(() {
                              _isExpanded = !_isExpanded;
                            });
                          },
                        ),
                        Text(
                          'Recommanded Loading',
                          style: textTheme.titleLarge!.copyWith(
                            color: colorScheme.onSurface,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.fromLTRB(10, 0, 10, 0),
                  child: Divider(),
                ),

                ClipRect(
                  child: ConstrainedBox(
                    constraints: BoxConstraints(
                      maxHeight: _isExpanded ? _maxHeight : 0,
                    ),
                    child: Scrollbar(
                      controller: _scrollController,
                      thumbVisibility: true,
                      trackVisibility: false,
                      child: SingleChildScrollView(
                        scrollDirection: Axis.vertical,
                        controller: _scrollController,
                        child: Padding(
                          padding: const EdgeInsets.fromLTRB(10, 0, 0, 0),
                          child: DefaultTextStyle(
                            style: textTheme.bodyMedium!.copyWith(
                              color: colorScheme.onSurface,
                            ),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                _itemBlockWithTable(
                                  backgroundColor: Colors.transparent,
                                  borderColor: Colors.transparent,
                                  '1. Use below values only if question does not specify the loading\n2. Finish unit weight = ${data.finishUnitWeight} [${unit[9]}]',
                                  [
                                    'Usage',
                                    'Finish [${unit[4]}]',
                                    'Service [${unit[1]}]',
                                    'Partition [${unit[1]}]',
                                    'SDL [${unit[1]}]',
                                    'LL [${unit[1]}]',
                                    'FRP [hrs]',
                                  ],
                                  loading,
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            );
          },
          error: (error, stackTrace) {
            return Text('Error: $error');
          },
          loading: () {
            return Text('Loading...');
          },
        );
        ;
      },
      error: (error, stackTrace) => Text(error.toString()),
      loading: () => CircularProgressIndicator(),
    );
  }

  Widget _itemBlockWithTable(
    String requirementTitle,
    List<String> requirementSutitle,
    List<RecommendedLoad> requirementDetails, {
    Color? backgroundColor,
    Color? borderColor,
  }) {
    //*initialize
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;
    backgroundColor ??= colorScheme.primaryContainer.withAlpha(50);
    borderColor ??= colorScheme.primary.withAlpha(50);
    final NumberFormat f1 = NumberFormat('0.0');
    return Padding(
      padding: const EdgeInsets.fromLTRB(0, 8, 0, 8),
      child: Container(
        decoration: BoxDecoration(
          color: backgroundColor,
          borderRadius: BorderRadius.circular(8.0),
          border: Border.all(width: 1.5, color: borderColor),
        ),
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: DefaultTextStyle(
            style: textTheme.bodyMedium!.copyWith(
              color: colorScheme.onPrimaryContainer,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  requirementTitle,
                  style: textTheme.labelLarge!.copyWith(
                    color: colorScheme.onPrimaryContainer,
                  ),
                ),
                SizedBox(height: 5.0),
                Row(
                  children: [
                    Expanded(
                      flex: 2,
                      child: Table(
                        defaultVerticalAlignment:
                            TableCellVerticalAlignment.middle,
                        border: TableBorder.all(
                          color: colorScheme.onPrimaryContainer.withAlpha(50),
                          width: 0.75,
                        ),
                        // columnWidths: const {
                        //   0: FlexColumnWidth(2),
                        //   1: FlexColumnWidth(3),
                        // },
                        children: [
                          TableRow(
                            decoration: BoxDecoration(
                              color: colorScheme.primaryContainer.withAlpha(
                                200,
                              ),
                            ),
                            children: [
                              ...List.generate(requirementSutitle.length, (
                                index,
                              ) {
                                return Align(
                                  child: Padding(
                                    padding: const EdgeInsets.all(4.0),
                                    child: Text(
                                      requirementSutitle[index],
                                      style: textTheme.labelLarge!.copyWith(
                                        color: colorScheme.onPrimaryContainer,
                                      ),
                                    ),
                                  ),
                                );
                              }),
                            ],
                          ),
                          ...List.generate(requirementDetails.length, (index) {
                            return TableRow(
                              decoration: BoxDecoration(
                                color: colorScheme.primaryContainer.withAlpha(
                                  50,
                                ),
                              ),
                              children:
                                  requirementDetails[index].toJson().entries.map((
                                    entry,
                                  ) {
                                    return Align(
                                      child: Padding(
                                        padding: const EdgeInsets.all(4.0),
                                        child: Text(
                                          entry.value is num
                                              ? '${f1.format(entry.value)}'
                                              : entry.value,
                                          style: textTheme.bodyMedium!.copyWith(
                                            color:
                                                colorScheme.onPrimaryContainer,
                                          ),
                                        ),
                                      ),
                                    );
                                  }).toList(),
                            );
                          }),
                        ],
                      ),
                    ),
                    SizedBox(width: 10.0),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildVerticalDividor(
    List<String> requirement,
    List<String> solution, {
    Color dividorColor = Colors.black,
    double dividorWidth = 1.5,
  }) {
    return Container(
      width: 10,
      decoration: BoxDecoration(
        border: BorderDirectional(
          start: BorderSide(color: dividorColor, width: dividorWidth),
        ),
      ),
      // child: LayoutBuilder(
      //   builder: (context, constraints) {
      //     return SizedBox(
      //       height: constraints.maxHeight,
      //     );
      //   },
      child: Column(
        children: [
          ...List.generate(max(requirement.length + 1, solution.length), (
            index,
          ) {
            return Text('');
          }),
        ],
      ),
    );
  }

  // Method to update heights based on the current media query
  void _updateHeights() {
    final double screenHeight = MediaQuery.of(context).size.height;
    _maxHeight =
        (widget.maxHeight == null)
            ? screenHeight * 0.7
            : (widget.maxHeight! > screenHeight * 0.7)
            ? screenHeight * 0.7
            : widget.maxHeight!;

    _minHeight =
        (widget.minHeight == null)
            ? screenHeight * 0.1
            : (widget.minHeight! > screenHeight * 0.1)
            ? screenHeight * 0.1
            : widget.minHeight!;

    // Adjust _maxHeight if needed

    if (_maxHeight < _minHeight) {
      _maxHeight = _minHeight;
    }
  }
}
