// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'steel_transfer_truss_scheme_input_global.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$SteelTransferTrussSchemeInputGlobal {

 String get id; double get span; double get loadWidth; double get strZone; double get fsy; double get unbracedLength; String get usage; double get slabThickness;
/// Create a copy of SteelTransferTrussSchemeInputGlobal
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$SteelTransferTrussSchemeInputGlobalCopyWith<SteelTransferTrussSchemeInputGlobal> get copyWith => _$SteelTransferTrussSchemeInputGlobalCopyWithImpl<SteelTransferTrussSchemeInputGlobal>(this as SteelTransferTrussSchemeInputGlobal, _$identity);

  /// Serializes this SteelTransferTrussSchemeInputGlobal to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is SteelTransferTrussSchemeInputGlobal&&(identical(other.id, id) || other.id == id)&&(identical(other.span, span) || other.span == span)&&(identical(other.loadWidth, loadWidth) || other.loadWidth == loadWidth)&&(identical(other.strZone, strZone) || other.strZone == strZone)&&(identical(other.fsy, fsy) || other.fsy == fsy)&&(identical(other.unbracedLength, unbracedLength) || other.unbracedLength == unbracedLength)&&(identical(other.usage, usage) || other.usage == usage)&&(identical(other.slabThickness, slabThickness) || other.slabThickness == slabThickness));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,span,loadWidth,strZone,fsy,unbracedLength,usage,slabThickness);

@override
String toString() {
  return 'SteelTransferTrussSchemeInputGlobal(id: $id, span: $span, loadWidth: $loadWidth, strZone: $strZone, fsy: $fsy, unbracedLength: $unbracedLength, usage: $usage, slabThickness: $slabThickness)';
}


}

/// @nodoc
abstract mixin class $SteelTransferTrussSchemeInputGlobalCopyWith<$Res>  {
  factory $SteelTransferTrussSchemeInputGlobalCopyWith(SteelTransferTrussSchemeInputGlobal value, $Res Function(SteelTransferTrussSchemeInputGlobal) _then) = _$SteelTransferTrussSchemeInputGlobalCopyWithImpl;
@useResult
$Res call({
 String id, double span, double loadWidth, double strZone, double fsy, double unbracedLength, String usage, double slabThickness
});




}
/// @nodoc
class _$SteelTransferTrussSchemeInputGlobalCopyWithImpl<$Res>
    implements $SteelTransferTrussSchemeInputGlobalCopyWith<$Res> {
  _$SteelTransferTrussSchemeInputGlobalCopyWithImpl(this._self, this._then);

  final SteelTransferTrussSchemeInputGlobal _self;
  final $Res Function(SteelTransferTrussSchemeInputGlobal) _then;

/// Create a copy of SteelTransferTrussSchemeInputGlobal
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? span = null,Object? loadWidth = null,Object? strZone = null,Object? fsy = null,Object? unbracedLength = null,Object? usage = null,Object? slabThickness = null,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,span: null == span ? _self.span : span // ignore: cast_nullable_to_non_nullable
as double,loadWidth: null == loadWidth ? _self.loadWidth : loadWidth // ignore: cast_nullable_to_non_nullable
as double,strZone: null == strZone ? _self.strZone : strZone // ignore: cast_nullable_to_non_nullable
as double,fsy: null == fsy ? _self.fsy : fsy // ignore: cast_nullable_to_non_nullable
as double,unbracedLength: null == unbracedLength ? _self.unbracedLength : unbracedLength // ignore: cast_nullable_to_non_nullable
as double,usage: null == usage ? _self.usage : usage // ignore: cast_nullable_to_non_nullable
as String,slabThickness: null == slabThickness ? _self.slabThickness : slabThickness // ignore: cast_nullable_to_non_nullable
as double,
  ));
}

}


/// Adds pattern-matching-related methods to [SteelTransferTrussSchemeInputGlobal].
extension SteelTransferTrussSchemeInputGlobalPatterns on SteelTransferTrussSchemeInputGlobal {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _SteelTransferTrussSchemeInputGlobal value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _SteelTransferTrussSchemeInputGlobal() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _SteelTransferTrussSchemeInputGlobal value)  $default,){
final _that = this;
switch (_that) {
case _SteelTransferTrussSchemeInputGlobal():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _SteelTransferTrussSchemeInputGlobal value)?  $default,){
final _that = this;
switch (_that) {
case _SteelTransferTrussSchemeInputGlobal() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  double span,  double loadWidth,  double strZone,  double fsy,  double unbracedLength,  String usage,  double slabThickness)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _SteelTransferTrussSchemeInputGlobal() when $default != null:
return $default(_that.id,_that.span,_that.loadWidth,_that.strZone,_that.fsy,_that.unbracedLength,_that.usage,_that.slabThickness);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  double span,  double loadWidth,  double strZone,  double fsy,  double unbracedLength,  String usage,  double slabThickness)  $default,) {final _that = this;
switch (_that) {
case _SteelTransferTrussSchemeInputGlobal():
return $default(_that.id,_that.span,_that.loadWidth,_that.strZone,_that.fsy,_that.unbracedLength,_that.usage,_that.slabThickness);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  double span,  double loadWidth,  double strZone,  double fsy,  double unbracedLength,  String usage,  double slabThickness)?  $default,) {final _that = this;
switch (_that) {
case _SteelTransferTrussSchemeInputGlobal() when $default != null:
return $default(_that.id,_that.span,_that.loadWidth,_that.strZone,_that.fsy,_that.unbracedLength,_that.usage,_that.slabThickness);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _SteelTransferTrussSchemeInputGlobal extends SteelTransferTrussSchemeInputGlobal {
   _SteelTransferTrussSchemeInputGlobal({this.id = '1', this.span = 20.0, this.loadWidth = 10.0, this.strZone = 1200.0, this.fsy = 355.0, this.unbracedLength = 5.0, this.usage = '', this.slabThickness = 130.0}): super._();
  factory _SteelTransferTrussSchemeInputGlobal.fromJson(Map<String, dynamic> json) => _$SteelTransferTrussSchemeInputGlobalFromJson(json);

@override@JsonKey() final  String id;
@override@JsonKey() final  double span;
@override@JsonKey() final  double loadWidth;
@override@JsonKey() final  double strZone;
@override@JsonKey() final  double fsy;
@override@JsonKey() final  double unbracedLength;
@override@JsonKey() final  String usage;
@override@JsonKey() final  double slabThickness;

/// Create a copy of SteelTransferTrussSchemeInputGlobal
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$SteelTransferTrussSchemeInputGlobalCopyWith<_SteelTransferTrussSchemeInputGlobal> get copyWith => __$SteelTransferTrussSchemeInputGlobalCopyWithImpl<_SteelTransferTrussSchemeInputGlobal>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$SteelTransferTrussSchemeInputGlobalToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _SteelTransferTrussSchemeInputGlobal&&(identical(other.id, id) || other.id == id)&&(identical(other.span, span) || other.span == span)&&(identical(other.loadWidth, loadWidth) || other.loadWidth == loadWidth)&&(identical(other.strZone, strZone) || other.strZone == strZone)&&(identical(other.fsy, fsy) || other.fsy == fsy)&&(identical(other.unbracedLength, unbracedLength) || other.unbracedLength == unbracedLength)&&(identical(other.usage, usage) || other.usage == usage)&&(identical(other.slabThickness, slabThickness) || other.slabThickness == slabThickness));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,span,loadWidth,strZone,fsy,unbracedLength,usage,slabThickness);

@override
String toString() {
  return 'SteelTransferTrussSchemeInputGlobal(id: $id, span: $span, loadWidth: $loadWidth, strZone: $strZone, fsy: $fsy, unbracedLength: $unbracedLength, usage: $usage, slabThickness: $slabThickness)';
}


}

/// @nodoc
abstract mixin class _$SteelTransferTrussSchemeInputGlobalCopyWith<$Res> implements $SteelTransferTrussSchemeInputGlobalCopyWith<$Res> {
  factory _$SteelTransferTrussSchemeInputGlobalCopyWith(_SteelTransferTrussSchemeInputGlobal value, $Res Function(_SteelTransferTrussSchemeInputGlobal) _then) = __$SteelTransferTrussSchemeInputGlobalCopyWithImpl;
@override @useResult
$Res call({
 String id, double span, double loadWidth, double strZone, double fsy, double unbracedLength, String usage, double slabThickness
});




}
/// @nodoc
class __$SteelTransferTrussSchemeInputGlobalCopyWithImpl<$Res>
    implements _$SteelTransferTrussSchemeInputGlobalCopyWith<$Res> {
  __$SteelTransferTrussSchemeInputGlobalCopyWithImpl(this._self, this._then);

  final _SteelTransferTrussSchemeInputGlobal _self;
  final $Res Function(_SteelTransferTrussSchemeInputGlobal) _then;

/// Create a copy of SteelTransferTrussSchemeInputGlobal
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? span = null,Object? loadWidth = null,Object? strZone = null,Object? fsy = null,Object? unbracedLength = null,Object? usage = null,Object? slabThickness = null,}) {
  return _then(_SteelTransferTrussSchemeInputGlobal(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,span: null == span ? _self.span : span // ignore: cast_nullable_to_non_nullable
as double,loadWidth: null == loadWidth ? _self.loadWidth : loadWidth // ignore: cast_nullable_to_non_nullable
as double,strZone: null == strZone ? _self.strZone : strZone // ignore: cast_nullable_to_non_nullable
as double,fsy: null == fsy ? _self.fsy : fsy // ignore: cast_nullable_to_non_nullable
as double,unbracedLength: null == unbracedLength ? _self.unbracedLength : unbracedLength // ignore: cast_nullable_to_non_nullable
as double,usage: null == usage ? _self.usage : usage // ignore: cast_nullable_to_non_nullable
as String,slabThickness: null == slabThickness ? _self.slabThickness : slabThickness // ignore: cast_nullable_to_non_nullable
as double,
  ));
}


}

// dart format on
