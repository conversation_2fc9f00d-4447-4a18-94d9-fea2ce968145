// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'pile_end_bearing_bored_data.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$PileEndBearingBoredData {

 double get safeBearing; double get fos; double get fcu; double get maxPileDiameter; double get maxSteelRatio; double get slsLoad; double get ulsLoad; double get diameter; double get ratioOfBelloutDia; double get length; double get baseCapacity; double get totalGroundResistance; double get strCapacity; String get rebar;//will be overriden  as soon as new instance created
 double get steelRatio; bool get isSelected; String get calsLog;//will be overriden  as soon as new instance created
 String get pileEndBearingBoredSchemeId;
/// Create a copy of PileEndBearingBoredData
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$PileEndBearingBoredDataCopyWith<PileEndBearingBoredData> get copyWith => _$PileEndBearingBoredDataCopyWithImpl<PileEndBearingBoredData>(this as PileEndBearingBoredData, _$identity);

  /// Serializes this PileEndBearingBoredData to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is PileEndBearingBoredData&&(identical(other.safeBearing, safeBearing) || other.safeBearing == safeBearing)&&(identical(other.fos, fos) || other.fos == fos)&&(identical(other.fcu, fcu) || other.fcu == fcu)&&(identical(other.maxPileDiameter, maxPileDiameter) || other.maxPileDiameter == maxPileDiameter)&&(identical(other.maxSteelRatio, maxSteelRatio) || other.maxSteelRatio == maxSteelRatio)&&(identical(other.slsLoad, slsLoad) || other.slsLoad == slsLoad)&&(identical(other.ulsLoad, ulsLoad) || other.ulsLoad == ulsLoad)&&(identical(other.diameter, diameter) || other.diameter == diameter)&&(identical(other.ratioOfBelloutDia, ratioOfBelloutDia) || other.ratioOfBelloutDia == ratioOfBelloutDia)&&(identical(other.length, length) || other.length == length)&&(identical(other.baseCapacity, baseCapacity) || other.baseCapacity == baseCapacity)&&(identical(other.totalGroundResistance, totalGroundResistance) || other.totalGroundResistance == totalGroundResistance)&&(identical(other.strCapacity, strCapacity) || other.strCapacity == strCapacity)&&(identical(other.rebar, rebar) || other.rebar == rebar)&&(identical(other.steelRatio, steelRatio) || other.steelRatio == steelRatio)&&(identical(other.isSelected, isSelected) || other.isSelected == isSelected)&&(identical(other.calsLog, calsLog) || other.calsLog == calsLog)&&(identical(other.pileEndBearingBoredSchemeId, pileEndBearingBoredSchemeId) || other.pileEndBearingBoredSchemeId == pileEndBearingBoredSchemeId));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,safeBearing,fos,fcu,maxPileDiameter,maxSteelRatio,slsLoad,ulsLoad,diameter,ratioOfBelloutDia,length,baseCapacity,totalGroundResistance,strCapacity,rebar,steelRatio,isSelected,calsLog,pileEndBearingBoredSchemeId);

@override
String toString() {
  return 'PileEndBearingBoredData(safeBearing: $safeBearing, fos: $fos, fcu: $fcu, maxPileDiameter: $maxPileDiameter, maxSteelRatio: $maxSteelRatio, slsLoad: $slsLoad, ulsLoad: $ulsLoad, diameter: $diameter, ratioOfBelloutDia: $ratioOfBelloutDia, length: $length, baseCapacity: $baseCapacity, totalGroundResistance: $totalGroundResistance, strCapacity: $strCapacity, rebar: $rebar, steelRatio: $steelRatio, isSelected: $isSelected, calsLog: $calsLog, pileEndBearingBoredSchemeId: $pileEndBearingBoredSchemeId)';
}


}

/// @nodoc
abstract mixin class $PileEndBearingBoredDataCopyWith<$Res>  {
  factory $PileEndBearingBoredDataCopyWith(PileEndBearingBoredData value, $Res Function(PileEndBearingBoredData) _then) = _$PileEndBearingBoredDataCopyWithImpl;
@useResult
$Res call({
 double safeBearing, double fos, double fcu, double maxPileDiameter, double maxSteelRatio, double slsLoad, double ulsLoad, double diameter, double ratioOfBelloutDia, double length, double baseCapacity, double totalGroundResistance, double strCapacity, String rebar, double steelRatio, bool isSelected, String calsLog, String pileEndBearingBoredSchemeId
});




}
/// @nodoc
class _$PileEndBearingBoredDataCopyWithImpl<$Res>
    implements $PileEndBearingBoredDataCopyWith<$Res> {
  _$PileEndBearingBoredDataCopyWithImpl(this._self, this._then);

  final PileEndBearingBoredData _self;
  final $Res Function(PileEndBearingBoredData) _then;

/// Create a copy of PileEndBearingBoredData
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? safeBearing = null,Object? fos = null,Object? fcu = null,Object? maxPileDiameter = null,Object? maxSteelRatio = null,Object? slsLoad = null,Object? ulsLoad = null,Object? diameter = null,Object? ratioOfBelloutDia = null,Object? length = null,Object? baseCapacity = null,Object? totalGroundResistance = null,Object? strCapacity = null,Object? rebar = null,Object? steelRatio = null,Object? isSelected = null,Object? calsLog = null,Object? pileEndBearingBoredSchemeId = null,}) {
  return _then(_self.copyWith(
safeBearing: null == safeBearing ? _self.safeBearing : safeBearing // ignore: cast_nullable_to_non_nullable
as double,fos: null == fos ? _self.fos : fos // ignore: cast_nullable_to_non_nullable
as double,fcu: null == fcu ? _self.fcu : fcu // ignore: cast_nullable_to_non_nullable
as double,maxPileDiameter: null == maxPileDiameter ? _self.maxPileDiameter : maxPileDiameter // ignore: cast_nullable_to_non_nullable
as double,maxSteelRatio: null == maxSteelRatio ? _self.maxSteelRatio : maxSteelRatio // ignore: cast_nullable_to_non_nullable
as double,slsLoad: null == slsLoad ? _self.slsLoad : slsLoad // ignore: cast_nullable_to_non_nullable
as double,ulsLoad: null == ulsLoad ? _self.ulsLoad : ulsLoad // ignore: cast_nullable_to_non_nullable
as double,diameter: null == diameter ? _self.diameter : diameter // ignore: cast_nullable_to_non_nullable
as double,ratioOfBelloutDia: null == ratioOfBelloutDia ? _self.ratioOfBelloutDia : ratioOfBelloutDia // ignore: cast_nullable_to_non_nullable
as double,length: null == length ? _self.length : length // ignore: cast_nullable_to_non_nullable
as double,baseCapacity: null == baseCapacity ? _self.baseCapacity : baseCapacity // ignore: cast_nullable_to_non_nullable
as double,totalGroundResistance: null == totalGroundResistance ? _self.totalGroundResistance : totalGroundResistance // ignore: cast_nullable_to_non_nullable
as double,strCapacity: null == strCapacity ? _self.strCapacity : strCapacity // ignore: cast_nullable_to_non_nullable
as double,rebar: null == rebar ? _self.rebar : rebar // ignore: cast_nullable_to_non_nullable
as String,steelRatio: null == steelRatio ? _self.steelRatio : steelRatio // ignore: cast_nullable_to_non_nullable
as double,isSelected: null == isSelected ? _self.isSelected : isSelected // ignore: cast_nullable_to_non_nullable
as bool,calsLog: null == calsLog ? _self.calsLog : calsLog // ignore: cast_nullable_to_non_nullable
as String,pileEndBearingBoredSchemeId: null == pileEndBearingBoredSchemeId ? _self.pileEndBearingBoredSchemeId : pileEndBearingBoredSchemeId // ignore: cast_nullable_to_non_nullable
as String,
  ));
}

}


/// Adds pattern-matching-related methods to [PileEndBearingBoredData].
extension PileEndBearingBoredDataPatterns on PileEndBearingBoredData {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _PileEndBearingBoredData value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _PileEndBearingBoredData() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _PileEndBearingBoredData value)  $default,){
final _that = this;
switch (_that) {
case _PileEndBearingBoredData():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _PileEndBearingBoredData value)?  $default,){
final _that = this;
switch (_that) {
case _PileEndBearingBoredData() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( double safeBearing,  double fos,  double fcu,  double maxPileDiameter,  double maxSteelRatio,  double slsLoad,  double ulsLoad,  double diameter,  double ratioOfBelloutDia,  double length,  double baseCapacity,  double totalGroundResistance,  double strCapacity,  String rebar,  double steelRatio,  bool isSelected,  String calsLog,  String pileEndBearingBoredSchemeId)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _PileEndBearingBoredData() when $default != null:
return $default(_that.safeBearing,_that.fos,_that.fcu,_that.maxPileDiameter,_that.maxSteelRatio,_that.slsLoad,_that.ulsLoad,_that.diameter,_that.ratioOfBelloutDia,_that.length,_that.baseCapacity,_that.totalGroundResistance,_that.strCapacity,_that.rebar,_that.steelRatio,_that.isSelected,_that.calsLog,_that.pileEndBearingBoredSchemeId);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( double safeBearing,  double fos,  double fcu,  double maxPileDiameter,  double maxSteelRatio,  double slsLoad,  double ulsLoad,  double diameter,  double ratioOfBelloutDia,  double length,  double baseCapacity,  double totalGroundResistance,  double strCapacity,  String rebar,  double steelRatio,  bool isSelected,  String calsLog,  String pileEndBearingBoredSchemeId)  $default,) {final _that = this;
switch (_that) {
case _PileEndBearingBoredData():
return $default(_that.safeBearing,_that.fos,_that.fcu,_that.maxPileDiameter,_that.maxSteelRatio,_that.slsLoad,_that.ulsLoad,_that.diameter,_that.ratioOfBelloutDia,_that.length,_that.baseCapacity,_that.totalGroundResistance,_that.strCapacity,_that.rebar,_that.steelRatio,_that.isSelected,_that.calsLog,_that.pileEndBearingBoredSchemeId);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( double safeBearing,  double fos,  double fcu,  double maxPileDiameter,  double maxSteelRatio,  double slsLoad,  double ulsLoad,  double diameter,  double ratioOfBelloutDia,  double length,  double baseCapacity,  double totalGroundResistance,  double strCapacity,  String rebar,  double steelRatio,  bool isSelected,  String calsLog,  String pileEndBearingBoredSchemeId)?  $default,) {final _that = this;
switch (_that) {
case _PileEndBearingBoredData() when $default != null:
return $default(_that.safeBearing,_that.fos,_that.fcu,_that.maxPileDiameter,_that.maxSteelRatio,_that.slsLoad,_that.ulsLoad,_that.diameter,_that.ratioOfBelloutDia,_that.length,_that.baseCapacity,_that.totalGroundResistance,_that.strCapacity,_that.rebar,_that.steelRatio,_that.isSelected,_that.calsLog,_that.pileEndBearingBoredSchemeId);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _PileEndBearingBoredData extends PileEndBearingBoredData {
   _PileEndBearingBoredData({this.safeBearing = 1000, this.fos = 3, this.fcu = 45, this.maxPileDiameter = 2000, this.maxSteelRatio = 0.04, this.slsLoad = 1000, this.ulsLoad = 2000, this.diameter = 0, this.ratioOfBelloutDia = 1.65, this.length = 0, this.baseCapacity = 0, this.totalGroundResistance = 0, this.strCapacity = 0, this.rebar = '', this.steelRatio = 0, this.isSelected = false, this.calsLog = '', this.pileEndBearingBoredSchemeId = ''}): super._();
  factory _PileEndBearingBoredData.fromJson(Map<String, dynamic> json) => _$PileEndBearingBoredDataFromJson(json);

@override@JsonKey() final  double safeBearing;
@override@JsonKey() final  double fos;
@override@JsonKey() final  double fcu;
@override@JsonKey() final  double maxPileDiameter;
@override@JsonKey() final  double maxSteelRatio;
@override@JsonKey() final  double slsLoad;
@override@JsonKey() final  double ulsLoad;
@override@JsonKey() final  double diameter;
@override@JsonKey() final  double ratioOfBelloutDia;
@override@JsonKey() final  double length;
@override@JsonKey() final  double baseCapacity;
@override@JsonKey() final  double totalGroundResistance;
@override@JsonKey() final  double strCapacity;
@override@JsonKey() final  String rebar;
//will be overriden  as soon as new instance created
@override@JsonKey() final  double steelRatio;
@override@JsonKey() final  bool isSelected;
@override@JsonKey() final  String calsLog;
//will be overriden  as soon as new instance created
@override@JsonKey() final  String pileEndBearingBoredSchemeId;

/// Create a copy of PileEndBearingBoredData
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$PileEndBearingBoredDataCopyWith<_PileEndBearingBoredData> get copyWith => __$PileEndBearingBoredDataCopyWithImpl<_PileEndBearingBoredData>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$PileEndBearingBoredDataToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _PileEndBearingBoredData&&(identical(other.safeBearing, safeBearing) || other.safeBearing == safeBearing)&&(identical(other.fos, fos) || other.fos == fos)&&(identical(other.fcu, fcu) || other.fcu == fcu)&&(identical(other.maxPileDiameter, maxPileDiameter) || other.maxPileDiameter == maxPileDiameter)&&(identical(other.maxSteelRatio, maxSteelRatio) || other.maxSteelRatio == maxSteelRatio)&&(identical(other.slsLoad, slsLoad) || other.slsLoad == slsLoad)&&(identical(other.ulsLoad, ulsLoad) || other.ulsLoad == ulsLoad)&&(identical(other.diameter, diameter) || other.diameter == diameter)&&(identical(other.ratioOfBelloutDia, ratioOfBelloutDia) || other.ratioOfBelloutDia == ratioOfBelloutDia)&&(identical(other.length, length) || other.length == length)&&(identical(other.baseCapacity, baseCapacity) || other.baseCapacity == baseCapacity)&&(identical(other.totalGroundResistance, totalGroundResistance) || other.totalGroundResistance == totalGroundResistance)&&(identical(other.strCapacity, strCapacity) || other.strCapacity == strCapacity)&&(identical(other.rebar, rebar) || other.rebar == rebar)&&(identical(other.steelRatio, steelRatio) || other.steelRatio == steelRatio)&&(identical(other.isSelected, isSelected) || other.isSelected == isSelected)&&(identical(other.calsLog, calsLog) || other.calsLog == calsLog)&&(identical(other.pileEndBearingBoredSchemeId, pileEndBearingBoredSchemeId) || other.pileEndBearingBoredSchemeId == pileEndBearingBoredSchemeId));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,safeBearing,fos,fcu,maxPileDiameter,maxSteelRatio,slsLoad,ulsLoad,diameter,ratioOfBelloutDia,length,baseCapacity,totalGroundResistance,strCapacity,rebar,steelRatio,isSelected,calsLog,pileEndBearingBoredSchemeId);

@override
String toString() {
  return 'PileEndBearingBoredData(safeBearing: $safeBearing, fos: $fos, fcu: $fcu, maxPileDiameter: $maxPileDiameter, maxSteelRatio: $maxSteelRatio, slsLoad: $slsLoad, ulsLoad: $ulsLoad, diameter: $diameter, ratioOfBelloutDia: $ratioOfBelloutDia, length: $length, baseCapacity: $baseCapacity, totalGroundResistance: $totalGroundResistance, strCapacity: $strCapacity, rebar: $rebar, steelRatio: $steelRatio, isSelected: $isSelected, calsLog: $calsLog, pileEndBearingBoredSchemeId: $pileEndBearingBoredSchemeId)';
}


}

/// @nodoc
abstract mixin class _$PileEndBearingBoredDataCopyWith<$Res> implements $PileEndBearingBoredDataCopyWith<$Res> {
  factory _$PileEndBearingBoredDataCopyWith(_PileEndBearingBoredData value, $Res Function(_PileEndBearingBoredData) _then) = __$PileEndBearingBoredDataCopyWithImpl;
@override @useResult
$Res call({
 double safeBearing, double fos, double fcu, double maxPileDiameter, double maxSteelRatio, double slsLoad, double ulsLoad, double diameter, double ratioOfBelloutDia, double length, double baseCapacity, double totalGroundResistance, double strCapacity, String rebar, double steelRatio, bool isSelected, String calsLog, String pileEndBearingBoredSchemeId
});




}
/// @nodoc
class __$PileEndBearingBoredDataCopyWithImpl<$Res>
    implements _$PileEndBearingBoredDataCopyWith<$Res> {
  __$PileEndBearingBoredDataCopyWithImpl(this._self, this._then);

  final _PileEndBearingBoredData _self;
  final $Res Function(_PileEndBearingBoredData) _then;

/// Create a copy of PileEndBearingBoredData
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? safeBearing = null,Object? fos = null,Object? fcu = null,Object? maxPileDiameter = null,Object? maxSteelRatio = null,Object? slsLoad = null,Object? ulsLoad = null,Object? diameter = null,Object? ratioOfBelloutDia = null,Object? length = null,Object? baseCapacity = null,Object? totalGroundResistance = null,Object? strCapacity = null,Object? rebar = null,Object? steelRatio = null,Object? isSelected = null,Object? calsLog = null,Object? pileEndBearingBoredSchemeId = null,}) {
  return _then(_PileEndBearingBoredData(
safeBearing: null == safeBearing ? _self.safeBearing : safeBearing // ignore: cast_nullable_to_non_nullable
as double,fos: null == fos ? _self.fos : fos // ignore: cast_nullable_to_non_nullable
as double,fcu: null == fcu ? _self.fcu : fcu // ignore: cast_nullable_to_non_nullable
as double,maxPileDiameter: null == maxPileDiameter ? _self.maxPileDiameter : maxPileDiameter // ignore: cast_nullable_to_non_nullable
as double,maxSteelRatio: null == maxSteelRatio ? _self.maxSteelRatio : maxSteelRatio // ignore: cast_nullable_to_non_nullable
as double,slsLoad: null == slsLoad ? _self.slsLoad : slsLoad // ignore: cast_nullable_to_non_nullable
as double,ulsLoad: null == ulsLoad ? _self.ulsLoad : ulsLoad // ignore: cast_nullable_to_non_nullable
as double,diameter: null == diameter ? _self.diameter : diameter // ignore: cast_nullable_to_non_nullable
as double,ratioOfBelloutDia: null == ratioOfBelloutDia ? _self.ratioOfBelloutDia : ratioOfBelloutDia // ignore: cast_nullable_to_non_nullable
as double,length: null == length ? _self.length : length // ignore: cast_nullable_to_non_nullable
as double,baseCapacity: null == baseCapacity ? _self.baseCapacity : baseCapacity // ignore: cast_nullable_to_non_nullable
as double,totalGroundResistance: null == totalGroundResistance ? _self.totalGroundResistance : totalGroundResistance // ignore: cast_nullable_to_non_nullable
as double,strCapacity: null == strCapacity ? _self.strCapacity : strCapacity // ignore: cast_nullable_to_non_nullable
as double,rebar: null == rebar ? _self.rebar : rebar // ignore: cast_nullable_to_non_nullable
as String,steelRatio: null == steelRatio ? _self.steelRatio : steelRatio // ignore: cast_nullable_to_non_nullable
as double,isSelected: null == isSelected ? _self.isSelected : isSelected // ignore: cast_nullable_to_non_nullable
as bool,calsLog: null == calsLog ? _self.calsLog : calsLog // ignore: cast_nullable_to_non_nullable
as String,pileEndBearingBoredSchemeId: null == pileEndBearingBoredSchemeId ? _self.pileEndBearingBoredSchemeId : pileEndBearingBoredSchemeId // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

// dart format on
