// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'steel_cantilever_truss_scheme_data.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$SteelCantileverTrussSchemeData {

 String get usage; double get slabThickness; double get span; double get loadWidth; double get strZone; double get fsy; double get unbracedLength; String get steelSection; double get chordAxialCapacity; double get leverArm; double get momentCapacity; String get liveLoadDeflection; String get id;//will be overriden as soon as new instance created
 String get calsLog; String get beamForce;
/// Create a copy of SteelCantileverTrussSchemeData
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$SteelCantileverTrussSchemeDataCopyWith<SteelCantileverTrussSchemeData> get copyWith => _$SteelCantileverTrussSchemeDataCopyWithImpl<SteelCantileverTrussSchemeData>(this as SteelCantileverTrussSchemeData, _$identity);

  /// Serializes this SteelCantileverTrussSchemeData to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is SteelCantileverTrussSchemeData&&(identical(other.usage, usage) || other.usage == usage)&&(identical(other.slabThickness, slabThickness) || other.slabThickness == slabThickness)&&(identical(other.span, span) || other.span == span)&&(identical(other.loadWidth, loadWidth) || other.loadWidth == loadWidth)&&(identical(other.strZone, strZone) || other.strZone == strZone)&&(identical(other.fsy, fsy) || other.fsy == fsy)&&(identical(other.unbracedLength, unbracedLength) || other.unbracedLength == unbracedLength)&&(identical(other.steelSection, steelSection) || other.steelSection == steelSection)&&(identical(other.chordAxialCapacity, chordAxialCapacity) || other.chordAxialCapacity == chordAxialCapacity)&&(identical(other.leverArm, leverArm) || other.leverArm == leverArm)&&(identical(other.momentCapacity, momentCapacity) || other.momentCapacity == momentCapacity)&&(identical(other.liveLoadDeflection, liveLoadDeflection) || other.liveLoadDeflection == liveLoadDeflection)&&(identical(other.id, id) || other.id == id)&&(identical(other.calsLog, calsLog) || other.calsLog == calsLog)&&(identical(other.beamForce, beamForce) || other.beamForce == beamForce));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,usage,slabThickness,span,loadWidth,strZone,fsy,unbracedLength,steelSection,chordAxialCapacity,leverArm,momentCapacity,liveLoadDeflection,id,calsLog,beamForce);

@override
String toString() {
  return 'SteelCantileverTrussSchemeData(usage: $usage, slabThickness: $slabThickness, span: $span, loadWidth: $loadWidth, strZone: $strZone, fsy: $fsy, unbracedLength: $unbracedLength, steelSection: $steelSection, chordAxialCapacity: $chordAxialCapacity, leverArm: $leverArm, momentCapacity: $momentCapacity, liveLoadDeflection: $liveLoadDeflection, id: $id, calsLog: $calsLog, beamForce: $beamForce)';
}


}

/// @nodoc
abstract mixin class $SteelCantileverTrussSchemeDataCopyWith<$Res>  {
  factory $SteelCantileverTrussSchemeDataCopyWith(SteelCantileverTrussSchemeData value, $Res Function(SteelCantileverTrussSchemeData) _then) = _$SteelCantileverTrussSchemeDataCopyWithImpl;
@useResult
$Res call({
 String usage, double slabThickness, double span, double loadWidth, double strZone, double fsy, double unbracedLength, String steelSection, double chordAxialCapacity, double leverArm, double momentCapacity, String liveLoadDeflection, String id, String calsLog, String beamForce
});




}
/// @nodoc
class _$SteelCantileverTrussSchemeDataCopyWithImpl<$Res>
    implements $SteelCantileverTrussSchemeDataCopyWith<$Res> {
  _$SteelCantileverTrussSchemeDataCopyWithImpl(this._self, this._then);

  final SteelCantileverTrussSchemeData _self;
  final $Res Function(SteelCantileverTrussSchemeData) _then;

/// Create a copy of SteelCantileverTrussSchemeData
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? usage = null,Object? slabThickness = null,Object? span = null,Object? loadWidth = null,Object? strZone = null,Object? fsy = null,Object? unbracedLength = null,Object? steelSection = null,Object? chordAxialCapacity = null,Object? leverArm = null,Object? momentCapacity = null,Object? liveLoadDeflection = null,Object? id = null,Object? calsLog = null,Object? beamForce = null,}) {
  return _then(_self.copyWith(
usage: null == usage ? _self.usage : usage // ignore: cast_nullable_to_non_nullable
as String,slabThickness: null == slabThickness ? _self.slabThickness : slabThickness // ignore: cast_nullable_to_non_nullable
as double,span: null == span ? _self.span : span // ignore: cast_nullable_to_non_nullable
as double,loadWidth: null == loadWidth ? _self.loadWidth : loadWidth // ignore: cast_nullable_to_non_nullable
as double,strZone: null == strZone ? _self.strZone : strZone // ignore: cast_nullable_to_non_nullable
as double,fsy: null == fsy ? _self.fsy : fsy // ignore: cast_nullable_to_non_nullable
as double,unbracedLength: null == unbracedLength ? _self.unbracedLength : unbracedLength // ignore: cast_nullable_to_non_nullable
as double,steelSection: null == steelSection ? _self.steelSection : steelSection // ignore: cast_nullable_to_non_nullable
as String,chordAxialCapacity: null == chordAxialCapacity ? _self.chordAxialCapacity : chordAxialCapacity // ignore: cast_nullable_to_non_nullable
as double,leverArm: null == leverArm ? _self.leverArm : leverArm // ignore: cast_nullable_to_non_nullable
as double,momentCapacity: null == momentCapacity ? _self.momentCapacity : momentCapacity // ignore: cast_nullable_to_non_nullable
as double,liveLoadDeflection: null == liveLoadDeflection ? _self.liveLoadDeflection : liveLoadDeflection // ignore: cast_nullable_to_non_nullable
as String,id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,calsLog: null == calsLog ? _self.calsLog : calsLog // ignore: cast_nullable_to_non_nullable
as String,beamForce: null == beamForce ? _self.beamForce : beamForce // ignore: cast_nullable_to_non_nullable
as String,
  ));
}

}


/// Adds pattern-matching-related methods to [SteelCantileverTrussSchemeData].
extension SteelCantileverTrussSchemeDataPatterns on SteelCantileverTrussSchemeData {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _SteelCantileverTrussSchemeData value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _SteelCantileverTrussSchemeData() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _SteelCantileverTrussSchemeData value)  $default,){
final _that = this;
switch (_that) {
case _SteelCantileverTrussSchemeData():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _SteelCantileverTrussSchemeData value)?  $default,){
final _that = this;
switch (_that) {
case _SteelCantileverTrussSchemeData() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String usage,  double slabThickness,  double span,  double loadWidth,  double strZone,  double fsy,  double unbracedLength,  String steelSection,  double chordAxialCapacity,  double leverArm,  double momentCapacity,  String liveLoadDeflection,  String id,  String calsLog,  String beamForce)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _SteelCantileverTrussSchemeData() when $default != null:
return $default(_that.usage,_that.slabThickness,_that.span,_that.loadWidth,_that.strZone,_that.fsy,_that.unbracedLength,_that.steelSection,_that.chordAxialCapacity,_that.leverArm,_that.momentCapacity,_that.liveLoadDeflection,_that.id,_that.calsLog,_that.beamForce);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String usage,  double slabThickness,  double span,  double loadWidth,  double strZone,  double fsy,  double unbracedLength,  String steelSection,  double chordAxialCapacity,  double leverArm,  double momentCapacity,  String liveLoadDeflection,  String id,  String calsLog,  String beamForce)  $default,) {final _that = this;
switch (_that) {
case _SteelCantileverTrussSchemeData():
return $default(_that.usage,_that.slabThickness,_that.span,_that.loadWidth,_that.strZone,_that.fsy,_that.unbracedLength,_that.steelSection,_that.chordAxialCapacity,_that.leverArm,_that.momentCapacity,_that.liveLoadDeflection,_that.id,_that.calsLog,_that.beamForce);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String usage,  double slabThickness,  double span,  double loadWidth,  double strZone,  double fsy,  double unbracedLength,  String steelSection,  double chordAxialCapacity,  double leverArm,  double momentCapacity,  String liveLoadDeflection,  String id,  String calsLog,  String beamForce)?  $default,) {final _that = this;
switch (_that) {
case _SteelCantileverTrussSchemeData() when $default != null:
return $default(_that.usage,_that.slabThickness,_that.span,_that.loadWidth,_that.strZone,_that.fsy,_that.unbracedLength,_that.steelSection,_that.chordAxialCapacity,_that.leverArm,_that.momentCapacity,_that.liveLoadDeflection,_that.id,_that.calsLog,_that.beamForce);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _SteelCantileverTrussSchemeData extends SteelCantileverTrussSchemeData {
   _SteelCantileverTrussSchemeData({this.usage = '', this.slabThickness = 150.0, this.span = 1.0, this.loadWidth = 1.0, this.strZone = 500.0, this.fsy = 355.0, this.unbracedLength = 5.0, this.steelSection = '', this.chordAxialCapacity = 0.0, this.leverArm = 0.0, this.momentCapacity = 0.0, this.liveLoadDeflection = '', this.id = '1', this.calsLog = '', this.beamForce = ''}): super._();
  factory _SteelCantileverTrussSchemeData.fromJson(Map<String, dynamic> json) => _$SteelCantileverTrussSchemeDataFromJson(json);

@override@JsonKey() final  String usage;
@override@JsonKey() final  double slabThickness;
@override@JsonKey() final  double span;
@override@JsonKey() final  double loadWidth;
@override@JsonKey() final  double strZone;
@override@JsonKey() final  double fsy;
@override@JsonKey() final  double unbracedLength;
@override@JsonKey() final  String steelSection;
@override@JsonKey() final  double chordAxialCapacity;
@override@JsonKey() final  double leverArm;
@override@JsonKey() final  double momentCapacity;
@override@JsonKey() final  String liveLoadDeflection;
@override@JsonKey() final  String id;
//will be overriden as soon as new instance created
@override@JsonKey() final  String calsLog;
@override@JsonKey() final  String beamForce;

/// Create a copy of SteelCantileverTrussSchemeData
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$SteelCantileverTrussSchemeDataCopyWith<_SteelCantileverTrussSchemeData> get copyWith => __$SteelCantileverTrussSchemeDataCopyWithImpl<_SteelCantileverTrussSchemeData>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$SteelCantileverTrussSchemeDataToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _SteelCantileverTrussSchemeData&&(identical(other.usage, usage) || other.usage == usage)&&(identical(other.slabThickness, slabThickness) || other.slabThickness == slabThickness)&&(identical(other.span, span) || other.span == span)&&(identical(other.loadWidth, loadWidth) || other.loadWidth == loadWidth)&&(identical(other.strZone, strZone) || other.strZone == strZone)&&(identical(other.fsy, fsy) || other.fsy == fsy)&&(identical(other.unbracedLength, unbracedLength) || other.unbracedLength == unbracedLength)&&(identical(other.steelSection, steelSection) || other.steelSection == steelSection)&&(identical(other.chordAxialCapacity, chordAxialCapacity) || other.chordAxialCapacity == chordAxialCapacity)&&(identical(other.leverArm, leverArm) || other.leverArm == leverArm)&&(identical(other.momentCapacity, momentCapacity) || other.momentCapacity == momentCapacity)&&(identical(other.liveLoadDeflection, liveLoadDeflection) || other.liveLoadDeflection == liveLoadDeflection)&&(identical(other.id, id) || other.id == id)&&(identical(other.calsLog, calsLog) || other.calsLog == calsLog)&&(identical(other.beamForce, beamForce) || other.beamForce == beamForce));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,usage,slabThickness,span,loadWidth,strZone,fsy,unbracedLength,steelSection,chordAxialCapacity,leverArm,momentCapacity,liveLoadDeflection,id,calsLog,beamForce);

@override
String toString() {
  return 'SteelCantileverTrussSchemeData(usage: $usage, slabThickness: $slabThickness, span: $span, loadWidth: $loadWidth, strZone: $strZone, fsy: $fsy, unbracedLength: $unbracedLength, steelSection: $steelSection, chordAxialCapacity: $chordAxialCapacity, leverArm: $leverArm, momentCapacity: $momentCapacity, liveLoadDeflection: $liveLoadDeflection, id: $id, calsLog: $calsLog, beamForce: $beamForce)';
}


}

/// @nodoc
abstract mixin class _$SteelCantileverTrussSchemeDataCopyWith<$Res> implements $SteelCantileverTrussSchemeDataCopyWith<$Res> {
  factory _$SteelCantileverTrussSchemeDataCopyWith(_SteelCantileverTrussSchemeData value, $Res Function(_SteelCantileverTrussSchemeData) _then) = __$SteelCantileverTrussSchemeDataCopyWithImpl;
@override @useResult
$Res call({
 String usage, double slabThickness, double span, double loadWidth, double strZone, double fsy, double unbracedLength, String steelSection, double chordAxialCapacity, double leverArm, double momentCapacity, String liveLoadDeflection, String id, String calsLog, String beamForce
});




}
/// @nodoc
class __$SteelCantileverTrussSchemeDataCopyWithImpl<$Res>
    implements _$SteelCantileverTrussSchemeDataCopyWith<$Res> {
  __$SteelCantileverTrussSchemeDataCopyWithImpl(this._self, this._then);

  final _SteelCantileverTrussSchemeData _self;
  final $Res Function(_SteelCantileverTrussSchemeData) _then;

/// Create a copy of SteelCantileverTrussSchemeData
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? usage = null,Object? slabThickness = null,Object? span = null,Object? loadWidth = null,Object? strZone = null,Object? fsy = null,Object? unbracedLength = null,Object? steelSection = null,Object? chordAxialCapacity = null,Object? leverArm = null,Object? momentCapacity = null,Object? liveLoadDeflection = null,Object? id = null,Object? calsLog = null,Object? beamForce = null,}) {
  return _then(_SteelCantileverTrussSchemeData(
usage: null == usage ? _self.usage : usage // ignore: cast_nullable_to_non_nullable
as String,slabThickness: null == slabThickness ? _self.slabThickness : slabThickness // ignore: cast_nullable_to_non_nullable
as double,span: null == span ? _self.span : span // ignore: cast_nullable_to_non_nullable
as double,loadWidth: null == loadWidth ? _self.loadWidth : loadWidth // ignore: cast_nullable_to_non_nullable
as double,strZone: null == strZone ? _self.strZone : strZone // ignore: cast_nullable_to_non_nullable
as double,fsy: null == fsy ? _self.fsy : fsy // ignore: cast_nullable_to_non_nullable
as double,unbracedLength: null == unbracedLength ? _self.unbracedLength : unbracedLength // ignore: cast_nullable_to_non_nullable
as double,steelSection: null == steelSection ? _self.steelSection : steelSection // ignore: cast_nullable_to_non_nullable
as String,chordAxialCapacity: null == chordAxialCapacity ? _self.chordAxialCapacity : chordAxialCapacity // ignore: cast_nullable_to_non_nullable
as double,leverArm: null == leverArm ? _self.leverArm : leverArm // ignore: cast_nullable_to_non_nullable
as double,momentCapacity: null == momentCapacity ? _self.momentCapacity : momentCapacity // ignore: cast_nullable_to_non_nullable
as double,liveLoadDeflection: null == liveLoadDeflection ? _self.liveLoadDeflection : liveLoadDeflection // ignore: cast_nullable_to_non_nullable
as String,id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,calsLog: null == calsLog ? _self.calsLog : calsLog // ignore: cast_nullable_to_non_nullable
as String,beamForce: null == beamForce ? _self.beamForce : beamForce // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

// dart format on
