import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:structify/domain_layer/beam_scheme_data.dart';
import 'package:structify/domain_layer/beam_scheme_input.dart';
import 'package:structify/domain_layer/column_scheme_data.dart';
import 'package:structify/domain_layer/column_scheme_input.dart';
import 'package:structify/domain_layer/column_scheme_input_global.dart';
import 'package:structify/domain_layer/global_data.dart';
import 'package:structify/domain_layer/slab_scheme_data.dart';
import 'package:structify/domain_layer/slab_scheme_input.dart';
import 'package:structify/domain_layer/steel_column_scheme_input.dart';
import 'package:structify/domain_layer/strzone_table.dart';
import 'package:structify/domain_layer/transfer_beam_scheme_data.dart';
import 'package:structify/domain_layer/transfer_beam_scheme_input.dart';

import '../domain_layer/basement_wall_scheme_data.dart';
import '../domain_layer/basement_wall_scheme_input.dart';
import '../domain_layer/cantilever_scheme_data.dart';
import '../domain_layer/cantilever_scheme_input.dart';
import '../domain_layer/cantilever_scheme_input_global.dart';
import '../domain_layer/data_struct/carbon_struct.dart';
import '../domain_layer/data_struct/recommend_load.dart';
import '../domain_layer/footing_scheme_data.dart';
import '../domain_layer/footing_scheme_input.dart';
import '../domain_layer/footing_scheme_input_global.dart';
import '../domain_layer/loading_table.dart';
import '../domain_layer/pile_driven_data.dart';
import '../domain_layer/pile_driven_input.dart';
import '../domain_layer/pile_driven_input_global.dart';
import '../domain_layer/pile_end_bearing_bored_data.dart';
import '../domain_layer/pile_end_bearing_bored_input.dart';
import '../domain_layer/pile_end_bearing_bored_input_global.dart';
import '../domain_layer/pile_frictional_bored_data.dart';
import '../domain_layer/pile_frictional_bored_input.dart';
import '../domain_layer/pile_frictional_bored_input_global.dart';
import '../domain_layer/pile_socketed_data.dart';
import '../domain_layer/programme_item.dart';
import '../domain_layer/steel_beam_scheme_data.dart';
import '../domain_layer/steel_beam_scheme_input.dart';
import '../domain_layer/steel_cantilever_truss_scheme_data.dart';
import '../domain_layer/steel_cantilever_truss_scheme_input.dart';
import '../domain_layer/steel_cantilever_truss_scheme_input_global.dart';
import '../domain_layer/steel_column_scheme_data.dart';
import '../domain_layer/steel_column_scheme_input_global.dart';
import '../domain_layer/steel_transfer_truss_scheme_data.dart';
import '../domain_layer/steel_transfer_truss_scheme_input.dart';
import '../domain_layer/steel_transfer_truss_scheme_input_global.dart';
import '../domain_layer/transfer_beam_scheme_input_global.dart';
import '../domain_layer/wind_load.dart';
import '../domain_layer/wind_load_global.dart';
import '../presentation_layer/screen/homescreen.dart';

part 'app_watcher.g.dart';

@riverpod
class AppWatcher extends _$AppWatcher {
  late final List<LoadingTable> loadingTableList;

  @override
  FutureOr<void> build() async {
    //* watch all providers that will be saved to database
    // print('Build: ------App Watcher (${DateTime.now()})------');

    final GlobalData globalData = await ref.watch(
      globalDataControllerProvider.future,
    );

    final List<LoadingTable> loadingTables = await ref.watch(
      loadingTablesControllerProvider.future,
    );
    if (loadingTables.isEmpty) {
      await ref.read(loadingTablesControllerProvider.notifier).addEmptytable();
    }

    final BeamSchemeInput beamSchemeInput = await ref.watch(
      beamSchemeInputControllerProvider.future,
    );

    final List<BeamSchemeData> beamSchemeData = await ref.watch(
      beamSchemeDataControllerProvider.future,
    );

    final SlabSchemeInput slabSchemeInput = await ref.watch(
      slabSchemeInputControllerProvider.future,
    );

    final List<SlabSchemeData> slabSchemeData = await ref.watch(
      slabSchemeDataControllerProvider.future,
    );

    List<ColumnSchemeInput> columnSchemeInput = await ref.watch(
      columnSchemeInputControllerProvider.future,
    );
    if (columnSchemeInput.isEmpty) {
      await ref
          .read(columnSchemeInputControllerProvider.notifier)
          .addEmptytable();
    }

    final ColumnSchemeInputGlobal columnSchemeInputGlobal = await ref.watch(
      columnSchemeInputGlobalControllerProvider.future,
    );

    final List<ColumnSchemeData> columnSchemeData = await ref.watch(
      columnSchemeDataControllerProvider.future,
    );

    final TransferBeamSchemeInputGlobal transferBeamSchemeInputGlobal =
        await ref.watch(transferBeamSchemeInputGlobalControllerProvider.future);

    final List<TransferBeamSchemeInput> transferBeamSchemeInput = await ref
        .watch(transferBeamSchemeInputControllerProvider.future);
    if (transferBeamSchemeInput.isEmpty) {
      await ref
          .read(transferBeamSchemeInputControllerProvider.notifier)
          .addEmptytable();
    }
    final TransferBeamSchemeData transferBeamSchemeData = await ref.watch(
      transferBeamSchemeDataControllerProvider.future,
    );
    if (transferBeamSchemeData.beamForce == '') {
      await ref
          .read(transferBeamSchemeDataControllerProvider.notifier)
          .transferBeamScheming();
    }

    final CantileverSchemeInputGlobal cantileverSchemeInputGlobal = await ref
        .watch(cantileverSchemeInputGlobalControllerProvider.future);

    final List<CantileverSchemeInput> cantileverSchemeInput = await ref.watch(
      cantileverSchemeInputControllerProvider.future,
    );
    if (cantileverSchemeInput.isEmpty) {
      await ref
          .read(cantileverSchemeInputControllerProvider.notifier)
          .addEmptytable();
    }

    final CantileverSchemeData cantileverSchemeData = await ref.watch(
      cantileverSchemeDataControllerProvider.future,
    );

    final SteelBeamSchemeInput steelBeamSchemeInput = await ref.watch(
      steelBeamSchemeInputControllerProvider.future,
    );

    final List<SteelBeamSchemeData> steelBeamSchemeData = await ref.watch(
      steelBeamSchemeDataControllerProvider.future,
    );

    final List<SteelColumnSchemeInput> steelColumnSchemeInput = await ref.watch(
      steelColumnSchemeInputControllerProvider.future,
    );
    if (steelColumnSchemeInput.isEmpty) {
      await ref
          .read(steelColumnSchemeInputControllerProvider.notifier)
          .addEmptytable();
    }

    final SteelColumnSchemeInputGlobal steelColumnSchemeInputGlobal = await ref
        .watch(steelColumnSchemeInputGlobalControllerProvider.future);

    final List<SteelColumnSchemeData> steelColumnSchemeData = await ref.watch(
      steelColumnSchemeDataControllerProvider.future,
    );

    final List<SteelTransferTrussSchemeInput> steelTransferTrussSchemeInput =
        await ref.watch(steelTransferTrussSchemeInputControllerProvider.future);
    if (steelTransferTrussSchemeInput.isEmpty) {
      await ref
          .read(steelTransferTrussSchemeInputControllerProvider.notifier)
          .addEmptytable();
    }
    final SteelTransferTrussSchemeInputGlobal
    steelTransferTrussSchemeInputGlobal = await ref.watch(
      steelTransferTrussSchemeInputGlobalControllerProvider.future,
    );

    final SteelTransferTrussSchemeData steelTransferTrussSchemeData = await ref
        .watch(steelTransferTrussSchemeDataControllerProvider.future);
    if (steelTransferTrussSchemeData.beamForce == '') {
      await ref
          .read(steelTransferTrussSchemeDataControllerProvider.notifier)
          .transferTrussScheming();
    }

    final List<SteelCantileverTrussSchemeInput>
    steelCantileverTrussSchemeInput = await ref.watch(
      steelCantileverTrussSchemeInputControllerProvider.future,
    );
    if (steelCantileverTrussSchemeInput.isEmpty) {
      await ref
          .read(steelCantileverTrussSchemeInputControllerProvider.notifier)
          .addEmptytable();
    }
    final SteelCantileverTrussSchemeInputGlobal
    steelCantileverTrussSchemeInputGlobal = await ref.watch(
      steelCantileverTrussSchemeInputGlobalControllerProvider.future,
    );

    final SteelCantileverTrussSchemeData steelCantileverTrussSchemeData =
        await ref.watch(
          steelCantileverTrussSchemeDataControllerProvider.future,
        );

    final PileEndBearingBoredInput pileEndBearingBoredInput = await ref.watch(
      pileEndBearingBoredInputControllerProvider.future,
    );

    final PileEndBearingBoredInputGlobal pileEndBearingBoredInputGlobal =
        await ref.watch(
          pileEndBearingBoredInputGlobalControllerProvider.future,
        );

    final List<PileEndBearingBoredData> pileEndBearingBoredData = await ref
        .watch(pileEndBearingBoredDataControllerProvider.future);

    final PileFrictionalBoredInput pileFrictionalBoredInput = await ref.watch(
      pileFrictionalBoredInputControllerProvider.future,
    );

    final PileFrictionalBoredInputGlobal pileFrictionalBoredInputGlobal =
        await ref.watch(
          pileFrictionalBoredInputGlobalControllerProvider.future,
        );

    final List<PileFrictionalBoredData> pileFrictionalBoredData = await ref
        .watch(pileFrictionalBoredDataControllerProvider.future);
    final PileSocketedData pileSocketedData = await ref.watch(
      pileSocketedDataControllerProvider.future,
    );

    final PileDrivenInput pileDrivenInput = await ref.watch(
      pileDrivenInputControllerProvider.future,
    );

    final PileDrivenInputGlobal pileDrivenInputGlobal = await ref.watch(
      pileDrivenInputGlobalControllerProvider.future,
    );

    final List<PileDrivenData> pileDrivenData = await ref.watch(
      pileDrivenDataControllerProvider.future,
    );

    final FootingSchemeInput footingSchemeInput = await ref.watch(
      footingSchemeInputControllerProvider.future,
    );

    final FootingSchemeInputGlobal footingSchemeInputGlobal = await ref.watch(
      footingSchemeInputGlobalControllerProvider.future,
    );

    final List<FootingSchemeData> footingSchemeData = await ref.watch(
      footingSchemeDataControllerProvider.future,
    );

    final List<StrZoneTable> strZoneTables = await ref.watch(
      strZoneTablesControllerProvider.future,
    );

    final List<RecommendedLoad> recommendedLoad = await ref.watch(
      recommendedLoadControllerProvider.future,
    );

    final BasementWallSchemeInput basementWallSchemeInput = await ref.watch(
      basementWallSchemeInputControllerProvider.future,
    );

    final BasementWallSchemeData basementWallSchemeData = await ref.watch(
      basementWallSchemeDataControllerProvider.future,
    );

    final List<ProgrammeItem> programmeItems = await ref.watch(
      programmeItemsControllerProvider.future,
    );
    if (programmeItems.isEmpty) {
      await ref.read(programmeItemsControllerProvider.notifier).addEmptytable();
    }

    final List<WindLoad> windLoadInputs = await ref.watch(
      windLoadInputControllerProvider.future,
    );
    if (windLoadInputs.isEmpty) {
      await ref.read(windLoadInputControllerProvider.notifier).addEmptytable();
    }
    final WindLoadGlobal windLoadInputGlobal = await ref.watch(
      windLoadInputGlobalControllerProvider.future,
    );
    final List<Carbon> carbonInput = await ref.watch(
      carbonInputControllerProvider.future,
    );
    if (carbonInput.isEmpty) {
      await ref.read(carbonInputControllerProvider.notifier).addEmptytable();
    }

    //* go saving now
    await ref
        .read(appDatabaseControllerProvider.notifier)
        .saveAllLoadingTables(loadingTables);

    await ref
        .read(appDatabaseControllerProvider.notifier)
        .saveGlobalData(globalData);

    await ref
        .read(appDatabaseControllerProvider.notifier)
        .saveBeamSchemeInput(beamSchemeInput);

    await ref
        .read(appDatabaseControllerProvider.notifier)
        .saveBeamSchemeData(beamSchemeData);

    await ref
        .read(appDatabaseControllerProvider.notifier)
        .saveSlabSchemeInput(slabSchemeInput);

    await ref
        .read(appDatabaseControllerProvider.notifier)
        .saveSlabSchemeData(slabSchemeData);

    await ref
        .read(appDatabaseControllerProvider.notifier)
        .saveColumnSchemeInput(columnSchemeInput);

    await ref
        .read(appDatabaseControllerProvider.notifier)
        .saveColumnSchemeInputGlobal(columnSchemeInputGlobal);

    await ref
        .read(appDatabaseControllerProvider.notifier)
        .saveColumnSchemeData(columnSchemeData);

    await ref
        .read(appDatabaseControllerProvider.notifier)
        .saveTransferBeamSchemeInputGlobal(transferBeamSchemeInputGlobal);

    await ref
        .read(appDatabaseControllerProvider.notifier)
        .saveTransferBeamSchemeInput(transferBeamSchemeInput);

    await ref
        .read(appDatabaseControllerProvider.notifier)
        .saveTransferBeamSchemeData(transferBeamSchemeData);

    await ref
        .read(appDatabaseControllerProvider.notifier)
        .saveSteelBeamSchemeInput(steelBeamSchemeInput);

    await ref
        .read(appDatabaseControllerProvider.notifier)
        .saveCantileverSchemeInputGlobal(cantileverSchemeInputGlobal);

    await ref
        .read(appDatabaseControllerProvider.notifier)
        .saveCantileverSchemeInput(cantileverSchemeInput);
    await ref
        .read(appDatabaseControllerProvider.notifier)
        .saveCantileverSchemeData(cantileverSchemeData);

    await ref
        .read(appDatabaseControllerProvider.notifier)
        .saveSteelBeamSchemeData(steelBeamSchemeData);

    await ref
        .read(appDatabaseControllerProvider.notifier)
        .saveSteelColumnSchemeInput(steelColumnSchemeInput);

    await ref
        .read(appDatabaseControllerProvider.notifier)
        .saveSteelColumnSchemeInputGlobal(steelColumnSchemeInputGlobal);

    await ref
        .read(appDatabaseControllerProvider.notifier)
        .saveSteelColumnSchemeData(steelColumnSchemeData);

    await ref
        .read(appDatabaseControllerProvider.notifier)
        .saveSteelTransferTrussSchemeInput(steelTransferTrussSchemeInput);

    await ref
        .read(appDatabaseControllerProvider.notifier)
        .saveSteelTransferTrussSchemeInputGlobal(
          steelTransferTrussSchemeInputGlobal,
        );

    await ref
        .read(appDatabaseControllerProvider.notifier)
        .saveSteelTransferTrussSchemeData(steelTransferTrussSchemeData);

    await ref
        .read(appDatabaseControllerProvider.notifier)
        .saveSteelCantileverTrussSchemeInput(steelCantileverTrussSchemeInput);

    await ref
        .read(appDatabaseControllerProvider.notifier)
        .saveSteelCantileverTrussSchemeInputGlobal(
          steelCantileverTrussSchemeInputGlobal,
        );

    await ref
        .read(appDatabaseControllerProvider.notifier)
        .saveSteelCantileverTrussSchemeData(steelCantileverTrussSchemeData);

    await ref
        .read(appDatabaseControllerProvider.notifier)
        .savePileEndBearingBoredInput(pileEndBearingBoredInput);

    await ref
        .read(appDatabaseControllerProvider.notifier)
        .savePileEndBearingBoredInputGlobal(pileEndBearingBoredInputGlobal);

    await ref
        .read(appDatabaseControllerProvider.notifier)
        .savePileEndBearingBoredData(pileEndBearingBoredData);

    await ref
        .read(appDatabaseControllerProvider.notifier)
        .savePileFrictionalBoredInput(pileFrictionalBoredInput);

    await ref
        .read(appDatabaseControllerProvider.notifier)
        .savePileFrictionalBoredInputGlobal(pileFrictionalBoredInputGlobal);

    await ref
        .read(appDatabaseControllerProvider.notifier)
        .savePileFrictionalBoredData(pileFrictionalBoredData);

    await ref
        .read(appDatabaseControllerProvider.notifier)
        .savePileSocketedData(pileSocketedData);

    await ref
        .read(appDatabaseControllerProvider.notifier)
        .savePileDrivenInput(pileDrivenInput);

    await ref
        .read(appDatabaseControllerProvider.notifier)
        .savePileDrivenInputGlobal(pileDrivenInputGlobal);

    await ref
        .read(appDatabaseControllerProvider.notifier)
        .savePileDrivenData(pileDrivenData);

    await ref
        .read(appDatabaseControllerProvider.notifier)
        .saveFootingSchemeInput(footingSchemeInput);

    await ref
        .read(appDatabaseControllerProvider.notifier)
        .saveFootingSchemeInputGlobal(footingSchemeInputGlobal);

    await ref
        .read(appDatabaseControllerProvider.notifier)
        .saveFootingSchemeData(footingSchemeData);

    await ref
        .read(appDatabaseControllerProvider.notifier)
        .saveStrZoneTables(strZoneTables);

    await ref
        .read(appDatabaseControllerProvider.notifier)
        .saveRecommendedLoad(recommendedLoad);

    await ref
        .read(appDatabaseControllerProvider.notifier)
        .saveBasementWallSchemeInput(basementWallSchemeInput);

    await ref
        .read(appDatabaseControllerProvider.notifier)
        .saveBasementWallSchemeData(basementWallSchemeData);

    await ref
        .read(appDatabaseControllerProvider.notifier)
        .saveProgrammeItems(programmeItems);

    await ref
        .read(appDatabaseControllerProvider.notifier)
        .saveWindLoad(windLoadInputs);

    await ref
        .read(appDatabaseControllerProvider.notifier)
        .saveWindLoadGlobal(windLoadInputGlobal);

    await ref
        .read(appDatabaseControllerProvider.notifier)
        .saveCarbonInput(carbonInput);
  }
}
