import 'dart:math' as math;
import 'dart:typed_data';
import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:structify/domain_layer/transfer_beam_scheme_input.dart';
import 'package:structify/domain_layer/transfer_beam_scheme_input_global.dart';
import 'package:structify/misc/custom_func.dart';
import 'package:structify/presentation_layer/screen/homescreen.dart';
import 'package:vector_math/vector_math.dart' as vec;

import '../../../domain_layer/global_data.dart';
import '../../../domain_layer/loading_data.dart';
import '../../../domain_layer/preferences.dart';
import '../../../domain_layer/transfer_beam_scheme_data.dart';

class DrawTransferBeamLoadingInfo extends ConsumerWidget {
  DrawTransferBeamLoadingInfo({
    required this.sketchWidth,
    required this.sketchHeight,
    this.fontSize,
    super.key,
  });

  final double sketchWidth;
  final double sketchHeight;
  double? fontSize;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final data1 = ref.watch(transferBeamSchemeInputControllerProvider);
    final data2 = ref.watch(transferBeamSchemeInputGlobalControllerProvider);
    final data3 = ref.watch(globalDataControllerProvider);
    final data4 = ref.watch(transferBeamSchemeDataControllerProvider);
    final ColorScheme colorScheme = Theme.of(context).colorScheme;
    final TextTheme textTheme = Theme.of(context).textTheme;

    return data1.when(
      data: (input) {
        return data2.when(
          data: (inputGlobal) {
            return data3.when(
              data: (globalData) {
                return data4.when(
                  data: (transferBeamSchemeData) {
                    final constraints = BoxConstraints(
                      maxWidth: sketchWidth,
                      maxHeight: sketchHeight,
                      minHeight: 100,
                      minWidth: 100,
                    );

                    return FutureBuilder(
                      future: ref
                          .read(loadingTablesControllerProvider.notifier)
                          .getLoadingData(inputGlobal.usage),
                      initialData: LoadingData(),
                      builder: (context, asyncSnapshot) {
                        if (asyncSnapshot.hasError) {
                          return Icon(Icons.error_outline);
                        } else {
                          return Center(
                            child: Container(
                              decoration: BoxDecoration(
                                color: colorScheme.surfaceContainer.withAlpha(
                                  100,
                                ),
                                borderRadius: BorderRadius.circular(10.0),
                                border: Border.all(
                                  color: Colors.black.withAlpha(100),
                                ),
                              ),
                              width:
                                  constraints.maxWidth == double.infinity
                                      ? 100
                                      : constraints.maxWidth,
                              height:
                                  constraints.maxHeight == double.infinity
                                      ? 100
                                      : constraints.maxHeight,
                              child: CustomPaint(
                                painter: DrawTransferBeamLoadingInfoPainter(
                                  inputGlobal: inputGlobal,
                                  globalData: globalData,
                                  boxConstraints: constraints,
                                  fontSize: fontSize,
                                  context: context,
                                  loadingData: asyncSnapshot.data!,
                                  transferBeamSchemeData:
                                      transferBeamSchemeData,
                                ),
                              ),
                            ),
                          );
                        }
                      },
                    );
                  },
                  error: (error, stackTrace) {
                    return Text(error.toString());
                  },
                  loading: () {
                    return const CircularProgressIndicator();
                  },
                );
              },
              error: (error, stackTrace) {
                return Text(error.toString());
              },
              loading: () {
                return const CircularProgressIndicator();
              },
            );
          },
          error: (error, stackTrace) {
            return Text(error.toString());
          },
          loading: () {
            return const CircularProgressIndicator();
          },
        );
      },
      error: (error, stackTrace) => Text(error.toString()),
      loading: () => CircularProgressIndicator(),
    );
  }
}

class DrawTransferBeamLoadingInfoPainter extends CustomPainter {
  DrawTransferBeamLoadingInfoPainter({
    required this.inputGlobal,
    required this.globalData,
    required this.boxConstraints,
    this.fontSize,
    required this.context,
    required this.loadingData,
    required this.transferBeamSchemeData,
  });

  final TransferBeamSchemeInputGlobal inputGlobal;
  final GlobalData globalData;
  final BoxConstraints boxConstraints;
  double? fontSize;
  final BuildContext context;
  final LoadingData loadingData;
  final TransferBeamSchemeData transferBeamSchemeData;

  @override
  void paint(Canvas canvas, Size size) {
    final ColorScheme colorScheme = Theme.of(context).colorScheme;
    final TextTheme textTheme = Theme.of(context).textTheme;
    final double secBeamSpacing;
    double offsetCounter = 15;
    final double minDim = size.width;

    final double ratio = minDim / inputGlobal.span * 4 / 5;
    late Offset startP1, endP1, startP2, endP2, startP3, endP3;

    fontSize = fontSize ?? math.min(12, math.min(size.width, size.height) / 8);

    Paint beamPaint =
        Paint()
          ..color = colorScheme.onSurface
          ..strokeCap = StrokeCap.round
          ..style = PaintingStyle.stroke
          ..strokeWidth = 2.0;

    Paint supportPaint =
        Paint()
          ..color = colorScheme.onSurface
          ..strokeCap = StrokeCap.round
          ..style = PaintingStyle.fill
          ..strokeWidth = 2;

    Paint loadPaint =
        Paint()
          ..color = Colors.red.withAlpha(150)
          ..strokeCap = StrokeCap.round
          ..style = PaintingStyle.fill
          ..strokeWidth = 1.0;

    Paint UDLPaint =
        Paint()
          ..color = Colors.black.withAlpha(100)
          ..strokeCap = StrokeCap.round
          ..style = PaintingStyle.stroke
          ..strokeWidth = 1.0;

    Path path = Path();

    // ******************
    // todo: draw beam
    // ******************
    startP1 = Offset(0.5 * (size.width), 0.85 * (size.height));

    startP2 = startP1 + Offset(-0.5 * (inputGlobal.span) * ratio, 0);
    endP2 = startP2 + Offset(1 * (inputGlobal.span) * ratio, 0);

    canvas.drawLine(startP2, endP2, beamPaint);

    // ******************
    // todo: draw support
    // ******************
    final supportLeft = Float32List.fromList(
      [
        startP2,
        startP2 + Offset(10, 10),
        startP2 + Offset(-10, 10),
        startP2,
      ].expand((i) => [i.dx, i.dy]).toList(),
    );
    final supportRight = Float32List.fromList(
      [
        endP2,
        endP2 + Offset(10, 10),
        endP2 + Offset(-10, 10),
        endP2,
      ].expand((i) => [i.dx, i.dy]).toList(),
    );

    canvas.drawRawPoints(PointMode.polygon, supportLeft, supportPaint);
    canvas.drawRawPoints(PointMode.polygon, supportRight, supportPaint);
    _drawDimLine(
      canvas,
      fontSize!,
      startP2,
      endP2,
      offsetCounter,
      fontSize! / 2,
      "Span",
      context,
      fontColor: Colors.black.withAlpha(100),
    );
    offsetCounter += 5;
    // ******************
    // todo: draw loading
    // ******************
    final input = [TransferBeamSchemeInput(pointLoad: 10, distA: 3)];
    final maxLoad = maxFromNum(input.map((e) => e.pointLoad).toList());
    for (var p in input) {
      // startP3 = endP3 + Offset(0, -p.pointLoad/maxLoad * size.height*4/5/2 );
      // canvas.drawLine(startP3, endP3, loadPaint);
      endP3 = startP2 + Offset(p.distA * ratio, 0);
      final load = Float32List.fromList(
        [
          endP3,
          endP3 +
              Offset(
                0,
                math.min(
                  -p.pointLoad / maxLoad * size.height * 4 / 5 / 2,
                  -size.height / 20,
                ),
              ),
          endP3,
          endP3 +
              Offset(
                -5,
                math.min(
                  -p.pointLoad / maxLoad * size.height * 4 / 5 / 2 / 2,
                  -size.height / 40,
                ),
              ),
          endP3,
          endP3 +
              Offset(
                5,
                math.min(
                  -p.pointLoad / maxLoad * size.height * 4 / 5 / 2 / 2,
                  -size.height / 40,
                ),
              ),
        ].expand((i) => [i.dx, i.dy]).toList(),
      );

      // draw loading itself (arrow)
      if (p.pointLoad != 0) {
        canvas.drawRawPoints(PointMode.polygon, load, loadPaint);

        // draw distance A
        _drawDimLine(
          canvas,
          fontSize!,
          startP2,
          endP3,
          offsetCounter,
          fontSize! / 4,
          "Distance a",
          context, 
          fontColor: Colors.red.withAlpha(200),
        );
        //
        // draw loading magnitude
        _drawText(
          canvas,
          fontSize!,
          endP3 + Offset(0, -p.pointLoad / maxLoad * size.height * 4 / 5 / 2),
          endP3,
          -10,
          fontSize! / 4,
          "Point Load",
          context,
          fontColor: Colors.red .withAlpha(200),
        );
        offsetCounter += 5;
      }
    }
    //* Calculate UDL
    double beamSelfWeight =
        (inputGlobal.loadWidth *
                transferBeamSchemeData.slabThickness *
                math.pow(10, -3) + //* slab
            transferBeamSchemeData.strZone *
                transferBeamSchemeData.mainWidth *
                math.pow(10, -6)) * //* beam
        globalData.rcUnitWeight;

    double ulsUDL =
        loadingData.ulsLoad * inputGlobal.loadWidth +
        globalData.sdlFactor * beamSelfWeight;

    //* Draw UDL
    startP3 = Offset(startP2.dx, startP2.dy - offsetCounter - 10);
    endP3 = Offset(endP2.dx, endP2.dy - offsetCounter - 10);
    int segmentCounts = 10;
    double segmentLength = (endP2.dx - startP2.dx) / segmentCounts;
    Offset centerP;
    for (int i = 0; i < segmentCounts; i++) {
      startP3 = Offset(startP2.dx + segmentLength * i, startP3.dy);
      endP3 = startP3 + Offset(segmentLength, 0);
      centerP = (startP3 + endP3) / 2;
      Rect rec1 = Rect.fromCenter(
        center: centerP,
        width: segmentLength,
        height: segmentLength,
      );
      canvas.drawArc(rec1, math.pi, math.pi, true, UDLPaint);
    }

    _drawText(
      canvas,
      fontSize!,
      startP2 + Offset(0, -offsetCounter - 10 - 15),
      endP2 + Offset(0, -offsetCounter - 10 - 15),
      0,
      0,
      "UDL",
      context,
      fontColor: Colors.black.withAlpha(100),
    );
  }

  void _drawDimLine(
    Canvas canvas,
    double fontSize,
    Offset start,
    Offset end,
    double offsetDistance,
    double dimExtension,
    String label,
    BuildContext context,
    {Color? fontColor,}
  ) {
    double tempOffset = 10; // forthe dim line end
    late Offset tempEnd;
    late Offset tempStart;
    late Offset textCenter;

    late double angle;

    late List<String> unit;

    fontColor ??= Theme.of(context).colorScheme.primary;

    late final vec.Vector2 u;
    late final vec.Vector2 uUnit;
    late final vec.Vector2 vUnit;
    late final vec.Vector2 vUnit45;
    late final vec.Matrix2 m;

    final ColorScheme colorScheme = Theme.of(context).colorScheme;
    final TextTheme textTheme = Theme.of(context).textTheme;

    // get the unit
    switch (globalData.unit) {
      case 'metrics':
        unit = PreferredUnit.metrics;
        break;
      case 'imperial':
        unit = PreferredUnit.imperial;
        break;
      default:
        unit = PreferredUnit.metrics;
    }

    // double dimExtension = offsetDistance.abs() / 2;

    final Paint paint =
        Paint()
          ..color = fontColor
          ..strokeWidth = 1.0
          ..style = PaintingStyle.stroke
          ..strokeJoin = StrokeJoin.round;

    // line vector
    u = vec.Vector2(end.dx - start.dx, end.dy - start.dy);
    uUnit = u / u.length;

    // orthogonal vector
    vUnit = vec.Vector2(uUnit.y, uUnit.x); //rotate aniti-clockwise

    // Vector of parallel line with Offset
    Offset newStart = Offset(
      start.dx - vUnit.x * offsetDistance - uUnit.x * dimExtension,
      start.dy - vUnit.y * offsetDistance - uUnit.y * dimExtension,
    );
    Offset newEnd = Offset(
      end.dx - vUnit.x * offsetDistance + uUnit.x * dimExtension,
      end.dy - vUnit.y * offsetDistance + uUnit.y * dimExtension,
    );
    canvas.drawLine(newStart, newEnd, paint);

    // draw start crossing
    tempStart = Offset(
      (start.dx - vUnit.x * offsetDistance) - vUnit.x * dimExtension,
      (start.dy - vUnit.y * offsetDistance) - vUnit.y * dimExtension,
    );
    tempEnd = Offset(
      (start.dx - vUnit.x * offsetDistance) + vUnit.x * dimExtension,
      (start.dy - vUnit.y * offsetDistance) + vUnit.y * dimExtension,
    );
    canvas.drawLine(tempStart, tempEnd, paint);

    m = vec.Matrix2.rotation(-45 * math.pi / 180);
    vUnit45 = m * vUnit;
    tempStart = Offset(
      (start.dx - vUnit.x * offsetDistance) - vUnit45.x * dimExtension,
      (start.dy - vUnit.y * offsetDistance) - vUnit45.y * dimExtension,
    );
    tempEnd = Offset(
      (start.dx - vUnit.x * offsetDistance) + vUnit45.x * dimExtension,
      (start.dy - vUnit.y * offsetDistance) + vUnit45.y * dimExtension,
    );
    canvas.drawLine(tempStart, tempEnd, paint);

    // draw end crossing
    tempStart = Offset(
      (end.dx - vUnit.x * offsetDistance) - vUnit.x * dimExtension,
      (end.dy - vUnit.y * offsetDistance) - vUnit.y * dimExtension,
    );
    tempEnd = Offset(
      (end.dx - vUnit.x * offsetDistance) + vUnit.x * dimExtension,
      (end.dy - vUnit.y * offsetDistance) + vUnit.y * dimExtension,
    );
    canvas.drawLine(tempStart, tempEnd, paint);

    tempStart = Offset(
      (end.dx - vUnit.x * offsetDistance) - vUnit45.x * dimExtension,
      (end.dy - vUnit.y * offsetDistance) - vUnit45.y * dimExtension,
    );
    tempEnd = Offset(
      (end.dx - vUnit.x * offsetDistance) + vUnit45.x * dimExtension,
      (end.dy - vUnit.y * offsetDistance) + vUnit45.y * dimExtension,
    );
    canvas.drawLine(tempStart, tempEnd, paint);

    // indicate span length and beam size

    TextSpan textSpan = TextSpan(
      text: label,
      style: TextStyle(
        fontSize: fontSize,
        color: fontColor,
        fontWeight: FontWeight.bold,
      ),
    );

    TextPainter textPainter = TextPainter(
      text: textSpan,
      textAlign: TextAlign.center,
      textDirection: TextDirection.ltr,
    )..layout();

    textCenter = Offset(
      (start.dx + end.dx) / 2 -
          vUnit.x * (offsetDistance * 1.0 + 12) -
          uUnit.x * textPainter.width / 2,
      (start.dy + end.dy) / 2 -
          vUnit.y * (offsetDistance * 1.0 + 12) -
          uUnit.y * textPainter.width / 2,
    );
    angle = math.atan2(end.dy - start.dy, end.dx - start.dx) * 180 / math.pi;

    canvas.save();
    canvas.translate(textCenter.dx, textCenter.dy);
    canvas.rotate(angle * math.pi / 180);
    textPainter.paint(canvas, Offset.zero);
    canvas.restore();
  }

  void _drawText(
    Canvas canvas,
    double fontSize,
    Offset start,
    Offset end,
    double offsetDistance,
    double dimExtension,
    String label,
    BuildContext context, {
    Color? fontColor,
  }) {
    double tempOffset = 10; // forthe dim line end
    late Offset tempEnd;
    late Offset tempStart;
    late Offset textCenter;

    late double angle;

    late List<String> unit;

    fontColor ??= Theme.of(context).colorScheme.primary;
    // fontColor ??= Colors.red;

    late final vec.Vector2 u;
    late final vec.Vector2 uUnit;
    late final vec.Vector2 vUnit;
    late final vec.Vector2 vUnit45;
    late final vec.Matrix2 m;

    final ColorScheme colorScheme = Theme.of(context).colorScheme;
    final TextTheme textTheme = Theme.of(context).textTheme;

    // get the unit
    switch (globalData.unit) {
      case 'metrics':
        unit = PreferredUnit.metrics;
        break;
      case 'imperial':
        unit = PreferredUnit.imperial;
        break;
      default:
        unit = PreferredUnit.metrics;
    }

    // double dimExtension = offsetDistance.abs() / 2;

    final Paint paint =
        Paint()
          ..color = fontColor
          ..strokeWidth = 1.0
          ..style = PaintingStyle.stroke
          ..strokeJoin = StrokeJoin.round;

    // line vector
    u = vec.Vector2(end.dx - start.dx, end.dy - start.dy);
    uUnit = u / u.length;

    // orthogonal vector
    vUnit = vec.Vector2(uUnit.y, uUnit.x); //rotate aniti-clockwise

    // Vector of parallel line with Offset
    Offset newStart = Offset(
      start.dx - vUnit.x * offsetDistance - uUnit.x * dimExtension,
      start.dy - vUnit.y * offsetDistance - uUnit.y * dimExtension,
    );
    Offset newEnd = Offset(
      end.dx - vUnit.x * offsetDistance + uUnit.x * dimExtension,
      end.dy - vUnit.y * offsetDistance + uUnit.y * dimExtension,
    );

    // indicate span length and beam size

    TextSpan textSpan = TextSpan(
      text: label,
      style: TextStyle(
        fontSize: fontSize,
        color: fontColor,
        fontWeight: FontWeight.bold,
      ),
    );

    TextPainter textPainter = TextPainter(
      text: textSpan,
      textAlign: TextAlign.center,
      textDirection: TextDirection.ltr,
    )..layout();

    textCenter = Offset(
      (start.dx + end.dx) / 2 -
          vUnit.x * (offsetDistance * 1.0 + 12) -
          uUnit.x * textPainter.width / 2,
      (start.dy + end.dy) / 2 -
          vUnit.y * (offsetDistance * 1.0 + 12) -
          uUnit.y * textPainter.width / 2,
    );
    angle = math.atan2(end.dy - start.dy, end.dx - start.dx) * 180 / math.pi;

    canvas.save();
    canvas.translate(textCenter.dx, textCenter.dy);
    canvas.rotate(angle * math.pi / 180);
    textPainter.paint(canvas, Offset.zero);
    canvas.restore();
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    // TODO: implement shouldRepaint
    return false;
  }
}
