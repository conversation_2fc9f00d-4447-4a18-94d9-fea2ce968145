import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:structify/domain_layer/slab_scheme_data.dart';
import 'package:structify/presentation_layer/small_elements/popup/custom_popup.dart';
import 'package:structify/presentation_layer/small_elements/sketch/draw_beam_scheme_info.dart';

//presentation layer
import 'button/function_button.dart';
import 'input/custom_stateful_double_input.dart';
import 'input/custom_stateful_int_input.dart';
import '../screen/homescreen.dart';
import 'input/custom_stateful_dropList.dart';

//domain layer
import '../../domain_layer/preferences.dart';
import 'popup/custom_tooltip.dart';
import 'button/selection_button.dart';

class BeamSchemeInputUi extends ConsumerStatefulWidget {
  const BeamSchemeInputUi({
    this.isExpanded,
    this.maxHeight,
    this.minHeight,
    super.key,
  });

  final bool? isExpanded;
  final double? maxHeight;
  final double? minHeight;

  @override
  ConsumerState<ConsumerStatefulWidget> createState() =>
      _BeamSchemeInputState();
}

class _BeamSchemeInputState extends ConsumerState<BeamSchemeInputUi>
    with WidgetsBindingObserver {
  late bool _isExpanded;
  late double _maxHeight;
  late double _minHeight;
  late ScrollController _controller;
  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _controller.dispose();
    super.dispose();
  }

  @override
  void initState() {
    WidgetsBinding.instance.addObserver(this);
    _isExpanded = widget.isExpanded ?? false;
    _maxHeight = widget.maxHeight ?? 210;
    _minHeight = widget.minHeight ?? 50;
    _controller = ScrollController();
    // _updateHeights();

    super.initState();
  }

  @override
  void didChangeDependencies() {
    _updateHeights();
    super.didChangeDependencies();
    // Update heights when dependencies change, including after initState
  }

  @override
  void didChangeMetrics() {
    // This method is called when the metrics change (like resizing the window)
    setState(() {
      _updateHeights();
    });
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    final beamSchemeInput = ref.watch(beamSchemeInputControllerProvider);
    final beamSchemeData = ref.watch(beamSchemeDataControllerProvider);
    final slabSchemeData = ref.watch(slabSchemeDataControllerProvider);
    final globalData = ref.watch(globalDataControllerProvider);
    final loadingTables = ref.watch(loadingTablesControllerProvider);

    // ref.watch(appWatcherProvider);
    return loadingTables.when(
      data: (tables) {
        return globalData.when(
          data: (data) {
            late final List<String> preferredUnit;
            switch (data.unit) {
              case 'metrics':
                preferredUnit = PreferredUnit.metrics;
                break;
              case 'imperial':
                preferredUnit = PreferredUnit.imperial;
                break;
              default:
                preferredUnit = PreferredUnit.metrics;
                break;
            }
            return beamSchemeInput.when(
              data: (input) {
                return slabSchemeData.when(
                  data: (slabData) {
                    //* get any slab is selected

                    final int containerOpacity = 175;
                    final int textOpacity = 225;
                    return Column(
                      children: [
                        Padding(
                          padding: const EdgeInsets.fromLTRB(10, 0, 10, 0),
                          child: Divider(),
                        ),
                        Padding(
                          padding: const EdgeInsets.fromLTRB(10, 0, 0, 0),
                          child: Align(
                            alignment: Alignment.centerLeft,
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Wrap(
                                  crossAxisAlignment: WrapCrossAlignment.center,
                                  children: [
                                    IconButton(
                                      icon: Icon(
                                        _isExpanded
                                            ? Icons.expand_less
                                            : Icons.expand_more,
                                      ),
                                      color: colorScheme.onSurface,
                                      onPressed: () {
                                        setState(() {
                                          _isExpanded = !_isExpanded;
                                        });
                                      },
                                    ),
                                    Text(
                                      'RC Beam Scheme Input',
                                      style: textTheme.titleLarge!.copyWith(
                                        color: colorScheme.onSurface,
                                      ),
                                    ),
                                    SizedBox(width: 10.0),
                                    CustomPopup(
                                      popupWidth: 550,
                                      popupHeight: 550,
                                      widgetList: [
                                        DrawBeamSchemeInfo(
                                          sketchWidth: 500,
                                          sketchHeight: 500,
                                        ),
                                      ],
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                        ),
                        Padding(
                          padding: const EdgeInsets.fromLTRB(10, 0, 10, 0),
                          child: Divider(),
                        ),
                        ClipRect(
                          child: ConstrainedBox(
                            constraints: BoxConstraints(
                              maxHeight: _isExpanded ? _maxHeight : 0,
                            ),
                            child: Scrollbar(
                              controller: _controller,
                              thumbVisibility: true,
                              trackVisibility: false,
                              child: SingleChildScrollView(
                                controller: _controller,
                                child: Column(
                                  mainAxisSize: MainAxisSize.min,
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Padding(
                                      padding: const EdgeInsets.fromLTRB(
                                        8.0,
                                        0.0,
                                        8.0,
                                        0.0,
                                      ),
                                      child: Row(
                                        children: [
                                          Expanded(
                                            flex: 1,
                                            child: Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                Container(
                                                  decoration: BoxDecoration(
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                          10.0,
                                                        ),
                                                    border: Border.all(
                                                      color: Colors.grey
                                                          .withAlpha(150),
                                                    ),
                                                    color: colorScheme
                                                        .secondaryContainer
                                                        .withAlpha(
                                                          containerOpacity,
                                                        ),
                                                  ),
                                                  child: Padding(
                                                    padding:
                                                        const EdgeInsets.all(
                                                          4.0,
                                                        ),
                                                    child: Text(
                                                      'Main Beam',
                                                      style: textTheme
                                                          .titleSmall!
                                                          .copyWith(
                                                            color: colorScheme
                                                                .onSecondaryContainer
                                                                .withAlpha(
                                                                  textOpacity,
                                                                ),
                                                          ),
                                                    ),
                                                  ),
                                                ),
                                                SizedBox(height: 10.0),
                                                Row(
                                                  mainAxisSize:
                                                      MainAxisSize.min,
                                                  mainAxisAlignment:
                                                      MainAxisAlignment.start,
                                                  children: [
                                                    Flexible(
                                                      child: CustomStatefulDoubleInput(
                                                        title:
                                                            'Str Zone [${preferredUnit[4]}]',
                                                        titleStyle: textTheme
                                                            .labelLarge!
                                                            .copyWith(
                                                              color: colorScheme
                                                                  .error
                                                                  .withAlpha(
                                                                    200,
                                                                  ),
                                                            ),
                                                        value:
                                                            input.mainStrZone,
                                                        onChanged: (value) {
                                                          ref
                                                              .read(
                                                                beamSchemeInputControllerProvider
                                                                    .notifier,
                                                              )
                                                              .updateTable(
                                                                mainStrZone:
                                                                    value,
                                                              );
                                                        },
                                                      ),
                                                    ),
                                                    SizedBox(width: 5.0),
                                                    Flexible(
                                                      child: CustomStatefulDoubleInput(
                                                        title:
                                                            'Preferred k-value',
                                                        titleStyle: textTheme
                                                            .labelLarge!
                                                            .copyWith(
                                                              color: colorScheme
                                                                  .error
                                                                  .withAlpha(
                                                                    200,
                                                                  ),
                                                            ),
                                                        tooltipText:
                                                            'Affect flexural strength. Design will be as close to this k-value\n'
                                                            'as possible while considering the code limit as well',
                                                        value: input.mainKValue,
                                                        onChanged: (value) {
                                                          ref
                                                              .read(
                                                                beamSchemeInputControllerProvider
                                                                    .notifier,
                                                              )
                                                              .updateTable(
                                                                mainKValue:
                                                                    value,
                                                              );
                                                        },
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ],
                                            ),
                                          ),
                                          SizedBox(width: 5.0),
                                          Flexible(
                                            child: Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                Container(
                                                  decoration: BoxDecoration(
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                          10.0,
                                                        ),
                                                    border: Border.all(
                                                      color: Colors.grey
                                                          .withAlpha(150),
                                                    ),
                                                    color: colorScheme
                                                        .secondaryContainer
                                                        .withAlpha(
                                                          containerOpacity,
                                                        ),
                                                  ),
                                                  child: Padding(
                                                    padding:
                                                        const EdgeInsets.all(
                                                          4.0,
                                                        ),
                                                    child: Text(
                                                      'Secondary Beam',
                                                      style: textTheme
                                                          .titleSmall!
                                                          .copyWith(
                                                            color: colorScheme
                                                                .onSecondaryContainer
                                                                .withAlpha(
                                                                  textOpacity,
                                                                ),
                                                          ),
                                                    ),
                                                  ),
                                                ),
                                                SizedBox(height: 10.0),
                                                Row(
                                                  mainAxisSize:
                                                      MainAxisSize.min,
                                                  mainAxisAlignment:
                                                      MainAxisAlignment.start,
                                                  children: [
                                                    Flexible(
                                                      child: CustomStatefulDoubleInput(
                                                        title:
                                                            'Str Zone [${preferredUnit[4]}]',
                                                        titleStyle: textTheme
                                                            .labelLarge!
                                                            .copyWith(
                                                              color: colorScheme
                                                                  .error
                                                                  .withAlpha(
                                                                    200,
                                                                  ),
                                                            ),
                                                        value: input.secStrZone,
                                                        onChanged: (value) {
                                                          ref
                                                              .read(
                                                                beamSchemeInputControllerProvider
                                                                    .notifier,
                                                              )
                                                              .updateTable(
                                                                secStrZone:
                                                                    value,
                                                              );
                                                        },
                                                      ),
                                                    ),
                                                    SizedBox(width: 5.0),
                                                    Flexible(
                                                      child: CustomStatefulDoubleInput(
                                                        title:
                                                            'Preferred k-value',
                                                        titleStyle: textTheme
                                                            .labelLarge!
                                                            .copyWith(
                                                              color: colorScheme
                                                                  .error
                                                                  .withAlpha(
                                                                    200,
                                                                  ),
                                                            ),
                                                        tooltipText:
                                                            'Affect Strength. Design will be as close to this k-value\n'
                                                            'as possible while considering the code limit as well',
                                                        value: input.secKValue,
                                                        onChanged: (value) {
                                                          ref
                                                              .read(
                                                                beamSchemeInputControllerProvider
                                                                    .notifier,
                                                              )
                                                              .updateTable(
                                                                secKValue:
                                                                    value,
                                                              );
                                                        },
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ],
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                    Padding(
                                      padding: const EdgeInsets.fromLTRB(
                                        8.0,
                                        0.0,
                                        8.0,
                                        0.0,
                                      ),
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          SizedBox(height: 5.0),
                                          Container(
                                            decoration: BoxDecoration(
                                              borderRadius:
                                                  BorderRadius.circular(10.0),
                                              border: Border.all(
                                                color: Colors.grey.withAlpha(
                                                  150,
                                                ),
                                              ),
                                              color: colorScheme
                                                  .secondaryContainer
                                                  .withAlpha(containerOpacity),
                                            ),
                                            child: Padding(
                                              padding: const EdgeInsets.all(
                                                4.0,
                                              ),
                                              child: Text(
                                                'Initial Parameters',
                                                style: textTheme.titleSmall!
                                                    .copyWith(
                                                      color: colorScheme
                                                          .onSecondaryContainer
                                                          .withAlpha(
                                                            textOpacity,
                                                          ),
                                                    ),
                                              ),
                                            ),
                                          ),
                                          SizedBox(height: 10.0),
                                          Row(
                                            mainAxisSize: MainAxisSize.min,
                                            mainAxisAlignment:
                                                MainAxisAlignment.start,
                                            children: [
                                              Flexible(
                                                child: CustomStatefulDoubleInput(
                                                  title:
                                                      'Short Span [${preferredUnit[3]}]',
                                                  value: input.shortSpan,
                                                  onChanged: (value) async {
                                                    await ref
                                                        .read(
                                                          beamSchemeInputControllerProvider
                                                              .notifier,
                                                        )
                                                        .updateTable(
                                                          shortSpan: value,
                                                        );
                                                  },
                                                ),
                                              ),
                                              SizedBox(width: 5.0),
                                              Flexible(
                                                child: CustomStatefulDoubleInput(
                                                  title:
                                                      'Long Span [${preferredUnit[3]}]',
                                                  value: input.longSpan,
                                                  onChanged: (value) async {
                                                    await ref
                                                        .read(
                                                          beamSchemeInputControllerProvider
                                                              .notifier,
                                                        )
                                                        .updateTable(
                                                          longSpan: value,
                                                        );
                                                  },
                                                ),
                                              ),
                                              SizedBox(width: 5.0),
                                              Flexible(
                                                child: CustomStatefulIntInput(
                                                  title: 'Bays',
                                                  value: input.bays,
                                                  onChanged: (value) async {
                                                    await ref
                                                        .read(
                                                          beamSchemeInputControllerProvider
                                                              .notifier,
                                                        )
                                                        .updateTable(
                                                          bays: value,
                                                        );
                                                  },
                                                ),
                                              ),
                                              Flexible(
                                                child: SizedBox(width: 5.0),
                                              ),
                                            ],
                                          ),
                                        ],
                                      ),
                                    ),
                                    Padding(
                                      padding: const EdgeInsets.fromLTRB(
                                        8.0,
                                        0.0,
                                        8.0,
                                        0.0,
                                      ),
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,

                                        children: [
                                          SizedBox(height: 5.0),
                                          Container(
                                            decoration: BoxDecoration(
                                              borderRadius:
                                                  BorderRadius.circular(10.0),
                                              border: Border.all(
                                                color: Colors.grey.withAlpha(
                                                  150,
                                                ),
                                              ),
                                              color: colorScheme
                                                  .secondaryContainer
                                                  .withAlpha(containerOpacity),
                                            ),
                                            child: Padding(
                                              padding: const EdgeInsets.all(
                                                4.0,
                                              ),
                                              child: Text(
                                                'Iteration Parameters',
                                                style: textTheme.titleSmall!
                                                    .copyWith(
                                                      color: colorScheme
                                                          .onSecondaryContainer
                                                          .withAlpha(
                                                            textOpacity,
                                                          ),
                                                    ),
                                              ),
                                            ),
                                          ),
                                          SizedBox(height: 10.0),
                                          Row(
                                            children: [
                                              Flexible(
                                                child: CustomStatefulDoubleInput(
                                                  title:
                                                      'Delta Short Span [${preferredUnit[3]}]',
                                                  value:
                                                      input.shortSpanIncreament,
                                                  tooltipText:
                                                      'Change in short span length\n'
                                                      'in each iteration step',
                                                  onChanged: (value) {
                                                    ref
                                                        .read(
                                                          beamSchemeInputControllerProvider
                                                              .notifier,
                                                        )
                                                        .updateTable(
                                                          shortSpanIncreament:
                                                              value,
                                                        );
                                                  },
                                                ),
                                              ),
                                              SizedBox(width: 5.0),
                                              Flexible(
                                                child: CustomStatefulDoubleInput(
                                                  title:
                                                      'Delta Long Span [${preferredUnit[3]}]',
                                                  value:
                                                      input.longSpanIncreament,
                                                  tooltipText:
                                                      'Change in long span length\n'
                                                      'in each iteration step',
                                                  onChanged: (value) {
                                                    ref
                                                        .read(
                                                          beamSchemeInputControllerProvider
                                                              .notifier,
                                                        )
                                                        .updateTable(
                                                          longSpanIncreament:
                                                              value,
                                                        );
                                                  },
                                                ),
                                              ),
                                              SizedBox(width: 5.0),
                                              Flexible(
                                                child: CustomStatefulIntInput(
                                                  title: 'Delta Bays',
                                                  tooltipText:
                                                      'Change in number of bays\n'
                                                      'in each iteration step',
                                                  value: input.baysIncreament,
                                                  onChanged: (value) {
                                                    ref
                                                        .read(
                                                          beamSchemeInputControllerProvider
                                                              .notifier,
                                                        )
                                                        .updateTable(
                                                          baysIncreament: value,
                                                        );
                                                  },
                                                ),
                                              ),
                                              SizedBox(width: 5.0),
                                              Flexible(
                                                child: CustomStatefulIntInput(
                                                  title: 'Steps',
                                                  value: input.iterationSteps,
                                                  onChanged: (value) {
                                                    ref
                                                        .read(
                                                          beamSchemeInputControllerProvider
                                                              .notifier,
                                                        )
                                                        .updateTable(
                                                          iterationSteps: value,
                                                        );
                                                  },
                                                ),
                                              ),
                                            ],
                                          ),
                                        ],
                                      ),
                                    ),
                                    Padding(
                                      padding: const EdgeInsets.fromLTRB(
                                        8.0,
                                        0.0,
                                        8.0,
                                        0.0,
                                      ),
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          SizedBox(height: 5.0),
                                          Container(
                                            decoration: BoxDecoration(
                                              borderRadius:
                                                  BorderRadius.circular(10.0),
                                              border: Border.all(
                                                color: Colors.grey.withAlpha(
                                                  150,
                                                ),
                                              ),
                                              color: colorScheme
                                                  .secondaryContainer
                                                  .withAlpha(containerOpacity),
                                            ),
                                            child: Padding(
                                              padding: const EdgeInsets.all(
                                                4.0,
                                              ),
                                              child: Text(
                                                'Limit',
                                                style: textTheme.titleSmall!
                                                    .copyWith(
                                                      color: colorScheme
                                                          .onSecondaryContainer
                                                          .withAlpha(
                                                            textOpacity,
                                                          ),
                                                    ),
                                              ),
                                            ),
                                          ),
                                          SizedBox(height: 10.0),
                                          Row(
                                            children: [
                                              Flexible(
                                                child: CustomStatefulDoubleInput(
                                                  title:
                                                      'Max Beam Width [${preferredUnit[4]}]',
                                                  titleStyle: textTheme
                                                      .labelLarge!
                                                      .copyWith(
                                                        color: colorScheme.error
                                                            .withAlpha(200),
                                                      ),
                                                  tooltipText:
                                                      'Affect flexural strength.\nRecommended same ascolumn width for typical bay.',
                                                  value: input.maxWidth,
                                                  onChanged: (value) {
                                                    ref
                                                        .read(
                                                          beamSchemeInputControllerProvider
                                                              .notifier,
                                                        )
                                                        .updateTable(
                                                          maxWidth: value,
                                                        );
                                                  },
                                                ),
                                              ),
                                              SizedBox(width: 5.0),
                                              Flexible(
                                                child: CustomStatefulIntInput(
                                                  title: 'Max Rebar Layer',

                                                  tooltipText:
                                                      'Affect flexural Strength.\nNot recommended >2 for typical bay',
                                                  titleStyle: textTheme
                                                      .labelLarge!
                                                      .copyWith(
                                                        color: colorScheme.error
                                                            .withAlpha(200),
                                                      ),
                                                  value: input.maxLayers,
                                                  onChanged: (value) {
                                                    ref
                                                        .read(
                                                          beamSchemeInputControllerProvider
                                                              .notifier,
                                                        )
                                                        .updateTable(
                                                          maxLayers: value,
                                                        );
                                                  },
                                                ),
                                              ),
                                              SizedBox(width: 5.0),
                                              Flexible(
                                                child: CustomStatefulIntInput(
                                                  title:
                                                      'Min Links Spacing [${preferredUnit[4]}]',
                                                  tooltipText:
                                                      'Affect shear capacity.\nNot recommended <100mm',
                                                  titleStyle: textTheme
                                                      .labelLarge!
                                                      .copyWith(
                                                        color: colorScheme.error
                                                            .withAlpha(200),
                                                      ),
                                                  value: input.minS,
                                                  onChanged: (value) {
                                                    ref
                                                        .read(
                                                          beamSchemeInputControllerProvider
                                                              .notifier,
                                                        )
                                                        .updateTable(
                                                          minS: value,
                                                        );
                                                  },
                                                ),
                                              ),
                                              SizedBox(width: 5.0),
                                              Flexible(
                                                child: CustomStatefulIntInput(
                                                  title:
                                                      'Max Links Spacing [${preferredUnit[4]}]',
                                                  tooltipText:
                                                      'Affect shear capacity.\nNot recommended >300mm',
                                                  titleStyle: textTheme
                                                      .labelLarge!
                                                      .copyWith(
                                                        color: colorScheme.error
                                                            .withAlpha(200),
                                                      ),
                                                  value: input.maxS,
                                                  onChanged: (value) {
                                                    ref
                                                        .read(
                                                          beamSchemeInputControllerProvider
                                                              .notifier,
                                                        )
                                                        .updateTable(
                                                          maxS: value,
                                                        );
                                                  },
                                                ),
                                              ),
                                            ],
                                          ),
                                        ],
                                      ),
                                    ),
                                    Padding(
                                      padding: const EdgeInsets.fromLTRB(
                                        8.0,
                                        0.0,
                                        8.0,
                                        0.0,
                                      ),
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,

                                        children: [
                                          SizedBox(height: 5.0),
                                          Container(
                                            decoration: BoxDecoration(
                                              borderRadius:
                                                  BorderRadius.circular(10.0),
                                              border: Border.all(
                                                color: Colors.grey.withAlpha(
                                                  150,
                                                ),
                                              ),
                                              color: colorScheme
                                                  .secondaryContainer
                                                  .withAlpha(containerOpacity),
                                            ),
                                            child: Padding(
                                              padding: const EdgeInsets.all(
                                                4.0,
                                              ),
                                              child: Text(
                                                'Other Info',
                                                style: textTheme.titleSmall!
                                                    .copyWith(
                                                      color: colorScheme
                                                          .onSecondaryContainer
                                                          .withAlpha(
                                                            textOpacity,
                                                          ),
                                                    ),
                                              ),
                                            ),
                                          ),
                                          SizedBox(height: 10.0),
                                          Row(
                                            mainAxisSize: MainAxisSize.min,
                                            mainAxisAlignment:
                                                MainAxisAlignment.start,
                                            children: [
                                              Flexible(
                                                child: ClipRect(
                                                  child: Container(
                                                    width: 100,
                                                    child: CustomStatefulDropDown(
                                                      items:
                                                          tables
                                                              .map(
                                                                (e) => e.usage,
                                                              )
                                                              .toList(),
                                                      selectedValue:
                                                          input.usage == ''
                                                              ? null
                                                              : input.usage,
                                                      onTap: (selectedValue) {
                                                        ref
                                                            .read(
                                                              beamSchemeInputControllerProvider
                                                                  .notifier,
                                                            )
                                                            .updateTable(
                                                              usage:
                                                                  selectedValue,
                                                            );
                                                      },
                                                    ),
                                                  ),
                                                ),
                                              ),
                                              SizedBox(width: 5.0),

                                              Flexible(
                                                child: CustomStatefulDoubleInput(
                                                  readOnly:
                                                      input.useSlabSelected,
                                                  title:
                                                      'Slab Thickness [${preferredUnit[4]}]',
                                                  value: input.slabThickness,
                                                  onChanged: (value) {
                                                    ref
                                                        .read(
                                                          beamSchemeInputControllerProvider
                                                              .notifier,
                                                        )
                                                        .updateTable(
                                                          slabThickness: value,
                                                        );
                                                  },
                                                ),
                                              ),
                                            ],
                                          ),
                                          SizedBox(height: 10.0),
                                          Row(
                                            children: [
                                              SizedBox(width: 5.0),
                                              SelectionButton(
                                                labelTextStyle:
                                                    input.useSlabSelected
                                                        ? textTheme.labelLarge!
                                                            .copyWith(
                                                              color:
                                                                  colorScheme
                                                                      .onTertiaryContainer,
                                                            )
                                                        : textTheme.labelLarge!
                                                            .copyWith(
                                                              color:
                                                                  colorScheme
                                                                      .onSurface,
                                                            ),
                                                labelText: 'Use Slab Selected',
                                                pressedColor:
                                                    colorScheme
                                                        .tertiaryContainer,
                                                bgColor:
                                                    input.useSlabSelected
                                                        ? colorScheme
                                                            .tertiaryContainer
                                                        : colorScheme
                                                            .surfaceContainer,

                                                onTap: (value) async {
                                                  final input = await ref.read(
                                                    beamSchemeInputControllerProvider
                                                        .future,
                                                  );

                                                  await ref
                                                      .read(
                                                        beamSchemeInputControllerProvider
                                                            .notifier,
                                                      )
                                                      .updateTable(
                                                        useSlabSelected:
                                                            !input
                                                                .useSlabSelected,
                                                      );
                                                  //* Tricky here: if the new toggled value (!input.useSlabSelected) is true,
                                                  //* we run below logic
                                                  if (!input.useSlabSelected) {
                                                    final selectedSlab =
                                                        slabData.firstWhere(
                                                          (scheme) =>
                                                              scheme.isSelected,
                                                          orElse:
                                                              () =>
                                                                  SlabSchemeData(
                                                                    strZone:
                                                                        0.0,
                                                                  ),
                                                        );
                                                    await ref
                                                        .read(
                                                          beamSchemeInputControllerProvider
                                                              .notifier,
                                                        )
                                                        .updateTable(
                                                          slabThickness:
                                                              selectedSlab
                                                                  .strZone,
                                                        );
                                                  }
                                                },
                                              ),
                                              SizedBox(width: 5.0),
                                              Flexible(
                                                child: FunctionButton(
                                                  labelIcon: Icon(
                                                    Icons.calculate_outlined,
                                                  ),
                                                  labelText: 'Run',
                                                  onTap: (isPressed) async {
                                                    await ref
                                                        .read(
                                                          beamSchemeDataControllerProvider
                                                              .notifier,
                                                        )
                                                        .bacthTypBayScheming();
                                                  },
                                                ),
                                              ),
                                              SizedBox(width: 5.0),
                                              Flexible(
                                                child: FunctionButton(
                                                  labelIcon: Icon(
                                                    Icons.delete_outlined,
                                                  ),
                                                  labelText: 'Clear Schemes',
                                                  onTap: (isPressed) async {
                                                    await ref
                                                        .read(
                                                          beamSchemeDataControllerProvider
                                                              .notifier,
                                                        )
                                                        .deleteAllTable();
                                                  },
                                                ),
                                              ),
                                            ],
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    );
                  },
                  loading: () => const CircularProgressIndicator(),
                  error: (error, stackTrace) => Text(error.toString()),
                );
              },
              error: (error, stackTrace) => Text(error.toString()),
              loading: () => const CircularProgressIndicator(),
            );
          },
          error: (error, stackTrace) => Text(error.toString()),
          loading: () => Center(child: CircularProgressIndicator()),
        );
      },
      error: (error, stackTrace) => Text(error.toString()),
      loading: () => Center(child: CircularProgressIndicator()),
    );
  }

  void _updateHeights() {
    final double screenHeight = MediaQuery.of(context).size.height;
    _maxHeight =
        (widget.maxHeight == null)
            ? screenHeight * 0.6
            : (widget.maxHeight! > screenHeight * 0.6)
            ? screenHeight * 0.6
            : widget.maxHeight!;

    _minHeight =
        (widget.minHeight == null)
            ? screenHeight * 0.1
            : (widget.minHeight! > screenHeight * 0.1)
            ? screenHeight * 0.1
            : widget.minHeight!;

    // Adjust _maxHeight if needed

    if (_maxHeight < _minHeight) {
      _maxHeight = _minHeight;
    }
  }
}
