// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'pile_driven_input.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$PileDrivenInput {

 double get sptNValue; double get fos; double get maxPileLength; double get slsLoad; double get ulsLoad; bool get useSelectColLoad; String get id;
/// Create a copy of PileDrivenInput
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$PileDrivenInputCopyWith<PileDrivenInput> get copyWith => _$PileDrivenInputCopyWithImpl<PileDrivenInput>(this as PileDrivenInput, _$identity);

  /// Serializes this PileDrivenInput to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is PileDrivenInput&&(identical(other.sptNValue, sptNValue) || other.sptNValue == sptNValue)&&(identical(other.fos, fos) || other.fos == fos)&&(identical(other.maxPileLength, maxPileLength) || other.maxPileLength == maxPileLength)&&(identical(other.slsLoad, slsLoad) || other.slsLoad == slsLoad)&&(identical(other.ulsLoad, ulsLoad) || other.ulsLoad == ulsLoad)&&(identical(other.useSelectColLoad, useSelectColLoad) || other.useSelectColLoad == useSelectColLoad)&&(identical(other.id, id) || other.id == id));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,sptNValue,fos,maxPileLength,slsLoad,ulsLoad,useSelectColLoad,id);

@override
String toString() {
  return 'PileDrivenInput(sptNValue: $sptNValue, fos: $fos, maxPileLength: $maxPileLength, slsLoad: $slsLoad, ulsLoad: $ulsLoad, useSelectColLoad: $useSelectColLoad, id: $id)';
}


}

/// @nodoc
abstract mixin class $PileDrivenInputCopyWith<$Res>  {
  factory $PileDrivenInputCopyWith(PileDrivenInput value, $Res Function(PileDrivenInput) _then) = _$PileDrivenInputCopyWithImpl;
@useResult
$Res call({
 double sptNValue, double fos, double maxPileLength, double slsLoad, double ulsLoad, bool useSelectColLoad, String id
});




}
/// @nodoc
class _$PileDrivenInputCopyWithImpl<$Res>
    implements $PileDrivenInputCopyWith<$Res> {
  _$PileDrivenInputCopyWithImpl(this._self, this._then);

  final PileDrivenInput _self;
  final $Res Function(PileDrivenInput) _then;

/// Create a copy of PileDrivenInput
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? sptNValue = null,Object? fos = null,Object? maxPileLength = null,Object? slsLoad = null,Object? ulsLoad = null,Object? useSelectColLoad = null,Object? id = null,}) {
  return _then(_self.copyWith(
sptNValue: null == sptNValue ? _self.sptNValue : sptNValue // ignore: cast_nullable_to_non_nullable
as double,fos: null == fos ? _self.fos : fos // ignore: cast_nullable_to_non_nullable
as double,maxPileLength: null == maxPileLength ? _self.maxPileLength : maxPileLength // ignore: cast_nullable_to_non_nullable
as double,slsLoad: null == slsLoad ? _self.slsLoad : slsLoad // ignore: cast_nullable_to_non_nullable
as double,ulsLoad: null == ulsLoad ? _self.ulsLoad : ulsLoad // ignore: cast_nullable_to_non_nullable
as double,useSelectColLoad: null == useSelectColLoad ? _self.useSelectColLoad : useSelectColLoad // ignore: cast_nullable_to_non_nullable
as bool,id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,
  ));
}

}


/// Adds pattern-matching-related methods to [PileDrivenInput].
extension PileDrivenInputPatterns on PileDrivenInput {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _PileDrivenInput value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _PileDrivenInput() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _PileDrivenInput value)  $default,){
final _that = this;
switch (_that) {
case _PileDrivenInput():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _PileDrivenInput value)?  $default,){
final _that = this;
switch (_that) {
case _PileDrivenInput() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( double sptNValue,  double fos,  double maxPileLength,  double slsLoad,  double ulsLoad,  bool useSelectColLoad,  String id)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _PileDrivenInput() when $default != null:
return $default(_that.sptNValue,_that.fos,_that.maxPileLength,_that.slsLoad,_that.ulsLoad,_that.useSelectColLoad,_that.id);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( double sptNValue,  double fos,  double maxPileLength,  double slsLoad,  double ulsLoad,  bool useSelectColLoad,  String id)  $default,) {final _that = this;
switch (_that) {
case _PileDrivenInput():
return $default(_that.sptNValue,_that.fos,_that.maxPileLength,_that.slsLoad,_that.ulsLoad,_that.useSelectColLoad,_that.id);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( double sptNValue,  double fos,  double maxPileLength,  double slsLoad,  double ulsLoad,  bool useSelectColLoad,  String id)?  $default,) {final _that = this;
switch (_that) {
case _PileDrivenInput() when $default != null:
return $default(_that.sptNValue,_that.fos,_that.maxPileLength,_that.slsLoad,_that.ulsLoad,_that.useSelectColLoad,_that.id);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _PileDrivenInput extends PileDrivenInput {
   _PileDrivenInput({this.sptNValue = 50, this.fos = 2, this.maxPileLength = 30, this.slsLoad = 1000, this.ulsLoad = 2000, this.useSelectColLoad = false, this.id = '1'}): super._();
  factory _PileDrivenInput.fromJson(Map<String, dynamic> json) => _$PileDrivenInputFromJson(json);

@override@JsonKey() final  double sptNValue;
@override@JsonKey() final  double fos;
@override@JsonKey() final  double maxPileLength;
@override@JsonKey() final  double slsLoad;
@override@JsonKey() final  double ulsLoad;
@override@JsonKey() final  bool useSelectColLoad;
@override@JsonKey() final  String id;

/// Create a copy of PileDrivenInput
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$PileDrivenInputCopyWith<_PileDrivenInput> get copyWith => __$PileDrivenInputCopyWithImpl<_PileDrivenInput>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$PileDrivenInputToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _PileDrivenInput&&(identical(other.sptNValue, sptNValue) || other.sptNValue == sptNValue)&&(identical(other.fos, fos) || other.fos == fos)&&(identical(other.maxPileLength, maxPileLength) || other.maxPileLength == maxPileLength)&&(identical(other.slsLoad, slsLoad) || other.slsLoad == slsLoad)&&(identical(other.ulsLoad, ulsLoad) || other.ulsLoad == ulsLoad)&&(identical(other.useSelectColLoad, useSelectColLoad) || other.useSelectColLoad == useSelectColLoad)&&(identical(other.id, id) || other.id == id));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,sptNValue,fos,maxPileLength,slsLoad,ulsLoad,useSelectColLoad,id);

@override
String toString() {
  return 'PileDrivenInput(sptNValue: $sptNValue, fos: $fos, maxPileLength: $maxPileLength, slsLoad: $slsLoad, ulsLoad: $ulsLoad, useSelectColLoad: $useSelectColLoad, id: $id)';
}


}

/// @nodoc
abstract mixin class _$PileDrivenInputCopyWith<$Res> implements $PileDrivenInputCopyWith<$Res> {
  factory _$PileDrivenInputCopyWith(_PileDrivenInput value, $Res Function(_PileDrivenInput) _then) = __$PileDrivenInputCopyWithImpl;
@override @useResult
$Res call({
 double sptNValue, double fos, double maxPileLength, double slsLoad, double ulsLoad, bool useSelectColLoad, String id
});




}
/// @nodoc
class __$PileDrivenInputCopyWithImpl<$Res>
    implements _$PileDrivenInputCopyWith<$Res> {
  __$PileDrivenInputCopyWithImpl(this._self, this._then);

  final _PileDrivenInput _self;
  final $Res Function(_PileDrivenInput) _then;

/// Create a copy of PileDrivenInput
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? sptNValue = null,Object? fos = null,Object? maxPileLength = null,Object? slsLoad = null,Object? ulsLoad = null,Object? useSelectColLoad = null,Object? id = null,}) {
  return _then(_PileDrivenInput(
sptNValue: null == sptNValue ? _self.sptNValue : sptNValue // ignore: cast_nullable_to_non_nullable
as double,fos: null == fos ? _self.fos : fos // ignore: cast_nullable_to_non_nullable
as double,maxPileLength: null == maxPileLength ? _self.maxPileLength : maxPileLength // ignore: cast_nullable_to_non_nullable
as double,slsLoad: null == slsLoad ? _self.slsLoad : slsLoad // ignore: cast_nullable_to_non_nullable
as double,ulsLoad: null == ulsLoad ? _self.ulsLoad : ulsLoad // ignore: cast_nullable_to_non_nullable
as double,useSelectColLoad: null == useSelectColLoad ? _self.useSelectColLoad : useSelectColLoad // ignore: cast_nullable_to_non_nullable
as bool,id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

// dart format on
