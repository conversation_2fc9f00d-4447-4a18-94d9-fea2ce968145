// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'transfer_beam_scheme_input_global.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_TransferBeamSchemeInputGlobal _$TransferBeamSchemeInputGlobalFromJson(
  Map<String, dynamic> json,
) => _TransferBeamSchemeInputGlobal(
  id: json['id'] as String? ?? '1',
  span: (json['span'] as num?)?.toDouble() ?? 20.0,
  loadWidth: (json['loadWidth'] as num?)?.toDouble() ?? 10.0,
  strZone: (json['strZone'] as num?)?.toDouble() ?? 1000.0,
  fcu: (json['fcu'] as num?)?.toDouble() ?? 45.0,
  cover: (json['cover'] as num?)?.toDouble() ?? 40.0,
  mainKValue: (json['mainKValue'] as num?)?.toDouble() ?? 0.25,
  mainSteelRatio: (json['mainSteelRatio'] as num?)?.toDouble() ?? 0.025,
  minS: (json['minS'] as num?)?.toInt() ?? 100,
  maxS: (json['maxS'] as num?)?.toInt() ?? 300,
  maxWidth: (json['maxWidth'] as num?)?.toDouble() ?? 2000.0,
  maxLayers: (json['maxLayers'] as num?)?.toInt() ?? 4,
  usage: json['usage'] as String? ?? '',
  slabThickness: (json['slabThickness'] as num?)?.toDouble() ?? 150.0,
  useSlabSelected: json['useSlabSelected'] as bool? ?? false,
);

Map<String, dynamic> _$TransferBeamSchemeInputGlobalToJson(
  _TransferBeamSchemeInputGlobal instance,
) => <String, dynamic>{
  'id': instance.id,
  'span': instance.span,
  'loadWidth': instance.loadWidth,
  'strZone': instance.strZone,
  'fcu': instance.fcu,
  'cover': instance.cover,
  'mainKValue': instance.mainKValue,
  'mainSteelRatio': instance.mainSteelRatio,
  'minS': instance.minS,
  'maxS': instance.maxS,
  'maxWidth': instance.maxWidth,
  'maxLayers': instance.maxLayers,
  'usage': instance.usage,
  'slabThickness': instance.slabThickness,
  'useSlabSelected': instance.useSlabSelected,
};
