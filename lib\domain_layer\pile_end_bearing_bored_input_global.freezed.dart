// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'pile_end_bearing_bored_input_global.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$PileEndBearingBoredInputGlobal {

 double get colLoadFactor; String get id;
/// Create a copy of PileEndBearingBoredInputGlobal
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$PileEndBearingBoredInputGlobalCopyWith<PileEndBearingBoredInputGlobal> get copyWith => _$PileEndBearingBoredInputGlobalCopyWithImpl<PileEndBearingBoredInputGlobal>(this as PileEndBearingBoredInputGlobal, _$identity);

  /// Serializes this PileEndBearingBoredInputGlobal to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is PileEndBearingBoredInputGlobal&&(identical(other.colLoadFactor, colLoadFactor) || other.colLoadFactor == colLoadFactor)&&(identical(other.id, id) || other.id == id));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,colLoadFactor,id);

@override
String toString() {
  return 'PileEndBearingBoredInputGlobal(colLoadFactor: $colLoadFactor, id: $id)';
}


}

/// @nodoc
abstract mixin class $PileEndBearingBoredInputGlobalCopyWith<$Res>  {
  factory $PileEndBearingBoredInputGlobalCopyWith(PileEndBearingBoredInputGlobal value, $Res Function(PileEndBearingBoredInputGlobal) _then) = _$PileEndBearingBoredInputGlobalCopyWithImpl;
@useResult
$Res call({
 double colLoadFactor, String id
});




}
/// @nodoc
class _$PileEndBearingBoredInputGlobalCopyWithImpl<$Res>
    implements $PileEndBearingBoredInputGlobalCopyWith<$Res> {
  _$PileEndBearingBoredInputGlobalCopyWithImpl(this._self, this._then);

  final PileEndBearingBoredInputGlobal _self;
  final $Res Function(PileEndBearingBoredInputGlobal) _then;

/// Create a copy of PileEndBearingBoredInputGlobal
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? colLoadFactor = null,Object? id = null,}) {
  return _then(_self.copyWith(
colLoadFactor: null == colLoadFactor ? _self.colLoadFactor : colLoadFactor // ignore: cast_nullable_to_non_nullable
as double,id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,
  ));
}

}


/// Adds pattern-matching-related methods to [PileEndBearingBoredInputGlobal].
extension PileEndBearingBoredInputGlobalPatterns on PileEndBearingBoredInputGlobal {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _PileEndBearingBoredInputGlobal value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _PileEndBearingBoredInputGlobal() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _PileEndBearingBoredInputGlobal value)  $default,){
final _that = this;
switch (_that) {
case _PileEndBearingBoredInputGlobal():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _PileEndBearingBoredInputGlobal value)?  $default,){
final _that = this;
switch (_that) {
case _PileEndBearingBoredInputGlobal() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( double colLoadFactor,  String id)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _PileEndBearingBoredInputGlobal() when $default != null:
return $default(_that.colLoadFactor,_that.id);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( double colLoadFactor,  String id)  $default,) {final _that = this;
switch (_that) {
case _PileEndBearingBoredInputGlobal():
return $default(_that.colLoadFactor,_that.id);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( double colLoadFactor,  String id)?  $default,) {final _that = this;
switch (_that) {
case _PileEndBearingBoredInputGlobal() when $default != null:
return $default(_that.colLoadFactor,_that.id);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _PileEndBearingBoredInputGlobal extends PileEndBearingBoredInputGlobal {
   _PileEndBearingBoredInputGlobal({this.colLoadFactor = 1.0, this.id = '1'}): super._();
  factory _PileEndBearingBoredInputGlobal.fromJson(Map<String, dynamic> json) => _$PileEndBearingBoredInputGlobalFromJson(json);

@override@JsonKey() final  double colLoadFactor;
@override@JsonKey() final  String id;

/// Create a copy of PileEndBearingBoredInputGlobal
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$PileEndBearingBoredInputGlobalCopyWith<_PileEndBearingBoredInputGlobal> get copyWith => __$PileEndBearingBoredInputGlobalCopyWithImpl<_PileEndBearingBoredInputGlobal>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$PileEndBearingBoredInputGlobalToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _PileEndBearingBoredInputGlobal&&(identical(other.colLoadFactor, colLoadFactor) || other.colLoadFactor == colLoadFactor)&&(identical(other.id, id) || other.id == id));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,colLoadFactor,id);

@override
String toString() {
  return 'PileEndBearingBoredInputGlobal(colLoadFactor: $colLoadFactor, id: $id)';
}


}

/// @nodoc
abstract mixin class _$PileEndBearingBoredInputGlobalCopyWith<$Res> implements $PileEndBearingBoredInputGlobalCopyWith<$Res> {
  factory _$PileEndBearingBoredInputGlobalCopyWith(_PileEndBearingBoredInputGlobal value, $Res Function(_PileEndBearingBoredInputGlobal) _then) = __$PileEndBearingBoredInputGlobalCopyWithImpl;
@override @useResult
$Res call({
 double colLoadFactor, String id
});




}
/// @nodoc
class __$PileEndBearingBoredInputGlobalCopyWithImpl<$Res>
    implements _$PileEndBearingBoredInputGlobalCopyWith<$Res> {
  __$PileEndBearingBoredInputGlobalCopyWithImpl(this._self, this._then);

  final _PileEndBearingBoredInputGlobal _self;
  final $Res Function(_PileEndBearingBoredInputGlobal) _then;

/// Create a copy of PileEndBearingBoredInputGlobal
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? colLoadFactor = null,Object? id = null,}) {
  return _then(_PileEndBearingBoredInputGlobal(
colLoadFactor: null == colLoadFactor ? _self.colLoadFactor : colLoadFactor // ignore: cast_nullable_to_non_nullable
as double,id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

// dart format on
