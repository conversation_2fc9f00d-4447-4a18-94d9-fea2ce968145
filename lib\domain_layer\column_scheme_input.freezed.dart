// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'column_scheme_input.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$ColumnSchemeInput {

 String get usage; double get slabThickness; double get loadWidth; double get loadLength; int get nosOfFloor; String get columnSchemeInputId;
/// Create a copy of ColumnSchemeInput
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ColumnSchemeInputCopyWith<ColumnSchemeInput> get copyWith => _$ColumnSchemeInputCopyWithImpl<ColumnSchemeInput>(this as ColumnSchemeInput, _$identity);

  /// Serializes this ColumnSchemeInput to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ColumnSchemeInput&&(identical(other.usage, usage) || other.usage == usage)&&(identical(other.slabThickness, slabThickness) || other.slabThickness == slabThickness)&&(identical(other.loadWidth, loadWidth) || other.loadWidth == loadWidth)&&(identical(other.loadLength, loadLength) || other.loadLength == loadLength)&&(identical(other.nosOfFloor, nosOfFloor) || other.nosOfFloor == nosOfFloor)&&(identical(other.columnSchemeInputId, columnSchemeInputId) || other.columnSchemeInputId == columnSchemeInputId));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,usage,slabThickness,loadWidth,loadLength,nosOfFloor,columnSchemeInputId);

@override
String toString() {
  return 'ColumnSchemeInput(usage: $usage, slabThickness: $slabThickness, loadWidth: $loadWidth, loadLength: $loadLength, nosOfFloor: $nosOfFloor, columnSchemeInputId: $columnSchemeInputId)';
}


}

/// @nodoc
abstract mixin class $ColumnSchemeInputCopyWith<$Res>  {
  factory $ColumnSchemeInputCopyWith(ColumnSchemeInput value, $Res Function(ColumnSchemeInput) _then) = _$ColumnSchemeInputCopyWithImpl;
@useResult
$Res call({
 String usage, double slabThickness, double loadWidth, double loadLength, int nosOfFloor, String columnSchemeInputId
});




}
/// @nodoc
class _$ColumnSchemeInputCopyWithImpl<$Res>
    implements $ColumnSchemeInputCopyWith<$Res> {
  _$ColumnSchemeInputCopyWithImpl(this._self, this._then);

  final ColumnSchemeInput _self;
  final $Res Function(ColumnSchemeInput) _then;

/// Create a copy of ColumnSchemeInput
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? usage = null,Object? slabThickness = null,Object? loadWidth = null,Object? loadLength = null,Object? nosOfFloor = null,Object? columnSchemeInputId = null,}) {
  return _then(_self.copyWith(
usage: null == usage ? _self.usage : usage // ignore: cast_nullable_to_non_nullable
as String,slabThickness: null == slabThickness ? _self.slabThickness : slabThickness // ignore: cast_nullable_to_non_nullable
as double,loadWidth: null == loadWidth ? _self.loadWidth : loadWidth // ignore: cast_nullable_to_non_nullable
as double,loadLength: null == loadLength ? _self.loadLength : loadLength // ignore: cast_nullable_to_non_nullable
as double,nosOfFloor: null == nosOfFloor ? _self.nosOfFloor : nosOfFloor // ignore: cast_nullable_to_non_nullable
as int,columnSchemeInputId: null == columnSchemeInputId ? _self.columnSchemeInputId : columnSchemeInputId // ignore: cast_nullable_to_non_nullable
as String,
  ));
}

}


/// Adds pattern-matching-related methods to [ColumnSchemeInput].
extension ColumnSchemeInputPatterns on ColumnSchemeInput {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _ColumnSchemeInput value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _ColumnSchemeInput() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _ColumnSchemeInput value)  $default,){
final _that = this;
switch (_that) {
case _ColumnSchemeInput():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _ColumnSchemeInput value)?  $default,){
final _that = this;
switch (_that) {
case _ColumnSchemeInput() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String usage,  double slabThickness,  double loadWidth,  double loadLength,  int nosOfFloor,  String columnSchemeInputId)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _ColumnSchemeInput() when $default != null:
return $default(_that.usage,_that.slabThickness,_that.loadWidth,_that.loadLength,_that.nosOfFloor,_that.columnSchemeInputId);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String usage,  double slabThickness,  double loadWidth,  double loadLength,  int nosOfFloor,  String columnSchemeInputId)  $default,) {final _that = this;
switch (_that) {
case _ColumnSchemeInput():
return $default(_that.usage,_that.slabThickness,_that.loadWidth,_that.loadLength,_that.nosOfFloor,_that.columnSchemeInputId);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String usage,  double slabThickness,  double loadWidth,  double loadLength,  int nosOfFloor,  String columnSchemeInputId)?  $default,) {final _that = this;
switch (_that) {
case _ColumnSchemeInput() when $default != null:
return $default(_that.usage,_that.slabThickness,_that.loadWidth,_that.loadLength,_that.nosOfFloor,_that.columnSchemeInputId);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _ColumnSchemeInput extends ColumnSchemeInput {
   _ColumnSchemeInput({this.usage = '', this.slabThickness = 150.0, this.loadWidth = 8, this.loadLength = 12, this.nosOfFloor = 1, this.columnSchemeInputId = ''}): super._();
  factory _ColumnSchemeInput.fromJson(Map<String, dynamic> json) => _$ColumnSchemeInputFromJson(json);

@override@JsonKey() final  String usage;
@override@JsonKey() final  double slabThickness;
@override@JsonKey() final  double loadWidth;
@override@JsonKey() final  double loadLength;
@override@JsonKey() final  int nosOfFloor;
@override@JsonKey() final  String columnSchemeInputId;

/// Create a copy of ColumnSchemeInput
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ColumnSchemeInputCopyWith<_ColumnSchemeInput> get copyWith => __$ColumnSchemeInputCopyWithImpl<_ColumnSchemeInput>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ColumnSchemeInputToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ColumnSchemeInput&&(identical(other.usage, usage) || other.usage == usage)&&(identical(other.slabThickness, slabThickness) || other.slabThickness == slabThickness)&&(identical(other.loadWidth, loadWidth) || other.loadWidth == loadWidth)&&(identical(other.loadLength, loadLength) || other.loadLength == loadLength)&&(identical(other.nosOfFloor, nosOfFloor) || other.nosOfFloor == nosOfFloor)&&(identical(other.columnSchemeInputId, columnSchemeInputId) || other.columnSchemeInputId == columnSchemeInputId));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,usage,slabThickness,loadWidth,loadLength,nosOfFloor,columnSchemeInputId);

@override
String toString() {
  return 'ColumnSchemeInput(usage: $usage, slabThickness: $slabThickness, loadWidth: $loadWidth, loadLength: $loadLength, nosOfFloor: $nosOfFloor, columnSchemeInputId: $columnSchemeInputId)';
}


}

/// @nodoc
abstract mixin class _$ColumnSchemeInputCopyWith<$Res> implements $ColumnSchemeInputCopyWith<$Res> {
  factory _$ColumnSchemeInputCopyWith(_ColumnSchemeInput value, $Res Function(_ColumnSchemeInput) _then) = __$ColumnSchemeInputCopyWithImpl;
@override @useResult
$Res call({
 String usage, double slabThickness, double loadWidth, double loadLength, int nosOfFloor, String columnSchemeInputId
});




}
/// @nodoc
class __$ColumnSchemeInputCopyWithImpl<$Res>
    implements _$ColumnSchemeInputCopyWith<$Res> {
  __$ColumnSchemeInputCopyWithImpl(this._self, this._then);

  final _ColumnSchemeInput _self;
  final $Res Function(_ColumnSchemeInput) _then;

/// Create a copy of ColumnSchemeInput
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? usage = null,Object? slabThickness = null,Object? loadWidth = null,Object? loadLength = null,Object? nosOfFloor = null,Object? columnSchemeInputId = null,}) {
  return _then(_ColumnSchemeInput(
usage: null == usage ? _self.usage : usage // ignore: cast_nullable_to_non_nullable
as String,slabThickness: null == slabThickness ? _self.slabThickness : slabThickness // ignore: cast_nullable_to_non_nullable
as double,loadWidth: null == loadWidth ? _self.loadWidth : loadWidth // ignore: cast_nullable_to_non_nullable
as double,loadLength: null == loadLength ? _self.loadLength : loadLength // ignore: cast_nullable_to_non_nullable
as double,nosOfFloor: null == nosOfFloor ? _self.nosOfFloor : nosOfFloor // ignore: cast_nullable_to_non_nullable
as int,columnSchemeInputId: null == columnSchemeInputId ? _self.columnSchemeInputId : columnSchemeInputId // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

// dart format on
