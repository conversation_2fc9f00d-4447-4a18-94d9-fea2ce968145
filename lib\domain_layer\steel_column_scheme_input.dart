import 'package:freezed_annotation/freezed_annotation.dart';
// import 'package:flutter/foundation.dart';
part 'steel_column_scheme_input.freezed.dart';
part 'steel_column_scheme_input.g.dart';

@freezed
abstract class SteelColumnSchemeInput with _$SteelColumnSchemeInput{
  const SteelColumnSchemeInput._();
  factory SteelColumnSchemeInput({
    @Default('') String usage,
    @Default(130.0) double slabThickness,
    @Default(8) double loadWidth,
    @Default(12) double loadLength,
    @Default(1) int nosOfFloor,
    @Default('') String steelColumnSchemeInputId, //will be overriden  as soon as new instance created
  }) = _SteelColumnSchemeInput;

  factory SteelColumnSchemeInput.fromJson(Map<String, Object?> json) =>
      _$SteelColumnSchemeInputFromJson(json);
}
