import 'dart:convert';
import 'dart:io';
import 'package:drift/drift.dart';
import 'package:drift/native.dart';
import 'package:path/path.dart' as p;
import 'package:path_provider/path_provider.dart';
import 'package:structify/domain_layer/basement_wall_scheme_data.dart';
import 'package:structify/domain_layer/basement_wall_scheme_input.dart';
import 'package:structify/domain_layer/beam_scheme_data.dart';
import 'package:structify/domain_layer/cantilever_scheme_data.dart';
import 'package:structify/domain_layer/cantilever_scheme_input.dart';
import 'package:structify/domain_layer/cantilever_scheme_input_global.dart';
import 'package:structify/domain_layer/column_scheme_input.dart';
import 'package:structify/domain_layer/column_scheme_input_global.dart';
import 'package:structify/domain_layer/data_struct/recommend_load.dart';
import 'package:structify/domain_layer/programme_item.dart';
import 'package:structify/domain_layer/slab_scheme_data.dart';
import 'package:structify/domain_layer/slab_scheme_input.dart';
import 'package:structify/domain_layer/steel_cantilever_truss_scheme_data.dart';
import 'package:structify/domain_layer/steel_cantilever_truss_scheme_input.dart';
import 'package:structify/domain_layer/steel_cantilever_truss_scheme_input_global.dart';
import 'package:structify/domain_layer/transfer_beam_scheme_data.dart';
import 'package:structify/domain_layer/transfer_beam_scheme_input.dart';
import 'package:structify/domain_layer/transfer_beam_scheme_input_global.dart';
import 'package:structify/domain_layer/wind_load.dart';

//data layer
import '../data_layer/entities.dart';

//domain layer
import '../domain_layer/column_scheme_data.dart';
import '../domain_layer/data_struct/carbon_struct.dart';
import '../domain_layer/footing_scheme_data.dart';
import '../domain_layer/footing_scheme_input.dart';
import '../domain_layer/footing_scheme_input_global.dart';
import '../domain_layer/loading_table.dart';
import '../domain_layer/beam_scheme_input.dart';
import '../domain_layer/global_data.dart';
import '../domain_layer/pile_driven_data.dart';
import '../domain_layer/pile_driven_input.dart';
import '../domain_layer/pile_driven_input_global.dart';
import '../domain_layer/pile_end_bearing_bored_data.dart';
import '../domain_layer/pile_end_bearing_bored_input.dart';
import '../domain_layer/pile_end_bearing_bored_input_global.dart';
import '../domain_layer/pile_frictional_bored_data.dart';
import '../domain_layer/pile_frictional_bored_input.dart';
import '../domain_layer/pile_frictional_bored_input_global.dart';
import '../domain_layer/pile_socketed_data.dart';
import '../domain_layer/steel_beam_scheme_data.dart';
import '../domain_layer/steel_beam_scheme_input.dart';
import '../domain_layer/steel_column_scheme_data.dart';
import '../domain_layer/steel_column_scheme_input.dart';
import '../domain_layer/steel_column_scheme_input_global.dart';
import '../domain_layer/steel_transfer_truss_scheme_data.dart';
import '../domain_layer/steel_transfer_truss_scheme_input.dart';
import '../domain_layer/steel_transfer_truss_scheme_input_global.dart';
import '../domain_layer/strzone_table.dart';
import '../domain_layer/wind_load_global.dart';

part 'app_database.g.dart';

@DriftDatabase(include: {'tables.drift'})
class AppDatabase extends _$AppDatabase {
  // static AppDatabase instance() => AppDatabase();
  // AppDatabase() : super(_openConnection());
  // Private constructor
  AppDatabase._() : super(_openConnection());

  // Static instance variable
  static final AppDatabase _instance = AppDatabase._();

  // public constructor is factory type to return the same instance
  factory AppDatabase() {
    return _instance;
  }

  //another method to return the same instance
  factory AppDatabase.instance() => _instance;

  @override
  int get schemaVersion => 1;

  @override
  MigrationStrategy get migration {
    return MigrationStrategy(
      onCreate: (Migrator m) async {
        await m.createAll();
      },
      onUpgrade: (Migrator m, int from, int to) async {
        if (from < 1) {
          // placeholder;
        }
        if (from < 2) {
          // placeholder;
        }
      },
    );
  }

  Future<void> saveAllLoadingTables(List<LoadingTable> tables) async {
    if (tables.isNotEmpty) {
      List<ListLoadingTableCompanion> rows = [];
      await Future.forEach(tables, (loadingTable) async {
        final x1 =
            LoadingTableEntity(
              usage: loadingTable.usage,
              finish: loadingTable.finish,
              service: loadingTable.service,
              liveLoad: loadingTable.liveLoad,
              loadingTableId: loadingTable.loadingTableId,
            ).toCompanion();
        rows.add(x1);
        // await transaction(() async {
        // await into(tasks).insertOnConflictUpdate(x1);
      });
      await transaction(() async {
        await batch((batch) {
          batch.insertAllOnConflictUpdate(listLoadingTable, rows);
        });
      });
    }
    // finally, check if any task exist in database only
    // this tasks need to be deleted as they no longer exist in
    // latest task list

    final Set<String> x =
        tables.map((loadingTable) => loadingTable.loadingTableId).toSet();
    await transaction(() async {
      await (delete(listLoadingTable)
        ..where((tbl) => tbl.loadingTableId.isNotIn(x))).go();
    });

    // print("Save All Task: Done !");
  }

  Future<void> deleteAllLoadingTables() async {
    await transaction(() async {
      await (delete(listLoadingTable)).go();
    });
  }

  Future<List<LoadingTable>> queryAllLoadingTables() async {
    final x = await select(listLoadingTable).get();
    final List<LoadingTable> finalList = [];
    await Future.forEach(x, (x1) async {
      final x2 = LoadingTable(
        usage: x1.usage,
        finish: x1.finish,
        service: x1.service,
        liveLoad: x1.liveLoad,
        loadingTableId: x1.loadingTableId,
      );
      finalList.add(x2);
    });
    return finalList;
  }

  Future<void> saveGlobalData(GlobalData data) async {
    final x1 =
        GlobalDataEntity(
          id: '1',
          unit: data.unit,
          sdlFactor: data.sdlFactor,
          llFactor: data.llFactor,
          rcUnitWeight: data.rcUnitWeight,
          finishUnitWeight: data.finishUnitWeight,
          steelUnitWeight: data.steelUnitWeight,
          soilUnitWeight: data.soilUnitWeight,
          waterUnitWeight: data.waterUnitWeight,
          // showGlobalDataUiInSubPage: data.showGlobalDataUiInSubPage,
        ).toCompanion();
    await transaction(() async {
      await (into(listGlobalData).insertOnConflictUpdate(x1));
    });
    // await (into(listGlobalData).insertOnConflictUpdate(x1));
  }

  Future<GlobalData> queryGlobalData() async {
    final x = await select(listGlobalData).get();
    if (x.isNotEmpty) {
      final globalData = GlobalData(
        unit: x[0].unit,
        sdlFactor: x[0].sdlFactor,
        llFactor: x[0].llFactor,
        rcUnitWeight: x[0].rcUnitWeight,
        finishUnitWeight: x[0].finishUnitWeight,
        steelUnitWeight: x[0].steelUnitWeight,
        soilUnitWeight: x[0].soilUnitWeight,
        waterUnitWeight: x[0].waterUnitWeight,
        // showGlobalDataUiInSubPage: x[0].showGlobalDataUiInSubPage,
      );
      return globalData;
    } else {
      final globalData = GlobalData();
      return globalData;
    }
  }

  Future<void> saveBeamSchemeInput(BeamSchemeInput data) async {
    final x1 =
        BeamSchemeInputEntity(
          id: '1',
          shortSpan: data.shortSpan,
          longSpan: data.longSpan,
          bays: data.bays,
          mainStrZone: data.mainStrZone,
          secStrZone: data.secStrZone,
          fcu: data.fcu,
          cover: data.cover,
          mainKValue: data.mainKValue,
          mainSteelRatio: data.mainSteelRatio,
          secKValue: data.secKValue,
          secSteelRatio: data.secSteelRatio,
          minS: data.minS,
          maxS: data.maxS,
          maxWidth: data.maxWidth,
          maxLayers: data.maxLayers,
          shortSpanIncreament: data.shortSpanIncreament,
          longSpanIncreament: data.longSpanIncreament,
          baysIncreament: data.baysIncreament,
          iterationSteps: data.iterationSteps,
          usage: data.usage,
          slabThickness: data.slabThickness,
          useSlabSelected: data.useSlabSelected,
        ).toCompanion();

    await (into(listBeamSchemeInput).insertOnConflictUpdate(x1));
  }

  Future<void> saveBeamSchemeData(List<BeamSchemeData> beamSchemes) async {
    final List<ListBeamSchemeDataCompanion> finalList = [];
    await Future.forEach(beamSchemes, (scheme) async {
      final x1 =
          BeamSchemeDataEntity(
            usage: scheme.usage,
            finish: scheme.finish,
            service: scheme.service,
            liveLoad: scheme.liveLoad,
            loadingTableId: scheme.loadingTableId,
            shortSpan: scheme.shortSpan,
            longSpan: scheme.longSpan,
            bays: scheme.bays,
            mainStrZone: scheme.mainStrZone,
            secStrZone: scheme.secStrZone,
            fcu: scheme.fcu,
            cover: scheme.cover,
            mainWidth: scheme.mainWidth,
            secWidth: scheme.secWidth,
            mainTopBar: scheme.mainTopBar,
            mainBottomBar: scheme.mainBottomBar,
            mainLinks: scheme.mainLinks,
            secTopBar: scheme.secTopBar,
            secBottomBar: scheme.secBottomBar,
            secLinks: scheme.secLinks,
            beamSchemeId: scheme.beamSchemeId,
            calsLog: scheme.calsLog,
            isSelected: scheme.isSelected,
          ).toCompanion();

      finalList.add(x1);
    });
    await transaction(() async {
      await batch((batch) {
        batch.insertAllOnConflictUpdate(listBeamSchemeData, finalList);
      });
    });
    // finally, check if any task exist in database only
    // this tasks need to be deleted as they no longer exist in
    // latest task list

    final Set<String> x2 =
        beamSchemes.map((scheme) => scheme.beamSchemeId).toSet();
    await transaction(() async {
      await (delete(listBeamSchemeData)
        ..where((tbl) => tbl.beamSchemeId.isNotIn(x2))).go();
    });
  }

  Future<BeamSchemeInput> queryBeamSchemeInput() async {
    final x = await select(listBeamSchemeInput).get();
    final BeamSchemeInput beamSchemeInput;
    if (x.isNotEmpty) {
      beamSchemeInput = BeamSchemeInput(
        id: x[0].id,
        shortSpan: x[0].shortSpan,
        longSpan: x[0].longSpan,
        bays: x[0].bays,
        mainStrZone: x[0].mainStrZone,
        secStrZone: x[0].secStrZone,
        fcu: x[0].fcu,
        cover: x[0].cover,
        mainKValue: x[0].mainKValue,
        mainSteelRatio: x[0].mainSteelRatio,
        secKValue: x[0].secKValue,
        secSteelRatio: x[0].secSteelRatio,
        minS: x[0].minS,
        maxS: x[0].maxS,
        maxWidth: x[0].maxWidth,
        maxLayers: x[0].maxLayers,
        shortSpanIncreament: x[0].shortSpanIncreament,
        longSpanIncreament: x[0].longSpanIncreament,
        baysIncreament: x[0].baysIncreament,
        iterationSteps: x[0].iterationSteps,
        usage: x[0].usage,
        slabThickness: x[0].slabThickness,
        useSlabSelected: x[0].useSlabSelected,
      );
    } else {
      beamSchemeInput = BeamSchemeInput();
    }
    return beamSchemeInput;
  }

  Future<List<BeamSchemeData>> queryBeamSchemeData() async {
    final x = await select(listBeamSchemeData).get();
    List<BeamSchemeData> finalList = [];
    if (x.isNotEmpty) {
      await Future.forEach(x, (x1) {
        final data = BeamSchemeData(
          usage: x1.usage,
          finish: x1.finish,
          service: x1.service,
          liveLoad: x1.liveLoad,
          loadingTableId: x1.loadingTableId,
          shortSpan: x1.shortSpan,
          longSpan: x1.longSpan,
          bays: x1.bays,
          mainStrZone: x1.mainStrZone,
          secStrZone: x1.secStrZone,
          fcu: x1.fcu,
          cover: x1.cover,
          mainWidth: x1.mainWidth,
          secWidth: x1.secWidth,
          mainTopBar: x1.mainTopBar,
          mainBottomBar: x1.mainBottomBar,
          mainLinks: x1.mainLinks,
          secTopBar: x1.secTopBar,
          secBottomBar: x1.secBottomBar,
          secLinks: x1.secLinks,
          beamSchemeId: x1.beamSchemeId,
          calsLog: x1.calsLog,
          isSelected: x1.isSelected,
        );
        finalList.add(data);
      });
    }
    return finalList;
  }

  Future<SlabSchemeInput> querySlabSchemeInput() async {
    final x = await select(listSlabSchemeInput).get();
    final SlabSchemeInput slabSchemeInput;
    if (x.isNotEmpty) {
      slabSchemeInput = SlabSchemeInput(
        id: x[0].id,
        span: x[0].span,
        fcu: x[0].fcu,
        cover: x[0].cover,
        mainKValue: x[0].mainKValue,
        mainSteelRatio: x[0].mainSteelRatio,
        minS: x[0].minS,
        maxS: x[0].maxS,
        maxDepth: x[0].maxDepth,
        minDepth: x[0].minDepth,
        maxLayers: x[0].maxLayers,
        spanIncreament: x[0].spanIncreament,
        iterationSteps: x[0].iterationSteps,
        usage: x[0].usage,
      );
    } else {
      slabSchemeInput = SlabSchemeInput();
    }
    return slabSchemeInput;
  }

  Future<List<SlabSchemeData>> querySlabSchemeData() async {
    final x = await select(listSlabSchemeData).get();
    final List<SlabSchemeData> finalList = [];
    if (x.isNotEmpty) {
      await Future.forEach(x, (x1) {
        final data = SlabSchemeData(
          usage: x1.usage,
          finish: x1.finish,
          service: x1.service,
          liveLoad: x1.liveLoad,
          loadingTableId: x1.loadingTableId,
          span: x1.span,
          strZone: x1.strZone,
          fcu: x1.fcu,
          cover: x1.cover,
          mainTopBar: x1.mainTopBar,
          mainBottomBar: x1.mainBottomBar,
          mainLinks: x1.mainLinks,
          slabSchemeId: x1.slabSchemeId,
          calsLog: x1.calsLog,
          isSelected: x1.isSelected,
        );
        finalList.add(data);
      });
    }
    return finalList;
  }

  Future<void> saveSlabSchemeInput(SlabSchemeInput data) async {
    final x1 =
        SlabSchemeInputEntity(
          id: '1',
          span: data.span,
          fcu: data.fcu,
          cover: data.cover,
          mainKValue: data.mainKValue,
          mainSteelRatio: data.mainSteelRatio,
          minS: data.minS,
          maxS: data.maxS,
          maxDepth: data.maxDepth,
          minDepth: data.minDepth,
          maxLayers: data.maxLayers,
          spanIncreament: data.spanIncreament,
          iterationSteps: data.iterationSteps,
          usage: data.usage,
        ).toCompanion();
    await (into(listSlabSchemeInput).insertOnConflictUpdate(x1));
  }

  Future<void> saveSlabSchemeData(List<SlabSchemeData> slabSchemes) async {
    final List<ListSlabSchemeDataCompanion> finalList = [];
    await Future.forEach(slabSchemes, (scheme) async {
      final x1 =
          SlabSchemeDataEntity(
            usage: scheme.usage,
            finish: scheme.finish,
            service: scheme.service,
            liveLoad: scheme.liveLoad,
            loadingTableId: scheme.loadingTableId,
            span: scheme.span,
            strZone: scheme.strZone,
            fcu: scheme.fcu,
            cover: scheme.cover,
            mainTopBar: scheme.mainTopBar,
            mainBottomBar: scheme.mainBottomBar,
            mainLinks: scheme.mainLinks,
            slabSchemeId: scheme.slabSchemeId,
            calsLog: scheme.calsLog,
            isSelected: scheme.isSelected,
          ).toCompanion();

      finalList.add(x1);
    });
    await transaction(() async {
      await batch((batch) {
        batch.insertAllOnConflictUpdate(listSlabSchemeData, finalList);
      });
    });
    // finally, check if any task exist in database only
    // this tasks need to be deleted as they no longer exist in
    // latest task list

    final Set<String> x2 =
        slabSchemes.map((scheme) => scheme.slabSchemeId).toSet();
    await transaction(() async {
      await (delete(listSlabSchemeData)
        ..where((tbl) => tbl.slabSchemeId.isNotIn(x2))).go();
    });
  }

  Future<List<ColumnSchemeInput>> queryColumnSchemeInput() async {
    final x = await select(listColumnSchemeInput).get();
    final List<ColumnSchemeInput> finalList = [];
    await Future.forEach(x, (x1) {
      final x2 = ColumnSchemeInput(
        usage: x1.usage,
        slabThickness: x1.slabThickness,
        loadWidth: x1.loadWidth,
        loadLength: x1.loadLength,
        nosOfFloor: x1.nosOfFloor,
        columnSchemeInputId: x1.columnSchemeInputId,
      );
      finalList.add(x2);
    });

    return finalList;
  }

  Future<void> saveColumnSchemeInput(List<ColumnSchemeInput> tables) async {
    if (tables.isNotEmpty) {
      List<ListColumnSchemeInputCompanion> rows = [];
      await Future.forEach(tables, (input) async {
        final x1 =
            ColumnSchemeInputEntity(
              usage: input.usage,
              slabThickness: input.slabThickness,
              loadWidth: input.loadWidth,
              loadLength: input.loadLength,
              nosOfFloor: input.nosOfFloor,
              columnSchemeInputId: input.columnSchemeInputId,
            ).toCompanion();
        rows.add(x1);
        // await transaction(() async {
        // await into(tasks).insertOnConflictUpdate(x1);
      });
      await transaction(() async {
        await batch((batch) {
          batch.insertAllOnConflictUpdate(listColumnSchemeInput, rows);
        });
      });
    }
    // finally, check if any task exist in database only
    // this tasks need to be deleted as they no longer exist in
    // latest task list

    final Set<String> x = tables.map((x) => x.columnSchemeInputId).toSet();
    await transaction(() async {
      await (delete(listColumnSchemeInput)
        ..where((tbl) => tbl.columnSchemeInputId.isNotIn(x))).go();
    });
    // print("Save All Task: Done !");
  }

  Future<ColumnSchemeInputGlobal> queryColumnSchemeInputGlobal() async {
    final x = await select(listColumnSchemeInputGlobal).get();
    if (x.isNotEmpty) {
      final columnSchemeInputGlobal = ColumnSchemeInputGlobal(
        columnSchemeInputGlobalId: x[0].columnSchemeInputGlobalId,
        cover: x[0].cover,
        minColumnSize: x[0].minColumnSize,
        sizeIncrement: x[0].sizeIncrement,
        iterationSteps: x[0].iterationSteps,
        concreteGrade: x[0].concreteGrade,
        minSteelRatio: x[0].minSteelRatio,
        maxSteelRatio: x[0].maxSteelRatio,
        safetyFactor: x[0].safetyFactor,
        minClearS: x[0].minClearS,
        useSlabSelected: x[0].useSlabSelected,
      );
      return columnSchemeInputGlobal;
    } else {
      final columnSchemeInputGlobal = ColumnSchemeInputGlobal();
      return columnSchemeInputGlobal;
    }
  }

  Future<void> saveColumnSchemeInputGlobal(
    ColumnSchemeInputGlobal input,
  ) async {
    final x1 =
        ColumnSchemeInputGlobalEntity(
          columnSchemeInputGlobalId: input.columnSchemeInputGlobalId,
          cover: input.cover,
          minColumnSize: input.minColumnSize,
          sizeIncrement: input.sizeIncrement,
          iterationSteps: input.iterationSteps,
          concreteGrade: input.concreteGrade,
          minSteelRatio: input.minSteelRatio,
          maxSteelRatio: input.maxSteelRatio,
          safetyFactor: input.safetyFactor,
          minClearS: input.minClearS,
          useSlabSelected: input.useSlabSelected,
        ).toCompanion();
    await transaction(() async {
      await (into(listColumnSchemeInputGlobal).insertOnConflictUpdate(x1));
    });
  }

  Future<List<ColumnSchemeData>> queryColumnSchemeData() async {
    final x = await select(listColumnSchemeData).get();
    final List<ColumnSchemeData> finalList = [];
    if (x.isNotEmpty) {
      await Future.forEach(x, (x1) {
        final data = ColumnSchemeData(
          columnSchemeDataId: x1.columnSchemeDataId,
          sdl: x1.sdl,
          ll: x1.ll,
          slsLoad: x1.slsLoad,
          ulsLoad: x1.ulsLoad,
          size: x1.size,
          fcu: x1.fcu,
          cover: x1.cover,
          axialCapacitySquare: x1.axialCapacitySquare,
          mainBarSquare: x1.mainBarSquare,
          steelRatioSqaure: x1.steelRatioSqaure,
          axialCapacityCircle: x1.axialCapacityCircle,
          mainBarCircle: x1.mainBarCircle,
          steelRatioCircle: x1.steelRatioCircle,
          calsLog: x1.calsLog,
          isSelected: x1.isSelected,
        );
        finalList.add(data);
      });
    }
    return finalList;
  }

  Future<void> saveColumnSchemeData(List<ColumnSchemeData> tables) async {
    if (tables.isNotEmpty) {
      List<ListColumnSchemeDataCompanion> rows = [];
      await Future.forEach(tables, (input) async {
        final x1 =
            ColumnSchemeDataEntity(
              columnSchemeDataId: input.columnSchemeDataId,
              sdl: input.sdl,
              ll: input.ll,
              slsLoad: input.slsLoad,
              ulsLoad: input.ulsLoad,
              size: input.size,
              fcu: input.fcu,
              cover: input.cover,
              axialCapacitySquare: input.axialCapacitySquare,
              mainBarSquare: input.mainBarSquare,
              steelRatioSqaure: input.steelRatioSqaure,
              axialCapacityCircle: input.axialCapacityCircle,
              mainBarCircle: input.mainBarCircle,
              steelRatioCircle: input.steelRatioCircle,
              calsLog: input.calsLog,
              isSelected: input.isSelected,
            ).toCompanion();
        rows.add(x1);
        // await transaction(() async {
        // await into(tasks).insertOnConflictUpdate(x1);
      });
      await transaction(() async {
        await batch((batch) {
          batch.insertAllOnConflictUpdate(listColumnSchemeData, rows);
        });
      });
    }
    // finally, check if any task exist in database only
    // this tasks need to be deleted as they no longer exist in
    // latest task list

    final Set<String> x = tables.map((x) => x.columnSchemeDataId).toSet();
    await transaction(() async {
      await (delete(listColumnSchemeData)
        ..where((tbl) => tbl.columnSchemeDataId.isNotIn(x))).go();
    });
    // print("Save All Task: Done !");
  }

  Future<TransferBeamSchemeInputGlobal>
  queryTransferBeamSchemeInputGlobal() async {
    final x = await select(listTransferBeamSchemeInputGlobal).get();
    late final TransferBeamSchemeInputGlobal transferBeamSchemeInputGlobal;
    if (x.isNotEmpty) {
      transferBeamSchemeInputGlobal = TransferBeamSchemeInputGlobal(
        id: x[0].id,
        span: x[0].span,
        loadWidth: x[0].loadWidth,
        strZone: x[0].strZone,
        fcu: x[0].fcu,
        cover: x[0].cover,
        mainKValue: x[0].mainKValue,
        mainSteelRatio: x[0].mainSteelRatio,
        minS: x[0].minS,
        maxS: x[0].maxS,
        maxWidth: x[0].maxWidth,
        maxLayers: x[0].maxLayers,
        usage: x[0].usage,
        slabThickness: x[0].slabThickness,
        useSlabSelected: x[0].useSlabSelected,
      );
    } else {
      transferBeamSchemeInputGlobal = TransferBeamSchemeInputGlobal();
    }
    return transferBeamSchemeInputGlobal;
  }

  Future<void> saveTransferBeamSchemeInputGlobal(
    TransferBeamSchemeInputGlobal data,
  ) async {
    final x1 =
        TransferBeamSchemeInputGlobalEntity(
          id: '1',
          span: data.span,
          loadWidth: data.loadWidth,
          strZone: data.strZone,
          fcu: data.fcu,
          cover: data.cover,
          mainKValue: data.mainKValue,
          mainSteelRatio: data.mainSteelRatio,
          minS: data.minS,
          maxS: data.maxS,
          maxWidth: data.maxWidth,
          maxLayers: data.maxLayers,
          usage: data.usage,
          slabThickness: data.slabThickness,
          useSlabSelected: data.useSlabSelected,
        ).toCompanion();
    await transaction(() async {
      await (into(
        listTransferBeamSchemeInputGlobal,
      ).insertOnConflictUpdate(x1));
    });
    // await (into(listGlobalData).insertOnConflictUpdate(x1));
  }

  Future<List<TransferBeamSchemeInput>> queryTransferBeamSchemeInput() async {
    final x = await select(listTransferBeamSchemeInput).get();
    final List<TransferBeamSchemeInput> finalList = [];
    if (x.isNotEmpty) {
      await Future.forEach(x, (x1) {
        final data = TransferBeamSchemeInput(
          pointLoad: x1.pointLoad,
          distA: x1.distA,
          loadFromSelectedCol: x1.loadFromSelectedCol,
          transferBeamSchemeInputId: x1.transferBeamSchemeInputId,
        );
        finalList.add(data);
      });
    }
    return finalList;
  }

  Future<void> saveTransferBeamSchemeInput(
    List<TransferBeamSchemeInput> inputs,
  ) async {
    // print("Save: transferBeamSchemeInput");
    if (inputs.isNotEmpty) {
      List<ListTransferBeamSchemeInputCompanion> rows = [];
      await Future.forEach(inputs, (input) async {
        final x1 =
            TransferBeamSchemeInputEntity(
              pointLoad: input.pointLoad,
              distA: input.distA,
              loadFromSelectedCol: input.loadFromSelectedCol,
              transferBeamSchemeInputId: input.transferBeamSchemeInputId,
            ).toCompanion();
        rows.add(x1);
        // await transaction(() async {
        // await into(tasks).insertOnConflictUpdate(x1);
      });
      await transaction(() async {
        await batch((batch) {
          batch.insertAllOnConflictUpdate(listTransferBeamSchemeInput, rows);
        });
      });
    }
    // finally, check if any thing exist in database but not provider
    // provider always overrides. That means database is always based on provider, not the reverse.

    final Set<String> x =
        inputs.map((x) => x.transferBeamSchemeInputId).toSet();
    await transaction(() async {
      await (delete(listTransferBeamSchemeInput)
        ..where((tbl) => tbl.transferBeamSchemeInputId.isNotIn(x))).go();
    });
    // print("Save All Task: Done !");
  }

  Future<TransferBeamSchemeData> queryTransferBeamSchemeData() async {
    final x = await select(listTransferBeamSchemeData).get();
    final TransferBeamSchemeData transferBeamSchemeData;
    if (x.isNotEmpty) {
      transferBeamSchemeData = TransferBeamSchemeData(
        usage: x[0].usage,
        slabThickness: x[0].slabThickness,
        span: x[0].span,
        loadWidth: x[0].loadWidth,
        strZone: x[0].strZone,
        fcu: x[0].fcu,
        cover: x[0].cover,
        mainWidth: x[0].mainWidth,
        mainTopBar: x[0].mainTopBar,
        mainBottomBar: x[0].mainBottomBar,
        mainLinks: x[0].mainLinks,
        id: x[0].id,
        calsLog: x[0].calsLog,
        beamForce: x[0].beamForce,
      );
    } else {
      transferBeamSchemeData = TransferBeamSchemeData();
    }
    return transferBeamSchemeData;
  }

  Future<void> saveTransferBeamSchemeData(
    TransferBeamSchemeData transferBeamSchemeData,
  ) async {
    final x1 =
        TransferBeamSchemeDataEntity(
          usage: transferBeamSchemeData.usage,
          slabThickness: transferBeamSchemeData.slabThickness,
          span: transferBeamSchemeData.span,
          loadWidth: transferBeamSchemeData.loadWidth,
          strZone: transferBeamSchemeData.strZone,
          fcu: transferBeamSchemeData.fcu,
          cover: transferBeamSchemeData.cover,
          mainWidth: transferBeamSchemeData.mainWidth,
          mainTopBar: transferBeamSchemeData.mainTopBar,
          mainBottomBar: transferBeamSchemeData.mainBottomBar,
          mainLinks: transferBeamSchemeData.mainLinks,
          id: transferBeamSchemeData.id,
          calsLog: transferBeamSchemeData.calsLog,
          beamForce: transferBeamSchemeData.beamForce,
        ).toCompanion();
    await transaction(() async {
      await (into(listTransferBeamSchemeData).insertOnConflictUpdate(x1));
    });
  }

  Future<SteelBeamSchemeInput> querySteelBeamSchemeInput() async {
    final x = await select(listSteelBeamSchemeInput).get();
    final SteelBeamSchemeInput steelBeamSchemeInput;
    if (x.isNotEmpty) {
      steelBeamSchemeInput = SteelBeamSchemeInput(
        id: x[0].id,
        shortSpan: x[0].shortSpan,
        longSpan: x[0].longSpan,
        bays: x[0].bays,
        strZone: x[0].strZone,
        fsy: x[0].fsy,
        shortSpanIncreament: x[0].shortSpanIncreament,
        longSpanIncreament: x[0].longSpanIncreament,
        baysIncreament: x[0].baysIncreament,
        iterationSteps: x[0].iterationSteps,
        usage: x[0].usage,
        slabThickness: x[0].slabThickness,
        compositeActionFactor: x[0].compositeActionFactor,
      );
    } else {
      steelBeamSchemeInput = SteelBeamSchemeInput();
    }
    return steelBeamSchemeInput;
  }

  Future<void> saveSteelBeamSchemeInput(
    SteelBeamSchemeInput steelBeamSchemeInput,
  ) async {
    final x1 =
        SteelBeamSchemeInputEntity(
          id: '1',
          shortSpan: steelBeamSchemeInput.shortSpan,
          longSpan: steelBeamSchemeInput.longSpan,
          bays: steelBeamSchemeInput.bays,
          strZone: steelBeamSchemeInput.strZone,
          fsy: steelBeamSchemeInput.fsy,
          shortSpanIncreament: steelBeamSchemeInput.shortSpanIncreament,
          longSpanIncreament: steelBeamSchemeInput.longSpanIncreament,
          baysIncreament: steelBeamSchemeInput.baysIncreament,
          iterationSteps: steelBeamSchemeInput.iterationSteps,
          usage: steelBeamSchemeInput.usage,
          slabThickness: steelBeamSchemeInput.slabThickness,
          compositeActionFactor: steelBeamSchemeInput.compositeActionFactor,
        ).toCompanion();
    await transaction(() async {
      await (into(listSteelBeamSchemeInput).insertOnConflictUpdate(x1));
    });
  }

  Future<List<SteelBeamSchemeData>> querySteelBeamSchemeData() async {
    final x = await select(listSteelBeamSchemeData).get();
    final List<SteelBeamSchemeData> finalList = [];
    if (x.isNotEmpty) {
      await Future.forEach(x, (x1) {
        final data = SteelBeamSchemeData(
          usage: x1.usage,
          finish: x1.finish,
          service: x1.service,
          liveLoad: x1.liveLoad,
          loadingTableId: x1.loadingTableId,
          slabThickness: x1.slabThickness,
          compositeActionFactor: x1.compositeActionFactor,
          shortSpan: x1.shortSpan,
          longSpan: x1.longSpan,
          bays: x1.bays,
          strZone: x1.strZone,
          fsy: x1.fsy,
          mainBeamSection: x1.mainBeamSection,
          secBeamSection: x1.secBeamSection,
          steelBeamSchemeId: x1.steelBeamSchemeId,
          calsLog: x1.calsLog,
        );
        finalList.add(data);
      });
    }
    return finalList;
  }

  Future<void> saveSteelBeamSchemeData(
    List<SteelBeamSchemeData> steelBeamSchemeData,
  ) async {
    final List<ListSteelBeamSchemeDataCompanion> finalList = [];
    await Future.forEach(steelBeamSchemeData, (scheme) async {
      final x1 =
          SteelBeamSchemeDataEntity(
            usage: scheme.usage,
            finish: scheme.finish,
            service: scheme.service,
            liveLoad: scheme.liveLoad,
            loadingTableId: scheme.loadingTableId,
            slabThickness: scheme.slabThickness,
            compositeActionFactor: scheme.compositeActionFactor,
            shortSpan: scheme.shortSpan,
            longSpan: scheme.longSpan,
            bays: scheme.bays,
            strZone: scheme.strZone,
            fsy: scheme.fsy,
            mainBeamSection: scheme.mainBeamSection,
            secBeamSection: scheme.secBeamSection,
            steelBeamSchemeId: scheme.steelBeamSchemeId,
            calsLog: scheme.calsLog,
          ).toCompanion();

      finalList.add(x1);
    });
    await transaction(() async {
      await batch((batch) {
        batch.insertAllOnConflictUpdate(listSteelBeamSchemeData, finalList);
      });
    });
    // finally, check if any task exist in database only
    // this tasks need to be deleted as they no longer exist in
    // latest task list

    final Set<String> x2 =
        steelBeamSchemeData.map((scheme) => scheme.steelBeamSchemeId).toSet();
    await transaction(() async {
      await (delete(listSteelBeamSchemeData)
        ..where((tbl) => tbl.steelBeamSchemeId.isNotIn(x2))).go();
    });
  }

  Future<List<SteelColumnSchemeInput>> querySteelColumnSchemeInput() async {
    final x = await select(listSteelColumnSchemeInput).get();
    final List<SteelColumnSchemeInput> finalList = [];
    if (x.isNotEmpty) {
      await Future.forEach(x, (x1) {
        final data = SteelColumnSchemeInput(
          usage: x1.usage,
          slabThickness: x1.slabThickness,
          loadWidth: x1.loadWidth,
          loadLength: x1.loadLength,
          nosOfFloor: x1.nosOfFloor,
          steelColumnSchemeInputId: x1.steelColumnSchemeInputId,
        );
        finalList.add(data);
      });
    }
    return finalList;
  }

  Future<void> saveSteelColumnSchemeInput(
    List<SteelColumnSchemeInput> stealColumnSchemes,
  ) async {
    final List<ListSteelColumnSchemeInputCompanion> finalList = [];
    await Future.forEach(stealColumnSchemes, (scheme) async {
      final x1 =
          SteelColumnSchemeInputEntity(
            usage: scheme.usage,
            slabThickness: scheme.slabThickness,
            loadWidth: scheme.loadWidth,
            loadLength: scheme.loadLength,
            nosOfFloor: scheme.nosOfFloor,
            steelColumnSchemeInputId: scheme.steelColumnSchemeInputId,
          ).toCompanion();

      finalList.add(x1);
    });
    await transaction(() async {
      await batch((batch) {
        batch.insertAllOnConflictUpdate(listSteelColumnSchemeInput, finalList);
      });
    });
    // finally, check if any task exist in database only
    // this tasks need to be deleted as they no longer exist in
    // latest task list
    final Set<String> x2 =
        stealColumnSchemes
            .map((scheme) => scheme.steelColumnSchemeInputId)
            .toSet();
    await transaction(() async {
      await (delete(listSteelColumnSchemeInput)
        ..where((tbl) => tbl.steelColumnSchemeInputId.isNotIn(x2))).go();
    });
  }

  Future<SteelColumnSchemeInputGlobal>
  querySteelColumnSchemeInputGlobal() async {
    final x = await select(listSteelColumnSchemeInputGlobal).get();
    final SteelColumnSchemeInputGlobal steelColumnSchemeInputGlobal;
    if (x.isNotEmpty) {
      steelColumnSchemeInputGlobal = SteelColumnSchemeInputGlobal(
        id: x[0].id,
        steelGrade: x[0].steelGrade,
        unbracedLength: x[0].unbracedLength,
      );
    } else {
      steelColumnSchemeInputGlobal = SteelColumnSchemeInputGlobal();
    }
    return steelColumnSchemeInputGlobal;
  }

  Future<void> saveSteelColumnSchemeInputGlobal(
    SteelColumnSchemeInputGlobal steelColumnSchemeInputGlobal,
  ) async {
    final x1 =
        SteelColumnSchemeInputGlobalEntity(
          id: '1',
          steelGrade: steelColumnSchemeInputGlobal.steelGrade,
          unbracedLength: steelColumnSchemeInputGlobal.unbracedLength,
        ).toCompanion();
    await transaction(() async {
      await (into(listSteelColumnSchemeInputGlobal).insertOnConflictUpdate(x1));
    });
  }

  Future<List<SteelColumnSchemeData>> querySteelColumnSchemeData() async {
    final x = await select(listSteelColumnSchemeData).get();
    final List<SteelColumnSchemeData> finalList = [];
    if (x.isNotEmpty) {
      await Future.forEach(x, (x1) {
        final data = SteelColumnSchemeData(
          steelColumnSchemeDataId: x1.steelColumnSchemeDataId,
          sdl: x1.sdl,
          ll: x1.ll,
          slsLoad: x1.slsLoad,
          ulsLoad: x1.ulsLoad,
          fsy: x1.fsy,
          unbracedLength: x1.unbracedLength,
          axialCapacity: x1.axialCapacity,
          steelSection: x1.steelSection,
          calsLog: x1.calsLog,
        );
        finalList.add(data);
      });
    }
    return finalList;
  }

  Future<void> saveSteelColumnSchemeData(
    List<SteelColumnSchemeData> steelColumnSchemeData,
  ) async {
    final List<ListSteelColumnSchemeDataCompanion> finalList = [];
    await Future.forEach(steelColumnSchemeData, (scheme) async {
      final x1 =
          SteelColumnSchemeDataEntity(
            steelColumnSchemeDataId: scheme.steelColumnSchemeDataId,
            sdl: scheme.sdl,
            ll: scheme.ll,
            slsLoad: scheme.slsLoad,
            ulsLoad: scheme.ulsLoad,
            fsy: scheme.fsy,
            unbracedLength: scheme.unbracedLength,
            axialCapacity: scheme.axialCapacity,
            steelSection: scheme.steelSection,
            calsLog: scheme.calsLog,
          ).toCompanion();

      finalList.add(x1);
    });
    await transaction(() async {
      await batch((batch) {
        batch.insertAllOnConflictUpdate(listSteelColumnSchemeData, finalList);
      });
    });
    // finally, check if any task exist in database only
    // this tasks need to be deleted as they no longer exist in
    // latest task list

    final Set<String> x2 =
        steelColumnSchemeData
            .map((scheme) => scheme.steelColumnSchemeDataId)
            .toSet();
    await transaction(() async {
      await (delete(listSteelColumnSchemeData)
        ..where((tbl) => tbl.steelColumnSchemeDataId.isNotIn(x2))).go();
    });
  }

  Future<List<SteelTransferTrussSchemeInput>>
  querySteelTransferTrussSchemeInput() async {
    final x = await select(listSteelTransferTrussSchemeInput).get();
    final List<SteelTransferTrussSchemeInput> finalList = [];
    if (x.isNotEmpty) {
      await Future.forEach(x, (x1) {
        final data = SteelTransferTrussSchemeInput(
          pointLoad: x1.pointLoad,
          distA: x1.distA,
          steelTransferTrussSchemeInputId: x1.steelTransferTrussSchemeInputId,
        );
        finalList.add(data);
      });
    }
    return finalList;
  }

  Future<void> saveSteelTransferTrussSchemeInput(
    List<SteelTransferTrussSchemeInput> inputs,
  ) async {
    final List<ListSteelTransferTrussSchemeInputCompanion> finalList = [];
    await Future.forEach(inputs, (input) async {
      final x1 =
          SteelTransferTrussSchemeInputEntity(
            pointLoad: input.pointLoad,
            distA: input.distA,
            steelTransferTrussSchemeInputId:
                input.steelTransferTrussSchemeInputId,
          ).toCompanion();

      finalList.add(x1);
    });
    await transaction(() async {
      await batch((batch) {
        batch.insertAllOnConflictUpdate(
          listSteelTransferTrussSchemeInput,
          finalList,
        );
      });
    });
    // finally, check if any task exist in database only
    // this tasks need to be deleted as they no longer exist in
    // latest task list

    final Set<String> x2 =
        inputs.map((input) => input.steelTransferTrussSchemeInputId).toSet();
    await transaction(() async {
      await (delete(listSteelTransferTrussSchemeInput)
        ..where((tbl) => tbl.steelTransferTrussSchemeInputId.isNotIn(x2))).go();
    });
  }

  Future<SteelTransferTrussSchemeInputGlobal>
  querySteelTransferTrussSchemeInputGlobal() async {
    final x = await select(listSteelTransferTrussSchemeInputGlobal).get();
    final SteelTransferTrussSchemeInputGlobal
    steelTransferTrussSchemeInputGlobal;
    if (x.isNotEmpty) {
      steelTransferTrussSchemeInputGlobal = SteelTransferTrussSchemeInputGlobal(
        id: x[0].id,
        span: x[0].span,
        loadWidth: x[0].loadWidth,
        strZone: x[0].strZone,
        fsy: x[0].fsy,
        unbracedLength: x[0].unbracedLength,
        usage: x[0].usage,
        slabThickness: x[0].slabThickness,
      );
    } else {
      steelTransferTrussSchemeInputGlobal =
          SteelTransferTrussSchemeInputGlobal();
    }
    return steelTransferTrussSchemeInputGlobal;
  }

  Future<void> saveSteelTransferTrussSchemeInputGlobal(
    SteelTransferTrussSchemeInputGlobal data,
  ) async {
    final x1 =
        SteelTransferTrussSchemeInputGlobalEntity(
          id: '1',
          span: data.span,
          loadWidth: data.loadWidth,
          strZone: data.strZone,
          fsy: data.fsy,
          unbracedLength: data.unbracedLength,
          usage: data.usage,
          slabThickness: data.slabThickness,
        ).toCompanion();
    await transaction(() async {
      await (into(
        listSteelTransferTrussSchemeInputGlobal,
      ).insertOnConflictUpdate(x1));
    });
  }

  Future<SteelTransferTrussSchemeData>
  querySteelTransferTrussSchemeData() async {
    final x = await select(listSteelTransferTrussSchemeData).get();
    final SteelTransferTrussSchemeData steelTransferTrussSchemeData;
    if (x.isNotEmpty) {
      steelTransferTrussSchemeData = SteelTransferTrussSchemeData(
        usage: x[0].usage,
        slabThickness: x[0].slabThickness,
        span: x[0].span,
        loadWidth: x[0].loadWidth,
        strZone: x[0].strZone,
        fsy: x[0].fsy,
        unbracedLength: x[0].unbracedLength,
        steelSection: x[0].steelSection,
        chordAxialCapacity: x[0].chordAxialCapacity,
        leverArm: x[0].leverArm,
        momentCapacity: x[0].momentCapacity,
        liveLoadDeflection: x[0].liveLoadDeflection,
        id: x[0].id,
        calsLog: x[0].calsLog,
        beamForce: x[0].beamForce,
      );
    } else {
      steelTransferTrussSchemeData = SteelTransferTrussSchemeData();
    }
    return steelTransferTrussSchemeData;
  }

  Future<void> saveSteelTransferTrussSchemeData(
    SteelTransferTrussSchemeData data,
  ) async {
    final x1 =
        SteelTransferTrussSchemeDataEntity(
          id: '1',
          usage: data.usage,
          slabThickness: data.slabThickness,
          span: data.span,
          loadWidth: data.loadWidth,
          strZone: data.strZone,
          fsy: data.fsy,
          unbracedLength: data.unbracedLength,
          steelSection: data.steelSection,
          chordAxialCapacity: data.chordAxialCapacity,
          leverArm: data.leverArm,
          momentCapacity: data.momentCapacity,
          liveLoadDeflection: data.liveLoadDeflection,
          calsLog: data.calsLog,
          beamForce: data.beamForce,
        ).toCompanion();
    await transaction(() async {
      await (into(listSteelTransferTrussSchemeData).insertOnConflictUpdate(x1));
    });
  }

  Future<PileFrictionalBoredInput> queryPileFrictionalBoredInput() async {
    final x = await select(listPileFrictionalBoredInput).get();
    final PileFrictionalBoredInput pileFrictionalBoredInput;
    if (x.isNotEmpty) {
      pileFrictionalBoredInput = PileFrictionalBoredInput(
        id: x[0].id,
        sptNValue: x[0].sptNValue,
        soilUnitWeight: x[0].soilUnitWeight,
        kTan: x[0].kTan,
        fos: x[0].fos,
        fcu: x[0].fcu,
        maxPileLength: x[0].maxPileLength,
        maxPileDiameter: x[0].maxPileDiameter,
        ratioOfBelloutDia: x[0].ratioOfBelloutDia,
        maxSteelRatio: x[0].maxSteelRatio,
        slsLoad: x[0].slsLoad,
        ulsLoad: x[0].ulsLoad,
        diaIncrement: x[0].diaIncrement,
        useSelectColLoad: x[0].useSelectColLoad,
      );
    } else {
      pileFrictionalBoredInput = PileFrictionalBoredInput();
    }
    return pileFrictionalBoredInput;
  }

  Future<void> savePileFrictionalBoredInput(
    PileFrictionalBoredInput data,
  ) async {
    final x1 =
        PileFrictionalBoredInputEntity(
          id: '1',
          sptNValue: data.sptNValue,
          soilUnitWeight: data.soilUnitWeight,
          kTan: data.kTan,
          fos: data.fos,
          fcu: data.fcu,
          maxPileLength: data.maxPileLength,
          maxPileDiameter: data.maxPileDiameter,
          ratioOfBelloutDia: data.ratioOfBelloutDia,
          maxSteelRatio: data.maxSteelRatio,
          slsLoad: data.slsLoad,
          ulsLoad: data.ulsLoad,
          diaIncrement: data.diaIncrement,
          useSelectColLoad: data.useSelectColLoad,
        ).toCompanion();
    await transaction(() async {
      await (into(listPileFrictionalBoredInput).insertOnConflictUpdate(x1));
    });
  }

  Future<List<PileFrictionalBoredData>> queryPileFrictionalBoredData() async {
    final x = await select(listPileFrictionalBoredData).get();
    final List<PileFrictionalBoredData> finalList = [];
    if (x.isNotEmpty) {
      await Future.forEach(x, (x1) {
        final data = PileFrictionalBoredData(
          sptNValue: x1.sptNValue,
          soilUnitWeight: x1.soilUnitWeight,
          kTan: x1.kTan,
          fos: x1.fos,
          fcu: x1.fcu,
          maxPileLength: x1.maxPileLength,
          maxPileDiameter: x1.maxPileDiameter,
          maxSteelRatio: x1.maxSteelRatio,
          slsLoad: x1.slsLoad,
          ulsLoad: x1.ulsLoad,
          diameter: x1.diameter,
          ratioOfBelloutDia: x1.ratioOfBelloutDia,
          length: x1.length,
          shaftCapacity: x1.shaftCapacity,
          baseCapacity: x1.baseCapacity,
          totalGroundResistance: x1.totalGroundResistance,
          strCapacity: x1.strCapacity,
          rebar: x1.rebar,
          steelRatio: x1.steelRatio,
          isSelected: x1.isSelected,
          calsLog: x1.calsLog,
          pileFrictionalBoredSchemeId: x1.pileFrictionalBoredSchemeId,
        );
        finalList.add(data);
      });
    }
    return finalList;
  }

  Future<void> savePileFrictionalBoredData(
    List<PileFrictionalBoredData> pileFrictionalBoredData,
  ) async {
    final List<ListPileFrictionalBoredDataCompanion> finalList = [];
    await Future.forEach(pileFrictionalBoredData, (data) async {
      final x1 =
          PileFrictionalBoredDataEntity(
            sptNValue: data.sptNValue,
            soilUnitWeight: data.soilUnitWeight,
            kTan: data.kTan,
            fos: data.fos,
            fcu: data.fcu,
            maxPileLength: data.maxPileLength,
            maxPileDiameter: data.maxPileDiameter,
            ratioOfBelloutDia: data.ratioOfBelloutDia,
            maxSteelRatio: data.maxSteelRatio,
            slsLoad: data.slsLoad,
            ulsLoad: data.ulsLoad,
            diameter: data.diameter,
            length: data.length,
            shaftCapacity: data.shaftCapacity,
            baseCapacity: data.baseCapacity,
            totalGroundResistance: data.totalGroundResistance,
            strCapacity: data.strCapacity,
            rebar: data.rebar,
            steelRatio: data.steelRatio,
            isSelected: data.isSelected,
            calsLog: data.calsLog,
            pileFrictionalBoredSchemeId: data.pileFrictionalBoredSchemeId,
          ).toCompanion();

      finalList.add(x1);
    });
    await transaction(() async {
      await batch((batch) {
        batch.insertAllOnConflictUpdate(listPileFrictionalBoredData, finalList);
      });
    });
    // finally, check if any task exist in database only
    // this tasks need to be deleted as they no longer exist in
    // latest task list

    final Set<String> x2 =
        pileFrictionalBoredData
            .map((data) => data.pileFrictionalBoredSchemeId)
            .toSet();
    await transaction(() async {
      await (delete(listPileFrictionalBoredData)
        ..where((tbl) => tbl.pileFrictionalBoredSchemeId.isNotIn(x2))).go();
    });
  }

  Future<PileSocketedData> queryPileSocketedData() async {
    final x = await select(listPileSocketedData).get();
    final PileSocketedData pileSocketedData;
    if (x.isNotEmpty) {
      pileSocketedData = PileSocketedData(
        isSelected: x[0].isSelected,
        pileSocketedSchemeId: x[0].pileSocketedSchemeId,
      );
    } else {
      pileSocketedData = PileSocketedData();
    }
    return pileSocketedData;
  }

  Future<void> savePileSocketedData(PileSocketedData pileSocketedData) async {
    final x1 =
        PileSocketedDataEntity(
          isSelected: pileSocketedData.isSelected,
          pileSocketedSchemeId: pileSocketedData.pileSocketedSchemeId,
        ).toCompanion();
    await transaction(() async {
      await (into(listPileSocketedData).insertOnConflictUpdate(x1));
    });
  }

  Future<PileDrivenInput> queryPileDrivenInput() async {
    final x = await select(listPileDrivenInput).get();
    final PileDrivenInput pileDrivenInput;
    if (x.isNotEmpty) {
      pileDrivenInput = PileDrivenInput(
        sptNValue: x[0].sptNValue,
        fos: x[0].fos,
        maxPileLength: x[0].maxPileLength,
        slsLoad: x[0].slsLoad,
        ulsLoad: x[0].ulsLoad,
        useSelectColLoad: x[0].useSelectColLoad,
        id: x[0].id,
      );
    } else {
      pileDrivenInput = PileDrivenInput();
    }
    return pileDrivenInput;
  }

  Future<void> savePileDrivenInput(PileDrivenInput pileDrivenInput) async {
    final x1 =
        PileDrivenInputEntity(
          sptNValue: pileDrivenInput.sptNValue,
          fos: pileDrivenInput.fos,
          maxPileLength: pileDrivenInput.maxPileLength,
          slsLoad: pileDrivenInput.slsLoad,
          ulsLoad: pileDrivenInput.ulsLoad,
          useSelectColLoad: pileDrivenInput.useSelectColLoad,
          id: pileDrivenInput.id,
        ).toCompanion();
    await transaction(() async {
      await (into(listPileDrivenInput).insertOnConflictUpdate(x1));
    });
  }

  Future<List<PileDrivenData>> queryPileDrivenData() async {
    final x = await select(listPileDrivenData).get();
    final List<PileDrivenData> finalList = [];
    if (x.isNotEmpty) {
      await Future.forEach(x, (x1) {
        final data = PileDrivenData(
          sptNValue: x1.sptNValue,
          fos: x1.fos,
          maxPileLength: x1.maxPileLength,
          slsLoad: x1.slsLoad,
          ulsLoad: x1.ulsLoad,
          section: x1.section,
          length: x1.length,
          shaftCapacity: x1.shaftCapacity,
          strSLSCapacuity: x1.strSLSCapacuity,
          totalGroundResistance: x1.totalGroundResistance,
          strULSCapacity: x1.strULSCapacity,
          isSelected: x1.isSelected,
          calsLog: x1.calsLog,
          pileDrivenSchemeId: x1.pileDrivenSchemeId,
        );
        finalList.add(data);
      });
    }
    return finalList;
  }

  Future<void> savePileDrivenData(List<PileDrivenData> pileDrivenData) async {
    final List<ListPileDrivenDataCompanion> finalList = [];
    await Future.forEach(pileDrivenData, (data) async {
      final x1 =
          PileDrivenDataEntity(
            sptNValue: data.sptNValue,
            fos: data.fos,
            maxPileLength: data.maxPileLength,
            slsLoad: data.slsLoad,
            ulsLoad: data.ulsLoad,
            section: data.section,
            length: data.length,
            shaftCapacity: data.shaftCapacity,
            strSLSCapacuity: data.strSLSCapacuity,
            totalGroundResistance: data.totalGroundResistance,
            strULSCapacity: data.strULSCapacity,
            isSelected: data.isSelected,
            calsLog: data.calsLog,
            pileDrivenSchemeId: data.pileDrivenSchemeId,
          ).toCompanion();

      finalList.add(x1);
    });
    await transaction(() async {
      await batch((batch) {
        batch.insertAllOnConflictUpdate(listPileDrivenData, finalList);
      });
    });
    // finally, check if any task exist in database only
    // this tasks need to be deleted as they no longer exist in
    // latest task list

    final Set<String> x2 =
        pileDrivenData.map((data) => data.pileDrivenSchemeId).toSet();
    await transaction(() async {
      await (delete(listPileDrivenData)
        ..where((tbl) => tbl.pileDrivenSchemeId.isNotIn(x2))).go();
    });
  }

  Future<FootingSchemeInput> queryFootingSchemeInput() async {
    final x = await select(listFootingSchemeInput).get();
    final FootingSchemeInput footingSchemeInput;
    if (x.isNotEmpty) {
      footingSchemeInput = FootingSchemeInput(
        id: x[0].id,
        fcu: x[0].fcu,
        cover: x[0].cover,
        mainKValue: x[0].mainKValue,
        mainSteelRatio: x[0].mainSteelRatio,
        minS: x[0].minS,
        maxS: x[0].maxS,
        maxDepth: x[0].maxDepth,
        minDepth: x[0].minDepth,
        maxLayers: x[0].maxLayers,
        groundType: x[0].groundType,
        soilNValue: x[0].soilNValue,
        footingTopLevel: x[0].footingTopLevel,
        waterTableLevel: x[0].waterTableLevel,
        rockCapacity: x[0].rockCapacity,
        colShape: x[0].colShape,
        columnSize: x[0].columnSize,
        slsLoad: x[0].slsLoad,
        ulsLoad: x[0].ulsLoad,
        useSelectColLoad: x[0].useSelectColLoad,
      );
    } else {
      footingSchemeInput = FootingSchemeInput();
    }
    return footingSchemeInput;
  }

  Future<void> saveFootingSchemeInput(
    FootingSchemeInput footingSchemeInput,
  ) async {
    final x1 =
        FootingSchemeInputEntity(
          id: footingSchemeInput.id,
          fcu: footingSchemeInput.fcu,
          cover: footingSchemeInput.cover,
          mainKValue: footingSchemeInput.mainKValue,
          mainSteelRatio: footingSchemeInput.mainSteelRatio,
          minS: footingSchemeInput.minS,
          maxS: footingSchemeInput.maxS,
          maxDepth: footingSchemeInput.maxDepth,
          minDepth: footingSchemeInput.minDepth,
          maxLayers: footingSchemeInput.maxLayers,
          groundType: footingSchemeInput.groundType,
          soilNValue: footingSchemeInput.soilNValue,
          footingTopLevel: footingSchemeInput.footingTopLevel,
          waterTableLevel: footingSchemeInput.waterTableLevel,
          rockCapacity: footingSchemeInput.rockCapacity,
          colShape: footingSchemeInput.colShape,
          columnSize: footingSchemeInput.columnSize,
          slsLoad: footingSchemeInput.slsLoad,
          ulsLoad: footingSchemeInput.ulsLoad,
          useSelectColLoad: footingSchemeInput.useSelectColLoad,
        ).toCompanion();
    await transaction(() async {
      await (into(listFootingSchemeInput).insertOnConflictUpdate(x1));
    });
  }

  Future<List<FootingSchemeData>> queryFootingSchemeData() async {
    final x = await select(listFootingSchemeData).get();
    final List<FootingSchemeData> finalList = [];
    if (x.isNotEmpty) {
      await Future.forEach(x, (x1) {
        final data = FootingSchemeData(
          size: x1.size,
          strZone: x1.strZone,
          fcu: x1.fcu,
          cover: x1.cover,
          groundType: x1.groundType,
          soilNValue: x1.soilNValue,
          footingTopLevel: x1.footingTopLevel,
          waterTableLevel: x1.waterTableLevel,
          rockCapacity: x1.rockCapacity,
          colShape: x1.colShape,
          columnSize: x1.columnSize,
          slsLoad: x1.slsLoad,
          ulsLoad: x1.ulsLoad,
          mainTopBar: x1.mainTopBar,
          mainBottomBar: x1.mainBottomBar,
          mainLinks: x1.mainLinks,
          punchingLinks: x1.punchingLinks,
          calsLog: x1.calsLog,
          isSelected: x1.isSelected,
          footingSchemeDataId: x1.footingSchemeDataId,
        );
        finalList.add(data);
      });
    }
    return finalList;
  }

  Future<void> saveFootingSchemeData(
    List<FootingSchemeData> footingSchemeData,
  ) async {
    final List<ListFootingSchemeDataCompanion> finalList = [];
    await Future.forEach(footingSchemeData, (data) async {
      final x1 =
          FootingSchemeDataEntity(
            size: data.size,
            strZone: data.strZone,
            fcu: data.fcu,
            cover: data.cover,
            groundType: data.groundType,
            soilNValue: data.soilNValue,
            footingTopLevel: data.footingTopLevel,
            waterTableLevel: data.waterTableLevel,
            rockCapacity: data.rockCapacity,
            colShape: data.colShape,
            columnSize: data.columnSize,
            slsLoad: data.slsLoad,
            ulsLoad: data.ulsLoad,
            mainTopBar: data.mainTopBar,
            mainBottomBar: data.mainBottomBar,
            mainLinks: data.mainLinks,
            punchingLinks: data.punchingLinks,
            calsLog: data.calsLog,
            isSelected: data.isSelected,
            footingSchemeDataId: data.footingSchemeDataId,
          ).toCompanion();

      finalList.add(x1);
    });
    await transaction(() async {
      await batch((batch) {
        batch.insertAllOnConflictUpdate(listFootingSchemeData, finalList);
      });
    });
    // finally, check if any task exist in database only
    // this tasks need to be deleted as they no longer exist in
    // latest task list

    final Set<String> x2 =
        footingSchemeData.map((data) => data.footingSchemeDataId).toSet();
    await transaction(() async {
      await (delete(listFootingSchemeData)
        ..where((tbl) => tbl.footingSchemeDataId.isNotIn(x2))).go();
    });
  }

  Future<List<StrZoneTable>> queryStrZoneTables() async {
    final x = await select(listStrZoneTable).get();
    final List<StrZoneTable> finalList = [];
    if (x.isNotEmpty) {
      await Future.forEach(x, (x1) {
        final data = StrZoneTable(
          floor: x1.floor,
          height: x1.height,
          finish: x1.finish,
          service: x1.service,
          clear: x1.clear,
          nosOfFloor: x1.nosOfFloor,
          strZoneId: x1.strZoneId,
        );
        finalList.add(data);
      });
    }
    return finalList;
  }

  Future<void> saveStrZoneTables(List<StrZoneTable> strZoneTable) async {
    final List<ListStrZoneTableCompanion> finalList = [];
    await Future.forEach(strZoneTable, (data) async {
      final x1 =
          StrZoneTableEntity(
            strZoneId: data.strZoneId,
            floor: data.floor,
            height: data.height,
            finish: data.finish,
            service: data.service,
            clear: data.clear,
            nosOfFloor: data.nosOfFloor,
          ).toCompanion();
      finalList.add(x1);
    });
    await transaction(() async {
      await batch((batch) {
        batch.insertAllOnConflictUpdate(listStrZoneTable, finalList);
      });
    });
    // finally, check if any task exist in database only
    // this tasks need to be deleted as they no longer exist in
    // latest task list

    final Set<String> x2 = strZoneTable.map((data) => data.floor).toSet();
    await transaction(() async {
      await (delete(listStrZoneTable)
        ..where((tbl) => tbl.floor.isNotIn(x2))).go();
    });
  }

  Future<void> saveRecommendedLoad(
    List<RecommendedLoad> recommendedLoad,
  ) async {
    final List<ListRecommendedLoadCompanion> finalList = [];
    await Future.forEach(recommendedLoad, (data) async {
      final x1 =
          RecommendedLoadEntity(
            usage: data.usage,
            finish: data.finish,
            service: data.service,
            partitionLoad: data.partitionLoad,
            sdl: data.sdl,
            ll: data.ll,
            frp: data.frp,
          ).toCompanion();
      finalList.add(x1);
    });
    await transaction(() async {
      await batch((batch) {
        batch.insertAllOnConflictUpdate(listRecommendedLoad, finalList);
      });
    });
  }

  Future<PileEndBearingBoredInput> queryPileEndBearingBoredInput() async {
    final x = await select(listPileEndBearingBoredInput).get();
    final PileEndBearingBoredInput pileEndBearingBoredInput;
    if (x.isNotEmpty) {
      pileEndBearingBoredInput = PileEndBearingBoredInput(
        safeBearing: x[0].safeBearing,
        fos: x[0].fos,
        fcu: x[0].fcu,
        maxPileDiameter: x[0].maxPileDiameter,
        ratioOfBelloutDia: x[0].ratioOfBelloutDia,
        maxSteelRatio: x[0].maxSteelRatio,
        slsLoad: x[0].slsLoad,
        ulsLoad: x[0].ulsLoad,
        diaIncrement: x[0].diaIncrement,
        useSelectColLoad: x[0].useSelectColLoad,
        id: x[0].id,
      );
    } else {
      pileEndBearingBoredInput = PileEndBearingBoredInput();
    }
    return pileEndBearingBoredInput;
  }

  Future<void> savePileEndBearingBoredInput(
    PileEndBearingBoredInput pileEndBearingBoredInput,
  ) async {
    final x1 =
        PileEndBearingBoredInputEntity(
          safeBearing: pileEndBearingBoredInput.safeBearing,
          fos: pileEndBearingBoredInput.fos,
          fcu: pileEndBearingBoredInput.fcu,
          maxPileDiameter: pileEndBearingBoredInput.maxPileDiameter,
          ratioOfBelloutDia: pileEndBearingBoredInput.ratioOfBelloutDia,
          maxSteelRatio: pileEndBearingBoredInput.maxSteelRatio,
          slsLoad: pileEndBearingBoredInput.slsLoad,
          ulsLoad: pileEndBearingBoredInput.ulsLoad,
          diaIncrement: pileEndBearingBoredInput.diaIncrement,
          useSelectColLoad: pileEndBearingBoredInput.useSelectColLoad,
          id: pileEndBearingBoredInput.id,
        ).toCompanion();
    await transaction(() async {
      await (into(listPileEndBearingBoredInput).insertOnConflictUpdate(x1));
    });
  }

  Future<List<PileEndBearingBoredData>> queryPileEndBearingBoredData() async {
    final x = await select(listPileEndBearingBoredData).get();
    final List<PileEndBearingBoredData> finalList = [];
    if (x.isNotEmpty) {
      await Future.forEach(x, (x1) {
        finalList.add(
          PileEndBearingBoredData(
            safeBearing: x1.safeBearing,
            fos: x1.fos,
            fcu: x1.fcu,
            maxPileDiameter: x1.maxPileDiameter,
            maxSteelRatio: x1.maxSteelRatio,
            slsLoad: x1.slsLoad,
            ulsLoad: x1.ulsLoad,
            diameter: x1.diameter,
            ratioOfBelloutDia: x1.ratioOfBelloutDia,
            length: x1.length,
            baseCapacity: x1.baseCapacity,
            totalGroundResistance: x1.totalGroundResistance,
            strCapacity: x1.strCapacity,
            rebar: x1.rebar,
            steelRatio: x1.steelRatio,
            isSelected: x1.isSelected,
            calsLog: x1.calsLog,
            pileEndBearingBoredSchemeId: x1.pileEndBearingBoredSchemeId,
          ),
        );
      });
    }
    return finalList;
  }

  Future<void> savePileEndBearingBoredData(
    List<PileEndBearingBoredData> pileEndBearingBoredData,
  ) async {
    final List<ListPileEndBearingBoredDataCompanion> finalList = [];
    await Future.forEach(pileEndBearingBoredData, (data) async {
      final x1 =
          PileEndBearingBoredDataEntity(
            safeBearing: data.safeBearing,
            fos: data.fos,
            fcu: data.fcu,
            maxPileDiameter: data.maxPileDiameter,
            maxSteelRatio: data.maxSteelRatio,
            slsLoad: data.slsLoad,
            ulsLoad: data.ulsLoad,
            diameter: data.diameter,
            ratioOfBelloutDia: data.ratioOfBelloutDia,
            length: data.length,
            baseCapacity: data.baseCapacity,
            totalGroundResistance: data.totalGroundResistance,
            strCapacity: data.strCapacity,
            rebar: data.rebar,
            steelRatio: data.steelRatio,
            isSelected: data.isSelected,
            calsLog: data.calsLog,
            pileEndBearingBoredSchemeId: data.pileEndBearingBoredSchemeId,
          ).toCompanion();
      finalList.add(x1);
      await transaction(() async {
        await batch((batch) {
          batch.insertAllOnConflictUpdate(
            listPileEndBearingBoredData,
            finalList,
          );
        });
      });
      // finally, check if any task exist in database only
      // this tasks need to be deleted as they no longer exist in
      // latest task list

      final Set<String> x2 =
          pileEndBearingBoredData
              .map((data) => data.pileEndBearingBoredSchemeId)
              .toSet();
      await transaction(() async {
        await (delete(listPileEndBearingBoredData)
          ..where((tbl) => tbl.pileEndBearingBoredSchemeId.isNotIn(x2))).go();
      });
    });
  }

  Future<PileEndBearingBoredInputGlobal>
  queryPileEndBearingBoredInputGlobal() async {
    final x = await select(listPileEndBearingBoredInputGlobal).get();
    final PileEndBearingBoredInputGlobal pileEndBearingBoredInputGlobal;
    if (x.isNotEmpty) {
      pileEndBearingBoredInputGlobal = PileEndBearingBoredInputGlobal(
        colLoadFactor: x[0].colLoadFactor,
        id: x[0].id,
      );
    } else {
      pileEndBearingBoredInputGlobal = PileEndBearingBoredInputGlobal();
    }
    return pileEndBearingBoredInputGlobal;
  }

  Future<void> savePileEndBearingBoredInputGlobal(
    PileEndBearingBoredInputGlobal pileEndBearingBoredInputGlobal,
  ) async {
    final x1 =
        PileEndBearingBoredInputGlobalEntity(
          colLoadFactor: pileEndBearingBoredInputGlobal.colLoadFactor,
          id: pileEndBearingBoredInputGlobal.id,
        ).toCompanion();
    await transaction(() async {
      await (into(
        listPileEndBearingBoredInputGlobal,
      ).insertOnConflictUpdate(x1));
    });
  }

  Future<PileFrictionalBoredInputGlobal>
  queryPileFrictionalBoredInputGlobal() async {
    final x = await select(listPileFrictionalBoredInputGlobal).get();
    final PileFrictionalBoredInputGlobal pileFrictionalBoredInputGlobal;
    if (x.isNotEmpty) {
      pileFrictionalBoredInputGlobal = PileFrictionalBoredInputGlobal(
        colLoadFactor: x[0].colLoadFactor,
        id: x[0].id,
      );
    } else {
      pileFrictionalBoredInputGlobal = PileFrictionalBoredInputGlobal();
    }
    return pileFrictionalBoredInputGlobal;
  }

  Future<void> savePileFrictionalBoredInputGlobal(
    PileFrictionalBoredInputGlobal pileFrictionalBoredInputGlobal,
  ) async {
    final x1 =
        PileFrictionalBoredInputGlobalEntity(
          colLoadFactor: pileFrictionalBoredInputGlobal.colLoadFactor,
          id: pileFrictionalBoredInputGlobal.id,
        ).toCompanion();
    await transaction(() async {
      await (into(
        listPileFrictionalBoredInputGlobal,
      ).insertOnConflictUpdate(x1));
    });
  }

  Future<PileDrivenInputGlobal> queryPileDrivenInputGlobal() async {
    final x = await select(listPileDrivenInputGlobal).get();

    final PileDrivenInputGlobal pileDrivenInputGlobal;
    if (x.isNotEmpty) {
      pileDrivenInputGlobal = PileDrivenInputGlobal(
        colLoadFactor: x[0].colLoadFactor,
        id: x[0].id,
      );
    } else {
      pileDrivenInputGlobal = PileDrivenInputGlobal();
    }
    return pileDrivenInputGlobal;
  }

  Future<void> savePileDrivenInputGlobal(
    PileDrivenInputGlobal pileDrivenInputGlobal,
  ) async {
    final x1 =
        PileDrivenInputGlobalEntity(
          colLoadFactor: pileDrivenInputGlobal.colLoadFactor,
          id: pileDrivenInputGlobal.id,
        ).toCompanion();
    await transaction(() async {
      await (into(listPileDrivenInputGlobal).insertOnConflictUpdate(x1));
    });
  }

  Future<FootingSchemeInputGlobal> queryFootingSchemeInputGlobal() async {
    final x = await select(listFootingSchemeInputGlobal).get();
    final FootingSchemeInputGlobal footingSchemeInputGlobal;
    if (x.isNotEmpty) {
      footingSchemeInputGlobal = FootingSchemeInputGlobal(
        colLoadFactor: x[0].colLoadFactor,
        id: x[0].id,
      );
    } else {
      footingSchemeInputGlobal = FootingSchemeInputGlobal();
    }
    return footingSchemeInputGlobal;
  }

  Future<void> saveFootingSchemeInputGlobal(
    FootingSchemeInputGlobal footingSchemeInputGlobal,
  ) async {
    final x1 =
        FootingSchemeInputGlobalEntity(
          colLoadFactor: footingSchemeInputGlobal.colLoadFactor,
          id: footingSchemeInputGlobal.id,
        ).toCompanion();
    await transaction(() async {
      await (into(listFootingSchemeInputGlobal).insertOnConflictUpdate(x1));
    });
  }

  Future<CantileverSchemeInputGlobal> queryCantileverSchemeInputGlobal() async {
    final x = await select(listCantileverSchemeInputGlobal).get();
    final CantileverSchemeInputGlobal cantileverSchemeInputGlobal;
    if (x.isNotEmpty) {
      cantileverSchemeInputGlobal = CantileverSchemeInputGlobal(
        id: x[0].id,
        span: x[0].span,
        loadWidth: x[0].loadWidth,
        strZone: x[0].strZone,
        fcu: x[0].fcu,
        cover: x[0].cover,
        mainKValue: x[0].mainKValue,
        mainSteelRatio: x[0].mainSteelRatio,
        minS: x[0].minS,
        maxS: x[0].maxS,
        maxWidth: x[0].maxWidth,
        maxLayers: x[0].maxLayers,
        usage: x[0].usage,
        slabThickness: x[0].slabThickness,
        useSlabSelected: x[0].useSlabSelected,
      );
    } else {
      cantileverSchemeInputGlobal = CantileverSchemeInputGlobal();
    }
    return cantileverSchemeInputGlobal;
  }

  Future<void> saveCantileverSchemeInputGlobal(
    CantileverSchemeInputGlobal cantileverSchemeInputGlobal,
  ) async {
    final x1 =
        CantileverSchemeInputGlobalEntity(
          id: '1',
          span: cantileverSchemeInputGlobal.span,
          loadWidth: cantileverSchemeInputGlobal.loadWidth,
          strZone: cantileverSchemeInputGlobal.strZone,
          fcu: cantileverSchemeInputGlobal.fcu,
          cover: cantileverSchemeInputGlobal.cover,
          mainKValue: cantileverSchemeInputGlobal.mainKValue,
          mainSteelRatio: cantileverSchemeInputGlobal.mainSteelRatio,
          minS: cantileverSchemeInputGlobal.minS,
          maxS: cantileverSchemeInputGlobal.maxS,
          maxWidth: cantileverSchemeInputGlobal.maxWidth,
          maxLayers: cantileverSchemeInputGlobal.maxLayers,
          usage: cantileverSchemeInputGlobal.usage,
          slabThickness: cantileverSchemeInputGlobal.slabThickness,
          useSlabSelected: cantileverSchemeInputGlobal.useSlabSelected,
        ).toCompanion();
    await transaction(() async {
      await (into(listCantileverSchemeInputGlobal).insertOnConflictUpdate(x1));
    });
  }

  Future<List<CantileverSchemeInput>> queryCantileverSchemeInput() async {
    final x = await select(listCantileverSchemeInput).get();
    final List<CantileverSchemeInput> finalList = [];
    if (x.isNotEmpty) {
      await Future.forEach(x, (x1) {
        final data = CantileverSchemeInput(
          pointLoad: x1.pointLoad,
          distA: x1.distA,
          loadFromSelectedCol: x1.loadFromSelectedCol,
          cantileverSchemeInputId: x1.cantileverSchemeInputId,
        );
        finalList.add(data);
      });
    }
    return finalList;
  }

  Future<void> saveCantileverSchemeInput(
    List<CantileverSchemeInput> cantileverSchemeInput,
  ) async {
    if (cantileverSchemeInput.isNotEmpty) {
      List<ListCantileverSchemeInputCompanion> rows = [];
      await Future.forEach(cantileverSchemeInput, (input) async {
        final x1 =
            CantileverSchemeInputEntity(
              pointLoad: input.pointLoad,
              distA: input.distA,
              loadFromSelectedCol: input.loadFromSelectedCol,
              cantileverSchemeInputId: input.cantileverSchemeInputId,
            ).toCompanion();
        rows.add(x1);
      });
      await transaction(() async {
        await batch((batch) {
          batch.insertAllOnConflictUpdate(listCantileverSchemeInput, rows);
        });
      });
      // finally, check if any task exist in database only
      // this tasks need to be deleted as they no longer exist in
      // latest task list

      final Set<String> x =
          cantileverSchemeInput.map((x) => x.cantileverSchemeInputId).toSet();
      await transaction(() async {
        await (delete(listCantileverSchemeInput)
          ..where((tbl) => tbl.cantileverSchemeInputId.isNotIn(x))).go();
      });
    }
  }

  Future<CantileverSchemeData> queryCantileverSchemeData() async {
    final x = await select(listCantileverSchemeData).get();
    final CantileverSchemeData cantileverSchemeData;
    if (x.isNotEmpty) {
      cantileverSchemeData = CantileverSchemeData(
        usage: x[0].usage,
        slabThickness: x[0].slabThickness,
        span: x[0].span,
        loadWidth: x[0].loadWidth,
        strZone: x[0].strZone,
        fcu: x[0].fcu,
        cover: x[0].cover,
        mainWidth: x[0].mainWidth,
        mainTopBar: x[0].mainTopBar,
        mainBottomBar: x[0].mainBottomBar,
        mainLinks: x[0].mainLinks,
        id: x[0].id,
        calsLog: x[0].calsLog,
        beamForce: x[0].beamForce,
      );
    } else {
      cantileverSchemeData = CantileverSchemeData();
    }
    return cantileverSchemeData;
  }

  Future<void> saveCantileverSchemeData(
    CantileverSchemeData cantileverSchemeData,
  ) async {
    final x1 =
        CantileverSchemeDataEntity(
          usage: cantileverSchemeData.usage,
          slabThickness: cantileverSchemeData.slabThickness,
          span: cantileverSchemeData.span,
          loadWidth: cantileverSchemeData.loadWidth,
          strZone: cantileverSchemeData.strZone,
          fcu: cantileverSchemeData.fcu,
          cover: cantileverSchemeData.cover,
          mainWidth: cantileverSchemeData.mainWidth,
          mainTopBar: cantileverSchemeData.mainTopBar,
          mainBottomBar: cantileverSchemeData.mainBottomBar,
          mainLinks: cantileverSchemeData.mainLinks,
          id: cantileverSchemeData.id,
          calsLog: cantileverSchemeData.calsLog,
          beamForce: cantileverSchemeData.beamForce,
        ).toCompanion();
    await transaction(() async {
      await (into(listCantileverSchemeData).insertOnConflictUpdate(x1));
    });
  }

  Future<SteelCantileverTrussSchemeInputGlobal>
  querySteelCantileverTrussSchemeInputGlobal() async {
    final x = await select(listSteelCantileverTrussSchemeInputGlobal).get();
    final SteelCantileverTrussSchemeInputGlobal
    steelCantileverTrussSchemeInputGlobal;
    if (x.isNotEmpty) {
      steelCantileverTrussSchemeInputGlobal =
          SteelCantileverTrussSchemeInputGlobal(
            id: x[0].id,
            span: x[0].span,
            loadWidth: x[0].loadWidth,
            strZone: x[0].strZone,
            fsy: x[0].fsy,
            unbracedLength: x[0].unbracedLength,
            usage: x[0].usage,
            slabThickness: x[0].slabThickness,
          );
    } else {
      steelCantileverTrussSchemeInputGlobal =
          SteelCantileverTrussSchemeInputGlobal();
    }
    return steelCantileverTrussSchemeInputGlobal;
  }

  Future<void> saveSteelCantileverTrussSchemeInputGlobal(
    SteelCantileverTrussSchemeInputGlobal steelCantileverTrussSchemeInputGlobal,
  ) async {
    final x1 =
        SteelCantileverTrussSchemeInputGlobalEntity(
          id: '1',
          span: steelCantileverTrussSchemeInputGlobal.span,
          loadWidth: steelCantileverTrussSchemeInputGlobal.loadWidth,
          strZone: steelCantileverTrussSchemeInputGlobal.strZone,
          fsy: steelCantileverTrussSchemeInputGlobal.fsy,
          unbracedLength: steelCantileverTrussSchemeInputGlobal.unbracedLength,
          usage: steelCantileverTrussSchemeInputGlobal.usage,
          slabThickness: steelCantileverTrussSchemeInputGlobal.slabThickness,
        ).toCompanion();
    await transaction(() async {
      await (into(
        listSteelCantileverTrussSchemeInputGlobal,
      ).insertOnConflictUpdate(x1));
    });
  }

  Future<List<SteelCantileverTrussSchemeInput>>
  querySteelCantileverTrussSchemeInput() async {
    final x = await select(listSteelCantileverTrussSchemeInput).get();
    final List<SteelCantileverTrussSchemeInput> finalList = [];
    if (x.isNotEmpty) {
      await Future.forEach(x, (x1) {
        final data = SteelCantileverTrussSchemeInput(
          pointLoad: x1.pointLoad,
          distA: x1.distA,
          steelCantileverTrussSchemeInputId:
              x1.steelCantileverTrussSchemeInputId,
        );
        finalList.add(data);
      });
    }
    return finalList;
  }

  Future<void> saveSteelCantileverTrussSchemeInput(
    List<SteelCantileverTrussSchemeInput> steelCantileverTrussSchemeInput,
  ) async {
    if (steelCantileverTrussSchemeInput.isNotEmpty) {
      List<ListSteelCantileverTrussSchemeInputCompanion> rows = [];
      await Future.forEach(steelCantileverTrussSchemeInput, (input) async {
        final x1 =
            SteelCantileverTrussSchemeInputEntity(
              pointLoad: input.pointLoad,
              distA: input.distA,
              steelCantileverTrussSchemeInputId:
                  input.steelCantileverTrussSchemeInputId,
            ).toCompanion();
        rows.add(x1);
      });
      await transaction(() async {
        await batch((batch) {
          batch.insertAllOnConflictUpdate(
            listSteelCantileverTrussSchemeInput,
            rows,
          );
        });
      });

      // finally, check if any task exist in database only
      // this tasks need to be deleted as they no longer exist in
      // latest task list

      final Set<String> x =
          steelCantileverTrussSchemeInput
              .map((x) => x.steelCantileverTrussSchemeInputId)
              .toSet();
      await transaction(() async {
        await (delete(listSteelCantileverTrussSchemeInput)..where(
          (tbl) => tbl.steelCantileverTrussSchemeInputId.isNotIn(x),
        )).go();
      });
    }
  }

  Future<SteelCantileverTrussSchemeData>
  querySteelCantileverTrussSchemeData() async {
    final x = await select(listSteelCantileverTrussSchemeData).get();
    final SteelCantileverTrussSchemeData steelCantileverTrussSchemeData;
    if (x.isNotEmpty) {
      steelCantileverTrussSchemeData = SteelCantileverTrussSchemeData(
        usage: x[0].usage,
        slabThickness: x[0].slabThickness,
        span: x[0].span,
        loadWidth: x[0].loadWidth,
        strZone: x[0].strZone,
        fsy: x[0].fsy,
        unbracedLength: x[0].unbracedLength,
        steelSection: x[0].steelSection,
        chordAxialCapacity: x[0].chordAxialCapacity,
        leverArm: x[0].leverArm,
        momentCapacity: x[0].momentCapacity,
        liveLoadDeflection: x[0].liveLoadDeflection,
        id: x[0].id,
        calsLog: x[0].calsLog,
        beamForce: x[0].beamForce,
      );
    } else {
      steelCantileverTrussSchemeData = SteelCantileverTrussSchemeData();
    }
    return steelCantileverTrussSchemeData;
  }

  Future<void> saveSteelCantileverTrussSchemeData(
    SteelCantileverTrussSchemeData steelCantileverTrussSchemeData,
  ) async {
    final x1 =
        SteelCantileverTrussSchemeDataEntity(
          id: '1',
          usage: steelCantileverTrussSchemeData.usage,
          slabThickness: steelCantileverTrussSchemeData.slabThickness,
          span: steelCantileverTrussSchemeData.span,
          loadWidth: steelCantileverTrussSchemeData.loadWidth,
          strZone: steelCantileverTrussSchemeData.strZone,
          fsy: steelCantileverTrussSchemeData.fsy,
          unbracedLength: steelCantileverTrussSchemeData.unbracedLength,
          steelSection: steelCantileverTrussSchemeData.steelSection,
          chordAxialCapacity: steelCantileverTrussSchemeData.chordAxialCapacity,
          leverArm: steelCantileverTrussSchemeData.leverArm,
          momentCapacity: steelCantileverTrussSchemeData.momentCapacity,
          liveLoadDeflection: steelCantileverTrussSchemeData.liveLoadDeflection,
          calsLog: steelCantileverTrussSchemeData.calsLog,
          beamForce: steelCantileverTrussSchemeData.beamForce,
        ).toCompanion();
    await transaction(() async {
      await (into(
        listSteelCantileverTrussSchemeData,
      ).insertOnConflictUpdate(x1));
    });
  }

  Future<BasementWallSchemeInput> queryBasementWallSchemeInput() async {
    final x = await select(listBasementWallSchemeInput).get();
    final BasementWallSchemeInput basementWallSchemeInput;
    if (x.isNotEmpty) {
      basementWallSchemeInput = BasementWallSchemeInput(
        id: x[0].id,
        fcu: x[0].fcu,
        cover: x[0].cover,
        mainKValue: x[0].mainKValue,
        mainSteelRatio: x[0].mainSteelRatio,
        minS: x[0].minS,
        maxS: x[0].maxS,
        maxDepth: x[0].maxDepth,
        minDepth: x[0].minDepth,
        maxLayers: x[0].maxLayers,
        wallTopLevel: x[0].wallTopLevel,
        wallBottomLevel: x[0].wallBottomLevel,
        soilTopLevel: x[0].soilTopLevel,
        waterTopLevel: x[0].waterTopLevel,
      );
    } else {
      basementWallSchemeInput = BasementWallSchemeInput();
    }
    return basementWallSchemeInput;
  }

  Future<void> saveBasementWallSchemeInput(
    BasementWallSchemeInput basementWallSchemeInput,
  ) async {
    final x1 =
        BasementWallSchemeInputEntity(
          id: '1',
          fcu: basementWallSchemeInput.fcu,
          cover: basementWallSchemeInput.cover,
          mainKValue: basementWallSchemeInput.mainKValue,
          mainSteelRatio: basementWallSchemeInput.mainSteelRatio,
          minS: basementWallSchemeInput.minS,
          maxS: basementWallSchemeInput.maxS,
          maxDepth: basementWallSchemeInput.maxDepth,
          minDepth: basementWallSchemeInput.minDepth,
          maxLayers: basementWallSchemeInput.maxLayers,
          wallTopLevel: basementWallSchemeInput.wallTopLevel,
          wallBottomLevel: basementWallSchemeInput.wallBottomLevel,
          soilTopLevel: basementWallSchemeInput.soilTopLevel,
          waterTopLevel: basementWallSchemeInput.waterTopLevel,
        ).toCompanion();
    await transaction(() async {
      await (into(listBasementWallSchemeInput).insertOnConflictUpdate(x1));
    });
  }

  Future<BasementWallSchemeData> queryBasementWallSchemeData() async {
    final x = await select(listBasementWallSchemeData).get();
    final BasementWallSchemeData basementWallSchemeData;
    if (x.isNotEmpty) {
      basementWallSchemeData = BasementWallSchemeData(
        strZone: x[0].strZone,
        fcu: x[0].fcu,
        cover: x[0].cover,
        mainTopBar: x[0].mainTopBar,
        mainBottomBar: x[0].mainBottomBar,
        mainLinks: x[0].mainLinks,
        wallTopLevel: x[0].wallTopLevel,
        wallBottomLevel: x[0].wallBottomLevel,
        soilTopLevel: x[0].soilTopLevel,
        waterTopLevel: x[0].waterTopLevel,
        basementWallSchemeId: x[0].basementWallSchemeId,
        calsLog: x[0].calsLog,
        wallForceULS: x[0].wallForceULS,
        wallForceSLS: x[0].wallForceSLS,
      );
    } else {
      basementWallSchemeData = BasementWallSchemeData();
    }
    return basementWallSchemeData;
  }

  Future<void> saveBasementWallSchemeData(
    BasementWallSchemeData basementWallSchemeData,
  ) async {
    final x1 =
        BasementWallSchemeDataEntity(
          strZone: basementWallSchemeData.strZone,
          fcu: basementWallSchemeData.fcu,
          cover: basementWallSchemeData.cover,
          mainTopBar: basementWallSchemeData.mainTopBar,
          mainBottomBar: basementWallSchemeData.mainBottomBar,
          mainLinks: basementWallSchemeData.mainLinks,
          wallTopLevel: basementWallSchemeData.wallTopLevel,
          wallBottomLevel: basementWallSchemeData.wallBottomLevel,
          soilTopLevel: basementWallSchemeData.soilTopLevel,
          waterTopLevel: basementWallSchemeData.waterTopLevel,
          basementWallSchemeId: basementWallSchemeData.basementWallSchemeId,
          calsLog: basementWallSchemeData.calsLog,
          wallForceULS: basementWallSchemeData.wallForceULS,
          wallForceSLS: basementWallSchemeData.wallForceSLS,
        ).toCompanion();
    await transaction(() async {
      await (into(listBasementWallSchemeData).insertOnConflictUpdate(x1));
    });
  }

  Future<List<ProgrammeItem>> queryAllProgrammeItems() async {
    final x = await select(listProgrammeItem).get();
    final List<ProgrammeItem> finalList = [];
    if (x.isNotEmpty) {
      await Future.forEach(x, (x1) {
        final data = ProgrammeItem(
          itemName: x1.itemName,
          start: x1.start,
          duration: x1.duration,
          isTouched: x1.isTouched,
          id: x1.id,
        );
        finalList.add(data);
      });
    }
    return finalList;
  }

  Future<void> saveProgrammeItems(List<ProgrammeItem> programmeItems) async {
    final List<ListProgrammeItemCompanion> finalList = [];
    await Future.forEach(programmeItems, (item) async {
      final x1 =
          ProgrammeItemEntity(
            itemName: item.itemName,
            start: item.start,
            duration: item.duration,
            isTouched: item.isTouched,
            id: item.id,
          ).toCompanion();
      finalList.add(x1);
    });
    await transaction(() async {
      await batch((batch) {
        batch.insertAllOnConflictUpdate(listProgrammeItem, finalList);
      });
    });
    // finally, check if any task exist in database only
    // this tasks need to be deleted as they no longer exist in
    // latest task list

    final Set<String> x2 = programmeItems.map((item) => item.id).toSet();
    await transaction(() async {
      await (delete(listProgrammeItem)
        ..where((tbl) => tbl.id.isNotIn(x2))).go();
    });
  }

  Future<List<WindLoad>> queryWindLoadInput() async {
    final x = await select(listWindLoad).get();
    final List<WindLoad> finalList = [];
    if (x.isNotEmpty) {
      await Future.forEach(x, (x1) {
        final data = WindLoad(
          h: x1.h,
          bTop: x1.bTop,
          dTop: x1.dTop,
          z: x1.z,
          b: x1.b,
          d: x1.d,
          sS: x1.sS,
          bldgType: x1.bldgType,
          amplificationFactor: x1.amplificationFactor,
          id: x1.id,
        );
        finalList.add(data);
      });
    }
    return finalList;
  }

  Future<void> saveAllWindLoad(List<WindLoad> windLoad) async {
    final List<ListWindLoadCompanion> finalList = [];
    await Future.forEach(windLoad, (load) async {
      final x1 =
          WindLoadEntity(
            h: load.h,
            bTop: load.bTop,
            dTop: load.dTop,
            z: load.z,
            b: load.b,
            d: load.d,
            sS: load.sS,
            bldgType: load.bldgType,
            amplificationFactor: load.amplificationFactor,
            id: load.id,
          ).toCompanion();
      finalList.add(x1);
    });
    await transaction(() async {
      await batch((batch) {
        batch.insertAllOnConflictUpdate(listWindLoad, finalList);
      });
    });
    // finally, check if any task exist in database only
    // this tasks need to be deleted as they no longer exist in
    // latest task list

    final Set<String> x2 = windLoad.map((load) => load.id).toSet();
    await transaction(() async {
      await (delete(listWindLoad)..where((tbl) => tbl.id.isNotIn(x2))).go();
    });
  }

  Future<WindLoadGlobal> queryWindLoadInputGlobal() async {
    final x = await select(listWindLoadGlobal).get();
    final WindLoadGlobal windLoadGlobal;
    if (x.isNotEmpty) {
      windLoadGlobal = WindLoadGlobal(
        h: x[0].h,
        bTop: x[0].bTop,
        dTop: x[0].dTop,
        sS: x[0].sS,
        bldgType: x[0].bldgType,
        amplificationFactor: x[0].amplificationFactor,
        id: x[0].id,
      );
    } else {
      windLoadGlobal = WindLoadGlobal();
    }
    return windLoadGlobal;
  }

  Future<void> saveWindLoadGlobal(WindLoadGlobal windLoadGlobal) async {
    final x1 =
        WindLoadGlobalEntity(
          h: windLoadGlobal.h,
          bTop: windLoadGlobal.bTop,
          dTop: windLoadGlobal.dTop,
          sS: windLoadGlobal.sS,
          bldgType: windLoadGlobal.bldgType,
          amplificationFactor: windLoadGlobal.amplificationFactor,
          id: windLoadGlobal.id,
        ).toCompanion();
    await transaction(() async {
      await (into(listWindLoadGlobal).insertOnConflictUpdate(x1));
    });
  }

  Future<List<Carbon>> queryCarbonInput() async {
    final x = await select(listCarbon).get();
    final List<Carbon> finalList = [];
    if (x.isNotEmpty) {
      await Future.forEach(x, (x1) {
        final data = Carbon.fromJson(jsonDecode(x1.carbon));
        finalList.add(data);
      });
    }
    return finalList;
  }

  Future<void> saveCarbonInput(List<Carbon> carbons) async {
    final List<ListCarbonCompanion> finalList = [];
    await Future.forEach(carbons, (carbon) async {
      final x1 = CarbonEntity(carbon: carbon, id: carbon.id).toCompanion();
      finalList.add(x1);
    });
    await transaction(() async {
      await batch((batch) {
        batch.insertAllOnConflictUpdate(listCarbon, finalList);
      });
    });
    // finally, check if any task exist in database only
    // this tasks need to be deleted as they no longer exist in
    // latest task list

    final Set<String> x2 = carbons.map((carbon) => carbon.id).toSet();
    await transaction(() async {
      await (delete(listCarbon)..where((tbl) => tbl.id.isNotIn(x2))).go();
    });
  }
}

LazyDatabase _openConnection() {
  return LazyDatabase(() async {
    final dbFolder = await getApplicationDocumentsDirectory();
    final file = File(p.join(dbFolder.path, 'structify_db.sqlite'));
    return NativeDatabase.createInBackground(file);
  });
}
