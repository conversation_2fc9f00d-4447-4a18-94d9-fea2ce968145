import 'package:freezed_annotation/freezed_annotation.dart';
// import 'package:flutter/foundation.dart';
part 'steel_column_scheme_input_global.freezed.dart';
part 'steel_column_scheme_input_global.g.dart';

@freezed
abstract class SteelColumnSchemeInputGlobal with _$SteelColumnSchemeInputGlobal{
  const SteelColumnSchemeInputGlobal._();
  factory SteelColumnSchemeInputGlobal({
    @Default('1') String id,
    @Default('S355') String steelGrade,
    @Default(5.0) double unbracedLength,

  }) = _SteelColumnSchemeInputGlobal;

  factory SteelColumnSchemeInputGlobal.fromJson(Map<String, Object?> json) =>
      _$SteelColumnSchemeInputGlobalFromJson(json);
}