import 'package:freezed_annotation/freezed_annotation.dart';
// import 'package:flutter/foundation.dart';
part 'basement_wall_scheme_input.freezed.dart';
part 'basement_wall_scheme_input.g.dart';

@freezed
abstract class BasementWallSchemeInput with _$BasementWallSchemeInput {
  const BasementWallSchemeInput._();
  factory BasementWallSchemeInput({
    @Default('1') String id,
    @Default(45.0) double fcu,
    @Default(75.0) double cover,
    @Default(0.156) double mainKValue,
    @Default(0.04) double mainSteelRatio,
    @Default(100) int minS,
    @Default(300) int maxS,
    @Default(500) double maxDepth,
    @Default(150.0) double minDepth,
    @Default(2) int maxLayers,
    @Default(0.0) double wallTopLevel,
    @Default(-3.0) double wallBottomLevel,
    @Default(0.0) double soilTopLevel,
    @Default(0.0) double waterTopLevel,

  }) = _BasementWallSchemeInput;

  factory BasementWallSchemeInput.fromJson(Map<String, Object?> json) =>
      _$BasementWallSchemeInputFromJson(json);
}
