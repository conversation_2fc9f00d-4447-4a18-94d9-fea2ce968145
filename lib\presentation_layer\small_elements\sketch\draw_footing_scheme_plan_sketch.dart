import 'dart:math' as math;
import 'dart:ui' as ui;
import 'dart:ui';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:structify/domain_layer/slab_scheme_data.dart';
import 'package:structify/presentation_layer/screen/homescreen.dart';
import 'package:vector_math/vector_math.dart' as vec;

import '../../../domain_layer/footing_scheme_data.dart';
import '../../../domain_layer/global_data.dart';
import '../../../domain_layer/preferences.dart';

class DrawFootingSchemePlanSketch extends ConsumerWidget {
  DrawFootingSchemePlanSketch({
    required this.sketchWidth,
    required this.sketchHeight,
    required this.index,
    this.fontSize,
    super.key,
  });

  final int index;
  final double sketchWidth;
  final double sketchHeight;
  double? fontSize;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final data1 = ref.watch(footingSchemeDataControllerProvider);
    final data2 = ref.watch(globalDataControllerProvider);
    final ColorScheme colorScheme = Theme.of(context).colorScheme;
    final TextTheme textTheme = Theme.of(context).textTheme;
    fontSize = fontSize ?? math.min(sketchWidth, sketchHeight) / 15;
    return data1.when(
      data: (footingSchemedata) {
        return data2.when(
          data: (globalData) {
            final constraints = BoxConstraints(
              maxWidth: sketchWidth,
              maxHeight: sketchHeight,
              minHeight: 100,
              minWidth: 100,
            );
            return Center(
              child: Container(
                decoration: BoxDecoration(
                  color: colorScheme.surfaceContainer,
                  borderRadius: BorderRadius.circular(10.0),
                  border: Border.all(color: Colors.black.withAlpha(100)),
                ),
                width:
                    constraints.maxWidth == double.infinity
                        ? 100
                        : constraints.maxWidth,
                height:
                    constraints.maxHeight == double.infinity
                        ? 100
                        : constraints.maxHeight,
                child: CustomPaint(
                  painter: DrawFootingSchemePlanSketchPainter(
                    footingSchemedata: footingSchemedata[index],
                    globalData: globalData,
                    boxConstraints: constraints,
                    fontSize: fontSize,
                    context: context,
                  ),
                ),
              ),
            );
          },

          error: (error, stackTrace) {
            return Text(error.toString());
          },
          loading: () {
            return const CircularProgressIndicator();
          },
        );
      },
      error: (error, stackTrace) {
        return Text(error.toString());
      },
      loading: () {
        return const CircularProgressIndicator();
      },
    );
  }
}

class DrawFootingSchemePlanSketchPainter extends CustomPainter {
  final FootingSchemeData footingSchemedata;
  final GlobalData globalData;
  final BoxConstraints boxConstraints;
  double? fontSize;
  final BuildContext context;

  DrawFootingSchemePlanSketchPainter({
    required this.footingSchemedata,
    required this.globalData,
    required this.boxConstraints,
    this.fontSize,
    required this.context,
  });

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return true;
  }

  @override
  void paint(Canvas canvas, Size size) {
    final ColorScheme colorScheme = Theme.of(context).colorScheme;
    final TextTheme textTheme = Theme.of(context).textTheme;
    late final double secBeamSpacing;
    late final String coulmnSize;
    Color fontColor = Colors.red.withAlpha(200);

    fontSize = fontSize ?? math.min(size.width, size.height) / 40;

    Paint rcPaint =
        Paint()
          ..color = colorScheme.onSurface
          ..strokeCap = StrokeCap.round
          ..style = PaintingStyle.stroke
          ..strokeWidth = 2.0;

    Paint rebarPaint =
        Paint()
          ..color = colorScheme.onSurface
          ..strokeCap = StrokeCap.round
          ..style = PaintingStyle.stroke
          ..strokeWidth = 2.0;

    final double minDim = math.min(
      boxConstraints.maxWidth,
      boxConstraints.maxHeight,
    );
    final double ratio = minDim / footingSchemedata.size * 4 / 5;

    late Offset startP, endP, startP1, endP1, startP2, endP2;
    Path path = Path();
    // ******************
    // draw  Plan
    // ******************
    startP = Offset(
      (0.5 * (boxConstraints.maxWidth) - footingSchemedata.size / 2 * ratio),
      (0.5 * (boxConstraints.maxHeight) - footingSchemedata.size / 2 * ratio),
    );
    final points = Float32List.fromList(
      _getPointsFromOffset(startP, [
        Offset(footingSchemedata.size * ratio, 0),
        Offset(0, footingSchemedata.size * ratio),
        Offset(-footingSchemedata.size * ratio, 0),
        Offset(0, -footingSchemedata.size * ratio),
      ]),
    );
    canvas.drawRawPoints(PointMode.polygon, points, rcPaint);
    _drawDimLine(
      canvas,
      fontSize!,
      startP + Offset(0, boxConstraints.maxHeight / 15),
      startP +
          Offset(footingSchemedata.size * ratio, boxConstraints.maxHeight / 15),
      0,
      fontSize! / 2,
      footingSchemedata.size,
      globalData,
      context,
    );
    // ******************
    // draw Column Plan
    // ******************
    if (footingSchemedata.colShape == 'Square') {
      startP1 = Offset(
        (0.5 * (boxConstraints.maxWidth) -
            footingSchemedata.columnSize / 2 * ratio),
        (0.5 * (boxConstraints.maxHeight) -
            footingSchemedata.columnSize / 2 * ratio),
      );

      final points2 = Float32List.fromList(
        _getPointsFromOffset(startP1, [
          Offset(footingSchemedata.columnSize * ratio, 0),
          Offset(0, footingSchemedata.columnSize * ratio),
          Offset(-footingSchemedata.columnSize * ratio, 0),
          Offset(0, -footingSchemedata.columnSize * ratio),
        ]),
      );
      canvas.drawRawPoints(PointMode.polygon, points2, rcPaint);
      _drawDimLine(
        canvas,
        fontSize!,
        startP1 + Offset(0, boxConstraints.maxHeight / 15),
        startP1 +
            Offset(
              footingSchemedata.columnSize * ratio,
              boxConstraints.maxHeight / 15,
            ),
        0,
        fontSize! / 2,
        footingSchemedata.columnSize,
        globalData,
        context,
      );
    } else {
      startP1 = Offset(
        (0.5 * (boxConstraints.maxWidth)),
        (0.5 * (boxConstraints.maxHeight)),
      );
      canvas.drawCircle(
        startP1,
        footingSchemedata.columnSize * ratio / 2,
        rcPaint,
      );
      _drawDimLine(
        canvas,
        fontSize!,
        startP1 + Offset(-footingSchemedata.columnSize / 2 * ratio, 0),
        startP1 + Offset(footingSchemedata.columnSize / 2 * ratio, 0),
        0,
        fontSize! / 2,
        footingSchemedata.columnSize,
        globalData,
        context,
      );
    }

    // ******************
    //* Draw Text
    // ******************
    // Footing Text
    TextSpan textSpan = TextSpan(
      text:
          'Footing:\n${NumberFormat('0').format(footingSchemedata.size)}(W)x${NumberFormat('0').format(footingSchemedata.size)}(L)x${NumberFormat('0').format(footingSchemedata.strZone)}(H)',
      style: TextStyle(
        fontSize: fontSize,
        color: fontColor,
        fontWeight: FontWeight.bold,
      ),
    );

    TextPainter textPainter = TextPainter(
      text: textSpan,
      textAlign: TextAlign.center,
      textDirection: ui.TextDirection.ltr,
    )..layout();

    textPainter.paint(
      canvas,
      Offset(
        boxConstraints.maxWidth / 2 - textPainter.width / 2,
        footingSchemedata.size * ratio - boxConstraints.maxHeight / 8,
      ),
    );

    // Column Text
    textSpan = TextSpan(
      text:
          footingSchemedata.colShape == 'Square'
              ? 'Column: ${NumberFormat('0').format(footingSchemedata.columnSize)}x${NumberFormat('0').format(footingSchemedata.columnSize)}'
              : 'Column: \u03D5${footingSchemedata.columnSize}',
      style: TextStyle(
        fontSize: fontSize,
        color: fontColor,
        fontWeight: FontWeight.bold,
      ),
    );

    textPainter = TextPainter(
      text: textSpan,
      textAlign: TextAlign.center,
      textDirection: ui.TextDirection.ltr,
    )..layout();

    textPainter.paint(
      canvas,
      Offset(
        boxConstraints.maxWidth / 2 - textPainter.width / 2,
        boxConstraints.maxHeight / 2 +
            footingSchemedata.size / 2 * ratio -
            boxConstraints.maxHeight / 50,
      ),
    );
  }

  void _rebarDesignation(
    List<int> rebarDia,
    int i,
    List<int> rebarSpacing,
    ui.Color fontColor,
    ui.Canvas canvas,
    ui.Offset endP3,
  ) {
    TextSpan textSpan = TextSpan(
      text: 'T${rebarDia[i]}-${rebarSpacing[i]}',
      style: TextStyle(
        fontSize: fontSize,
        color: fontColor,
        fontWeight: FontWeight.bold,
      ),
    );

    TextPainter textPainter = TextPainter(
      text: textSpan,
      textAlign: TextAlign.center,
      textDirection: ui.TextDirection.ltr,
    )..layout();

    textPainter.paint(canvas, endP3 + Offset(7, -7));
  }

  void _drawDimLine(
    Canvas canvas,
    double fontSize,
    Offset start,
    Offset end,
    double offsetDistance,
    double dimExtension,
    double slabDepth,
    GlobalData globalData,
    BuildContext context,
  ) {
    double tempOffset = 10; // forthe dim line end
    late Offset tempEnd;
    late Offset tempStart;
    late Offset textCenter;

    late double angle;

    late List<String> unit;

    Color fontColor = Colors.red.withAlpha(200);

    late final vec.Vector2 u;
    late final vec.Vector2 uUnit;
    late final vec.Vector2 vUnit;
    late final vec.Vector2 vUnit45;
    late final vec.Matrix2 m;

    final ColorScheme colorScheme = Theme.of(context).colorScheme;
    final TextTheme textTheme = Theme.of(context).textTheme;

    // get the unit
    switch (globalData.unit) {
      case 'metrics':
        unit = PreferredUnit.metrics;
        break;
      case 'imperial':
        unit = PreferredUnit.imperial;
        break;
      default:
        unit = PreferredUnit.metrics;
    }

    // double dimExtension = offsetDistance.abs() / 2;

    final Paint paint =
        Paint()
          ..color = fontColor
          ..strokeWidth = 1.0
          ..style = PaintingStyle.stroke
          ..strokeJoin = StrokeJoin.round;

    // line vector
    u = vec.Vector2(end.dx - start.dx, end.dy - start.dy);
    uUnit = u / u.length;

    // orthogonal vector
    vUnit = vec.Vector2(uUnit.y, uUnit.x); //rotate aniti-clockwise

    // Vector of parallel line with Offset
    Offset newStart = Offset(
      start.dx - vUnit.x * offsetDistance - uUnit.x * dimExtension,
      start.dy - vUnit.y * offsetDistance - uUnit.y * dimExtension,
    );
    Offset newEnd = Offset(
      end.dx - vUnit.x * offsetDistance + uUnit.x * dimExtension,
      end.dy - vUnit.y * offsetDistance + uUnit.y * dimExtension,
    );
    canvas.drawLine(newStart, newEnd, paint);

    // draw start crossing
    tempStart = Offset(
      (start.dx - vUnit.x * offsetDistance) - vUnit.x * dimExtension,
      (start.dy - vUnit.y * offsetDistance) - vUnit.y * dimExtension,
    );
    tempEnd = Offset(
      (start.dx - vUnit.x * offsetDistance) + vUnit.x * dimExtension,
      (start.dy - vUnit.y * offsetDistance) + vUnit.y * dimExtension,
    );
    canvas.drawLine(tempStart, tempEnd, paint);

    m = vec.Matrix2.rotation(-45 * math.pi / 180);
    vUnit45 = m * vUnit;
    tempStart = Offset(
      (start.dx - vUnit.x * offsetDistance) - vUnit45.x * dimExtension,
      (start.dy - vUnit.y * offsetDistance) - vUnit45.y * dimExtension,
    );
    tempEnd = Offset(
      (start.dx - vUnit.x * offsetDistance) + vUnit45.x * dimExtension,
      (start.dy - vUnit.y * offsetDistance) + vUnit45.y * dimExtension,
    );
    canvas.drawLine(tempStart, tempEnd, paint);

    // draw end crossing
    tempStart = Offset(
      (end.dx - vUnit.x * offsetDistance) - vUnit.x * dimExtension,
      (end.dy - vUnit.y * offsetDistance) - vUnit.y * dimExtension,
    );
    tempEnd = Offset(
      (end.dx - vUnit.x * offsetDistance) + vUnit.x * dimExtension,
      (end.dy - vUnit.y * offsetDistance) + vUnit.y * dimExtension,
    );
    canvas.drawLine(tempStart, tempEnd, paint);

    tempStart = Offset(
      (end.dx - vUnit.x * offsetDistance) - vUnit45.x * dimExtension,
      (end.dy - vUnit.y * offsetDistance) - vUnit45.y * dimExtension,
    );
    tempEnd = Offset(
      (end.dx - vUnit.x * offsetDistance) + vUnit45.x * dimExtension,
      (end.dy - vUnit.y * offsetDistance) + vUnit45.y * dimExtension,
    );
    canvas.drawLine(tempStart, tempEnd, paint);

    // indicate span length and beam size

    TextSpan textSpan = TextSpan(
      text: slabDepth.toStringAsFixed(0),
      style: TextStyle(
        fontSize: fontSize,
        color: fontColor,
        fontWeight: FontWeight.bold,
      ),
    );

    TextPainter textPainter = TextPainter(
      text: textSpan,
      textAlign: TextAlign.center,
      textDirection: ui.TextDirection.ltr,
    )..layout();

    textCenter = Offset(
      (start.dx + end.dx) / 2 -
          vUnit.x * offsetDistance * 2 -
          uUnit.x * textPainter.width / 2,
      (start.dy + end.dy) / 2 -
          vUnit.y * offsetDistance * 2 -
          uUnit.y * textPainter.width / 2,
    );

    angle = math.atan2(end.dy - start.dy, end.dx - start.dx) * 180 / math.pi;

    canvas.save();
    canvas.translate(textCenter.dx, textCenter.dy);
    canvas.rotate(angle * math.pi / 180);
    textPainter.paint(canvas, Offset.zero);
    canvas.restore();
  }

  Float32List _getPointsFromOffset(Offset start, List<Offset> offsets) {
    List<Offset> points = [start];
    for (Offset offset in offsets) {
      start = start + offset;
      points.add(start);
    }
    return Float32List.fromList(points.expand((i) => [i.dx, i.dy]).toList());
  }
}
