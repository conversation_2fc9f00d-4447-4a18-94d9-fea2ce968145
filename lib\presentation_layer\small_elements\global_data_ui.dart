import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:structify/domain_layer/preferences.dart';

//presentation layer
import 'input/custom_stateful_double_input.dart';
import '../screen/homescreen.dart';

class GlobalDataUi extends ConsumerStatefulWidget {
  const GlobalDataUi({
    this.isExpanded,
    this.maxHeight,
    this.minHeight,
    super.key,
  });

  final bool? isExpanded;
  final double? maxHeight;
  final double? minHeight;

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _GlobalDataUIState();
}

class _GlobalDataUIState extends ConsumerState<GlobalDataUi>
    with WidgetsBindingObserver {
  late bool _isExpanded;
  late double _maxHeight;
  late double _minHeight;

  late ScrollController _scrollController;

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void initState() {
    WidgetsBinding.instance.addObserver(this);
    _isExpanded = widget.isExpanded ?? false;
    _maxHeight = widget.maxHeight ?? 210;
    _minHeight = widget.minHeight ?? 50;
    _scrollController = ScrollController();
    // _updateHeights();

    super.initState();
  }

  @override
  void didChangeDependencies() {
    _updateHeights();
    super.didChangeDependencies();
    // Update heights when dependencies change, including after initState
  }

  @override
  void didChangeMetrics() {
    // This method is called when the metrics change (like resizing the window)
    setState(() {
      _updateHeights();
    });
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    final int containerOpacity = 175;
    final int textOpacity = 225;

    ref.watch(appWatcherProvider);
    final globalData = ref.watch(globalDataControllerProvider);

    return globalData.when(
      data: (data) {
        late final List<String> preferredUnit;
        switch (data.unit) {
          case 'metrics':
            preferredUnit = PreferredUnit.metrics;
            break;
          case 'imperial':
            preferredUnit = PreferredUnit.imperial;
            break;
          default:
            preferredUnit = PreferredUnit.metrics;
            break;
        }

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.fromLTRB(10, 0, 10, 0),
              child: Divider(),
            ),
            Padding(
              padding: const EdgeInsets.fromLTRB(10, 0, 0, 0),
              child: Align(
                alignment: Alignment.centerLeft,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        IconButton(
                          icon: Icon(
                            _isExpanded ? Icons.expand_less : Icons.expand_more,
                          ),
                          color: colorScheme.onSurface,
                          onPressed: () {
                            setState(() {
                              _isExpanded = !_isExpanded;
                            });
                          },
                        ),
                        Text(
                          'General',
                          style: textTheme.titleLarge!.copyWith(
                            color: colorScheme.onSurface,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            Padding(
              padding: const EdgeInsets.fromLTRB(10, 0, 10, 0),
              child: Divider(),
            ),
            ClipRect(
              child: ConstrainedBox(
                constraints: BoxConstraints(
                  maxHeight: _isExpanded ? _maxHeight : 0,
                ),
                child: Scrollbar(
                  controller: _scrollController,
                  thumbVisibility: true,
                  child: SingleChildScrollView(
                    controller: _scrollController,
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Padding(
                          padding: const EdgeInsets.fromLTRB(
                            8.0,
                            4.0,
                            8.0,
                            4.0,
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Container(
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(10.0),
                                  border: Border.all(
                                    color: Colors.grey.withAlpha(150),
                                  ),
                                  color: colorScheme.secondaryContainer
                                      .withAlpha(containerOpacity),
                                ),
                                child: Padding(
                                  padding: const EdgeInsets.all(4.0),
                                  child: Text(
                                    'Load Factor',
                                    style: textTheme.titleSmall!.copyWith(
                                      color: colorScheme.onSecondaryContainer
                                          .withAlpha(textOpacity),
                                    ),
                                  ),
                                ),
                              ),
                              SizedBox(height: 10.0),
                              Row(
                                mainAxisSize: MainAxisSize.min,
                                mainAxisAlignment: MainAxisAlignment.start,
                                children: [
                                  Flexible(
                                    child: CustomStatefulDoubleInput(
                                      title: 'SDL Load Factor',
                                      tooltipText:
                                          'Should be based on code adopted',
                                      value: data.sdlFactor,
                                      onChanged: (value) async {
                                        await ref
                                            .read(
                                              globalDataControllerProvider
                                                  .notifier,
                                            )
                                            .updateGlobalData(sdlFactor: value);
                                      },
                                    ),
                                  ),
                                  SizedBox(width: 5.0),
                                  Flexible(
                                    child: CustomStatefulDoubleInput(
                                      title: 'LL Load Factor',
                                      tooltipText:
                                          'Should be based on code adopted',
                                      value: data.llFactor,
                                      onChanged: (value) async {
                                        await ref
                                            .read(
                                              globalDataControllerProvider
                                                  .notifier,
                                            )
                                            .updateGlobalData(llFactor: value);
                                      },
                                    ),
                                  ),
                                  Flexible(child: SizedBox(width: 5.0)),
                                  Flexible(child: SizedBox(width: 5.0)),
                                ],
                              ),
                            ],
                          ),
                        ),
                        SizedBox(height: 5.0),
                        Padding(
                          padding: const EdgeInsets.fromLTRB(
                            8.0,
                            4.0,
                            8.0,
                            4.0,
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Container(
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(10.0),
                                  border: Border.all(
                                    color: Colors.grey.withAlpha(150),
                                  ),
                                  color: colorScheme.secondaryContainer
                                      .withAlpha(containerOpacity),
                                ),
                                child: Padding(
                                  padding: const EdgeInsets.all(4.0),
                                  child: Text(
                                    'Material Unit Weight',
                                    style: textTheme.titleSmall!.copyWith(
                                      color: colorScheme.onSecondaryContainer
                                          .withAlpha(textOpacity),
                                    ),
                                  ),
                                ),
                              ),
                              SizedBox(height: 10.0),
                              Row(
                                children: [
                                  Flexible(
                                    child: CustomStatefulDoubleInput(
                                      title:
                                          'Finish  [${preferredUnit[0]}/${preferredUnit[3]}3]',
                                      tooltipText:
                                          'Should be based on code adopted',
                                      value: data.finishUnitWeight,
                                      onChanged: (value) async {
                                        await ref
                                            .read(
                                              globalDataControllerProvider
                                                  .notifier,
                                            )
                                            .updateGlobalData(
                                              finishUnitWeight: value,
                                            );
                                      },
                                    ),
                                  ),
                                  SizedBox(width: 5.0),
                                  Flexible(
                                    child: CustomStatefulDoubleInput(
                                      title:
                                          'Reinforced Concrete  [${preferredUnit[0]}/${preferredUnit[3]}3]',
                                      tooltipText:
                                          'Should be based on code adopted',
                                      value: data.rcUnitWeight,
                                      onChanged: (value) async {
                                        await ref
                                            .read(
                                              globalDataControllerProvider
                                                  .notifier,
                                            )
                                            .updateGlobalData(
                                              rcUnitWeight: value,
                                            );
                                      },
                                    ),
                                  ),
                                  SizedBox(width: 5.0),
                                  Flexible(
                                    child: CustomStatefulDoubleInput(
                                      title:
                                          'Steel  [${preferredUnit[0]}/${preferredUnit[3]}3]',
                                      tooltipText:
                                          'Should be based on code adopted',
                                      value: data.steelUnitWeight,
                                      onChanged: (value) async {
                                        await ref
                                            .read(
                                              globalDataControllerProvider
                                                  .notifier,
                                            )
                                            .updateGlobalData(
                                              steelUnitWeight: value,
                                            );
                                      },
                                    ),
                                  ),
                                  SizedBox(width: 5.0),
                                  Flexible(
                                    child: CustomStatefulDoubleInput(
                                      title:
                                          'Soil  [${preferredUnit[0]}/${preferredUnit[3]}3]',
                                      tooltipText:
                                          'Should be based on code adopted',
                                      value: data.soilUnitWeight,
                                      onChanged: (value) async {
                                        await ref
                                            .read(
                                              globalDataControllerProvider
                                                  .notifier,
                                            )
                                            .updateGlobalData(
                                              soilUnitWeight: value,
                                            );
                                      },
                                    ),
                                  ),
                                  SizedBox(width: 5.0),
                                  Flexible(
                                    child: CustomStatefulDoubleInput(
                                      title:
                                          'Water  [${preferredUnit[0]}/${preferredUnit[3]}3]',
                                      tooltipText:
                                          'Should be based on code adopted',
                                      value: data.waterUnitWeight,
                                      onChanged: (value) async {
                                        await ref
                                            .read(
                                              globalDataControllerProvider
                                                  .notifier,
                                            )
                                            .updateGlobalData(
                                              waterUnitWeight: value,
                                            );
                                      },
                                    ),
                                  ),
                                  Flexible(child: SizedBox(width: 5.0)),
                                ],
                              ),
                              SizedBox(height: 10.0),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ],
        );
      },
      error: (error, _) => Text('Error'),
      loading: () => Center(child: CircularProgressIndicator()),
    );
  }

  void _updateHeights() {
    final double screenHeight = MediaQuery.of(context).size.height;
    _maxHeight =
        (widget.maxHeight == null)
            ? screenHeight * 0.5
            : (widget.maxHeight! > screenHeight * 0.5)
            ? screenHeight * 0.5
            : widget.maxHeight!;

    _minHeight =
        (widget.minHeight == null)
            ? screenHeight * 0.1
            : (widget.minHeight! > screenHeight * 0.1)
            ? screenHeight * 0.1
            : widget.minHeight!;

    // Adjust _maxHeight if needed

    if (_maxHeight < _minHeight) {
      _maxHeight = _minHeight;
    }
  }
}
