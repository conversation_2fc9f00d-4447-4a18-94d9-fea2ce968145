import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'dart:math';
import 'package:intl/intl.dart';
import 'package:structify/domain_layer/data_struct/str_force_struct.dart';
import 'package:structify/domain_layer/steel_transfer_truss_scheme_input.dart';
import 'package:structify/misc/custom_func.dart';
import 'dart:convert';

//presentation layer
import '../../domain_layer/mixin/mixin_steel_str.dart';
import '../../domain_layer/mixin/mixin_str_general_cals.dart';
import '../../domain_layer/mixin/mixin_tools_for_ui.dart';
import '../../domain_layer/steel_transfer_truss_scheme_data.dart';
import '../../domain_layer/steel_transfer_truss_scheme_input_global.dart';
import '../screen/homescreen.dart';

// domain layer
import '../../domain_layer/global_data.dart';
import '../../domain_layer/loading_table.dart';
import '../../domain_layer/preferences.dart';
part 'steel_transfer_truss_scheme_data_controller.g.dart';

@riverpod
class SteelTransferTrussSchemeDataController
    extends _$SteelTransferTrussSchemeDataController
    with StrGeneralCals, SteelStrHK, MixinToolsForUI {
  @override
  FutureOr<SteelTransferTrussSchemeData> build() async {
    final steelTransferSchemeDataList =
        await ref
            .read(appDatabaseControllerProvider.notifier)
            .querySteelTransferTrussSchemeData();
    final data1 = ref.watch(loadingTablesControllerProvider);
    final data2 = ref.watch(steelTransferTrussSchemeInputControllerProvider);
    final data3 = ref.watch(
      steelTransferTrussSchemeInputGlobalControllerProvider,
    );
    return data1.when(
      data: (loadingTables) async {
        return data2.when(
          data: (inputs) async {
            return data3.when(
              data: (inputGlobal) async {
                //*Validate the Loading
                final usages = loadingTables.map((x) => x.usage).toList();
                SteelTransferTrussSchemeData schemeData;
                if (usages.contains(steelTransferSchemeDataList.usage)) {
                  schemeData = steelTransferSchemeDataList;
                } else {
                  schemeData = steelTransferSchemeDataList.copyWith(
                    usage: usages.first,
                  );
                }
                return await transferTrussScheming();
              },
              error: (error, stackTrace) => SteelTransferTrussSchemeData(),
              loading: () => SteelTransferTrussSchemeData(),
            );
          },
          error: (error, stackTrace) => SteelTransferTrussSchemeData(),
          loading: () => SteelTransferTrussSchemeData(),
        );
      },
      error: (error, stackTrace) => SteelTransferTrussSchemeData(),
      loading: () => SteelTransferTrussSchemeData(),
    );
  }

  Future<SteelTransferTrussSchemeData> transferTrussScheming() async {
    // * Read Necessary data
    final GlobalData globalData = await ref.read(
      globalDataControllerProvider.future,
    );
    final List<LoadingTable> loadingTables = await ref.read(
      loadingTablesControllerProvider.future,
    );
    final List<SteelTransferTrussSchemeInput> inputs = await ref.read(
      steelTransferTrussSchemeInputControllerProvider.future,
    );
    final SteelTransferTrussSchemeInputGlobal inputGlobal = await ref.read(
      steelTransferTrussSchemeInputGlobalControllerProvider.future,
    );
    // ********************************
    // * initialization
    // ********************************
    // iteration status
    late bool shouldRedesign;
    bool reDesignMc = true;
    // bool reDesignI = true;

    //iteration parameter to track
    late double depth;

    //provided tension, compression steel and links
    Map<String, dynamic> section = {};
    final List<Map<String, dynamic>> steelSections =
        await getAllSteelISection();

    // design moment and shear
    late double mD, vD;

    // design capacity
    double fsy = inputGlobal.fsy;
    late double Mc;

    // record
    StringBuffer buffer = StringBuffer();
    List<bool> status = [false];
    LoadingTable loadingTable;
    double chordAxialCapacity = 0, leverArm = 0;
    String liveLoadDeflection = '';

    // Some global data
    final double f1 = globalData.sdlFactor, f2 = globalData.llFactor;

    final double Es = 205000; // [MPa] Elastic modulus of steel
    late double sdl, ll, ulsUDL;

    //For Calculating internal force
    int n;
    double div, h;
    List<double> listMd = [], listVd = [], listX = [];
    double xMaxMd = 0, xMaxVd = 0;
    double tempMd = 0, tempVd = 0, tempP = 0, totalL = 0;
    int division = 100;
    Set<double> setA = {}, setB = {}, setC = {};
    Map<String, dynamic> beamForceMap = {};
    String beamForceInString = ''; //JSON String version

    //* get section
    section = steelSections.first;

    // ********************************
    // Design Looping
    // ********************************
    shouldRedesign = true; // at least run the first

    while (shouldRedesign) {
      // ********************************
      // * get loading
      // ********************************

      // ********************************
      // todo: design moment and shear adjusted with beam self-weight
      // ********************************

      //Reset For Calculating internal force
      listMd = [];
      listVd = [];
      xMaxMd = 0;
      xMaxVd = 0;
      tempMd = 0;
      tempVd = 0;
      tempP = 0;
      setA = {};
      setB = {};
      listX = [];

      loadingTable = loadingTables.firstWhere(
        (item) => item.usage == inputGlobal.usage,
      );
      sdl =
          (getPressureFromThick(
                loadingTable.finish,
                globalData.finishUnitWeight,
              ) +
              loadingTable.service) *
          inputGlobal.loadWidth;
      ll = loadingTable.liveLoad * inputGlobal.loadWidth;

      //! get thet self weight of chord and the diagonal member
      //! assumed chord and diagonal members same size for conservative
      n = (inputGlobal.span / inputGlobal.unbracedLength).ceil();
      div = inputGlobal.span / n / 2;
      h = inputGlobal.strZone / 1000;
      totalL =
          inputGlobal.span * 2 + // top + bottom chord
          h * 2 + // start and end vertical member
          2 * n * (sqrt(pow(div, 2) + pow(h, 2))); // diagonal members;

      //* get UDL
      ulsUDL =
          f1 * sdl + // Floor load: SDL
          f2 * ll + // Floor Load: LL
          f1 *
              getSelfWeight(
                globalData.steelUnitWeight,
                double.parse(section['A']) * pow(10, 2),
              ) *
              totalL /
              inputGlobal.span +
          // Truss Self weight (again, assume chord/diagonal member same section)
          f1 *
              getPressureFromThick(
                inputGlobal.slabThickness,
                globalData.rcUnitWeight,
              ) *
              inputGlobal.loadWidth; // slab self-weight

      listX = List.generate(division + 1, (i) {
        return i * (inputGlobal.span / division);
      });

      // !we add point load location + small distance  into x
      // !coz the shear exactly at point load location is undefined.
      listX.insertAll(
        listX.length,
        inputs.map((e) {
          if (e.distA == 0 || e.distA == inputGlobal.span) {
            return e.distA;
          }
          if (e.distA + inputGlobal.span / 200 < inputGlobal.span) {
            return e.distA + inputGlobal.span / 200;
          } else {
            return inputGlobal.span;
          }
        }).toSet(),
      );

      // !make sure list X does not contain any value same as inputs.distA
      // !coz the shear exactly at point load location is undefined.
      setA = listX.toSet();
      setC = {0, inputGlobal.span};
      setB = inputs.map((e) => e.distA).toSet();
      setB = setB.difference(setC); // make sure end point is not in Set B
      setA = setA.difference(
        setB,
      ); // make sure point load location is not in Set A

      // convert the location back to list and sort it to ascending order
      listX = setA.toList();
      listX.sort();

      for (double x in listX) {
        //* 1.  Md and Vd due to UDL
        tempMd += getMomentAtXBeamUDL(ulsUDL, inputGlobal.span, x);
        tempVd += getShearAtXBeamUDL(ulsUDL, inputGlobal.span, x);

        //* 2. Md and Vd due to point load
        for (int i = 0; i < inputs.length; i++) {
          tempMd += getMomentAtXBeamPointLoad(
            inputs[i].pointLoad,
            inputGlobal.span,
            x,
            inputs[i].distA,
          );
          tempVd += getShearAtXBeamPointLoad(
            inputs[i].pointLoad,
            inputGlobal.span,
            x,
            inputs[i].distA,
          );
        }
        //store the result
        listMd.add(tempMd);
        listVd.add(tempVd);

        //reset tempMd and tempVd
        tempMd = 0;
        tempVd = 0;
      }
      // get the design Moment and shear
      mD = maxFromNum(listMd);
      vD = max(maxFromNum(listVd).abs(), minFromNum(listVd).abs());
      // also get the location of max force
      xMaxMd = listX[listMd.indexOf(mD)];

      if (vD == maxFromNum(listVd).abs()) {
        xMaxVd = listX[listVd.indexOf(maxFromNum(listVd))];
      } else {
        xMaxVd = listX[listVd.indexOf(minFromNum(listVd))];
      }
      // -----------------------------
      //* design strength
      // -----------------------------
      fsy = inputGlobal.fsy;
      fsy = await getDesignStrength(section['name'], fsy, steelSections);

      // -----------------------------
      // * design for ULS moment (assume moment control)
      // -----------------------------
      // Mc = summaztion of (fsy * A * d), where d is the lever arm
      // d = lever arm = str zone - slab thickness - section depth
      leverArm =
          inputGlobal.strZone -
          inputGlobal.slabThickness -
          double.parse(section['d']) * 10; // [mm]
      chordAxialCapacity = await getAxialCapacitySteelUC(
        fsyGrade: inputGlobal.fsy,
        unbracedLength: inputGlobal.unbracedLength,
        sectionName: section['name'],
        steelSections: steelSections,
      );

      Mc = 2 * (chordAxialCapacity * leverArm * pow(10, -3) / 2);

      // ********************************
      // * Design validaiton in each loop
      // ********************************
      // ! if the depth exceeds the str zone available, directly escape and declare the design failed
      depth = double.parse(section['d']) * 10; // [mm]
      if (2 * depth + inputGlobal.slabThickness > inputGlobal.strZone) {
        status[0] = false;
        break;
      }

      //* validate the Mc
      if (Mc < mD) {
        reDesignMc = true;
        final index = steelSections.indexOf(section) + 1;
        if (index <= steelSections.length - 1) {
          section = steelSections[index];
          continue; // no need to run rest of design process
        } else {
          section = steelSections[index - 1];
          reDesignMc = false;
          status[0] = false; // ! will break later as reDesignMc = false
        }
      } else {
        reDesignMc = false;
        status[0] = true;
      }
      shouldRedesign = reDesignMc;
    }
    //* record the internal force for return
    for (int i = 0; i < listX.length; i++) {
      final beamForce = StrForce(x: listX[i], Md: listMd[i], Vd: listVd[i]);
      beamForceMap[i.toString()] = beamForce;
    }
    beamForceInString = jsonEncode(
      beamForceMap,
      toEncodable:
          (value) =>
              value is StrForce
                  ? value.toJson()
                  : throw UnsupportedError('Cannot convert to JSON: $value'),
    );
    // ********************************
    // * Return Design
    // ********************************

    // * record the cals result
    _recordCalsResult(
      buffer,
      xMaxMd: xMaxMd,
      xMaxVd: xMaxVd,
      inputGlobal,
      globalData,
      strZone: inputGlobal.strZone,
      udl: ulsUDL,
      M_d: mD,
      Mc: Mc,
      V_d: vD,
      fsy: fsy,
      Es: Es,
      unbracedLength: inputGlobal.unbracedLength,
      chordAxialCapacity: chordAxialCapacity,
      leverArm: leverArm,
      momentCapacity: Mc,
      section: section,
      status: status,
    );

    // ! Finally assign the result
    final newScheme = SteelTransferTrussSchemeData(
      usage: inputGlobal.usage,
      slabThickness: inputGlobal.slabThickness,
      span: inputGlobal.span,
      loadWidth: inputGlobal.loadWidth,
      strZone: inputGlobal.strZone,
      fsy: inputGlobal.fsy,
      unbracedLength: inputGlobal.unbracedLength,
      steelSection: section['name'].toString().trim(),
      chordAxialCapacity: chordAxialCapacity,
      leverArm: leverArm,
      momentCapacity: Mc,
      liveLoadDeflection: liveLoadDeflection,
      id: '1',
      calsLog: buffer.toString(),
      beamForce: beamForceInString,
    );
    state = AsyncData(newScheme);
    return newScheme;
  }

  void _recordCalsResult(
    StringBuffer buffer,
    SteelTransferTrussSchemeInputGlobal input,
    GlobalData globalData, {
    required double xMaxMd,
    required double xMaxVd,
    required double strZone,
    required double udl,
    required double M_d,
    required double Mc,
    required double V_d,
    required double fsy,
    required double Es,
    required double unbracedLength,
    required double chordAxialCapacity,
    required double leverArm,
    required double momentCapacity,
    required Map<String, dynamic> section,
    required List<bool> status,
  }) {
    final x = globalData.unit;
    late final List<String> unit;
    late final double occupiedStrZone;
    switch (x) {
      case 'metrics':
        unit = PreferredUnit.metrics;
        break;
      case 'imperial':
        unit = PreferredUnit.imperial;
        break;
      default:
        unit = PreferredUnit.metrics;
        break;
    }
    buffer.clear();
    //* formatter
    NumberFormat f0 = NumberFormat('0'),
        f1 = NumberFormat('0.0'),
        f3 = NumberFormat('0.000');

    // * record target beam and status
    if (status[0]) {
      buffer.write('Truss Status: pass\n');
      buffer.write('Required Mc: pass\n');
    } else {
      buffer.write('Truss Status: fail\n');
      buffer.write('Required Mc: fail\n');
    }
    buffer.write('Designation: ${section['name']}\n');

    // * record input
    buffer.write('Span: ${f1.format(input.span)} [${unit[3]}] | ');
    buffer.write('Load Width: ${f1.format(input.loadWidth)} [${unit[3]}] | ');
    buffer.write(
      'Slab Thickness: ${f1.format(input.slabThickness)} [${unit[4]}] | ',
    );
    buffer.write('Str Zone: ${f0.format(input.strZone)} [${unit[4]}]\n');
    occupiedStrZone =
        double.parse(section['d']) * 10 * 2 + input.slabThickness; // [mm]
    buffer.write(
      'Occupied Str Zone: ${f0.format(occupiedStrZone)} [${unit[4]}] | ',
    );
    // * record loading
    buffer.write('UDL: ${f1.format(udl)} [${unit[0]}/${unit[3]}] | ');
    buffer.write('M: ${f0.format(M_d)} [${unit[2]}] | ');
    buffer.write('xMaxMd: ${f3.format(xMaxMd)} [${unit[3]}] | ');
    buffer.write('V: ${f0.format(V_d)} [${unit[0]}] | ');
    buffer.write('xMaxVd: ${f3.format(xMaxVd)} [${unit[3]}]\n');

    // * record capcity
    buffer.write(
      'Chord Axaial Capacity: ${f0.format(chordAxialCapacity)} [${unit[0]}]\n',
    );
    buffer.write('Lever Arm: ${f0.format(leverArm)} [${unit[4]}] | ');
    buffer.write('Mc: ${f0.format(Mc)} [${unit[2]}]\n');
    if (status[0]) {
      buffer.write('Moment Capacity: pass | ');
    } else {
      buffer.write('Moment Capacity: fail | ');
    }
  }
}
