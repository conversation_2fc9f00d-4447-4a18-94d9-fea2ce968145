import 'package:freezed_annotation/freezed_annotation.dart';
// import 'package:flutter/foundation.dart';
part 'pile_frictional_bored_input.freezed.dart';
part 'pile_frictional_bored_input.g.dart';

@freezed
abstract class PileFrictionalBoredInput with _$PileFrictionalBoredInput{
  const PileFrictionalBoredInput._();
  factory PileFrictionalBoredInput({
    @Default(50) double sptNValue,
    @Default(19) double soilUnitWeight,
    @Default(0.25) double kTan,
    @Default(3) double fos,
    @Default(45) double fcu,
    @Default(30) double maxPileLength,
    @Default(2000) double maxPileDiameter,
    @Default(1.65) double ratioOfBelloutDia,
    @Default(0.04) double maxSteelRatio,
    @Default(1000) double slsLoad,
    @Default(2000) double ulsLoad,
    @Default(200) double diaIncrement,
    @Default(false) bool useSelectColLoad, 
    @Default('1') String id, //will be overriden  as soon as new instance created
  }) = _PileFrictionalBoredInput;

  factory PileFrictionalBoredInput.fromJson(Map<String, Object?> json) =>
      _$PileFrictionalBoredInputFromJson(json);
}
