// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'steel_column_scheme_input_global.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$SteelColumnSchemeInputGlobal {

 String get id; String get steelGrade; double get unbracedLength;
/// Create a copy of SteelColumnSchemeInputGlobal
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$SteelColumnSchemeInputGlobalCopyWith<SteelColumnSchemeInputGlobal> get copyWith => _$SteelColumnSchemeInputGlobalCopyWithImpl<SteelColumnSchemeInputGlobal>(this as SteelColumnSchemeInputGlobal, _$identity);

  /// Serializes this SteelColumnSchemeInputGlobal to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is SteelColumnSchemeInputGlobal&&(identical(other.id, id) || other.id == id)&&(identical(other.steelGrade, steelGrade) || other.steelGrade == steelGrade)&&(identical(other.unbracedLength, unbracedLength) || other.unbracedLength == unbracedLength));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,steelGrade,unbracedLength);

@override
String toString() {
  return 'SteelColumnSchemeInputGlobal(id: $id, steelGrade: $steelGrade, unbracedLength: $unbracedLength)';
}


}

/// @nodoc
abstract mixin class $SteelColumnSchemeInputGlobalCopyWith<$Res>  {
  factory $SteelColumnSchemeInputGlobalCopyWith(SteelColumnSchemeInputGlobal value, $Res Function(SteelColumnSchemeInputGlobal) _then) = _$SteelColumnSchemeInputGlobalCopyWithImpl;
@useResult
$Res call({
 String id, String steelGrade, double unbracedLength
});




}
/// @nodoc
class _$SteelColumnSchemeInputGlobalCopyWithImpl<$Res>
    implements $SteelColumnSchemeInputGlobalCopyWith<$Res> {
  _$SteelColumnSchemeInputGlobalCopyWithImpl(this._self, this._then);

  final SteelColumnSchemeInputGlobal _self;
  final $Res Function(SteelColumnSchemeInputGlobal) _then;

/// Create a copy of SteelColumnSchemeInputGlobal
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? steelGrade = null,Object? unbracedLength = null,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,steelGrade: null == steelGrade ? _self.steelGrade : steelGrade // ignore: cast_nullable_to_non_nullable
as String,unbracedLength: null == unbracedLength ? _self.unbracedLength : unbracedLength // ignore: cast_nullable_to_non_nullable
as double,
  ));
}

}


/// Adds pattern-matching-related methods to [SteelColumnSchemeInputGlobal].
extension SteelColumnSchemeInputGlobalPatterns on SteelColumnSchemeInputGlobal {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _SteelColumnSchemeInputGlobal value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _SteelColumnSchemeInputGlobal() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _SteelColumnSchemeInputGlobal value)  $default,){
final _that = this;
switch (_that) {
case _SteelColumnSchemeInputGlobal():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _SteelColumnSchemeInputGlobal value)?  $default,){
final _that = this;
switch (_that) {
case _SteelColumnSchemeInputGlobal() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String steelGrade,  double unbracedLength)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _SteelColumnSchemeInputGlobal() when $default != null:
return $default(_that.id,_that.steelGrade,_that.unbracedLength);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String steelGrade,  double unbracedLength)  $default,) {final _that = this;
switch (_that) {
case _SteelColumnSchemeInputGlobal():
return $default(_that.id,_that.steelGrade,_that.unbracedLength);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String steelGrade,  double unbracedLength)?  $default,) {final _that = this;
switch (_that) {
case _SteelColumnSchemeInputGlobal() when $default != null:
return $default(_that.id,_that.steelGrade,_that.unbracedLength);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _SteelColumnSchemeInputGlobal extends SteelColumnSchemeInputGlobal {
   _SteelColumnSchemeInputGlobal({this.id = '1', this.steelGrade = 'S355', this.unbracedLength = 5.0}): super._();
  factory _SteelColumnSchemeInputGlobal.fromJson(Map<String, dynamic> json) => _$SteelColumnSchemeInputGlobalFromJson(json);

@override@JsonKey() final  String id;
@override@JsonKey() final  String steelGrade;
@override@JsonKey() final  double unbracedLength;

/// Create a copy of SteelColumnSchemeInputGlobal
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$SteelColumnSchemeInputGlobalCopyWith<_SteelColumnSchemeInputGlobal> get copyWith => __$SteelColumnSchemeInputGlobalCopyWithImpl<_SteelColumnSchemeInputGlobal>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$SteelColumnSchemeInputGlobalToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _SteelColumnSchemeInputGlobal&&(identical(other.id, id) || other.id == id)&&(identical(other.steelGrade, steelGrade) || other.steelGrade == steelGrade)&&(identical(other.unbracedLength, unbracedLength) || other.unbracedLength == unbracedLength));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,steelGrade,unbracedLength);

@override
String toString() {
  return 'SteelColumnSchemeInputGlobal(id: $id, steelGrade: $steelGrade, unbracedLength: $unbracedLength)';
}


}

/// @nodoc
abstract mixin class _$SteelColumnSchemeInputGlobalCopyWith<$Res> implements $SteelColumnSchemeInputGlobalCopyWith<$Res> {
  factory _$SteelColumnSchemeInputGlobalCopyWith(_SteelColumnSchemeInputGlobal value, $Res Function(_SteelColumnSchemeInputGlobal) _then) = __$SteelColumnSchemeInputGlobalCopyWithImpl;
@override @useResult
$Res call({
 String id, String steelGrade, double unbracedLength
});




}
/// @nodoc
class __$SteelColumnSchemeInputGlobalCopyWithImpl<$Res>
    implements _$SteelColumnSchemeInputGlobalCopyWith<$Res> {
  __$SteelColumnSchemeInputGlobalCopyWithImpl(this._self, this._then);

  final _SteelColumnSchemeInputGlobal _self;
  final $Res Function(_SteelColumnSchemeInputGlobal) _then;

/// Create a copy of SteelColumnSchemeInputGlobal
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? steelGrade = null,Object? unbracedLength = null,}) {
  return _then(_SteelColumnSchemeInputGlobal(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,steelGrade: null == steelGrade ? _self.steelGrade : steelGrade // ignore: cast_nullable_to_non_nullable
as String,unbracedLength: null == unbracedLength ? _self.unbracedLength : unbracedLength // ignore: cast_nullable_to_non_nullable
as double,
  ));
}


}

// dart format on
