// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'recommended_load_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$recommendedLoadControllerHash() =>
    r'3f55b4f3f4a9cf8b21e7005c79e725bae4aca1c2';

/// See also [RecommendedLoadController].
@ProviderFor(RecommendedLoadController)
final recommendedLoadControllerProvider = AutoDisposeAsyncNotifierProvider<
  RecommendedLoadController,
  List<RecommendedLoad>
>.internal(
  RecommendedLoadController.new,
  name: r'recommendedLoadControllerProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$recommendedLoadControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$RecommendedLoadController =
    AutoDisposeAsyncNotifier<List<RecommendedLoad>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
