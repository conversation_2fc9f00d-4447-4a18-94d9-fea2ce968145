// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'strzone_tables_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$strZoneTablesControllerHash() =>
    r'09b3512cd9cc8fbee232684caffdef11696fa905';

/// See also [StrZoneTablesController].
@ProviderFor(StrZoneTablesController)
final strZoneTablesControllerProvider = AutoDisposeAsyncNotifierProvider<
  StrZoneTablesController,
  List<StrZoneTable>
>.internal(
  StrZoneTablesController.new,
  name: r'strZoneTablesControllerProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$strZoneTablesControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$StrZoneTablesController =
    AutoDisposeAsyncNotifier<List<StrZoneTable>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
