// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'pile_frictional_bored_input_global.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$PileFrictionalBoredInputGlobal {

 double get colLoadFactor; String get id;
/// Create a copy of PileFrictionalBoredInputGlobal
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$PileFrictionalBoredInputGlobalCopyWith<PileFrictionalBoredInputGlobal> get copyWith => _$PileFrictionalBoredInputGlobalCopyWithImpl<PileFrictionalBoredInputGlobal>(this as PileFrictionalBoredInputGlobal, _$identity);

  /// Serializes this PileFrictionalBoredInputGlobal to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is PileFrictionalBoredInputGlobal&&(identical(other.colLoadFactor, colLoadFactor) || other.colLoadFactor == colLoadFactor)&&(identical(other.id, id) || other.id == id));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,colLoadFactor,id);

@override
String toString() {
  return 'PileFrictionalBoredInputGlobal(colLoadFactor: $colLoadFactor, id: $id)';
}


}

/// @nodoc
abstract mixin class $PileFrictionalBoredInputGlobalCopyWith<$Res>  {
  factory $PileFrictionalBoredInputGlobalCopyWith(PileFrictionalBoredInputGlobal value, $Res Function(PileFrictionalBoredInputGlobal) _then) = _$PileFrictionalBoredInputGlobalCopyWithImpl;
@useResult
$Res call({
 double colLoadFactor, String id
});




}
/// @nodoc
class _$PileFrictionalBoredInputGlobalCopyWithImpl<$Res>
    implements $PileFrictionalBoredInputGlobalCopyWith<$Res> {
  _$PileFrictionalBoredInputGlobalCopyWithImpl(this._self, this._then);

  final PileFrictionalBoredInputGlobal _self;
  final $Res Function(PileFrictionalBoredInputGlobal) _then;

/// Create a copy of PileFrictionalBoredInputGlobal
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? colLoadFactor = null,Object? id = null,}) {
  return _then(_self.copyWith(
colLoadFactor: null == colLoadFactor ? _self.colLoadFactor : colLoadFactor // ignore: cast_nullable_to_non_nullable
as double,id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,
  ));
}

}


/// Adds pattern-matching-related methods to [PileFrictionalBoredInputGlobal].
extension PileFrictionalBoredInputGlobalPatterns on PileFrictionalBoredInputGlobal {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _PileFrictionalBoredInputGlobal value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _PileFrictionalBoredInputGlobal() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _PileFrictionalBoredInputGlobal value)  $default,){
final _that = this;
switch (_that) {
case _PileFrictionalBoredInputGlobal():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _PileFrictionalBoredInputGlobal value)?  $default,){
final _that = this;
switch (_that) {
case _PileFrictionalBoredInputGlobal() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( double colLoadFactor,  String id)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _PileFrictionalBoredInputGlobal() when $default != null:
return $default(_that.colLoadFactor,_that.id);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( double colLoadFactor,  String id)  $default,) {final _that = this;
switch (_that) {
case _PileFrictionalBoredInputGlobal():
return $default(_that.colLoadFactor,_that.id);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( double colLoadFactor,  String id)?  $default,) {final _that = this;
switch (_that) {
case _PileFrictionalBoredInputGlobal() when $default != null:
return $default(_that.colLoadFactor,_that.id);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _PileFrictionalBoredInputGlobal extends PileFrictionalBoredInputGlobal {
   _PileFrictionalBoredInputGlobal({this.colLoadFactor = 1.0, this.id = '1'}): super._();
  factory _PileFrictionalBoredInputGlobal.fromJson(Map<String, dynamic> json) => _$PileFrictionalBoredInputGlobalFromJson(json);

@override@JsonKey() final  double colLoadFactor;
@override@JsonKey() final  String id;

/// Create a copy of PileFrictionalBoredInputGlobal
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$PileFrictionalBoredInputGlobalCopyWith<_PileFrictionalBoredInputGlobal> get copyWith => __$PileFrictionalBoredInputGlobalCopyWithImpl<_PileFrictionalBoredInputGlobal>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$PileFrictionalBoredInputGlobalToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _PileFrictionalBoredInputGlobal&&(identical(other.colLoadFactor, colLoadFactor) || other.colLoadFactor == colLoadFactor)&&(identical(other.id, id) || other.id == id));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,colLoadFactor,id);

@override
String toString() {
  return 'PileFrictionalBoredInputGlobal(colLoadFactor: $colLoadFactor, id: $id)';
}


}

/// @nodoc
abstract mixin class _$PileFrictionalBoredInputGlobalCopyWith<$Res> implements $PileFrictionalBoredInputGlobalCopyWith<$Res> {
  factory _$PileFrictionalBoredInputGlobalCopyWith(_PileFrictionalBoredInputGlobal value, $Res Function(_PileFrictionalBoredInputGlobal) _then) = __$PileFrictionalBoredInputGlobalCopyWithImpl;
@override @useResult
$Res call({
 double colLoadFactor, String id
});




}
/// @nodoc
class __$PileFrictionalBoredInputGlobalCopyWithImpl<$Res>
    implements _$PileFrictionalBoredInputGlobalCopyWith<$Res> {
  __$PileFrictionalBoredInputGlobalCopyWithImpl(this._self, this._then);

  final _PileFrictionalBoredInputGlobal _self;
  final $Res Function(_PileFrictionalBoredInputGlobal) _then;

/// Create a copy of PileFrictionalBoredInputGlobal
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? colLoadFactor = null,Object? id = null,}) {
  return _then(_PileFrictionalBoredInputGlobal(
colLoadFactor: null == colLoadFactor ? _self.colLoadFactor : colLoadFactor // ignore: cast_nullable_to_non_nullable
as double,id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

// dart format on
