import 'package:riverpod_annotation/riverpod_annotation.dart';

//presentation layer
import '../../domain_layer/column_scheme_data.dart';
import '../../domain_layer/pile_frictional_bored_input.dart';
import '../screen/homescreen.dart';

// domain layer

part 'pile_frictional_bored_input_controller.g.dart';

@riverpod
class PileFrictionalBoredInputController
    extends _$PileFrictionalBoredInputController {
  @override
  FutureOr<PileFrictionalBoredInput> build() async {
    // print('Build: Column Scheme Input Global');
    PileFrictionalBoredInput pileFrictionalBoredInput =
        await ref
            .read(appDatabaseControllerProvider.notifier)
            .queryPileFrictionalBoredInput();

    final data1 = ref.watch(columnSchemeDataControllerProvider);
    final data2 = ref.watch(pileFrictionalBoredInputGlobalControllerProvider);
    final data3 = ref.watch(globalDataControllerProvider);
    return data1.when(
      data: (colData) async {
        return data2.when(
          data: (inputGlobal) async {
            return data3.when(
              data: (globalData) async {
                //* Validate the soil unit weight 
                pileFrictionalBoredInput = pileFrictionalBoredInput.copyWith(
                  soilUnitWeight: globalData.soilUnitWeight,
                );
                //* Validate load from selected column (if used)
                final selectedColumn = colData.firstWhere(
                  (scheme) => scheme.isSelected,
                  orElse: () => ColumnSchemeData(),
                );
                if (pileFrictionalBoredInput.useSelectColLoad) {
                  if ((inputGlobal.colLoadFactor ?? 0) == 0) {
                    inputGlobal = inputGlobal.copyWith(colLoadFactor: 1.0);
                    // update it meanwhile
                    await ref
                        .read(
                          pileFrictionalBoredInputGlobalControllerProvider
                              .notifier,
                        )
                        .updateTable(colLoadFactor: 1.0);
                  }
                  pileFrictionalBoredInput = pileFrictionalBoredInput.copyWith(
                    slsLoad: selectedColumn.slsLoad / inputGlobal.colLoadFactor,
                    ulsLoad: selectedColumn.ulsLoad / inputGlobal.colLoadFactor,
                  );
                }
                return pileFrictionalBoredInput;
              },
              error: (error, stackTrace) => PileFrictionalBoredInput(),
              loading: () => PileFrictionalBoredInput(),
            );
          },
          error: (error, stackTrace) => PileFrictionalBoredInput(),
          loading: () => PileFrictionalBoredInput(),
        );
      },
      error: (error, stackTrace) => PileFrictionalBoredInput(),
      loading: () => PileFrictionalBoredInput(),
    );
  }

  Future<void> updateTable({
    double? sptNValue,
    double? soilUnitWeight,
    double? kTan,
    double? fos,
    double? fcu,
    double? maxPileLength,
    double? maxPileDiameter,
    double? ratioOfBelloutDia,
    double? maxSteelRatio,
    double? slsLoad,
    double? ulsLoad,
    double? diaIncrement,
    bool? useSelectColLoad,
  }) async {
    final x = await future;
    PileFrictionalBoredInput newState = x.copyWith(
      id: '1',
      sptNValue: sptNValue ?? x.sptNValue,
      soilUnitWeight: soilUnitWeight ?? x.soilUnitWeight,
      kTan: kTan ?? x.kTan,
      fos: fos ?? x.fos,
      fcu: fcu ?? x.fcu,
      maxPileLength: maxPileLength ?? x.maxPileLength,
      maxPileDiameter: maxPileDiameter ?? x.maxPileDiameter,
      ratioOfBelloutDia: ratioOfBelloutDia ?? x.ratioOfBelloutDia,
      maxSteelRatio: maxSteelRatio ?? x.maxSteelRatio,
      slsLoad: slsLoad ?? x.slsLoad,
      ulsLoad: ulsLoad ?? x.ulsLoad,
      diaIncrement: diaIncrement ?? x.diaIncrement,
      useSelectColLoad: useSelectColLoad ?? x.useSelectColLoad,
    );
    // Update the state only if there are changes
    state = AsyncData(newState);
  }
}
