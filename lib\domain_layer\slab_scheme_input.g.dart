// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'slab_scheme_input.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_SlabSchemeInput _$SlabSchemeInputFromJson(Map<String, dynamic> json) =>
    _SlabSchemeInput(
      id: json['id'] as String? ?? '1',
      span: (json['span'] as num?)?.toDouble() ?? 5.0,
      fcu: (json['fcu'] as num?)?.toDouble() ?? 45.0,
      cover: (json['cover'] as num?)?.toDouble() ?? 35.0,
      mainKValue: (json['mainKValue'] as num?)?.toDouble() ?? 0.156,
      mainSteelRatio: (json['mainSteelRatio'] as num?)?.toDouble() ?? 0.04,
      minS: (json['minS'] as num?)?.toInt() ?? 100,
      maxS: (json['maxS'] as num?)?.toInt() ?? 300,
      maxDepth: (json['maxDepth'] as num?)?.toDouble() ?? 500,
      minDepth: (json['minDepth'] as num?)?.toDouble() ?? 150.0,
      maxLayers: (json['maxLayers'] as num?)?.toInt() ?? 2,
      spanIncreament: (json['spanIncreament'] as num?)?.toDouble() ?? 0.5,
      iterationSteps: (json['iterationSteps'] as num?)?.toInt() ?? 4,
      usage: json['usage'] as String? ?? '',
    );

Map<String, dynamic> _$SlabSchemeInputToJson(_SlabSchemeInput instance) =>
    <String, dynamic>{
      'id': instance.id,
      'span': instance.span,
      'fcu': instance.fcu,
      'cover': instance.cover,
      'mainKValue': instance.mainKValue,
      'mainSteelRatio': instance.mainSteelRatio,
      'minS': instance.minS,
      'maxS': instance.maxS,
      'maxDepth': instance.maxDepth,
      'minDepth': instance.minDepth,
      'maxLayers': instance.maxLayers,
      'spanIncreament': instance.spanIncreament,
      'iterationSteps': instance.iterationSteps,
      'usage': instance.usage,
    };
