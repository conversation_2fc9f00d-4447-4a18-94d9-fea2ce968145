// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'loading_tables_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$loadingTablesControllerHash() =>
    r'0a1f5e10c691b8df3359af92832baffab315c1e8';

/// See also [LoadingTablesController].
@ProviderFor(LoadingTablesController)
final loadingTablesControllerProvider = AutoDisposeAsyncNotifierProvider<
  LoadingTablesController,
  List<LoadingTable>
>.internal(
  LoadingTablesController.new,
  name: r'loadingTablesControllerProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$loadingTablesControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$LoadingTablesController =
    AutoDisposeAsyncNotifier<List<LoadingTable>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
