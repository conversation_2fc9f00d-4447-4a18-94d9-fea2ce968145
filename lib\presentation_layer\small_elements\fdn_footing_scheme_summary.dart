import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:structify/domain_layer/mixin/mixin_rc_str.dart';
import 'package:structify/domain_layer/mixin/mixin_str_general_cals.dart';
import 'package:structify/domain_layer/mixin/mixin_tools_for_ui.dart';
import 'package:structify/domain_layer/footing_scheme_data.dart';
import 'package:structify/domain_layer/global_data.dart';
import 'package:structify/domain_layer/slab_scheme_data.dart';
import 'package:structify/presentation_layer/small_elements/sketch/draw_slab_scheme_sketch.dart';
import 'package:structify/presentation_layer/small_elements/button/function_button.dart';

//below for printing
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:printing/printing.dart';
// import 'dart:typed_data';
// import 'dart:ui';
// import 'package:structify/presentation_layer/small_elements/loadcals_summary_ui.dart';

//domain layer
import '../../domain_layer/preferences.dart';

// presentation layer
import '../screen/homescreen.dart';
// import '../small_elements/custom_stateful_double_input.dart';
// import '../small_elements/custom_stateful_text_input.dart';
// import '../small_elements/global_data_ui.dart';

//misc
import 'sketch/draw_footing_scheme_plan_sketch.dart';
import 'sketch/draw_footing_scheme_sketch.dart';
import 'button/selection_button.dart';

class FootingSchemeSummary extends ConsumerStatefulWidget {
  const FootingSchemeSummary({
    this.isExpanded,
    this.maxHeight,
    this.minHeight,
    super.key,
  });

  final bool? isExpanded;
  final double? maxHeight;
  final double? minHeight;

  @override
  ConsumerState<ConsumerStatefulWidget> createState() =>
      _FootingSchemeSummaryState();
}

class _FootingSchemeSummaryState extends ConsumerState<FootingSchemeSummary>
    with WidgetsBindingObserver, MixinToolsForUI, StrGeneralCals, RCStrHK {
  late bool _isExpanded;
  late double _maxHeight;
  late double _minHeight;

  late ScrollController _scrollController;

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void initState() {
    WidgetsBinding.instance.addObserver(this);
    _isExpanded = widget.isExpanded ?? false;
    _maxHeight = widget.maxHeight ?? 300;
    _minHeight = widget.minHeight ?? 50;
    _scrollController = ScrollController();
    // _updateHeights();

    super.initState();
  }

  @override
  void didChangeDependencies() {
    _updateHeights();
    super.didChangeDependencies();
    // Update heights when dependencies change, including after initState
  }

  @override
  void didChangeMetrics() {
    // This method is called when the metrics change (like resizing the window)
    setState(() {
      _updateHeights();
    });
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    final footingSchemeTables = ref.watch(footingSchemeDataControllerProvider);
    final globalData = ref.watch(globalDataControllerProvider);

    final TextStyle subtitleStyle = textTheme.labelMedium!;
    final TextStyle bodyStyle = textTheme.bodySmall!;

    // return Placeholder();

    return footingSchemeTables.when(
      data: (tables) {
        //start reutnr real stuffs when loading tables available
        return globalData.when(
          //also, start reutnr real stuffs when global data tables available
          data: (data) {
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding: const EdgeInsets.fromLTRB(10, 0, 10, 0),
                  child: Divider(),
                ),
                Padding(
                  padding: const EdgeInsets.fromLTRB(10, 0, 0, 0),
                  child: Align(
                    alignment: Alignment.centerLeft,
                    child: Wrap(
                      crossAxisAlignment: WrapCrossAlignment.center,
                      children: [
                        IconButton(
                          icon: Icon(
                            _isExpanded ? Icons.expand_less : Icons.expand_more,
                          ),
                          color: colorScheme.onSurface,
                          onPressed: () {
                            setState(() {
                              _isExpanded = !_isExpanded;
                            });
                          },
                        ),
                        Text(
                          'Footing Scheme Summary ',
                          style: textTheme.titleLarge!.copyWith(
                            color: colorScheme.onSurface,
                          ),
                        ),
                        Container(
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(5.0),
                            color: colorScheme.surfaceContainer.withAlpha(225),
                            border: Border.all(
                              color: Colors.black.withAlpha(125),
                            ),
                          ),
                          child: Padding(
                            padding: const EdgeInsets.all(2.0),
                            child: Text(
                              '(Total: ${tables.length})',
                              style: textTheme.labelMedium!.copyWith(
                                color: colorScheme.onSurfaceVariant.withAlpha(
                                  225,
                                ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),

                Padding(
                  padding: const EdgeInsets.fromLTRB(10, 0, 10, 0),
                  child: Divider(),
                ),

                ClipRect(
                  child: ConstrainedBox(
                    constraints: BoxConstraints(
                      maxHeight: _isExpanded ? _maxHeight : 0,
                    ),
                    child: Column(
                      children: [
                        Builder(
                          builder: (context) {
                            if (tables.isEmpty) {
                              return Padding(
                                padding: const EdgeInsets.fromLTRB(
                                  10,
                                  0,
                                  10,
                                  0,
                                ),
                                child: Container(
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(5.0),
                                    color: colorScheme.errorContainer.withAlpha(
                                      100,
                                    ),
                                    border: Border.all(
                                      width: 0.5,
                                      color: colorScheme.error.withAlpha(100),
                                    ),
                                  ),
                                  child: Padding(
                                    padding: const EdgeInsets.all(4.0),
                                    child: Text(
                                      'No Footing Scheme Data',
                                      style: textTheme.labelMedium!.copyWith(
                                        color: colorScheme.error,
                                      ),
                                    ),
                                  ),
                                ),
                              );
                            } else {
                              return SizedBox(height: 0, width: 0);
                            }
                          },
                        ),

                        Flexible(
                          child: Scrollbar(
                            controller: _scrollController,
                            thumbVisibility: true,
                            trackVisibility: false,
                            child: DefaultTextStyle(
                              style: textTheme.bodySmall!.copyWith(
                                color: colorScheme.onSurface,
                              ),
                              child: ListView.builder(
                                controller: _scrollController,
                                itemCount: tables.length,
                                itemBuilder: (context, index) {
                                  late Widget footingStatus;
                                  final calsLogMap = extractCalsLog(
                                    tables[index].calsLog,
                                  );
                                  final double maxDeflection = getDoubleValue(
                                    calsLogMap,
                                    'Max Deflection (Cantilever)',
                                  );
                                  final double deflectionLimit = getDoubleValue(
                                    calsLogMap,
                                    'Deflection Limit (Cantilever)',
                                  );

                                  final bool designStatus =
                                      !RegExp(
                                        r'fail',
                                        caseSensitive: false,
                                      ).hasMatch(tables[index].calsLog);

                                  final int containerOpacity = 175;
                                  final int textOpacity = 255;

                                  if (designStatus) {
                                    footingStatus = Container(
                                      decoration: BoxDecoration(
                                        borderRadius: BorderRadius.circular(
                                          4.0,
                                        ),
                                        color: colorScheme.primary.withAlpha(
                                          containerOpacity,
                                        ),
                                        border: Border.all(
                                          width: 0.5,
                                          color: colorScheme.primary.withAlpha(
                                            containerOpacity,
                                          ),
                                        ),
                                      ),
                                      child: Padding(
                                        padding: const EdgeInsets.all(4.0),
                                        child: Text(
                                          'Pass',
                                          style: textTheme.bodySmall!.copyWith(
                                            color: colorScheme.onPrimary
                                                .withAlpha(textOpacity),
                                          ),
                                        ),
                                      ),
                                    );
                                  } else {
                                    footingStatus = Container(
                                      decoration: BoxDecoration(
                                        borderRadius: BorderRadius.circular(
                                          4.0,
                                        ),
                                        color: colorScheme.error.withAlpha(
                                          containerOpacity,
                                        ),
                                        border: Border.all(
                                          width: 0.5,
                                          color: colorScheme.onError.withAlpha(
                                            containerOpacity,
                                          ),
                                        ),
                                      ),
                                      child: Padding(
                                        padding: const EdgeInsets.all(4.0),
                                        child: Text(
                                          'Fail',
                                          style: textTheme.bodySmall!.copyWith(
                                            color: colorScheme.onError
                                                .withAlpha(textOpacity),
                                          ),
                                        ),
                                      ),
                                    );
                                  }

                                  late List<String> unit;
                                  switch (data.unit) {
                                    case 'metrics':
                                      unit = PreferredUnit.metrics;
                                      break;
                                    case 'imperial':
                                      unit = PreferredUnit.imperial;
                                      break;
                                    default:
                                      unit = PreferredUnit.metrics;
                                  }

                                  return Padding(
                                    padding: const EdgeInsets.fromLTRB(
                                      8.0,
                                      4.0,
                                      8.0,
                                      4.0,
                                    ),
                                    child: Column(
                                      children: [
                                        Row(
                                          mainAxisSize: MainAxisSize.min,
                                          children: [
                                            Column(
                                              children: [
                                                FunctionButton(
                                                  labelText: ' ${index + 1} ',
                                                  pressedColor: colorScheme
                                                      .tertiary
                                                      .withAlpha(150),
                                                  onTap: (value) async {
                                                    final tables = await ref.read(
                                                      footingSchemeDataControllerProvider
                                                          .future,
                                                    );
                                                    await ref
                                                        .read(
                                                          footingSchemeDataControllerProvider
                                                              .notifier,
                                                        )
                                                        .toggleSelectScheme(
                                                          tables[index]
                                                              .footingSchemeDataId,
                                                        );
                                                  },
                                                ),
                                                SizedBox(height: 5.0),
                                                SelectionButton(
                                                  labelIcon: Icon(
                                                    Icons.check,
                                                    color:
                                                        tables[index].isSelected
                                                            ? colorScheme
                                                                .onTertiary
                                                            : colorScheme
                                                                .onSurface
                                                                .withAlpha(100),
                                                  ),
                                                  labelText: '',
                                                  // pressedColor:
                                                  //     colorScheme.tertiary,
                                                  bgColor:
                                                      tables[index].isSelected
                                                          ? colorScheme.tertiary
                                                          : colorScheme
                                                              .surfaceContainer
                                                              .withAlpha(100),

                                                  onTap: (value) async {
                                                    final tables = await ref.read(
                                                      footingSchemeDataControllerProvider
                                                          .future,
                                                    );
                                                    await ref
                                                        .read(
                                                          footingSchemeDataControllerProvider
                                                              .notifier,
                                                        )
                                                        .toggleSelectScheme(
                                                          tables[index]
                                                              .footingSchemeDataId,
                                                        );
                                                  },
                                                ),
                                                SizedBox(height: 5.0),
                                                FunctionButton(
                                                  labelText: 'Show Cals',
                                                  pressedColor: colorScheme
                                                      .tertiary
                                                      .withAlpha(150),
                                                  onTap: (value) async {
                                                    _presentCalsRecord(index);
                                                  },
                                                ),
                                              ],
                                            ),

                                            IconButton(
                                              icon: Icon(
                                                Icons.delete,
                                                color: colorScheme.onSurface,
                                              ),
                                              onPressed: () {
                                                ref
                                                    .read(
                                                      footingSchemeDataControllerProvider
                                                          .notifier,
                                                    )
                                                    .deleteTable(
                                                      tables[index]
                                                          .footingSchemeDataId,
                                                    );
                                              },
                                            ),
                                            Flexible(
                                              child: DefaultTextStyle(
                                                style: bodyStyle,
                                                child: Row(
                                                  children: [
                                                    footingStatus,
                                                    SizedBox(width: 10.0),
                                                    Column(
                                                      mainAxisSize:
                                                          MainAxisSize.min,
                                                      crossAxisAlignment:
                                                          CrossAxisAlignment
                                                              .start,
                                                      children: [
                                                        Text(
                                                          'Size [${unit[4]}]:',
                                                          style: subtitleStyle,
                                                        ),
                                                        Text(
                                                          'fcu [${unit[5]}]:',
                                                          style: subtitleStyle,
                                                        ),

                                                        Text(
                                                          'Ground Type :',
                                                          style: subtitleStyle,
                                                        ),
                                                        Builder(
                                                          builder: (context) {
                                                            if (tables[index]
                                                                    .groundType ==
                                                                'Soil') {
                                                              return Text(
                                                                'Soil N Value:',
                                                                style:
                                                                    subtitleStyle,
                                                              );
                                                            } else {
                                                              return Text(
                                                                'Safe Bearing Capacity:',
                                                                style:
                                                                    subtitleStyle,
                                                              );
                                                            }
                                                          },
                                                        ),
                                                        SizedBox(height: 10.0),
                                                        Text(
                                                          'Footing Top Level [${unit[3]}]:',
                                                          style: subtitleStyle,
                                                        ),
                                                        Text(
                                                          'Water Table Level [${unit[3]}]:',
                                                          style: subtitleStyle,
                                                        ),
                                                        Text(
                                                          'SLS Load [${unit[0]}]:',
                                                          style: subtitleStyle,
                                                        ),
                                                        Text(
                                                          'ULS Load [${unit[0]}]:',
                                                          style: subtitleStyle,
                                                        ),
                                                        SizedBox(height: 10.0),
                                                        Text(
                                                          'Top Bar (Compression): ',
                                                          style: subtitleStyle,
                                                        ),
                                                        Text(
                                                          'Bottom Bar (Tension): ',
                                                          style: subtitleStyle,
                                                        ),
                                                        Text(
                                                          'Links: ',
                                                          style: subtitleStyle,
                                                        ),
                                                        Text(
                                                          'Punching Links: ',
                                                          style: subtitleStyle,
                                                        ),
                                                        Text(
                                                          'Deflection: [${unit[4]}]',
                                                          style: subtitleStyle,
                                                        ),
                                                      ],
                                                    ),

                                                    SizedBox(width: 10.0),
                                                    Column(
                                                      mainAxisSize:
                                                          MainAxisSize.min,
                                                      crossAxisAlignment:
                                                          CrossAxisAlignment
                                                              .start,
                                                      children: [
                                                        Text(
                                                          '${NumberFormat('0').format(tables[index].size)}x${NumberFormat('0').format(tables[index].size)}',
                                                        ),
                                                        Text(
                                                          '${NumberFormat('0').format(tables[index].fcu)}',
                                                        ),
                                                        Text(
                                                          '${tables[index].groundType}',
                                                        ),
                                                        Builder(
                                                          builder: (context) {
                                                            if (tables[index]
                                                                    .groundType ==
                                                                'Soil') {
                                                              return Text(
                                                                '${NumberFormat('0').format(tables[index].soilNValue)}',
                                                              );
                                                            } else {
                                                              return Text(
                                                                '${NumberFormat('0').format(tables[index].rockCapacity)}',
                                                              );
                                                            }
                                                          },
                                                        ),
                                                        SizedBox(height: 10.0),
                                                        Text(
                                                          '${NumberFormat('0.000').format(tables[index].footingTopLevel)}',
                                                        ),
                                                        Text(
                                                          '${NumberFormat('0.000').format(tables[index].waterTableLevel)}',
                                                        ),
                                                        Text(
                                                          '${NumberFormat('0').format(tables[index].slsLoad)}',
                                                        ),
                                                        Text(
                                                          '${NumberFormat('0').format(tables[index].ulsLoad)}',
                                                        ),
                                                        SizedBox(height: 10.0),
                                                        Text(
                                                          '${tables[index].mainTopBar}',
                                                        ),
                                                        Text(
                                                          '${tables[index].mainBottomBar}',
                                                        ),
                                                        Text(
                                                          '${tables[index].mainLinks}',
                                                        ),

                                                        Text(
                                                          '${tables[index].punchingLinks}@1.5d from col face',
                                                        ),
                                                        Text(
                                                          '${getDoubleValue(extractCalsLog(tables[index].calsLog), 'Max Deflection (Cantilever)')}',
                                                        ),
                                                      ],
                                                    ),
                                                    SizedBox(width: 5.0),
                                                    GestureDetector(
                                                      onTap: () async {
                                                        await showDialog(
                                                          context: context,
                                                          builder: (context) {
                                                            return Dialog(
                                                              backgroundColor:
                                                                  colorScheme
                                                                      .surfaceContainer,
                                                              child: SizedBox(
                                                                width: 550,
                                                                height: 550,
                                                                child: DrawFootingSchemeSketch(
                                                                  sketchWidth:
                                                                      500,
                                                                  sketchHeight:
                                                                      500,
                                                                  index: index,
                                                                  // fontSize: 9.0,
                                                                ),
                                                              ),
                                                            );
                                                          },
                                                        );
                                                      },
                                                      child:
                                                          DrawFootingSchemeSketch(
                                                            sketchWidth: 150,
                                                            sketchHeight: 150,
                                                            index: index,
                                                          ),
                                                    ),
                                                    SizedBox(width: 5.0),
                                                    GestureDetector(
                                                      onTap: () async {
                                                        await showDialog(
                                                          context: context,
                                                          builder: (context) {
                                                            return Dialog(
                                                              backgroundColor:
                                                                  colorScheme
                                                                      .surfaceContainer,
                                                              child: SizedBox(
                                                                width: 550,
                                                                height: 550,
                                                                child: DrawFootingSchemePlanSketch(
                                                                  sketchWidth:
                                                                      500,
                                                                  sketchHeight:
                                                                      500,
                                                                  index: index,
                                                                  // fontSize: 9.0,
                                                                ),
                                                              ),
                                                            );
                                                          },
                                                        );
                                                      },
                                                      child:
                                                          DrawFootingSchemePlanSketch(
                                                            sketchWidth: 150,
                                                            sketchHeight: 150,
                                                            index: index,
                                                          ),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                            ),
                                            SizedBox(width: 5.0),
                                          ],
                                        ),
                                        Padding(
                                          padding: const EdgeInsets.fromLTRB(
                                            10,
                                            0,
                                            10,
                                            0,
                                          ),
                                          child: Divider(
                                            thickness: 0.2,
                                            color: colorScheme.primary,
                                          ),
                                        ),
                                      ],
                                    ),
                                  );
                                },
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            );
          },
          error: (error, stackTrace) => Text(error.toString()),
          loading: () => CircularProgressIndicator(),
        );
      },
      error: (error, stackTrace) => Text(error.toString()),
      loading: () => CircularProgressIndicator(),
    );
  }

  Future<void> _presentCalsRecord(int index) async {
    final globalData = await ref.read(globalDataControllerProvider.future);
    final schemes = await ref.read(footingSchemeDataControllerProvider.future);
    late List<String> unit;
    switch (globalData.unit) {
      case 'metrics':
        unit = PreferredUnit.metrics;
        break;
      case 'imperial':
        unit = PreferredUnit.imperial;
        break;
      default:
        unit = PreferredUnit.metrics;
    }
    // 1. get the record first
    Map<String, List<dynamic>> result = extractCalsLog(schemes[index].calsLog);

    final footingCals = _writeSlabResult(
      globalData,
      result, // input the main beam result
      schemes,
      index,
      unit,
    );

    final f0 = NumberFormat('0'),
        f1 = NumberFormat('0.0'),
        f3 = NumberFormat('0.000');

    await showDialog(
      context: context,
      builder: (context) {
        ColorScheme colorScheme = Theme.of(context).colorScheme;
        TextTheme textTheme = Theme.of(context).textTheme;
        Color bgColor = colorScheme.surfaceContainer;
        Color onBgColor = colorScheme.onSurface;
        Color titleBgColor = colorScheme.primary.withAlpha(150);
        Color titleOnBgColor = colorScheme.onPrimary;
        TextStyle titleTextStyle = textTheme.labelSmall!.copyWith(
          color: titleOnBgColor,
        );
        ScrollController scrollController = ScrollController();

        const double maxH = 600;
        const double maxW = 600;

        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(15),
          ),
          backgroundColor: bgColor,
          child: ConstrainedBox(
            constraints: const BoxConstraints(maxWidth: maxW, maxHeight: maxH),
            child: Column(
              children: [
                Align(
                  alignment: Alignment.centerRight,
                  child: Padding(
                    padding: const EdgeInsets.fromLTRB(0, 10, 10, 0),
                    child: FunctionButton(
                      bgColor: titleBgColor,
                      labelTextColor: titleOnBgColor,
                      labelIcon: Icon(
                        Icons.print_outlined,
                        color: titleOnBgColor.withAlpha(175),
                      ),
                      labelText: '',
                      onTap: (value) {
                        _exportListToPdf(
                          context,
                          ref,
                          footingCals.toString().split(RegExp(r'\r?\n')),
                        );
                      },
                    ),
                  ),
                ),

                Scrollbar(
                  controller: scrollController,
                  thumbVisibility: true,
                  trackVisibility: false,
                  child: ConstrainedBox(
                    constraints: const BoxConstraints(
                      maxWidth: maxW,
                      maxHeight: maxH - 70,
                    ),
                    child: SingleChildScrollView(
                      controller: scrollController,
                      child: Padding(
                        padding: const EdgeInsets.fromLTRB(25.0, 5, 25.0, 5),
                        child: DefaultTextStyle(
                          style: textTheme.labelMedium!.copyWith(
                            color: onBgColor,
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              Container(
                                decoration: BoxDecoration(
                                  color: colorScheme.tertiary,
                                  shape: BoxShape.circle,
                                  border: Border.all(color: titleOnBgColor),
                                ),
                                child: Padding(
                                  padding: const EdgeInsets.all(12.0),
                                  child: Text(
                                    '${index + 1}',
                                    style: titleTextStyle.copyWith(
                                      fontSize: 24,
                                      color: colorScheme.onTertiary,
                                    ),
                                  ),
                                ),
                              ),
                              SizedBox(width: 10),
                              Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Container(
                                    decoration: BoxDecoration(
                                      color: titleBgColor,
                                      shape: BoxShape.rectangle,
                                      borderRadius: BorderRadius.circular(10.0),
                                      border: Border.all(color: titleOnBgColor),
                                    ),
                                    child: Padding(
                                      padding: const EdgeInsets.all(4.0),
                                      child: Text(
                                        'Size:\n${f0.format(schemes[index].size)}(W)x${f0.format(schemes[index].size)}(L)}x${f0.format(schemes[index].strZone)}(D)[${unit[4]}]',
                                        style: titleTextStyle,
                                      ),
                                    ),
                                  ),
                                  SizedBox(width: 5),
                                  Container(
                                    decoration: BoxDecoration(
                                      color: titleBgColor,
                                      shape: BoxShape.rectangle,
                                      borderRadius: BorderRadius.circular(10.0),
                                      border: Border.all(color: titleOnBgColor),
                                    ),
                                    child: Padding(
                                      padding: const EdgeInsets.all(4.0),
                                      child: Text(
                                        'Ground Type :\n${schemes[index].groundType} ',
                                        style: titleTextStyle,
                                      ),
                                    ),
                                  ),
                                  SizedBox(width: 5),
                                  Container(
                                    decoration: BoxDecoration(
                                      color: titleBgColor,
                                      shape: BoxShape.rectangle,
                                      borderRadius: BorderRadius.circular(10.0),
                                      border: Border.all(color: titleOnBgColor),
                                    ),
                                    child: Padding(
                                      padding: const EdgeInsets.all(4.0),
                                      child: Text(
                                        'SLS Load [${unit[0]}]:\n${schemes[index].slsLoad} ',
                                        style: titleTextStyle,
                                      ),
                                    ),
                                  ),
                                  SizedBox(width: 5),
                                  Container(
                                    decoration: BoxDecoration(
                                      color: titleBgColor,
                                      shape: BoxShape.rectangle,
                                      borderRadius: BorderRadius.circular(10.0),
                                      border: Border.all(color: titleOnBgColor),
                                    ),
                                    child: Padding(
                                      padding: const EdgeInsets.all(4.0),
                                      child: Text(
                                        'ULS Load [${unit[0]}]:\n${schemes[index].ulsLoad} ',
                                        style: titleTextStyle,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                              SizedBox(height: 15),
                              Container(
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(10.0),
                                  border: Border.all(color: onBgColor),
                                ),
                                child: Padding(
                                  padding: const EdgeInsets.all(8.0),
                                  child: Text(footingCals.toString()),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  StringBuffer _writeSlabResult(
    GlobalData globalData,
    Map<String, List<dynamic>> result,
    List<FootingSchemeData> schemes,
    int index,
    List<String> unit,
  ) {
    StringBuffer buffer = StringBuffer();
    double tempDouble = 0.0, tempDouble2 = 0.0, tempDouble3 = 0.0;
    int tempInt = 0;
    String tempString = '';
    final NumberFormat f0 = NumberFormat('0'),
        f1 = NumberFormat('0.0'),
        f3 = NumberFormat('0.000'),
        f5 = NumberFormat('0.00000');
    final FootingSchemeData scheme = schemes[index];

    //* Calculations
    final double pSLS =
            scheme.slsLoad /
                (scheme.size * scheme.size * pow(10, -6)) + // point load
            globalData.rcUnitWeight * scheme.strZone * pow(10, -3),
        pcSLS = getDoubleValue(result, 'Ground Capacity (SLS)'),
        pULS =
            (scheme.ulsLoad / (scheme.size * scheme.size * pow(10, -6))) +
            globalData.sdlFactor *
                scheme.strZone *
                pow(10, -3) *
                globalData.rcUnitWeight,
        span = (scheme.size - scheme.columnSize) / 2 / 1000;
    double uo, dist, po;
    bool isSubmerged =
        scheme.footingTopLevel - scheme.strZone / 1000 <=
        scheme.waterTableLevel;
    //* presentation
    buffer.write('------------------------------\n');
    buffer.write('Footing\n');
    buffer.write(
      '(${f0.format(scheme.size)}(W)x${f0.format(scheme.size)}(L)x${f0.format(scheme.strZone)}(D))\n',
    );
    buffer.write('------------------------------\n');
    buffer.write('*******************\n');
    buffer.write('General Info\n');
    buffer.write('*******************\n');
    buffer.write('Column Size: ${f0.format(scheme.columnSize)} [${unit[4]}]\n');
    buffer.write('SLS Load: ${f0.format(scheme.slsLoad)} [${unit[0]}]\n');
    buffer.write('ULS Load: ${f0.format(scheme.ulsLoad)} [${unit[0]}]\n');
    buffer.write('\n');

    buffer.write('Soil Type: ${scheme.groundType}\n');
    if (scheme.groundType == 'Soil') {
      buffer.write('Soil N Value: ${f0.format(scheme.soilNValue)}\n');
      buffer.write(
        'Water Table Level: ${f3.format(scheme.waterTableLevel)} [mPD]\n',
      );
      buffer.write(
        'Footing Top Level: ${f3.format(scheme.footingTopLevel)} [mPD]\n',
      );
      buffer.write('Submerged? ${isSubmerged ? 'Yes' : 'No'}\n');
    } else {
      buffer.write(
        'Safe Bearing Capacity: ${f0.format(scheme.rockCapacity)} [${unit[1]}]\n',
      );
    }

    buffer.write('*******************\n');
    buffer.write('Loading\n');
    buffer.write('*******************\n');
    //* Load in kN and  Column Size

    //* sls ground pressure
    buffer.write('SLS Ground pressure, p\n');
    buffer.write('= SLS Load / Footing Size^2 + Self-weight\n');
    buffer.write(
      '= ${f0.format(scheme.slsLoad)}/ ${f1.format(scheme.size / 1000)}^2 + ${f1.format(globalData.rcUnitWeight)}*${f3.format(scheme.strZone / 1000)}\n',
    );
    buffer.write('= ${f0.format(pSLS)} [${unit[1]}]\n');

    //* uls ground pressure
    buffer.write('\nULS Ground Pressure,w\n');
    buffer.write('= ULS Load / Footing Size^2 + Footing Self-weight\n');
    buffer.write(
      '= ${f0.format(scheme.ulsLoad)}/ ${f3.format(scheme.size / 1000)}^2 + ${f1.format(globalData.sdlFactor)}*${f3.format(scheme.strZone / 1000)}* ${f1.format(globalData.rcUnitWeight)}\n',
    );
    buffer.write('= ${f0.format(pULS)} [${unit[1]}]\n');

    //* span
    buffer.write('\nSpan, L\n');
    buffer.write('= (Footing Size - Column Size)/2\n');
    buffer.write(
      '= (${f0.format(scheme.size)} - ${f0.format(scheme.columnSize)})/2/1000\n',
    );
    buffer.write('=${span} [${unit[3]}]\n');

    //*moment and shear
    buffer.write('\nM\n= wL^2/2\n= ');
    buffer.write('${f0.format(getDoubleValue(result, 'M'))} [${unit[2]}]\n');

    buffer.write('\nV\n= wL\n= ');
    buffer.write('${f0.format(getDoubleValue(result, 'V'))} [${unit[0]}]\n');

    //* check SLS ground pressure
    buffer.write('*******************\n');
    buffer.write('Check SLS State\n');
    buffer.write('*******************\n');
    buffer.write('Ground Type = ');
    if (scheme.groundType == 'Soil') {
      buffer.write('Soil\n');
      buffer.write('Soil N Value = ');
      buffer.write('${f0.format(scheme.soilNValue)}\n');
      buffer.write('Submerged? ${isSubmerged ? 'Yes' : 'No'}\n');
      buffer.write('>> Ground Capacity, pc = ');
      buffer.write('${f0.format(pcSLS)} [${unit[1]}]\n');
      buffer.write('(from code and based on Soil N Value)\n');
    } else {
      buffer.write('Other\n');
      buffer.write('Ground Capacity, pc = ');
      buffer.write('${f0.format(pcSLS)} [${unit[1]}]\n');
    }

    if (pcSLS > pSLS) {
      buffer.write('> p = ${f1.format(pSLS)} [${unit[1]}]\n');
    } else {
      buffer.write('< p \n');
    }
    buffer.write('*******************\n');
    buffer.write('Check moement\n');
    buffer.write('*******************\n');

    buffer.write('d = ');
    tempDouble = getDoubleValue(result, 'd');
    buffer.write('${f0.format(tempDouble)} [${unit[4]}]\n');

    buffer.write('d_c = ');
    tempDouble = getDoubleValue(result, 'd_c');
    buffer.write('${f0.format(tempDouble)} [${unit[4]}]\n');

    buffer.write('k = ');
    tempDouble = getDoubleValue(result, 'k');
    buffer.write('${f3.format(tempDouble)} \n');

    buffer.write('z = ');
    tempDouble = getDoubleValue(result, 'z');
    buffer.write('${f0.format(tempDouble)} [${unit[4]}]\n');

    buffer.write('As_t,req = ');
    tempDouble = getDoubleValue(result, 'As_t');
    buffer.write('${f0.format(tempDouble)} [${unit[6]}]\n');

    buffer.write('As_t,pro');
    tempString = getStringValue(result, 'As_t_pro Designation');

    if (tempString != 'null') {
      buffer.write(' = $tempString');
      tempDouble = getDoubleValue(result, 'As_t_pro');
      buffer.write(' = ${f0.format(tempDouble)} [${unit[6]}]\n');
    } else {
      buffer.write(' = $tempString\n');
    }

    buffer.write('As_c,req = ');
    tempDouble = getDoubleValue(result, 'As_c');
    buffer.write('${f0.format(tempDouble)} [${unit[6]}]\n');

    buffer.write('As_c,pro');
    tempString = getStringValue(result, 'As_c_pro Designation');

    if (tempString != 'null') {
      buffer.write(' = $tempString');
      tempDouble = getDoubleValue(result, 'As_c_pro');
      buffer.write(' = ${f0.format(tempDouble)} [${unit[6]}]\n');
    } else {
      buffer.write(' = $tempString\n');
    }

    buffer.write('*******************\n');
    buffer.write('Check shear\n');
    buffer.write('*******************\n');

    buffer.write('v = ');
    tempDouble = getDoubleValue(result, 'v_d');
    buffer.write('${f3.format(tempDouble)} [${unit[5]}]\n');

    buffer.write('v_r = ');
    tempDouble = getDoubleValue(result, 'v_r');
    buffer.write('${f3.format(tempDouble)} [${unit[5]}]\n');

    buffer.write('v_c\n');
    tempDouble = getDoubleValue(result, 'v_c');
    buffer.write('= ${f3.format(tempDouble)} [${unit[5]}]\n');

    buffer.write('Asv_req');
    tempDouble = getDoubleValue(result, 'l_req');
    if (tempDouble > 0.0) {
      buffer.write(
        ' = ${getDoubleValue(result, 'l_req')} [${unit[6]}/${unit[4]}]\n',
      );
    } else {
      buffer.write(' = N.A.');
      buffer.write(
        ' = ${getDoubleValue(result, 'l_req')} [${unit[6]}/${unit[4]}]\n',
      );
    }

    buffer.write('Asv_pro');
    tempString = getStringValue(result, 'l_pro Designation');
    if (tempString != 'null') {
      buffer.write(' = $tempString');
      buffer.write(
        ' = ${getDoubleValue(result, 'l_pro')} [${unit[6]}/${unit[4]}]\n',
      );
    } else {
      buffer.write(' = $tempString\n');
    }
    //* punching shear
    buffer.write('*******************\n');
    buffer.write('Check Punching Shear\n');
    buffer.write('*******************\n');

    buffer.write('P\n');
    buffer.write('= ULS Load\n');
    buffer.write('= ${f0.format(scheme.ulsLoad)} [${unit[0]}]\n');

    buffer.write('vd\n');
    buffer.write('= P/(uo * d)\n');

    //* at column face
    buffer.write('------At Column Face------\n');
    buffer.write('uo\n= Column perimeter\n');
    if (scheme.colShape == 'Square') {
      uo = 4 * scheme.columnSize;
      buffer.write('=4*${f0.format(scheme.columnSize)}\n');
    } else {
      uo = pi * scheme.columnSize;
      buffer.write('=\u03C0*${f0.format(scheme.columnSize)}\n');
    }
    buffer.write('= ${f0.format(uo)} [${unit[4]}]\n');
    buffer.write('vd\n');
    buffer.write(
      '= ${f3.format(getDoubleValue(result, 'vDPunch'))} [${unit[5]}]\n',
    );

    buffer.write('vc\n= max(7, 0.8\u221Afcu)\n= ');
    buffer.write('max(7, 0.8\u221A${f0.format(scheme.fcu)})\n');
    buffer.write(
      '= ${f3.format(getDoubleValue(result, 'vCPunch'))} [${unit[5]}]',
    );
    if (getDoubleValue(result, 'vCPunch') > getDoubleValue(result, 'vDPunch')) {
      buffer.write(' > vd (OK)\n');
    } else {
      buffer.write(' < vd (Fail)\n');
    }
    //* at 1.5d from column face
    buffer.write('------At 1.5d from Column Face------\n');
    if (scheme.colShape == 'Square') {
      dist = scheme.columnSize + 3 * getDoubleValue(result, 'd');
      buffer.write('p1\n= column size + 3d\n');
      buffer.write(
        '= ${f0.format(scheme.columnSize)} + 3*${f0.format(getDoubleValue(result, 'd'))}\n',
      );
      buffer.write('= ${f0.format(dist)}\n');
      if (dist > scheme.size) {
        buffer.write('\n> ${f0.format(scheme.size)}\n');
        buffer.write('>>> use p1 = 4*${f0.format(scheme.size)}\n');
      } else {
        buffer.write('<= ${f0.format(scheme.size)}\n');
      }
      dist = min(dist, scheme.size);
      uo = 4 * dist;
      buffer.write(
        'uo\n= 4*p1\n= 4*${f0.format(dist)}\n= ${f0.format(uo)} [${unit[4]}]\n',
      );
    } else {
      dist = scheme.columnSize + 3 * getDoubleValue(result, 'd');
      buffer.write('p1\n= column size + 3d\n= ');
      buffer.write(
        '${f0.format(scheme.columnSize)} + 3*${f0.format(getDoubleValue(result, 'd'))}\n',
      );
      buffer.write('= ${f0.format(dist)} [${unit[4]}]\n');
      if (dist > scheme.size) {
        buffer.write('> ${f0.format(scheme.size)}\n');
        buffer.write('>>> use p1 = ${f0.format(scheme.size)} [${unit[4]}]\n');
      } else {
        buffer.write('<= ${f0.format(scheme.size)} [${unit[4]}]\n');
      }
      dist = min(dist, scheme.size);
      uo = pi * dist;
      buffer.write(
        'uo\n= \u03C0*p1\n= \u03C0*${f0.format(dist)}\n= ${f0.format(uo)} [${unit[4]}]\n',
      );
    }
    buffer.write('vd\n');
    buffer.write('= P/(uo * d)\n');
    buffer.write(
      '= ${f3.format(getDoubleValue(result, 'vDPunch15'))} [${unit[5]}]\n',
    );
    buffer.write('vc\n');
    buffer.write(
      '= ${f3.format(getDoubleValue(result, 'v_c'))} [${unit[5]}]\n',
    );
    buffer.write('Asvp_req\n');
    buffer.write('= (vd-vc)*uo*d/ (0.87 * fyv)\n');
    buffer.write(
      '= ${f0.format(getDoubleValue(result, 'lReqPunch'))} [${unit[6]}]\n',
    );
    buffer.write('Asvp_pro\n');
    buffer.write(
      '= ${getStringValue(result, 'l_pro Punching Designation')} along uo = ${f0.format(uo)} [${unit[4]}]\n',
    );
    buffer.write(
      '= ${f0.format(getDoubleValue(result, 'lProPunch'))} [${unit[6]}]\n',
    );
    if (getDoubleValue(result, 'lProPunch') >
        getDoubleValue(result, 'lReqPunch')) {
      buffer.write('> Asvp_req (OK)\n');
    } else {
      buffer.write('< Asvp_req (Fail)\n');
    }

    buffer.write('*******************\n');
    buffer.write('Check actual deflection\n');
    buffer.write('*******************\n');

    buffer.write('Max \u0394\n');
    tempDouble = getDoubleValue(result, 'Max Deflection (Cantilever)');
    final List<double> L1 = <double>[
      getDoubleValue(result, "Span"),
      getElasticModulus(scheme.fcu),
      getSecondAreaMomentRectangle(1000, scheme.strZone),
    ];
    buffer.write('= wL^4/(8EI)\n');
    buffer.write(
      '= ${f0.format(pSLS)} * ${f0.format(L1[0])}^4 / (8 * ${f0.format(L1[1])} * ${f0.format(L1[2])})\n',
    );
    buffer.write(
      '= ${f3.format(getDoubleValue(result, 'Max Deflection (Cantilever)'))} [${unit[4]}]\n',
    );
    buffer.write('Limit: L/250 = ${f3.format(L1[0] / 250)} [${unit[4]}] ');
    if (getDoubleValue(result, 'Max Deflection (Cantilever)') < L1[0] / 250) {
      buffer.write('(OK)');
    } else {
      buffer.write('(Fail)\n');
      buffer.write('Precamber/add compression bar in detailed design.\n');
    }
    //* log any error / warnings
    List<String> errors = [], warnings = [];
    RegExp failReg = RegExp(r'fail', caseSensitive: false);
    if (failReg.hasMatch(buffer.toString())) {
      errors.add('Result not reliable. Something fails.');
      warnings.add('1. Consider to adjust footing max depth');
      warnings.add('2. Consider to adjust preferred k-value');
    }

    if (failReg.hasMatch(getStringValue(result, 'As_t_pro Designation'))) {
      errors.add('Tension bar fails.');
    }
    if (failReg.hasMatch(getStringValue(result, 'As_c_pro Designation'))) {
      errors.add('Compression bar fails.');
    }
    if (failReg.hasMatch(getStringValue(result, 'l_pro Designation'))) {
      errors.add('Links fails.');
    }

    if (warnings.isNotEmpty) {
      buffer = addWarningHeader(buffer, warnings);
    }
    if (errors.isNotEmpty) {
      buffer = addErrorHeader(buffer, errors);
    }

    return buffer;
  }

  Future<void> _exportListToPdf(
    BuildContext context,
    WidgetRef ref,
    List<String> contents,
  ) async {
    final pw.ThemeData customTheme = pw.ThemeData.withFont(
      base: pw.Font.ttf(await rootBundle.load("assets/OpenSans-Regular.ttf")),
      bold: pw.Font.ttf(await rootBundle.load("assets/OpenSans-Bold.ttf")),
      italic: pw.Font.ttf(await rootBundle.load("assets/OpenSans-Italic.ttf")),
      boldItalic: pw.Font.ttf(
        await rootBundle.load("assets/OpenSans-BoldItalic.ttf"),
      ),
    );

    final pdf = pw.Document(theme: customTheme);
    final maxLines = 45;
    List<pw.Widget> pageBuffer = [];
    // TextStyle textStyle = Theme.of(context).textTheme.bodySmall!;

    for (int i = 0; i < contents.length; i++) {
      pageBuffer.add(pw.Text(contents[i]));

      //! note that the builder of .addPage is deferred callback
      //! it means it executes later when the pdf is actually printed out
      //! so we put in copy of pageBuffer to it everytime we .addPage

      if ((pageBuffer.length == maxLines) || (i == contents.length - 1)) {
        final pageBufferCopy = List<pw.Text>.from(pageBuffer);
        pdf.addPage(
          pw.Page(
            margin: pw.EdgeInsets.fromLTRB(25, 35, 25, 35),
            build: (pw.Context context) {
              // return pw.Center(
              //    child: pw.Text(
              //     'testing',
              //     style: pw.TextStyle(fontSize: textStyle.fontSize!),
              //    )
              // );
              return pw.Column(
                mainAxisAlignment: pw.MainAxisAlignment.start,
                crossAxisAlignment: pw.CrossAxisAlignment.start,
                children: pageBufferCopy,
              );
            },
          ),
        );
        pageBuffer.clear();
      }
    }

    await Printing.layoutPdf(
      onLayout: (PdfPageFormat format) async => pdf.save(),
    );
  }

  // Method to update heights based on the current media query
  void _updateHeights() {
    final double screenHeight = MediaQuery.of(context).size.height;
    _maxHeight =
        (widget.maxHeight == null)
            ? screenHeight * 0.6
            : (widget.maxHeight! > screenHeight * 0.6)
            ? screenHeight * 0.6
            : widget.maxHeight!;

    _minHeight =
        (widget.minHeight == null)
            ? screenHeight * 0.1
            : (widget.minHeight! > screenHeight * 0.1)
            ? screenHeight * 0.1
            : widget.minHeight!;

    // Adjust _maxHeight if needed

    if (_maxHeight < _minHeight) {
      _maxHeight = _minHeight;
    }
  }
}
