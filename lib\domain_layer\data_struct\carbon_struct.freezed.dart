// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'carbon_struct.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
Carbon _$CarbonFromJson(
  Map<String, dynamic> json
) {
        switch (json['runtimeType']) {
                  case 'concreteArea':
          return ConcreteArea.fromJson(
            json
          );
                case 'concreteLine':
          return ConcreteLine.fromJson(
            json
          );
                case 'rebar':
          return Rebar.fromJson(
            json
          );
                case 'steelLine':
          return SteelLine.fromJson(
            json
          );
                case 'metalArea':
          return MetalArea.fromJson(
            json
          );
        
          default:
            throw CheckedFromJsonException(
  json,
  'runtimeType',
  'Carbon',
  'Invalid union type "${json['runtimeType']}"!'
);
        }
      
}

/// @nodoc
mixin _$Carbon implements DiagnosticableTreeMixin {

 String get name; String get carbonType;//[top + bottom bars]
 double get density;// [kg/m3]
 double get ecA13;// production [kgCO2e/kg]
 double get ecA4;// transport to the site [kgCO2e/kg]
 double get wF;// waste factor, for ecA53 cals
 double get sCO2;// biogenic carbon sequestered from the air, for ecA53 cals
 double get ecC2;// transport away from site [kgCO2e/m2]
 double get ecC34;// recycle and disposal[kgCO2e/m2]
 String get id;
/// Create a copy of Carbon
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$CarbonCopyWith<Carbon> get copyWith => _$CarbonCopyWithImpl<Carbon>(this as Carbon, _$identity);

  /// Serializes this Carbon to a JSON map.
  Map<String, dynamic> toJson();

@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'Carbon'))
    ..add(DiagnosticsProperty('name', name))..add(DiagnosticsProperty('carbonType', carbonType))..add(DiagnosticsProperty('density', density))..add(DiagnosticsProperty('ecA13', ecA13))..add(DiagnosticsProperty('ecA4', ecA4))..add(DiagnosticsProperty('wF', wF))..add(DiagnosticsProperty('sCO2', sCO2))..add(DiagnosticsProperty('ecC2', ecC2))..add(DiagnosticsProperty('ecC34', ecC34))..add(DiagnosticsProperty('id', id));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is Carbon&&(identical(other.name, name) || other.name == name)&&(identical(other.carbonType, carbonType) || other.carbonType == carbonType)&&(identical(other.density, density) || other.density == density)&&(identical(other.ecA13, ecA13) || other.ecA13 == ecA13)&&(identical(other.ecA4, ecA4) || other.ecA4 == ecA4)&&(identical(other.wF, wF) || other.wF == wF)&&(identical(other.sCO2, sCO2) || other.sCO2 == sCO2)&&(identical(other.ecC2, ecC2) || other.ecC2 == ecC2)&&(identical(other.ecC34, ecC34) || other.ecC34 == ecC34)&&(identical(other.id, id) || other.id == id));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,name,carbonType,density,ecA13,ecA4,wF,sCO2,ecC2,ecC34,id);

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'Carbon(name: $name, carbonType: $carbonType, density: $density, ecA13: $ecA13, ecA4: $ecA4, wF: $wF, sCO2: $sCO2, ecC2: $ecC2, ecC34: $ecC34, id: $id)';
}


}

/// @nodoc
abstract mixin class $CarbonCopyWith<$Res>  {
  factory $CarbonCopyWith(Carbon value, $Res Function(Carbon) _then) = _$CarbonCopyWithImpl;
@useResult
$Res call({
 String name, String carbonType, double density, double ecA13, double ecA4, double wF, double sCO2, double ecC2, double ecC34, String id
});




}
/// @nodoc
class _$CarbonCopyWithImpl<$Res>
    implements $CarbonCopyWith<$Res> {
  _$CarbonCopyWithImpl(this._self, this._then);

  final Carbon _self;
  final $Res Function(Carbon) _then;

/// Create a copy of Carbon
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? name = null,Object? carbonType = null,Object? density = null,Object? ecA13 = null,Object? ecA4 = null,Object? wF = null,Object? sCO2 = null,Object? ecC2 = null,Object? ecC34 = null,Object? id = null,}) {
  return _then(_self.copyWith(
name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,carbonType: null == carbonType ? _self.carbonType : carbonType // ignore: cast_nullable_to_non_nullable
as String,density: null == density ? _self.density : density // ignore: cast_nullable_to_non_nullable
as double,ecA13: null == ecA13 ? _self.ecA13 : ecA13 // ignore: cast_nullable_to_non_nullable
as double,ecA4: null == ecA4 ? _self.ecA4 : ecA4 // ignore: cast_nullable_to_non_nullable
as double,wF: null == wF ? _self.wF : wF // ignore: cast_nullable_to_non_nullable
as double,sCO2: null == sCO2 ? _self.sCO2 : sCO2 // ignore: cast_nullable_to_non_nullable
as double,ecC2: null == ecC2 ? _self.ecC2 : ecC2 // ignore: cast_nullable_to_non_nullable
as double,ecC34: null == ecC34 ? _self.ecC34 : ecC34 // ignore: cast_nullable_to_non_nullable
as double,id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,
  ));
}

}


/// Adds pattern-matching-related methods to [Carbon].
extension CarbonPatterns on Carbon {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>({TResult Function( ConcreteArea value)?  concreteArea,TResult Function( ConcreteLine value)?  concreteLine,TResult Function( Rebar value)?  rebar,TResult Function( SteelLine value)?  steelLine,TResult Function( MetalArea value)?  metalArea,required TResult orElse(),}){
final _that = this;
switch (_that) {
case ConcreteArea() when concreteArea != null:
return concreteArea(_that);case ConcreteLine() when concreteLine != null:
return concreteLine(_that);case Rebar() when rebar != null:
return rebar(_that);case SteelLine() when steelLine != null:
return steelLine(_that);case MetalArea() when metalArea != null:
return metalArea(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>({required TResult Function( ConcreteArea value)  concreteArea,required TResult Function( ConcreteLine value)  concreteLine,required TResult Function( Rebar value)  rebar,required TResult Function( SteelLine value)  steelLine,required TResult Function( MetalArea value)  metalArea,}){
final _that = this;
switch (_that) {
case ConcreteArea():
return concreteArea(_that);case ConcreteLine():
return concreteLine(_that);case Rebar():
return rebar(_that);case SteelLine():
return steelLine(_that);case MetalArea():
return metalArea(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>({TResult? Function( ConcreteArea value)?  concreteArea,TResult? Function( ConcreteLine value)?  concreteLine,TResult? Function( Rebar value)?  rebar,TResult? Function( SteelLine value)?  steelLine,TResult? Function( MetalArea value)?  metalArea,}){
final _that = this;
switch (_that) {
case ConcreteArea() when concreteArea != null:
return concreteArea(_that);case ConcreteLine() when concreteLine != null:
return concreteLine(_that);case Rebar() when rebar != null:
return rebar(_that);case SteelLine() when steelLine != null:
return steelLine(_that);case MetalArea() when metalArea != null:
return metalArea(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>({TResult Function( String name,  String carbonType,  double thk,  double area,  double totalSteelRatio,  double density,  double ecA13,  double ecA4,  double wF,  double sCO2,  double ecC2,  double ecC34,  String id)?  concreteArea,TResult Function( String name,  String carbonType,  double length,  double sectionSize,  double totalSteelRatio,  double density,  double ecA13,  double ecA4,  double wF,  double sCO2,  double ecC2,  double ecC34,  String id)?  concreteLine,TResult Function( String name,  String carbonType,  double length,  double sectionSize,  double density,  double ecA13,  double ecA4,  double wF,  double sCO2,  double ecC2,  double ecC34,  String id)?  rebar,TResult Function( String name,  String carbonType,  double length,  double sectionSize,  double density,  double ecA13,  double ecA4,  double wF,  double sCO2,  double ecC2,  double ecC34,  String id)?  steelLine,TResult Function( String name,  String carbonType,  double area,  double density,  double ecA13,  double ecA4,  double wF,  double sCO2,  double ecC2,  double ecC34,  String id)?  metalArea,required TResult orElse(),}) {final _that = this;
switch (_that) {
case ConcreteArea() when concreteArea != null:
return concreteArea(_that.name,_that.carbonType,_that.thk,_that.area,_that.totalSteelRatio,_that.density,_that.ecA13,_that.ecA4,_that.wF,_that.sCO2,_that.ecC2,_that.ecC34,_that.id);case ConcreteLine() when concreteLine != null:
return concreteLine(_that.name,_that.carbonType,_that.length,_that.sectionSize,_that.totalSteelRatio,_that.density,_that.ecA13,_that.ecA4,_that.wF,_that.sCO2,_that.ecC2,_that.ecC34,_that.id);case Rebar() when rebar != null:
return rebar(_that.name,_that.carbonType,_that.length,_that.sectionSize,_that.density,_that.ecA13,_that.ecA4,_that.wF,_that.sCO2,_that.ecC2,_that.ecC34,_that.id);case SteelLine() when steelLine != null:
return steelLine(_that.name,_that.carbonType,_that.length,_that.sectionSize,_that.density,_that.ecA13,_that.ecA4,_that.wF,_that.sCO2,_that.ecC2,_that.ecC34,_that.id);case MetalArea() when metalArea != null:
return metalArea(_that.name,_that.carbonType,_that.area,_that.density,_that.ecA13,_that.ecA4,_that.wF,_that.sCO2,_that.ecC2,_that.ecC34,_that.id);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>({required TResult Function( String name,  String carbonType,  double thk,  double area,  double totalSteelRatio,  double density,  double ecA13,  double ecA4,  double wF,  double sCO2,  double ecC2,  double ecC34,  String id)  concreteArea,required TResult Function( String name,  String carbonType,  double length,  double sectionSize,  double totalSteelRatio,  double density,  double ecA13,  double ecA4,  double wF,  double sCO2,  double ecC2,  double ecC34,  String id)  concreteLine,required TResult Function( String name,  String carbonType,  double length,  double sectionSize,  double density,  double ecA13,  double ecA4,  double wF,  double sCO2,  double ecC2,  double ecC34,  String id)  rebar,required TResult Function( String name,  String carbonType,  double length,  double sectionSize,  double density,  double ecA13,  double ecA4,  double wF,  double sCO2,  double ecC2,  double ecC34,  String id)  steelLine,required TResult Function( String name,  String carbonType,  double area,  double density,  double ecA13,  double ecA4,  double wF,  double sCO2,  double ecC2,  double ecC34,  String id)  metalArea,}) {final _that = this;
switch (_that) {
case ConcreteArea():
return concreteArea(_that.name,_that.carbonType,_that.thk,_that.area,_that.totalSteelRatio,_that.density,_that.ecA13,_that.ecA4,_that.wF,_that.sCO2,_that.ecC2,_that.ecC34,_that.id);case ConcreteLine():
return concreteLine(_that.name,_that.carbonType,_that.length,_that.sectionSize,_that.totalSteelRatio,_that.density,_that.ecA13,_that.ecA4,_that.wF,_that.sCO2,_that.ecC2,_that.ecC34,_that.id);case Rebar():
return rebar(_that.name,_that.carbonType,_that.length,_that.sectionSize,_that.density,_that.ecA13,_that.ecA4,_that.wF,_that.sCO2,_that.ecC2,_that.ecC34,_that.id);case SteelLine():
return steelLine(_that.name,_that.carbonType,_that.length,_that.sectionSize,_that.density,_that.ecA13,_that.ecA4,_that.wF,_that.sCO2,_that.ecC2,_that.ecC34,_that.id);case MetalArea():
return metalArea(_that.name,_that.carbonType,_that.area,_that.density,_that.ecA13,_that.ecA4,_that.wF,_that.sCO2,_that.ecC2,_that.ecC34,_that.id);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>({TResult? Function( String name,  String carbonType,  double thk,  double area,  double totalSteelRatio,  double density,  double ecA13,  double ecA4,  double wF,  double sCO2,  double ecC2,  double ecC34,  String id)?  concreteArea,TResult? Function( String name,  String carbonType,  double length,  double sectionSize,  double totalSteelRatio,  double density,  double ecA13,  double ecA4,  double wF,  double sCO2,  double ecC2,  double ecC34,  String id)?  concreteLine,TResult? Function( String name,  String carbonType,  double length,  double sectionSize,  double density,  double ecA13,  double ecA4,  double wF,  double sCO2,  double ecC2,  double ecC34,  String id)?  rebar,TResult? Function( String name,  String carbonType,  double length,  double sectionSize,  double density,  double ecA13,  double ecA4,  double wF,  double sCO2,  double ecC2,  double ecC34,  String id)?  steelLine,TResult? Function( String name,  String carbonType,  double area,  double density,  double ecA13,  double ecA4,  double wF,  double sCO2,  double ecC2,  double ecC34,  String id)?  metalArea,}) {final _that = this;
switch (_that) {
case ConcreteArea() when concreteArea != null:
return concreteArea(_that.name,_that.carbonType,_that.thk,_that.area,_that.totalSteelRatio,_that.density,_that.ecA13,_that.ecA4,_that.wF,_that.sCO2,_that.ecC2,_that.ecC34,_that.id);case ConcreteLine() when concreteLine != null:
return concreteLine(_that.name,_that.carbonType,_that.length,_that.sectionSize,_that.totalSteelRatio,_that.density,_that.ecA13,_that.ecA4,_that.wF,_that.sCO2,_that.ecC2,_that.ecC34,_that.id);case Rebar() when rebar != null:
return rebar(_that.name,_that.carbonType,_that.length,_that.sectionSize,_that.density,_that.ecA13,_that.ecA4,_that.wF,_that.sCO2,_that.ecC2,_that.ecC34,_that.id);case SteelLine() when steelLine != null:
return steelLine(_that.name,_that.carbonType,_that.length,_that.sectionSize,_that.density,_that.ecA13,_that.ecA4,_that.wF,_that.sCO2,_that.ecC2,_that.ecC34,_that.id);case MetalArea() when metalArea != null:
return metalArea(_that.name,_that.carbonType,_that.area,_that.density,_that.ecA13,_that.ecA4,_that.wF,_that.sCO2,_that.ecC2,_that.ecC34,_that.id);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class ConcreteArea extends Carbon with DiagnosticableTreeMixin {
  const ConcreteArea({this.name = '', this.carbonType = 'concreteArea', this.thk = 0.0, this.area = 0.0, this.totalSteelRatio = 0.0, this.density = 2400.0, this.ecA13 = 0.198, this.ecA4 = 0.003, this.wF = 0.053, this.sCO2 = -1.64, this.ecC2 = 0.009, this.ecC34 = 0.0012, this.id = '', final  String? $type}): $type = $type ?? 'concreteArea',super._();
  factory ConcreteArea.fromJson(Map<String, dynamic> json) => _$ConcreteAreaFromJson(json);

@override@JsonKey() final  String name;
@override@JsonKey() final  String carbonType;
@JsonKey() final  double thk;
//[mm]
@JsonKey() final  double area;
//[m2]
@JsonKey() final  double totalSteelRatio;
//[top + bottom bars]
@override@JsonKey() final  double density;
// [kg/m3]
@override@JsonKey() final  double ecA13;
// production [kgCO2e/kg]
@override@JsonKey() final  double ecA4;
// transport to the site [kgCO2e/kg]
@override@JsonKey() final  double wF;
// waste factor, for ecA53 cals
@override@JsonKey() final  double sCO2;
@override@JsonKey() final  double ecC2;
// transport away from site [kgCO2e/m2]
@override@JsonKey() final  double ecC34;
// recycle and disposal[kgCO2e/m2]
@override@JsonKey() final  String id;

@JsonKey(name: 'runtimeType')
final String $type;


/// Create a copy of Carbon
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ConcreteAreaCopyWith<ConcreteArea> get copyWith => _$ConcreteAreaCopyWithImpl<ConcreteArea>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ConcreteAreaToJson(this, );
}
@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'Carbon.concreteArea'))
    ..add(DiagnosticsProperty('name', name))..add(DiagnosticsProperty('carbonType', carbonType))..add(DiagnosticsProperty('thk', thk))..add(DiagnosticsProperty('area', area))..add(DiagnosticsProperty('totalSteelRatio', totalSteelRatio))..add(DiagnosticsProperty('density', density))..add(DiagnosticsProperty('ecA13', ecA13))..add(DiagnosticsProperty('ecA4', ecA4))..add(DiagnosticsProperty('wF', wF))..add(DiagnosticsProperty('sCO2', sCO2))..add(DiagnosticsProperty('ecC2', ecC2))..add(DiagnosticsProperty('ecC34', ecC34))..add(DiagnosticsProperty('id', id));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ConcreteArea&&(identical(other.name, name) || other.name == name)&&(identical(other.carbonType, carbonType) || other.carbonType == carbonType)&&(identical(other.thk, thk) || other.thk == thk)&&(identical(other.area, area) || other.area == area)&&(identical(other.totalSteelRatio, totalSteelRatio) || other.totalSteelRatio == totalSteelRatio)&&(identical(other.density, density) || other.density == density)&&(identical(other.ecA13, ecA13) || other.ecA13 == ecA13)&&(identical(other.ecA4, ecA4) || other.ecA4 == ecA4)&&(identical(other.wF, wF) || other.wF == wF)&&(identical(other.sCO2, sCO2) || other.sCO2 == sCO2)&&(identical(other.ecC2, ecC2) || other.ecC2 == ecC2)&&(identical(other.ecC34, ecC34) || other.ecC34 == ecC34)&&(identical(other.id, id) || other.id == id));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,name,carbonType,thk,area,totalSteelRatio,density,ecA13,ecA4,wF,sCO2,ecC2,ecC34,id);

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'Carbon.concreteArea(name: $name, carbonType: $carbonType, thk: $thk, area: $area, totalSteelRatio: $totalSteelRatio, density: $density, ecA13: $ecA13, ecA4: $ecA4, wF: $wF, sCO2: $sCO2, ecC2: $ecC2, ecC34: $ecC34, id: $id)';
}


}

/// @nodoc
abstract mixin class $ConcreteAreaCopyWith<$Res> implements $CarbonCopyWith<$Res> {
  factory $ConcreteAreaCopyWith(ConcreteArea value, $Res Function(ConcreteArea) _then) = _$ConcreteAreaCopyWithImpl;
@override @useResult
$Res call({
 String name, String carbonType, double thk, double area, double totalSteelRatio, double density, double ecA13, double ecA4, double wF, double sCO2, double ecC2, double ecC34, String id
});




}
/// @nodoc
class _$ConcreteAreaCopyWithImpl<$Res>
    implements $ConcreteAreaCopyWith<$Res> {
  _$ConcreteAreaCopyWithImpl(this._self, this._then);

  final ConcreteArea _self;
  final $Res Function(ConcreteArea) _then;

/// Create a copy of Carbon
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? name = null,Object? carbonType = null,Object? thk = null,Object? area = null,Object? totalSteelRatio = null,Object? density = null,Object? ecA13 = null,Object? ecA4 = null,Object? wF = null,Object? sCO2 = null,Object? ecC2 = null,Object? ecC34 = null,Object? id = null,}) {
  return _then(ConcreteArea(
name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,carbonType: null == carbonType ? _self.carbonType : carbonType // ignore: cast_nullable_to_non_nullable
as String,thk: null == thk ? _self.thk : thk // ignore: cast_nullable_to_non_nullable
as double,area: null == area ? _self.area : area // ignore: cast_nullable_to_non_nullable
as double,totalSteelRatio: null == totalSteelRatio ? _self.totalSteelRatio : totalSteelRatio // ignore: cast_nullable_to_non_nullable
as double,density: null == density ? _self.density : density // ignore: cast_nullable_to_non_nullable
as double,ecA13: null == ecA13 ? _self.ecA13 : ecA13 // ignore: cast_nullable_to_non_nullable
as double,ecA4: null == ecA4 ? _self.ecA4 : ecA4 // ignore: cast_nullable_to_non_nullable
as double,wF: null == wF ? _self.wF : wF // ignore: cast_nullable_to_non_nullable
as double,sCO2: null == sCO2 ? _self.sCO2 : sCO2 // ignore: cast_nullable_to_non_nullable
as double,ecC2: null == ecC2 ? _self.ecC2 : ecC2 // ignore: cast_nullable_to_non_nullable
as double,ecC34: null == ecC34 ? _self.ecC34 : ecC34 // ignore: cast_nullable_to_non_nullable
as double,id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

/// @nodoc
@JsonSerializable()

class ConcreteLine extends Carbon with DiagnosticableTreeMixin {
  const ConcreteLine({this.name = '', this.carbonType = 'concreteLine', this.length = 0.0, this.sectionSize = 0.0, this.totalSteelRatio = 0.0, this.density = 2400.0, this.ecA13 = 0.198, this.ecA4 = 0.003, this.wF = 0.053, this.sCO2 = -1.64, this.ecC2 = 0.009, this.ecC34 = 0.0012, this.id = '', final  String? $type}): $type = $type ?? 'concreteLine',super._();
  factory ConcreteLine.fromJson(Map<String, dynamic> json) => _$ConcreteLineFromJson(json);

@override@JsonKey() final  String name;
@override@JsonKey() final  String carbonType;
@JsonKey() final  double length;
//[m]
@JsonKey() final  double sectionSize;
//[mm2]
@JsonKey() final  double totalSteelRatio;
//[top + bottom bars]
@override@JsonKey() final  double density;
// [kg/m3]
@override@JsonKey() final  double ecA13;
// production [kgCO2e/kg]
@override@JsonKey() final  double ecA4;
// transport to the site [kgCO2e/kg]
@override@JsonKey() final  double wF;
// waste factor, for ecA53 cals
@override@JsonKey() final  double sCO2;
@override@JsonKey() final  double ecC2;
// transport away from site [kgCO2e/m2]
@override@JsonKey() final  double ecC34;
// recycle and disposal[kgCO2e/m2]
@override@JsonKey() final  String id;

@JsonKey(name: 'runtimeType')
final String $type;


/// Create a copy of Carbon
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ConcreteLineCopyWith<ConcreteLine> get copyWith => _$ConcreteLineCopyWithImpl<ConcreteLine>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ConcreteLineToJson(this, );
}
@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'Carbon.concreteLine'))
    ..add(DiagnosticsProperty('name', name))..add(DiagnosticsProperty('carbonType', carbonType))..add(DiagnosticsProperty('length', length))..add(DiagnosticsProperty('sectionSize', sectionSize))..add(DiagnosticsProperty('totalSteelRatio', totalSteelRatio))..add(DiagnosticsProperty('density', density))..add(DiagnosticsProperty('ecA13', ecA13))..add(DiagnosticsProperty('ecA4', ecA4))..add(DiagnosticsProperty('wF', wF))..add(DiagnosticsProperty('sCO2', sCO2))..add(DiagnosticsProperty('ecC2', ecC2))..add(DiagnosticsProperty('ecC34', ecC34))..add(DiagnosticsProperty('id', id));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ConcreteLine&&(identical(other.name, name) || other.name == name)&&(identical(other.carbonType, carbonType) || other.carbonType == carbonType)&&(identical(other.length, length) || other.length == length)&&(identical(other.sectionSize, sectionSize) || other.sectionSize == sectionSize)&&(identical(other.totalSteelRatio, totalSteelRatio) || other.totalSteelRatio == totalSteelRatio)&&(identical(other.density, density) || other.density == density)&&(identical(other.ecA13, ecA13) || other.ecA13 == ecA13)&&(identical(other.ecA4, ecA4) || other.ecA4 == ecA4)&&(identical(other.wF, wF) || other.wF == wF)&&(identical(other.sCO2, sCO2) || other.sCO2 == sCO2)&&(identical(other.ecC2, ecC2) || other.ecC2 == ecC2)&&(identical(other.ecC34, ecC34) || other.ecC34 == ecC34)&&(identical(other.id, id) || other.id == id));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,name,carbonType,length,sectionSize,totalSteelRatio,density,ecA13,ecA4,wF,sCO2,ecC2,ecC34,id);

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'Carbon.concreteLine(name: $name, carbonType: $carbonType, length: $length, sectionSize: $sectionSize, totalSteelRatio: $totalSteelRatio, density: $density, ecA13: $ecA13, ecA4: $ecA4, wF: $wF, sCO2: $sCO2, ecC2: $ecC2, ecC34: $ecC34, id: $id)';
}


}

/// @nodoc
abstract mixin class $ConcreteLineCopyWith<$Res> implements $CarbonCopyWith<$Res> {
  factory $ConcreteLineCopyWith(ConcreteLine value, $Res Function(ConcreteLine) _then) = _$ConcreteLineCopyWithImpl;
@override @useResult
$Res call({
 String name, String carbonType, double length, double sectionSize, double totalSteelRatio, double density, double ecA13, double ecA4, double wF, double sCO2, double ecC2, double ecC34, String id
});




}
/// @nodoc
class _$ConcreteLineCopyWithImpl<$Res>
    implements $ConcreteLineCopyWith<$Res> {
  _$ConcreteLineCopyWithImpl(this._self, this._then);

  final ConcreteLine _self;
  final $Res Function(ConcreteLine) _then;

/// Create a copy of Carbon
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? name = null,Object? carbonType = null,Object? length = null,Object? sectionSize = null,Object? totalSteelRatio = null,Object? density = null,Object? ecA13 = null,Object? ecA4 = null,Object? wF = null,Object? sCO2 = null,Object? ecC2 = null,Object? ecC34 = null,Object? id = null,}) {
  return _then(ConcreteLine(
name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,carbonType: null == carbonType ? _self.carbonType : carbonType // ignore: cast_nullable_to_non_nullable
as String,length: null == length ? _self.length : length // ignore: cast_nullable_to_non_nullable
as double,sectionSize: null == sectionSize ? _self.sectionSize : sectionSize // ignore: cast_nullable_to_non_nullable
as double,totalSteelRatio: null == totalSteelRatio ? _self.totalSteelRatio : totalSteelRatio // ignore: cast_nullable_to_non_nullable
as double,density: null == density ? _self.density : density // ignore: cast_nullable_to_non_nullable
as double,ecA13: null == ecA13 ? _self.ecA13 : ecA13 // ignore: cast_nullable_to_non_nullable
as double,ecA4: null == ecA4 ? _self.ecA4 : ecA4 // ignore: cast_nullable_to_non_nullable
as double,wF: null == wF ? _self.wF : wF // ignore: cast_nullable_to_non_nullable
as double,sCO2: null == sCO2 ? _self.sCO2 : sCO2 // ignore: cast_nullable_to_non_nullable
as double,ecC2: null == ecC2 ? _self.ecC2 : ecC2 // ignore: cast_nullable_to_non_nullable
as double,ecC34: null == ecC34 ? _self.ecC34 : ecC34 // ignore: cast_nullable_to_non_nullable
as double,id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

/// @nodoc
@JsonSerializable()

class Rebar extends Carbon with DiagnosticableTreeMixin {
  const Rebar({this.name = '', this.carbonType = 'rebar', this.length = 0.0, this.sectionSize = 0.0, this.density = 7850.0, this.ecA13 = 0.72, this.ecA4 = 0.003, this.wF = 0.053, this.sCO2 = -1.64, this.ecC2 = 0.009, this.ecC34 = 0.0012, this.id = '', final  String? $type}): $type = $type ?? 'rebar',super._();
  factory Rebar.fromJson(Map<String, dynamic> json) => _$RebarFromJson(json);

@override@JsonKey() final  String name;
@override@JsonKey() final  String carbonType;
@JsonKey() final  double length;
//[m]
@JsonKey() final  double sectionSize;
//[mm2]
@override@JsonKey() final  double density;
// [kg/m3]
@override@JsonKey() final  double ecA13;
// production [kgCO2e/kg]
@override@JsonKey() final  double ecA4;
// transport to the site [kgCO2e/kg]
@override@JsonKey() final  double wF;
// waste factor, for ecA53 cals
@override@JsonKey() final  double sCO2;
// biogenic carbon sequestered from the air, for ecA53 cals
@override@JsonKey() final  double ecC2;
// transport away from site [kgCO2e/m2]
@override@JsonKey() final  double ecC34;
// recycle and disposal[kgCO2e/m2]
@override@JsonKey() final  String id;

@JsonKey(name: 'runtimeType')
final String $type;


/// Create a copy of Carbon
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$RebarCopyWith<Rebar> get copyWith => _$RebarCopyWithImpl<Rebar>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$RebarToJson(this, );
}
@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'Carbon.rebar'))
    ..add(DiagnosticsProperty('name', name))..add(DiagnosticsProperty('carbonType', carbonType))..add(DiagnosticsProperty('length', length))..add(DiagnosticsProperty('sectionSize', sectionSize))..add(DiagnosticsProperty('density', density))..add(DiagnosticsProperty('ecA13', ecA13))..add(DiagnosticsProperty('ecA4', ecA4))..add(DiagnosticsProperty('wF', wF))..add(DiagnosticsProperty('sCO2', sCO2))..add(DiagnosticsProperty('ecC2', ecC2))..add(DiagnosticsProperty('ecC34', ecC34))..add(DiagnosticsProperty('id', id));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is Rebar&&(identical(other.name, name) || other.name == name)&&(identical(other.carbonType, carbonType) || other.carbonType == carbonType)&&(identical(other.length, length) || other.length == length)&&(identical(other.sectionSize, sectionSize) || other.sectionSize == sectionSize)&&(identical(other.density, density) || other.density == density)&&(identical(other.ecA13, ecA13) || other.ecA13 == ecA13)&&(identical(other.ecA4, ecA4) || other.ecA4 == ecA4)&&(identical(other.wF, wF) || other.wF == wF)&&(identical(other.sCO2, sCO2) || other.sCO2 == sCO2)&&(identical(other.ecC2, ecC2) || other.ecC2 == ecC2)&&(identical(other.ecC34, ecC34) || other.ecC34 == ecC34)&&(identical(other.id, id) || other.id == id));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,name,carbonType,length,sectionSize,density,ecA13,ecA4,wF,sCO2,ecC2,ecC34,id);

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'Carbon.rebar(name: $name, carbonType: $carbonType, length: $length, sectionSize: $sectionSize, density: $density, ecA13: $ecA13, ecA4: $ecA4, wF: $wF, sCO2: $sCO2, ecC2: $ecC2, ecC34: $ecC34, id: $id)';
}


}

/// @nodoc
abstract mixin class $RebarCopyWith<$Res> implements $CarbonCopyWith<$Res> {
  factory $RebarCopyWith(Rebar value, $Res Function(Rebar) _then) = _$RebarCopyWithImpl;
@override @useResult
$Res call({
 String name, String carbonType, double length, double sectionSize, double density, double ecA13, double ecA4, double wF, double sCO2, double ecC2, double ecC34, String id
});




}
/// @nodoc
class _$RebarCopyWithImpl<$Res>
    implements $RebarCopyWith<$Res> {
  _$RebarCopyWithImpl(this._self, this._then);

  final Rebar _self;
  final $Res Function(Rebar) _then;

/// Create a copy of Carbon
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? name = null,Object? carbonType = null,Object? length = null,Object? sectionSize = null,Object? density = null,Object? ecA13 = null,Object? ecA4 = null,Object? wF = null,Object? sCO2 = null,Object? ecC2 = null,Object? ecC34 = null,Object? id = null,}) {
  return _then(Rebar(
name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,carbonType: null == carbonType ? _self.carbonType : carbonType // ignore: cast_nullable_to_non_nullable
as String,length: null == length ? _self.length : length // ignore: cast_nullable_to_non_nullable
as double,sectionSize: null == sectionSize ? _self.sectionSize : sectionSize // ignore: cast_nullable_to_non_nullable
as double,density: null == density ? _self.density : density // ignore: cast_nullable_to_non_nullable
as double,ecA13: null == ecA13 ? _self.ecA13 : ecA13 // ignore: cast_nullable_to_non_nullable
as double,ecA4: null == ecA4 ? _self.ecA4 : ecA4 // ignore: cast_nullable_to_non_nullable
as double,wF: null == wF ? _self.wF : wF // ignore: cast_nullable_to_non_nullable
as double,sCO2: null == sCO2 ? _self.sCO2 : sCO2 // ignore: cast_nullable_to_non_nullable
as double,ecC2: null == ecC2 ? _self.ecC2 : ecC2 // ignore: cast_nullable_to_non_nullable
as double,ecC34: null == ecC34 ? _self.ecC34 : ecC34 // ignore: cast_nullable_to_non_nullable
as double,id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

/// @nodoc
@JsonSerializable()

class SteelLine extends Carbon with DiagnosticableTreeMixin {
  const SteelLine({this.name = '', this.carbonType = 'steelLine', this.length = 0.0, this.sectionSize = 0.0, this.density = 7850.0, this.ecA13 = 1.64, this.ecA4 = 0.003, this.wF = 0.01, this.sCO2 = -1.64, this.ecC2 = 0.009, this.ecC34 = 0.0012, this.id = '', final  String? $type}): $type = $type ?? 'steelLine',super._();
  factory SteelLine.fromJson(Map<String, dynamic> json) => _$SteelLineFromJson(json);

@override@JsonKey() final  String name;
@override@JsonKey() final  String carbonType;
@JsonKey() final  double length;
//[m]
@JsonKey() final  double sectionSize;
//[mm2]
@override@JsonKey() final  double density;
// [kg/m3]
@override@JsonKey() final  double ecA13;
// production [kgCO2e/kg]
@override@JsonKey() final  double ecA4;
// transport to the site [kgCO2e/kg]
@override@JsonKey() final  double wF;
// waste factor, for ecA53 cals
@override@JsonKey() final  double sCO2;
// biogenic carbon sequestered from the air, for ecA53 cals
@override@JsonKey() final  double ecC2;
// transport away from site [kgCO2e/m2]
@override@JsonKey() final  double ecC34;
// recycle and disposal[kgCO2e/m2]
@override@JsonKey() final  String id;

@JsonKey(name: 'runtimeType')
final String $type;


/// Create a copy of Carbon
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$SteelLineCopyWith<SteelLine> get copyWith => _$SteelLineCopyWithImpl<SteelLine>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$SteelLineToJson(this, );
}
@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'Carbon.steelLine'))
    ..add(DiagnosticsProperty('name', name))..add(DiagnosticsProperty('carbonType', carbonType))..add(DiagnosticsProperty('length', length))..add(DiagnosticsProperty('sectionSize', sectionSize))..add(DiagnosticsProperty('density', density))..add(DiagnosticsProperty('ecA13', ecA13))..add(DiagnosticsProperty('ecA4', ecA4))..add(DiagnosticsProperty('wF', wF))..add(DiagnosticsProperty('sCO2', sCO2))..add(DiagnosticsProperty('ecC2', ecC2))..add(DiagnosticsProperty('ecC34', ecC34))..add(DiagnosticsProperty('id', id));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is SteelLine&&(identical(other.name, name) || other.name == name)&&(identical(other.carbonType, carbonType) || other.carbonType == carbonType)&&(identical(other.length, length) || other.length == length)&&(identical(other.sectionSize, sectionSize) || other.sectionSize == sectionSize)&&(identical(other.density, density) || other.density == density)&&(identical(other.ecA13, ecA13) || other.ecA13 == ecA13)&&(identical(other.ecA4, ecA4) || other.ecA4 == ecA4)&&(identical(other.wF, wF) || other.wF == wF)&&(identical(other.sCO2, sCO2) || other.sCO2 == sCO2)&&(identical(other.ecC2, ecC2) || other.ecC2 == ecC2)&&(identical(other.ecC34, ecC34) || other.ecC34 == ecC34)&&(identical(other.id, id) || other.id == id));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,name,carbonType,length,sectionSize,density,ecA13,ecA4,wF,sCO2,ecC2,ecC34,id);

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'Carbon.steelLine(name: $name, carbonType: $carbonType, length: $length, sectionSize: $sectionSize, density: $density, ecA13: $ecA13, ecA4: $ecA4, wF: $wF, sCO2: $sCO2, ecC2: $ecC2, ecC34: $ecC34, id: $id)';
}


}

/// @nodoc
abstract mixin class $SteelLineCopyWith<$Res> implements $CarbonCopyWith<$Res> {
  factory $SteelLineCopyWith(SteelLine value, $Res Function(SteelLine) _then) = _$SteelLineCopyWithImpl;
@override @useResult
$Res call({
 String name, String carbonType, double length, double sectionSize, double density, double ecA13, double ecA4, double wF, double sCO2, double ecC2, double ecC34, String id
});




}
/// @nodoc
class _$SteelLineCopyWithImpl<$Res>
    implements $SteelLineCopyWith<$Res> {
  _$SteelLineCopyWithImpl(this._self, this._then);

  final SteelLine _self;
  final $Res Function(SteelLine) _then;

/// Create a copy of Carbon
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? name = null,Object? carbonType = null,Object? length = null,Object? sectionSize = null,Object? density = null,Object? ecA13 = null,Object? ecA4 = null,Object? wF = null,Object? sCO2 = null,Object? ecC2 = null,Object? ecC34 = null,Object? id = null,}) {
  return _then(SteelLine(
name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,carbonType: null == carbonType ? _self.carbonType : carbonType // ignore: cast_nullable_to_non_nullable
as String,length: null == length ? _self.length : length // ignore: cast_nullable_to_non_nullable
as double,sectionSize: null == sectionSize ? _self.sectionSize : sectionSize // ignore: cast_nullable_to_non_nullable
as double,density: null == density ? _self.density : density // ignore: cast_nullable_to_non_nullable
as double,ecA13: null == ecA13 ? _self.ecA13 : ecA13 // ignore: cast_nullable_to_non_nullable
as double,ecA4: null == ecA4 ? _self.ecA4 : ecA4 // ignore: cast_nullable_to_non_nullable
as double,wF: null == wF ? _self.wF : wF // ignore: cast_nullable_to_non_nullable
as double,sCO2: null == sCO2 ? _self.sCO2 : sCO2 // ignore: cast_nullable_to_non_nullable
as double,ecC2: null == ecC2 ? _self.ecC2 : ecC2 // ignore: cast_nullable_to_non_nullable
as double,ecC34: null == ecC34 ? _self.ecC34 : ecC34 // ignore: cast_nullable_to_non_nullable
as double,id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

/// @nodoc
@JsonSerializable()

class MetalArea extends Carbon with DiagnosticableTreeMixin {
  const MetalArea({this.name = '', this.carbonType = 'metalArea', this.area = 0.0, this.density = 13.79, this.ecA13 = 2.83, this.ecA4 = 0.003, this.wF = 0.01, this.sCO2 = -1.64, this.ecC2 = 0.009, this.ecC34 = 0.0012, this.id = '', final  String? $type}): $type = $type ?? 'metalArea',super._();
  factory MetalArea.fromJson(Map<String, dynamic> json) => _$MetalAreaFromJson(json);

@override@JsonKey() final  String name;
@override@JsonKey() final  String carbonType;
@JsonKey() final  double area;
//[m2]
@override@JsonKey() final  double density;
//* [kg/m2]
@override@JsonKey() final  double ecA13;
// production [kgCO2e/kg]
@override@JsonKey() final  double ecA4;
// transport to the site [kgCO2e/kg]
@override@JsonKey() final  double wF;
// waste factor, for ecA53 cals
@override@JsonKey() final  double sCO2;
// biogenic carbon sequestered from the air, for ecA53 cals
@override@JsonKey() final  double ecC2;
// transport away from site [kgCO2e/m2]
@override@JsonKey() final  double ecC34;
// recycle and disposal[kgCO2e/m2]
@override@JsonKey() final  String id;

@JsonKey(name: 'runtimeType')
final String $type;


/// Create a copy of Carbon
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$MetalAreaCopyWith<MetalArea> get copyWith => _$MetalAreaCopyWithImpl<MetalArea>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$MetalAreaToJson(this, );
}
@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'Carbon.metalArea'))
    ..add(DiagnosticsProperty('name', name))..add(DiagnosticsProperty('carbonType', carbonType))..add(DiagnosticsProperty('area', area))..add(DiagnosticsProperty('density', density))..add(DiagnosticsProperty('ecA13', ecA13))..add(DiagnosticsProperty('ecA4', ecA4))..add(DiagnosticsProperty('wF', wF))..add(DiagnosticsProperty('sCO2', sCO2))..add(DiagnosticsProperty('ecC2', ecC2))..add(DiagnosticsProperty('ecC34', ecC34))..add(DiagnosticsProperty('id', id));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is MetalArea&&(identical(other.name, name) || other.name == name)&&(identical(other.carbonType, carbonType) || other.carbonType == carbonType)&&(identical(other.area, area) || other.area == area)&&(identical(other.density, density) || other.density == density)&&(identical(other.ecA13, ecA13) || other.ecA13 == ecA13)&&(identical(other.ecA4, ecA4) || other.ecA4 == ecA4)&&(identical(other.wF, wF) || other.wF == wF)&&(identical(other.sCO2, sCO2) || other.sCO2 == sCO2)&&(identical(other.ecC2, ecC2) || other.ecC2 == ecC2)&&(identical(other.ecC34, ecC34) || other.ecC34 == ecC34)&&(identical(other.id, id) || other.id == id));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,name,carbonType,area,density,ecA13,ecA4,wF,sCO2,ecC2,ecC34,id);

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'Carbon.metalArea(name: $name, carbonType: $carbonType, area: $area, density: $density, ecA13: $ecA13, ecA4: $ecA4, wF: $wF, sCO2: $sCO2, ecC2: $ecC2, ecC34: $ecC34, id: $id)';
}


}

/// @nodoc
abstract mixin class $MetalAreaCopyWith<$Res> implements $CarbonCopyWith<$Res> {
  factory $MetalAreaCopyWith(MetalArea value, $Res Function(MetalArea) _then) = _$MetalAreaCopyWithImpl;
@override @useResult
$Res call({
 String name, String carbonType, double area, double density, double ecA13, double ecA4, double wF, double sCO2, double ecC2, double ecC34, String id
});




}
/// @nodoc
class _$MetalAreaCopyWithImpl<$Res>
    implements $MetalAreaCopyWith<$Res> {
  _$MetalAreaCopyWithImpl(this._self, this._then);

  final MetalArea _self;
  final $Res Function(MetalArea) _then;

/// Create a copy of Carbon
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? name = null,Object? carbonType = null,Object? area = null,Object? density = null,Object? ecA13 = null,Object? ecA4 = null,Object? wF = null,Object? sCO2 = null,Object? ecC2 = null,Object? ecC34 = null,Object? id = null,}) {
  return _then(MetalArea(
name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,carbonType: null == carbonType ? _self.carbonType : carbonType // ignore: cast_nullable_to_non_nullable
as String,area: null == area ? _self.area : area // ignore: cast_nullable_to_non_nullable
as double,density: null == density ? _self.density : density // ignore: cast_nullable_to_non_nullable
as double,ecA13: null == ecA13 ? _self.ecA13 : ecA13 // ignore: cast_nullable_to_non_nullable
as double,ecA4: null == ecA4 ? _self.ecA4 : ecA4 // ignore: cast_nullable_to_non_nullable
as double,wF: null == wF ? _self.wF : wF // ignore: cast_nullable_to_non_nullable
as double,sCO2: null == sCO2 ? _self.sCO2 : sCO2 // ignore: cast_nullable_to_non_nullable
as double,ecC2: null == ecC2 ? _self.ecC2 : ecC2 // ignore: cast_nullable_to_non_nullable
as double,ecC34: null == ecC34 ? _self.ecC34 : ecC34 // ignore: cast_nullable_to_non_nullable
as double,id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

// dart format on
