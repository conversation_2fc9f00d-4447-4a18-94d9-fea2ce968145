import 'package:riverpod_annotation/riverpod_annotation.dart';

//presentation layer
import '../../domain_layer/cantilever_scheme_input_global.dart';
import '../../domain_layer/slab_scheme_data.dart';
import '../../domain_layer/transfer_beam_scheme_input_global.dart';
import '../screen/homescreen.dart';

// domain layer

part 'cantilever_scheme_input_global_controller.g.dart';

@riverpod
class CantileverSchemeInputGlobalController
    extends _$CantileverSchemeInputGlobalController {
  @override
  FutureOr<CantileverSchemeInputGlobal> build() async {
    // print('Build: Transfer Beam Input Global');
    CantileverSchemeInputGlobal cantileverSchemeInputGlobal =
        await ref
            .read(appDatabaseControllerProvider.notifier)
            .queryCantileverSchemeInputGlobal();
    final data1 = ref.watch(loadingTablesControllerProvider);
    final data2 = ref.watch(slabSchemeDataControllerProvider);
    return data1.when(
      data: (loadingTables) async {
        return data2.when(
          data: (slabData) async {
            //* Validate loading usage
            final usages = loadingTables.map((x) => x.usage).toList();
            if (cantileverSchemeInputGlobal.usage == '' ||
                !usages.contains(cantileverSchemeInputGlobal.usage)) {
              cantileverSchemeInputGlobal = cantileverSchemeInputGlobal
                  .copyWith(usage: usages.first);
            }

            //*Validate slab thickness
            if (cantileverSchemeInputGlobal.useSlabSelected) {
              final selectedSlab = slabData.firstWhere(
                (scheme) => scheme.isSelected,
                orElse: () => SlabSchemeData(),
              );
              cantileverSchemeInputGlobal = cantileverSchemeInputGlobal
                  .copyWith(slabThickness: selectedSlab.strZone);
            }
            return cantileverSchemeInputGlobal;
          },
          error: (error, stackTrace) => CantileverSchemeInputGlobal(),
          loading: () => CantileverSchemeInputGlobal(),
        );
      },
      error: (error, stackTrace) => CantileverSchemeInputGlobal(),
      loading: () => CantileverSchemeInputGlobal(),
    );

  }

  Future<void> updateTable({
    String? id,
    double? span,
    double? loadWidth,
    double? strZone,
    double? fcu,
    double? cover,
    double? mainKValue,
    double? mainSteelRatio,
    int? minS,
    int? maxS,
    double? maxWidth,
    int? maxLayers,
    String? usage,
    double? slabThickness,
    bool? useSlabSelected,
  }) async {
    final x = await future;
    // Create a list of updates
    final newState = x.copyWith(
      id: id ?? x.id,
      span: span ?? x.span,
      loadWidth: loadWidth ?? x.loadWidth,
      strZone: strZone ?? x.strZone,
      fcu: fcu ?? x.fcu,
      cover: cover ?? x.cover,
      mainKValue: mainKValue ?? x.mainKValue,
      mainSteelRatio: mainSteelRatio ?? x.mainSteelRatio,
      minS: minS ?? x.minS,
      maxS: maxS ?? x.maxS,
      maxWidth: maxWidth ?? x.maxWidth,
      maxLayers: maxLayers ?? x.maxLayers,
      usage: usage ?? x.usage,
      slabThickness: slabThickness ?? x.slabThickness,
      useSlabSelected: useSlabSelected ?? x.useSlabSelected,
    );
    state = AsyncData(newState);
  }
}
