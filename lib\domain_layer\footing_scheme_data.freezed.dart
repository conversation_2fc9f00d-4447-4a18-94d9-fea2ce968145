// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'footing_scheme_data.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$FootingSchemeData {

 double get size; double get strZone; double get fcu; double get cover; String get groundType; double get soilNValue; double get footingTopLevel; double get waterTableLevel; double get rockCapacity; String get colShape; double get columnSize; double get slsLoad; double get ulsLoad; String get mainTopBar; String get mainBottomBar; String get mainLinks; String get punchingLinks; String get calsLog; bool get isSelected; String get footingSchemeDataId;
/// Create a copy of FootingSchemeData
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$FootingSchemeDataCopyWith<FootingSchemeData> get copyWith => _$FootingSchemeDataCopyWithImpl<FootingSchemeData>(this as FootingSchemeData, _$identity);

  /// Serializes this FootingSchemeData to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is FootingSchemeData&&(identical(other.size, size) || other.size == size)&&(identical(other.strZone, strZone) || other.strZone == strZone)&&(identical(other.fcu, fcu) || other.fcu == fcu)&&(identical(other.cover, cover) || other.cover == cover)&&(identical(other.groundType, groundType) || other.groundType == groundType)&&(identical(other.soilNValue, soilNValue) || other.soilNValue == soilNValue)&&(identical(other.footingTopLevel, footingTopLevel) || other.footingTopLevel == footingTopLevel)&&(identical(other.waterTableLevel, waterTableLevel) || other.waterTableLevel == waterTableLevel)&&(identical(other.rockCapacity, rockCapacity) || other.rockCapacity == rockCapacity)&&(identical(other.colShape, colShape) || other.colShape == colShape)&&(identical(other.columnSize, columnSize) || other.columnSize == columnSize)&&(identical(other.slsLoad, slsLoad) || other.slsLoad == slsLoad)&&(identical(other.ulsLoad, ulsLoad) || other.ulsLoad == ulsLoad)&&(identical(other.mainTopBar, mainTopBar) || other.mainTopBar == mainTopBar)&&(identical(other.mainBottomBar, mainBottomBar) || other.mainBottomBar == mainBottomBar)&&(identical(other.mainLinks, mainLinks) || other.mainLinks == mainLinks)&&(identical(other.punchingLinks, punchingLinks) || other.punchingLinks == punchingLinks)&&(identical(other.calsLog, calsLog) || other.calsLog == calsLog)&&(identical(other.isSelected, isSelected) || other.isSelected == isSelected)&&(identical(other.footingSchemeDataId, footingSchemeDataId) || other.footingSchemeDataId == footingSchemeDataId));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hashAll([runtimeType,size,strZone,fcu,cover,groundType,soilNValue,footingTopLevel,waterTableLevel,rockCapacity,colShape,columnSize,slsLoad,ulsLoad,mainTopBar,mainBottomBar,mainLinks,punchingLinks,calsLog,isSelected,footingSchemeDataId]);

@override
String toString() {
  return 'FootingSchemeData(size: $size, strZone: $strZone, fcu: $fcu, cover: $cover, groundType: $groundType, soilNValue: $soilNValue, footingTopLevel: $footingTopLevel, waterTableLevel: $waterTableLevel, rockCapacity: $rockCapacity, colShape: $colShape, columnSize: $columnSize, slsLoad: $slsLoad, ulsLoad: $ulsLoad, mainTopBar: $mainTopBar, mainBottomBar: $mainBottomBar, mainLinks: $mainLinks, punchingLinks: $punchingLinks, calsLog: $calsLog, isSelected: $isSelected, footingSchemeDataId: $footingSchemeDataId)';
}


}

/// @nodoc
abstract mixin class $FootingSchemeDataCopyWith<$Res>  {
  factory $FootingSchemeDataCopyWith(FootingSchemeData value, $Res Function(FootingSchemeData) _then) = _$FootingSchemeDataCopyWithImpl;
@useResult
$Res call({
 double size, double strZone, double fcu, double cover, String groundType, double soilNValue, double footingTopLevel, double waterTableLevel, double rockCapacity, String colShape, double columnSize, double slsLoad, double ulsLoad, String mainTopBar, String mainBottomBar, String mainLinks, String punchingLinks, String calsLog, bool isSelected, String footingSchemeDataId
});




}
/// @nodoc
class _$FootingSchemeDataCopyWithImpl<$Res>
    implements $FootingSchemeDataCopyWith<$Res> {
  _$FootingSchemeDataCopyWithImpl(this._self, this._then);

  final FootingSchemeData _self;
  final $Res Function(FootingSchemeData) _then;

/// Create a copy of FootingSchemeData
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? size = null,Object? strZone = null,Object? fcu = null,Object? cover = null,Object? groundType = null,Object? soilNValue = null,Object? footingTopLevel = null,Object? waterTableLevel = null,Object? rockCapacity = null,Object? colShape = null,Object? columnSize = null,Object? slsLoad = null,Object? ulsLoad = null,Object? mainTopBar = null,Object? mainBottomBar = null,Object? mainLinks = null,Object? punchingLinks = null,Object? calsLog = null,Object? isSelected = null,Object? footingSchemeDataId = null,}) {
  return _then(_self.copyWith(
size: null == size ? _self.size : size // ignore: cast_nullable_to_non_nullable
as double,strZone: null == strZone ? _self.strZone : strZone // ignore: cast_nullable_to_non_nullable
as double,fcu: null == fcu ? _self.fcu : fcu // ignore: cast_nullable_to_non_nullable
as double,cover: null == cover ? _self.cover : cover // ignore: cast_nullable_to_non_nullable
as double,groundType: null == groundType ? _self.groundType : groundType // ignore: cast_nullable_to_non_nullable
as String,soilNValue: null == soilNValue ? _self.soilNValue : soilNValue // ignore: cast_nullable_to_non_nullable
as double,footingTopLevel: null == footingTopLevel ? _self.footingTopLevel : footingTopLevel // ignore: cast_nullable_to_non_nullable
as double,waterTableLevel: null == waterTableLevel ? _self.waterTableLevel : waterTableLevel // ignore: cast_nullable_to_non_nullable
as double,rockCapacity: null == rockCapacity ? _self.rockCapacity : rockCapacity // ignore: cast_nullable_to_non_nullable
as double,colShape: null == colShape ? _self.colShape : colShape // ignore: cast_nullable_to_non_nullable
as String,columnSize: null == columnSize ? _self.columnSize : columnSize // ignore: cast_nullable_to_non_nullable
as double,slsLoad: null == slsLoad ? _self.slsLoad : slsLoad // ignore: cast_nullable_to_non_nullable
as double,ulsLoad: null == ulsLoad ? _self.ulsLoad : ulsLoad // ignore: cast_nullable_to_non_nullable
as double,mainTopBar: null == mainTopBar ? _self.mainTopBar : mainTopBar // ignore: cast_nullable_to_non_nullable
as String,mainBottomBar: null == mainBottomBar ? _self.mainBottomBar : mainBottomBar // ignore: cast_nullable_to_non_nullable
as String,mainLinks: null == mainLinks ? _self.mainLinks : mainLinks // ignore: cast_nullable_to_non_nullable
as String,punchingLinks: null == punchingLinks ? _self.punchingLinks : punchingLinks // ignore: cast_nullable_to_non_nullable
as String,calsLog: null == calsLog ? _self.calsLog : calsLog // ignore: cast_nullable_to_non_nullable
as String,isSelected: null == isSelected ? _self.isSelected : isSelected // ignore: cast_nullable_to_non_nullable
as bool,footingSchemeDataId: null == footingSchemeDataId ? _self.footingSchemeDataId : footingSchemeDataId // ignore: cast_nullable_to_non_nullable
as String,
  ));
}

}


/// Adds pattern-matching-related methods to [FootingSchemeData].
extension FootingSchemeDataPatterns on FootingSchemeData {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _FootingSchemeData value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _FootingSchemeData() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _FootingSchemeData value)  $default,){
final _that = this;
switch (_that) {
case _FootingSchemeData():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _FootingSchemeData value)?  $default,){
final _that = this;
switch (_that) {
case _FootingSchemeData() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( double size,  double strZone,  double fcu,  double cover,  String groundType,  double soilNValue,  double footingTopLevel,  double waterTableLevel,  double rockCapacity,  String colShape,  double columnSize,  double slsLoad,  double ulsLoad,  String mainTopBar,  String mainBottomBar,  String mainLinks,  String punchingLinks,  String calsLog,  bool isSelected,  String footingSchemeDataId)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _FootingSchemeData() when $default != null:
return $default(_that.size,_that.strZone,_that.fcu,_that.cover,_that.groundType,_that.soilNValue,_that.footingTopLevel,_that.waterTableLevel,_that.rockCapacity,_that.colShape,_that.columnSize,_that.slsLoad,_that.ulsLoad,_that.mainTopBar,_that.mainBottomBar,_that.mainLinks,_that.punchingLinks,_that.calsLog,_that.isSelected,_that.footingSchemeDataId);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( double size,  double strZone,  double fcu,  double cover,  String groundType,  double soilNValue,  double footingTopLevel,  double waterTableLevel,  double rockCapacity,  String colShape,  double columnSize,  double slsLoad,  double ulsLoad,  String mainTopBar,  String mainBottomBar,  String mainLinks,  String punchingLinks,  String calsLog,  bool isSelected,  String footingSchemeDataId)  $default,) {final _that = this;
switch (_that) {
case _FootingSchemeData():
return $default(_that.size,_that.strZone,_that.fcu,_that.cover,_that.groundType,_that.soilNValue,_that.footingTopLevel,_that.waterTableLevel,_that.rockCapacity,_that.colShape,_that.columnSize,_that.slsLoad,_that.ulsLoad,_that.mainTopBar,_that.mainBottomBar,_that.mainLinks,_that.punchingLinks,_that.calsLog,_that.isSelected,_that.footingSchemeDataId);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( double size,  double strZone,  double fcu,  double cover,  String groundType,  double soilNValue,  double footingTopLevel,  double waterTableLevel,  double rockCapacity,  String colShape,  double columnSize,  double slsLoad,  double ulsLoad,  String mainTopBar,  String mainBottomBar,  String mainLinks,  String punchingLinks,  String calsLog,  bool isSelected,  String footingSchemeDataId)?  $default,) {final _that = this;
switch (_that) {
case _FootingSchemeData() when $default != null:
return $default(_that.size,_that.strZone,_that.fcu,_that.cover,_that.groundType,_that.soilNValue,_that.footingTopLevel,_that.waterTableLevel,_that.rockCapacity,_that.colShape,_that.columnSize,_that.slsLoad,_that.ulsLoad,_that.mainTopBar,_that.mainBottomBar,_that.mainLinks,_that.punchingLinks,_that.calsLog,_that.isSelected,_that.footingSchemeDataId);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _FootingSchemeData extends FootingSchemeData {
   _FootingSchemeData({this.size = 5.0, this.strZone = 500.0, this.fcu = 45.0, this.cover = 35.0, this.groundType = 'Soil', this.soilNValue = 50, this.footingTopLevel = 0, this.waterTableLevel = -3, this.rockCapacity = 1000, this.colShape = 'Sqaure', this.columnSize = 1000.0, this.slsLoad = 2000.0, this.ulsLoad = 3000.0, this.mainTopBar = '', this.mainBottomBar = '', this.mainLinks = '', this.punchingLinks = '', this.calsLog = '', this.isSelected = false, this.footingSchemeDataId = '1'}): super._();
  factory _FootingSchemeData.fromJson(Map<String, dynamic> json) => _$FootingSchemeDataFromJson(json);

@override@JsonKey() final  double size;
@override@JsonKey() final  double strZone;
@override@JsonKey() final  double fcu;
@override@JsonKey() final  double cover;
@override@JsonKey() final  String groundType;
@override@JsonKey() final  double soilNValue;
@override@JsonKey() final  double footingTopLevel;
@override@JsonKey() final  double waterTableLevel;
@override@JsonKey() final  double rockCapacity;
@override@JsonKey() final  String colShape;
@override@JsonKey() final  double columnSize;
@override@JsonKey() final  double slsLoad;
@override@JsonKey() final  double ulsLoad;
@override@JsonKey() final  String mainTopBar;
@override@JsonKey() final  String mainBottomBar;
@override@JsonKey() final  String mainLinks;
@override@JsonKey() final  String punchingLinks;
@override@JsonKey() final  String calsLog;
@override@JsonKey() final  bool isSelected;
@override@JsonKey() final  String footingSchemeDataId;

/// Create a copy of FootingSchemeData
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$FootingSchemeDataCopyWith<_FootingSchemeData> get copyWith => __$FootingSchemeDataCopyWithImpl<_FootingSchemeData>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$FootingSchemeDataToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _FootingSchemeData&&(identical(other.size, size) || other.size == size)&&(identical(other.strZone, strZone) || other.strZone == strZone)&&(identical(other.fcu, fcu) || other.fcu == fcu)&&(identical(other.cover, cover) || other.cover == cover)&&(identical(other.groundType, groundType) || other.groundType == groundType)&&(identical(other.soilNValue, soilNValue) || other.soilNValue == soilNValue)&&(identical(other.footingTopLevel, footingTopLevel) || other.footingTopLevel == footingTopLevel)&&(identical(other.waterTableLevel, waterTableLevel) || other.waterTableLevel == waterTableLevel)&&(identical(other.rockCapacity, rockCapacity) || other.rockCapacity == rockCapacity)&&(identical(other.colShape, colShape) || other.colShape == colShape)&&(identical(other.columnSize, columnSize) || other.columnSize == columnSize)&&(identical(other.slsLoad, slsLoad) || other.slsLoad == slsLoad)&&(identical(other.ulsLoad, ulsLoad) || other.ulsLoad == ulsLoad)&&(identical(other.mainTopBar, mainTopBar) || other.mainTopBar == mainTopBar)&&(identical(other.mainBottomBar, mainBottomBar) || other.mainBottomBar == mainBottomBar)&&(identical(other.mainLinks, mainLinks) || other.mainLinks == mainLinks)&&(identical(other.punchingLinks, punchingLinks) || other.punchingLinks == punchingLinks)&&(identical(other.calsLog, calsLog) || other.calsLog == calsLog)&&(identical(other.isSelected, isSelected) || other.isSelected == isSelected)&&(identical(other.footingSchemeDataId, footingSchemeDataId) || other.footingSchemeDataId == footingSchemeDataId));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hashAll([runtimeType,size,strZone,fcu,cover,groundType,soilNValue,footingTopLevel,waterTableLevel,rockCapacity,colShape,columnSize,slsLoad,ulsLoad,mainTopBar,mainBottomBar,mainLinks,punchingLinks,calsLog,isSelected,footingSchemeDataId]);

@override
String toString() {
  return 'FootingSchemeData(size: $size, strZone: $strZone, fcu: $fcu, cover: $cover, groundType: $groundType, soilNValue: $soilNValue, footingTopLevel: $footingTopLevel, waterTableLevel: $waterTableLevel, rockCapacity: $rockCapacity, colShape: $colShape, columnSize: $columnSize, slsLoad: $slsLoad, ulsLoad: $ulsLoad, mainTopBar: $mainTopBar, mainBottomBar: $mainBottomBar, mainLinks: $mainLinks, punchingLinks: $punchingLinks, calsLog: $calsLog, isSelected: $isSelected, footingSchemeDataId: $footingSchemeDataId)';
}


}

/// @nodoc
abstract mixin class _$FootingSchemeDataCopyWith<$Res> implements $FootingSchemeDataCopyWith<$Res> {
  factory _$FootingSchemeDataCopyWith(_FootingSchemeData value, $Res Function(_FootingSchemeData) _then) = __$FootingSchemeDataCopyWithImpl;
@override @useResult
$Res call({
 double size, double strZone, double fcu, double cover, String groundType, double soilNValue, double footingTopLevel, double waterTableLevel, double rockCapacity, String colShape, double columnSize, double slsLoad, double ulsLoad, String mainTopBar, String mainBottomBar, String mainLinks, String punchingLinks, String calsLog, bool isSelected, String footingSchemeDataId
});




}
/// @nodoc
class __$FootingSchemeDataCopyWithImpl<$Res>
    implements _$FootingSchemeDataCopyWith<$Res> {
  __$FootingSchemeDataCopyWithImpl(this._self, this._then);

  final _FootingSchemeData _self;
  final $Res Function(_FootingSchemeData) _then;

/// Create a copy of FootingSchemeData
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? size = null,Object? strZone = null,Object? fcu = null,Object? cover = null,Object? groundType = null,Object? soilNValue = null,Object? footingTopLevel = null,Object? waterTableLevel = null,Object? rockCapacity = null,Object? colShape = null,Object? columnSize = null,Object? slsLoad = null,Object? ulsLoad = null,Object? mainTopBar = null,Object? mainBottomBar = null,Object? mainLinks = null,Object? punchingLinks = null,Object? calsLog = null,Object? isSelected = null,Object? footingSchemeDataId = null,}) {
  return _then(_FootingSchemeData(
size: null == size ? _self.size : size // ignore: cast_nullable_to_non_nullable
as double,strZone: null == strZone ? _self.strZone : strZone // ignore: cast_nullable_to_non_nullable
as double,fcu: null == fcu ? _self.fcu : fcu // ignore: cast_nullable_to_non_nullable
as double,cover: null == cover ? _self.cover : cover // ignore: cast_nullable_to_non_nullable
as double,groundType: null == groundType ? _self.groundType : groundType // ignore: cast_nullable_to_non_nullable
as String,soilNValue: null == soilNValue ? _self.soilNValue : soilNValue // ignore: cast_nullable_to_non_nullable
as double,footingTopLevel: null == footingTopLevel ? _self.footingTopLevel : footingTopLevel // ignore: cast_nullable_to_non_nullable
as double,waterTableLevel: null == waterTableLevel ? _self.waterTableLevel : waterTableLevel // ignore: cast_nullable_to_non_nullable
as double,rockCapacity: null == rockCapacity ? _self.rockCapacity : rockCapacity // ignore: cast_nullable_to_non_nullable
as double,colShape: null == colShape ? _self.colShape : colShape // ignore: cast_nullable_to_non_nullable
as String,columnSize: null == columnSize ? _self.columnSize : columnSize // ignore: cast_nullable_to_non_nullable
as double,slsLoad: null == slsLoad ? _self.slsLoad : slsLoad // ignore: cast_nullable_to_non_nullable
as double,ulsLoad: null == ulsLoad ? _self.ulsLoad : ulsLoad // ignore: cast_nullable_to_non_nullable
as double,mainTopBar: null == mainTopBar ? _self.mainTopBar : mainTopBar // ignore: cast_nullable_to_non_nullable
as String,mainBottomBar: null == mainBottomBar ? _self.mainBottomBar : mainBottomBar // ignore: cast_nullable_to_non_nullable
as String,mainLinks: null == mainLinks ? _self.mainLinks : mainLinks // ignore: cast_nullable_to_non_nullable
as String,punchingLinks: null == punchingLinks ? _self.punchingLinks : punchingLinks // ignore: cast_nullable_to_non_nullable
as String,calsLog: null == calsLog ? _self.calsLog : calsLog // ignore: cast_nullable_to_non_nullable
as String,isSelected: null == isSelected ? _self.isSelected : isSelected // ignore: cast_nullable_to_non_nullable
as bool,footingSchemeDataId: null == footingSchemeDataId ? _self.footingSchemeDataId : footingSchemeDataId // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

// dart format on
