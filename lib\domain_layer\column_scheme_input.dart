import 'package:freezed_annotation/freezed_annotation.dart';
// import 'package:flutter/foundation.dart';
part 'column_scheme_input.freezed.dart';
part 'column_scheme_input.g.dart';

@freezed
abstract class ColumnSchemeInput with _$ColumnSchemeInput{
  const ColumnSchemeInput._();
  factory ColumnSchemeInput({
    @Default('') String usage,
    @Default(150.0) double slabThickness,
    @Default(8) double loadWidth,
    @Default(12) double loadLength,
    @Default(1) int nosOfFloor,
    @Default('') String columnSchemeInputId, //will be overriden  as soon as new instance created
  }) = _ColumnSchemeInput;

  factory ColumnSchemeInput.fromJson(Map<String, Object?> json) =>
      _$ColumnSchemeInputFromJson(json);
}
