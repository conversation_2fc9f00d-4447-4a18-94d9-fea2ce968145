// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'strzone_table.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_StrZoneTable _$StrZoneTableFromJson(Map<String, dynamic> json) =>
    _StrZoneTable(
      floor: json['floor'] as String? ?? 'Floor',
      height: (json['height'] as num?)?.toDouble() ?? 3500.0,
      finish: (json['finish'] as num?)?.toDouble() ?? 50,
      service: (json['service'] as num?)?.toDouble() ?? 300,
      clear: (json['clear'] as num?)?.toDouble() ?? 2300,
      nosOfFloor: (json['nosOfFloor'] as num?)?.toInt() ?? 1,
      strZoneId: json['strZoneId'] as String? ?? '',
    );

Map<String, dynamic> _$StrZoneTableToJson(_StrZoneTable instance) =>
    <String, dynamic>{
      'floor': instance.floor,
      'height': instance.height,
      'finish': instance.finish,
      'service': instance.service,
      'clear': instance.clear,
      'nosOfFloor': instance.nosOfFloor,
      'strZoneId': instance.strZoneId,
    };
