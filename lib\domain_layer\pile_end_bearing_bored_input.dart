import 'package:freezed_annotation/freezed_annotation.dart';
// import 'package:flutter/foundation.dart';
part 'pile_end_bearing_bored_input.freezed.dart';
part 'pile_end_bearing_bored_input.g.dart';

@freezed
abstract class PileEndBearingBoredInput with _$PileEndBearingBoredInput{
  const PileEndBearingBoredInput._();
  factory PileEndBearingBoredInput({
    @Default(1000) double safeBearing,
    @Default(1) double fos,
    @Default(45) double fcu,
    @Default(30) double maxPileLength,
    @Default(2000) double maxPileDiameter,
    @Default(1.65) double ratioOfBelloutDia,
    @Default(0.04) double maxSteelRatio,
    @Default(1000) double slsLoad,
    @Default(2000) double ulsLoad,
    @Default(200) double diaIncrement,
    @Default(false) bool useSelectColLoad,
    @Default(1.0) double colLoadFactor,
    @Default('1') String id, //will be overriden  as soon as new instance created
  }) = _PileEndBearingBoredInput;

  factory PileEndBearingBoredInput.fromJson(Map<String, Object?> json) =>
      _$PileEndBearingBoredInputFromJson(json); 
}
