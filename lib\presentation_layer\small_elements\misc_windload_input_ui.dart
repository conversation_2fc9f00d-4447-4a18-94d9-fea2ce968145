import 'dart:ffi';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:nanoid2/nanoid2.dart';
import 'package:structify/presentation_layer/small_elements/button/wind_load_button.dart';

//domain layer
import '../../domain_layer/column_scheme_data.dart';
import '../../domain_layer/data_struct/preferences.dart';

// presentation layer
import '../../domain_layer/slab_scheme_data.dart';
import '../screen/homescreen.dart';
import 'input/custom_stateful_double_input.dart';
import 'input/custom_stateful_text_input.dart';
import 'popup/custom_popup.dart';
import 'input/custom_stateful_dropList.dart';
import 'input/custom_stateful_int_input.dart';
import 'sketch/draw_transfer_beam_loading_info.dart';
import 'button/function_button.dart';
import 'button/selection_button.dart';

class WindLoadInputUi extends ConsumerStatefulWidget {
  const WindLoadInputUi({
    this.isExpanded,
    this.maxHeight,
    this.minHeight,
    super.key,
  });

  final bool? isExpanded;
  final double? maxHeight;
  final double? minHeight;

  @override
  ConsumerState<ConsumerStatefulWidget> createState() =>
      _WindLoadInputUiState();
}

class _WindLoadInputUiState extends ConsumerState<WindLoadInputUi>
    with WidgetsBindingObserver {
  late bool _isExpanded;
  late double _maxHeight;
  late double _minHeight;
  late CustomStatefulTextInput _usageInput;
  late String _id;
  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void initState() {
    WidgetsBinding.instance.addObserver(this);
    _isExpanded = widget.isExpanded ?? false;
    _maxHeight = widget.maxHeight ?? 300;
    _minHeight = widget.minHeight ?? 50;
    _usageInput = CustomStatefulTextInput();
    _id = _generateTaskID();

    // _updateHeights();

    super.initState();
  }

  @override
  void didChangeDependencies() {
    _updateHeights();
    super.didChangeDependencies();
    // Update heights when dependencies change, including after initState
  }

  @override
  void didChangeMetrics() {
    // This method is called when the metrics change (like resizing the window)
    setState(() {
      _updateHeights();
    });
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;
    final globalData = ref.watch(globalDataControllerProvider);
    final windLoadInput = ref.watch(windLoadInputControllerProvider);
    final windLoadInputGlobal = ref.watch(
      windLoadInputGlobalControllerProvider,
    );
    final scrollController = ScrollController();
    final int containerOpacity = 175;
    final int textOpacity = 225;

    final NumberFormat f0 = NumberFormat('0'),
        f1 = NumberFormat('0.0'),
        f3 = NumberFormat('0.000');

    return globalData.when(
      data: (data) {
        late final List<String> units;
        switch (data.unit) {
          case 'metrics':
            units = PreferredUnit.metrics;
            break;
          case 'imperial':
            units = PreferredUnit.imperial;
            break;
          default:
            units = PreferredUnit.metrics;
            break;
        }

        return windLoadInputGlobal.when(
          data: (inputGlobal) {
            return windLoadInput.when(
              data: (input) {
                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Padding(
                      padding: const EdgeInsets.fromLTRB(10, 0, 10, 0),
                      child: Divider(),
                    ),
                    Padding(
                      padding: const EdgeInsets.fromLTRB(10, 0, 0, 0),
                      child: Align(
                        alignment: Alignment.centerLeft,
                        child: Wrap(
                          crossAxisAlignment: WrapCrossAlignment.center,
                          children: [
                            Wrap(
                              crossAxisAlignment: WrapCrossAlignment.center,
                              children: [
                                IconButton(
                                  icon: Icon(
                                    _isExpanded
                                        ? Icons.keyboard_arrow_up
                                        : Icons.keyboard_arrow_down,
                                    color: colorScheme.onSurface,
                                  ),
                                  onPressed: () {
                                    setState(() {
                                      _isExpanded = !_isExpanded;
                                    });
                                  },
                                ),
                                Text(
                                  'Wind Load Inputs ',
                                  style: textTheme.titleLarge!.copyWith(
                                    color: colorScheme.onSurface,
                                  ),
                                ),
                              ],
                            ),
                            Container(
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(5.0),
                                color: colorScheme.surfaceContainer.withAlpha(
                                  225,
                                ),
                                border: Border.all(
                                  color: Colors.black.withAlpha(125),
                                ),
                              ),
                              child: Padding(
                                padding: const EdgeInsets.all(2.0),
                                child: Text(
                                  '(Total: ${input.length})',
                                  style: textTheme.labelMedium!.copyWith(
                                    color: colorScheme.onSurfaceVariant
                                        .withAlpha(225),
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.fromLTRB(10, 0, 10, 0),
                      child: Divider(),
                    ),
                    Flexible(
                      child: ConstrainedBox(
                        constraints: BoxConstraints(
                          maxHeight: _isExpanded ? _maxHeight : 0,
                        ),
                        child: Column(
                          children: [
                            Padding(
                              padding: const EdgeInsets.fromLTRB(
                                8.0,
                                0.0,
                                8.0,
                                0.0,
                              ),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Row(
                                    children: [
                                      Flexible(
                                        child: CustomStatefulDoubleInput(
                                          title:
                                              'Building Height, H [${units[3]}]',
                                          value: inputGlobal.h,
                                          listener: (hasFocus, value) async {
                                            if (!hasFocus) {
                                              await ref
                                                  .read(
                                                    windLoadInputGlobalControllerProvider
                                                        .notifier,
                                                  )
                                                  .updateTable(h: value);
                                            }
                                          },
                                        ),
                                      ),
                                      SizedBox(width: 5.0),
                                      Flexible(
                                        child: CustomStatefulDoubleInput(
                                          title:
                                              'Building Width at Top, B [${units[3]}]',
                                          value: inputGlobal.bTop,
                                          tooltipText:
                                              'Building Load Width at Top\n(perpendicular to wind load)',
                                          listener: (hasFocus, value) async {
                                            if (!hasFocus) {
                                              await ref
                                                  .read(
                                                    windLoadInputGlobalControllerProvider
                                                        .notifier,
                                                  )
                                                  .updateTable(bTop: value);
                                            }
                                          },
                                        ),
                                      ),
                                      SizedBox(width: 5.0),
                                      Flexible(
                                        child: CustomStatefulDoubleInput(
                                          title:
                                              'Building Depth at Top, D [${units[3]}]',
                                          value: inputGlobal.dTop,
                                          tooltipText:
                                              'Building Depth at Top\n(parallel to wind load)',
                                          listener: (hasFocus, value) async {
                                            if (!hasFocus) {
                                              await ref
                                                  .read(
                                                    windLoadInputGlobalControllerProvider
                                                        .notifier,
                                                  )
                                                  .updateTable(dTop: value);
                                            }
                                          },
                                        ),
                                      ),
                                      SizedBox(width: 5.0),
                                      Flexible(
                                        child: CustomStatefulDoubleInput(
                                          title: 'Size Factor',
                                          value: inputGlobal.sS,
                                          tooltipText:
                                              'Size Factor as per\nHKCoPWind2019 Section 5\nmax. ~= 1.11',
                                          listener: (hasFocus, value) async {
                                            if (!hasFocus) {
                                              await ref
                                                  .read(
                                                    windLoadInputGlobalControllerProvider
                                                        .notifier,
                                                  )
                                                  .updateTable(sS: value);
                                            }
                                          },
                                        ),
                                      ),
                                    ],
                                  ),
                                  SizedBox(height: 10.0),
                                  Row(
                                    mainAxisSize: MainAxisSize.min,
                                    mainAxisAlignment: MainAxisAlignment.start,
                                    children: [
                                      Flexible(
                                        child: CustomStatefulDoubleInput(
                                          title: 'Load Factor',
                                          tooltipText:
                                              'Approximate the effect of\nacross-wind and torsion effect',
                                          value:
                                              inputGlobal.amplificationFactor,
                                          listener: (hasFocus, value) async {
                                            if (!hasFocus) {
                                              await ref
                                                  .read(
                                                    windLoadInputGlobalControllerProvider
                                                        .notifier,
                                                  )
                                                  .updateTable(
                                                    amplificationFactor: value,
                                                  );
                                            }
                                          },
                                        ),
                                      ),
                                      SizedBox(width: 5.0),
                                      Flexible(
                                        child: Column(
                                          mainAxisSize: MainAxisSize.min,
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Text(
                                              'Bldg Type: ',
                                              style: textTheme.labelSmall!
                                                  .copyWith(
                                                    color:
                                                        colorScheme.onSurface,
                                                  ),
                                            ),
                                            CustomStatefulDropDown(
                                              items: ['Concrete', 'Steel'],
                                              selectedValue:
                                                  inputGlobal.bldgType,
                                              onTap: (selectedValue) async {
                                                await ref
                                                    .read(
                                                      windLoadInputGlobalControllerProvider
                                                          .notifier,
                                                    )
                                                    .updateTable(
                                                      bldgType: selectedValue,
                                                    );
                                              },
                                            ),
                                          ],
                                        ),
                                      ),
                                      SizedBox(width: 5.0),
                                      WindLoadButton(
                                        onChange: (result) async {
                                          final input = await ref.read(
                                            windLoadInputControllerProvider
                                                .future,
                                          );
                                          final sortedInput = [
                                            ...input,
                                          ]..sort((a, b) => a.z.compareTo(b.z));

                                          if (result != null) {
                                            final nosOfFloor = int.parse(
                                              result['nosOfFloor'].toString(),
                                            );
                                            final floorHeight = double.parse(
                                              result['floorHeight'].toString(),
                                            );
                                            final b = double.parse(
                                              result['b'].toString(),
                                            );
                                            double totalZ = sortedInput.last.z;
                                            for (
                                              int i = 0;
                                              i < nosOfFloor;
                                              i++
                                            ) {
                                              totalZ += floorHeight;
                                              await ref
                                                  .read(
                                                    windLoadInputControllerProvider
                                                        .notifier,
                                                  )
                                                  .addTable(totalZ, b);
                                            }
                                          }
                                        },
                                      ),
                                      Flexible(child: SizedBox(width: 5.0)),
                                    ],
                                  ),
                                  SizedBox(height: 10.0),
                                ],
                              ),
                            ),
                            SizedBox(height: 10.0),
                            Flexible(
                              child: ClipRect(
                                child: Scrollbar(
                                  controller: scrollController,
                                  thumbVisibility: true,
                                  child: ListView.builder(
                                    controller: scrollController,
                                    itemCount: input.length,
                                    itemBuilder: (context, index) {
                                      return Padding(
                                        padding: const EdgeInsets.fromLTRB(
                                          8.0,
                                          4.0,
                                          8.0,
                                          4.0,
                                        ),
                                        child: Row(
                                          mainAxisSize: MainAxisSize.min,
                                          mainAxisAlignment:
                                              MainAxisAlignment.start,
                                          children: [
                                            Container(
                                              decoration: BoxDecoration(
                                                shape: BoxShape.circle,
                                                color: colorScheme
                                                    .surfaceContainer
                                                    .withAlpha(200),
                                                // borderRadius: BorderRadius.circular(5),
                                                border: Border.all(
                                                  color: Colors.black,
                                                ),
                                              ),
                                              child: Padding(
                                                padding: const EdgeInsets.all(
                                                  4.0,
                                                ),
                                                child: Text(
                                                  '${index + 1}',
                                                  style: textTheme.labelLarge!
                                                      .copyWith(
                                                        color: colorScheme
                                                            .onSurfaceVariant
                                                            .withAlpha(250),
                                                      ),
                                                ),
                                              ),
                                            ),
                                            IconButton(
                                              icon: Icon(
                                                Icons.copy_all_outlined,
                                                color: colorScheme.onSurface,
                                              ),
                                              onPressed: () async {
                                                await ref
                                                    .read(
                                                      windLoadInputControllerProvider
                                                          .notifier,
                                                    )
                                                    .copyAndInsertTable(index);
                                              },
                                            ),
                                            IconButton(
                                              icon: Icon(
                                                Icons.delete_outline,
                                                color: colorScheme.onSurface,
                                              ),
                                              onPressed: () async {
                                                await ref
                                                    .read(
                                                      windLoadInputControllerProvider
                                                          .notifier,
                                                    )
                                                    .deleteTable(
                                                      input[index].id,
                                                    );
                                              },
                                            ),
                                            SizedBox(width: 5.0),
                                            Flexible(
                                              child: CustomStatefulDoubleInput(
                                                key: ValueKey(
                                                  '${input[index].id}_z',
                                                ),
                                                title: 'z [${units[3]}]',
                                                tooltipText:
                                                    'Floor height from ground where\nwind pressure is considered',
                                                value: input[index].z,
                                                listener: (
                                                  hasFocus,
                                                  value,
                                                ) async {
                                                  if (!hasFocus) {
                                                    await ref
                                                        .read(
                                                          windLoadInputControllerProvider
                                                              .notifier,
                                                        )
                                                        .updateTable(
                                                          id: input[index].id,
                                                          z: value,
                                                        );
                                                  }
                                                },
                                              ),
                                            ),
                                            SizedBox(width: 5.0),
                                            Flexible(
                                              child: CustomStatefulDoubleInput(
                                                key: ValueKey(
                                                  '${input[index].id}_b',
                                                ),
                                                title: 'b [${units[3]}]',
                                                tooltipText:
                                                    'Plan width (perpendicular to wind) where\nwind pressure is considered',
                                                value: input[index].b,
                                                listener: (
                                                  hasFocus,
                                                  value,
                                                ) async {
                                                  if (!hasFocus) {
                                                    await ref
                                                        .read(
                                                          windLoadInputControllerProvider
                                                              .notifier,
                                                        )
                                                        .updateTable(
                                                          id: input[index].id,
                                                          b: value,
                                                        );
                                                  }
                                                },
                                              ),
                                            ),
                                            Flexible(
                                              child: SizedBox(width: 5.0),
                                            ),
                                          ],
                                        ),
                                      );
                                    },
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                );
              },
              error: (error, stackTrace) {
                return Text('Error: $error');
              },
              loading: () {
                return const CircularProgressIndicator();
              },
            );
          },
          error: (error, stackTrace) => Text(error.toString()),
          loading: () => CircularProgressIndicator(),
        );
      },
      error: (error, stackTrace) => Text(error.toString()),
      loading: () => CircularProgressIndicator(),
    );
  }

  void _updateHeights() {
    final double screenHeight = MediaQuery.of(context).size.height;
    _maxHeight =
        (widget.maxHeight == null)
            ? screenHeight * 0.6
            : (widget.maxHeight! > screenHeight * 0.6)
            ? screenHeight * 0.6
            : widget.maxHeight!;

    _minHeight =
        (widget.minHeight == null)
            ? screenHeight * 0.05
            : (widget.minHeight! > screenHeight * 0.05)
            ? screenHeight * 0.05
            : widget.minHeight!;

    // Adjust _maxHeight if needed
    if (_maxHeight < _minHeight) {
      _maxHeight = _minHeight;
    }
  }
}

String _generateTaskID() {
  String newID = nanoid(alphabet: Alphabet.noDoppelgangerSafe);
  newID = nanoid(alphabet: Alphabet.noDoppelgangerSafe);
  return newID;
}
