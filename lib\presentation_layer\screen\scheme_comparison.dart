import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:structify/data_layer/app_database.dart';
import 'package:drift_db_viewer/drift_db_viewer.dart';

//below for printing
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:printing/printing.dart';
import 'package:structify/misc/custom_func.dart';
import 'package:structify/presentation_layer/small_elements/misc_Intro_summary.dart';
import 'package:structify/presentation_layer/small_elements/misc_appraisal_summary.dart';
import 'package:structify/presentation_layer/small_elements/pile_socketed_scheme_summary.dart';
import 'package:structify/presentation_layer/small_elements/misc_scheme_comparison_summary.dart';

//domain layer
import '../../domain_layer/preferences.dart';

// presentation layer
import '../small_elements/dessign_assumption.dart';
import '../small_elements/button/function_button.dart';
import '../small_elements/misc_recommended_loading_summary.dart';
import 'homescreen.dart';

class SchemeComparison extends ConsumerWidget {
  SchemeComparison({super.key});

  final GlobalKey _globalKey = GlobalKey();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final ColorScheme colorScheme = Theme.of(context).colorScheme;
    final TextTheme textTheme = Theme.of(context).textTheme;
    final globalData = ref.watch(globalDataControllerProvider);
    ref.watch(appWatcherProvider);
    return globalData.when(
      data: (data) {
        return Scaffold(
          // appBar: AppBar(
          //   title: Text(
          //     'Loading Calculator',
          //     style: textTheme.titleLarge!.copyWith(
          //       color: colorScheme.onPrimary,
          //     ),
          //   ),
          //   backgroundColor: colorScheme.secondaryContainer,
          //   leading: IconButton(
          //     icon: Icon(
          //       Icons.arrow_back,
          //       color: colorScheme.onPrimary,
          //     ), // Customize the icon
          //     onPressed: () {
          //       // Custom action for the back button
          //       Navigator.pop(context); // Go back to the previous screen
          //     },
          //   ),
          //   actions: [
          //     IconButton(
          //       icon: Icon(
          //         Icons.print,
          //         color: colorScheme.onSecondaryContainer,
          //       ),
          //       onPressed: () {
          //         _exportListToPdf(context, ref, rowsPerPage: 14);
          //       },
          //     ),
          //     IconButton(
          //       icon: Icon(
          //         Icons.data_array_outlined,
          //         color: colorScheme.onSecondaryContainer,
          //       ), // Customize the icon
          //       onPressed: () {
          //         final db = AppDatabase(); //This should be a singleton
          //         Navigator.of(context).push(
          //           MaterialPageRoute(builder: (context) => DriftDbViewer(db)),
          //         );
          //       },
          //     ),
          //   ],
          // ),
          backgroundColor: colorScheme.surface,
          body: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Padding(
                padding: const EdgeInsets.all(8.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    FunctionButton(
                      labelIcon: Icon(Icons.print_outlined),
                      labelText: 'Summary',
                      onTap: (isPressed) async {
                        showSummary(context);
                      },
                    ),
                    SizedBox(width: 5.0),FunctionButton(
                      labelIcon: Icon(Icons.print_outlined),
                      labelText: 'Print',
                      onTap: (isPressed) async {
                        // await _exportListToPdf(context, ref, rowsPerPage: 13);
                      },
                    ),
                    SizedBox(width: 5.0),
                    FunctionButton(
                      labelIcon: Icon(Icons.data_array_outlined),
                      labelText: 'Data',
                      onTap: (isPressed) async {
                        final db = AppDatabase(); //This should be a singleton
                        Navigator.of(context).push(
                          MaterialPageRoute(
                            builder: (context) => DriftDbViewer(db),
                          ),
                        );
                      },
                    ),
                  ],
                ),
              ),
              SchemeComparisonSummary(isExpanded: true),
            ],
          ),
        );
      },
      error: (error, stackTrace) {
        return Text('Error: $error');
      },
      loading: () {
        return CircularProgressIndicator();
      },
    );
  }

  Future<void> _exportListToPdf(
    BuildContext context,
    WidgetRef ref, {
    int rowsPerPage = 10,
  }) async {
    //* Initialize parameters
    final pdf = pw.Document();
    final globalData = ref.read(globalDataControllerProvider.future);
    final pileSocketedScheme = ref.read(
      pileSocketedDataControllerProvider.future,
    );

    //*Color Scheme
    final ColorScheme colorScheme = Theme.of(context).colorScheme;
    final PdfColor bodyTextColor = PdfColor.fromInt(
      colorScheme.onSurface.toARGB32(),
    );
    final PdfColor titleTextColor = PdfColor.fromInt(
      colorScheme.onSurface.toARGB32(),
    );
    final PdfColor highlightTextColor = PdfColor.fromInt(
      colorScheme.error.toARGB32(),
    );

    //*Font Styles
    final bodyFont = await PdfGoogleFonts.openSansRegular();
    final titleFont = await PdfGoogleFonts.openSansBold();

    //* Text Styles
    final pw.TextStyle titleTextStyle = pw.TextStyle(
      fontSize: 12,
      color: PdfColors.black,
      font: titleFont,
    );
    final pw.TextStyle bodyTextStyle = pw.TextStyle(
      fontSize: 12,
      color: PdfColors.black,
      font: bodyFont,
    );
    final pw.TextStyle highlightText = pw.TextStyle(
      fontSize: 12,
      color: PdfColors.black,
      font: bodyFont,
    );

    //* Add page into the docuemnts
    pdf.addPage(
      pw.Page(
        margin: pw.EdgeInsets.fromLTRB(25, 35, 25, 35),
        pageFormat: PdfPageFormat.a4,
        orientation: pw.PageOrientation.portrait,
        build: (pw.Context context) {
          return pw.Padding(
            padding: const pw.EdgeInsets.fromLTRB(10, 0, 0, 0),
            child: pw.DefaultTextStyle(
              style: bodyTextStyle,
              child: pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.start,
                mainAxisSize: pw.MainAxisSize.min,
                children: [
                  pw.Align(
                    alignment: pw.Alignment.center,
                    child: pw.Text(
                      'Socketed Steel H Pile Schemes Summary',
                      style: pw.TextStyle(
                        fontSize: 20,
                        fontWeight: pw.FontWeight.bold,
                      ),
                    ),
                  ),
                  pw.SizedBox(height: 10.0),
                  pw.Row(
                    children: [
                      pw.Text('Section: ', style: titleTextStyle),
                      pw.Text('UBP 305x305x223'),
                    ],
                  ),
                  pw.Row(
                    children: [
                      pw.Text('Diameter: ', style: titleTextStyle),
                      pw.Text('610 mm'),
                    ],
                  ),
                  pw.Row(
                    children: [
                      pw.Text('Length: ', style: titleTextStyle),
                      pw.Text('11.5 m'),
                    ],
                  ),
                  pw.Row(
                    children: [
                      pw.Text('fcu (grout): ', style: titleTextStyle),
                      pw.Text('45 MPa'),
                    ],
                  ),
                  //* Pile Capacity
                  pw.Divider(),
                  pw.Text('Pile Capacity (ULS Load):', style: titleTextStyle),
                  pw.Text('= Fy*A'),
                  pw.Text('= 430*284*100/1000'),
                  pw.Text('= 12212 kN/pile', style: highlightText),
                  pw.Divider(),
                  pw.Text('Pile Capacity (SLS Load):', style: titleTextStyle),
                  pw.Text('= min(6106,6611,6955) '),
                  pw.Text(
                    '= 6106 kN/pile (see below cals)',
                    style: highlightText,
                  ),

                  //* Structural Capacity
                  pw.Text('Structural Capacity Limit:', style: titleTextStyle),
                  pw.Text('0.5*Fy*A'),
                  pw.Text('= 0.5*430*284*100/1000'),
                  pw.Text('= 6106 kN/pile', style: highlightText),
                  pw.Text('(reduced Fy as 16 mm < max(tw,Tf) < 40 mm)'),
                  pw.SizedBox(height: 10),
                  //* Shaft Capacity: Groutn to Rock
                  pw.Text(
                    'Shaft Capacity (grount and rock):',
                    style: titleTextStyle,
                  ),
                  pw.Text('\u03C3*\u03C0*D*L,'),
                  pw.Text('= 300*\u03C0*0.61*11.5 '),
                  pw.Text('= 6611 kN/pile', style: highlightText),
                  pw.SizedBox(height: 10),
                  //* Shaft Capacity: Groutn to Steel
                  pw.Text(
                    'Shaft Capacity (grount and steel):',
                    style: titleTextStyle,
                  ),
                  pw.Text('\u03C3*\u03C0*p*L, (p = surface area per meter)'),
                  pw.Text('= 320*\u03C0*1.89*11.5'),
                  pw.Text('= 6955 kN/pile', style: highlightText),
                ],
              ),
            ),
          );
        },
      ),
    );
    await Printing.layoutPdf(
      onLayout: (PdfPageFormat format) async => pdf.save(),
    );
  }
}

pw.Page _customPage(
  BuildContext context,
  WidgetRef ref, {
  int startFrom = 0,
  int? rowsPerPage,
}) {
  // final textStyle = Theme.of(context).textTheme.bodySmall;
  return pw.Page(
    build: (pw.Context context) {
      final pileFrictionalBoredSchemes = ref.read(
        pileFrictionalBoredDataControllerProvider,
      );
      final globaldata = ref.read(globalDataControllerProvider);

      return pileFrictionalBoredSchemes.when(
        data: (tables) {
          return globaldata.when(
            data: (data) {
              final double titleFontSize = 8;
              final double normalFontSize = 8;

              return pw.Center(
                child: pw.Column(
                  mainAxisAlignment: pw.MainAxisAlignment.start,
                  children: [
                    ...List.generate(rowsPerPage ?? tables.length - startFrom, (
                      index,
                    ) {
                      NumberFormat f0 = NumberFormat('0');
                      NumberFormat f3 = NumberFormat('0.000');

                      late List<String> unit;
                      switch (data.unit) {
                        case 'metrics':
                          unit = PreferredUnit.metrics;
                          break;
                        case 'imperial':
                          unit = PreferredUnit.imperial;
                          break;
                        default:
                          unit = PreferredUnit.metrics;
                      }
                      return pw.DefaultTextStyle(
                        style: pw.TextStyle(fontSize: normalFontSize),
                        child: pw.Column(
                          children: [
                            pw.Row(
                              children: [
                                pw.Text(
                                  '${index + startFrom + 1}',
                                  style: pw.TextStyle(
                                    fontSize: titleFontSize,
                                    fontWeight: pw.FontWeight.bold,
                                  ),
                                ),

                                _customVerticalDivider(),

                                pw.Column(
                                  crossAxisAlignment:
                                      pw.CrossAxisAlignment.start,
                                  children: [
                                    pw.Text(
                                      'Diameter:',
                                      style: pw.TextStyle(
                                        fontSize: titleFontSize,
                                        fontWeight: pw.FontWeight.bold,
                                      ),
                                    ),
                                    pw.Text(
                                      'Length [${unit[4]}]:',
                                      style: pw.TextStyle(
                                        fontSize: titleFontSize,
                                        fontWeight: pw.FontWeight.bold,
                                      ),
                                    ),
                                    pw.Text(
                                      'fcu [${unit[5]}]:',
                                      style: pw.TextStyle(
                                        fontSize: titleFontSize,
                                        fontWeight: pw.FontWeight.bold,
                                      ),
                                    ),
                                    pw.Text(
                                      'Rebar :',
                                      style: pw.TextStyle(
                                        fontSize: titleFontSize,
                                        fontWeight: pw.FontWeight.bold,
                                      ),
                                    ),
                                  ],
                                ),
                                pw.SizedBox(width: 5.0),
                                pw.Column(
                                  crossAxisAlignment:
                                      pw.CrossAxisAlignment.start,
                                  children: [
                                    pw.Text(
                                      '${tables[index + startFrom].diameter}',
                                      style: pw.TextStyle(
                                        fontSize: titleFontSize,
                                        fontWeight: pw.FontWeight.bold,
                                      ),
                                    ),
                                    pw.Text(
                                      '${tables[index + startFrom].length}',
                                      style: pw.TextStyle(
                                        fontSize: titleFontSize,
                                        fontWeight: pw.FontWeight.bold,
                                      ),
                                    ),
                                    pw.Text(
                                      f0.format(tables[index + startFrom].fcu),
                                      style: pw.TextStyle(
                                        fontSize: titleFontSize,
                                        fontWeight: pw.FontWeight.bold,
                                      ),
                                    ),
                                    pw.Text(
                                      tables[index + startFrom].rebar,
                                      style: pw.TextStyle(
                                        fontSize: titleFontSize,
                                        fontWeight: pw.FontWeight.bold,
                                      ),
                                    ),
                                  ],
                                ),

                                _customVerticalDivider(),

                                pw.Row(
                                  children: [
                                    pw.Column(
                                      crossAxisAlignment:
                                          pw.CrossAxisAlignment.start,

                                      children: [
                                        pw.Text('SLS Load [${unit[0]}]: '),
                                        pw.Text('ULS Load [${unit[0]}]: '),
                                        pw.Text(
                                          'Ground Capacity [${unit[0]}]: ',
                                        ),
                                        pw.Text(
                                          'Structural Capacity [${unit[0]}]: ',
                                        ),
                                      ],
                                    ),
                                    pw.SizedBox(width: 5.0),

                                    pw.Column(
                                      crossAxisAlignment:
                                          pw.CrossAxisAlignment.start,

                                      children: [
                                        pw.Text(
                                          f0.format(
                                            tables[index + startFrom].slsLoad,
                                          ),
                                        ),
                                        pw.Text(
                                          f0.format(
                                            tables[index + startFrom].ulsLoad,
                                          ),
                                        ),
                                        pw.Text(
                                          f0.format(
                                            tables[index + startFrom]
                                                .totalGroundResistance,
                                          ),
                                        ),
                                        pw.Text(
                                          f0.format(
                                            tables[index + startFrom]
                                                .strCapacity,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ],
                                ),
                                _customVerticalDivider(),
                              ],
                            ),
                            _customDivider(),
                          ],
                        ),
                      );
                    }),
                  ],
                ),
              );
            },
            error: (error, stacktrace) => pw.Text('Error: $error'),
            loading: () => pw.Text('Loading'),
          );
        },
        error: (error, stackTrace) => pw.Text('Error: $error'),
        loading: () => pw.Text('loading'),
      );
    },
  );
}

pw.Widget _customVerticalDivider() {
  return pw.Row(
    children: [
      pw.SizedBox(width: 15.0),
      pw.Container(width: 1.0, height: 30, color: PdfColors.grey400),
      pw.SizedBox(width: 15.0),
    ],
  );
}

pw.Widget _customDivider() {
  return pw.Divider(color: PdfColors.grey400);
}
