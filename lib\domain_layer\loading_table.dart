import 'package:freezed_annotation/freezed_annotation.dart';
// import 'package:flutter/foundation.dart';
part 'loading_table.freezed.dart';
part 'loading_table.g.dart';

@freezed
abstract class LoadingTable with _$LoadingTable{
  const LoadingTable._();
  factory LoadingTable({
    @Default('Usage') String usage,
    @Default(0.0) double finish,
    @Default(0.0) double service,
    @Default(0.0) double liveLoad,
    @Default('') String loadingTableId, //will be overriden  as soon as new instance created
  }) = _LoadingTable;

  factory LoadingTable.fromJson(Map<String, Object?> json) =>
      _$LoadingTableFromJson(json);
}
