import 'dart:math' as math;
import 'dart:typed_data';
import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:structify/domain_layer/steel_transfer_truss_scheme_input.dart';
import 'package:structify/domain_layer/steel_transfer_truss_scheme_input_global.dart';
import 'package:structify/misc/custom_func.dart';
import 'package:structify/presentation_layer/screen/homescreen.dart';
import 'package:vector_math/vector_math.dart' as vec;

import '../../../domain_layer/global_data.dart';
import '../../../domain_layer/preferences.dart';

class DrawSteelCantileverTrussInputInfo extends ConsumerWidget {
  DrawSteelCantileverTrussInputInfo({
    required this.sketchWidth,
    required this.sketchHeight,
    this.fontSize,
    super.key,
  });

  final double sketchWidth;
  final double sketchHeight;
  double? fontSize;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final data1 = ref.watch(steelTransferTrussSchemeInputControllerProvider);
    final data2 = ref.watch(
      steelTransferTrussSchemeInputGlobalControllerProvider,
    );
    final data3 = ref.watch(globalDataControllerProvider);

    final ColorScheme colorScheme = Theme.of(context).colorScheme;
    final TextTheme textTheme = Theme.of(context).textTheme;
    final constraints = BoxConstraints(
      maxWidth: sketchWidth,
      maxHeight: sketchHeight,
      minHeight: 100,
      minWidth: 100,
    );
    return data1.when(
      data: (input) {
        return data2.when(
          data: (inputGlobal) {
            return data3.when(
              data: (globalData) {
                return Center(
                  child: Container(
                    decoration: BoxDecoration(
                      color: colorScheme.surfaceContainer.withAlpha(100),
                      borderRadius: BorderRadius.circular(10.0),
                      border: Border.all(color: Colors.black.withAlpha(100)),
                    ),
                    width:
                        constraints.maxWidth == double.infinity
                            ? 100
                            : constraints.maxWidth,
                    height:
                        constraints.maxHeight == double.infinity
                            ? 100
                            : constraints.maxHeight,
                    child: CustomPaint(
                      painter: DrawSteelCantileverTrussLoadingPainter(
                        globalData: globalData,
                        boxConstraints: constraints,
                        fontSize: fontSize,
                        context: context,
                      ),
                    ),
                  ),
                );
              },
              error: (error, stackTrace) {
                return Text(error.toString());
              },
              loading: () {
                return const CircularProgressIndicator();
              },
            );
          },
          error: (error, stackTrace) {
            return Text(error.toString());
          },
          loading: () {
            return const CircularProgressIndicator();
          },
        );
      },
      error: (error, stackTrace) => Text(error.toString()),
      loading: () => CircularProgressIndicator(),
    );
  }
}

class DrawSteelCantileverTrussLoadingPainter extends CustomPainter {
  DrawSteelCantileverTrussLoadingPainter({
    required this.globalData,
    required this.boxConstraints,
    this.fontSize,
    required this.context,
  });

  final GlobalData globalData;
  final BoxConstraints boxConstraints;
  double? fontSize;
  final BuildContext context;

  @override
  void paint(Canvas canvas, Size size) {
    final ColorScheme colorScheme = Theme.of(context).colorScheme;
    final TextTheme textTheme = Theme.of(context).textTheme;
    final double secBeamSpacing;
    double offsetCounter = 25;
    final double minDim = size.width;
    final List<SteelTransferTrussSchemeInput> input = [
      SteelTransferTrussSchemeInput(pointLoad: 100, distA: 3),
    ];
    final SteelTransferTrussSchemeInputGlobal inputGlobal =
        SteelTransferTrussSchemeInputGlobal();

    final double ratio = minDim / inputGlobal.span * 4 / 5;
    late Offset startP1, endP1, startP2, endP2, startP3, endP3, startP4, endP4;

    fontSize = fontSize ?? math.min(12, math.min(size.width, size.height) / 8);

    Paint beamPaint =
        Paint()
          ..color = colorScheme.onSurface
          ..strokeCap = StrokeCap.round
          ..style = PaintingStyle.stroke
          ..strokeWidth = 2.0;

    Paint supportPaint =
        Paint()
          ..color = colorScheme.onSurface
          ..strokeCap = StrokeCap.round
          ..style = PaintingStyle.fill
          ..strokeWidth = 2;

    Paint loadPaint =
        Paint()
          ..color = Colors.red.withAlpha(150)
          ..strokeCap = StrokeCap.round
          ..style = PaintingStyle.fill
          ..strokeWidth = 1.0;
    Paint UDLPaint =
        Paint()
          ..color =  Colors.black.withAlpha(100)
          ..strokeCap = StrokeCap.round
          ..style = PaintingStyle.stroke
          ..strokeWidth = 1.0;
    Path path = Path();

    // ******************
    // todo: draw truss
    // ******************
    startP1 = Offset(0.5 * (size.width), 0.85 * (size.height));

    startP2 = startP1 + Offset(-0.5 * (inputGlobal.span) * ratio, 0);
    endP2 = startP2 + Offset(1 * (inputGlobal.span) * ratio, 0);
    canvas.drawLine(startP2, endP2, beamPaint);

    startP3 = startP2 + Offset(0, -inputGlobal.strZone / 1000 * ratio);
    endP3 = startP3 + Offset(1 * (inputGlobal.span) * ratio, 0);
    canvas.drawLine(startP3, endP3, beamPaint);

    //*draw vertical member at end
    startP4 = startP2;
    endP4 = startP3;
    canvas.drawLine(startP4, endP4, beamPaint);

    startP4 = endP2;
    endP4 = endP3;
    canvas.drawLine(startP4, endP4, beamPaint);
    //* draw diagonal members
    final n = (inputGlobal.span / inputGlobal.unbracedLength).ceil();
    final div = inputGlobal.span / n / 2 * ratio;
    final h = inputGlobal.strZone / 1000 * ratio;

    startP4 = startP2;
    final points = Float32List.fromList(
      _getPointsFromOffset(startP4, [
        ...List.generate(2 * n + 1, (index) {
          if (index == 0) {
            return Offset(0, 0);
          }
          if (index % 2 == 0) {
            return Offset(div, h);
          } else {
            return Offset(div, -h);
          }
        }),
      ]),
    );
    canvas.drawRawPoints(PointMode.polygon, points, beamPaint);

    // ******************
    // todo: draw support
    // ******************
    // final supportLeft = Float32List.fromList(
    //   [
    //     startP2,
    //     startP2 + Offset(10, 10),
    //     startP2 + Offset(-10, 10),
    //     startP2,
    //   ].expand((i) => [i.dx, i.dy]).toList(),
    // );
    final supportRight = Float32List.fromList(
      [
        endP2,
        endP2 + Offset(5, -5),
        endP2,
        endP2 + Offset(0, -10),
        endP2 + Offset(0 + 5, -10 - 5),
        endP2 + Offset(0, -10),
        endP2 + Offset(0, -20),
        endP2 + Offset(0 + 5, -20 - 5),
        endP2 + Offset(0, -20),
        endP2 + Offset(0, 10),
        endP2 + Offset(0 + 5, 10 - 5),
        endP2 + Offset(0, 10),
      ].expand((i) => [i.dx, i.dy]).toList(),
    );

    // canvas.drawRawPoints(PointMode.polygon, supportLeft, supportPaint);
    canvas.drawRawPoints(PointMode.polygon, supportRight, supportPaint);
    _drawDimLine(
      canvas,
      fontSize!,
      startP3,
      endP3,
      size.height / 15,
      fontSize! / 2,
      'Span',
      context,
      fontColor: Colors.black.withAlpha(100),
    );
    offsetCounter += 5;
    // ******************
    // todo: draw loading
    // ******************
    final maxLoad = maxFromNum(input.map((e) => e.pointLoad).toList());

    for (var p in input) {
      // startP3 = endP3 + Offset(0, -p.pointLoad/maxLoad * size.height*4/5/2 );
      // canvas.drawLine(startP3, endP3, loadPaint);
      endP3 = startP3 + Offset(p.distA * ratio, 0);
      final load = Float32List.fromList(
        [
          endP3,
          endP3 +
              Offset(
                0,
                math.min(
                  -p.pointLoad / maxLoad * size.height * 4 / 5 / 2,
                  -size.height / 10,
                ),
              ),
          endP3,
          endP3 +
              Offset(
                -5,
                math.min(
                  -p.pointLoad / maxLoad * size.height * 4 / 5 / 2 / 2,
                  -size.height / 20,
                ),
              ),
          endP3,
          endP3 +
              Offset(
                5,
                math.min(
                  -p.pointLoad / maxLoad * size.height * 4 / 5 / 2 / 2,
                  -size.height / 20,
                ),
              ),
        ].expand((i) => [i.dx, i.dy]).toList(),
      );

      // draw loading itself (arrow)
      if (p.pointLoad != 0) {
        canvas.drawRawPoints(PointMode.polygon, load, loadPaint);
        // draw distance A
        _drawDimLine(
          canvas,
          fontSize!,
          startP3,
          endP3,
          offsetCounter,
          fontSize! / 4,
          'Distance a',
          context,
          fontColor: Colors.red.withAlpha(200),
        );
        // draw loading magnitude
        _drawText(
          canvas,
          fontSize!,
          endP3 + Offset(0, -p.pointLoad / maxLoad * size.height * 4 / 5 / 2),
          endP3,
          -10,
          fontSize! / 4,
          'Point Load',
          context,
          fontColor: Colors.red.withAlpha(200),
        );
        offsetCounter += 10;
      }
    }
     //* Draw UDL
    // quick refactor: use mixin here to bring in the functionality of Summary
    // final calsLogMap = extractCalsLog(scheme.calsLog);
    // final udl = getDoubleValue(calsLogMap, 'UDL');


    offsetCounter += 10;
    startP3 = startP3 + Offset(0, -offsetCounter);
    endP3 = startP3 + Offset(1 * (inputGlobal.span) * ratio, 0);
    int segmentCounts = 10;
    double segmentLength = (endP3.dx - startP3.dx) / segmentCounts;
    Offset centerP;
    for (int i = 0; i < segmentCounts; i++) {
      startP4 = Offset(startP3.dx + segmentLength * i, startP3.dy);
      endP4 = startP4 + Offset(segmentLength, 0);
      centerP = (startP4 + endP4) / 2;
      Rect rec1 = Rect.fromCenter(
        center: centerP,
        width: segmentLength,
        height: segmentLength,
      );
      canvas.drawArc(rec1, math.pi, math.pi, true, UDLPaint);
    }
    //

    // draw UDL text
    _drawText(
      canvas,
      fontSize!,
      (startP3+endP3)/2 + Offset(0, -25),
      (startP3+endP3)/2 + Offset(10, -25),
      -10,
      fontSize! / 4,
      'UDL',
      context,
      fontColor: Colors.black.withAlpha(100),
    );
  }

  void _drawDimLine(
    Canvas canvas,
    double fontSize,
    Offset start,
    Offset end,
    double offsetDistance,
    double dimExtension,
    String label,
    BuildContext context, {
    Color? fontColor,
  }) {
    double tempOffset = 10; // forthe dim line end
    late Offset tempEnd;
    late Offset tempStart;
    late Offset textCenter;

    late double angle;

    late List<String> unit;

    // fontColor ??= Colors.red.withAlpha(200);
    fontColor ??= Theme.of(context).colorScheme.primary;

    late final vec.Vector2 u;
    late final vec.Vector2 uUnit;
    late final vec.Vector2 vUnit;
    late final vec.Vector2 vUnit45;
    late final vec.Matrix2 m;

    final ColorScheme colorScheme = Theme.of(context).colorScheme;
    final TextTheme textTheme = Theme.of(context).textTheme;

    // get the unit
    switch (globalData.unit) {
      case 'metrics':
        unit = PreferredUnit.metrics;
        break;
      case 'imperial':
        unit = PreferredUnit.imperial;
        break;
      default:
        unit = PreferredUnit.metrics;
    }

    // double dimExtension = offsetDistance.abs() / 2;

    final Paint paint =
        Paint()
          ..color = fontColor
          ..strokeWidth = 1.0
          ..style = PaintingStyle.stroke
          ..strokeJoin = StrokeJoin.round;

    // line vector
    u = vec.Vector2(end.dx - start.dx, end.dy - start.dy);
    uUnit = u / u.length;

    // orthogonal vector
    vUnit = vec.Vector2(uUnit.y, uUnit.x); //rotate aniti-clockwise

    // Vector of parallel line with Offset
    Offset newStart = Offset(
      start.dx - vUnit.x * offsetDistance - uUnit.x * dimExtension,
      start.dy - vUnit.y * offsetDistance - uUnit.y * dimExtension,
    );
    Offset newEnd = Offset(
      end.dx - vUnit.x * offsetDistance + uUnit.x * dimExtension,
      end.dy - vUnit.y * offsetDistance + uUnit.y * dimExtension,
    );
    canvas.drawLine(newStart, newEnd, paint);

    // draw start crossing
    tempStart = Offset(
      (start.dx - vUnit.x * offsetDistance) - vUnit.x * dimExtension,
      (start.dy - vUnit.y * offsetDistance) - vUnit.y * dimExtension,
    );
    tempEnd = Offset(
      (start.dx - vUnit.x * offsetDistance) + vUnit.x * dimExtension,
      (start.dy - vUnit.y * offsetDistance) + vUnit.y * dimExtension,
    );
    canvas.drawLine(tempStart, tempEnd, paint);

    m = vec.Matrix2.rotation(-45 * math.pi / 180);
    vUnit45 = m * vUnit;
    tempStart = Offset(
      (start.dx - vUnit.x * offsetDistance) - vUnit45.x * dimExtension,
      (start.dy - vUnit.y * offsetDistance) - vUnit45.y * dimExtension,
    );
    tempEnd = Offset(
      (start.dx - vUnit.x * offsetDistance) + vUnit45.x * dimExtension,
      (start.dy - vUnit.y * offsetDistance) + vUnit45.y * dimExtension,
    );
    canvas.drawLine(tempStart, tempEnd, paint);

    // draw end crossing
    tempStart = Offset(
      (end.dx - vUnit.x * offsetDistance) - vUnit.x * dimExtension,
      (end.dy - vUnit.y * offsetDistance) - vUnit.y * dimExtension,
    );
    tempEnd = Offset(
      (end.dx - vUnit.x * offsetDistance) + vUnit.x * dimExtension,
      (end.dy - vUnit.y * offsetDistance) + vUnit.y * dimExtension,
    );
    canvas.drawLine(tempStart, tempEnd, paint);

    tempStart = Offset(
      (end.dx - vUnit.x * offsetDistance) - vUnit45.x * dimExtension,
      (end.dy - vUnit.y * offsetDistance) - vUnit45.y * dimExtension,
    );
    tempEnd = Offset(
      (end.dx - vUnit.x * offsetDistance) + vUnit45.x * dimExtension,
      (end.dy - vUnit.y * offsetDistance) + vUnit45.y * dimExtension,
    );
    canvas.drawLine(tempStart, tempEnd, paint);

    // indicate span length and beam size

    TextSpan textSpan = TextSpan(
      text: label,
      style: TextStyle(
        fontSize: fontSize,
        color: fontColor,
        fontWeight: FontWeight.bold,
      ),
    );

    TextPainter textPainter = TextPainter(
      text: textSpan,
      textAlign: TextAlign.center,
      textDirection: TextDirection.ltr,
    )..layout();

    textCenter = Offset(
      (start.dx + end.dx) / 2 -
          vUnit.x * (offsetDistance * 1.0 + 12) -
          uUnit.x * textPainter.width / 2,
      (start.dy + end.dy) / 2 -
          vUnit.y * (offsetDistance * 1.0 + 12) -
          uUnit.y * textPainter.width / 2,
    );
    angle = math.atan2(end.dy - start.dy, end.dx - start.dx) * 180 / math.pi;

    canvas.save();
    canvas.translate(textCenter.dx, textCenter.dy);
    canvas.rotate(angle * math.pi / 180);
    textPainter.paint(canvas, Offset.zero);
    canvas.restore();
  }

  void _drawText(
    Canvas canvas,
    double fontSize,
    Offset start,
    Offset end,
    double offsetDistance,
    double dimExtension,
    String label,
    BuildContext context, {
    Color? fontColor,
  }) {
    double tempOffset = 10; // forthe dim line end
    late Offset tempEnd;
    late Offset tempStart;
    late Offset textCenter;

    late double angle;

    late List<String> unit;

    // fontColor ??= Colors.red;
    fontColor ??= Theme.of(context).colorScheme.primary;

    late final vec.Vector2 u;
    late final vec.Vector2 uUnit;
    late final vec.Vector2 vUnit;
    late final vec.Vector2 vUnit45;
    late final vec.Matrix2 m;

    final ColorScheme colorScheme = Theme.of(context).colorScheme;
    final TextTheme textTheme = Theme.of(context).textTheme;

    // get the unit
    switch (globalData.unit) {
      case 'metrics':
        unit = PreferredUnit.metrics;
        break;
      case 'imperial':
        unit = PreferredUnit.imperial;
        break;
      default:
        unit = PreferredUnit.metrics;
    }

    // double dimExtension = offsetDistance.abs() / 2;

    final Paint paint =
        Paint()
          ..color = fontColor
          ..strokeWidth = 1.0
          ..style = PaintingStyle.stroke
          ..strokeJoin = StrokeJoin.round;

    // line vector
    u = vec.Vector2(end.dx - start.dx, end.dy - start.dy);
    uUnit = u / u.length;

    // orthogonal vector
    vUnit = vec.Vector2(uUnit.y, uUnit.x); //rotate aniti-clockwise

    // Vector of parallel line with Offset
    Offset newStart = Offset(
      start.dx - vUnit.x * offsetDistance - uUnit.x * dimExtension,
      start.dy - vUnit.y * offsetDistance - uUnit.y * dimExtension,
    );
    Offset newEnd = Offset(
      end.dx - vUnit.x * offsetDistance + uUnit.x * dimExtension,
      end.dy - vUnit.y * offsetDistance + uUnit.y * dimExtension,
    );

    // indicate span length and beam size

    TextSpan textSpan = TextSpan(
      text: label,
      style: TextStyle(
        fontSize: fontSize,
        color: fontColor,
        fontWeight: FontWeight.bold,
      ),
    );

    TextPainter textPainter = TextPainter(
      text: textSpan,
      textAlign: TextAlign.center,
      textDirection: TextDirection.ltr,
    )..layout();

    textCenter = Offset(
      (start.dx + end.dx) / 2 -
          vUnit.x * (offsetDistance * 1.0 + 12) -
          uUnit.x * textPainter.width / 2,
      (start.dy + end.dy) / 2 -
          vUnit.y * (offsetDistance * 1.0 + 12) -
          uUnit.y * textPainter.width / 2,
    );
    angle = math.atan2(end.dy - start.dy, end.dx - start.dx) * 180 / math.pi;

    canvas.save();
    canvas.translate(textCenter.dx, textCenter.dy);
    canvas.rotate(angle * math.pi / 180);
    textPainter.paint(canvas, Offset.zero);
    canvas.restore();
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    // TODO: implement shouldRepaint
    return false;
  }

  Float32List _getPointsFromOffset(Offset start, List<Offset> offsets) {
    List<Offset> points = [start];
    for (Offset offset in offsets) {
      start = start + offset;
      points.add(start);
    }
    return Float32List.fromList(points.expand((i) => [i.dx, i.dy]).toList());
  }
}
