import 'package:freezed_annotation/freezed_annotation.dart';
// import 'package:flutter/foundation.dart';
part 'column_scheme_input_global.freezed.dart';
part 'column_scheme_input_global.g.dart';

@freezed
abstract class ColumnSchemeInputGlobal with _$ColumnSchemeInputGlobal{
  const ColumnSchemeInputGlobal._();
  factory ColumnSchemeInputGlobal({
    @Default(1) int columnSchemeInputGlobalId,
    @Default(30) double cover,
    @Default(300) double minColumnSize,
    @Default(100) double sizeIncrement,
    @Default(10) int iterationSteps,
    @Default('C45') String concreteGrade,
    @Default(0.02) double minSteelRatio,
    @Default(0.04) double maxSteelRatio,
    @Default(1.0) double safetyFactor,
    @Default(60) double minClearS,
    @Default(false) bool useSlabSelected,

  }) = _ColumnSchemeInputGlobal;

  factory ColumnSchemeInputGlobal.fromJson(Map<String, Object?> json) =>
      _$ColumnSchemeInputGlobalFromJson(json);
}

