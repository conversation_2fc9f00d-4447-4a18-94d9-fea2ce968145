// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'column_scheme_input_global.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_ColumnSchemeInputGlobal _$ColumnSchemeInputGlobalFromJson(
  Map<String, dynamic> json,
) => _ColumnSchemeInputGlobal(
  columnSchemeInputGlobalId:
      (json['columnSchemeInputGlobalId'] as num?)?.toInt() ?? 1,
  cover: (json['cover'] as num?)?.toDouble() ?? 30,
  minColumnSize: (json['minColumnSize'] as num?)?.toDouble() ?? 300,
  sizeIncrement: (json['sizeIncrement'] as num?)?.toDouble() ?? 100,
  iterationSteps: (json['iterationSteps'] as num?)?.toInt() ?? 10,
  concreteGrade: json['concreteGrade'] as String? ?? 'C45',
  minSteelRatio: (json['minSteelRatio'] as num?)?.toDouble() ?? 0.02,
  maxSteelRatio: (json['maxSteelRatio'] as num?)?.toDouble() ?? 0.04,
  safetyFactor: (json['safetyFactor'] as num?)?.toDouble() ?? 1.0,
  minClearS: (json['minClearS'] as num?)?.toDouble() ?? 60,
  useSlabSelected: json['useSlabSelected'] as bool? ?? false,
);

Map<String, dynamic> _$ColumnSchemeInputGlobalToJson(
  _ColumnSchemeInputGlobal instance,
) => <String, dynamic>{
  'columnSchemeInputGlobalId': instance.columnSchemeInputGlobalId,
  'cover': instance.cover,
  'minColumnSize': instance.minColumnSize,
  'sizeIncrement': instance.sizeIncrement,
  'iterationSteps': instance.iterationSteps,
  'concreteGrade': instance.concreteGrade,
  'minSteelRatio': instance.minSteelRatio,
  'maxSteelRatio': instance.maxSteelRatio,
  'safetyFactor': instance.safetyFactor,
  'minClearS': instance.minClearS,
  'useSlabSelected': instance.useSlabSelected,
};
