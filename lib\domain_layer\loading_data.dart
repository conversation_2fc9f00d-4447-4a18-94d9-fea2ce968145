import 'package:freezed_annotation/freezed_annotation.dart';
part 'loading_data.freezed.dart';
part 'loading_data.g.dart';

@freezed
abstract class LoadingData with _$LoadingData {
  const LoadingData._();
  factory LoadingData({
    @Default(0.0) double sdl,
    @Default(0.0) double ll,
    @Default(0.0) double slsLoad,
    @Default(0.0) double ulsLoad
  }) = _LoadingData; 

  factory LoadingData.fromJson(Map<String, Object?> json) =>
      _$LoadingDataFromJson(json);
}
