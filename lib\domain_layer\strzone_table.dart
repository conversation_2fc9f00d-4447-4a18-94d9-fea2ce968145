import 'package:freezed_annotation/freezed_annotation.dart';
// import 'package:flutter/foundation.dart';
part 'strzone_table.freezed.dart';
part 'strzone_table.g.dart';

@freezed
abstract class StrZoneTable with _$StrZoneTable{
  const StrZoneTable._();
  factory StrZoneTable({
    @Default('Floor') String floor,
    @Default(3500.0) double height,
    @Default(50) double finish,
    @Default(300) double service,
    @Default(2300) double clear,
    @Default(1) int nosOfFloor,
    @Default('') String strZoneId, //will be overriden  as soon as new instance created
  }) = _StrZoneTable;

  factory StrZoneTable.fromJson(Map<String, Object?> json) =>
      _$StrZoneTableFromJson(json);
}
