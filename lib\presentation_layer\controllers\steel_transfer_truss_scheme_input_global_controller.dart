import 'package:riverpod_annotation/riverpod_annotation.dart';

//presentation layer
import '../../domain_layer/steel_transfer_truss_scheme_input_global.dart';
import '../screen/homescreen.dart';

// domain layer

part 'steel_transfer_truss_scheme_input_global_controller.g.dart';

@riverpod
class SteelTransferTrussSchemeInputGlobalController
    extends _$SteelTransferTrussSchemeInputGlobalController {
  @override
  FutureOr<SteelTransferTrussSchemeInputGlobal> build() async {
    // print('Build: Column Scheme Input Global');
    SteelTransferTrussSchemeInputGlobal steelTransferTrussSchemeGlobalInput =
        await ref
            .read(appDatabaseControllerProvider.notifier)
            . querySteelTransferTrussSchemeInputGlobal();
    final data = ref.watch(loadingTablesControllerProvider);
    return data.when(
      data: (loadingTables) async {
        final usages = loadingTables.map((x) => x.usage).toList();
        if (steelTransferTrussSchemeGlobalInput.usage == '' ||
            !usages.contains(steelTransferTrussSchemeGlobalInput.usage)) {
          steelTransferTrussSchemeGlobalInput =
              steelTransferTrussSchemeGlobalInput.copyWith(usage: usages.first);
        }
        return steelTransferTrussSchemeGlobalInput;
      },
      error: (error, stackTrace) => SteelTransferTrussSchemeInputGlobal(),
      loading: () => SteelTransferTrussSchemeInputGlobal(),
    );
  }

  Future<void> updateTable({
    String? id,
    double? span,
    double? loadWidth,
    double? strZone,
    double? fsy,
    double? unbracedLength,
    String? usage,
    double? slabThickness,
  }) async {
    final x = await future;
    SteelTransferTrussSchemeInputGlobal newState = x.copyWith(
      id: id ?? x.id,
      span: span ?? x.span,
      loadWidth: loadWidth ?? x.loadWidth,
      strZone: strZone ?? x.strZone,
      fsy: fsy ?? x.fsy,
      unbracedLength: unbracedLength ?? x.unbracedLength,
      usage: usage ?? x.usage,
      slabThickness: slabThickness ?? x.slabThickness,
    );
    // Update the state only if there are changes
    state = AsyncData(newState);
  }
}
