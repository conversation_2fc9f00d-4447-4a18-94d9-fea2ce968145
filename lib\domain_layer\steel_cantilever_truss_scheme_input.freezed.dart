// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'steel_cantilever_truss_scheme_input.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$SteelCantileverTrussSchemeInput {

 double get pointLoad; double get distA; String get steelCantileverTrussSchemeInputId;
/// Create a copy of SteelCantileverTrussSchemeInput
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$SteelCantileverTrussSchemeInputCopyWith<SteelCantileverTrussSchemeInput> get copyWith => _$SteelCantileverTrussSchemeInputCopyWithImpl<SteelCantileverTrussSchemeInput>(this as SteelCantileverTrussSchemeInput, _$identity);

  /// Serializes this SteelCantileverTrussSchemeInput to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is SteelCantileverTrussSchemeInput&&(identical(other.pointLoad, pointLoad) || other.pointLoad == pointLoad)&&(identical(other.distA, distA) || other.distA == distA)&&(identical(other.steelCantileverTrussSchemeInputId, steelCantileverTrussSchemeInputId) || other.steelCantileverTrussSchemeInputId == steelCantileverTrussSchemeInputId));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,pointLoad,distA,steelCantileverTrussSchemeInputId);

@override
String toString() {
  return 'SteelCantileverTrussSchemeInput(pointLoad: $pointLoad, distA: $distA, steelCantileverTrussSchemeInputId: $steelCantileverTrussSchemeInputId)';
}


}

/// @nodoc
abstract mixin class $SteelCantileverTrussSchemeInputCopyWith<$Res>  {
  factory $SteelCantileverTrussSchemeInputCopyWith(SteelCantileverTrussSchemeInput value, $Res Function(SteelCantileverTrussSchemeInput) _then) = _$SteelCantileverTrussSchemeInputCopyWithImpl;
@useResult
$Res call({
 double pointLoad, double distA, String steelCantileverTrussSchemeInputId
});




}
/// @nodoc
class _$SteelCantileverTrussSchemeInputCopyWithImpl<$Res>
    implements $SteelCantileverTrussSchemeInputCopyWith<$Res> {
  _$SteelCantileverTrussSchemeInputCopyWithImpl(this._self, this._then);

  final SteelCantileverTrussSchemeInput _self;
  final $Res Function(SteelCantileverTrussSchemeInput) _then;

/// Create a copy of SteelCantileverTrussSchemeInput
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? pointLoad = null,Object? distA = null,Object? steelCantileverTrussSchemeInputId = null,}) {
  return _then(_self.copyWith(
pointLoad: null == pointLoad ? _self.pointLoad : pointLoad // ignore: cast_nullable_to_non_nullable
as double,distA: null == distA ? _self.distA : distA // ignore: cast_nullable_to_non_nullable
as double,steelCantileverTrussSchemeInputId: null == steelCantileverTrussSchemeInputId ? _self.steelCantileverTrussSchemeInputId : steelCantileverTrussSchemeInputId // ignore: cast_nullable_to_non_nullable
as String,
  ));
}

}


/// Adds pattern-matching-related methods to [SteelCantileverTrussSchemeInput].
extension SteelCantileverTrussSchemeInputPatterns on SteelCantileverTrussSchemeInput {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _SteelCantileverTrussSchemeInput value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _SteelCantileverTrussSchemeInput() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _SteelCantileverTrussSchemeInput value)  $default,){
final _that = this;
switch (_that) {
case _SteelCantileverTrussSchemeInput():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _SteelCantileverTrussSchemeInput value)?  $default,){
final _that = this;
switch (_that) {
case _SteelCantileverTrussSchemeInput() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( double pointLoad,  double distA,  String steelCantileverTrussSchemeInputId)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _SteelCantileverTrussSchemeInput() when $default != null:
return $default(_that.pointLoad,_that.distA,_that.steelCantileverTrussSchemeInputId);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( double pointLoad,  double distA,  String steelCantileverTrussSchemeInputId)  $default,) {final _that = this;
switch (_that) {
case _SteelCantileverTrussSchemeInput():
return $default(_that.pointLoad,_that.distA,_that.steelCantileverTrussSchemeInputId);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( double pointLoad,  double distA,  String steelCantileverTrussSchemeInputId)?  $default,) {final _that = this;
switch (_that) {
case _SteelCantileverTrussSchemeInput() when $default != null:
return $default(_that.pointLoad,_that.distA,_that.steelCantileverTrussSchemeInputId);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _SteelCantileverTrussSchemeInput extends SteelCantileverTrussSchemeInput {
   _SteelCantileverTrussSchemeInput({this.pointLoad = 0.0, this.distA = 3.0, this.steelCantileverTrussSchemeInputId = ''}): super._();
  factory _SteelCantileverTrussSchemeInput.fromJson(Map<String, dynamic> json) => _$SteelCantileverTrussSchemeInputFromJson(json);

@override@JsonKey() final  double pointLoad;
@override@JsonKey() final  double distA;
@override@JsonKey() final  String steelCantileverTrussSchemeInputId;

/// Create a copy of SteelCantileverTrussSchemeInput
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$SteelCantileverTrussSchemeInputCopyWith<_SteelCantileverTrussSchemeInput> get copyWith => __$SteelCantileverTrussSchemeInputCopyWithImpl<_SteelCantileverTrussSchemeInput>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$SteelCantileverTrussSchemeInputToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _SteelCantileverTrussSchemeInput&&(identical(other.pointLoad, pointLoad) || other.pointLoad == pointLoad)&&(identical(other.distA, distA) || other.distA == distA)&&(identical(other.steelCantileverTrussSchemeInputId, steelCantileverTrussSchemeInputId) || other.steelCantileverTrussSchemeInputId == steelCantileverTrussSchemeInputId));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,pointLoad,distA,steelCantileverTrussSchemeInputId);

@override
String toString() {
  return 'SteelCantileverTrussSchemeInput(pointLoad: $pointLoad, distA: $distA, steelCantileverTrussSchemeInputId: $steelCantileverTrussSchemeInputId)';
}


}

/// @nodoc
abstract mixin class _$SteelCantileverTrussSchemeInputCopyWith<$Res> implements $SteelCantileverTrussSchemeInputCopyWith<$Res> {
  factory _$SteelCantileverTrussSchemeInputCopyWith(_SteelCantileverTrussSchemeInput value, $Res Function(_SteelCantileverTrussSchemeInput) _then) = __$SteelCantileverTrussSchemeInputCopyWithImpl;
@override @useResult
$Res call({
 double pointLoad, double distA, String steelCantileverTrussSchemeInputId
});




}
/// @nodoc
class __$SteelCantileverTrussSchemeInputCopyWithImpl<$Res>
    implements _$SteelCantileverTrussSchemeInputCopyWith<$Res> {
  __$SteelCantileverTrussSchemeInputCopyWithImpl(this._self, this._then);

  final _SteelCantileverTrussSchemeInput _self;
  final $Res Function(_SteelCantileverTrussSchemeInput) _then;

/// Create a copy of SteelCantileverTrussSchemeInput
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? pointLoad = null,Object? distA = null,Object? steelCantileverTrussSchemeInputId = null,}) {
  return _then(_SteelCantileverTrussSchemeInput(
pointLoad: null == pointLoad ? _self.pointLoad : pointLoad // ignore: cast_nullable_to_non_nullable
as double,distA: null == distA ? _self.distA : distA // ignore: cast_nullable_to_non_nullable
as double,steelCantileverTrussSchemeInputId: null == steelCantileverTrussSchemeInputId ? _self.steelCantileverTrussSchemeInputId : steelCantileverTrussSchemeInputId // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

// dart format on
