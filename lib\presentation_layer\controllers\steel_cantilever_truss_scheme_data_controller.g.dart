// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'steel_cantilever_truss_scheme_data_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$steelCantileverTrussSchemeDataControllerHash() =>
    r'adbfce047489c86618c3ee4c222cb05e4027374d';

/// See also [SteelCantileverTrussSchemeDataController].
@ProviderFor(SteelCantileverTrussSchemeDataController)
final steelCantileverTrussSchemeDataControllerProvider =
    AutoDisposeAsyncNotifierProvider<
      SteelCantileverTrussSchemeDataController,
      SteelCantileverTrussSchemeData
    >.internal(
      SteelCantileverTrussSchemeDataController.new,
      name: r'steelCantileverTrussSchemeDataControllerProvider',
      debugGetCreateSourceHash:
          const bool.fromEnvironment('dart.vm.product')
              ? null
              : _$steelCantileverTrussSchemeDataControllerHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$SteelCantileverTrussSchemeDataController =
    AutoDisposeAsyncNotifier<SteelCantileverTrussSchemeData>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
