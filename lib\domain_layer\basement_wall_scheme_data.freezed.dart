// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'basement_wall_scheme_data.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$BasementWallSchemeData {

 double get strZone; double get fcu; double get cover; String get mainTopBar; String get mainBottomBar; String get mainLinks; double get wallTopLevel; double get wallBottomLevel; double get soilTopLevel; double get waterTopLevel; String get basementWallSchemeId; String get calsLog; String get wallForceULS;// format is slab force / beam force 
 String get wallForceSLS;
/// Create a copy of BasementWallSchemeData
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$BasementWallSchemeDataCopyWith<BasementWallSchemeData> get copyWith => _$BasementWallSchemeDataCopyWithImpl<BasementWallSchemeData>(this as BasementWallSchemeData, _$identity);

  /// Serializes this BasementWallSchemeData to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is BasementWallSchemeData&&(identical(other.strZone, strZone) || other.strZone == strZone)&&(identical(other.fcu, fcu) || other.fcu == fcu)&&(identical(other.cover, cover) || other.cover == cover)&&(identical(other.mainTopBar, mainTopBar) || other.mainTopBar == mainTopBar)&&(identical(other.mainBottomBar, mainBottomBar) || other.mainBottomBar == mainBottomBar)&&(identical(other.mainLinks, mainLinks) || other.mainLinks == mainLinks)&&(identical(other.wallTopLevel, wallTopLevel) || other.wallTopLevel == wallTopLevel)&&(identical(other.wallBottomLevel, wallBottomLevel) || other.wallBottomLevel == wallBottomLevel)&&(identical(other.soilTopLevel, soilTopLevel) || other.soilTopLevel == soilTopLevel)&&(identical(other.waterTopLevel, waterTopLevel) || other.waterTopLevel == waterTopLevel)&&(identical(other.basementWallSchemeId, basementWallSchemeId) || other.basementWallSchemeId == basementWallSchemeId)&&(identical(other.calsLog, calsLog) || other.calsLog == calsLog)&&(identical(other.wallForceULS, wallForceULS) || other.wallForceULS == wallForceULS)&&(identical(other.wallForceSLS, wallForceSLS) || other.wallForceSLS == wallForceSLS));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,strZone,fcu,cover,mainTopBar,mainBottomBar,mainLinks,wallTopLevel,wallBottomLevel,soilTopLevel,waterTopLevel,basementWallSchemeId,calsLog,wallForceULS,wallForceSLS);

@override
String toString() {
  return 'BasementWallSchemeData(strZone: $strZone, fcu: $fcu, cover: $cover, mainTopBar: $mainTopBar, mainBottomBar: $mainBottomBar, mainLinks: $mainLinks, wallTopLevel: $wallTopLevel, wallBottomLevel: $wallBottomLevel, soilTopLevel: $soilTopLevel, waterTopLevel: $waterTopLevel, basementWallSchemeId: $basementWallSchemeId, calsLog: $calsLog, wallForceULS: $wallForceULS, wallForceSLS: $wallForceSLS)';
}


}

/// @nodoc
abstract mixin class $BasementWallSchemeDataCopyWith<$Res>  {
  factory $BasementWallSchemeDataCopyWith(BasementWallSchemeData value, $Res Function(BasementWallSchemeData) _then) = _$BasementWallSchemeDataCopyWithImpl;
@useResult
$Res call({
 double strZone, double fcu, double cover, String mainTopBar, String mainBottomBar, String mainLinks, double wallTopLevel, double wallBottomLevel, double soilTopLevel, double waterTopLevel, String basementWallSchemeId, String calsLog, String wallForceULS, String wallForceSLS
});




}
/// @nodoc
class _$BasementWallSchemeDataCopyWithImpl<$Res>
    implements $BasementWallSchemeDataCopyWith<$Res> {
  _$BasementWallSchemeDataCopyWithImpl(this._self, this._then);

  final BasementWallSchemeData _self;
  final $Res Function(BasementWallSchemeData) _then;

/// Create a copy of BasementWallSchemeData
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? strZone = null,Object? fcu = null,Object? cover = null,Object? mainTopBar = null,Object? mainBottomBar = null,Object? mainLinks = null,Object? wallTopLevel = null,Object? wallBottomLevel = null,Object? soilTopLevel = null,Object? waterTopLevel = null,Object? basementWallSchemeId = null,Object? calsLog = null,Object? wallForceULS = null,Object? wallForceSLS = null,}) {
  return _then(_self.copyWith(
strZone: null == strZone ? _self.strZone : strZone // ignore: cast_nullable_to_non_nullable
as double,fcu: null == fcu ? _self.fcu : fcu // ignore: cast_nullable_to_non_nullable
as double,cover: null == cover ? _self.cover : cover // ignore: cast_nullable_to_non_nullable
as double,mainTopBar: null == mainTopBar ? _self.mainTopBar : mainTopBar // ignore: cast_nullable_to_non_nullable
as String,mainBottomBar: null == mainBottomBar ? _self.mainBottomBar : mainBottomBar // ignore: cast_nullable_to_non_nullable
as String,mainLinks: null == mainLinks ? _self.mainLinks : mainLinks // ignore: cast_nullable_to_non_nullable
as String,wallTopLevel: null == wallTopLevel ? _self.wallTopLevel : wallTopLevel // ignore: cast_nullable_to_non_nullable
as double,wallBottomLevel: null == wallBottomLevel ? _self.wallBottomLevel : wallBottomLevel // ignore: cast_nullable_to_non_nullable
as double,soilTopLevel: null == soilTopLevel ? _self.soilTopLevel : soilTopLevel // ignore: cast_nullable_to_non_nullable
as double,waterTopLevel: null == waterTopLevel ? _self.waterTopLevel : waterTopLevel // ignore: cast_nullable_to_non_nullable
as double,basementWallSchemeId: null == basementWallSchemeId ? _self.basementWallSchemeId : basementWallSchemeId // ignore: cast_nullable_to_non_nullable
as String,calsLog: null == calsLog ? _self.calsLog : calsLog // ignore: cast_nullable_to_non_nullable
as String,wallForceULS: null == wallForceULS ? _self.wallForceULS : wallForceULS // ignore: cast_nullable_to_non_nullable
as String,wallForceSLS: null == wallForceSLS ? _self.wallForceSLS : wallForceSLS // ignore: cast_nullable_to_non_nullable
as String,
  ));
}

}


/// Adds pattern-matching-related methods to [BasementWallSchemeData].
extension BasementWallSchemeDataPatterns on BasementWallSchemeData {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _BasementWallSchemeData value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _BasementWallSchemeData() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _BasementWallSchemeData value)  $default,){
final _that = this;
switch (_that) {
case _BasementWallSchemeData():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _BasementWallSchemeData value)?  $default,){
final _that = this;
switch (_that) {
case _BasementWallSchemeData() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( double strZone,  double fcu,  double cover,  String mainTopBar,  String mainBottomBar,  String mainLinks,  double wallTopLevel,  double wallBottomLevel,  double soilTopLevel,  double waterTopLevel,  String basementWallSchemeId,  String calsLog,  String wallForceULS,  String wallForceSLS)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _BasementWallSchemeData() when $default != null:
return $default(_that.strZone,_that.fcu,_that.cover,_that.mainTopBar,_that.mainBottomBar,_that.mainLinks,_that.wallTopLevel,_that.wallBottomLevel,_that.soilTopLevel,_that.waterTopLevel,_that.basementWallSchemeId,_that.calsLog,_that.wallForceULS,_that.wallForceSLS);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( double strZone,  double fcu,  double cover,  String mainTopBar,  String mainBottomBar,  String mainLinks,  double wallTopLevel,  double wallBottomLevel,  double soilTopLevel,  double waterTopLevel,  String basementWallSchemeId,  String calsLog,  String wallForceULS,  String wallForceSLS)  $default,) {final _that = this;
switch (_that) {
case _BasementWallSchemeData():
return $default(_that.strZone,_that.fcu,_that.cover,_that.mainTopBar,_that.mainBottomBar,_that.mainLinks,_that.wallTopLevel,_that.wallBottomLevel,_that.soilTopLevel,_that.waterTopLevel,_that.basementWallSchemeId,_that.calsLog,_that.wallForceULS,_that.wallForceSLS);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( double strZone,  double fcu,  double cover,  String mainTopBar,  String mainBottomBar,  String mainLinks,  double wallTopLevel,  double wallBottomLevel,  double soilTopLevel,  double waterTopLevel,  String basementWallSchemeId,  String calsLog,  String wallForceULS,  String wallForceSLS)?  $default,) {final _that = this;
switch (_that) {
case _BasementWallSchemeData() when $default != null:
return $default(_that.strZone,_that.fcu,_that.cover,_that.mainTopBar,_that.mainBottomBar,_that.mainLinks,_that.wallTopLevel,_that.wallBottomLevel,_that.soilTopLevel,_that.waterTopLevel,_that.basementWallSchemeId,_that.calsLog,_that.wallForceULS,_that.wallForceSLS);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _BasementWallSchemeData extends BasementWallSchemeData {
   _BasementWallSchemeData({this.strZone = 0.0, this.fcu = 45.0, this.cover = 75.0, this.mainTopBar = '', this.mainBottomBar = '', this.mainLinks = '', this.wallTopLevel = 0.0, this.wallBottomLevel = -3.0, this.soilTopLevel = 0.0, this.waterTopLevel = 0.0, this.basementWallSchemeId = '1', this.calsLog = '', this.wallForceULS = '', this.wallForceSLS = ''}): super._();
  factory _BasementWallSchemeData.fromJson(Map<String, dynamic> json) => _$BasementWallSchemeDataFromJson(json);

@override@JsonKey() final  double strZone;
@override@JsonKey() final  double fcu;
@override@JsonKey() final  double cover;
@override@JsonKey() final  String mainTopBar;
@override@JsonKey() final  String mainBottomBar;
@override@JsonKey() final  String mainLinks;
@override@JsonKey() final  double wallTopLevel;
@override@JsonKey() final  double wallBottomLevel;
@override@JsonKey() final  double soilTopLevel;
@override@JsonKey() final  double waterTopLevel;
@override@JsonKey() final  String basementWallSchemeId;
@override@JsonKey() final  String calsLog;
@override@JsonKey() final  String wallForceULS;
// format is slab force / beam force 
@override@JsonKey() final  String wallForceSLS;

/// Create a copy of BasementWallSchemeData
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$BasementWallSchemeDataCopyWith<_BasementWallSchemeData> get copyWith => __$BasementWallSchemeDataCopyWithImpl<_BasementWallSchemeData>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$BasementWallSchemeDataToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _BasementWallSchemeData&&(identical(other.strZone, strZone) || other.strZone == strZone)&&(identical(other.fcu, fcu) || other.fcu == fcu)&&(identical(other.cover, cover) || other.cover == cover)&&(identical(other.mainTopBar, mainTopBar) || other.mainTopBar == mainTopBar)&&(identical(other.mainBottomBar, mainBottomBar) || other.mainBottomBar == mainBottomBar)&&(identical(other.mainLinks, mainLinks) || other.mainLinks == mainLinks)&&(identical(other.wallTopLevel, wallTopLevel) || other.wallTopLevel == wallTopLevel)&&(identical(other.wallBottomLevel, wallBottomLevel) || other.wallBottomLevel == wallBottomLevel)&&(identical(other.soilTopLevel, soilTopLevel) || other.soilTopLevel == soilTopLevel)&&(identical(other.waterTopLevel, waterTopLevel) || other.waterTopLevel == waterTopLevel)&&(identical(other.basementWallSchemeId, basementWallSchemeId) || other.basementWallSchemeId == basementWallSchemeId)&&(identical(other.calsLog, calsLog) || other.calsLog == calsLog)&&(identical(other.wallForceULS, wallForceULS) || other.wallForceULS == wallForceULS)&&(identical(other.wallForceSLS, wallForceSLS) || other.wallForceSLS == wallForceSLS));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,strZone,fcu,cover,mainTopBar,mainBottomBar,mainLinks,wallTopLevel,wallBottomLevel,soilTopLevel,waterTopLevel,basementWallSchemeId,calsLog,wallForceULS,wallForceSLS);

@override
String toString() {
  return 'BasementWallSchemeData(strZone: $strZone, fcu: $fcu, cover: $cover, mainTopBar: $mainTopBar, mainBottomBar: $mainBottomBar, mainLinks: $mainLinks, wallTopLevel: $wallTopLevel, wallBottomLevel: $wallBottomLevel, soilTopLevel: $soilTopLevel, waterTopLevel: $waterTopLevel, basementWallSchemeId: $basementWallSchemeId, calsLog: $calsLog, wallForceULS: $wallForceULS, wallForceSLS: $wallForceSLS)';
}


}

/// @nodoc
abstract mixin class _$BasementWallSchemeDataCopyWith<$Res> implements $BasementWallSchemeDataCopyWith<$Res> {
  factory _$BasementWallSchemeDataCopyWith(_BasementWallSchemeData value, $Res Function(_BasementWallSchemeData) _then) = __$BasementWallSchemeDataCopyWithImpl;
@override @useResult
$Res call({
 double strZone, double fcu, double cover, String mainTopBar, String mainBottomBar, String mainLinks, double wallTopLevel, double wallBottomLevel, double soilTopLevel, double waterTopLevel, String basementWallSchemeId, String calsLog, String wallForceULS, String wallForceSLS
});




}
/// @nodoc
class __$BasementWallSchemeDataCopyWithImpl<$Res>
    implements _$BasementWallSchemeDataCopyWith<$Res> {
  __$BasementWallSchemeDataCopyWithImpl(this._self, this._then);

  final _BasementWallSchemeData _self;
  final $Res Function(_BasementWallSchemeData) _then;

/// Create a copy of BasementWallSchemeData
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? strZone = null,Object? fcu = null,Object? cover = null,Object? mainTopBar = null,Object? mainBottomBar = null,Object? mainLinks = null,Object? wallTopLevel = null,Object? wallBottomLevel = null,Object? soilTopLevel = null,Object? waterTopLevel = null,Object? basementWallSchemeId = null,Object? calsLog = null,Object? wallForceULS = null,Object? wallForceSLS = null,}) {
  return _then(_BasementWallSchemeData(
strZone: null == strZone ? _self.strZone : strZone // ignore: cast_nullable_to_non_nullable
as double,fcu: null == fcu ? _self.fcu : fcu // ignore: cast_nullable_to_non_nullable
as double,cover: null == cover ? _self.cover : cover // ignore: cast_nullable_to_non_nullable
as double,mainTopBar: null == mainTopBar ? _self.mainTopBar : mainTopBar // ignore: cast_nullable_to_non_nullable
as String,mainBottomBar: null == mainBottomBar ? _self.mainBottomBar : mainBottomBar // ignore: cast_nullable_to_non_nullable
as String,mainLinks: null == mainLinks ? _self.mainLinks : mainLinks // ignore: cast_nullable_to_non_nullable
as String,wallTopLevel: null == wallTopLevel ? _self.wallTopLevel : wallTopLevel // ignore: cast_nullable_to_non_nullable
as double,wallBottomLevel: null == wallBottomLevel ? _self.wallBottomLevel : wallBottomLevel // ignore: cast_nullable_to_non_nullable
as double,soilTopLevel: null == soilTopLevel ? _self.soilTopLevel : soilTopLevel // ignore: cast_nullable_to_non_nullable
as double,waterTopLevel: null == waterTopLevel ? _self.waterTopLevel : waterTopLevel // ignore: cast_nullable_to_non_nullable
as double,basementWallSchemeId: null == basementWallSchemeId ? _self.basementWallSchemeId : basementWallSchemeId // ignore: cast_nullable_to_non_nullable
as String,calsLog: null == calsLog ? _self.calsLog : calsLog // ignore: cast_nullable_to_non_nullable
as String,wallForceULS: null == wallForceULS ? _self.wallForceULS : wallForceULS // ignore: cast_nullable_to_non_nullable
as String,wallForceSLS: null == wallForceSLS ? _self.wallForceSLS : wallForceSLS // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

// dart format on
