// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'pile_end_bearing_bored_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_PileEndBearingBoredData _$PileEndBearingBoredDataFromJson(
  Map<String, dynamic> json,
) => _PileEndBearingBoredData(
  safeBearing: (json['safeBearing'] as num?)?.toDouble() ?? 1000,
  fos: (json['fos'] as num?)?.toDouble() ?? 3,
  fcu: (json['fcu'] as num?)?.toDouble() ?? 45,
  maxPileDiameter: (json['maxPileDiameter'] as num?)?.toDouble() ?? 2000,
  maxSteelRatio: (json['maxSteelRatio'] as num?)?.toDouble() ?? 0.04,
  slsLoad: (json['slsLoad'] as num?)?.toDouble() ?? 1000,
  ulsLoad: (json['ulsLoad'] as num?)?.toDouble() ?? 2000,
  diameter: (json['diameter'] as num?)?.toDouble() ?? 0,
  ratioOfBelloutDia: (json['ratioOfBelloutDia'] as num?)?.toDouble() ?? 1.65,
  length: (json['length'] as num?)?.toDouble() ?? 0,
  baseCapacity: (json['baseCapacity'] as num?)?.toDouble() ?? 0,
  totalGroundResistance:
      (json['totalGroundResistance'] as num?)?.toDouble() ?? 0,
  strCapacity: (json['strCapacity'] as num?)?.toDouble() ?? 0,
  rebar: json['rebar'] as String? ?? '',
  steelRatio: (json['steelRatio'] as num?)?.toDouble() ?? 0,
  isSelected: json['isSelected'] as bool? ?? false,
  calsLog: json['calsLog'] as String? ?? '',
  pileEndBearingBoredSchemeId:
      json['pileEndBearingBoredSchemeId'] as String? ?? '',
);

Map<String, dynamic> _$PileEndBearingBoredDataToJson(
  _PileEndBearingBoredData instance,
) => <String, dynamic>{
  'safeBearing': instance.safeBearing,
  'fos': instance.fos,
  'fcu': instance.fcu,
  'maxPileDiameter': instance.maxPileDiameter,
  'maxSteelRatio': instance.maxSteelRatio,
  'slsLoad': instance.slsLoad,
  'ulsLoad': instance.ulsLoad,
  'diameter': instance.diameter,
  'ratioOfBelloutDia': instance.ratioOfBelloutDia,
  'length': instance.length,
  'baseCapacity': instance.baseCapacity,
  'totalGroundResistance': instance.totalGroundResistance,
  'strCapacity': instance.strCapacity,
  'rebar': instance.rebar,
  'steelRatio': instance.steelRatio,
  'isSelected': instance.isSelected,
  'calsLog': instance.calsLog,
  'pileEndBearingBoredSchemeId': instance.pileEndBearingBoredSchemeId,
};
