// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'column_scheme_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_ColumnSchemeData _$ColumnSchemeDataFromJson(
  Map<String, dynamic> json,
) => _ColumnSchemeData(
  columnSchemeDataId: json['columnSchemeDataId'] as String? ?? '',
  sdl: (json['sdl'] as num?)?.toDouble() ?? 0.0,
  ll: (json['ll'] as num?)?.toDouble() ?? 0.0,
  slsLoad: (json['slsLoad'] as num?)?.toDouble() ?? 0.0,
  ulsLoad: (json['ulsLoad'] as num?)?.toDouble() ?? 0.0,
  size: (json['size'] as num?)?.toDouble() ?? 500.0,
  fcu: (json['fcu'] as num?)?.toDouble() ?? 45.0,
  cover: (json['cover'] as num?)?.toDouble() ?? 40.0,
  axialCapacitySquare: (json['axialCapacitySquare'] as num?)?.toDouble() ?? 0.0,
  mainBarSquare: json['mainBarSquare'] as String? ?? '',
  steelRatioSqaure: (json['steelRatioSqaure'] as num?)?.toDouble() ?? 0.0,
  axialCapacityCircle: (json['axialCapacityCircle'] as num?)?.toDouble() ?? 0.0,
  mainBarCircle: json['mainBarCircle'] as String? ?? '',
  steelRatioCircle: (json['steelRatioCircle'] as num?)?.toDouble() ?? 0.0,
  calsLog: json['calsLog'] as String? ?? '',
  isSelected: json['isSelected'] as bool? ?? false,
);

Map<String, dynamic> _$ColumnSchemeDataToJson(_ColumnSchemeData instance) =>
    <String, dynamic>{
      'columnSchemeDataId': instance.columnSchemeDataId,
      'sdl': instance.sdl,
      'll': instance.ll,
      'slsLoad': instance.slsLoad,
      'ulsLoad': instance.ulsLoad,
      'size': instance.size,
      'fcu': instance.fcu,
      'cover': instance.cover,
      'axialCapacitySquare': instance.axialCapacitySquare,
      'mainBarSquare': instance.mainBarSquare,
      'steelRatioSqaure': instance.steelRatioSqaure,
      'axialCapacityCircle': instance.axialCapacityCircle,
      'mainBarCircle': instance.mainBarCircle,
      'steelRatioCircle': instance.steelRatioCircle,
      'calsLog': instance.calsLog,
      'isSelected': instance.isSelected,
    };
