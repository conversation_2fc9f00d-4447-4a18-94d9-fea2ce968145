// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'footing_scheme_input.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$FootingSchemeInput {

 String get id; double get fcu; double get cover; double get mainKValue; double get mainSteelRatio; int get minS; int get maxS; double get maxDepth; double get minDepth; int get maxLayers; String get groundType; double get soilNValue; double get footingTopLevel; double get waterTableLevel; double get rockCapacity; String get colShape; double get columnSize; double get slsLoad; double get ulsLoad; bool get useSelectColLoad;
/// Create a copy of FootingSchemeInput
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$FootingSchemeInputCopyWith<FootingSchemeInput> get copyWith => _$FootingSchemeInputCopyWithImpl<FootingSchemeInput>(this as FootingSchemeInput, _$identity);

  /// Serializes this FootingSchemeInput to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is FootingSchemeInput&&(identical(other.id, id) || other.id == id)&&(identical(other.fcu, fcu) || other.fcu == fcu)&&(identical(other.cover, cover) || other.cover == cover)&&(identical(other.mainKValue, mainKValue) || other.mainKValue == mainKValue)&&(identical(other.mainSteelRatio, mainSteelRatio) || other.mainSteelRatio == mainSteelRatio)&&(identical(other.minS, minS) || other.minS == minS)&&(identical(other.maxS, maxS) || other.maxS == maxS)&&(identical(other.maxDepth, maxDepth) || other.maxDepth == maxDepth)&&(identical(other.minDepth, minDepth) || other.minDepth == minDepth)&&(identical(other.maxLayers, maxLayers) || other.maxLayers == maxLayers)&&(identical(other.groundType, groundType) || other.groundType == groundType)&&(identical(other.soilNValue, soilNValue) || other.soilNValue == soilNValue)&&(identical(other.footingTopLevel, footingTopLevel) || other.footingTopLevel == footingTopLevel)&&(identical(other.waterTableLevel, waterTableLevel) || other.waterTableLevel == waterTableLevel)&&(identical(other.rockCapacity, rockCapacity) || other.rockCapacity == rockCapacity)&&(identical(other.colShape, colShape) || other.colShape == colShape)&&(identical(other.columnSize, columnSize) || other.columnSize == columnSize)&&(identical(other.slsLoad, slsLoad) || other.slsLoad == slsLoad)&&(identical(other.ulsLoad, ulsLoad) || other.ulsLoad == ulsLoad)&&(identical(other.useSelectColLoad, useSelectColLoad) || other.useSelectColLoad == useSelectColLoad));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hashAll([runtimeType,id,fcu,cover,mainKValue,mainSteelRatio,minS,maxS,maxDepth,minDepth,maxLayers,groundType,soilNValue,footingTopLevel,waterTableLevel,rockCapacity,colShape,columnSize,slsLoad,ulsLoad,useSelectColLoad]);

@override
String toString() {
  return 'FootingSchemeInput(id: $id, fcu: $fcu, cover: $cover, mainKValue: $mainKValue, mainSteelRatio: $mainSteelRatio, minS: $minS, maxS: $maxS, maxDepth: $maxDepth, minDepth: $minDepth, maxLayers: $maxLayers, groundType: $groundType, soilNValue: $soilNValue, footingTopLevel: $footingTopLevel, waterTableLevel: $waterTableLevel, rockCapacity: $rockCapacity, colShape: $colShape, columnSize: $columnSize, slsLoad: $slsLoad, ulsLoad: $ulsLoad, useSelectColLoad: $useSelectColLoad)';
}


}

/// @nodoc
abstract mixin class $FootingSchemeInputCopyWith<$Res>  {
  factory $FootingSchemeInputCopyWith(FootingSchemeInput value, $Res Function(FootingSchemeInput) _then) = _$FootingSchemeInputCopyWithImpl;
@useResult
$Res call({
 String id, double fcu, double cover, double mainKValue, double mainSteelRatio, int minS, int maxS, double maxDepth, double minDepth, int maxLayers, String groundType, double soilNValue, double footingTopLevel, double waterTableLevel, double rockCapacity, String colShape, double columnSize, double slsLoad, double ulsLoad, bool useSelectColLoad
});




}
/// @nodoc
class _$FootingSchemeInputCopyWithImpl<$Res>
    implements $FootingSchemeInputCopyWith<$Res> {
  _$FootingSchemeInputCopyWithImpl(this._self, this._then);

  final FootingSchemeInput _self;
  final $Res Function(FootingSchemeInput) _then;

/// Create a copy of FootingSchemeInput
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? fcu = null,Object? cover = null,Object? mainKValue = null,Object? mainSteelRatio = null,Object? minS = null,Object? maxS = null,Object? maxDepth = null,Object? minDepth = null,Object? maxLayers = null,Object? groundType = null,Object? soilNValue = null,Object? footingTopLevel = null,Object? waterTableLevel = null,Object? rockCapacity = null,Object? colShape = null,Object? columnSize = null,Object? slsLoad = null,Object? ulsLoad = null,Object? useSelectColLoad = null,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,fcu: null == fcu ? _self.fcu : fcu // ignore: cast_nullable_to_non_nullable
as double,cover: null == cover ? _self.cover : cover // ignore: cast_nullable_to_non_nullable
as double,mainKValue: null == mainKValue ? _self.mainKValue : mainKValue // ignore: cast_nullable_to_non_nullable
as double,mainSteelRatio: null == mainSteelRatio ? _self.mainSteelRatio : mainSteelRatio // ignore: cast_nullable_to_non_nullable
as double,minS: null == minS ? _self.minS : minS // ignore: cast_nullable_to_non_nullable
as int,maxS: null == maxS ? _self.maxS : maxS // ignore: cast_nullable_to_non_nullable
as int,maxDepth: null == maxDepth ? _self.maxDepth : maxDepth // ignore: cast_nullable_to_non_nullable
as double,minDepth: null == minDepth ? _self.minDepth : minDepth // ignore: cast_nullable_to_non_nullable
as double,maxLayers: null == maxLayers ? _self.maxLayers : maxLayers // ignore: cast_nullable_to_non_nullable
as int,groundType: null == groundType ? _self.groundType : groundType // ignore: cast_nullable_to_non_nullable
as String,soilNValue: null == soilNValue ? _self.soilNValue : soilNValue // ignore: cast_nullable_to_non_nullable
as double,footingTopLevel: null == footingTopLevel ? _self.footingTopLevel : footingTopLevel // ignore: cast_nullable_to_non_nullable
as double,waterTableLevel: null == waterTableLevel ? _self.waterTableLevel : waterTableLevel // ignore: cast_nullable_to_non_nullable
as double,rockCapacity: null == rockCapacity ? _self.rockCapacity : rockCapacity // ignore: cast_nullable_to_non_nullable
as double,colShape: null == colShape ? _self.colShape : colShape // ignore: cast_nullable_to_non_nullable
as String,columnSize: null == columnSize ? _self.columnSize : columnSize // ignore: cast_nullable_to_non_nullable
as double,slsLoad: null == slsLoad ? _self.slsLoad : slsLoad // ignore: cast_nullable_to_non_nullable
as double,ulsLoad: null == ulsLoad ? _self.ulsLoad : ulsLoad // ignore: cast_nullable_to_non_nullable
as double,useSelectColLoad: null == useSelectColLoad ? _self.useSelectColLoad : useSelectColLoad // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}

}


/// Adds pattern-matching-related methods to [FootingSchemeInput].
extension FootingSchemeInputPatterns on FootingSchemeInput {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _FootingSchemeInput value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _FootingSchemeInput() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _FootingSchemeInput value)  $default,){
final _that = this;
switch (_that) {
case _FootingSchemeInput():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _FootingSchemeInput value)?  $default,){
final _that = this;
switch (_that) {
case _FootingSchemeInput() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  double fcu,  double cover,  double mainKValue,  double mainSteelRatio,  int minS,  int maxS,  double maxDepth,  double minDepth,  int maxLayers,  String groundType,  double soilNValue,  double footingTopLevel,  double waterTableLevel,  double rockCapacity,  String colShape,  double columnSize,  double slsLoad,  double ulsLoad,  bool useSelectColLoad)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _FootingSchemeInput() when $default != null:
return $default(_that.id,_that.fcu,_that.cover,_that.mainKValue,_that.mainSteelRatio,_that.minS,_that.maxS,_that.maxDepth,_that.minDepth,_that.maxLayers,_that.groundType,_that.soilNValue,_that.footingTopLevel,_that.waterTableLevel,_that.rockCapacity,_that.colShape,_that.columnSize,_that.slsLoad,_that.ulsLoad,_that.useSelectColLoad);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  double fcu,  double cover,  double mainKValue,  double mainSteelRatio,  int minS,  int maxS,  double maxDepth,  double minDepth,  int maxLayers,  String groundType,  double soilNValue,  double footingTopLevel,  double waterTableLevel,  double rockCapacity,  String colShape,  double columnSize,  double slsLoad,  double ulsLoad,  bool useSelectColLoad)  $default,) {final _that = this;
switch (_that) {
case _FootingSchemeInput():
return $default(_that.id,_that.fcu,_that.cover,_that.mainKValue,_that.mainSteelRatio,_that.minS,_that.maxS,_that.maxDepth,_that.minDepth,_that.maxLayers,_that.groundType,_that.soilNValue,_that.footingTopLevel,_that.waterTableLevel,_that.rockCapacity,_that.colShape,_that.columnSize,_that.slsLoad,_that.ulsLoad,_that.useSelectColLoad);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  double fcu,  double cover,  double mainKValue,  double mainSteelRatio,  int minS,  int maxS,  double maxDepth,  double minDepth,  int maxLayers,  String groundType,  double soilNValue,  double footingTopLevel,  double waterTableLevel,  double rockCapacity,  String colShape,  double columnSize,  double slsLoad,  double ulsLoad,  bool useSelectColLoad)?  $default,) {final _that = this;
switch (_that) {
case _FootingSchemeInput() when $default != null:
return $default(_that.id,_that.fcu,_that.cover,_that.mainKValue,_that.mainSteelRatio,_that.minS,_that.maxS,_that.maxDepth,_that.minDepth,_that.maxLayers,_that.groundType,_that.soilNValue,_that.footingTopLevel,_that.waterTableLevel,_that.rockCapacity,_that.colShape,_that.columnSize,_that.slsLoad,_that.ulsLoad,_that.useSelectColLoad);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _FootingSchemeInput extends FootingSchemeInput {
   _FootingSchemeInput({this.id = '1', this.fcu = 45.0, this.cover = 75.0, this.mainKValue = 0.156, this.mainSteelRatio = 0.04, this.minS = 100, this.maxS = 300, this.maxDepth = 2000, this.minDepth = 500.0, this.maxLayers = 4, this.groundType = 'Soil', this.soilNValue = 50, this.footingTopLevel = 0, this.waterTableLevel = -3, this.rockCapacity = 1000, this.colShape = 'Square', this.columnSize = 1000.0, this.slsLoad = 1500.0, this.ulsLoad = 3000.0, this.useSelectColLoad = false}): super._();
  factory _FootingSchemeInput.fromJson(Map<String, dynamic> json) => _$FootingSchemeInputFromJson(json);

@override@JsonKey() final  String id;
@override@JsonKey() final  double fcu;
@override@JsonKey() final  double cover;
@override@JsonKey() final  double mainKValue;
@override@JsonKey() final  double mainSteelRatio;
@override@JsonKey() final  int minS;
@override@JsonKey() final  int maxS;
@override@JsonKey() final  double maxDepth;
@override@JsonKey() final  double minDepth;
@override@JsonKey() final  int maxLayers;
@override@JsonKey() final  String groundType;
@override@JsonKey() final  double soilNValue;
@override@JsonKey() final  double footingTopLevel;
@override@JsonKey() final  double waterTableLevel;
@override@JsonKey() final  double rockCapacity;
@override@JsonKey() final  String colShape;
@override@JsonKey() final  double columnSize;
@override@JsonKey() final  double slsLoad;
@override@JsonKey() final  double ulsLoad;
@override@JsonKey() final  bool useSelectColLoad;

/// Create a copy of FootingSchemeInput
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$FootingSchemeInputCopyWith<_FootingSchemeInput> get copyWith => __$FootingSchemeInputCopyWithImpl<_FootingSchemeInput>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$FootingSchemeInputToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _FootingSchemeInput&&(identical(other.id, id) || other.id == id)&&(identical(other.fcu, fcu) || other.fcu == fcu)&&(identical(other.cover, cover) || other.cover == cover)&&(identical(other.mainKValue, mainKValue) || other.mainKValue == mainKValue)&&(identical(other.mainSteelRatio, mainSteelRatio) || other.mainSteelRatio == mainSteelRatio)&&(identical(other.minS, minS) || other.minS == minS)&&(identical(other.maxS, maxS) || other.maxS == maxS)&&(identical(other.maxDepth, maxDepth) || other.maxDepth == maxDepth)&&(identical(other.minDepth, minDepth) || other.minDepth == minDepth)&&(identical(other.maxLayers, maxLayers) || other.maxLayers == maxLayers)&&(identical(other.groundType, groundType) || other.groundType == groundType)&&(identical(other.soilNValue, soilNValue) || other.soilNValue == soilNValue)&&(identical(other.footingTopLevel, footingTopLevel) || other.footingTopLevel == footingTopLevel)&&(identical(other.waterTableLevel, waterTableLevel) || other.waterTableLevel == waterTableLevel)&&(identical(other.rockCapacity, rockCapacity) || other.rockCapacity == rockCapacity)&&(identical(other.colShape, colShape) || other.colShape == colShape)&&(identical(other.columnSize, columnSize) || other.columnSize == columnSize)&&(identical(other.slsLoad, slsLoad) || other.slsLoad == slsLoad)&&(identical(other.ulsLoad, ulsLoad) || other.ulsLoad == ulsLoad)&&(identical(other.useSelectColLoad, useSelectColLoad) || other.useSelectColLoad == useSelectColLoad));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hashAll([runtimeType,id,fcu,cover,mainKValue,mainSteelRatio,minS,maxS,maxDepth,minDepth,maxLayers,groundType,soilNValue,footingTopLevel,waterTableLevel,rockCapacity,colShape,columnSize,slsLoad,ulsLoad,useSelectColLoad]);

@override
String toString() {
  return 'FootingSchemeInput(id: $id, fcu: $fcu, cover: $cover, mainKValue: $mainKValue, mainSteelRatio: $mainSteelRatio, minS: $minS, maxS: $maxS, maxDepth: $maxDepth, minDepth: $minDepth, maxLayers: $maxLayers, groundType: $groundType, soilNValue: $soilNValue, footingTopLevel: $footingTopLevel, waterTableLevel: $waterTableLevel, rockCapacity: $rockCapacity, colShape: $colShape, columnSize: $columnSize, slsLoad: $slsLoad, ulsLoad: $ulsLoad, useSelectColLoad: $useSelectColLoad)';
}


}

/// @nodoc
abstract mixin class _$FootingSchemeInputCopyWith<$Res> implements $FootingSchemeInputCopyWith<$Res> {
  factory _$FootingSchemeInputCopyWith(_FootingSchemeInput value, $Res Function(_FootingSchemeInput) _then) = __$FootingSchemeInputCopyWithImpl;
@override @useResult
$Res call({
 String id, double fcu, double cover, double mainKValue, double mainSteelRatio, int minS, int maxS, double maxDepth, double minDepth, int maxLayers, String groundType, double soilNValue, double footingTopLevel, double waterTableLevel, double rockCapacity, String colShape, double columnSize, double slsLoad, double ulsLoad, bool useSelectColLoad
});




}
/// @nodoc
class __$FootingSchemeInputCopyWithImpl<$Res>
    implements _$FootingSchemeInputCopyWith<$Res> {
  __$FootingSchemeInputCopyWithImpl(this._self, this._then);

  final _FootingSchemeInput _self;
  final $Res Function(_FootingSchemeInput) _then;

/// Create a copy of FootingSchemeInput
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? fcu = null,Object? cover = null,Object? mainKValue = null,Object? mainSteelRatio = null,Object? minS = null,Object? maxS = null,Object? maxDepth = null,Object? minDepth = null,Object? maxLayers = null,Object? groundType = null,Object? soilNValue = null,Object? footingTopLevel = null,Object? waterTableLevel = null,Object? rockCapacity = null,Object? colShape = null,Object? columnSize = null,Object? slsLoad = null,Object? ulsLoad = null,Object? useSelectColLoad = null,}) {
  return _then(_FootingSchemeInput(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,fcu: null == fcu ? _self.fcu : fcu // ignore: cast_nullable_to_non_nullable
as double,cover: null == cover ? _self.cover : cover // ignore: cast_nullable_to_non_nullable
as double,mainKValue: null == mainKValue ? _self.mainKValue : mainKValue // ignore: cast_nullable_to_non_nullable
as double,mainSteelRatio: null == mainSteelRatio ? _self.mainSteelRatio : mainSteelRatio // ignore: cast_nullable_to_non_nullable
as double,minS: null == minS ? _self.minS : minS // ignore: cast_nullable_to_non_nullable
as int,maxS: null == maxS ? _self.maxS : maxS // ignore: cast_nullable_to_non_nullable
as int,maxDepth: null == maxDepth ? _self.maxDepth : maxDepth // ignore: cast_nullable_to_non_nullable
as double,minDepth: null == minDepth ? _self.minDepth : minDepth // ignore: cast_nullable_to_non_nullable
as double,maxLayers: null == maxLayers ? _self.maxLayers : maxLayers // ignore: cast_nullable_to_non_nullable
as int,groundType: null == groundType ? _self.groundType : groundType // ignore: cast_nullable_to_non_nullable
as String,soilNValue: null == soilNValue ? _self.soilNValue : soilNValue // ignore: cast_nullable_to_non_nullable
as double,footingTopLevel: null == footingTopLevel ? _self.footingTopLevel : footingTopLevel // ignore: cast_nullable_to_non_nullable
as double,waterTableLevel: null == waterTableLevel ? _self.waterTableLevel : waterTableLevel // ignore: cast_nullable_to_non_nullable
as double,rockCapacity: null == rockCapacity ? _self.rockCapacity : rockCapacity // ignore: cast_nullable_to_non_nullable
as double,colShape: null == colShape ? _self.colShape : colShape // ignore: cast_nullable_to_non_nullable
as String,columnSize: null == columnSize ? _self.columnSize : columnSize // ignore: cast_nullable_to_non_nullable
as double,slsLoad: null == slsLoad ? _self.slsLoad : slsLoad // ignore: cast_nullable_to_non_nullable
as double,ulsLoad: null == ulsLoad ? _self.ulsLoad : ulsLoad // ignore: cast_nullable_to_non_nullable
as double,useSelectColLoad: null == useSelectColLoad ? _self.useSelectColLoad : useSelectColLoad // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}


}

// dart format on
