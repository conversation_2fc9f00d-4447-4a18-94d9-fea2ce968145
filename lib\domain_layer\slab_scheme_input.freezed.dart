// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'slab_scheme_input.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$SlabSchemeInput {

 String get id; double get span; double get fcu; double get cover; double get mainKValue; double get mainSteelRatio; int get minS; int get maxS; double get maxDepth; double get minDepth; int get maxLayers; double get spanIncreament; int get iterationSteps; String get usage;
/// Create a copy of SlabSchemeInput
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$SlabSchemeInputCopyWith<SlabSchemeInput> get copyWith => _$SlabSchemeInputCopyWithImpl<SlabSchemeInput>(this as SlabSchemeInput, _$identity);

  /// Serializes this SlabSchemeInput to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is SlabSchemeInput&&(identical(other.id, id) || other.id == id)&&(identical(other.span, span) || other.span == span)&&(identical(other.fcu, fcu) || other.fcu == fcu)&&(identical(other.cover, cover) || other.cover == cover)&&(identical(other.mainKValue, mainKValue) || other.mainKValue == mainKValue)&&(identical(other.mainSteelRatio, mainSteelRatio) || other.mainSteelRatio == mainSteelRatio)&&(identical(other.minS, minS) || other.minS == minS)&&(identical(other.maxS, maxS) || other.maxS == maxS)&&(identical(other.maxDepth, maxDepth) || other.maxDepth == maxDepth)&&(identical(other.minDepth, minDepth) || other.minDepth == minDepth)&&(identical(other.maxLayers, maxLayers) || other.maxLayers == maxLayers)&&(identical(other.spanIncreament, spanIncreament) || other.spanIncreament == spanIncreament)&&(identical(other.iterationSteps, iterationSteps) || other.iterationSteps == iterationSteps)&&(identical(other.usage, usage) || other.usage == usage));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,span,fcu,cover,mainKValue,mainSteelRatio,minS,maxS,maxDepth,minDepth,maxLayers,spanIncreament,iterationSteps,usage);

@override
String toString() {
  return 'SlabSchemeInput(id: $id, span: $span, fcu: $fcu, cover: $cover, mainKValue: $mainKValue, mainSteelRatio: $mainSteelRatio, minS: $minS, maxS: $maxS, maxDepth: $maxDepth, minDepth: $minDepth, maxLayers: $maxLayers, spanIncreament: $spanIncreament, iterationSteps: $iterationSteps, usage: $usage)';
}


}

/// @nodoc
abstract mixin class $SlabSchemeInputCopyWith<$Res>  {
  factory $SlabSchemeInputCopyWith(SlabSchemeInput value, $Res Function(SlabSchemeInput) _then) = _$SlabSchemeInputCopyWithImpl;
@useResult
$Res call({
 String id, double span, double fcu, double cover, double mainKValue, double mainSteelRatio, int minS, int maxS, double maxDepth, double minDepth, int maxLayers, double spanIncreament, int iterationSteps, String usage
});




}
/// @nodoc
class _$SlabSchemeInputCopyWithImpl<$Res>
    implements $SlabSchemeInputCopyWith<$Res> {
  _$SlabSchemeInputCopyWithImpl(this._self, this._then);

  final SlabSchemeInput _self;
  final $Res Function(SlabSchemeInput) _then;

/// Create a copy of SlabSchemeInput
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? span = null,Object? fcu = null,Object? cover = null,Object? mainKValue = null,Object? mainSteelRatio = null,Object? minS = null,Object? maxS = null,Object? maxDepth = null,Object? minDepth = null,Object? maxLayers = null,Object? spanIncreament = null,Object? iterationSteps = null,Object? usage = null,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,span: null == span ? _self.span : span // ignore: cast_nullable_to_non_nullable
as double,fcu: null == fcu ? _self.fcu : fcu // ignore: cast_nullable_to_non_nullable
as double,cover: null == cover ? _self.cover : cover // ignore: cast_nullable_to_non_nullable
as double,mainKValue: null == mainKValue ? _self.mainKValue : mainKValue // ignore: cast_nullable_to_non_nullable
as double,mainSteelRatio: null == mainSteelRatio ? _self.mainSteelRatio : mainSteelRatio // ignore: cast_nullable_to_non_nullable
as double,minS: null == minS ? _self.minS : minS // ignore: cast_nullable_to_non_nullable
as int,maxS: null == maxS ? _self.maxS : maxS // ignore: cast_nullable_to_non_nullable
as int,maxDepth: null == maxDepth ? _self.maxDepth : maxDepth // ignore: cast_nullable_to_non_nullable
as double,minDepth: null == minDepth ? _self.minDepth : minDepth // ignore: cast_nullable_to_non_nullable
as double,maxLayers: null == maxLayers ? _self.maxLayers : maxLayers // ignore: cast_nullable_to_non_nullable
as int,spanIncreament: null == spanIncreament ? _self.spanIncreament : spanIncreament // ignore: cast_nullable_to_non_nullable
as double,iterationSteps: null == iterationSteps ? _self.iterationSteps : iterationSteps // ignore: cast_nullable_to_non_nullable
as int,usage: null == usage ? _self.usage : usage // ignore: cast_nullable_to_non_nullable
as String,
  ));
}

}


/// Adds pattern-matching-related methods to [SlabSchemeInput].
extension SlabSchemeInputPatterns on SlabSchemeInput {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _SlabSchemeInput value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _SlabSchemeInput() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _SlabSchemeInput value)  $default,){
final _that = this;
switch (_that) {
case _SlabSchemeInput():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _SlabSchemeInput value)?  $default,){
final _that = this;
switch (_that) {
case _SlabSchemeInput() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  double span,  double fcu,  double cover,  double mainKValue,  double mainSteelRatio,  int minS,  int maxS,  double maxDepth,  double minDepth,  int maxLayers,  double spanIncreament,  int iterationSteps,  String usage)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _SlabSchemeInput() when $default != null:
return $default(_that.id,_that.span,_that.fcu,_that.cover,_that.mainKValue,_that.mainSteelRatio,_that.minS,_that.maxS,_that.maxDepth,_that.minDepth,_that.maxLayers,_that.spanIncreament,_that.iterationSteps,_that.usage);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  double span,  double fcu,  double cover,  double mainKValue,  double mainSteelRatio,  int minS,  int maxS,  double maxDepth,  double minDepth,  int maxLayers,  double spanIncreament,  int iterationSteps,  String usage)  $default,) {final _that = this;
switch (_that) {
case _SlabSchemeInput():
return $default(_that.id,_that.span,_that.fcu,_that.cover,_that.mainKValue,_that.mainSteelRatio,_that.minS,_that.maxS,_that.maxDepth,_that.minDepth,_that.maxLayers,_that.spanIncreament,_that.iterationSteps,_that.usage);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  double span,  double fcu,  double cover,  double mainKValue,  double mainSteelRatio,  int minS,  int maxS,  double maxDepth,  double minDepth,  int maxLayers,  double spanIncreament,  int iterationSteps,  String usage)?  $default,) {final _that = this;
switch (_that) {
case _SlabSchemeInput() when $default != null:
return $default(_that.id,_that.span,_that.fcu,_that.cover,_that.mainKValue,_that.mainSteelRatio,_that.minS,_that.maxS,_that.maxDepth,_that.minDepth,_that.maxLayers,_that.spanIncreament,_that.iterationSteps,_that.usage);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _SlabSchemeInput extends SlabSchemeInput {
   _SlabSchemeInput({this.id = '1', this.span = 5.0, this.fcu = 45.0, this.cover = 35.0, this.mainKValue = 0.156, this.mainSteelRatio = 0.04, this.minS = 100, this.maxS = 300, this.maxDepth = 500, this.minDepth = 150.0, this.maxLayers = 2, this.spanIncreament = 0.5, this.iterationSteps = 4, this.usage = ''}): super._();
  factory _SlabSchemeInput.fromJson(Map<String, dynamic> json) => _$SlabSchemeInputFromJson(json);

@override@JsonKey() final  String id;
@override@JsonKey() final  double span;
@override@JsonKey() final  double fcu;
@override@JsonKey() final  double cover;
@override@JsonKey() final  double mainKValue;
@override@JsonKey() final  double mainSteelRatio;
@override@JsonKey() final  int minS;
@override@JsonKey() final  int maxS;
@override@JsonKey() final  double maxDepth;
@override@JsonKey() final  double minDepth;
@override@JsonKey() final  int maxLayers;
@override@JsonKey() final  double spanIncreament;
@override@JsonKey() final  int iterationSteps;
@override@JsonKey() final  String usage;

/// Create a copy of SlabSchemeInput
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$SlabSchemeInputCopyWith<_SlabSchemeInput> get copyWith => __$SlabSchemeInputCopyWithImpl<_SlabSchemeInput>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$SlabSchemeInputToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _SlabSchemeInput&&(identical(other.id, id) || other.id == id)&&(identical(other.span, span) || other.span == span)&&(identical(other.fcu, fcu) || other.fcu == fcu)&&(identical(other.cover, cover) || other.cover == cover)&&(identical(other.mainKValue, mainKValue) || other.mainKValue == mainKValue)&&(identical(other.mainSteelRatio, mainSteelRatio) || other.mainSteelRatio == mainSteelRatio)&&(identical(other.minS, minS) || other.minS == minS)&&(identical(other.maxS, maxS) || other.maxS == maxS)&&(identical(other.maxDepth, maxDepth) || other.maxDepth == maxDepth)&&(identical(other.minDepth, minDepth) || other.minDepth == minDepth)&&(identical(other.maxLayers, maxLayers) || other.maxLayers == maxLayers)&&(identical(other.spanIncreament, spanIncreament) || other.spanIncreament == spanIncreament)&&(identical(other.iterationSteps, iterationSteps) || other.iterationSteps == iterationSteps)&&(identical(other.usage, usage) || other.usage == usage));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,span,fcu,cover,mainKValue,mainSteelRatio,minS,maxS,maxDepth,minDepth,maxLayers,spanIncreament,iterationSteps,usage);

@override
String toString() {
  return 'SlabSchemeInput(id: $id, span: $span, fcu: $fcu, cover: $cover, mainKValue: $mainKValue, mainSteelRatio: $mainSteelRatio, minS: $minS, maxS: $maxS, maxDepth: $maxDepth, minDepth: $minDepth, maxLayers: $maxLayers, spanIncreament: $spanIncreament, iterationSteps: $iterationSteps, usage: $usage)';
}


}

/// @nodoc
abstract mixin class _$SlabSchemeInputCopyWith<$Res> implements $SlabSchemeInputCopyWith<$Res> {
  factory _$SlabSchemeInputCopyWith(_SlabSchemeInput value, $Res Function(_SlabSchemeInput) _then) = __$SlabSchemeInputCopyWithImpl;
@override @useResult
$Res call({
 String id, double span, double fcu, double cover, double mainKValue, double mainSteelRatio, int minS, int maxS, double maxDepth, double minDepth, int maxLayers, double spanIncreament, int iterationSteps, String usage
});




}
/// @nodoc
class __$SlabSchemeInputCopyWithImpl<$Res>
    implements _$SlabSchemeInputCopyWith<$Res> {
  __$SlabSchemeInputCopyWithImpl(this._self, this._then);

  final _SlabSchemeInput _self;
  final $Res Function(_SlabSchemeInput) _then;

/// Create a copy of SlabSchemeInput
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? span = null,Object? fcu = null,Object? cover = null,Object? mainKValue = null,Object? mainSteelRatio = null,Object? minS = null,Object? maxS = null,Object? maxDepth = null,Object? minDepth = null,Object? maxLayers = null,Object? spanIncreament = null,Object? iterationSteps = null,Object? usage = null,}) {
  return _then(_SlabSchemeInput(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,span: null == span ? _self.span : span // ignore: cast_nullable_to_non_nullable
as double,fcu: null == fcu ? _self.fcu : fcu // ignore: cast_nullable_to_non_nullable
as double,cover: null == cover ? _self.cover : cover // ignore: cast_nullable_to_non_nullable
as double,mainKValue: null == mainKValue ? _self.mainKValue : mainKValue // ignore: cast_nullable_to_non_nullable
as double,mainSteelRatio: null == mainSteelRatio ? _self.mainSteelRatio : mainSteelRatio // ignore: cast_nullable_to_non_nullable
as double,minS: null == minS ? _self.minS : minS // ignore: cast_nullable_to_non_nullable
as int,maxS: null == maxS ? _self.maxS : maxS // ignore: cast_nullable_to_non_nullable
as int,maxDepth: null == maxDepth ? _self.maxDepth : maxDepth // ignore: cast_nullable_to_non_nullable
as double,minDepth: null == minDepth ? _self.minDepth : minDepth // ignore: cast_nullable_to_non_nullable
as double,maxLayers: null == maxLayers ? _self.maxLayers : maxLayers // ignore: cast_nullable_to_non_nullable
as int,spanIncreament: null == spanIncreament ? _self.spanIncreament : spanIncreament // ignore: cast_nullable_to_non_nullable
as double,iterationSteps: null == iterationSteps ? _self.iterationSteps : iterationSteps // ignore: cast_nullable_to_non_nullable
as int,usage: null == usage ? _self.usage : usage // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

// dart format on
