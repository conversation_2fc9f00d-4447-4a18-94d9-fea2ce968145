// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'transfer_beam_scheme_input_global_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$transferBeamSchemeInputGlobalControllerHash() =>
    r'c08c3d6e5135486b1f07d83d51e9f44a41588af5';

/// See also [TransferBeamSchemeInputGlobalController].
@ProviderFor(TransferBeamSchemeInputGlobalController)
final transferBeamSchemeInputGlobalControllerProvider =
    AutoDisposeAsyncNotifierProvider<
      TransferBeamSchemeInputGlobalController,
      TransferBeamSchemeInputGlobal
    >.internal(
      TransferBeamSchemeInputGlobalController.new,
      name: r'transferBeamSchemeInputGlobalControllerProvider',
      debugGetCreateSourceHash:
          const bool.fromEnvironment('dart.vm.product')
              ? null
              : _$transferBeamSchemeInputGlobalControllerHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$TransferBeamSchemeInputGlobalController =
    AutoDisposeAsyncNotifier<TransferBeamSchemeInputGlobal>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
