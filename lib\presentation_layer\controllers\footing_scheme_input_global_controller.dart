import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:structify/domain_layer/footing_scheme_input.dart';
import '../../domain_layer/footing_scheme_input_global.dart';
import '../screen/homescreen.dart';

part 'footing_scheme_input_global_controller.g.dart';

@riverpod
class FootingSchemeInputGlobalController
    extends _$FootingSchemeInputGlobalController {
  @override
  FutureOr<FootingSchemeInputGlobal> build() async {
    // print('Build: Slab Scheme Input');
    FootingSchemeInputGlobal footingSchemeInput =
        await ref
            .read(appDatabaseControllerProvider.notifier)
            .queryFootingSchemeInputGlobal();
    return footingSchemeInput;
  }

  Future<void> updateTable({double? colLoadFactor, String? id}) async {
    final x = await future;
    // Create a list of updates
    FootingSchemeInputGlobal newState = x.copyWith(
      colLoadFactor: colLoadFactor ?? x.colLoadFactor,
      id: id ?? x.id,
    );
    // Update the state only if there are changes
    state = AsyncData(newState);
  }
}
