import 'package:nanoid2/nanoid2.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

//presentation layer
import '../../domain_layer/column_scheme_data.dart';
import '../../domain_layer/wind_load.dart';
import '../../domain_layer/transfer_beam_scheme_input.dart';
import '../screen/homescreen.dart';

// domain layer
part 'windload_input_controller.g.dart';

@riverpod
class WindLoadInputController extends _$WindLoadInputController {
  @override
  FutureOr<List<WindLoad>> build() async {
    // print('Build: Transfer Beam Scheme Input');
    final List<WindLoad> windLoadInputs =
        await ref
            .read(appDatabaseControllerProvider.notifier)
            .queryWindLoadInput();

    final data1 = ref.watch(windLoadInputGlobalControllerProvider);
    return data1.when(
      data: (inputGlobal) async {
        if (windLoadInputs.isNotEmpty) {
          final List<WindLoad> finalList = [];
          await Future.forEach(windLoadInputs, (input) async {
            finalList.add(
              input.copyWith(
                h: inputGlobal.h,
                bTop: inputGlobal.bTop,
                dTop: inputGlobal.dTop,
                sS: inputGlobal.sS,
                bldgType: inputGlobal.bldgType,
                amplificationFactor: inputGlobal.amplificationFactor,
              ),
            );
          });
          return finalList;
        } else {
          return windLoadInputs;
        }
      },
      error: (error, stackTrace) => [],
      loading: () => [],
    );
  }

  Future<void> addTable(double z, double b) async {
    final x = await future;
    final id = await _generateTaskID();
    final inputGlobal = await ref.read(
      windLoadInputGlobalControllerProvider.future,
    );
    state = AsyncData([
      ...x,
      WindLoad(
        h: inputGlobal.h,
        bTop: inputGlobal.bTop,
        dTop: inputGlobal.dTop,
        sS: inputGlobal.sS,
        bldgType: inputGlobal.bldgType,
        amplificationFactor: inputGlobal.amplificationFactor,
        z: z,
        b: b,
        id: id,
      ),
    ]);
  }

  Future<void> addEmptytable() async {
    final x = await future;
    final id = await _generateTaskID();
    final inputGlobal = await ref.read(
      windLoadInputGlobalControllerProvider.future,
    );
    state = AsyncData([
      ...x,
      WindLoad(
        h: inputGlobal.h,
        bTop: inputGlobal.bTop,
        dTop: inputGlobal.dTop,
        sS: inputGlobal.sS,
        bldgType: inputGlobal.bldgType,
        amplificationFactor: inputGlobal.amplificationFactor,
        id: id,
      ),
    ]);
  }

  Future<void> deleteTable(String id) async {
    // print("Before deletion: ${state.map((item) => item.usage).toList()}");
    final x = await future;
    x.removeWhere((item) => item.id == id);
    // print("After deletion: ${x.map((item) => item.usage).toList()}");
    state = AsyncData(x);
  }

  Future<void> deleteAllTable() async {
    // print("Before deletion: ${state.map((item) => item.usage).toList()}");
    state = AsyncData(<WindLoad>[]);
  }

  Future<void> insertEmptyTable(int index) async {
    final x = await future;
    final id = await _generateTaskID();
    final inputGlobal = await ref.read(
      windLoadInputGlobalControllerProvider.future,
    );
    x.insert(
      index + 1,
      WindLoad(
        h: inputGlobal.h,
        bTop: inputGlobal.bTop,
        dTop: inputGlobal.dTop,
        sS: inputGlobal.sS,
        bldgType: inputGlobal.bldgType,
        amplificationFactor: inputGlobal.amplificationFactor,
        id: id,
      ),
    );
    state = AsyncData(x);
  }

  Future<void> copyAndInsertTable(int index) async {
    final x = await future;
    final id = await _generateTaskID();
    x.insert(index + 1, x[index].copyWith(id: id));
    state = AsyncData(x);
  }

  Future<String> _generateTaskID() async {
    String newID = nanoid(alphabet: Alphabet.noDoppelgangerSafe);
    final x = await future;
    final Set<String> transferBeamSchemeInputIds =
        x.map((item) => item.id).toSet();
    // final Set<String> taskIDs = ref.read(_taskIDsProvider);
    while (transferBeamSchemeInputIds.contains(newID)) {
      newID = nanoid(alphabet: Alphabet.noDoppelgangerSafe);
    }
    return newID;
  }

  Future<void> updateTable({
    double? h,
    double? bTop,
    double? dTop,
    double? z,
    double? b,
    double? d,
    double? sS,
    String? bldgType,
    double? amplificationFactor,
    required String id,
  }) async {
    final x = await future;
    List<WindLoad> finalList = [];
    late WindLoad data;
    // Create a list of updates
    await Future.forEach((x), (x1) {
      if (x1.id == id) {
        data = x1.copyWith(
          h: h ?? x1.h,
          bTop: bTop ?? x1.bTop,
          dTop: dTop ?? x1.dTop,
          sS: sS ?? x1.sS,
          bldgType: bldgType ?? x1.bldgType,
          amplificationFactor: amplificationFactor ?? x1.amplificationFactor,
          z: z ?? x1.z,
          b: b ?? x1.b,
          d: d ?? x1.d,
          id: id,
        );
      } else {
        data = x1;
      }
      finalList.add(data);
    });
    // Update the state only if there are changes
    state = AsyncData(finalList);
  }
}
