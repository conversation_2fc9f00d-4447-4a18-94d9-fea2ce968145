import 'package:freezed_annotation/freezed_annotation.dart';
// import 'package:flutter/foundation.dart';
part 'pile_driven_data.freezed.dart';
part 'pile_driven_data.g.dart';

@freezed
abstract class PileDrivenData with _$PileDrivenData {
  const PileDrivenData._();
  factory PileDrivenData({
    @Default(50) double sptNValue,
    @Default(3) double fos,
    @Default(30) double maxPileLength,
    @Default(1000) double slsLoad,
    @Default(2000) double ulsLoad,
    @Default('') String section,
    @Default(0) double length,
    @Default(0) double shaftCapacity,
    @Default(0) double strSLSCapacuity,
    @Default(0) double totalGroundResistance,
    @Default(0) double strULSCapacity,
    @Default(false) bool isSelected,
    @Default('') String calsLog,
    @Default('') String pileDrivenSchemeId, 
  }) = _PileDrivenData;

  factory PileDrivenData.fromJson(Map<String, Object?> json) =>
      _$PileDrivenDataFromJson(json);
}
