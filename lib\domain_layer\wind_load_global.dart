import 'dart:math';
import 'package:freezed_annotation/freezed_annotation.dart';
// import 'package:flutter/foundation.dart';
part 'wind_load_global.freezed.dart';
part 'wind_load_global.g.dart';

@freezed
abstract class WindLoadGlobal with _$WindLoadGlobal {
  const WindLoadGlobal._();
  factory WindLoadGlobal({
    @Default(50.0) double h, // Building Height 
    @Default(30.0) double bTop, // building width at top
    @Default(40.0) double dTop, // building width at top
    @Default(1.11) double sS, // size factor
    @Default('Concrete') String bldgType, // building depth at height considered
    @Default(1.4) double amplificationFactor, 
    @Default('1') String id,
  }) = _WindLoad;


  factory WindLoadGlobal.fromJson(Map<String, Object?> json) =>
      _$WindLoadFromJson(json);

}
