import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:structify/data_layer/app_database.dart';
import 'package:drift_db_viewer/drift_db_viewer.dart';

//below for printing
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:printing/printing.dart';
import 'package:structify/misc/custom_func.dart';

//domain layer
import '../../domain_layer/preferences.dart';

// presentation layer
import '../small_elements/dessign_assumption.dart';
import '../small_elements/button/function_button.dart';
import '../small_elements/pile_frictional_bored_input_ui.dart';
import '../small_elements/pile_frictional_bored_scheme_summary.dart';
import 'homescreen.dart';

class PileFrictionalBoredScheme extends ConsumerWidget {
  PileFrictionalBoredScheme({super.key});

  final GlobalKey _globalKey = GlobalKey();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final ColorScheme colorScheme = Theme.of(context).colorScheme;
    final TextTheme textTheme = Theme.of(context).textTheme;
    final globalData = ref.watch(globalDataControllerProvider);
    ref.watch(appWatcherProvider);
    return globalData.when(
      data: (data) {
        return Scaffold(
          // appBar: AppBar(
          //   title: Text(
          //     'Loading Calculator',
          //     style: textTheme.titleLarge!.copyWith(
          //       color: colorScheme.onPrimary,
          //     ),
          //   ),
          //   backgroundColor: colorScheme.secondaryContainer,
          //   leading: IconButton(
          //     icon: Icon(
          //       Icons.arrow_back,
          //       color: colorScheme.onPrimary,
          //     ), // Customize the icon
          //     onPressed: () {
          //       // Custom action for the back button
          //       Navigator.pop(context); // Go back to the previous screen
          //     },
          //   ),
          //   actions: [
          //     IconButton(
          //       icon: Icon(
          //         Icons.print,
          //         color: colorScheme.onSecondaryContainer,
          //       ),
          //       onPressed: () {
          //         _exportListToPdf(context, ref, rowsPerPage: 14);
          //       },
          //     ),
          //     IconButton(
          //       icon: Icon(
          //         Icons.data_array_outlined,
          //         color: colorScheme.onSecondaryContainer,
          //       ), // Customize the icon
          //       onPressed: () {
          //         final db = AppDatabase(); //This should be a singleton
          //         Navigator.of(context).push(
          //           MaterialPageRoute(builder: (context) => DriftDbViewer(db)),
          //         );
          //       },
          //     ),
          //   ],
          // ),
          backgroundColor: colorScheme.surface,
          body: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Padding(
                padding: const EdgeInsets.all(8.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    FunctionButton(
                      labelIcon: Icon(Icons.print_outlined),
                      labelText: 'Assumption',
                      onTap: (isPressed) async {
                        showAssumption(
                          context,
                          DesignAssumption(
                            isExpanded: true,
                            textStyle: textTheme.bodyMedium!.copyWith(
                              color: colorScheme.onInverseSurface,
                            ),
                            titleStyle: textTheme.titleLarge!.copyWith(
                              color: colorScheme.onInverseSurface,
                            ),
                            title: 'Assumption',
                            assumptions: [
                              'HKCoP for Structural Use of Concrete 2013 (HKCoPSUC2013)',
                              'HKCoP for Foundation 2017 (HKCoPF2017)',
                              'fy = 500 MPa | only T40 used',
                              'Detailing: based on cl. 9.7.4 of HKCoPSUC2013\ncover: 75mm | min steel ratio: 0.5% | min rebar clear: 70mm | max rebar clear: 260mm (~300c/c) | Assumed Links: T12-200',
                              'Both of shaft friction and base bearing are used | FOS applied for both',
                              'Ground Capacity (SLS Design): consider the shaft friction and base bearing',
                              'Structural Capacity (ULS Design): short braced column without significant moment (equation 6.56): N = 0.35fcuAc + 0.67Ascfy',
                            ],
                            tooltipText:
                                '------if Ground fails------\n-adjust FOS (recommended >=2.0) OR\n-adjust max pile diameter OR\n-adjust max pile length OR\n-consider more than one pile within one cap (last resort, as it complicates the drawings)\n'
                                '------if Structural fails------\n-adjust max pile diamaeter OR\n-adjust fcu\n',
                          ),
                        );
                      },
                    ),
                    SizedBox(width: 5.0),
                    FunctionButton(
                      labelIcon: Icon(Icons.print_outlined),
                      labelText: 'Summary',
                      onTap: (isPressed) async {
                        showSummary(context);
                      },
                    ),
                    SizedBox(width: 5.0),
                    FunctionButton(
                      labelIcon: Icon(Icons.print_outlined),
                      labelText: 'Print',
                      onTap: (isPressed) async {
                        await _exportListToPdf(context, ref, rowsPerPage: 11);
                      },
                    ),
                    SizedBox(width: 5.0),
                    FunctionButton(
                      labelIcon: Icon(Icons.data_array_outlined),
                      labelText: 'Data',
                      onTap: (isPressed) async {
                        final db = AppDatabase(); //This should be a singleton
                        Navigator.of(context).push(
                          MaterialPageRoute(
                            builder: (context) => DriftDbViewer(db),
                          ),
                        );
                      },
                    ),
                  ],
                ),
              ),
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Flexible(child: PileFrictionalBoredInputUi(isExpanded: true)),
                  Flexible(
                    child: PileFrictionalBoredSchemeSummary(isExpanded: true),
                  ),
                ],
              ),
            ],
          ),
          // floatingActionButton: FloatingActionButton(
          //   backgroundColor: colorScheme.secondaryContainer.withAlpha(150),
          //   child: Icon(
          //     Icons.add_circle_outline_outlined,
          //     color: colorScheme.onSecondaryContainer.withAlpha(150),
          //   ),
          //   onPressed: () {
          //     ref
          //         .read(steelColumnSchemeInputControllerProvider.notifier)
          //         .addEmptytable();
          //   },
          // ),
        );
      },
      error: (error, stackTrace) {
        return Text('Error: $error');
      },
      loading: () {
        return CircularProgressIndicator();
      },
    );
  }

  Future<void> _exportListToPdf(
    BuildContext context,
    WidgetRef ref, {
    int rowsPerPage = 10,
  }) async {
    final pdf = pw.Document();
    final pileFrictionalBoredSchemes = ref.read(
      pileFrictionalBoredDataControllerProvider,
    );
    pileFrictionalBoredSchemes.when(
      data: (data) {
        int counter = 0;
        do {
          if (counter + rowsPerPage <= data.length) {
            pdf.addPage(
              _customPage(
                context,
                ref,
                startFrom: counter,
                rowsPerPage: rowsPerPage,
              ),
            );
          } else {
            pdf.addPage(
              _customPage(
                context,
                ref,
                startFrom: counter,
                rowsPerPage: data.length - counter,
              ),
            );
          }
          counter += rowsPerPage;
        } while (counter <= data.length);
      },
      error: (error, stackTrace) => {},
      loading: () {},
    );

    await Printing.layoutPdf(
      onLayout: (PdfPageFormat format) async => pdf.save(),
    );
  }
}

pw.Page _customPage(
  BuildContext context,
  WidgetRef ref, {
  int startFrom = 0,
  int? rowsPerPage,
}) {
  // final textStyle = Theme.of(context).textTheme.bodySmall;
  return pw.Page(
    margin: pw.EdgeInsets.fromLTRB(25, 35, 25, 35),
    pageFormat: PdfPageFormat.a4,
    orientation: pw.PageOrientation.portrait,
    build: (pw.Context context) {
      final pileFrictionalBoredSchemes = ref.read(
        pileFrictionalBoredDataControllerProvider,
      );
      final globaldata = ref.read(globalDataControllerProvider);
      return pileFrictionalBoredSchemes.when(
        data: (tables) {
          return globaldata.when(
            data: (data) {
              final double titleFontSize = 8;
              final double normalFontSize = 8;

              return pw.Center(
                child: pw.Column(
                  mainAxisAlignment: pw.MainAxisAlignment.start,
                  children: [
                    pw.Align(
                      alignment: pw.Alignment.center,
                      child: pw.Text(
                        'Frictional Bored Pile Schemes Summary',
                        style: pw.TextStyle(
                          fontSize: 20,
                          fontWeight: pw.FontWeight.bold,
                        ),
                      ),
                    ),
                    pw.SizedBox(height: 10.0),
                    ...List.generate(rowsPerPage ?? tables.length - startFrom, (
                      index,
                    ) {
                      NumberFormat f0 = NumberFormat('0');
                      NumberFormat f3 = NumberFormat('0.000');

                      late List<String> unit;
                      switch (data.unit) {
                        case 'metrics':
                          unit = PreferredUnit.metrics;
                          break;
                        case 'imperial':
                          unit = PreferredUnit.imperial;
                          break;
                        default:
                          unit = PreferredUnit.metrics;
                      }
                      return pw.DefaultTextStyle(
                        style: pw.TextStyle(fontSize: normalFontSize),
                        child: pw.Column(
                          children: [
                            pw.Row(
                              children: [
                                pw.Text(
                                  '${index + startFrom + 1}',
                                  style: pw.TextStyle(
                                    fontSize: titleFontSize,
                                    fontWeight: pw.FontWeight.bold,
                                  ),
                                ),

                                _customVerticalDivider(),

                                pw.Column(
                                  crossAxisAlignment:
                                      pw.CrossAxisAlignment.start,
                                  children: [
                                    pw.Text(
                                      'SPT N-Value :',
                                      style: pw.TextStyle(
                                        fontSize: titleFontSize,
                                        fontWeight: pw.FontWeight.bold,
                                      ),
                                    ),
                                    pw.Text(
                                      'Diameter:',
                                      style: pw.TextStyle(
                                        fontSize: titleFontSize,
                                        fontWeight: pw.FontWeight.bold,
                                      ),
                                    ),
                                    pw.Text(
                                      'Length [${unit[4]}]:',
                                      style: pw.TextStyle(
                                        fontSize: titleFontSize,
                                        fontWeight: pw.FontWeight.bold,
                                      ),
                                    ),
                                    pw.Text(
                                      'fcu [${unit[5]}]:',
                                      style: pw.TextStyle(
                                        fontSize: titleFontSize,
                                        fontWeight: pw.FontWeight.bold,
                                      ),
                                    ),
                                    pw.Text(
                                      'Rebar :',
                                      style: pw.TextStyle(
                                        fontSize: titleFontSize,
                                        fontWeight: pw.FontWeight.bold,
                                      ),
                                    ),
                                  ],
                                ),
                                pw.SizedBox(width: 5.0),
                                pw.Column(
                                  crossAxisAlignment:
                                      pw.CrossAxisAlignment.start,
                                  children: [
                                    pw.Text(
                                      '${tables[index + startFrom].sptNValue}',
                                      style: pw.TextStyle(
                                        fontSize: titleFontSize,
                                        fontWeight: pw.FontWeight.bold,
                                      ),
                                    ),
                                    pw.Text(
                                      '${tables[index + startFrom].diameter}',
                                      style: pw.TextStyle(
                                        fontSize: titleFontSize,
                                        fontWeight: pw.FontWeight.bold,
                                      ),
                                    ),
                                    pw.Text(
                                      '${tables[index + startFrom].length}',
                                      style: pw.TextStyle(
                                        fontSize: titleFontSize,
                                        fontWeight: pw.FontWeight.bold,
                                      ),
                                    ),
                                    pw.Text(
                                      f0.format(tables[index + startFrom].fcu),
                                      style: pw.TextStyle(
                                        fontSize: titleFontSize,
                                        fontWeight: pw.FontWeight.bold,
                                      ),
                                    ),
                                    pw.Text(
                                      tables[index + startFrom].rebar,
                                      style: pw.TextStyle(
                                        fontSize: titleFontSize,
                                        fontWeight: pw.FontWeight.bold,
                                      ),
                                    ),
                                  ],
                                ),

                                _customVerticalDivider(),

                                pw.Row(
                                  children: [
                                    pw.Column(
                                      crossAxisAlignment:
                                          pw.CrossAxisAlignment.start,

                                      children: [
                                        
                                        pw.Text('SLS Load [${unit[0]}]: '),
                                        pw.Text('ULS Load [${unit[0]}]: '),
                                        pw.Text(
                                          'Ground Capacity [${unit[0]}]: ',
                                        ),
                                        pw.Text(
                                          'Structural Capacity [${unit[0]}]: ',
                                        ),
                                      ],
                                    ),
                                    pw.SizedBox(width: 5.0),

                                    pw.Column(
                                      crossAxisAlignment:
                                          pw.CrossAxisAlignment.start,

                                      children: [
                                        pw.Text(
                                          f0.format(
                                            tables[index + startFrom].slsLoad,
                                          ),
                                        ),
                                        pw.Text(
                                          f0.format(
                                            tables[index + startFrom].ulsLoad,
                                          ),
                                        ),
                                        pw.Text(
                                          f0.format(
                                            tables[index + startFrom]
                                                .totalGroundResistance,
                                          ),
                                        ),
                                        pw.Text(
                                          f0.format(
                                            tables[index + startFrom]
                                                .strCapacity,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ],
                                ),
                                _customVerticalDivider(),
                              ],
                            ),
                            _customDivider(),
                          ],
                        ),
                      );
                    }),
                  ],
                ),
              );
            },
            error: (error, stacktrace) => pw.Text('Error: $error'),
            loading: () => pw.Text('Loading'),
          );
        },
        error: (error, stackTrace) => pw.Text('Error: $error'),
        loading: () => pw.Text('loading'),
      );
    },
  );
}

pw.Widget _customVerticalDivider() {
  return pw.Row(
    children: [
      pw.SizedBox(width: 15.0),
      pw.Container(width: 1.0, height: 30, color: PdfColors.grey400),
      pw.SizedBox(width: 15.0),
    ],
  );
}

pw.Widget _customDivider() {
  return pw.Divider(color: PdfColors.grey400);
}
