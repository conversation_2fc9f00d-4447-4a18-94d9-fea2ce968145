// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'slab_scheme_data_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$slabSchemeDataControllerHash() =>
    r'236d32307ed7f449c681d66c58b5deb5c096b82b';

/// See also [SlabSchemeDataController].
@ProviderFor(SlabSchemeDataController)
final slabSchemeDataControllerProvider = AutoDisposeAsyncNotifierProvider<
  SlabSchemeDataController,
  List<SlabSchemeData>
>.internal(
  SlabSchemeDataController.new,
  name: r'slabSchemeDataControllerProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$slabSchemeDataControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$SlabSchemeDataController =
    AutoDisposeAsyncNotifier<List<SlabSchemeData>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
