import 'package:freezed_annotation/freezed_annotation.dart';
// import 'package:flutter/foundation.dart';
part 'steel_beam_scheme_data.freezed.dart';
part 'steel_beam_scheme_data.g.dart';

@freezed
abstract class SteelBeamSchemeData with _$SteelBeamSchemeData {
  const SteelBeamSchemeData._();
  factory SteelBeamSchemeData({
    @Default('') String usage,
    @Default(0.0) double finish,
    @Default(0.0) double service,
    @Default(0.0) double liveLoad,
    @Default('') String loadingTableId,
    @Default(0.0) double slabThickness,
    @Default(0.0) double compositeActionFactor,
    @Default(5.0) double shortSpan,
    @Default(12.0) double longSpan,
    @Default(2) int bays,
    @Default(500.0) double strZone,
    @Default(355.0) double fsy,
    @Default('') String mainBeamSection,
    @Default('') String secBeamSection,
    @Default('') String steelBeamSchemeId, //will be overriden  as soon as new instance created
    @Default('') String calsLog,
  }) = _SteelBeamSchemeData; 

  factory SteelBeamSchemeData.fromJson(Map<String, Object?> json) =>
      _$SteelBeamSchemeDataFromJson(json);
}
