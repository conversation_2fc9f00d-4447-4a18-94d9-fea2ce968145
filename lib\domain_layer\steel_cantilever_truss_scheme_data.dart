import 'package:freezed_annotation/freezed_annotation.dart';
// import 'package:flutter/foundation.dart';
part 'steel_cantilever_truss_scheme_data.freezed.dart';
part 'steel_cantilever_truss_scheme_data.g.dart';

@freezed
abstract class SteelCantileverTrussSchemeData with _$SteelCantileverTrussSchemeData {
  const SteelCantileverTrussSchemeData._();
  factory SteelCantileverTrussSchemeData({
    @Default('') String usage,
    @Default(150.0) double slabThickness,
    @Default(1.0) double span,
    @Default(1.0) double loadWidth,
    @Default(500.0) double strZone,
    @Default(355.0) double fsy,
    @Default(5.0) double unbracedLength,
    @Default('') String steelSection,
    @Default(0.0) double chordAxialCapacity,
    @Default(0.0) double leverArm,
    @Default(0.0) double momentCapacity,
    @Default('') String liveLoadDeflection,
    @Default('1') String id, //will be overriden as soon as new instance created
    @Default('') String calsLog,
    @Default('') String beamForce,
  }) = _SteelCantileverTrussSchemeData; 


  factory SteelCantileverTrussSchemeData.fromJson(Map<String, Object?> json) =>
      _$SteelCantileverTrussSchemeDataFromJson(json);
}

