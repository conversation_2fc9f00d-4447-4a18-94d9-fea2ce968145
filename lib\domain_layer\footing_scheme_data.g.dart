// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'footing_scheme_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_FootingSchemeData _$FootingSchemeDataFromJson(Map<String, dynamic> json) =>
    _FootingSchemeData(
      size: (json['size'] as num?)?.toDouble() ?? 5.0,
      strZone: (json['strZone'] as num?)?.toDouble() ?? 500.0,
      fcu: (json['fcu'] as num?)?.toDouble() ?? 45.0,
      cover: (json['cover'] as num?)?.toDouble() ?? 35.0,
      groundType: json['groundType'] as String? ?? 'Soil',
      soilNValue: (json['soilNValue'] as num?)?.toDouble() ?? 50,
      footingTopLevel: (json['footingTopLevel'] as num?)?.toDouble() ?? 0,
      waterTableLevel: (json['waterTableLevel'] as num?)?.toDouble() ?? -3,
      rockCapacity: (json['rockCapacity'] as num?)?.toDouble() ?? 1000,
      colShape: json['colShape'] as String? ?? 'Sqaure',
      columnSize: (json['columnSize'] as num?)?.toDouble() ?? 1000.0,
      slsLoad: (json['slsLoad'] as num?)?.toDouble() ?? 2000.0,
      ulsLoad: (json['ulsLoad'] as num?)?.toDouble() ?? 3000.0,
      mainTopBar: json['mainTopBar'] as String? ?? '',
      mainBottomBar: json['mainBottomBar'] as String? ?? '',
      mainLinks: json['mainLinks'] as String? ?? '',
      punchingLinks: json['punchingLinks'] as String? ?? '',
      calsLog: json['calsLog'] as String? ?? '',
      isSelected: json['isSelected'] as bool? ?? false,
      footingSchemeDataId: json['footingSchemeDataId'] as String? ?? '1',
    );

Map<String, dynamic> _$FootingSchemeDataToJson(_FootingSchemeData instance) =>
    <String, dynamic>{
      'size': instance.size,
      'strZone': instance.strZone,
      'fcu': instance.fcu,
      'cover': instance.cover,
      'groundType': instance.groundType,
      'soilNValue': instance.soilNValue,
      'footingTopLevel': instance.footingTopLevel,
      'waterTableLevel': instance.waterTableLevel,
      'rockCapacity': instance.rockCapacity,
      'colShape': instance.colShape,
      'columnSize': instance.columnSize,
      'slsLoad': instance.slsLoad,
      'ulsLoad': instance.ulsLoad,
      'mainTopBar': instance.mainTopBar,
      'mainBottomBar': instance.mainBottomBar,
      'mainLinks': instance.mainLinks,
      'punchingLinks': instance.punchingLinks,
      'calsLog': instance.calsLog,
      'isSelected': instance.isSelected,
      'footingSchemeDataId': instance.footingSchemeDataId,
    };
