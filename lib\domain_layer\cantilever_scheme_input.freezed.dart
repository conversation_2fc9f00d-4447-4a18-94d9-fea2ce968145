// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'cantilever_scheme_input.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$CantileverSchemeInput {

 double get pointLoad; double get distA; bool get loadFromSelectedCol; String get cantileverSchemeInputId;
/// Create a copy of CantileverSchemeInput
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$CantileverSchemeInputCopyWith<CantileverSchemeInput> get copyWith => _$CantileverSchemeInputCopyWithImpl<CantileverSchemeInput>(this as CantileverSchemeInput, _$identity);

  /// Serializes this CantileverSchemeInput to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is CantileverSchemeInput&&(identical(other.pointLoad, pointLoad) || other.pointLoad == pointLoad)&&(identical(other.distA, distA) || other.distA == distA)&&(identical(other.loadFromSelectedCol, loadFromSelectedCol) || other.loadFromSelectedCol == loadFromSelectedCol)&&(identical(other.cantileverSchemeInputId, cantileverSchemeInputId) || other.cantileverSchemeInputId == cantileverSchemeInputId));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,pointLoad,distA,loadFromSelectedCol,cantileverSchemeInputId);

@override
String toString() {
  return 'CantileverSchemeInput(pointLoad: $pointLoad, distA: $distA, loadFromSelectedCol: $loadFromSelectedCol, cantileverSchemeInputId: $cantileverSchemeInputId)';
}


}

/// @nodoc
abstract mixin class $CantileverSchemeInputCopyWith<$Res>  {
  factory $CantileverSchemeInputCopyWith(CantileverSchemeInput value, $Res Function(CantileverSchemeInput) _then) = _$CantileverSchemeInputCopyWithImpl;
@useResult
$Res call({
 double pointLoad, double distA, bool loadFromSelectedCol, String cantileverSchemeInputId
});




}
/// @nodoc
class _$CantileverSchemeInputCopyWithImpl<$Res>
    implements $CantileverSchemeInputCopyWith<$Res> {
  _$CantileverSchemeInputCopyWithImpl(this._self, this._then);

  final CantileverSchemeInput _self;
  final $Res Function(CantileverSchemeInput) _then;

/// Create a copy of CantileverSchemeInput
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? pointLoad = null,Object? distA = null,Object? loadFromSelectedCol = null,Object? cantileverSchemeInputId = null,}) {
  return _then(_self.copyWith(
pointLoad: null == pointLoad ? _self.pointLoad : pointLoad // ignore: cast_nullable_to_non_nullable
as double,distA: null == distA ? _self.distA : distA // ignore: cast_nullable_to_non_nullable
as double,loadFromSelectedCol: null == loadFromSelectedCol ? _self.loadFromSelectedCol : loadFromSelectedCol // ignore: cast_nullable_to_non_nullable
as bool,cantileverSchemeInputId: null == cantileverSchemeInputId ? _self.cantileverSchemeInputId : cantileverSchemeInputId // ignore: cast_nullable_to_non_nullable
as String,
  ));
}

}


/// Adds pattern-matching-related methods to [CantileverSchemeInput].
extension CantileverSchemeInputPatterns on CantileverSchemeInput {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _CantileverSchemeInput value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _CantileverSchemeInput() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _CantileverSchemeInput value)  $default,){
final _that = this;
switch (_that) {
case _CantileverSchemeInput():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _CantileverSchemeInput value)?  $default,){
final _that = this;
switch (_that) {
case _CantileverSchemeInput() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( double pointLoad,  double distA,  bool loadFromSelectedCol,  String cantileverSchemeInputId)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _CantileverSchemeInput() when $default != null:
return $default(_that.pointLoad,_that.distA,_that.loadFromSelectedCol,_that.cantileverSchemeInputId);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( double pointLoad,  double distA,  bool loadFromSelectedCol,  String cantileverSchemeInputId)  $default,) {final _that = this;
switch (_that) {
case _CantileverSchemeInput():
return $default(_that.pointLoad,_that.distA,_that.loadFromSelectedCol,_that.cantileverSchemeInputId);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( double pointLoad,  double distA,  bool loadFromSelectedCol,  String cantileverSchemeInputId)?  $default,) {final _that = this;
switch (_that) {
case _CantileverSchemeInput() when $default != null:
return $default(_that.pointLoad,_that.distA,_that.loadFromSelectedCol,_that.cantileverSchemeInputId);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _CantileverSchemeInput extends CantileverSchemeInput {
   _CantileverSchemeInput({this.pointLoad = 0.0, this.distA = 3.0, this.loadFromSelectedCol = false, this.cantileverSchemeInputId = ''}): super._();
  factory _CantileverSchemeInput.fromJson(Map<String, dynamic> json) => _$CantileverSchemeInputFromJson(json);

@override@JsonKey() final  double pointLoad;
@override@JsonKey() final  double distA;
@override@JsonKey() final  bool loadFromSelectedCol;
@override@JsonKey() final  String cantileverSchemeInputId;

/// Create a copy of CantileverSchemeInput
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$CantileverSchemeInputCopyWith<_CantileverSchemeInput> get copyWith => __$CantileverSchemeInputCopyWithImpl<_CantileverSchemeInput>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$CantileverSchemeInputToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _CantileverSchemeInput&&(identical(other.pointLoad, pointLoad) || other.pointLoad == pointLoad)&&(identical(other.distA, distA) || other.distA == distA)&&(identical(other.loadFromSelectedCol, loadFromSelectedCol) || other.loadFromSelectedCol == loadFromSelectedCol)&&(identical(other.cantileverSchemeInputId, cantileverSchemeInputId) || other.cantileverSchemeInputId == cantileverSchemeInputId));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,pointLoad,distA,loadFromSelectedCol,cantileverSchemeInputId);

@override
String toString() {
  return 'CantileverSchemeInput(pointLoad: $pointLoad, distA: $distA, loadFromSelectedCol: $loadFromSelectedCol, cantileverSchemeInputId: $cantileverSchemeInputId)';
}


}

/// @nodoc
abstract mixin class _$CantileverSchemeInputCopyWith<$Res> implements $CantileverSchemeInputCopyWith<$Res> {
  factory _$CantileverSchemeInputCopyWith(_CantileverSchemeInput value, $Res Function(_CantileverSchemeInput) _then) = __$CantileverSchemeInputCopyWithImpl;
@override @useResult
$Res call({
 double pointLoad, double distA, bool loadFromSelectedCol, String cantileverSchemeInputId
});




}
/// @nodoc
class __$CantileverSchemeInputCopyWithImpl<$Res>
    implements _$CantileverSchemeInputCopyWith<$Res> {
  __$CantileverSchemeInputCopyWithImpl(this._self, this._then);

  final _CantileverSchemeInput _self;
  final $Res Function(_CantileverSchemeInput) _then;

/// Create a copy of CantileverSchemeInput
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? pointLoad = null,Object? distA = null,Object? loadFromSelectedCol = null,Object? cantileverSchemeInputId = null,}) {
  return _then(_CantileverSchemeInput(
pointLoad: null == pointLoad ? _self.pointLoad : pointLoad // ignore: cast_nullable_to_non_nullable
as double,distA: null == distA ? _self.distA : distA // ignore: cast_nullable_to_non_nullable
as double,loadFromSelectedCol: null == loadFromSelectedCol ? _self.loadFromSelectedCol : loadFromSelectedCol // ignore: cast_nullable_to_non_nullable
as bool,cantileverSchemeInputId: null == cantileverSchemeInputId ? _self.cantileverSchemeInputId : cantileverSchemeInputId // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

// dart format on
