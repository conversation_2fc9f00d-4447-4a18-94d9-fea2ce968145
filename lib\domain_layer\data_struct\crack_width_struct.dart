import 'package:freezed_annotation/freezed_annotation.dart';
// import 'package:flutter/foundation.dart';
part 'crack_width_struct.freezed.dart';
part 'crack_width_struct.g.dart';

@freezed
abstract class CrackWidth with _$CrackWidth {
  const CrackWidth._();
  factory CrackWidth({
    @Default(0.0) double M,
    @Default(0.0) double h,
    @Default(0.0) double b,
    @Default(0.0) double d,
    @Default(0.0) double fcu,
    @Default(0.0) double As,
    @Default(0.0) double x,
    @Default(0.0) double c_min,
    @Default(0) int dia_l,
    @Default(200000.0) double Es,
    @Default(0.0) double a_cr,
    @Default(0.0) double fcc,
    @Default(0.0) double fst,
    @Default(0.0) double a_pi,
    @Default(0.0) double e_1, 
    @Default(0.0) double e_m,
    @Default(0.0) double crackWidth,
  }) = _CrackWidth;

  factory CrackWidth.fromJson(Map<String, Object?> json) =>
      _$CrackWidthFromJson(json);
}
