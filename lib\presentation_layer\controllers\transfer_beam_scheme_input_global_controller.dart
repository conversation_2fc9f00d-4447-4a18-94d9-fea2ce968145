import 'package:riverpod_annotation/riverpod_annotation.dart';

//presentation layer
import '../../domain_layer/slab_scheme_data.dart';
import '../../domain_layer/transfer_beam_scheme_input_global.dart';
import '../screen/homescreen.dart';

// domain layer

part 'transfer_beam_scheme_input_global_controller.g.dart';

@riverpod
class TransferBeamSchemeInputGlobalController
    extends _$TransferBeamSchemeInputGlobalController {
  @override
  FutureOr<TransferBeamSchemeInputGlobal> build() async {
    // print('Build: Transfer Beam Input Global');
    TransferBeamSchemeInputGlobal transferBeamSchemeInputGlobal =
        await ref
            .read(appDatabaseControllerProvider.notifier)
            .queryTransferBeamSchemeInputGlobal();
    final data1 = ref.watch(loadingTablesControllerProvider);
    final data2 = ref.watch(slabSchemeDataControllerProvider);
    return data1.when(
      data: (loadingTables) async {
        return data2.when(
          data: (slabData) async {
            //* Validate loading usage
            final usages = loadingTables.map((x) => x.usage).toList();
            if (transferBeamSchemeInputGlobal.usage == '' ||
                !usages.contains(transferBeamSchemeInputGlobal.usage)) {
              transferBeamSchemeInputGlobal = transferBeamSchemeInputGlobal
                  .copyWith(usage: usages.first);
            }

            //*Validate slab thickness
            if (transferBeamSchemeInputGlobal.useSlabSelected) {
              final selectedSlab = slabData.firstWhere(
                (scheme) => scheme.isSelected,
                orElse: () => SlabSchemeData(),
              );
              transferBeamSchemeInputGlobal = transferBeamSchemeInputGlobal
                  .copyWith(slabThickness: selectedSlab.strZone);
            }
            return transferBeamSchemeInputGlobal;
          },
          error: (error, stackTrace) => TransferBeamSchemeInputGlobal(),
          loading: () => TransferBeamSchemeInputGlobal(),
        );
      },
      error: (error, stackTrace) => TransferBeamSchemeInputGlobal(),
      loading: () => TransferBeamSchemeInputGlobal(),
    );

    // final data = ref.watch(loadingTablesControllerProvider);
    // return data.when(
    //   data: (loadingTables) async {
    //     final usages = loadingTables.map((x) => x.usage).toList();
    //     if (!usages.contains(transferBeamSchemeInput.usage)) {
    //       // if input's usage is deleted from loading table,
    //       // update input's usage to first usage then return again
    //       // to avoid error in its UI counterpart: usage dropdownlist

    //       // await updateTable(usage: usages[0]); // this won't work as infinite waiting results
    //       transferBeamSchemeInput = transferBeamSchemeInput.copyWith(usage: usages[0]);
    //     }
    //     return transferBeamSchemeInput;
    //   },
    //   error: (error, stackTrace) => TransferBeamSchemeInputGlobal(),
    //   loading: () => TransferBeamSchemeInputGlobal(),
    // );
  }

  Future<void> updateTable({
    String? id,
    double? span,
    double? loadWidth,
    double? strZone,
    double? fcu,
    double? cover,
    double? mainKValue,
    double? mainSteelRatio,
    int? minS,
    int? maxS,
    double? maxWidth,
    int? maxLayers,
    String? usage,
    double? slabThickness,
    bool? useSlabSelected,
  }) async {
    final x = await future;
    // Create a list of updates
    final newState = x.copyWith(
      id: id ?? x.id,
      span: span ?? x.span,
      loadWidth: loadWidth ?? x.loadWidth,
      strZone: strZone ?? x.strZone,
      fcu: fcu ?? x.fcu,
      cover: cover ?? x.cover,
      mainKValue: mainKValue ?? x.mainKValue,
      mainSteelRatio: mainSteelRatio ?? x.mainSteelRatio,
      minS: minS ?? x.minS,
      maxS: maxS ?? x.maxS,
      maxWidth: maxWidth ?? x.maxWidth,
      maxLayers: maxLayers ?? x.maxLayers,
      usage: usage ?? x.usage,
      slabThickness: slabThickness ?? x.slabThickness,
      useSlabSelected: useSlabSelected ?? x.useSlabSelected,
    );
    state = AsyncData(newState);
  }
}
