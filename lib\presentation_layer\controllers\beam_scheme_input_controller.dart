import 'package:riverpod_annotation/riverpod_annotation.dart';

//presentation layer
import '../../domain_layer/slab_scheme_data.dart';
import '../screen/homescreen.dart';

// domain layer
import '../../domain_layer/beam_scheme_input.dart';

part 'beam_scheme_input_controller.g.dart';

@riverpod
class BeamSchemeInputController extends _$BeamSchemeInputController {
  @override
  FutureOr<BeamSchemeInput> build() async {
    // print('Build: Beam Scheme Input');
    BeamSchemeInput beamSchemeInput =
        await ref
            .read(appDatabaseControllerProvider.notifier)
            .queryBeamSchemeInput();
    // return beamSchemeInput;
    final data1 = ref.watch(loadingTablesControllerProvider);
    final data2 = ref.watch(slabSchemeDataControllerProvider);
    return data1.when(
      data: (loadingTables)  {
        return data2.when(
          data: (slabData)  {
            //* Check if input use loading not exist in database
            final usages = loadingTables.map((x) => x.usage).toList();
            if (!usages.contains(beamSchemeInput.usage)) {
              beamSchemeInput = beamSchemeInput.copyWith(usage: usages[0]);
            }
            
            //* Check if selected slab thick used
            final selectedSlab = slabData.firstWhere(
              (scheme) => scheme.isSelected,
              orElse: () => SlabSchemeData(strZone: 0.0),
            );
            if (beamSchemeInput.useSlabSelected) {
              beamSchemeInput = beamSchemeInput.copyWith(
                slabThickness: selectedSlab.strZone,
              );
            }
            return beamSchemeInput;
          },
          error: (error, stackTrace) => BeamSchemeInput(),
          loading: () => BeamSchemeInput(),
        );
      },
      error: (error, stackTrace) => BeamSchemeInput(),
      loading: () => BeamSchemeInput(),
    );
  }

  Future<void> updateTable({
    String? id,
    double? shortSpan,
    double? longSpan,
    int? bays,
    double? mainStrZone,
    double? secStrZone,
    double? fcu,
    double? cover,
    double? mainKValue,
    double? mainSteelRatio,
    double? secKValue,
    double? secSteelRatio,
    int? minS,
    int? maxS,
    double? maxWidth,
    int? maxLayers,
    double? shortSpanIncreament,
    double? longSpanIncreament,
    int? baysIncreament,
    int? iterationSteps,
    String? usage,
    double? slabThickness,
    bool? useSlabSelected,
  }) async {
    final x = await future;
    // Create a list of updates
    final newState = x.copyWith(
      id: id ?? x.id,
      shortSpan: shortSpan ?? x.shortSpan,
      longSpan: longSpan ?? x.longSpan,
      bays: bays ?? x.bays,
      mainStrZone: mainStrZone ?? x.mainStrZone,
      secStrZone: secStrZone ?? x.secStrZone,
      fcu: fcu ?? x.fcu,
      cover: cover ?? x.cover,
      mainKValue: mainKValue ?? x.mainKValue,
      mainSteelRatio: mainSteelRatio ?? x.mainSteelRatio,
      secKValue: secKValue ?? x.secKValue,
      secSteelRatio: secSteelRatio ?? x.secSteelRatio,
      minS: minS ?? x.minS,
      maxS: maxS ?? x.maxS,
      maxWidth: maxWidth ?? x.maxWidth,
      maxLayers: maxLayers ?? x.maxLayers,
      shortSpanIncreament: shortSpanIncreament ?? x.shortSpanIncreament,
      longSpanIncreament: longSpanIncreament ?? x.longSpanIncreament,
      baysIncreament: baysIncreament ?? x.baysIncreament,
      iterationSteps: iterationSteps ?? x.iterationSteps,
      usage: usage ?? x.usage,
      slabThickness: slabThickness ?? x.slabThickness,
      useSlabSelected: useSlabSelected ?? x.useSlabSelected,
    );

    // Update the state only if there are changes
    state = AsyncData(newState);
  }
}
