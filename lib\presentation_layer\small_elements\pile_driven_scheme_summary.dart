// import 'dart:nativewrappers/_internal/vm/lib/math_patch.dart';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:structify/domain_layer/mixin/mixin_steel_str.dart';
import 'package:structify/domain_layer/pile_driven_input.dart';

//below for printing
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:printing/printing.dart';
// import 'dart:typed_data';
// import 'dart:ui';

//domain layer
import '../../domain_layer/global_data.dart';
import '../../domain_layer/pile_driven_data.dart';
import '../../domain_layer/preferences.dart';

// presentation layer
import '../screen/homescreen.dart';
// import '../small_elements/custom_stateful_double_input.dart';
// import '../small_elements/custom_stateful_text_input.dart';
// import '../small_elements/global_data_ui.dart';

import 'sketch/draw_pile_driven_scheme.dart';
import 'button/function_button.dart';
import 'button/selection_button.dart';

class PileDrivenSchemeSummary extends ConsumerStatefulWidget {
  const PileDrivenSchemeSummary({
    this.isExpanded,
    this.maxHeight,
    this.minHeight,
    super.key,
  });

  final bool? isExpanded;
  final double? maxHeight;
  final double? minHeight;

  @override
  ConsumerState<ConsumerStatefulWidget> createState() =>
      _PileDrivenSchemeSummaryState();
}

class _PileDrivenSchemeSummaryState
    extends ConsumerState<PileDrivenSchemeSummary>
    with WidgetsBindingObserver, SteelStrHK {
  late bool _isExpanded;
  late double _maxHeight;
  late double _minHeight;
  late ScrollController _scrollController;

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void initState() {
    WidgetsBinding.instance.addObserver(this);
    _isExpanded = widget.isExpanded ?? false;
    _maxHeight = widget.maxHeight ?? 300;
    _minHeight = widget.minHeight ?? 50;
    _scrollController = ScrollController();
    // _updateHeights();

    super.initState();
  }

  @override
  void didChangeDependencies() {
    _updateHeights();
    super.didChangeDependencies();
    // Update heights when dependencies change, including after initState
  }

  @override
  void didChangeMetrics() {
    // This method is called when the metrics change (like resizing the window)
    setState(() {
      _updateHeights();
    });
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    final loadingTables = ref.watch(loadingTablesControllerProvider);
    final globalData = ref.watch(globalDataControllerProvider);
    final pileDrivenInput = ref.watch(pileDrivenInputControllerProvider);

    final pileDrivenSchemes = ref.watch(pileDrivenDataControllerProvider);

    return loadingTables.when(
      data: (tables) {
        //start reutnr real stuffs when loading tables available
        return globalData.when(
          //also, start reutnr real stuffs when global data tables available
          data: (data) {
            return pileDrivenInput.when(
              data: (inputs) {
                return pileDrivenSchemes.when(
                  data: (schemes) {
                    late int indexMinScheme;
                    return Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Padding(
                          padding: const EdgeInsets.fromLTRB(10, 0, 10, 0),
                          child: Divider(),
                        ),
                        Padding(
                          padding: const EdgeInsets.fromLTRB(10, 0, 0, 0),
                          child: Align(
                            alignment: Alignment.centerLeft,
                            child: Wrap(
                              crossAxisAlignment: WrapCrossAlignment.center,
                              children: [
                                IconButton(
                                  icon: Icon(
                                    _isExpanded
                                        ? Icons.expand_less
                                        : Icons.expand_more,
                                  ),
                                  color: colorScheme.onSurface,
                                  onPressed: () {
                                    setState(() {
                                      _isExpanded = !_isExpanded;
                                    });
                                  },
                                ),
                                Text(
                                  'Driven Steel H Pile Scheme Summary ',
                                  style: textTheme.titleLarge!.copyWith(
                                    color: colorScheme.onSurface,
                                  ),
                                ),
                                Container(
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(5.0),
                                    color: colorScheme.surfaceContainer
                                        .withAlpha(225),
                                    border: Border.all(
                                      color: Colors.black.withAlpha(125),
                                    ),
                                  ),
                                  child: Padding(
                                    padding: const EdgeInsets.all(2.0),
                                    child: Text(
                                      '(Total: ${schemes.length})',
                                      style: textTheme.labelMedium!.copyWith(
                                        color: colorScheme.onSurfaceVariant
                                            .withAlpha(225),
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                        Padding(
                          padding: const EdgeInsets.fromLTRB(10, 0, 10, 0),
                          child: Divider(),
                        ),

                        ClipRect(
                          child: ConstrainedBox(
                            constraints: BoxConstraints(
                              maxHeight: _isExpanded ? _maxHeight : 0,
                            ),
                            child: Column(
                              children: [
                                Builder(
                                  builder: (context) {
                                    if (schemes.isEmpty) {
                                      return Align(
                                        child: Padding(
                                          padding: const EdgeInsets.fromLTRB(
                                            10,
                                            0,
                                            10,
                                            0,
                                          ),
                                          child: Container(
                                            decoration: BoxDecoration(
                                              borderRadius:
                                                  BorderRadius.circular(5.0),
                                              color: colorScheme.errorContainer
                                                  .withAlpha(100),
                                              border: Border.all(
                                                width: 0.5,
                                                color: colorScheme.error
                                                    .withAlpha(100),
                                              ),
                                            ),
                                            child: Padding(
                                              padding: const EdgeInsets.all(
                                                4.0,
                                              ),
                                              child: Text(
                                                'No Pile Scheme Data',
                                                style: textTheme.labelMedium!
                                                    .copyWith(
                                                      color: colorScheme.error,
                                                    ),
                                              ),
                                            ),
                                          ),
                                        ),
                                      );
                                    } else {
                                      return SizedBox(height: 0, width: 0);
                                    }
                                  },
                                ),
                                Flexible(
                                  child: Scrollbar(
                                    controller: _scrollController,
                                    thumbVisibility: true,
                                    child: DefaultTextStyle(
                                      style: textTheme.labelSmall!.copyWith(
                                        color: colorScheme.onSurface,
                                      ),
                                      child: ListView.builder(
                                        controller: _scrollController,
                                        itemCount: schemes.length,
                                        itemBuilder: (context, index) {
                                          late List<String> unit;
                                          switch (data.unit) {
                                            case 'metrics':
                                              unit = PreferredUnit.metrics;
                                              break;
                                            case 'imperial':
                                              unit = PreferredUnit.imperial;
                                              break;
                                            default:
                                              unit = PreferredUnit.metrics;
                                          }

                                          return Padding(
                                            padding: const EdgeInsets.fromLTRB(
                                              8.0,
                                              4.0,
                                              8.0,
                                              4.0,
                                            ),
                                            child: Row(
                                              children: [
                                                Column(
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.center,
                                                  children: [
                                                    FunctionButton(
                                                      labelText:
                                                          ' ${index + 1} ',
                                                      pressedColor: colorScheme
                                                          .tertiary
                                                          .withAlpha(150),
                                                      onTap: (value) async {
                                                        final schemes =
                                                            await ref.read(
                                                              pileDrivenDataControllerProvider
                                                                  .future,
                                                            );
                                                        await ref
                                                            .read(
                                                              pileDrivenDataControllerProvider
                                                                  .notifier,
                                                            )
                                                            .toggleSelectScheme(
                                                              schemes[index]
                                                                  .pileDrivenSchemeId,
                                                            );
                                                      },
                                                    ),
                                                    SizedBox(height: 5.0),
                                                    SelectionButton(
                                                      labelIcon: Icon(
                                                        Icons.check,
                                                        color:
                                                            schemes[index]
                                                                    .isSelected
                                                                ? colorScheme
                                                                    .onTertiary
                                                                : colorScheme
                                                                    .onSurface
                                                                    .withAlpha(
                                                                      100,
                                                                    ),
                                                      ),
                                                      labelText: '',
                                                      // pressedColor:
                                                      //     colorScheme.tertiary,
                                                      bgColor:
                                                          schemes[index]
                                                                  .isSelected
                                                              ? colorScheme
                                                                  .tertiary
                                                              : colorScheme
                                                                  .surfaceContainer
                                                                  .withAlpha(
                                                                    100,
                                                                  ),

                                                      onTap: (value) async {
                                                        final schemes =
                                                            await ref.read(
                                                              pileDrivenDataControllerProvider
                                                                  .future,
                                                            );
                                                        await ref
                                                            .read(
                                                              pileDrivenDataControllerProvider
                                                                  .notifier,
                                                            )
                                                            .toggleSelectScheme(
                                                              schemes[index]
                                                                  .pileDrivenSchemeId,
                                                            );
                                                      },
                                                    ),
                                                    SizedBox(height: 5.0),
                                                    FunctionButton(
                                                      labelText: 'Show Cals',
                                                      pressedColor: colorScheme
                                                          .tertiary
                                                          .withAlpha(150),
                                                      onTap: (value) async {
                                                        final globalData =
                                                            await ref.read(
                                                              globalDataControllerProvider
                                                                  .future,
                                                            );
                                                        _presentCalsRecord(
                                                          globalData,
                                                          unit,
                                                          inputs,
                                                          schemes,
                                                          index,
                                                        );
                                                      },
                                                    ),
                                                  ],
                                                ),
                                                IconButton(
                                                  icon: Icon(
                                                    Icons.delete,
                                                    color:
                                                        colorScheme.onSurface,
                                                  ),
                                                  onPressed: () {
                                                    ref
                                                        .read(
                                                          pileDrivenDataControllerProvider
                                                              .notifier,
                                                        )
                                                        .deleteTable(
                                                          schemes[index]
                                                              .pileDrivenSchemeId,
                                                        );
                                                  },
                                                ),
                                                Flexible(
                                                  child: Row(
                                                    mainAxisAlignment:
                                                        MainAxisAlignment.start,
                                                    mainAxisSize:
                                                        MainAxisSize.min,
                                                    children: [
                                                      SizedBox(width: 15.0),
                                                      Column(
                                                        mainAxisSize:
                                                            MainAxisSize.min,
                                                        crossAxisAlignment:
                                                            CrossAxisAlignment
                                                                .start,
                                                        children: [
                                                          Container(
                                                            decoration: BoxDecoration(
                                                              borderRadius:
                                                                  BorderRadius.circular(
                                                                    5.0,
                                                                  ),
                                                              color: colorScheme
                                                                  .secondaryContainer
                                                                  .withAlpha(
                                                                    200,
                                                                  ),
                                                            ),
                                                            child: Padding(
                                                              padding:
                                                                  const EdgeInsets.all(
                                                                    3.0,
                                                                  ),
                                                              child: Text(
                                                                'Section: ${schemes[index].section}',
                                                                style: textTheme
                                                                    .labelMedium!
                                                                    .copyWith(
                                                                      color:
                                                                          colorScheme
                                                                              .onSecondaryContainer,
                                                                    ),
                                                              ),
                                                            ),
                                                          ),
                                                          SizedBox(height: 5.0),
                                                          Container(
                                                            decoration: BoxDecoration(
                                                              borderRadius:
                                                                  BorderRadius.circular(
                                                                    5.0,
                                                                  ),
                                                              color: colorScheme
                                                                  .secondaryContainer
                                                                  .withAlpha(
                                                                    200,
                                                                  ),
                                                            ),
                                                            child: Padding(
                                                              padding:
                                                                  const EdgeInsets.all(
                                                                    3.0,
                                                                  ),
                                                              child: Text(
                                                                'Length: ${NumberFormat('0').format(schemes[index].length)} [${unit[3]}]',
                                                                style: textTheme
                                                                    .labelMedium!
                                                                    .copyWith(
                                                                      color:
                                                                          colorScheme
                                                                              .onSecondaryContainer,
                                                                    ),
                                                              ),
                                                            ),
                                                          ),
                                                          SizedBox(height: 5.0),
                                                          Container(
                                                            decoration: BoxDecoration(
                                                              borderRadius:
                                                                  BorderRadius.circular(
                                                                    5.0,
                                                                  ),
                                                              color: colorScheme
                                                                  .secondaryContainer
                                                                  .withAlpha(
                                                                    200,
                                                                  ),
                                                            ),
                                                            child: Padding(
                                                              padding:
                                                                  const EdgeInsets.all(
                                                                    3.0,
                                                                  ),
                                                              child: Text(
                                                                'Fy: ${460} [${unit[5]}]',
                                                                style: textTheme
                                                                    .labelMedium!
                                                                    .copyWith(
                                                                      color:
                                                                          colorScheme
                                                                              .onSecondaryContainer,
                                                                    ),
                                                              ),
                                                            ),
                                                          ),
                                                        ],
                                                      ),
                                                      SizedBox(width: 15.0),
                                                      Column(
                                                        mainAxisSize:
                                                            MainAxisSize.min,
                                                        crossAxisAlignment:
                                                            CrossAxisAlignment
                                                                .start,
                                                        children: [
                                                          Text(
                                                            'SPT N-Value: ${NumberFormat('0').format(schemes[index].sptNValue)}',
                                                          ),
                                                          Text(
                                                            'SLS Load: ${NumberFormat('0').format(schemes[index].slsLoad)} [${unit[0]}]',
                                                          ),
                                                          Text(
                                                            'ULS Load: ${NumberFormat('0').format(schemes[index].ulsLoad)} [${unit[0]}]',
                                                          ),
                                                          Text(
                                                            'Ground Capacity: ${NumberFormat('0').format(schemes[index].totalGroundResistance)} [${unit[0]}]',
                                                          ),
                                                          Text(
                                                            'Str Capacity: ${NumberFormat('0').format(schemes[index].strULSCapacity)} [${unit[0]}]',
                                                          ),
                                                        ],
                                                      ),
                                                      SizedBox(width: 5.0),
                                                      GestureDetector(
                                                        onTap: () async {
                                                          await showDialog(
                                                            context: context,
                                                            builder: (context) {
                                                              return Dialog(
                                                                backgroundColor:
                                                                    colorScheme
                                                                        .surfaceContainer,
                                                                child: SizedBox(
                                                                  width: 550,
                                                                  height: 550,
                                                                  child: DrawPileDrivenScheme(
                                                                    sketchWidth:
                                                                        500,
                                                                    sketchHeight:
                                                                        500,
                                                                    index:
                                                                        index,
                                                                  ),
                                                                ),
                                                              );
                                                            },
                                                          );
                                                        },
                                                        child:
                                                            DrawPileDrivenScheme(
                                                              sketchWidth: 125,
                                                              sketchHeight: 125,
                                                              index: index,
                                                            ),
                                                      ),
                                                      SizedBox(width: 5.0),
                                                    ],
                                                  ),
                                                ),
                                                SizedBox(width: 5.0),
                                              ],
                                            ),
                                          );
                                        },
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    );
                  },
                  error: (error, stackTrace) => Text(error.toString()),
                  loading: () => CircularProgressIndicator(),
                );
              },
              error: (error, stackTrace) => Text(error.toString()),
              loading: () => CircularProgressIndicator(),
            );
          },

          error: (error, stackTrace) => Text(error.toString()),
          loading: () => CircularProgressIndicator(),
        );
      },
      error: (error, stackTrace) => Text(error.toString()),
      loading: () => CircularProgressIndicator(),
    );
  }

  // Method to update heights based on the current media query
  void _updateHeights() {
    final double screenHeight = MediaQuery.of(context).size.height;
    _maxHeight =
        (widget.maxHeight == null)
            ? screenHeight * 0.6
            : (widget.maxHeight! > screenHeight * 0.6)
            ? screenHeight * 0.6
            : widget.maxHeight!;

    _minHeight =
        (widget.minHeight == null)
            ? screenHeight * 0.1
            : (widget.minHeight! > screenHeight * 0.1)
            ? screenHeight * 0.1
            : widget.minHeight!;

    // Adjust _maxHeight if needed

    if (_maxHeight < _minHeight) {
      _maxHeight = _minHeight;
    }
  }

  Future<void> _presentCalsRecord(
    GlobalData globalData,
    List<String> unit,
    PileDrivenInput input,
    List<PileDrivenData> schemes,
    int index,
  ) async {
    late List<String> unit;
    switch (globalData.unit) {
      case 'metrics':
        unit = PreferredUnit.metrics;
        break;
      case 'imperial':
        unit = PreferredUnit.imperial;
        break;
      default:
        unit = PreferredUnit.metrics;
    }
    const double maxH = 600;
    const double maxW = 600;
    final StringBuffer pileCals = await _writePileResult(
      globalData,
      schemes,
      input,
      index,
      unit,
    );

    await showDialog(
      context: context,
      builder: (context) {
        ColorScheme colorScheme = Theme.of(context).colorScheme;
        TextTheme textTheme = Theme.of(context).textTheme;
        Color bgColor = colorScheme.surfaceContainer;
        Color onBgColor = colorScheme.onSurface;
        Color titleBgColor = colorScheme.primary.withAlpha(150);
        Color titleOnBgColor = colorScheme.onPrimary;
        TextStyle titleTextStyle = textTheme.labelSmall!.copyWith(
          color: titleOnBgColor,
        );
        ScrollController scrollController = ScrollController();

        NumberFormat f0 = NumberFormat('0');
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(15),
          ),
          backgroundColor: bgColor,
          child: ConstrainedBox(
            constraints: const BoxConstraints(maxWidth: maxW, maxHeight: maxH),
            child: Column(
              children: [
                Align(
                  alignment: Alignment.centerRight,
                  child: Padding(
                    padding: const EdgeInsets.fromLTRB(0, 10, 10, 0),
                    child: FunctionButton(
                      bgColor: titleBgColor,
                      labelTextColor: titleOnBgColor,
                      labelIcon: Icon(
                        Icons.print_outlined,
                        color: titleOnBgColor.withAlpha(175),
                      ),
                      labelText: '',
                      onTap: (value) {
                        _exportListToPdf(
                          context,
                          ref,
                          pileCals.toString().split('\n'),
                        );
                      },
                    ),
                  ),
                ),

                Scrollbar(
                  controller: scrollController,
                  thumbVisibility: true,
                  trackVisibility: false,
                  child: ConstrainedBox(
                    constraints: const BoxConstraints(
                      maxWidth: maxW,
                      maxHeight: maxH - 70,
                    ),
                    child: SingleChildScrollView(
                      controller: scrollController,
                      child: Padding(
                        padding: const EdgeInsets.fromLTRB(25.0, 5, 25.0, 5),
                        child: DefaultTextStyle(
                          style: textTheme.labelMedium!.copyWith(
                            color: onBgColor,
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              Container(
                                decoration: BoxDecoration(
                                  color: colorScheme.tertiary,
                                  shape: BoxShape.circle,
                                  border: Border.all(color: titleOnBgColor),
                                ),
                                child: Padding(
                                  padding: const EdgeInsets.all(12.0),
                                  child: Text(
                                    '${index + 1}',
                                    style: titleTextStyle.copyWith(
                                      fontSize: 24,
                                      color: colorScheme.onTertiary,
                                    ),
                                  ),
                                ),
                              ),
                              Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Container(
                                    decoration: BoxDecoration(
                                      color: titleBgColor,
                                      shape: BoxShape.rectangle,
                                      borderRadius: BorderRadius.circular(10.0),
                                      border: Border.all(color: titleOnBgColor),
                                    ),
                                    child: Padding(
                                      padding: const EdgeInsets.all(4.0),
                                      child: Text(
                                        'SPT N Value :\n${f0.format(schemes[index].sptNValue)}',
                                        style: titleTextStyle,
                                      ),
                                    ),
                                  ),
                                  SizedBox(width: 5),
                                  Container(
                                    decoration: BoxDecoration(
                                      color: titleBgColor,
                                      shape: BoxShape.rectangle,
                                      borderRadius: BorderRadius.circular(10.0),
                                      border: Border.all(color: titleOnBgColor),
                                    ),
                                    child: Padding(
                                      padding: const EdgeInsets.all(4.0),
                                      child: Text(
                                        'Length:\n${f0.format(schemes[index].length)}',
                                        style: titleTextStyle,
                                      ),
                                    ),
                                  ),
                                  SizedBox(width: 5),
                                  Container(
                                    decoration: BoxDecoration(
                                      color: titleBgColor,
                                      shape: BoxShape.rectangle,
                                      borderRadius: BorderRadius.circular(10.0),
                                      border: Border.all(color: titleOnBgColor),
                                    ),
                                    child: Padding(
                                      padding: const EdgeInsets.all(4.0),
                                      child: Text(
                                        'Section:\n${schemes[index].section}',
                                        style: titleTextStyle,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                              SizedBox(height: 5),
                              Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Container(
                                    decoration: BoxDecoration(
                                      color: titleBgColor,
                                      shape: BoxShape.rectangle,
                                      borderRadius: BorderRadius.circular(10.0),
                                      border: Border.all(color: titleOnBgColor),
                                    ),
                                    child: Padding(
                                      padding: const EdgeInsets.all(4.0),
                                      child: Text(
                                        'SLS Load [${unit[0]}]:\n${f0.format(schemes[index].slsLoad)}',
                                        style: titleTextStyle,
                                      ),
                                    ),
                                  ),
                                  SizedBox(width: 5),
                                  Container(
                                    decoration: BoxDecoration(
                                      color: titleBgColor,
                                      shape: BoxShape.rectangle,
                                      borderRadius: BorderRadius.circular(10.0),
                                      border: Border.all(color: titleOnBgColor),
                                    ),
                                    child: Padding(
                                      padding: const EdgeInsets.all(4.0),
                                      child: Text(
                                        'ULS Load [${unit[0]}]:\n${f0.format(schemes[index].ulsLoad)}',
                                        style: titleTextStyle,
                                      ),
                                    ),
                                  ),
                                  SizedBox(width: 5),
                                  Container(
                                    decoration: BoxDecoration(
                                      color: titleBgColor,
                                      shape: BoxShape.rectangle,
                                      borderRadius: BorderRadius.circular(10.0),
                                      border: Border.all(color: titleOnBgColor),
                                    ),
                                    child: Padding(
                                      padding: const EdgeInsets.all(4.0),
                                      child: Text(
                                        ' Ground Capacity (SLS) [${unit[0]}]:\n${f0.format(schemes[index].totalGroundResistance)} ',
                                        style: titleTextStyle,
                                      ),
                                    ),
                                  ),
                                  SizedBox(width: 5),
                                  Container(
                                    decoration: BoxDecoration(
                                      color: titleBgColor,
                                      shape: BoxShape.rectangle,
                                      borderRadius: BorderRadius.circular(10.0),
                                      border: Border.all(color: titleOnBgColor),
                                    ),
                                    child: Padding(
                                      padding: const EdgeInsets.all(4.0),
                                      child: Text(
                                        'Str Capacity (ULS) [${unit[0]}]:\n${f0.format(schemes[index].strULSCapacity)}',
                                        style: titleTextStyle,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                              SizedBox(height: 5),
                              Container(
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(10.0),
                                  border: Border.all(color: onBgColor),
                                ),
                                child: Padding(
                                  padding: const EdgeInsets.all(8.0),
                                  child: Text(pileCals.toString()),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Future<StringBuffer> _writePileResult(
    GlobalData globalData,
    List<PileDrivenData> schemes,
    PileDrivenInput input,
    int index,
    List<String> unit,
  ) async {
    StringBuffer buffer = StringBuffer();
    PileDrivenData scheme = schemes[index];
    NumberFormat f0 = NumberFormat('0');
    List<Map<String, dynamic>> steelSections = await getAllSteelISection(
      sectionShape: 'UBP',
    );
    Map<String, dynamic> section = steelSections.firstWhere(
      (item) => item['name'] == scheme.section,
    );
    double f = 1.5, n, d, b, l, a, fsy;
    n = scheme.sptNValue;
    d = double.parse(section['d']) * 10;
    b = double.parse(section['b']) * 10;
    l = scheme.length;
    a = double.parse(section['A']); //[cm2]
    fsy = await getDesignStrength(scheme.section, 460, steelSections);
    buffer.write('*******************\n');
    buffer.write('Loading\n');
    buffer.write('*******************\n');

    buffer.write('SLS Total: ${f0.format(scheme.slsLoad)} [${unit[0]}]\n');
    buffer.write('ULS Total: ${f0.format(scheme.ulsLoad)} [${unit[0]}]\n');
    buffer.write('*******************\n');
    buffer.write('Parameters\n');
    buffer.write('*******************\n');
    buffer.write('SPT N Value, N: ${f0.format(scheme.sptNValue)}\n');

    buffer.write('FOS: ${f0.format(scheme.fos)}\n');

    buffer.write('*******************\n');
    buffer.write('Ground Capacity (SLS)\n');
    buffer.write('*******************\n');
    buffer.write('Shaft Capacity, Qs\n= f*N*Ap*L/FOS\n= ');
    buffer.write('$f* ${NumberFormat('0').format(n)}*');
    buffer.write(
      '(2*(${NumberFormat('0').format(d)}+${NumberFormat('0').format(b)})/1000)*',
    );
    buffer.write('(${NumberFormat('0').format(l)}/${f0.format(scheme.fos)})\n');
    buffer.write('= ${f0.format(scheme.shaftCapacity)} [${unit[0]}]\n');
    buffer.write('Structural SLS Capacity, Qstr\n= 0.3*Fy*A\n');
    buffer.write(
      '=0.3*${f0.format(fsy)}*${NumberFormat('0').format(a)}*100/1000\n',
    );
    buffer.write('= ${f0.format(scheme.strSLSCapacuity)} [${unit[0]}]\n');
    buffer.write(
      'Total Ground Resistance, min(Qs, Qstr)\n= ${f0.format(scheme.totalGroundResistance)} [${unit[0]}]\n',
    );
    if (scheme.totalGroundResistance > scheme.slsLoad) {
      buffer.write('> SLS Load (OK)\n');
    } else {
      buffer.write('< SLS Load (Fail)\n');
    }

    buffer.write('*******************\n');
    buffer.write('Structural Capacity (ULS)\n');
    buffer.write('*******************\n');
    buffer.write(
      'Pc\n= FyA\n= ${f0.format(fsy)}*${NumberFormat('0').format(a)}*100/1000\n',
    );
    buffer.write('= ${f0.format(scheme.strULSCapacity)} [${unit[0]}]\n');
    if (scheme.strULSCapacity > scheme.ulsLoad) {
      buffer.write('> ULS Load (OK)\n');
    } else {
      buffer.write('< ULS Load (Fail)\n');
    }
    return buffer;
  }

  // dynamic _toDouble(List<dynamic>? data) {
  //   if (data != null) {
  //     final x = double.tryParse(data[0]);
  //     if (x != null) {
  //       return x;
  //     } else {
  //       return 'null';
  //     }
  //   } else {
  //     return 'null';
  //   }
  // }

  // dynamic _toInt(List<dynamic>? data) {
  //   if (data != null) {
  //     final x = int.tryParse(data[0]);
  //     if (x != null) {
  //       return x;
  //     } else {
  //       return 'null';
  //     }
  //   } else {
  //     return 'null';
  //   }
  // }

  // dynamic _toString(List<dynamic>? data) {
  //   if (data != null) {
  //     final x = data[0];
  //     if (x != null) {
  //       return x;
  //     } else {
  //       return 'null';
  //     }
  //   } else {
  //     return 'null';
  //   }
  // }

  // String _getUnit(List<dynamic>? data) {
  //   if (data != null && data[1] != null) {
  //     return data[1];
  //   } else {
  //     return '[null]';
  //   }
  // }

  Future<void> _exportListToPdf(
    BuildContext context,
    WidgetRef ref,
    List<String> contents,
  ) async {
    final pw.ThemeData customTheme = pw.ThemeData.withFont(
      base: pw.Font.ttf(await rootBundle.load("assets/OpenSans-Regular.ttf")),
      bold: pw.Font.ttf(await rootBundle.load("assets/OpenSans-Bold.ttf")),
      italic: pw.Font.ttf(await rootBundle.load("assets/OpenSans-Italic.ttf")),
      boldItalic: pw.Font.ttf(
        await rootBundle.load("assets/OpenSans-BoldItalic.ttf"),
      ),
    );

    final pdf = pw.Document(theme: customTheme);
    final maxLines = 45;
    List<pw.Widget> pageBuffer = [];
    // TextStyle textStyle = Theme.of(context).textTheme.bodySmall!;

    for (int i = 0; i < contents.length; i++) {
      pageBuffer.add(pw.Text(contents[i]));

      //! note that the builder of .addPage is deferred callback
      //! it means it executes later when the pdf is actually printed out
      //! so we put in copy of pageBuffer to it everytime we .addPage

      if ((pageBuffer.length == maxLines) || (i == contents.length - 1)) {
        final pageBufferCopy = List<pw.Text>.from(pageBuffer);
        pdf.addPage(
          pw.Page(
            margin: pw.EdgeInsets.fromLTRB(25, 35, 25, 35),
            build: (pw.Context context) {
              // return pw.Center(
              //    child: pw.Text(
              //     'testing',
              //     style: pw.TextStyle(fontSize: textStyle.fontSize!),
              //    )
              // );
              return pw.Column(
                mainAxisAlignment: pw.MainAxisAlignment.start,
                crossAxisAlignment: pw.CrossAxisAlignment.start,
                children: pageBufferCopy,
              );
            },
          ),
        );
        pageBuffer.clear();
      }
    }

    await Printing.layoutPdf(
      onLayout: (PdfPageFormat format) async => pdf.save(),
    );
  }

  // Map<String, List<dynamic>> _extractCalsLog(String calsLog) {
  //   // 1. get the record first
  //   final record = calsLog;
  //   List<String> lines = record.split('\n');
  //   // Regex to match items
  //   RegExp titleRegExp = RegExp(r'([a-zA-Z_ ]*[:])');
  //   RegExp digitsRegExp = RegExp(r'([\d]+[\.]?[\d]?)');
  //   RegExp unitRegExp = RegExp(r'(\[(.+?)\])');
  //   RegExp textRegExp = RegExp(r'(?<=: )[\w ]*');

  //   Map<String, List<dynamic>> resultMap = {};

  //   for (String line in lines) {
  //     final List<String> newLines = line.split(r'|');
  //     for (String line in newLines) {
  //       final titleMatch = titleRegExp
  //           .firstMatch(line)
  //           ?.group(0)
  //           ?.trim()
  //           .replaceAll(':', '');
  //       final String? digitMatch =
  //           digitsRegExp.firstMatch(line)?.group(0)?.trim();
  //       String? unitMatch = unitRegExp.firstMatch(line)?.group(0);
  //       String? textMatch;

  //       //* capture numeric/text result
  //       if (titleMatch != null) {
  //         textMatch = textRegExp.firstMatch(line)?.group(0)?.trim();
  //         if (textMatch != null && textMatch.contains(RegExp(r'[a-zA-Z]'))) {
  //           resultMap[titleMatch] = [textMatch, unitMatch];
  //         } else {
  //           resultMap[titleMatch] = [digitMatch, unitMatch];
  //         }
  //         print(
  //           '${titleMatch}: ${resultMap[titleMatch]![0]} ${resultMap[titleMatch]![1]}',
  //         );
  //       }
  //     }
  //   }
  //   return resultMap;
  // }
}
