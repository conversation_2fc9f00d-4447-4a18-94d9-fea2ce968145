import 'dart:math';
// import 'dart:nativewrappers/_internal/vm/lib/math_patch.dart';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';

//below for printing
// import 'dart:typed_data';
// import 'dart:ui';
import 'package:structify/presentation_layer/small_elements/sketch/draw_steel_column_scheme.dart';

//domain layer
import '../../domain_layer/global_data.dart';
import '../../domain_layer/mixin/mixin_steel_str.dart';
import '../../domain_layer/mixin/mixin_tools_for_ui.dart';
import '../../domain_layer/preferences.dart';

// presentation layer
import '../../domain_layer/steel_column_scheme_data.dart';
import '../../domain_layer/steel_column_scheme_input.dart';
import '../../domain_layer/steel_column_scheme_input_global.dart';
import '../screen/homescreen.dart';
// import '../small_elements/custom_stateful_double_input.dart';
// import '../small_elements/custom_stateful_text_input.dart';
// import '../small_elements/global_data_ui.dart';

//misc

import 'button/function_button.dart';

class SteelColumnSchemeSummary extends ConsumerStatefulWidget {
  const SteelColumnSchemeSummary({
    this.isExpanded,
    this.maxHeight,
    this.minHeight,
    super.key,
  });

  final bool? isExpanded;
  final double? maxHeight;
  final double? minHeight;

  @override
  ConsumerState<ConsumerStatefulWidget> createState() =>
      _SteelColumnSchemeSummaryState();
}

class _SteelColumnSchemeSummaryState
    extends ConsumerState<SteelColumnSchemeSummary>
    with WidgetsBindingObserver, MixinToolsForUI , SteelStrHK {
  late bool _isExpanded;
  late double _maxHeight;
  late double _minHeight;
  late ScrollController _scrollController;

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void initState() {
    WidgetsBinding.instance.addObserver(this);
    _isExpanded = widget.isExpanded ?? false;
    _maxHeight = widget.maxHeight ?? 300;
    _minHeight = widget.minHeight ?? 50;
    _scrollController = ScrollController();
    // _updateHeights();

    super.initState();
  }

  @override
  void didChangeDependencies() {
    _updateHeights();
    super.didChangeDependencies();
    // Update heights when dependencies change, including after initState
  }

  @override
  void didChangeMetrics() {
    // This method is called when the metrics change (like resizing the window)
    setState(() {
      _updateHeights();
    });
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    final loadingTables = ref.watch(loadingTablesControllerProvider);
    final globalData = ref.watch(globalDataControllerProvider);
    final steelColumnInputs = ref.watch(
      steelColumnSchemeInputControllerProvider,
    );
    final steelColumnInputsGlobal = ref.watch(
      steelColumnSchemeInputGlobalControllerProvider,
    );
    final steelColumnSchemes = ref.watch(
      steelColumnSchemeDataControllerProvider,
    );

    return loadingTables.when(
      data: (tables) {
        //start reutnr real stuffs when loading tables available
        return globalData.when(
          //also, start reutnr real stuffs when global data tables available
          data: (data) {
            return steelColumnInputsGlobal.when(
              data: (inputsGlobal) {
                return steelColumnInputs.when(
                  data: (inputs) {
                    return steelColumnSchemes.when(
                      data: (schemes) {
                        late int indexMinScheme;
                        return Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Padding(
                              padding: const EdgeInsets.fromLTRB(10, 0, 10, 0),
                              child: Divider(),
                            ),
                            Padding(
                              padding: const EdgeInsets.fromLTRB(10, 0, 0, 0),
                              child: Align(
                                alignment: Alignment.centerLeft,
                                child: Wrap(
                              crossAxisAlignment: WrapCrossAlignment.center,
                                  children: [
                                    IconButton(
                                      icon: Icon(
                                        _isExpanded
                                            ? Icons.expand_less
                                            : Icons.expand_more,
                                      ),
                                      color: colorScheme.onSurface,
                                      onPressed: () {
                                        setState(() {
                                          _isExpanded = !_isExpanded;
                                        });
                                      },
                                    ),
                                    Text(
                                      'Steel Column Scheme Summary ',
                                      style: textTheme.titleLarge!.copyWith(
                                        color: colorScheme.onSurface,
                                      ),
                                    ),
                                    Container(
                                      decoration: BoxDecoration(
                                        borderRadius: BorderRadius.circular(
                                          5.0,
                                        ),
                                        color: colorScheme.surfaceContainer
                                            .withAlpha(225),
                                        border: Border.all(
                                          color: Colors.black.withAlpha(125),
                                        ),
                                      ),
                                      child: Padding(
                                        padding: const EdgeInsets.all(2.0),
                                        child: Text(
                                          '(Total: ${schemes.length})',
                                          style: textTheme.labelMedium!
                                              .copyWith(
                                                color: colorScheme
                                                    .onSurfaceVariant
                                                    .withAlpha(225),
                                              ),
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                            Padding(
                              padding: const EdgeInsets.fromLTRB(10, 0, 10, 0),
                              child: Divider(),
                            ),

                            ClipRect(
                              child: ConstrainedBox(
                                constraints: BoxConstraints(
                                  maxHeight: _isExpanded ? _maxHeight : 0,
                                ),
                                child: Column(
                                  children: [
                                    Builder(
                                      builder: (context) {
                                        if (schemes.isNotEmpty) {
                                          final maxTable = schemes.reduce((
                                            current,
                                            next,
                                          ) {
                                            int sizeComparison = current.fsy
                                                .compareTo(next.fsy);

                                            if (sizeComparison < 0) {
                                              return current; // Sort by fsy
                                            } else {
                                              return next;
                                            }
                                          });
                                          indexMinScheme = schemes.indexOf(
                                            maxTable,
                                          );

                                          late List<String> unit;
                                          switch (data.unit) {
                                            case 'metrics':
                                              unit = PreferredUnit.metrics;
                                              break;
                                            case 'imperial':
                                              unit = PreferredUnit.imperial;
                                              break;
                                            default:
                                              unit = PreferredUnit.metrics;
                                          }
                                          return DefaultTextStyle(
                                            style: textTheme.labelSmall!
                                                .copyWith(
                                                  color:
                                                      colorScheme
                                                          .onErrorContainer,
                                                ),
                                            child: Padding(
                                              padding:
                                                  const EdgeInsets.fromLTRB(
                                                    10,
                                                    0,
                                                    10,
                                                    0,
                                                  ),
                                              child: Container(
                                                decoration: BoxDecoration(
                                                  borderRadius:
                                                      BorderRadius.circular(
                                                        5.0,
                                                      ),
                                                  color: colorScheme
                                                      .errorContainer
                                                      .withAlpha(100),
                                                  border: Border.all(
                                                    width: 0.5,
                                                    color: colorScheme.error
                                                        .withAlpha(100),
                                                  ),
                                                ),
                                                child: Padding(
                                                  padding: const EdgeInsets.all(
                                                    4.0,
                                                  ),
                                                  child: Row(
                                                    mainAxisAlignment:
                                                        MainAxisAlignment.start,
                                                    mainAxisSize:
                                                        MainAxisSize.min,
                                                    children: [
                                                      Container(
                                                        decoration: BoxDecoration(
                                                          shape:
                                                              BoxShape.circle,
                                                          border: Border.all(
                                                            color:
                                                                colorScheme
                                                                    .onErrorContainer,
                                                            width: 1.0,
                                                          ),
                                                        ),
                                                        child: Padding(
                                                          padding:
                                                              const EdgeInsets.all(
                                                                8.0,
                                                              ),
                                                          child: Text(
                                                            '${indexMinScheme + 1}',
                                                            style: textTheme
                                                                .titleSmall!
                                                                .copyWith(
                                                                  color:
                                                                      colorScheme
                                                                          .onErrorContainer,
                                                                ),
                                                          ),
                                                        ),
                                                      ),
                                                      SizedBox(width: 15.0),
                                                      Row(
                                                        children: [
                                                          Column(
                                                            mainAxisSize:
                                                                MainAxisSize
                                                                    .min,
                                                            crossAxisAlignment:
                                                                CrossAxisAlignment
                                                                    .start,
                                                            children: [
                                                              Text(
                                                                'Minimal Scheme',
                                                                style: textTheme
                                                                    .labelMedium!
                                                                    .copyWith(
                                                                      color:
                                                                          colorScheme
                                                                              .error,
                                                                    ),
                                                              ),
                                                              Text(
                                                                'Section: ${maxTable.steelSection}',
                                                              ),
                                                              Text(
                                                                'Fy: ${NumberFormat('0').format(maxTable.fsy)} [${unit[5]}]',
                                                              ),
                                                              Text(
                                                                'L: ${inputsGlobal.unbracedLength} [${unit[3]}]',
                                                              ),
                                                            ],
                                                          ),
                                                          SizedBox(width: 15.0),
                                                          Column(
                                                            mainAxisSize:
                                                                MainAxisSize
                                                                    .min,
                                                            crossAxisAlignment:
                                                                CrossAxisAlignment
                                                                    .start,
                                                            children: [
                                                              Text(''),
                                                              Text(
                                                                'Capacity: ${NumberFormat('0').format(maxTable.axialCapacity)} [${unit[0]}]',
                                                              ),
                                                              Text(
                                                                'ULS Load: ${NumberFormat('0').format(maxTable.ulsLoad)} [${unit[0]}]',
                                                              ),
                                                              Text(
                                                                'SLS Load: ${NumberFormat('0').format(maxTable.slsLoad)} [${unit[0]}]',
                                                              ),
                                                            ],
                                                          ),
                                                        ],
                                                      ),
                                                      SizedBox(width: 15.0),
                                                    ],
                                                  ),
                                                ),
                                              ),
                                            ),
                                          );
                                        } else {
                                          return Align(
                                            child: Padding(
                                              padding:
                                                  const EdgeInsets.fromLTRB(
                                                    10,
                                                    0,
                                                    10,
                                                    0,
                                                  ),
                                              child: Container(
                                                decoration: BoxDecoration(
                                                  borderRadius:
                                                      BorderRadius.circular(
                                                        5.0,
                                                      ),
                                                  color: colorScheme
                                                      .errorContainer
                                                      .withAlpha(100),
                                                  border: Border.all(
                                                    width: 0.5,
                                                    color: colorScheme.error
                                                        .withAlpha(100),
                                                  ),
                                                ),
                                                child: Padding(
                                                  padding: const EdgeInsets.all(
                                                    4.0,
                                                  ),
                                                  child: Text(
                                                    'No Steel Column Scheme Data',
                                                    style: textTheme
                                                        .labelMedium!
                                                        .copyWith(
                                                          color:
                                                              colorScheme.error,
                                                        ),
                                                  ),
                                                ),
                                              ),
                                            ),
                                          );
                                        }
                                      },
                                    ),
                                    SizedBox(height: 10),
                                    Flexible(
                                      child: Scrollbar(
                                        controller: _scrollController,
                                        thumbVisibility: true,
                                        child: DefaultTextStyle(
                                          style: textTheme.labelSmall!.copyWith(
                                            color: colorScheme.onSurface,
                                          ),
                                          child: ListView.builder(
                                            controller: _scrollController,
                                            itemCount: schemes.length,
                                            itemBuilder: (context, index) {
                                              late List<String> unit;
                                              switch (data.unit) {
                                                case 'metrics':
                                                  unit = PreferredUnit.metrics;
                                                  break;
                                                case 'imperial':
                                                  unit = PreferredUnit.imperial;
                                                  break;
                                                default:
                                                  unit = PreferredUnit.metrics;
                                              }

                                              return Padding(
                                                padding:
                                                    const EdgeInsets.fromLTRB(
                                                      8.0,
                                                      4.0,
                                                      8.0,
                                                      4.0,
                                                    ),
                                                child: Row(
                                                  children: [
                                                    Column(
                                                      crossAxisAlignment:
                                                          CrossAxisAlignment
                                                              .center,
                                                      children: [
                                                        FunctionButton(
                                                          labelText:
                                                              ' ${index + 1} ',
                                                          pressedColor:
                                                              colorScheme
                                                                  .tertiary
                                                                  .withAlpha(
                                                                    150,
                                                                  ),
                                                        ),
                                                        SizedBox(height: 5.0),
                                                        FunctionButton(
                                                          labelText:
                                                              'Show Cals',
                                                          pressedColor:
                                                              colorScheme
                                                                  .tertiary
                                                                  .withAlpha(
                                                                    150,
                                                                  ),
                                                          onTap: (value) async {
                                                            final globalData =
                                                                await ref.read(
                                                                  globalDataControllerProvider
                                                                      .future,
                                                                );
                                                            _presentCalsRecord(
                                                              globalData,
                                                              unit,
                                                              inputs,
                                                              inputsGlobal,
                                                              schemes,
                                                              index,
                                                            );
                                                          },
                                                        ),
                                                        SizedBox(height: 5.0),
                                                      ],
                                                    ),
                                                    IconButton(
                                                      icon: Icon(
                                                        Icons.delete,
                                                        color:
                                                            colorScheme
                                                                .onSurface,
                                                      ),
                                                      onPressed: () {
                                                        ref
                                                            .read(
                                                              steelColumnSchemeDataControllerProvider
                                                                  .notifier,
                                                            )
                                                            .deleteTable(
                                                              schemes[index]
                                                                  .steelColumnSchemeDataId,
                                                            );
                                                      },
                                                    ),
                                                    Flexible(
                                                      child: Row(
                                                        mainAxisAlignment:
                                                            MainAxisAlignment
                                                                .start,
                                                        mainAxisSize:
                                                            MainAxisSize.min,
                                                        children: [
                                                          SizedBox(width: 15.0),
                                                          Column(
                                                            mainAxisSize:
                                                                MainAxisSize
                                                                    .min,
                                                            crossAxisAlignment:
                                                                CrossAxisAlignment
                                                                    .start,
                                                            children: [
                                                              Container(
                                                                decoration: BoxDecoration(
                                                                  borderRadius:
                                                                      BorderRadius.circular(
                                                                        5.0,
                                                                      ),
                                                                  color: colorScheme
                                                                      .primaryContainer
                                                                      .withAlpha(
                                                                        200,
                                                                      ),
                                                                ),
                                                                child: Padding(
                                                                  padding:
                                                                      const EdgeInsets.all(
                                                                        3.0,
                                                                      ),
                                                                  child: Text(
                                                                    'Section: ${schemes[index].steelSection}',
                                                                    style: textTheme
                                                                        .labelMedium!
                                                                        .copyWith(
                                                                          color:
                                                                              colorScheme.onPrimaryContainer,
                                                                        ),
                                                                  ),
                                                                ),
                                                              ),
                                                              SizedBox(
                                                                height: 5.0,
                                                              ),
                                                              Row(
                                                                children: [
                                                                  Container(
                                                                    decoration: BoxDecoration(
                                                                      borderRadius:
                                                                          BorderRadius.circular(
                                                                            5.0,
                                                                          ),
                                                                      color: colorScheme
                                                                          .secondaryContainer
                                                                          .withAlpha(
                                                                            200,
                                                                          ),
                                                                    ),
                                                                    child: Padding(
                                                                      padding:
                                                                          const EdgeInsets.all(
                                                                            3.0,
                                                                          ),
                                                                      child: Text(
                                                                        'Fy: ${NumberFormat('0').format(schemes[index].fsy)} [${unit[5]}]',
                                                                        style: textTheme.labelMedium!.copyWith(
                                                                          color:
                                                                              colorScheme.onSecondaryContainer,
                                                                        ),
                                                                      ),
                                                                    ),
                                                                  ),
                                                                  SizedBox(
                                                                    width: 5.0,
                                                                  ),
                                                                  Container(
                                                                    decoration: BoxDecoration(
                                                                      borderRadius:
                                                                          BorderRadius.circular(
                                                                            5.0,
                                                                          ),
                                                                      color: colorScheme
                                                                          .secondaryContainer
                                                                          .withAlpha(
                                                                            200,
                                                                          ),
                                                                    ),
                                                                    child: Padding(
                                                                      padding:
                                                                          const EdgeInsets.all(
                                                                            3.0,
                                                                          ),
                                                                      child: Text(
                                                                        'L: ${NumberFormat('0').format(inputsGlobal.unbracedLength)} [${unit[3]}]',
                                                                        style: textTheme.labelMedium!.copyWith(
                                                                          color:
                                                                              colorScheme.onSecondaryContainer,
                                                                        ),
                                                                      ),
                                                                    ),
                                                                  ),
                                                                ],
                                                              ),
                                                              Text(
                                                                'Capacity: ${NumberFormat('0').format(schemes[index].axialCapacity)} [${unit[0]}]',
                                                              ),
                                                              Text(
                                                                'ULS Load: ${NumberFormat('0').format(schemes[index].ulsLoad)} [${unit[0]}]',
                                                              ),
                                                              Text(
                                                                'SLS Load: ${NumberFormat('0').format(schemes[index].slsLoad)} [${unit[0]}]',
                                                              ),
                                                              SizedBox(
                                                                height: 10.0,
                                                              ),
                                                            ],
                                                          ),
                                                          SizedBox(width: 15.0),
                                                          GestureDetector(
                                                            onTap: () async {
                                                              await showDialog(
                                                                context:
                                                                    context,
                                                                builder: (
                                                                  context,
                                                                ) {
                                                                  return Dialog(
                                                                    backgroundColor:
                                                                        colorScheme
                                                                            .surfaceContainer,
                                                                    child: SizedBox(
                                                                      width:
                                                                          550,
                                                                      height:
                                                                          550,
                                                                      child: DrawSteelColumnScheme(
                                                                        sketchWidth:
                                                                            500,
                                                                        sketchHeight:
                                                                            500,
                                                                        index:
                                                                            index,
                                                                      ),
                                                                    ),
                                                                  );
                                                                },
                                                              );
                                                            },
                                                            child:
                                                                DrawSteelColumnScheme(
                                                                  sketchWidth:
                                                                      125,
                                                                  sketchHeight:
                                                                      125,
                                                                  index: index,
                                                                ),
                                                          ),
                                                        ],
                                                      ),
                                                    ),
                                                    SizedBox(width: 5.0),
                                                  ],
                                                ),
                                              );
                                            },
                                          ),
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ],
                        );
                      },
                      error: (error, stackTrace) => Text(error.toString()),
                      loading: () => CircularProgressIndicator(),
                    );
                  },
                  error: (error, stackTrace) => Text(error.toString()),
                  loading: () => CircularProgressIndicator(),
                );
              },
              error: (error, stackTrace) => Text(error.toString()),
              loading: () => CircularProgressIndicator(),
            );
          },
          error: (error, stackTrace) => Text(error.toString()),
          loading: () => CircularProgressIndicator(),
        );
      },
      error: (error, stackTrace) => Text(error.toString()),
      loading: () => CircularProgressIndicator(),
    );
  }

  void _updateHeights() {
    final double screenHeight = MediaQuery.of(context).size.height;
    _maxHeight =
        (widget.maxHeight == null)
            ? screenHeight * 0.5
            : (widget.maxHeight! > screenHeight * 0.5)
            ? screenHeight * 0.5
            : widget.maxHeight!;

    _minHeight =
        (widget.minHeight == null)
            ? screenHeight * 0.1
            : (widget.minHeight! > screenHeight * 0.1)
            ? screenHeight * 0.1
            : widget.minHeight!;

    // Adjust _maxHeight if needed

    if (_maxHeight < _minHeight) {
      _maxHeight = _minHeight;
    }
  }

  Future<void> _presentCalsRecord(
    GlobalData globalData,
    List<String> unit,
    List<SteelColumnSchemeInput> inputs,
    SteelColumnSchemeInputGlobal inputGlobal,
    List<SteelColumnSchemeData> schemes,
    int index,
  ) async {
    // 1. get the record first
    final record = schemes[index].calsLog;
    List<String> lines = record.split('\n');

    // Regex to match items
    RegExp titleRegExp = RegExp(r'([\w ()]*[:])');
    RegExp digitsRegExp = RegExp(r'(?<= )([\d]+[\.]?[\d]*(?= ))');
    RegExp unitRegExp = RegExp(r'(\[(.+?)\])');
    RegExp textRegExp = RegExp(r'(?<=: )\w*');

    Map<String, List<dynamic>> result = {};

    for (String line in lines) {
      final List<String> newLines = line.split(r'|');
      for (String line in newLines) {
        final titleMatch = titleRegExp
            .firstMatch(line)
            ?.group(0)
            ?.trim()
            .replaceAll(':', '');
        final digitMatch = digitsRegExp.firstMatch(line)?.group(0)?.trim();
        final unitMatch = unitRegExp.firstMatch(line)?.group(0);
        final textMatch = textRegExp.firstMatch(line)?.group(0)?.trim();

        if (titleMatch != null) {
          if (titleMatch.contains(RegExp(r'Designation|Scheme'))) {
            result[titleMatch] = [textMatch, unitMatch];
          } else {
            result[titleMatch] = [digitMatch, unitMatch];
          }
        }
      }
    }

    await showDialog(
      context: context,
      builder: (context) {
        ColorScheme colorScheme = Theme.of(context).colorScheme;
        TextTheme textTheme = Theme.of(context).textTheme;
        Color bgColor = colorScheme.surfaceContainer;
        Color onBgColor = colorScheme.onSurface;
        Color titleBgColor = colorScheme.primary.withAlpha(150);
        Color titleOnBgColor = colorScheme.onPrimary;
        TextStyle titleTextStyle = textTheme.labelSmall!.copyWith(
          color: titleOnBgColor,
        );
        ScrollController scrollController = ScrollController();

        late List<String> unit;
        switch (globalData.unit) {
          case 'metrics':
            unit = PreferredUnit.metrics;
            break;
          case 'imperial':
            unit = PreferredUnit.imperial;
            break;
          default:
            unit = PreferredUnit.metrics;
        }
        const double maxH = 600;
        const double maxW = 600;
        NumberFormat f0 = NumberFormat('0');

        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(15),
          ),
          backgroundColor: bgColor,
          child: ConstrainedBox(
            constraints: const BoxConstraints(maxWidth: maxW, maxHeight: maxH),
            child: FutureBuilder(
              future: _writeResult(
                result,
                index,
                unit,
              ),
              builder: (context, snapshot) {
                if (snapshot.hasData) {
                  final StringBuffer steelColumnCals = snapshot.data!;
                  return Column(
                    children: [
                      Align(
                        alignment: Alignment.centerRight,
                        child: Padding(
                          padding: const EdgeInsets.fromLTRB(0, 10, 10, 0),
                          child: FunctionButton(
                            bgColor: titleBgColor,
                            labelTextColor: titleOnBgColor,
                            labelIcon: Icon(
                              Icons.print_outlined,
                              color: titleOnBgColor.withAlpha(175),
                            ),
                            labelText: '',
                            onTap: (value) {
                              exportListToPdf(
                                context,
                                ref,
                                steelColumnCals.toString().split('\n'),
                              );
                            },
                          ),
                        ),
                      ),

                      Scrollbar(
                        controller: scrollController,
                        thumbVisibility: true,
                        trackVisibility: true,
                        child: ConstrainedBox(
                          constraints: const BoxConstraints(
                            maxWidth: maxW,
                            maxHeight: maxH - 70,
                          ),
                          child: SingleChildScrollView(
                            controller: scrollController,
                            child: Padding(
                              padding: const EdgeInsets.fromLTRB(
                                25.0,
                                5,
                                25.0,
                                5,
                              ),
                              child: DefaultTextStyle(
                                style: textTheme.labelMedium!.copyWith(
                                  color: onBgColor,
                                ),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  children: [
                                    Container(
                                      decoration: BoxDecoration(
                                        color: colorScheme.tertiary,
                                        shape: BoxShape.circle,
                                        border: Border.all(
                                          color: titleOnBgColor,
                                        ),
                                      ),
                                      child: Padding(
                                        padding: const EdgeInsets.all(12.0),
                                        child: Text(
                                          '${index + 1}',
                                          style: titleTextStyle.copyWith(
                                            fontSize: 24,
                                            color: colorScheme.onTertiary,
                                          ),
                                        ),
                                      ),
                                    ),
                                    Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      children: [
                                        Container(
                                          decoration: BoxDecoration(
                                            color: titleBgColor,
                                            shape: BoxShape.rectangle,
                                            borderRadius: BorderRadius.circular(
                                              10.0,
                                            ),
                                            border: Border.all(
                                              color: titleOnBgColor,
                                            ),
                                          ),
                                          child: Padding(
                                            padding: const EdgeInsets.all(4.0),
                                            child: Text(
                                              'Section :\n${schemes[index].steelSection}',
                                              style: titleTextStyle,
                                            ),
                                          ),
                                        ),
                                        SizedBox(width: 5),
                                        Container(
                                          decoration: BoxDecoration(
                                            color: titleBgColor,
                                            shape: BoxShape.rectangle,
                                            borderRadius: BorderRadius.circular(
                                              10.0,
                                            ),
                                            border: Border.all(
                                              color: titleOnBgColor,
                                            ),
                                          ),
                                          child: Padding(
                                            padding: const EdgeInsets.all(4.0),
                                            child: Text(
                                              'Fy [${unit[5]}]:\n${f0.format(schemes[index].fsy)} ',
                                              style: titleTextStyle,
                                            ),
                                          ),
                                        ),

                                        SizedBox(width: 5),
                                        Container(
                                          decoration: BoxDecoration(
                                            color: titleBgColor,
                                            shape: BoxShape.rectangle,
                                            borderRadius: BorderRadius.circular(
                                              10.0,
                                            ),
                                            border: Border.all(
                                              color: titleOnBgColor,
                                            ),
                                          ),
                                          child: Padding(
                                            padding: const EdgeInsets.all(4.0),
                                            child: Text(
                                              'SLS Load [${unit[0]}]:\n${f0.format(schemes[index].slsLoad)}',
                                              style: titleTextStyle,
                                            ),
                                          ),
                                        ),
                                        SizedBox(width: 5),
                                        Container(
                                          decoration: BoxDecoration(
                                            color: titleBgColor,
                                            shape: BoxShape.rectangle,
                                            borderRadius: BorderRadius.circular(
                                              10.0,
                                            ),
                                            border: Border.all(
                                              color: titleOnBgColor,
                                            ),
                                          ),
                                          child: Padding(
                                            padding: const EdgeInsets.all(4.0),
                                            child: Text(
                                              'ULS Load [${unit[0]}]:\n${f0.format(schemes[index].ulsLoad)}',
                                              style: titleTextStyle,
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                    SizedBox(height: 5),
                                    Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      children: [
                                        Container(
                                          decoration: BoxDecoration(
                                            color: titleBgColor,
                                            shape: BoxShape.rectangle,
                                            borderRadius: BorderRadius.circular(
                                              10.0,
                                            ),
                                            border: Border.all(
                                              color: titleOnBgColor,
                                            ),
                                          ),
                                          child: Padding(
                                            padding: const EdgeInsets.all(4.0),
                                            child: Text(
                                              'Capacity [${unit[0]}]:\n${f0.format(schemes[index].axialCapacity)} ',
                                              style: titleTextStyle,
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                    SizedBox(height: 5),
                                    Container(
                                      decoration: BoxDecoration(
                                        borderRadius: BorderRadius.circular(
                                          10.0,
                                        ),
                                        border: Border.all(color: onBgColor),
                                      ),
                                      child: Padding(
                                        padding: const EdgeInsets.all(8.0),
                                        child: Text(steelColumnCals.toString()),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  );
                } else if (snapshot.hasError) {
                  return Center(child: Text(snapshot.error.toString()));
                } else {
                  return const CircularProgressIndicator();
                }
              },
            ),
          ),
        );
      },
    );
  }

  Future<StringBuffer> _writeResult(
    Map<String, List> result,
    int index,
    List<String> unit,
  ) async {
    final GlobalData globalData = await ref.read(
      globalDataControllerProvider.future,
    );
    final List<SteelColumnSchemeData> schemes = await ref.read(
      steelColumnSchemeDataControllerProvider.future,
    );
    final List<SteelColumnSchemeInput> inputs = await ref.read(
      steelColumnSchemeInputControllerProvider.future,
    );
    final SteelColumnSchemeInputGlobal inputGlobal = await ref.read(
      steelColumnSchemeInputGlobalControllerProvider.future,
    );

    StringBuffer buffer = StringBuffer();
    SteelColumnSchemeData scheme = schemes[index];
    String usage;
    NumberFormat f0 = NumberFormat('0'), f1 = NumberFormat('0.0'), f3 = NumberFormat('0.000');
    late final double Es,
        maxThk,
        minR,
        lamda,
        lamda0,
        eta,
        alpha,
        pE,
        phi,
        pc,
        aG,
        axialCap;
    double fsy = scheme.fsy;
    Es = 205000;
    final List<Map<String, dynamic>> steelSections = await getAllSteelISection();
    final Map<String, dynamic> section = steelSections.firstWhere(
      (item) => item['name'] == scheme.steelSection,
    );
    buffer.write('*******************\n');
    buffer.write('Column Size:  ');
    buffer.write('${scheme.steelSection}\n');
    buffer.write('*******************\n');
    buffer.write('Loading\n');
    buffer.write('*******************\n');

    for (int i = 0; i < inputs.length; i++) {
      usage = inputs[i].usage;
      buffer.write('Usgae: $usage | ');
      buffer.write(
        'Load Area: ${f0.format(_toDouble(result['LoadArea (${i + 1})']))} [${unit[7]}] | ',
      );
      buffer.write(
        'SLS (${i + 1}): ${f0.format(_toDouble(result['SLS Load (${i + 1})']))} [${unit[0]}] | ',
      );
      buffer.write(
        'ULS (${i + 1}): ${f0.format(_toDouble(result['ULS Load (${i + 1})']))} [${unit[0]}]\n',
      );
    }
    buffer.write(
      'SLS Total: ${f0.format(_toDouble(result['SLS Total']))} [${unit[0]}]\n',
    );
    buffer.write(
      'ULS Total: ${f0.format(_toDouble(result['ULS Total']))} [${unit[0]}]\n',
    );
    buffer.write('*******************\n');
    buffer.write('Capacity\n');
    buffer.write('*******************\n');
    //* design strength updated
    fsy = await getDesignStrength(scheme.steelSection, fsy, steelSections);
    //* calculate the pc
    minR =
        min(
          double.tryParse(section['r22'])!,
          double.tryParse(section['r33'])!,
        ) *
        10; // [mm]
    maxThk =
        max(double.tryParse(section['tF'])!, double.tryParse(section['tW'])!) *
        10; // [mm]
    lamda = inputGlobal.unbracedLength * 1000 / minR;
    lamda0 = 0.2 * sqrt(pow(pi, 2) * Es / fsy);
    if (maxThk <= 40) {
      alpha =
          3.5; // buckling curve (b): Rolled I, about weak axis y-y, max thickness <= 40mm
    } else {
      alpha =
          5.5; // buckling curve (c): Rolled I, about weak axis y-y, max thickness > 40mm
    }
    eta = max(alpha * (lamda - lamda0) / 1000, 0);
    pE = pow(pi, 2) * Es / pow(lamda, 2);
    phi = (fsy + (eta + 1) * pE) / 2;
    pc = (pE * fsy) / (phi + sqrt(pow(phi, 2) - pE * fsy));
    aG = double.parse(section['A']) * 100; // [mm2]
    axialCap = pc * aG / 1000; // [kN]

    //* present the calculated parameters
    buffer.write(
      'Fy = ${f0.format(scheme.fsy)} [${unit[5]}] (design strength, not grade)\n',
    );
    buffer.write('E = ${f0.format(Es)} [${unit[5]}]\n');
    buffer.write(
      'L = ${f0.format(inputGlobal.unbracedLength * 1000)} [${unit[4]}]\n',
    );
    buffer.write(
      'r = ${f0.format(minR)} [${unit[4]}] (min radius of gyration)\n',
    );
    buffer.write('\u03BB = L/r = ${f0.format(lamda)}\n');
    buffer.write(
      '\u03BB\u2080 = 0.2\u221A(\u03C0^2*E/Fy) = ${f0.format(lamda0)}\n',
    );
    buffer.write('\u03B1 = ${f1.format(alpha)} (buckling curve: ${alpha == 3.5 ? 'b' : 'c'})\n' );
    buffer.write(
      '\u03B7 = max(\u03B1*(\u03BB-\u03BB\u2080)/1000,0) = ${f3.format(eta)}\n',
    );
    buffer.write(
      'pE = (\u03C0^2*E)/\u03BB^2 = ${f0.format(pE)} [${unit[5]}]\n ',
    );
    buffer.write('\u03A6 = (Fy + (\u03B7+1)*pE)/2 = ${f0.format(phi)}\n');
    buffer.write(
      'pc = (pE*Fy)/(\u03A6 + \u221A(\u03A6^2-pE*Fy)) =${f0.format(pc)} [${unit[5]}]\n',
    );
    buffer.write('Ag = ${f0.format(aG)} [${unit[6]}]\n');
    buffer.write('Pc = pc*Ag = ${f0.format(axialCap)} [${unit[0]}]\n');
    return buffer;
  }

  dynamic _toDouble(List<dynamic>? data) {
    if (data != null) {
      final x = double.tryParse(data[0]);
      if (x != null) {
        return x;
      } else {
        return 'null';
      }
    } else {
      return 'null';
    }
  }
}
