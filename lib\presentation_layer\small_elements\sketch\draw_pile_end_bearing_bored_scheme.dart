import 'dart:math' as math;

import 'package:flutter/material.dart';
import 'package:flutter/physics.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:structify/presentation_layer/screen/homescreen.dart';
import 'package:vector_math/vector_math.dart' as vec;

import '../../../domain_layer/global_data.dart';
import '../../../domain_layer/pile_end_bearing_bored_data.dart';
import '../../../domain_layer/preferences.dart';

class DrawPileEndBearingBoredScheme extends ConsumerWidget {
  DrawPileEndBearingBoredScheme({
    required this.sketchWidth,
    required this.sketchHeight,
    required this.index,
    this.fontSize,
    super.key,
  });

  final int index;
  final double sketchWidth;
  final double sketchHeight;
  double? fontSize;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final data1 = ref.watch(pileEndBearingBoredDataControllerProvider);
    final data2 = ref.watch(globalDataControllerProvider);
    final ColorScheme colorScheme = Theme.of(context).colorScheme;
    final TextTheme textTheme = Theme.of(context).textTheme;
    fontSize = fontSize ?? math.min(sketchWidth, sketchHeight) / 15;
    return data1.when(
      data: (pileSchemedata) {
        return data2.when(
          data: (globalData) {
            final constraints = BoxConstraints(
              maxWidth: sketchWidth,
              maxHeight: sketchHeight,
              minHeight: 100,
              minWidth: 100,
            );
            return Center(
              child: Container(
                decoration: BoxDecoration(
                  color: colorScheme.surfaceContainer.withAlpha(100),
                  borderRadius: BorderRadius.circular(10.0),
                  border: Border.all(color: Colors.black.withAlpha(100)),
                ),
                width:
                    constraints.maxWidth == double.infinity
                        ? 100
                        : constraints.maxWidth,
                height:
                    constraints.maxHeight == double.infinity
                        ? 100
                        : constraints.maxHeight,
                child: CustomPaint(
                  painter: DrawPileEndBearingBoredSchemePainter(
                    pileSchemedata: pileSchemedata[index],
                    globalData: globalData,
                    boxConstraints: constraints,
                    fontSize: fontSize,
                    context: context,
                  ),
                ),
              ),
            );
          },

          error: (error, stackTrace) {
            return Text(error.toString());
          },
          loading: () {
            return const CircularProgressIndicator();
          },
        );
      },
      error: (error, stackTrace) {
        return Text(error.toString());
      },
      loading: () {
        return const CircularProgressIndicator();
      },
    );
  }
}

class DrawPileEndBearingBoredSchemePainter extends CustomPainter {
  final PileEndBearingBoredData pileSchemedata;
  final GlobalData globalData;
  final BoxConstraints boxConstraints;
  double? fontSize;
  final BuildContext context;

  DrawPileEndBearingBoredSchemePainter({
    required this.pileSchemedata,
    required this.globalData,
    required this.boxConstraints,
    this.fontSize,
    required this.context,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final ColorScheme colorScheme = Theme.of(context).colorScheme;
    final TextTheme textTheme = Theme.of(context).textTheme;
    late final double secBeamSpacing;

    fontSize = fontSize ?? math.min(size.width, size.height) / 40;

    Paint rcPaint =
        Paint()
          ..color = colorScheme.onSurface
          ..strokeCap = StrokeCap.round
          ..style = PaintingStyle.stroke
          ..strokeWidth = 1.0;

    Paint rebarPaint =
        Paint()
          ..color = colorScheme.onSurface
          ..strokeCap = StrokeCap.round
          ..style = PaintingStyle.fill
          ..strokeWidth = 2.0;
    Paint rebarPaint2 =
        Paint()
          ..color = Colors.amberAccent
          ..strokeCap = StrokeCap.round
          ..style = PaintingStyle.fill
          ..strokeWidth = 2.0;

    final double minDim = math.min(
      boxConstraints.maxWidth,
      boxConstraints.maxHeight,
    );
    final double ratio = minDim / pileSchemedata.diameter * 2 / 3;

    late Offset startP1, endP1, startP2, endP2, startP3, endP3;

    Path path = Path();

    // ******************
    // draw RC
    // ******************
    startP1 = Offset(
      0.5 * (boxConstraints.maxWidth),
      0.5 * (boxConstraints.maxHeight),
    );

    canvas.drawCircle(startP1, pileSchemedata.diameter * ratio / 2, rcPaint);

    _drawDimLine(
      canvas,
      fontSize!,
      startP1 -
          Offset(
            pileSchemedata.diameter * ratio / 2,
            pileSchemedata.diameter * ratio / 2,
          ),
      startP1 +
          Offset(
            pileSchemedata.diameter * ratio / 2,
            -pileSchemedata.diameter * ratio / 2,
          ),
      10,
      fontSize! / 2,
      pileSchemedata.diameter,
      globalData,
      context,
    );

    // ******************
    // draw rebars
    // ******************

    final rebar = pileSchemedata.rebar;

    int rebarDia = 0;
    int nosOfRebar = 0;
    double x = 0,
        dx = 0,
        y = 0,
        dy = 0,
        r = pileSchemedata.diameter / 2 * ratio,
        phi = 0,
        theta = 0,
        cover = 40 * ratio,
        linkDia = 12 * ratio;
    final Color fontColor = Colors.red.withAlpha(200);
    final RegExp regex1 = RegExp(r"\d+(?=T)"), regex2 = RegExp(r"(?<=T)\d+");

    // -------------------
    // Draw Corner bar
    // -------------------

    nosOfRebar =
        regex1.allMatches(rebar).map((e) => int.parse(e.group(0)!)).toList()[0];
    rebarDia =
        regex2.allMatches(rebar).map((e) => int.parse(e.group(0)!)).toList()[0];
    r = r - cover - linkDia - rebarDia / 2 * ratio;
    x = 0 + startP1.dx;
    y = -r + startP1.dy;

    theta = 2 * math.pi / nosOfRebar;
    for (int i = 0; i < nosOfRebar; i++) {
      canvas.drawCircle(Offset(x, y), rebarDia / 2 * ratio, rebarPaint);

      if (nearZero(x - startP1.dx, 0.000000001)) {
        if (y - startP1.dy < 0) {
          phi = math.pi / 2;
        } else {
          phi = math.pi + math.pi / 2;
        }
      } else {
        if (y - startP1.dy < 0) {
          if (x - startP1.dx > 0) {
            // 1st quotient
            phi = math.atan(-(y - startP1.dy) / (x - startP1.dx));
          } else {
            // 2nd quotient
            phi = math.atan(-(y - startP1.dy) / (x - startP1.dx)) + math.pi;
          }
        } else {
          if (x - startP1.dx > 0) {
            //4th quotient
            phi = math.atan(-(y - startP1.dy) / (x - startP1.dx)) + 2 * math.pi;
          } else {
            //3rd quotient
            phi = math.atan(-(y - startP1.dy) / (x - startP1.dx)) + math.pi;
          }
        }
      }
      canvas.drawCircle(Offset(x, y), rebarDia / 2 * ratio, rebarPaint);
      dx = r * math.cos(phi - theta) - r * math.cos(phi);
      dy = r * math.sin(phi) - r * math.sin(phi - theta);

      y += dy;
      x += dx;
    }
    //draw links
    canvas.drawCircle(
      startP1,
      pileSchemedata.diameter * ratio / 2 - cover,
      rcPaint,
    );

    // Text for Bar
    TextSpan textSpan = TextSpan(
      text: '${nosOfRebar}T$rebarDia',
      style: TextStyle(
        fontSize: fontSize,
        color: fontColor,
        fontWeight: FontWeight.bold,
      ),
    );

    TextPainter textPainter = TextPainter(
      text: textSpan,
      textAlign: TextAlign.center,
      textDirection: TextDirection.ltr,
    )..layout();

    textPainter.paint(
      canvas,
      startP1 -
          Offset(textPainter.width / 2, pileSchemedata.diameter / 30 * ratio),
    );
  }

  // void _rebarDesignation(
  //   List<int> rebarDia,
  //   int i,
  //   List<int> rebarSpacing,
  //   ui.Color fontColor,
  //   ui.Canvas canvas,
  //   ui.Offset endP3,
  // ) {
  //   TextSpan textSpan = TextSpan(
  //     text: 'T${rebarDia[i]}-${rebarSpacing[i]}',
  //     style: TextStyle(
  //       fontSize: fontSize,
  //       color: fontColor,
  //       fontWeight: FontWeight.bold,
  //     ),
  //   );

  //   TextPainter textPainter = TextPainter(
  //     text: textSpan,
  //     textAlign: TextAlign.center,
  //     textDirection: TextDirection.ltr,
  //   )..layout();

  //   textPainter.paint(canvas, endP3 + Offset(7, -7));
  // }

  void _drawDimLine(
    Canvas canvas,
    double fontSize,
    Offset start,
    Offset end,
    double offsetDistance,
    double dimExtension,
    double slabDepth,
    GlobalData globalData,
    BuildContext context,
  ) {
    double tempOffset = 10; // forthe dim line end
    late Offset tempEnd;
    late Offset tempStart;
    late Offset textCenter;

    late double angle;

    late List<String> unit;

    Color fontColor = Colors.red.withAlpha(200);

    late final vec.Vector2 u;
    late final vec.Vector2 uUnit;
    late final vec.Vector2 vUnit;
    late final vec.Vector2 vUnit45;
    late final vec.Matrix2 m;

    final ColorScheme colorScheme = Theme.of(context).colorScheme;
    final TextTheme textTheme = Theme.of(context).textTheme;

    // get the unit
    switch (globalData.unit) {
      case 'metrics':
        unit = PreferredUnit.metrics;
        break;
      case 'imperial':
        unit = PreferredUnit.imperial;
        break;
      default:
        unit = PreferredUnit.metrics;
    }

    // double dimExtension = offsetDistance.abs() / 2;

    final Paint paint =
        Paint()
          ..color = fontColor
          ..strokeWidth = 1.0
          ..style = PaintingStyle.stroke
          ..strokeJoin = StrokeJoin.round;

    // line vector
    u = vec.Vector2(end.dx - start.dx, end.dy - start.dy);
    uUnit = u / u.length;

    // orthogonal vector
    vUnit = vec.Vector2(uUnit.y, uUnit.x); //rotate aniti-clockwise

    // Vector of parallel line with Offset
    Offset newStart = Offset(
      start.dx - vUnit.x * offsetDistance - uUnit.x * dimExtension,
      start.dy - vUnit.y * offsetDistance - uUnit.y * dimExtension,
    );
    Offset newEnd = Offset(
      end.dx - vUnit.x * offsetDistance + uUnit.x * dimExtension,
      end.dy - vUnit.y * offsetDistance + uUnit.y * dimExtension,
    );
    canvas.drawLine(newStart, newEnd, paint);

    // draw start crossing
    tempStart = Offset(
      (start.dx - vUnit.x * offsetDistance) - vUnit.x * dimExtension,
      (start.dy - vUnit.y * offsetDistance) - vUnit.y * dimExtension,
    );
    tempEnd = Offset(
      (start.dx - vUnit.x * offsetDistance) + vUnit.x * dimExtension,
      (start.dy - vUnit.y * offsetDistance) + vUnit.y * dimExtension,
    );
    canvas.drawLine(tempStart, tempEnd, paint);

    m = vec.Matrix2.rotation(-45 * math.pi / 180);
    vUnit45 = m * vUnit;
    tempStart = Offset(
      (start.dx - vUnit.x * offsetDistance) - vUnit45.x * dimExtension,
      (start.dy - vUnit.y * offsetDistance) - vUnit45.y * dimExtension,
    );
    tempEnd = Offset(
      (start.dx - vUnit.x * offsetDistance) + vUnit45.x * dimExtension,
      (start.dy - vUnit.y * offsetDistance) + vUnit45.y * dimExtension,
    );
    canvas.drawLine(tempStart, tempEnd, paint);

    // draw end crossing
    tempStart = Offset(
      (end.dx - vUnit.x * offsetDistance) - vUnit.x * dimExtension,
      (end.dy - vUnit.y * offsetDistance) - vUnit.y * dimExtension,
    );
    tempEnd = Offset(
      (end.dx - vUnit.x * offsetDistance) + vUnit.x * dimExtension,
      (end.dy - vUnit.y * offsetDistance) + vUnit.y * dimExtension,
    );
    canvas.drawLine(tempStart, tempEnd, paint);

    tempStart = Offset(
      (end.dx - vUnit.x * offsetDistance) - vUnit45.x * dimExtension,
      (end.dy - vUnit.y * offsetDistance) - vUnit45.y * dimExtension,
    );
    tempEnd = Offset(
      (end.dx - vUnit.x * offsetDistance) + vUnit45.x * dimExtension,
      (end.dy - vUnit.y * offsetDistance) + vUnit45.y * dimExtension,
    );
    canvas.drawLine(tempStart, tempEnd, paint);

    // indicate span length and beam size

    TextSpan textSpan = TextSpan(
      text: slabDepth.toStringAsFixed(0),
      style: TextStyle(
        fontSize: fontSize,
        color: fontColor,
        fontWeight: FontWeight.bold,
      ),
    );

    TextPainter textPainter = TextPainter(
      text: textSpan,
      textAlign: TextAlign.center,
      textDirection: TextDirection.ltr,
    )..layout();

    textCenter = Offset(
      (start.dx + end.dx) / 2 -
          vUnit.x * offsetDistance * 2 -
          uUnit.x * textPainter.width / 2,
      (start.dy + end.dy) / 2 -
          vUnit.y * offsetDistance * 2 -
          uUnit.y * textPainter.width / 2,
    );

    angle = math.atan2(end.dy - start.dy, end.dx - start.dx) * 180 / math.pi;

    canvas.save();
    canvas.translate(textCenter.dx, textCenter.dy);
    canvas.rotate(angle * math.pi / 180);
    textPainter.paint(canvas, Offset.zero);
    canvas.restore();
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    // TODO: implement shouldRepaint
    return false;
  }
}
