import 'dart:math';
// import 'dart:nativewrappers/_internal/vm/lib/math_patch.dart';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:structify/domain_layer/column_scheme_data.dart';
import 'package:structify/domain_layer/column_scheme_input.dart';
import 'package:structify/presentation_layer/small_elements/sketch/draw_column_scheme_circle.dart';
import 'package:structify/presentation_layer/small_elements/sketch/draw_column_scheme_square.dart';

//below for printing
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:printing/printing.dart';
// import 'dart:typed_data';
// import 'dart:ui';

//domain layer
import '../../domain_layer/global_data.dart';
import '../../domain_layer/mixin/mixin_tools_for_ui.dart';
import '../../domain_layer/preferences.dart';

// presentation layer
import '../screen/homescreen.dart';
// import '../small_elements/custom_stateful_double_input.dart';
// import '../small_elements/custom_stateful_text_input.dart';
// import '../small_elements/global_data_ui.dart';

//misc
import 'package:structify/domain_layer/column_scheme_input_global.dart';

import 'button/function_button.dart';
import 'button/selection_button.dart';

class ColumnSchemeSummary extends ConsumerStatefulWidget {
  const ColumnSchemeSummary({
    this.isExpanded,
    this.maxHeight,
    this.minHeight,
    super.key,
  });

  final bool? isExpanded;
  final double? maxHeight;
  final double? minHeight;

  @override
  ConsumerState<ConsumerStatefulWidget> createState() =>
      _ColumnSchemeSummaryState();
}

class _ColumnSchemeSummaryState extends ConsumerState<ColumnSchemeSummary>
    with WidgetsBindingObserver, MixinToolsForUI {
  late bool _isExpanded;
  late double _maxHeight;
  late double _minHeight;
  late ScrollController _scrollController;

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void initState() {
    WidgetsBinding.instance.addObserver(this);
    _isExpanded = widget.isExpanded ?? false;
    _maxHeight = widget.maxHeight ?? 300;
    _minHeight = widget.minHeight ?? 50;
    _scrollController = ScrollController();
    // _updateHeights();

    super.initState();
  }

  @override
  void didChangeDependencies() {
    _updateHeights();
    super.didChangeDependencies();
    // Update heights when dependencies change, including after initState
  }

  @override
  void didChangeMetrics() {
    // This method is called when the metrics change (like resizing the window)
    setState(() {
      _updateHeights();
    });
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    final loadingTables = ref.watch(loadingTablesControllerProvider);
    final globalData = ref.watch(globalDataControllerProvider);
    final columnInputs = ref.watch(columnSchemeInputControllerProvider);
    final columnInputsGlobal = ref.watch(
      columnSchemeInputGlobalControllerProvider,
    );
    final columnSchemes = ref.watch(columnSchemeDataControllerProvider);

    return loadingTables.when(
      data: (tables) {
        //start reutnr real stuffs when loading tables available
        return globalData.when(
          //also, start reutnr real stuffs when global data tables available
          data: (data) {
            return columnInputsGlobal.when(
              data: (inputsGlobal) {
                return columnInputs.when(
                  data: (inputs) {
                    return columnSchemes.when(
                      data: (schemes) {
                        late int indexMinScheme;
                        return Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Padding(
                              padding: const EdgeInsets.fromLTRB(10, 0, 10, 0),
                              child: Divider(),
                            ),
                            Padding(
                              padding: const EdgeInsets.fromLTRB(10, 0, 0, 0),
                              child: Align(
                                alignment: Alignment.centerLeft,
                                child: Wrap(
                                  crossAxisAlignment: WrapCrossAlignment.center,
                                  children: [
                                    IconButton(
                                      icon: Icon(
                                        _isExpanded
                                            ? Icons.expand_less
                                            : Icons.expand_more,
                                      ),
                                      color: colorScheme.onSurface,
                                      onPressed: () {
                                        setState(() {
                                          _isExpanded = !_isExpanded;
                                        });
                                      },
                                    ),
                                    Text(
                                      'RC Column Scheme Summary ',
                                      style: textTheme.titleLarge!.copyWith(
                                        color: colorScheme.onSurface,
                                      ),
                                    ),
                                    Container(
                                      decoration: BoxDecoration(
                                        borderRadius: BorderRadius.circular(
                                          5.0,
                                        ),
                                        color: colorScheme.surfaceContainer
                                            .withAlpha(225),
                                        border: Border.all(
                                          color: Colors.black.withAlpha(125),
                                        ),
                                      ),
                                      child: Padding(
                                        padding: const EdgeInsets.all(2.0),
                                        child: Text(
                                          '(Total: ${schemes.length})',
                                          style: textTheme.labelMedium!
                                              .copyWith(
                                                color: colorScheme
                                                    .onSurfaceVariant
                                                    .withAlpha(225),
                                              ),
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                            Padding(
                              padding: const EdgeInsets.fromLTRB(10, 0, 10, 0),
                              child: Divider(),
                            ),

                            ClipRect(
                              child: ConstrainedBox(
                                constraints: BoxConstraints(
                                  maxHeight: _isExpanded ? _maxHeight : 0,
                                ),
                                child: Column(
                                  children: [
                                    Builder(
                                      builder: (context) {
                                        if (schemes.isNotEmpty) {
                                          final maxTable = schemes.reduce((
                                            current,
                                            next,
                                          ) {
                                            int sizeComparison = current.size
                                                .compareTo(next.size);

                                            if (sizeComparison < 0) {
                                              return current; // Sort by size first
                                            } else if (sizeComparison > 0) {
                                              return next;
                                            } else {
                                              if (current.steelRatioSqaure <
                                                  next.steelRatioSqaure) {
                                                return current;
                                              } else if (current
                                                      .steelRatioSqaure >
                                                  next.steelRatioSqaure) {
                                                return next;
                                              } else {
                                                if (current.steelRatioCircle <
                                                    next.steelRatioCircle) {
                                                  return current;
                                                } else {
                                                  return next;
                                                }
                                              }
                                            }
                                            // If sizes are equal, sort by steel ratio

                                            // final currentSize = current.size;
                                            // final nextSize = next.size;
                                            // final currentRatioSqaure =
                                            //     current.steelRatioSqaure;
                                            // final nextRatioSqaure =
                                            //     next.steelRatioSqaure;

                                            // return currentSize <=
                                            //         nextSize
                                            //     ? current
                                            //     : next;
                                          });
                                          indexMinScheme = schemes.indexOf(
                                            maxTable,
                                          );

                                          late List<String> unit;
                                          switch (data.unit) {
                                            case 'metrics':
                                              unit = PreferredUnit.metrics;
                                              break;
                                            case 'imperial':
                                              unit = PreferredUnit.imperial;
                                              break;
                                            default:
                                              unit = PreferredUnit.metrics;
                                          }
                                          return DefaultTextStyle(
                                            style: textTheme.labelSmall!
                                                .copyWith(
                                                  color:
                                                      colorScheme
                                                          .onErrorContainer,
                                                ),
                                            child: Padding(
                                              padding:
                                                  const EdgeInsets.fromLTRB(
                                                    10,
                                                    0,
                                                    10,
                                                    0,
                                                  ),
                                              child: Container(
                                                decoration: BoxDecoration(
                                                  borderRadius:
                                                      BorderRadius.circular(
                                                        5.0,
                                                      ),
                                                  color: colorScheme
                                                      .errorContainer
                                                      .withAlpha(100),
                                                  border: Border.all(
                                                    width: 0.5,
                                                    color: colorScheme.error
                                                        .withAlpha(100),
                                                  ),
                                                ),
                                                child: Padding(
                                                  padding: const EdgeInsets.all(
                                                    4.0,
                                                  ),
                                                  child: Row(
                                                    mainAxisAlignment:
                                                        MainAxisAlignment.start,
                                                    mainAxisSize:
                                                        MainAxisSize.min,
                                                    children: [
                                                      Container(
                                                        decoration: BoxDecoration(
                                                          shape:
                                                              BoxShape.circle,
                                                          border: Border.all(
                                                            color:
                                                                colorScheme
                                                                    .onErrorContainer,
                                                            width: 1.0,
                                                          ),
                                                        ),
                                                        child: Padding(
                                                          padding:
                                                              const EdgeInsets.all(
                                                                8.0,
                                                              ),
                                                          child: Text(
                                                            '${indexMinScheme + 1}',
                                                            style: textTheme
                                                                .titleSmall!
                                                                .copyWith(
                                                                  color:
                                                                      colorScheme
                                                                          .onErrorContainer,
                                                                ),
                                                          ),
                                                        ),
                                                      ),
                                                      SizedBox(width: 15.0),
                                                      Column(
                                                        mainAxisSize:
                                                            MainAxisSize.min,
                                                        crossAxisAlignment:
                                                            CrossAxisAlignment
                                                                .start,
                                                        children: [
                                                          Text(
                                                            'Minimal Scheme',
                                                            style: textTheme
                                                                .labelMedium!
                                                                .copyWith(
                                                                  color:
                                                                      colorScheme
                                                                          .error,
                                                                ),
                                                          ),
                                                          Text(
                                                            'Size: ${NumberFormat('0').format(maxTable.size)} [${unit[4]}]',
                                                          ),
                                                          Text(
                                                            'Size: ${NumberFormat('0').format(maxTable.fcu)} [${unit[5]}]',
                                                          ),
                                                          Text(
                                                            'SLS Load: ${NumberFormat('0').format(maxTable.slsLoad)} [${unit[0]}]',
                                                          ),
                                                          Text(
                                                            'ULS Load: ${NumberFormat('0').format(maxTable.ulsLoad)} [${unit[0]}]',
                                                          ),
                                                        ],
                                                      ),
                                                      SizedBox(width: 15.0),
                                                      Column(
                                                        mainAxisSize:
                                                            MainAxisSize.min,
                                                        crossAxisAlignment:
                                                            CrossAxisAlignment
                                                                .start,
                                                        children: [
                                                          Container(
                                                            decoration: BoxDecoration(
                                                              borderRadius:
                                                                  BorderRadius.circular(
                                                                    5.0,
                                                                  ),
                                                              color: colorScheme
                                                                  .error
                                                                  .withAlpha(
                                                                    100,
                                                                  ),
                                                            ),
                                                            child: Padding(
                                                              padding:
                                                                  const EdgeInsets.all(
                                                                    3.0,
                                                                  ),
                                                              child: Text(
                                                                'Square Scheme',
                                                                style: textTheme
                                                                    .labelSmall!
                                                                    .copyWith(
                                                                      color:
                                                                          colorScheme
                                                                              .onError,
                                                                    ),
                                                              ),
                                                            ),
                                                          ),
                                                          Text(
                                                            'Capacity: ${NumberFormat('0').format(maxTable.axialCapacitySquare)} [${unit[0]}]',
                                                          ),
                                                          Text(
                                                            'Rebar: ${maxTable.mainBarSquare} ',
                                                          ),
                                                          Text(
                                                            'Steel Ratio: ${NumberFormat('0.000').format(maxTable.steelRatioSqaure * 100)}\u0025',
                                                          ),
                                                        ],
                                                      ),
                                                      SizedBox(width: 15.0),
                                                      Column(
                                                        mainAxisSize:
                                                            MainAxisSize.min,
                                                        crossAxisAlignment:
                                                            CrossAxisAlignment
                                                                .start,
                                                        children: [
                                                          Container(
                                                            decoration: BoxDecoration(
                                                              borderRadius:
                                                                  BorderRadius.circular(
                                                                    5.0,
                                                                  ),
                                                              color: colorScheme
                                                                  .error
                                                                  .withAlpha(
                                                                    100,
                                                                  ),
                                                            ),
                                                            child: Padding(
                                                              padding:
                                                                  const EdgeInsets.all(
                                                                    3.0,
                                                                  ),
                                                              child: Text(
                                                                'Circular Scheme',
                                                                style: textTheme
                                                                    .labelSmall!
                                                                    .copyWith(
                                                                      color:
                                                                          colorScheme
                                                                              .onError,
                                                                    ),
                                                              ),
                                                            ),
                                                          ),
                                                          Text(
                                                            'Capacity: ${NumberFormat('0').format(maxTable.axialCapacityCircle)} [${unit[0]}]',
                                                          ),
                                                          Text(
                                                            'Rebar: ${maxTable.mainBarCircle} ',
                                                          ),
                                                          Text(
                                                            'Steel Ratio: ${NumberFormat('0.000').format(maxTable.steelRatioCircle * 100)}\u0025',
                                                          ),
                                                        ],
                                                      ),
                                                    ],
                                                  ),
                                                ),
                                              ),
                                            ),
                                          );
                                        } else {
                                          return Align(
                                            child: Padding(
                                              padding:
                                                  const EdgeInsets.fromLTRB(
                                                    10,
                                                    0,
                                                    10,
                                                    0,
                                                  ),
                                              child: Container(
                                                decoration: BoxDecoration(
                                                  borderRadius:
                                                      BorderRadius.circular(
                                                        5.0,
                                                      ),
                                                  color: colorScheme
                                                      .errorContainer
                                                      .withAlpha(100),
                                                  border: Border.all(
                                                    width: 0.5,
                                                    color: colorScheme.error
                                                        .withAlpha(100),
                                                  ),
                                                ),
                                                child: Padding(
                                                  padding: const EdgeInsets.all(
                                                    4.0,
                                                  ),
                                                  child: Text(
                                                    'No Column Scheme Data',
                                                    style: textTheme
                                                        .labelMedium!
                                                        .copyWith(
                                                          color:
                                                              colorScheme.error,
                                                        ),
                                                  ),
                                                ),
                                              ),
                                            ),
                                          );
                                        }
                                      },
                                    ),
                                    SizedBox(height: 10),
                                    Flexible(
                                      child: Scrollbar(
                                        controller: _scrollController,
                                        thumbVisibility: true,
                                        child: DefaultTextStyle(
                                          style: textTheme.labelSmall!.copyWith(
                                            color: colorScheme.onSurface,
                                          ),
                                          child: ListView.builder(
                                            controller: _scrollController,
                                            itemCount: schemes.length,
                                            itemBuilder: (context, index) {
                                              late List<String> unit;
                                              switch (data.unit) {
                                                case 'metrics':
                                                  unit = PreferredUnit.metrics;
                                                  break;
                                                case 'imperial':
                                                  unit = PreferredUnit.imperial;
                                                  break;
                                                default:
                                                  unit = PreferredUnit.metrics;
                                              }

                                              return Padding(
                                                padding:
                                                    const EdgeInsets.fromLTRB(
                                                      8.0,
                                                      4.0,
                                                      8.0,
                                                      4.0,
                                                    ),
                                                child: Row(
                                                  children: [
                                                    Column(
                                                      crossAxisAlignment:
                                                          CrossAxisAlignment
                                                              .center,
                                                      children: [
                                                        FunctionButton(
                                                          labelText:
                                                              ' ${index + 1} ',
                                                          pressedColor:
                                                              colorScheme
                                                                  .tertiary
                                                                  .withAlpha(
                                                                    150,
                                                                  ),
                                                          onTap: (value) async {
                                                            final schemes =
                                                                await ref.read(
                                                                  columnSchemeDataControllerProvider
                                                                      .future,
                                                                );
                                                            await ref
                                                                .read(
                                                                  columnSchemeDataControllerProvider
                                                                      .notifier,
                                                                )
                                                                .toggleSelectScheme(
                                                                  schemes[index]
                                                                      .columnSchemeDataId,
                                                                );
                                                          },
                                                        ),
                                                        SizedBox(height: 5.0),

                                                        SelectionButton(
                                                          labelIcon: Icon(
                                                            Icons.check,
                                                            color:
                                                                schemes[index]
                                                                        .isSelected
                                                                    ? colorScheme
                                                                        .onTertiary
                                                                    : colorScheme
                                                                        .onSurface
                                                                        .withAlpha(
                                                                          100,
                                                                        ),
                                                          ),
                                                          labelText: '',
                                                          // pressedColor:
                                                          //     colorScheme.tertiary,
                                                          bgColor:
                                                              schemes[index]
                                                                      .isSelected
                                                                  ? colorScheme
                                                                      .tertiary
                                                                  : colorScheme
                                                                      .surfaceContainer
                                                                      .withAlpha(
                                                                        100,
                                                                      ),

                                                          onTap: (value) async {
                                                            final colSchemes =
                                                                await ref.read(
                                                                  columnSchemeDataControllerProvider
                                                                      .future,
                                                                );
                                                            await ref
                                                                .read(
                                                                  columnSchemeDataControllerProvider
                                                                      .notifier,
                                                                )
                                                                .toggleSelectScheme(
                                                                  colSchemes[index]
                                                                      .columnSchemeDataId,
                                                                );
                                                            final newColSchemes =
                                                                await ref.read(
                                                                  columnSchemeDataControllerProvider
                                                                      .future,
                                                                );
                                                            if (newColSchemes[index]
                                                                .isSelected) {
                                                              final removedTable =
                                                                  newColSchemes
                                                                      .removeAt(
                                                                        index,
                                                                      );
                                                              newColSchemes
                                                                  .insert(
                                                                    0,
                                                                    removedTable,
                                                                  );
                                                              await ref
                                                                  .read(
                                                                    columnSchemeDataControllerProvider
                                                                        .notifier,
                                                                  )
                                                                  .replaceEntireTable(
                                                                    newColSchemes,
                                                                  );
                                                              if (mounted) {
                                                                showSlidingFadingMessage(
                                                                  context,
                                                                  'Selected column scheme  (original index: ${index + 1}) pushed to top',
                                                                );
                                                              }
                                                            }
                                                          },
                                                        ),
                                                        SizedBox(height: 5.0),
                                                        FunctionButton(
                                                          key: ValueKey(
                                                            'columnSchemeId_${schemes[index].columnSchemeDataId}',
                                                          ),
                                                          labelText:
                                                              'Show Cals',
                                                          pressedColor:
                                                              colorScheme
                                                                  .tertiary
                                                                  .withAlpha(
                                                                    150,
                                                                  ),
                                                          onTap: (value) async {
                                                            final globalData =
                                                                await ref.read(
                                                                  globalDataControllerProvider
                                                                      .future,
                                                                );
                                                            _presentCalsRecord(
                                                              globalData,
                                                              unit,
                                                              index,
                                                            );
                                                          },
                                                        ),
                                                      ],
                                                    ),
                                                    IconButton(
                                                      icon: Icon(
                                                        Icons.delete,
                                                        color:
                                                            colorScheme
                                                                .onSurface,
                                                      ),
                                                      onPressed: () {
                                                        ref
                                                            .read(
                                                              columnSchemeDataControllerProvider
                                                                  .notifier,
                                                            )
                                                            .deleteTable(
                                                              schemes[index]
                                                                  .columnSchemeDataId,
                                                            );
                                                      },
                                                    ),
                                                    Flexible(
                                                      child: Row(
                                                        mainAxisAlignment:
                                                            MainAxisAlignment
                                                                .start,
                                                        mainAxisSize:
                                                            MainAxisSize.min,
                                                        children: [
                                                          SizedBox(width: 15.0),
                                                          Column(
                                                            mainAxisSize:
                                                                MainAxisSize
                                                                    .min,
                                                            crossAxisAlignment:
                                                                CrossAxisAlignment
                                                                    .start,
                                                            children: [
                                                              Container(
                                                                decoration: BoxDecoration(
                                                                  borderRadius:
                                                                      BorderRadius.circular(
                                                                        5.0,
                                                                      ),
                                                                  color: colorScheme
                                                                      .primaryContainer
                                                                      .withAlpha(
                                                                        200,
                                                                      ),
                                                                ),
                                                                child: Padding(
                                                                  padding:
                                                                      const EdgeInsets.all(
                                                                        3.0,
                                                                      ),
                                                                  child: Text(
                                                                    'Size: ${NumberFormat('0').format(schemes[index].size)}',
                                                                    style: textTheme
                                                                        .labelMedium!
                                                                        .copyWith(
                                                                          color:
                                                                              colorScheme.onPrimaryContainer,
                                                                        ),
                                                                  ),
                                                                ),
                                                              ),
                                                              SizedBox(
                                                                height: 2.0,
                                                              ),
                                                              Container(
                                                                decoration: BoxDecoration(
                                                                  borderRadius:
                                                                      BorderRadius.circular(
                                                                        5.0,
                                                                      ),
                                                                  color: colorScheme
                                                                      .secondaryContainer
                                                                      .withAlpha(
                                                                        200,
                                                                      ),
                                                                ),
                                                                child: Padding(
                                                                  padding:
                                                                      const EdgeInsets.all(
                                                                        3.0,
                                                                      ),
                                                                  child: Text(
                                                                    'fcu: ${NumberFormat('0').format(schemes[index].fcu)} [${unit[5]}]',
                                                                    style: textTheme
                                                                        .labelMedium!
                                                                        .copyWith(
                                                                          color:
                                                                              colorScheme.onSecondaryContainer,
                                                                        ),
                                                                  ),
                                                                ),
                                                              ),
                                                              Text(
                                                                'SLS Load: ${NumberFormat('0').format(schemes[index].slsLoad)} [${unit[0]}]',
                                                              ),
                                                              Text(
                                                                'ULS Load: ${NumberFormat('0').format(schemes[index].ulsLoad)} [${unit[0]}]',
                                                              ),
                                                            ],
                                                          ),
                                                          SizedBox(width: 15.0),
                                                          Column(
                                                            mainAxisSize:
                                                                MainAxisSize
                                                                    .min,
                                                            crossAxisAlignment:
                                                                CrossAxisAlignment
                                                                    .start,
                                                            children: [
                                                              Container(
                                                                decoration: BoxDecoration(
                                                                  borderRadius:
                                                                      BorderRadius.circular(
                                                                        5.0,
                                                                      ),
                                                                  color: colorScheme
                                                                      .primary
                                                                      .withAlpha(
                                                                        100,
                                                                      ),
                                                                ),
                                                                child: Padding(
                                                                  padding:
                                                                      const EdgeInsets.all(
                                                                        3.0,
                                                                      ),
                                                                  child: Text(
                                                                    'Square Scheme',
                                                                    style: textTheme
                                                                        .labelSmall!
                                                                        .copyWith(
                                                                          color:
                                                                              colorScheme.onPrimary,
                                                                        ),
                                                                  ),
                                                                ),
                                                              ),
                                                              Text(
                                                                'Capacity: ${NumberFormat('0').format(schemes[index].axialCapacitySquare)} [${unit[0]}]',
                                                              ),
                                                              Text(
                                                                'Rebar: ${schemes[index].mainBarSquare} ',
                                                              ),
                                                              Text(
                                                                'Steel Ratio: ${NumberFormat('0.000').format(schemes[index].steelRatioSqaure * 100)}\u0025',
                                                              ),
                                                            ],
                                                          ),
                                                          SizedBox(width: 15.0),
                                                          Column(
                                                            mainAxisSize:
                                                                MainAxisSize
                                                                    .min,
                                                            crossAxisAlignment:
                                                                CrossAxisAlignment
                                                                    .start,
                                                            children: [
                                                              Container(
                                                                decoration: BoxDecoration(
                                                                  borderRadius:
                                                                      BorderRadius.circular(
                                                                        5.0,
                                                                      ),
                                                                  color: colorScheme
                                                                      .secondary
                                                                      .withAlpha(
                                                                        100,
                                                                      ),
                                                                ),
                                                                child: Padding(
                                                                  padding:
                                                                      const EdgeInsets.all(
                                                                        3.0,
                                                                      ),
                                                                  child: Text(
                                                                    'Circular Scheme',
                                                                    style: textTheme
                                                                        .labelSmall!
                                                                        .copyWith(
                                                                          color:
                                                                              colorScheme.onSecondary,
                                                                        ),
                                                                  ),
                                                                ),
                                                              ),
                                                              Text(
                                                                'Capacity: ${NumberFormat('0').format(schemes[index].axialCapacityCircle)} [${unit[0]}]',
                                                              ),
                                                              Text(
                                                                'Rebar: ${schemes[index].mainBarCircle} ',
                                                              ),
                                                              Text(
                                                                'Steel Ratio: ${NumberFormat('0.000').format(schemes[index].steelRatioCircle * 100)}\u0025',
                                                              ),
                                                            ],
                                                          ),
                                                          SizedBox(width: 5.0),
                                                          GestureDetector(
                                                            onTap: () async {
                                                              await showDialog(
                                                                context:
                                                                    context,
                                                                builder: (
                                                                  context,
                                                                ) {
                                                                  return Dialog(
                                                                    backgroundColor:
                                                                        colorScheme
                                                                            .surfaceContainer,
                                                                    child: SizedBox(
                                                                      width:
                                                                          550,
                                                                      height:
                                                                          550,
                                                                      child: DrawColumnSchemeSquare(
                                                                        sketchWidth:
                                                                            500,
                                                                        sketchHeight:
                                                                            500,
                                                                        index:
                                                                            index,
                                                                      ),
                                                                    ),
                                                                  );
                                                                },
                                                              );
                                                            },
                                                            child:
                                                                DrawColumnSchemeSquare(
                                                                  sketchWidth:
                                                                      125,
                                                                  sketchHeight:
                                                                      125,
                                                                  index: index,
                                                                ),
                                                          ),
                                                          SizedBox(width: 5.0),
                                                          GestureDetector(
                                                            onTap: () async {
                                                              await showDialog(
                                                                context:
                                                                    context,
                                                                builder: (
                                                                  context,
                                                                ) {
                                                                  final GlobalData
                                                                  globalData =
                                                                      data;

                                                                  return Dialog(
                                                                    backgroundColor:
                                                                        colorScheme
                                                                            .surfaceContainer,
                                                                    child: SizedBox(
                                                                      width:
                                                                          550,
                                                                      height:
                                                                          550,
                                                                      child: DrawColumnSchemeCircle(
                                                                        sketchWidth:
                                                                            500,
                                                                        sketchHeight:
                                                                            500,
                                                                        index:
                                                                            index,
                                                                      ),
                                                                    ),
                                                                  );
                                                                },
                                                              );
                                                            },
                                                            child:
                                                                DrawColumnSchemeCircle(
                                                                  sketchWidth:
                                                                      125,
                                                                  sketchHeight:
                                                                      125,
                                                                  index: index,
                                                                ),
                                                          ),
                                                        ],
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              );
                                            },
                                          ),
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ],
                        );
                      },
                      error: (error, stackTrace) => Text(error.toString()),
                      loading: () => CircularProgressIndicator(),
                    );
                  },
                  error: (error, stackTrace) => Text(error.toString()),
                  loading: () => CircularProgressIndicator(),
                );
              },
              error: (error, stackTrace) => Text(error.toString()),
              loading: () => CircularProgressIndicator(),
            );
          },
          error: (error, stackTrace) => Text(error.toString()),
          loading: () => CircularProgressIndicator(),
        );
      },
      error: (error, stackTrace) => Text(error.toString()),
      loading: () => CircularProgressIndicator(),
    );
  }

  void _updateHeights() {
    final double screenHeight = MediaQuery.of(context).size.height;
    _maxHeight =
        (widget.maxHeight == null)
            ? screenHeight * 0.6
            : (widget.maxHeight! > screenHeight * 0.6)
            ? screenHeight * 0.6
            : widget.maxHeight!;

    _minHeight =
        (widget.minHeight == null)
            ? screenHeight * 0.1
            : (widget.minHeight! > screenHeight * 0.1)
            ? screenHeight * 0.1
            : widget.minHeight!;

    // Adjust _maxHeight if needed

    if (_maxHeight < _minHeight) {
      _maxHeight = _minHeight;
    }
  }

  Future<void> _presentCalsRecord(
    GlobalData globalData,
    List<String> unit,
    int index,
  ) async {
    List<ColumnSchemeData> schemes = await ref.read(
      columnSchemeDataControllerProvider.future,
    );
    final result = extractCalsLog(schemes[index].calsLog);
    final StringBuffer columnCals = await _writeResult(
      result, // input the main beam result
      index,
      unit,
    );
    if (mounted) {
      await showDialog(
        context: context,
        builder: (context) {
          ColorScheme colorScheme = Theme.of(context).colorScheme;
          TextTheme textTheme = Theme.of(context).textTheme;
          Color bgColor = colorScheme.surfaceContainer;
          Color onBgColor = colorScheme.onSurface;
          Color titleBgColor = colorScheme.primary.withAlpha(150);
          Color titleOnBgColor = colorScheme.onPrimary;
          TextStyle titleTextStyle = textTheme.labelSmall!.copyWith(
            color: titleOnBgColor,
          );
          ScrollController scrollController = ScrollController();

          late List<String> unit;
          switch (globalData.unit) {
            case 'metrics':
              unit = PreferredUnit.metrics;
              break;
            case 'imperial':
              unit = PreferredUnit.imperial;
              break;
            default:
              unit = PreferredUnit.metrics;
          }
          const double maxH = 600;
          const double maxW = 600;

          NumberFormat f0 = NumberFormat('0');
          return Dialog(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(15),
            ),
            backgroundColor: bgColor,
            child: ConstrainedBox(
              constraints: const BoxConstraints(
                maxWidth: maxW,
                maxHeight: maxH,
              ),
              child: Column(
                children: [
                  Align(
                    alignment: Alignment.centerRight,
                    child: Padding(
                      padding: const EdgeInsets.fromLTRB(0, 10, 10, 0),
                      child: FunctionButton(
                        bgColor: titleBgColor,
                        labelTextColor: titleOnBgColor,
                        labelIcon: Icon(
                          Icons.print_outlined,
                          color: titleOnBgColor.withAlpha(175),
                        ),
                        labelText: '',
                        onTap: (value) {
                          exportListToPdf(
                            context,
                            ref,
                            columnCals.toString().split('\n'),
                          );
                        },
                      ),
                    ),
                  ),

                  Scrollbar(
                    controller: scrollController,
                    thumbVisibility: true,
                    trackVisibility: true,
                    child: ConstrainedBox(
                      constraints: const BoxConstraints(
                        maxWidth: maxW,
                        maxHeight: maxH - 70,
                      ),
                      child: SingleChildScrollView(
                        controller: scrollController,
                        child: Padding(
                          padding: const EdgeInsets.fromLTRB(25.0, 5, 25.0, 5),
                          child: DefaultTextStyle(
                            style: textTheme.labelMedium!.copyWith(
                              color: onBgColor,
                            ),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                Container(
                                  decoration: BoxDecoration(
                                    color: colorScheme.tertiary,
                                    shape: BoxShape.circle,
                                    border: Border.all(color: titleOnBgColor),
                                  ),
                                  child: Padding(
                                    padding: const EdgeInsets.all(12.0),
                                    child: Text(
                                      '${index + 1}',
                                      style: titleTextStyle.copyWith(
                                        fontSize: 24,
                                        color: colorScheme.onTertiary,
                                      ),
                                    ),
                                  ),
                                ),
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Container(
                                      decoration: BoxDecoration(
                                        color: titleBgColor,
                                        shape: BoxShape.rectangle,
                                        borderRadius: BorderRadius.circular(
                                          10.0,
                                        ),
                                        border: Border.all(
                                          color: titleOnBgColor,
                                        ),
                                      ),
                                      child: Padding(
                                        padding: const EdgeInsets.all(4.0),
                                        child: Text(
                                          'Size [${unit[4]}] :\n${f0.format(schemes[index].size)}',
                                          style: titleTextStyle,
                                        ),
                                      ),
                                    ),
                                    SizedBox(width: 5),
                                    Container(
                                      decoration: BoxDecoration(
                                        color: titleBgColor,
                                        shape: BoxShape.rectangle,
                                        borderRadius: BorderRadius.circular(
                                          10.0,
                                        ),
                                        border: Border.all(
                                          color: titleOnBgColor,
                                        ),
                                      ),
                                      child: Padding(
                                        padding: const EdgeInsets.all(4.0),
                                        child: Text(
                                          'fcu [${unit[5]}]:\n${f0.format(schemes[index].fcu)} ',
                                          style: titleTextStyle,
                                        ),
                                      ),
                                    ),

                                    SizedBox(width: 5),
                                    Container(
                                      decoration: BoxDecoration(
                                        color: titleBgColor,
                                        shape: BoxShape.rectangle,
                                        borderRadius: BorderRadius.circular(
                                          10.0,
                                        ),
                                        border: Border.all(
                                          color: titleOnBgColor,
                                        ),
                                      ),
                                      child: Padding(
                                        padding: const EdgeInsets.all(4.0),
                                        child: Text(
                                          'SLS Load [${unit[0]}]:\n${f0.format(schemes[index].slsLoad)}',
                                          style: titleTextStyle,
                                        ),
                                      ),
                                    ),
                                    SizedBox(width: 5),
                                    Container(
                                      decoration: BoxDecoration(
                                        color: titleBgColor,
                                        shape: BoxShape.rectangle,
                                        borderRadius: BorderRadius.circular(
                                          10.0,
                                        ),
                                        border: Border.all(
                                          color: titleOnBgColor,
                                        ),
                                      ),
                                      child: Padding(
                                        padding: const EdgeInsets.all(4.0),
                                        child: Text(
                                          'ULS Load [${unit[0]}]:\n${f0.format(schemes[index].ulsLoad)}',
                                          style: titleTextStyle,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                                SizedBox(height: 5),
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Container(
                                      decoration: BoxDecoration(
                                        color: titleBgColor,
                                        shape: BoxShape.rectangle,
                                        borderRadius: BorderRadius.circular(
                                          10.0,
                                        ),
                                        border: Border.all(
                                          color: titleOnBgColor,
                                        ),
                                      ),
                                      child: Padding(
                                        padding: const EdgeInsets.all(4.0),
                                        child: Text(
                                          'Capacity (Square) [${unit[0]}]:\n${f0.format(schemes[index].axialCapacitySquare)} ',
                                          style: titleTextStyle,
                                        ),
                                      ),
                                    ),
                                    SizedBox(width: 5),
                                    Container(
                                      decoration: BoxDecoration(
                                        color: titleBgColor,
                                        shape: BoxShape.rectangle,
                                        borderRadius: BorderRadius.circular(
                                          10.0,
                                        ),
                                        border: Border.all(
                                          color: titleOnBgColor,
                                        ),
                                      ),
                                      child: Padding(
                                        padding: const EdgeInsets.all(4.0),
                                        child: Text(
                                          'Capacity (Circular) [${unit[0]}]:\n${f0.format(schemes[index].axialCapacityCircle)} ',
                                          style: titleTextStyle,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                                SizedBox(height: 5),
                                Container(
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(10.0),
                                    border: Border.all(color: onBgColor),
                                  ),
                                  child: Padding(
                                    padding: const EdgeInsets.all(8.0),
                                    child: Text(columnCals.toString()),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      );
    }
    ;
  }

  Future<StringBuffer> _writeResult(
    Map<String, List> result,
    int index,
    List<String> unit,
  ) async {
    List<ColumnSchemeData> schemes = await ref.read(
      columnSchemeDataControllerProvider.future,
    );
    List<ColumnSchemeInput> inputs = await ref.read(
      columnSchemeInputControllerProvider.future,
    );

    StringBuffer buffer = StringBuffer();
    ColumnSchemeData scheme = schemes[index];
    double tempDouble = 0.0, tempDouble2 = 0.0, tempDouble3 = 0.0;
    int tempInt = 0;
    String tempString = '', usage;
    NumberFormat f0 = NumberFormat('0');
    NumberFormat f3 = NumberFormat('0.000');

    buffer.write('------------------------------\n');
    tempDouble = getDoubleValue(result, 'Size');
    buffer.write('Column Size:  ');
    buffer.write('${NumberFormat('0').format(tempDouble)} [${unit[4]}]\n');
    buffer.write('------------------------------\n');
    buffer.write('*******************\n');
    buffer.write('Loading\n');
    buffer.write('*******************\n');

    for (int i = 0; i < inputs.length; i++) {
      usage = inputs[i].usage;
      buffer.write('(${i + 1}) >> ');
      buffer.write('Usgae: $usage | ');
      buffer.write(
        'Load Area: ${f0.format(getDoubleValue(result, 'LoadArea (${i + 1})'))} [${unit[7]}] | ',
      );
      buffer.write(
        'SLS : ${f0.format(getDoubleValue(result, 'SLS Load (${i + 1})'))} [${unit[0]}] | ',
      );
      buffer.write(
        'ULS : ${f0.format(getDoubleValue(result, 'ULS Load (${i + 1})'))} [${unit[0]}]\n',
      );
    }
    buffer.write('------------------------------\n');
    buffer.write('Total Load\n');
    buffer.write(
      'SLS Total: ${f0.format(getDoubleValue(result, 'SLS Total'))} [${unit[0]}]\n',
    );
    buffer.write(
      'ULS Total: ${f0.format(getDoubleValue(result, 'ULS Total'))} [${unit[0]}]\n',
    );
    buffer.write('------------------------------\n');
    buffer.write('*******************\n');
    buffer.write('Capacity\n');
    buffer.write('*******************\n');
    buffer.write('Pc\n= 0.35fcuAc + 0.67fyAs\n');
    buffer.write('------------------------------\n');
    buffer.write('Square Column\n');
    buffer.write('------------------------------\n');
    buffer.write(
      '${scheme.mainBarSquare} (steel ratio:${f3.format(scheme.steelRatioSqaure * 100)}\u0025)\n',
    );

    buffer.write(
      '= 0.35 x ${f0.format(scheme.fcu)} x ${f0.format(scheme.size)}^2 x (1-${f3.format(scheme.steelRatioSqaure)})',
    );
    buffer.write(
      ' + 0.67 x 500 x ${f0.format(scheme.size)}^2 x ${f3.format(scheme.steelRatioSqaure)}\n',
    );
    buffer.write('= ${f0.format(scheme.axialCapacitySquare)} [${unit[0]}]\n');
    buffer.write('------------------------------\n');
    buffer.write('Circular Column\n');
    buffer.write('------------------------------\n');
    buffer.write(
      '${scheme.mainBarCircle} (steel ratio: ${f3.format(scheme.steelRatioCircle * 100)}\u0025)\n',
    );

    buffer.write(
      '= 0.35 x ${f0.format(scheme.fcu)} x ${f0.format(scheme.size)}^2 x ${'\u03C0'}/4 x (1-${f3.format(scheme.steelRatioCircle)})',
    );
    buffer.write(
      ' + 0.67 x 500 x ${f0.format(scheme.size)}^2 x ${'\u03C0'}/4 x${f3.format(scheme.steelRatioCircle)}\n',
    );
    buffer.write('= ${f0.format(scheme.axialCapacityCircle)} [${unit[0]}]\n');

    return buffer;
  }
}
