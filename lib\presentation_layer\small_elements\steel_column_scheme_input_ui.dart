import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:structify/misc/show_overlay_msg.dart';
import 'package:structify/presentation_layer/small_elements/input/custom_stateful_int_input.dart';

//domain layer
import '../../domain_layer/preferences.dart';

// presentation layer
import '../screen/homescreen.dart';
import 'input/custom_stateful_double_input.dart';
import 'input/custom_stateful_text_input.dart';
import 'input/custom_stateful_dropList.dart';
import 'button/function_button.dart';
import 'popup/multiseleciton_dialog.dart';
// import '../small_elements/global_data_ui.dart';
// import '../small_elements/loadcals_summary_ui.dart';

class SteelColumnSchemeInputUi extends ConsumerStatefulWidget {
  const SteelColumnSchemeInputUi({
    this.isExpanded,
    this.maxHeight,
    this.minHeight,
    super.key,
  });

  final bool? isExpanded;
  final double? maxHeight;
  final double? minHeight;

  @override
  ConsumerState<ConsumerStatefulWidget> createState() =>
      _SteelColumnSchemeInputUiState();
}

class _SteelColumnSchemeInputUiState
    extends ConsumerState<SteelColumnSchemeInputUi>
    with WidgetsBindingObserver {
  late bool _isExpanded;
  late double _maxHeight;
  late double _minHeight;
  late CustomStatefulTextInput _usageInput;
  late GlobalKey _buttonKey;
  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void initState() {
    WidgetsBinding.instance.addObserver(this);
    _isExpanded = widget.isExpanded ?? false;
    _maxHeight = widget.maxHeight ?? 300;
    _minHeight = widget.minHeight ?? 50;
    _usageInput = CustomStatefulTextInput();
    // _updateHeights();
    _buttonKey = GlobalKey();
    super.initState();
  }

  @override
  void didChangeDependencies() {
    _updateHeights();
    super.didChangeDependencies();
    // Update heights when dependencies change, including after initState
  }

  @override
  void didChangeMetrics() {
    // This method is called when the metrics change (like resizing the window)
    setState(() {
      _updateHeights();
    });
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    final globalData = ref.watch(globalDataControllerProvider);
    final loadingTables = ref.watch(loadingTablesControllerProvider);
    final steelColumnSchemeInput = ref.watch(
      steelColumnSchemeInputControllerProvider,
    );
    final steelColumnSchemeInputGlobal = ref.watch(
      steelColumnSchemeInputGlobalControllerProvider,
    );
    final scrollController = ScrollController();

    return globalData.when(
      data: (data) {
        late final List<String> units;
        switch (data.unit) {
          case 'metrics':
            units = PreferredUnit.metrics;
            break;
          case 'imperial':
            units = PreferredUnit.imperial;
            break;
          default:
            units = PreferredUnit.metrics;
            break;
        }

        return loadingTables.when(
          data: (tables) {
            return steelColumnSchemeInput.when(
              data: (input) {
                return steelColumnSchemeInputGlobal.when(
                  data: (inputGlobal) {
                    return Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Padding(
                          padding: const EdgeInsets.fromLTRB(10, 0, 10, 0),
                          child: Divider(),
                        ),
                        Padding(
                          padding: const EdgeInsets.fromLTRB(10, 0, 0, 0),
                          child: Align(
                            alignment: Alignment.centerLeft,
                            child: Wrap(
                              crossAxisAlignment: WrapCrossAlignment.center,
                              children: [
                                Wrap(
                              crossAxisAlignment: WrapCrossAlignment.center,
                                  children: [
                                    IconButton(
                                      icon: Icon(
                                        _isExpanded
                                            ? Icons.keyboard_arrow_up
                                            : Icons.keyboard_arrow_down,
                                        color: colorScheme.onSurface,
                                      ),
                                      onPressed: () {
                                        setState(() {
                                          _isExpanded = !_isExpanded;
                                        });
                                      },
                                    ),
                                    Text(
                                      'Steel Column Scheme Inputs ',
                                      style: textTheme.titleLarge!.copyWith(
                                        color: colorScheme.onSurface,
                                      ),
                                    ),
                                  ],
                                ),
                                Container(
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(5.0),
                                    color: colorScheme.surfaceContainer
                                        .withAlpha(225),
                                    border: Border.all(
                                      color: Colors.black.withAlpha(125),
                                    ),
                                  ),
                                  child: Padding(
                                    padding: const EdgeInsets.all(2.0),
                                    child: Text(
                                      '(Total: ${input.length})',
                                      style: textTheme.labelMedium!.copyWith(
                                        color: colorScheme.onSurfaceVariant
                                            .withAlpha(225),
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                        Padding(
                          padding: const EdgeInsets.fromLTRB(10, 0, 10, 0),
                          child: Divider(),
                        ),
                        SizedBox(height: 10),
                        Padding(
                          padding: const EdgeInsets.fromLTRB(
                            8.0,
                            4.0,
                            8.0,
                            4.0,
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                              Flexible(
                                child: CustomStatefulDoubleInput(
                                  title: 'Unbraced Length [${units[5]}]',
                                  value: inputGlobal.unbracedLength,
                                  onChanged: (value) {
                                    ref
                                        .read(
                                          steelColumnSchemeInputGlobalControllerProvider
                                              .notifier,
                                        )
                                        .updateTable(unbracedLength: value);
                                  },
                                ),
                              ),
                              SizedBox(width: 10.0),
                              MultiSelectionDialog(
                                dialogWidth: 50.0,
                                dialogHeight: 400.0,
                                label: 'Grade',
                                selectedOptions: inputGlobal.steelGrade.split(
                                  ',',
                                ),
                                options: MaterialProps.steelGrade,
                                onPressed: (selectedOptions) async {
                                  final String steelGrade = selectedOptions
                                      .join(',');
                                  if (steelGrade.isNotEmpty) {
                                    await ref
                                        .read(
                                          steelColumnSchemeInputGlobalControllerProvider
                                              .notifier,
                                        )
                                        .updateTable(steelGrade: steelGrade);
                                  } else {
                                    //default to be S355 only
                                    await ref
                                        .read(
                                          steelColumnSchemeInputGlobalControllerProvider
                                              .notifier,
                                        )
                                        .updateTable(steelGrade: "S355");
                                  }
                                },
                              ),
                              Flexible(child: SizedBox(width: 5.0)),
                              // Flexible(child: SizedBox(width: 5.0)),
                            ],
                          ),
                        ),
                        SizedBox(height: 10),
                        Padding(
                          padding: const EdgeInsets.fromLTRB(
                            8.0,
                            4.0,
                            8.0,
                            4.0,
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                              FunctionButton(
                                labelIcon: Icon(Icons.add_box_outlined),
                                labelText: 'Add',
                                onTap: (isPressed) async {
                                  await ref
                                      .read(
                                        steelColumnSchemeInputControllerProvider
                                            .notifier,
                                      )
                                      .addEmptytable();
                                },
                              ),
                              SizedBox(width: 5.0),
                              FunctionButton(
                                key: _buttonKey,
                                labelIcon: Icon(Icons.calculate_outlined),
                                labelText: 'Run',
                                onTap: (isPressed) async {
                                  final steelColumnSchemeDataList =
                                      await ref
                                          .read(
                                            steelColumnSchemeDataControllerProvider
                                                .notifier,
                                          )
                                          .bacthColumnScheming();

                                  if (steelColumnSchemeDataList.isEmpty) {
                                    if (mounted) {
                                      showGentleMessageBox(
                                        context,
                                        _buttonKey,
                                        'No Steel Column Scheme Pass!',
                                        containerColor: colorScheme
                                            .errorContainer
                                            .withAlpha(175),
                                        textStyle: textTheme.labelMedium!
                                            .copyWith(
                                              color: colorScheme
                                                  .onErrorContainer
                                                  .withAlpha(175),
                                            ),
                                      );
                                    }
                                  }
                                },
                              ),
                              SizedBox(width: 5.0),
                              FunctionButton(
                                labelIcon: Icon(Icons.delete_outlined),
                                labelText: 'Clear Schemes',
                                onTap: (isPressed) async {
                                  await ref
                                      .read(
                                        steelColumnSchemeDataControllerProvider
                                            .notifier,
                                      )
                                      .deleteAllTable();
                                },
                              ),
                            ],
                          ),
                        ),
                        SizedBox(height: 10),
                        ClipRect(
                          child: ConstrainedBox(
                            constraints: BoxConstraints(
                              maxHeight: _isExpanded ? _maxHeight : 0,
                            ),
                            child: Scrollbar(
                              controller: scrollController,
                              thumbVisibility: true,
                              child: ListView.builder(
                                controller: scrollController,
                                itemCount: input.length,
                                itemBuilder: (context, index) {
                                  return Padding(
                                    padding: const EdgeInsets.fromLTRB(
                                      8.0,
                                      4.0,
                                      8.0,
                                      4.0,
                                    ),
                                    child: Row(
                                      mainAxisSize: MainAxisSize.min,
                                      mainAxisAlignment:
                                          MainAxisAlignment.start,
                                      children: [
                                        Container(
                                          decoration: BoxDecoration(
                                            shape: BoxShape.circle,
                                            color: colorScheme.surfaceContainer
                                                .withAlpha(200),
                                            // borderRadius: BorderRadius.circular(5),
                                            border: Border.all(
                                              color: Colors.black,
                                            ),
                                          ),
                                          child: Padding(
                                            padding: const EdgeInsets.all(4.0),
                                            child: Text(
                                              '${index + 1}',
                                              style: textTheme.labelLarge!
                                                  .copyWith(
                                                    color: colorScheme
                                                        .onSurfaceVariant
                                                        .withAlpha(250),
                                                  ),
                                            ),
                                          ),
                                        ),
                                        IconButton(
                                          icon: Icon(
                                            Icons.copy_all_outlined,
                                            color: colorScheme.onSurface,
                                          ),
                                          onPressed: () {
                                            ref
                                                .read(
                                                  steelColumnSchemeInputControllerProvider
                                                      .notifier,
                                                )
                                                .copyAndInsertTable(index);
                                          },
                                        ),
                                        IconButton(
                                          icon: Icon(
                                            Icons.delete_outline,
                                            color: colorScheme.onSurface,
                                          ),
                                          onPressed: () {
                                            ref
                                                .read(
                                                  steelColumnSchemeInputControllerProvider
                                                      .notifier,
                                                )
                                                .deleteTable(
                                                  input[index]
                                                      .steelColumnSchemeInputId,
                                                );
                                          },
                                        ),
                                        ClipRect(
                                          child: Container(
                                            width: 130,
                                            child: CustomStatefulDropDown(
                                              items:
                                                  tables
                                                      .map((e) => e.usage)
                                                      .toList(),
                                              selectedValue:
                                                  input[index].usage == ''
                                                      ? null
                                                      : input[index].usage,
                                              onTap: (selectedValue) async {
                                                final input = await ref.read(
                                                  steelColumnSchemeInputControllerProvider
                                                      .future,
                                                );
                                                ref
                                                    .read(
                                                      steelColumnSchemeInputControllerProvider
                                                          .notifier,
                                                    )
                                                    .updateTable(
                                                      steelColumnSchemeInputId:
                                                          input[index]
                                                              .steelColumnSchemeInputId,
                                                      usage: selectedValue,
                                                    );
                                              },
                                            ),
                                          ),
                                        ),
                                        SizedBox(width: 5.0),
                                        Flexible(
                                          child: CustomStatefulDoubleInput(
                                            title:
                                                'Slab Depth [${units[4]}]',
                                            key: ValueKey(
                                              '${input[index].steelColumnSchemeInputId}_slabThickness',
                                            ),
                                            value: input[index].slabThickness,
                                            listener: (hasFocus, value) {
                                              if (!hasFocus) {
                                                ref
                                                    .read(
                                                      steelColumnSchemeInputControllerProvider
                                                          .notifier,
                                                    )
                                                    .updateTable(
                                                      steelColumnSchemeInputId:
                                                          input[index]
                                                              .steelColumnSchemeInputId,
                                                      slabThickness: value,
                                                    );
                                              }
                                            },
                                          ),
                                        ),
                                        SizedBox(width: 5.0),
                                        Flexible(
                                          child: CustomStatefulIntInput(
                                            title: 'Nos of Floor',
                                            key: ValueKey(
                                              '${input[index].steelColumnSchemeInputId}_nosOfFloor',
                                            ),
                                            value: input[index].nosOfFloor,
                                            listener: (hasFocus, value) {
                                              if (!hasFocus) {
                                                ref
                                                    .read(
                                                      steelColumnSchemeInputControllerProvider
                                                          .notifier,
                                                    )
                                                    .updateTable(
                                                      steelColumnSchemeInputId:
                                                          input[index]
                                                              .steelColumnSchemeInputId,
                                                      nosOfFloor: value,
                                                    );
                                              }
                                            },
                                          ),
                                        ),
                                        SizedBox(width: 5.0),
                                        Flexible(
                                          child: CustomStatefulDoubleInput(
                                            title: 'Load Width [${units[3]}]',
                                            key: ValueKey(
                                              '${input[index].steelColumnSchemeInputId}_LoadWidth',
                                            ),
                                            value: input[index].loadWidth,
                                            listener: (hasFocus, value) {
                                              if (!hasFocus) {
                                                ref
                                                    .read(
                                                      steelColumnSchemeInputControllerProvider
                                                          .notifier,
                                                    )
                                                    .updateTable(
                                                      steelColumnSchemeInputId:
                                                          input[index]
                                                              .steelColumnSchemeInputId,
                                                      loadWidth: value,
                                                    );
                                              }
                                            },
                                          ),
                                        ),
                                        SizedBox(width: 5.0),
                                        Flexible(
                                          child: CustomStatefulDoubleInput(
                                            title: 'Load Length [${units[3]}]',
                                            key: ValueKey(
                                              '${input[index].steelColumnSchemeInputId}_LoadLength',
                                            ),
                                            value: input[index].loadLength,
                                            listener: (hasFocus, value) {
                                              if (!hasFocus) {
                                                ref
                                                    .read(
                                                      steelColumnSchemeInputControllerProvider
                                                          .notifier,
                                                    )
                                                    .updateTable(
                                                      steelColumnSchemeInputId:
                                                          input[index]
                                                              .steelColumnSchemeInputId,
                                                      loadLength: value,
                                                    );
                                              }
                                            },
                                          ),
                                        ),
                                      ],
                                    ),
                                  );
                                },
                              ),
                            ),
                          ),
                        ),
                      ],
                    );
                  },
                  error: (error, stackTrace) {
                    return Text('Error: $error');
                  },
                  loading: () {
                    return const CircularProgressIndicator();
                  },
                );
              },
              error: (error, stackTrace) {
                return Text('Error: $error');
              },
              loading: () {
                return const CircularProgressIndicator();
              },
            );
          },
          error: (error, stackTrace) => Text(error.toString()),
          loading: () => CircularProgressIndicator(),
        );
      },
      error: (error, stackTrace) {
        return Text('Error: $error');
      },
      loading: () {
        return const CircularProgressIndicator();
      },
    );
  }

  void _updateHeights() {
    final double screenHeight = MediaQuery.of(context).size.height;
    _maxHeight =
        (widget.maxHeight == null)
            ? screenHeight * 0.5
            : (widget.maxHeight! > screenHeight * 0.5)
            ? screenHeight * 0.5
            : widget.maxHeight!;

    _minHeight =
        (widget.minHeight == null)
            ? screenHeight * 0.1
            : (widget.minHeight! > screenHeight * 0.1)
            ? screenHeight * 0.1
            : widget.minHeight!;

    // Adjust _maxHeight if needed
    if (_maxHeight < _minHeight) {
      _maxHeight = _minHeight;
    }
  }
}
