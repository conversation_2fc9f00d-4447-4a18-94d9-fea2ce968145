import 'package:riverpod_annotation/riverpod_annotation.dart';

//presentation layer
import '../../domain_layer/column_scheme_data.dart';
import '../../domain_layer/pile_end_bearing_bored_input.dart';
import '../screen/homescreen.dart';

// domain layer

part 'pile_end_bearing_bored_input_controller.g.dart';

@riverpod
class PileEndBearingBoredInputController
    extends _$PileEndBearingBoredInputController {
  @override
  FutureOr<PileEndBearingBoredInput> build() async {
    // print('Build: Column Scheme Input Global');
    PileEndBearingBoredInput pileEndBearingBoredInput =
        await ref
            .read(appDatabaseControllerProvider.notifier)
            .queryPileEndBearingBoredInput();

    final data1 = ref.watch(columnSchemeDataControllerProvider);
    final data2 = ref.watch(pileEndBearingBoredInputGlobalControllerProvider);
    return data1.when(
      data: (colData) async {
        return data2.when(
          data: (inputGlobal) async {
            //* Validate Col Load
            final selectedColumn = colData.firstWhere(
              (scheme) => scheme.isSelected,
              orElse: () => ColumnSchemeData(),
            );
            if (pileEndBearingBoredInput.useSelectColLoad) {
              if ((inputGlobal.colLoadFactor ?? 0) == 0) {
                inputGlobal = inputGlobal.copyWith(colLoadFactor: 1.0);
                // update it meanwhile
                await ref
                    .read(pileEndBearingBoredInputGlobalControllerProvider.notifier)
                    .updateTable(colLoadFactor: 1.0);
              }
              pileEndBearingBoredInput = pileEndBearingBoredInput.copyWith(
                slsLoad:
                    selectedColumn.slsLoad /
                    inputGlobal.colLoadFactor,
                ulsLoad:
                    selectedColumn.ulsLoad /
                    inputGlobal.colLoadFactor,
              );
            }
            return pileEndBearingBoredInput;
          },
          error: (error, stackTrace) => PileEndBearingBoredInput(),
          loading: () => PileEndBearingBoredInput(),
        );
      },
      error: (error, stackTrace) => PileEndBearingBoredInput(),
      loading: () => PileEndBearingBoredInput(),
    );
  }

  Future<void> updateTable({
    double? safeBearing,
    double? fos,
    double? fcu,
    double? maxPileLength,
    double? maxPileDiameter,
    double? ratioOfBelloutDia,
    double? maxSteelRatio,
    double? slsLoad,
    double? ulsLoad,
    double? diaIncrement,
    bool? useSelectColLoad,
    double? colLoadFactor,
  }) async {
    final x = await future;
    PileEndBearingBoredInput newState = x.copyWith(
      safeBearing: safeBearing ?? x.safeBearing,
      fos: fos ?? x.fos,
      fcu: fcu ?? x.fcu,
      maxPileLength: maxPileLength ?? x.maxPileLength,
      maxPileDiameter: maxPileDiameter ?? x.maxPileDiameter,
      ratioOfBelloutDia: ratioOfBelloutDia ?? x.ratioOfBelloutDia,
      maxSteelRatio: maxSteelRatio ?? x.maxSteelRatio,
      slsLoad: slsLoad ?? x.slsLoad,
      ulsLoad: ulsLoad ?? x.ulsLoad,
      diaIncrement: diaIncrement ?? x.diaIncrement,
      useSelectColLoad: useSelectColLoad ?? x.useSelectColLoad,
      colLoadFactor: colLoadFactor ?? x.colLoadFactor,
      id: '1',
    );
    // Update the state only if there are changes
    state = AsyncData(newState);
  }
}
