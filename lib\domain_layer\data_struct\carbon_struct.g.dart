// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'carbon_struct.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ConcreteArea _$ConcreteAreaFromJson(Map<String, dynamic> json) => ConcreteArea(
  name: json['name'] as String? ?? '',
  carbonType: json['carbonType'] as String? ?? 'concreteArea',
  thk: (json['thk'] as num?)?.toDouble() ?? 0.0,
  area: (json['area'] as num?)?.toDouble() ?? 0.0,
  totalSteelRatio: (json['totalSteelRatio'] as num?)?.toDouble() ?? 0.0,
  density: (json['density'] as num?)?.toDouble() ?? 2400.0,
  ecA13: (json['ecA13'] as num?)?.toDouble() ?? 0.198,
  ecA4: (json['ecA4'] as num?)?.toDouble() ?? 0.003,
  wF: (json['wF'] as num?)?.toDouble() ?? 0.053,
  sCO2: (json['sCO2'] as num?)?.toDouble() ?? -1.64,
  ecC2: (json['ecC2'] as num?)?.toDouble() ?? 0.009,
  ecC34: (json['ecC34'] as num?)?.toDouble() ?? 0.0012,
  id: json['id'] as String? ?? '',
  $type: json['runtimeType'] as String?,
);

Map<String, dynamic> _$ConcreteAreaToJson(ConcreteArea instance) =>
    <String, dynamic>{
      'name': instance.name,
      'carbonType': instance.carbonType,
      'thk': instance.thk,
      'area': instance.area,
      'totalSteelRatio': instance.totalSteelRatio,
      'density': instance.density,
      'ecA13': instance.ecA13,
      'ecA4': instance.ecA4,
      'wF': instance.wF,
      'sCO2': instance.sCO2,
      'ecC2': instance.ecC2,
      'ecC34': instance.ecC34,
      'id': instance.id,
      'runtimeType': instance.$type,
    };

ConcreteLine _$ConcreteLineFromJson(Map<String, dynamic> json) => ConcreteLine(
  name: json['name'] as String? ?? '',
  carbonType: json['carbonType'] as String? ?? 'concreteLine',
  length: (json['length'] as num?)?.toDouble() ?? 0.0,
  sectionSize: (json['sectionSize'] as num?)?.toDouble() ?? 0.0,
  totalSteelRatio: (json['totalSteelRatio'] as num?)?.toDouble() ?? 0.0,
  density: (json['density'] as num?)?.toDouble() ?? 2400.0,
  ecA13: (json['ecA13'] as num?)?.toDouble() ?? 0.198,
  ecA4: (json['ecA4'] as num?)?.toDouble() ?? 0.003,
  wF: (json['wF'] as num?)?.toDouble() ?? 0.053,
  sCO2: (json['sCO2'] as num?)?.toDouble() ?? -1.64,
  ecC2: (json['ecC2'] as num?)?.toDouble() ?? 0.009,
  ecC34: (json['ecC34'] as num?)?.toDouble() ?? 0.0012,
  id: json['id'] as String? ?? '',
  $type: json['runtimeType'] as String?,
);

Map<String, dynamic> _$ConcreteLineToJson(ConcreteLine instance) =>
    <String, dynamic>{
      'name': instance.name,
      'carbonType': instance.carbonType,
      'length': instance.length,
      'sectionSize': instance.sectionSize,
      'totalSteelRatio': instance.totalSteelRatio,
      'density': instance.density,
      'ecA13': instance.ecA13,
      'ecA4': instance.ecA4,
      'wF': instance.wF,
      'sCO2': instance.sCO2,
      'ecC2': instance.ecC2,
      'ecC34': instance.ecC34,
      'id': instance.id,
      'runtimeType': instance.$type,
    };

Rebar _$RebarFromJson(Map<String, dynamic> json) => Rebar(
  name: json['name'] as String? ?? '',
  carbonType: json['carbonType'] as String? ?? 'rebar',
  length: (json['length'] as num?)?.toDouble() ?? 0.0,
  sectionSize: (json['sectionSize'] as num?)?.toDouble() ?? 0.0,
  density: (json['density'] as num?)?.toDouble() ?? 7850.0,
  ecA13: (json['ecA13'] as num?)?.toDouble() ?? 0.72,
  ecA4: (json['ecA4'] as num?)?.toDouble() ?? 0.003,
  wF: (json['wF'] as num?)?.toDouble() ?? 0.053,
  sCO2: (json['sCO2'] as num?)?.toDouble() ?? -1.64,
  ecC2: (json['ecC2'] as num?)?.toDouble() ?? 0.009,
  ecC34: (json['ecC34'] as num?)?.toDouble() ?? 0.0012,
  id: json['id'] as String? ?? '',
  $type: json['runtimeType'] as String?,
);

Map<String, dynamic> _$RebarToJson(Rebar instance) => <String, dynamic>{
  'name': instance.name,
  'carbonType': instance.carbonType,
  'length': instance.length,
  'sectionSize': instance.sectionSize,
  'density': instance.density,
  'ecA13': instance.ecA13,
  'ecA4': instance.ecA4,
  'wF': instance.wF,
  'sCO2': instance.sCO2,
  'ecC2': instance.ecC2,
  'ecC34': instance.ecC34,
  'id': instance.id,
  'runtimeType': instance.$type,
};

SteelLine _$SteelLineFromJson(Map<String, dynamic> json) => SteelLine(
  name: json['name'] as String? ?? '',
  carbonType: json['carbonType'] as String? ?? 'steelLine',
  length: (json['length'] as num?)?.toDouble() ?? 0.0,
  sectionSize: (json['sectionSize'] as num?)?.toDouble() ?? 0.0,
  density: (json['density'] as num?)?.toDouble() ?? 7850.0,
  ecA13: (json['ecA13'] as num?)?.toDouble() ?? 1.64,
  ecA4: (json['ecA4'] as num?)?.toDouble() ?? 0.003,
  wF: (json['wF'] as num?)?.toDouble() ?? 0.01,
  sCO2: (json['sCO2'] as num?)?.toDouble() ?? -1.64,
  ecC2: (json['ecC2'] as num?)?.toDouble() ?? 0.009,
  ecC34: (json['ecC34'] as num?)?.toDouble() ?? 0.0012,
  id: json['id'] as String? ?? '',
  $type: json['runtimeType'] as String?,
);

Map<String, dynamic> _$SteelLineToJson(SteelLine instance) => <String, dynamic>{
  'name': instance.name,
  'carbonType': instance.carbonType,
  'length': instance.length,
  'sectionSize': instance.sectionSize,
  'density': instance.density,
  'ecA13': instance.ecA13,
  'ecA4': instance.ecA4,
  'wF': instance.wF,
  'sCO2': instance.sCO2,
  'ecC2': instance.ecC2,
  'ecC34': instance.ecC34,
  'id': instance.id,
  'runtimeType': instance.$type,
};

MetalArea _$MetalAreaFromJson(Map<String, dynamic> json) => MetalArea(
  name: json['name'] as String? ?? '',
  carbonType: json['carbonType'] as String? ?? 'metalArea',
  area: (json['area'] as num?)?.toDouble() ?? 0.0,
  density: (json['density'] as num?)?.toDouble() ?? 13.79,
  ecA13: (json['ecA13'] as num?)?.toDouble() ?? 2.83,
  ecA4: (json['ecA4'] as num?)?.toDouble() ?? 0.003,
  wF: (json['wF'] as num?)?.toDouble() ?? 0.01,
  sCO2: (json['sCO2'] as num?)?.toDouble() ?? -1.64,
  ecC2: (json['ecC2'] as num?)?.toDouble() ?? 0.009,
  ecC34: (json['ecC34'] as num?)?.toDouble() ?? 0.0012,
  id: json['id'] as String? ?? '',
  $type: json['runtimeType'] as String?,
);

Map<String, dynamic> _$MetalAreaToJson(MetalArea instance) => <String, dynamic>{
  'name': instance.name,
  'carbonType': instance.carbonType,
  'area': instance.area,
  'density': instance.density,
  'ecA13': instance.ecA13,
  'ecA4': instance.ecA4,
  'wF': instance.wF,
  'sCO2': instance.sCO2,
  'ecC2': instance.ecC2,
  'ecC34': instance.ecC34,
  'id': instance.id,
  'runtimeType': instance.$type,
};
