// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'beam_scheme_input_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$beamSchemeInputControllerHash() =>
    r'a75f49812176e8b500ec2dd446df5be9a1a1db7c';

/// See also [BeamSchemeInputController].
@ProviderFor(BeamSchemeInputController)
final beamSchemeInputControllerProvider = AutoDisposeAsyncNotifierProvider<
  BeamSchemeInputController,
  BeamSchemeInput
>.internal(
  BeamSchemeInputController.new,
  name: r'beamSchemeInputControllerProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$beamSchemeInputControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$BeamSchemeInputController = AutoDisposeAsyncNotifier<BeamSchemeInput>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
