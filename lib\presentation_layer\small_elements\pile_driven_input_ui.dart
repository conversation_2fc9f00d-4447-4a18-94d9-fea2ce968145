import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:structify/domain_layer/preferences.dart';
import 'package:structify/misc/show_overlay_msg.dart';

//presentation layer
import '../../domain_layer/column_scheme_data.dart';
import 'button/function_button.dart';
import 'input/custom_stateful_double_input.dart';
import '../screen/homescreen.dart';
import 'button/selection_button.dart';

class PileDrivenInputUi extends ConsumerStatefulWidget {
  const PileDrivenInputUi({
    this.isExpanded,
    this.maxHeight,
    this.minHeight,
    super.key,
  });

  final bool? isExpanded;
  final double? maxHeight;
  final double? minHeight;

  @override
  ConsumerState<ConsumerStatefulWidget> createState() =>
      _PileDrivenInputUiState();
}

class _PileDrivenInputUiState extends ConsumerState<PileDrivenInputUi>
    with WidgetsBindingObserver {
  late bool _isExpanded;
  late double _maxHeight;
  late double _minHeight;

  late GlobalKey _buttonKey;

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void initState() {
    WidgetsBinding.instance.addObserver(this);
    _isExpanded = widget.isExpanded ?? false;
    _maxHeight = widget.maxHeight ?? 210;
    _minHeight = widget.minHeight ?? 50;
    _buttonKey = GlobalKey();
    // _updateHeights();

    super.initState();
  }

  @override
  void didChangeDependencies() {
    _updateHeights();
    super.didChangeDependencies();
    // Update heights when dependencies change, including after initState
  }

  @override
  void didChangeMetrics() {
    // This method is called when the metrics change (like resizing the window)
    setState(() {
      _updateHeights();
    });
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    ref.watch(appWatcherProvider);
    final globalData = ref.watch(globalDataControllerProvider);
    final pileDrivenInput = ref.watch(pileDrivenInputControllerProvider);
    final pileDrivenInputGlobal = ref.watch(
      pileDrivenInputGlobalControllerProvider,
    );
    return globalData.when(
      data: (data) {
        return pileDrivenInput.when(
          data: (input) {
            return pileDrivenInputGlobal.when(
              data: (inputGlobal) {
                late final List<String> unit;
                switch (data.unit) {
                  case 'metrics':
                    unit = PreferredUnit.metrics;
                    break;
                  case 'imperial':
                    unit = PreferredUnit.imperial;
                    break;
                  default:
                    unit = PreferredUnit.metrics;
                    break;
                }

                return Column(
                  children: [
                    Padding(
                      padding: const EdgeInsets.fromLTRB(10, 0, 10, 0),
                      child: Divider(),
                    ),
                    Padding(
                      padding: const EdgeInsets.fromLTRB(10, 0, 0, 0),
                      child: Align(
                        alignment: Alignment.centerLeft,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Wrap(
                              crossAxisAlignment: WrapCrossAlignment.center,
                              children: [
                                IconButton(
                                  icon: Icon(
                                    _isExpanded
                                        ? Icons.expand_less
                                        : Icons.expand_more,
                                  ),
                                  color: colorScheme.onSurface,
                                  onPressed: () {
                                    setState(() {
                                      _isExpanded = !_isExpanded;
                                    });
                                  },
                                ),
                                Text(
                                  'Driven Steel H Pile Scheme Input',
                                  style: textTheme.titleLarge!.copyWith(
                                    color: colorScheme.onSurface,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.fromLTRB(10, 0, 10, 0),
                      child: Divider(),
                    ),
                    ClipRect(
                      child: ConstrainedBox(
                        constraints: BoxConstraints(
                          maxHeight: _isExpanded ? _maxHeight : 0,
                        ),
                        child: SingleChildScrollView(
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Padding(
                                padding: const EdgeInsets.fromLTRB(
                                  8.0,
                                  8.0,
                                  8.0,
                                  4.0,
                                ),
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  mainAxisAlignment: MainAxisAlignment.start,
                                  children: [
                                    Flexible(
                                      child: CustomStatefulDoubleInput(
                                        title: 'SPT N-Value',
                                        value: input.sptNValue,
                                        maxValue: 80,
                                        tooltipText:
                                            'Good Practice: Limited to 80',
                                        onChanged: (value) async {
                                          await ref
                                              .read(
                                                pileDrivenInputControllerProvider
                                                    .notifier,
                                              )
                                              .updateTable(sptNValue: value);
                                        },
                                      ),
                                    ),
                                    SizedBox(width: 5.0),
                                    Flexible(
                                      child: CustomStatefulDoubleInput(
                                        title: 'FOS',
                                        value: input.fos,
                                        onChanged: (value) async {
                                          await ref
                                              .read(
                                                pileDrivenInputControllerProvider
                                                    .notifier,
                                              )
                                              .updateTable(fos: value);
                                        },
                                      ),
                                    ),
                                    SizedBox(width: 5.0),
                                    Flexible(
                                      child: CustomStatefulDoubleInput(
                                        title: 'Max Pile Length [${unit[3]}]',
                                        value: input.maxPileLength,
                                        onChanged: (value) async {
                                          await ref
                                              .read(
                                                pileDrivenInputControllerProvider
                                                    .notifier,
                                              )
                                              .updateTable(
                                                maxPileLength: value,
                                              );
                                        },
                                      ),
                                    ),
                                    SizedBox(width: 5.0),
                                    Flexible(
                                      child: CustomStatefulDoubleInput(
                                        readOnly: input.useSelectColLoad,
                                        title: 'SLS Load [${unit[0]}]',
                                        value: input.slsLoad,
                                        onChanged: (value) async {
                                          await ref
                                              .read(
                                                pileDrivenInputControllerProvider
                                                    .notifier,
                                              )
                                              .updateTable(slsLoad: value);
                                        },
                                      ),
                                    ),
                                    SizedBox(width: 5.0),
                                    Flexible(
                                      child: CustomStatefulDoubleInput(
                                        readOnly: input.useSelectColLoad,
                                        title: 'ULS Load [${unit[0]}]',
                                        value: input.ulsLoad,
                                        onChanged: (value) async {
                                          await ref
                                              .read(
                                                pileDrivenInputControllerProvider
                                                    .notifier,
                                              )
                                              .updateTable(ulsLoad: value);
                                        },
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              Row(
                                children: [
                                  SelectionButton(
                                    labelTextStyle:
                                        input.useSelectColLoad
                                            ? textTheme.labelLarge!.copyWith(
                                              color:
                                                  colorScheme
                                                      .onTertiaryContainer,
                                            )
                                            : textTheme.labelLarge!.copyWith(
                                              color: colorScheme.onSurface,
                                            ),
                                    labelText: 'Use Load from\nSelected Column',
                                    pressedColor: colorScheme.tertiaryContainer,
                                    bgColor:
                                        input.useSelectColLoad
                                            ? colorScheme.tertiaryContainer
                                            : colorScheme.surfaceContainer,

                                    onTap: (value) async {
                                      final input = await ref.read(
                                        pileDrivenInputControllerProvider
                                            .future,
                                      );
                                      final colData = await ref.read(
                                        columnSchemeDataControllerProvider
                                            .future,
                                      );

                                      await ref
                                          .read(
                                            pileDrivenInputControllerProvider
                                                .notifier,
                                          )
                                          .updateTable(
                                            useSelectColLoad:
                                                !input.useSelectColLoad,
                                          );
                                      //* Tricky here: if the new toggled value (!input.useSlabSelected) is true,
                                      //* we run below logic
                                      if (!input.useSelectColLoad) {
                                        final selectedColumn = colData
                                            .firstWhere(
                                              (scheme) => scheme.isSelected,
                                              orElse: () => ColumnSchemeData(),
                                            );
                                        await ref
                                            .read(
                                              pileDrivenInputControllerProvider
                                                  .notifier,
                                            )
                                            .updateTable(
                                              slsLoad:
                                                  selectedColumn.slsLoad /
                                                  inputGlobal.colLoadFactor,
                                              ulsLoad:
                                                  selectedColumn.ulsLoad /
                                                  inputGlobal.colLoadFactor,
                                            );
                                      }
                                    },
                                  ),
                                  SizedBox(width: 5.0),
                                  Flexible(
                                    child: CustomStatefulDoubleInput(
                                      readOnly: !input.useSelectColLoad,
                                      title: 'Column Load Dividor ',
                                      value: inputGlobal.colLoadFactor,
                                      listener: (hasFocus, value) async {
                                        if (!hasFocus) {
                                          await ref
                                              .read(
                                                pileDrivenInputGlobalControllerProvider
                                                    .notifier,
                                              )
                                              .updateTable(
                                                colLoadFactor: value,
                                              );
                                        }
                                      },
                                    ),
                                  ),
                                  SizedBox(width: 5.0),
                                  Flexible(
                                    child: FunctionButton(
                                      key: _buttonKey,
                                      labelIcon: Icon(Icons.calculate_outlined),
                                      labelText: 'Run',
                                      onTap: (isPressed) async {
                                        await ref
                                            .read(
                                              pileDrivenDataControllerProvider
                                                  .notifier,
                                            )
                                            .pileDrivenScheming();
                                      },
                                    ),
                                  ),
                                  SizedBox(width: 5.0),
                                  Flexible(
                                    child: FunctionButton(
                                      labelIcon: Icon(Icons.delete_outlined),
                                      labelText: 'Clear Schemes',
                                      onTap: (isPressed) async {
                                        await ref
                                            .read(
                                              pileDrivenDataControllerProvider
                                                  .notifier,
                                            )
                                            .deleteAllTable();
                                      },
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ],
                );
              },
              error: (error, loading) {
                return Text('Error: $error');
              },
              loading: () {
                return Text('Loading...');
              },
            );
          },
          error: (error, loading) {
            return Text('Error: $error');
          },
          loading: () {
            return Text('Loading...');
          },
        );
      },
      error: (error, loading) {
        return Text('Error: $error');
      },
      loading: () {
        return Text('Loading...');
      },
    );
  }

  void _updateHeights() {
    final double screenHeight = MediaQuery.of(context).size.height;
    _maxHeight =
        (widget.maxHeight == null)
            ? screenHeight * 0.6
            : (widget.maxHeight! > screenHeight * 0.6)
            ? screenHeight * 0.6
            : widget.maxHeight!;

    _minHeight =
        (widget.minHeight == null)
            ? screenHeight * 0.1
            : (widget.minHeight! > screenHeight * 0.1)
            ? screenHeight * 0.1
            : widget.minHeight!;

    // Adjust _maxHeight if needed

    if (_maxHeight < _minHeight) {
      _maxHeight = _minHeight;
    }
  }
}
