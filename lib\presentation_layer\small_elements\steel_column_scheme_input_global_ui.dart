import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

//presentation layer
// import '../controllers/steel_beam_scheme_input_controller.dart';
import 'input/custom_stateful_double_input.dart';
import '../screen/homescreen.dart';

//domain layer
import '../../domain_layer/preferences.dart';
import 'popup/custom_tooltip.dart';
import 'popup/multiseleciton_dialog.dart';

class SteelColumnSchemeInputGlobalUi extends ConsumerStatefulWidget {
  const SteelColumnSchemeInputGlobalUi({
    this.isExpanded,
    this.maxHeight,
    this.minHeight,
    super.key,
  });

  final bool? isExpanded;
  final double? maxHeight;
  final double? minHeight;

  @override
  ConsumerState<ConsumerStatefulWidget> createState() =>
      _SteelColumnSchemeInputGlobalUiState();
}

class _SteelColumnSchemeInputGlobalUiState
    extends ConsumerState<SteelColumnSchemeInputGlobalUi>
    with WidgetsBindingObserver {
  late bool _isExpanded;
  late double _maxHeight;
  late double _minHeight;
  late ScrollController _controller;

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _controller.dispose();
    super.dispose();
  }

  @override
  void initState() {
    WidgetsBinding.instance.addObserver(this);
    _isExpanded = widget.isExpanded ?? false;
    _maxHeight = widget.maxHeight ?? 210;
    _minHeight = widget.minHeight ?? 50;
    _controller = ScrollController();
    // _updateHeights();

    super.initState();
  }

  @override
  void didChangeDependencies() {
    _updateHeights();
    super.didChangeDependencies();
    // Update heights when dependencies change, including after initState
  }

  @override
  void didChangeMetrics() {
    // This method is called when the metrics change (like resizing the window)
    setState(() {
      _updateHeights();
    });
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;
    final steelColumnSchemeInput = ref.watch(
      steelColumnSchemeInputGlobalControllerProvider,
    );
    final globalData = ref.watch(globalDataControllerProvider);
    final loadingTables = ref.watch(loadingTablesControllerProvider);
    return loadingTables.when(
      data: (tables) {
        return globalData.when(
          data: (data) {
            late final List<String> unit;
            switch (data.unit) {
              case 'metrics':
                unit = PreferredUnit.metrics;
                break;
              case 'imperial':
                unit = PreferredUnit.imperial;
                break;
              default:
                unit = PreferredUnit.metrics;
                break;
            }
            return steelColumnSchemeInput.when(
              data: (input) {
                final int containerOpacity = 175;
                final int textOpacity = 225;
                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Padding(
                      padding: const EdgeInsets.fromLTRB(10, 0, 10, 0),
                      child: Divider(),
                    ),
                    Padding(
                      padding: const EdgeInsets.fromLTRB(10, 0, 0, 0),
                      child: Align(
                        alignment: Alignment.centerLeft,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                IconButton(
                                  icon: Icon(
                                    _isExpanded
                                        ? Icons.expand_less
                                        : Icons.expand_more,
                                  ),
                                  color: colorScheme.onSurface,
                                  onPressed: () {
                                    setState(() {
                                      _isExpanded = !_isExpanded;
                                    });
                                  },
                                ),
                                Text(
                                  'Steel Column Settings',
                                  style: textTheme.titleLarge!.copyWith(
                                    color: colorScheme.onSurface,
                                  ),
                                ),
                                SizedBox(width: 10.0),
                                CustomTooltip(
                                  tooltipText:
                                      'Only UC section used for scheming.',
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.fromLTRB(10, 0, 10, 0),
                      child: Divider(),
                    ),
                    ClipRect(
                      child: ConstrainedBox(
                        constraints: BoxConstraints(
                          maxHeight: _isExpanded ? _maxHeight : 0,
                        ),
                        child: Scrollbar(
                          controller: _controller,
                          thumbVisibility: true,
                          trackVisibility: false,
                          child: SingleChildScrollView(
                            controller: _controller,
                            child: Column(
                              mainAxisSize: MainAxisSize.min,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Padding(
                                  padding: const EdgeInsets.fromLTRB(
                                    8.0,
                                    0.0,
                                    8.0,
                                    0.0,
                                  ),
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Container(
                                        decoration: BoxDecoration(
                                          borderRadius: BorderRadius.circular(
                                            10.0,
                                          ),
                                          border: Border.all(
                                            color: Colors.grey.withAlpha(150),
                                          ),
                                          color: colorScheme.secondaryContainer
                                              .withAlpha(containerOpacity),
                                        ),
                                        child: Padding(
                                          padding: const EdgeInsets.all(4.0),
                                          child: Text(
                                            'Basic Info',
                                            style: textTheme.titleSmall!
                                                .copyWith(
                                                  color: colorScheme
                                                      .onSecondaryContainer
                                                      .withAlpha(textOpacity),
                                                ),
                                          ),
                                        ),
                                      ),
                                      SizedBox(height: 10.0),
                                      Row(
                                        mainAxisSize: MainAxisSize.min,
                                        mainAxisAlignment:
                                            MainAxisAlignment.start,
                                        children: [
                                          Flexible(
                                            child: CustomStatefulDoubleInput(
                                              title:
                                                  'Unbraced Length [${unit[5]}]',
                                              value: input.unbracedLength,
                                              onChanged: (value) {
                                                ref
                                                    .read(
                                                      steelColumnSchemeInputGlobalControllerProvider
                                                          .notifier,
                                                    )
                                                    .updateTable(
                                                      unbracedLength: value,
                                                    );
                                              },
                                            ),
                                          ),
                                          SizedBox(width: 10.0),
                                          MultiSelectionDialog(
                                            dialogWidth: 50.0,
                                            dialogHeight: 400.0,
                                            label: 'Grade',
                                            selectedOptions: input.steelGrade
                                                .split(','),
                                            options: MaterialProps.steelGrade,
                                            onPressed: (selectedOptions) async {
                                              final String steelGrade =
                                                  selectedOptions.join(',');
                                              if (steelGrade.isNotEmpty) {
                                                await ref
                                                    .read(
                                                      steelColumnSchemeInputGlobalControllerProvider
                                                          .notifier,
                                                    )
                                                    .updateTable(
                                                      steelGrade: steelGrade,
                                                    );
                                              } else {
                                                //default to be S355 only
                                                await ref
                                                    .read(
                                                      steelColumnSchemeInputGlobalControllerProvider
                                                          .notifier,
                                                    )
                                                    .updateTable(
                                                      steelGrade: "S355",
                                                    );
                                              }
                                            },
                                          ),
                                          Flexible(child: SizedBox(width: 5.0)),
                                          Flexible(child: SizedBox(width: 5.0)),
                                        ],
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                );
              },
              error: (error, stackTrace) => Text(error.toString()),
              loading: () => const CircularProgressIndicator(),
            );
          },
          error: (error, stackTrace) => Text(error.toString()),
          loading: () => Center(child: CircularProgressIndicator()),
        );
      },
      error: (error, stackTrace) => Text(error.toString()),
      loading: () => Center(child: CircularProgressIndicator()),
    );
  }

  void _updateHeights() {
    final double screenHeight = MediaQuery.of(context).size.height;
    _maxHeight =
        (widget.maxHeight == null)
            ? screenHeight * 0.5
            : (widget.maxHeight! > screenHeight * 0.5)
            ? screenHeight * 0.5
            : widget.maxHeight!;

    _minHeight =
        (widget.minHeight == null)
            ? screenHeight * 0.1
            : (widget.minHeight! > screenHeight * 0.1)
            ? screenHeight * 0.1
            : widget.minHeight!;

    // Adjust _maxHeight if needed

    if (_maxHeight < _minHeight) {
      _maxHeight = _minHeight;
    }
  }
}
