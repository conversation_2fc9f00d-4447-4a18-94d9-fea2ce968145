// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'steel_column_scheme_input_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$steelColumnSchemeInputControllerHash() =>
    r'15cd2d73873fa07331294c632b38b24f8fb41b3e';

/// See also [SteelColumnSchemeInputController].
@ProviderFor(SteelColumnSchemeInputController)
final steelColumnSchemeInputControllerProvider =
    AutoDisposeAsyncNotifierProvider<
      SteelColumnSchemeInputController,
      List<SteelColumnSchemeInput>
    >.internal(
      SteelColumnSchemeInputController.new,
      name: r'steelColumnSchemeInputControllerProvider',
      debugGetCreateSourceHash:
          const bool.fromEnvironment('dart.vm.product')
              ? null
              : _$steelColumnSchemeInputControllerHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$SteelColumnSchemeInputController =
    AutoDisposeAsyncNotifier<List<SteelColumnSchemeInput>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
