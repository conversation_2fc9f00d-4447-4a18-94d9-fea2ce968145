import 'dart:math';
import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:structify/domain_layer/programme_item.dart';
import 'package:structify/misc/custom_func.dart';
import '../../screen/homescreen.dart';

class ChartProgramme extends ConsumerStatefulWidget {
  const ChartProgramme(this.programmeItems, {super.key});

  final List<ProgrammeItem> programmeItems;
  @override
  ConsumerState<ConsumerStatefulWidget> createState() =>
      _ChartTransferBeamSchemeState();
}

class _ChartTransferBeamSchemeState extends ConsumerState<ChartProgramme> {
  late List<ProgrammeItem> _programmeItems;
  late ScrollController _scrollController;
  late int touchedIndex;

  @override
  void initState() {
    _programmeItems = widget.programmeItems;
    _scrollController = ScrollController();
    touchedIndex = -1;

    super.initState();
  }

  @override
  void didUpdateWidget(covariant ChartProgramme oldWidget) {
    // TODO: implement didUpdateWidget
    super.didUpdateWidget(oldWidget);
    _programmeItems = widget.programmeItems;
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final ColorScheme colorScheme = Theme.of(context).colorScheme;
    final TextTheme textTheme = Theme.of(context).textTheme;
    final size = MediaQuery.of(context).size;

    //* Axis Formatting
    final TextStyle titleStyle = textTheme.labelLarge!.copyWith(
      color: colorScheme.primary,
    );

    final TextStyle gridValueStyle = textTheme.labelMedium!.copyWith(
      color: colorScheme.secondary,
    );

    //* Tooltip Formatting
    // Color toolTipbgColor = colorScheme.tertiaryContainer.withAlpha(150);
    Color toolTipbgColor = Colors.transparent;
    final TextStyle tooltipStyle = textTheme.labelMedium!.copyWith(
      color: colorScheme.onSurface,
    );

    //* Chart Formatting
    Color chartBgColor = colorScheme.surface.withAlpha(50);

    //* Grid Formatting
    Color horizontalGridColor = colorScheme.secondary.withAlpha(50);
    Color horizontalGridZeroColor = colorScheme.error.withAlpha(100);
    Color verticalGridColor = colorScheme.secondary.withAlpha(50);

    //* Border Formatting
    Border chartBorder = Border.all(
      color: colorScheme.onSurface.withAlpha(100),
      width: 1.5,
    );

    //* touch Animation formatting
    Color barColor = colorScheme.primary.withAlpha(200);
    Color touchedBarColor = colorScheme.tertiaryContainer.withAlpha(225);
    Color barBackgroundColor = colorScheme.secondaryContainer.withAlpha(125);

    //* BarChartGroupData
    double chartMaxY = 0;
    if (_programmeItems.length > 0) {
      chartMaxY = _programmeItems.fold<double>(
        _programmeItems.first.end,
        (previousValue, element) => max(previousValue, element.end),
      );
    }

    final List<BarChartGroupData> rawBarGroups = [];
    if (_programmeItems.length > 0) {
      for (var i = 0; i < _programmeItems.length; i++) {
        rawBarGroups.add(
          _makeGroupData(
            i,
            _programmeItems[i].start,
            _programmeItems[i].end,
            maxY: chartMaxY,
            isTouched: _programmeItems[i].isTouched,
            barColor: barColor,
            // barWidth: size.height / 75,
            touchedBarColor: touchedBarColor,
            barBackgroundColor: barBackgroundColor,
          ),
        );
      }
    }

    return BarChart(
      curve: Curves.linear,
      duration: const Duration(milliseconds: 100),
      BarChartData(
        backgroundColor: chartBgColor,
        rotationQuarterTurns: 1,
        alignment: BarChartAlignment.start,
        maxY: chartMaxY, // Weeks go up on the Y-axis
        groupsSpace: size.height / 25, // Space between bar groups
        barGroups: rawBarGroups,
        barTouchData: BarTouchData(
          touchCallback: (touchEvent, barTouchResponse) async {
            if (!touchEvent.isInterestedForInteractions ||
                barTouchResponse == null ||
                barTouchResponse.spot == null) {
              touchedIndex = -1;
              await Future.forEach(_programmeItems, (item) async {
                await ref
                    .read(programmeItemsControllerProvider.notifier)
                    .updateTable(item.id, isTouched: false);
              });
              return;
            }
            touchedIndex = barTouchResponse.spot!.touchedBarGroupIndex;
            if (touchedIndex >= 0) {
              print('touchedindex: ${touchedIndex}');
              final String id = _programmeItems[touchedIndex].id;
              await Future.forEach(_programmeItems, (item) async {
                if (item.id == id) {
                  await ref
                      .read(programmeItemsControllerProvider.notifier)
                      .updateTable(item.id, isTouched: true);
                }
              });
            }
            setState(() {});
          },
          touchTooltipData: BarTouchTooltipData(
            tooltipHorizontalAlignment: FLHorizontalAlignment.center,
            // tooltipPadding: const EdgeInsets.all(8.0),
            tooltipMargin: 30,
            fitInsideVertically: true,
            fitInsideHorizontally: true,

            getTooltipItem: (group, groupIndex, rod, rodIndex) {
              final item = _programmeItems[groupIndex];
              return BarTooltipItem(
                '${item.start}-${item.end}',
                textAlign: TextAlign.start,
                tooltipStyle,
              );
            },
            getTooltipColor: (group) {
              return toolTipbgColor;
            },
          ),
        ),
        titlesData: FlTitlesData(
          show: true,
          bottomTitles: AxisTitles(
            axisNameWidget: Text('Item', style: titleStyle),
            sideTitles: SideTitles(
              showTitles: true,
              reservedSize: size.width / 10,
              getTitlesWidget: (value, meta) {
                return SideTitleWidget(
                  meta: meta,
                  child: Text(
                    '${_programmeItems[value.toInt()].itemName}',
                    style: gridValueStyle,
                  ),
                );
              },
            ),
          ),
          leftTitles: AxisTitles(
            axisNameWidget: Text('Duration [Week]', style: titleStyle),
            sideTitles: SideTitles(
              showTitles: true,
              reservedSize: 28,
              getTitlesWidget: (value, meta) {
                return SideTitleWidget(
                  meta: meta,
                  child: Text('${value}', style: gridValueStyle),
                );
              },
            ), // Hide default Y-axis labels
          ),
          topTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
          rightTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
        ),
        gridData: FlGridData(
          show: true,
          drawHorizontalLine: true,
          drawVerticalLine: true,
          getDrawingHorizontalLine:
              (value) => FlLine(color: horizontalGridColor, strokeWidth: 1),
          getDrawingVerticalLine:
              (value) => FlLine(color: verticalGridColor, strokeWidth: 1),
        ),
        borderData: FlBorderData(show: true, border: chartBorder),
      ),
    );
  }

  BarChartGroupData _makeGroupData(
    int x,
    double fromY,
    double toY, {
    double maxY = 0,
    bool isTouched = false,
    Color? barColor,
    Color? touchedBarColor,
    Color? barBackgroundColor,
    double? barWidth,
    bool showbackDrawRod = false,
  }) {
    barColor ??= Colors.amber;
    touchedBarColor ??= barColor;
    barBackgroundColor ??= Colors.black.withAlpha(100);
    barWidth ??= MediaQuery.of(context).size.height / 125;
    return BarChartGroupData(
      showingTooltipIndicators: [0],
      x: x,
      barRods: [
        BarChartRodData(
          fromY: fromY,
          toY: isTouched ? min(toY * 1.02, maxY == 0 ? toY : maxY) : toY,
          color: isTouched ? touchedBarColor : barColor,
          width: barWidth,

          backDrawRodData:
              showbackDrawRod
                  ? BackgroundBarChartRodData(
                    show: maxY > 0 ? true : false,
                    toY: maxY,
                    color: barBackgroundColor,
                  )
                  : null,
        ),
      ],
    );
  }
}
