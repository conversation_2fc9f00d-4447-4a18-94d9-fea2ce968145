// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'pile_end_bearing_bored_input_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$pileEndBearingBoredInputControllerHash() =>
    r'ab8e88b2bf42cb884a1a89983f87498ca9d4c303';

/// See also [PileEndBearingBoredInputController].
@ProviderFor(PileEndBearingBoredInputController)
final pileEndBearingBoredInputControllerProvider =
    AutoDisposeAsyncNotifierProvider<
      PileEndBearingBoredInputController,
      PileEndBearingBoredInput
    >.internal(
      PileEndBearingBoredInputController.new,
      name: r'pileEndBearingBoredInputControllerProvider',
      debugGetCreateSourceHash:
          const bool.fromEnvironment('dart.vm.product')
              ? null
              : _$pileEndBearingBoredInputControllerHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$PileEndBearingBoredInputController =
    AutoDisposeAsyncNotifier<PileEndBearingBoredInput>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
