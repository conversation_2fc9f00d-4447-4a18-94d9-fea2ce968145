// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'footing_scheme_input_global.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_FootingSchemeInputGlobal _$FootingSchemeInputGlobalFromJson(
  Map<String, dynamic> json,
) => _FootingSchemeInputGlobal(
  id: json['id'] as String? ?? '1',
  colLoadFactor: (json['colLoadFactor'] as num?)?.toDouble() ?? 1.0,
);

Map<String, dynamic> _$FootingSchemeInputGlobalToJson(
  _FootingSchemeInputGlobal instance,
) => <String, dynamic>{
  'id': instance.id,
  'colLoadFactor': instance.colLoadFactor,
};
