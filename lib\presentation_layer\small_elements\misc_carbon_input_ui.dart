import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:nanoid2/nanoid2.dart';

//domain layer
import '../../domain_layer/column_scheme_data.dart';
import '../../domain_layer/data_struct/carbon_struct.dart';
import '../../domain_layer/data_struct/preferences.dart';

// presentation layer
import '../../domain_layer/slab_scheme_data.dart';
import '../screen/homescreen.dart';
import 'input/custom_stateful_double_input.dart';
import 'input/custom_stateful_text_input.dart';
import 'popup/custom_popup.dart';
import 'input/custom_stateful_dropList.dart';
import 'input/custom_stateful_int_input.dart';
import 'popup/custom_tooltip.dart';
import 'sketch/draw_transfer_beam_loading_info.dart';
import 'button/function_button.dart';
import 'button/selection_button.dart';

class CarbonInputUi extends ConsumerStatefulWidget {
  const CarbonInputUi({
    this.isExpanded,
    this.maxHeight,
    this.minHeight,
    super.key,
  });

  final bool? isExpanded;
  final double? maxHeight;
  final double? minHeight;

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _CarbonInputUiState();
}

class _CarbonInputUiState extends ConsumerState<CarbonInputUi>
    with WidgetsBindingObserver {
  late bool _isExpanded;
  late double _maxHeight;
  late double _minHeight;
  late CustomStatefulTextInput _usageInput;
  late String _id;
  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void initState() {
    WidgetsBinding.instance.addObserver(this);
    _isExpanded = widget.isExpanded ?? false;
    _maxHeight = widget.maxHeight ?? 300;
    _minHeight = widget.minHeight ?? 50;
    _usageInput = CustomStatefulTextInput();
    _id = _generateTaskID();

    // _updateHeights();

    super.initState();
  }

  @override
  void didChangeDependencies() {
    _updateHeights();
    super.didChangeDependencies();
    // Update heights when dependencies change, including after initState
  }

  @override
  void didChangeMetrics() {
    // This method is called when the metrics change (like resizing the window)
    setState(() {
      _updateHeights();
    });
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;
    final globalData = ref.watch(globalDataControllerProvider);
    final carbonInput = ref.watch(carbonInputControllerProvider);
    final scrollController = ScrollController();
    final int containerOpacity = 175;
    final int textOpacity = 225;

    return globalData.when(
      data: (data) {
        late final List<String> units;
        switch (data.unit) {
          case 'metrics':
            units = PreferredUnit.metrics;
            break;
          case 'imperial':
            units = PreferredUnit.imperial;
            break;
          default:
            units = PreferredUnit.metrics;
            break;
        }

        return carbonInput.when(
          data: (input) {
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding: const EdgeInsets.fromLTRB(10, 0, 10, 0),
                  child: Divider(),
                ),
                Padding(
                  padding: const EdgeInsets.fromLTRB(10, 0, 0, 0),
                  child: Align(
                    alignment: Alignment.centerLeft,
                    child: Wrap(
                      crossAxisAlignment: WrapCrossAlignment.center,
                      children: [
                        Wrap(
                          crossAxisAlignment: WrapCrossAlignment.center,
                          children: [
                            IconButton(
                              icon: Icon(
                                _isExpanded
                                    ? Icons.keyboard_arrow_up
                                    : Icons.keyboard_arrow_down,
                                color: colorScheme.onSurface,
                              ),
                              onPressed: () {
                                setState(() {
                                  _isExpanded = !_isExpanded;
                                });
                              },
                            ),
                            Text(
                              'Carbon Inputs ',
                              style: textTheme.titleLarge!.copyWith(
                                color: colorScheme.onSurface,
                              ),
                            ),
                          ],
                        ),
                        Container(
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(5.0),
                            color: colorScheme.surfaceContainer.withAlpha(225),
                            border: Border.all(
                              color: Colors.black.withAlpha(125),
                            ),
                          ),
                          child: Padding(
                            padding: const EdgeInsets.all(2.0),
                            child: Text(
                              '(Total: ${input.length})',
                              style: textTheme.labelMedium!.copyWith(
                                color: colorScheme.onSurfaceVariant.withAlpha(
                                  225,
                                ),
                              ),
                            ),
                          ),
                        ),
                        SizedBox(width: 5.0),
                        CustomTooltip(
                          tooltipText:
                              'concreteArea: i.e slab, wall, pile cap'
                              '\nmetalArea: i.e. for floor formwork in steelworks'
                              '\nconcreteLine: i.e. beam, column, pile'
                              '\nrebar: i.e. rebars for RC elements'
                              '\nsteelLine: i.e. steel beam/column',
                        ),
                      ],
                    ),
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.fromLTRB(10, 0, 10, 0),
                  child: Divider(),
                ),
                ConstrainedBox(
                  constraints: BoxConstraints(
                    maxHeight: _isExpanded ? _maxHeight : 0,
                  ),
                  child: Scrollbar(
                    controller: scrollController,
                    thumbVisibility: true,
                    child: ListView.builder(
                      controller: scrollController,
                      itemCount: input.length,
                      itemBuilder: (context, index) {
                        return Padding(
                          padding: const EdgeInsets.fromLTRB(
                            8.0,
                            4.0,
                            8.0,
                            4.0,
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                              Container(
                                decoration: BoxDecoration(
                                  shape: BoxShape.circle,
                                  color: colorScheme.surfaceContainer.withAlpha(
                                    200,
                                  ),
                                  // borderRadius: BorderRadius.circular(5),
                                  border: Border.all(color: Colors.black),
                                ),
                                child: Padding(
                                  padding: const EdgeInsets.all(4.0),
                                  child: Text(
                                    '${index + 1}',
                                    style: textTheme.labelLarge!.copyWith(
                                      color: colorScheme.onSurfaceVariant
                                          .withAlpha(250),
                                    ),
                                  ),
                                ),
                              ),
                              IconButton(
                                icon: Icon(
                                  Icons.copy_all_outlined,
                                  color: colorScheme.onSurface,
                                ),
                                onPressed: () async {
                                  await ref
                                      .read(
                                        carbonInputControllerProvider.notifier,
                                      )
                                      .copyAndInsertTable(index);
                                },
                              ),
                              IconButton(
                                icon: Icon(
                                  Icons.delete_outline,
                                  color: colorScheme.onSurface,
                                ),
                                onPressed: () async {
                                  await ref
                                      .read(
                                        carbonInputControllerProvider.notifier,
                                      )
                                      .deleteTable(input[index].id);
                                },
                              ),
                              CustomStatefulDropDown(
                                items:
                                    CarbonType.values
                                        .map((e) => e.name)
                                        .where(
                                          (e) => e != CarbonType.rebar.name,
                                        )
                                        .toList(),
                                selectedValue: input[index].carbonType,
                                onTap: (selectedValue) async {
                                  await ref
                                      .read(
                                        carbonInputControllerProvider
                                            .notifier,
                                      )
                                      .updateType(
                                        input[index].id,
                                        CarbonType.values.firstWhere(
                                          (e) => e.name == selectedValue,
                                        ),
                                      );
                                },
                              ),
                              SizedBox(width: 5.0),
                              Flexible(
                                child: CustomStatefulTextInput(
                                  key: UniqueKey(),
                                  title: 'Name',
                                  value: input[index].name,
                                  listener: (hasFocus, value) async {
                                    if (!hasFocus) {
                                      await ref
                                          .read(
                                            carbonInputControllerProvider
                                                .notifier,
                                          )
                                          .updateTable(
                                            input[index].id,
                                            name: value,
                                          );
                                    }
                                  },
                                ),
                              ),
                              SizedBox(width: 5.0),
                              Builder(
                                builder: (context) {
                                  switch (input[index]) {
                                    case ConcreteArea(
                                      area: final double area,
                                      thk: final double thk,
                                      totalSteelRatio: final double
                                      totalSteelRatio,
                                    ):
                                      return Flexible(
                                        flex: 3,
                                        child: Row(
                                          children: [
                                            Flexible(
                                              child: CustomStatefulDoubleInput(
                                                key: UniqueKey(),
                                                title: 'Thk [${units[4]}]',
                                                value: thk,
                                                tooltipText:
                                                    'Thickness of slab,wall,pile cap',
                                                listener: (
                                                  hasFocus,
                                                  value,
                                                ) async {
                                                  if (!hasFocus) {
                                                    await ref
                                                        .read(
                                                          carbonInputControllerProvider
                                                              .notifier,
                                                        )
                                                        .updateTable(
                                                          input[index].id,
                                                          thk: value,
                                                        );
                                                  }
                                                },
                                              ),
                                            ),
                                            SizedBox(width: 5.0),
                                            Flexible(
                                              child: CustomStatefulDoubleInput(
                                                key: UniqueKey(),
                                                title: 'Area [${units[7]}]',
                                                value: area,
                                                tooltipText:
                                                    'On plan area of slab,pile cap OR\narea of wall in elevation view',
                                                listener: (
                                                  hasFocus,
                                                  value,
                                                ) async {
                                                  if (!hasFocus) {
                                                    await ref
                                                        .read(
                                                          carbonInputControllerProvider
                                                              .notifier,
                                                        )
                                                        .updateTable(
                                                          input[index].id,
                                                          area: value,
                                                        );
                                                  }
                                                },
                                              ),
                                            ),
                                            SizedBox(width: 5.0),
                                            Flexible(
                                              child: CustomStatefulDoubleInput(
                                                key: UniqueKey(),
                                                title: 'Steel Ratio',
                                                tooltipText:
                                                    'Top + Bottom Bars for slab, pile cap\nin steel ratio and decimal format\neg: 0.0251',
                                                value: totalSteelRatio,
                                                listener: (
                                                  hasFocus,
                                                  value,
                                                ) async {
                                                  if (!hasFocus) {
                                                    await ref
                                                        .read(
                                                          carbonInputControllerProvider
                                                              .notifier,
                                                        )
                                                        .updateTable(
                                                          input[index].id,
                                                          totalSteelRatio:
                                                              value,
                                                        );
                                                  }
                                                },
                                              ),
                                            ),
                                          ],
                                        ),
                                      );
                                    case MetalArea(area: final double area):
                                      return Flexible(
                                        flex: 2,
                                        child: Row(
                                          children: [
                                            Flexible(
                                              child: CustomStatefulDoubleInput(
                                                key: UniqueKey(),
                                                title: 'Area [${units[7]}]',
                                                value: area,
                                                tooltipText:
                                                    'On plan area of metal decking',
                                                listener: (
                                                  hasFocus,
                                                  value,
                                                ) async {
                                                  if (!hasFocus) {
                                                    await ref
                                                        .read(
                                                          carbonInputControllerProvider
                                                              .notifier,
                                                        )
                                                        .updateTable(
                                                          input[index].id,
                                                          area: value,
                                                        );
                                                  }
                                                },
                                              ),
                                            ),
                                            Flexible(
                                              child: SizedBox(width: 5.0),
                                            ),
                                          ],
                                        ),
                                      );
                                    case ConcreteLine(
                                      length: final double length,
                                      sectionSize: final double sectionSize,
                                      totalSteelRatio: final double
                                      totalSteelRatio,
                                    ):
                                      return Flexible(
                                        flex: 2,
                                        child: Row(
                                          children: [
                                            Flexible(
                                              child: CustomStatefulDoubleInput(
                                                key: UniqueKey(),
                                                title: 'Length [${units[3]}]',
                                                value: length,
                                                tooltipText:
                                                    'Length of beam, column, pile',
                                                listener: (
                                                  hasFocus,
                                                  value,
                                                ) async {
                                                  if (!hasFocus) {
                                                    await ref
                                                        .read(
                                                          carbonInputControllerProvider
                                                              .notifier,
                                                        )
                                                        .updateTable(
                                                          input[index].id,
                                                          length: value,
                                                        );
                                                  }
                                                },
                                              ),
                                            ),
                                            SizedBox(width: 5.0),
                                            Flexible(
                                              child: CustomStatefulDoubleInput(
                                                key: UniqueKey(),
                                                title: 'Section [${units[6]}]',
                                                value: sectionSize,
                                                tooltipText:
                                                    'Cross-section area of beam, column, pile',
                                                listener: (
                                                  hasFocus,
                                                  value,
                                                ) async {
                                                  if (!hasFocus) {
                                                    await ref
                                                        .read(
                                                          carbonInputControllerProvider
                                                              .notifier,
                                                        )
                                                        .updateTable(
                                                          input[index].id,
                                                          sectionSize: value,
                                                        );
                                                  }
                                                },
                                              ),
                                            ),
                                            SizedBox(width: 5.0),
                                            Flexible(
                                              child: CustomStatefulDoubleInput(
                                                key: UniqueKey(),
                                                title: 'Steel Ratio',
                                                value: totalSteelRatio,
                                                tooltipText:
                                                    'Top + Bottom Bars for beam OR\nall bars in column, pile'
                                                    ' in steel\nratio and decimal format\neg: 0.0251',
                                                listener: (
                                                  hasFocus,
                                                  value,
                                                ) async {
                                                  if (!hasFocus) {
                                                    await ref
                                                        .read(
                                                          carbonInputControllerProvider
                                                              .notifier,
                                                        )
                                                        .updateTable(
                                                          input[index].id,
                                                          totalSteelRatio:
                                                              value,
                                                        );
                                                  }
                                                },
                                              ),
                                            ),
                                          ],
                                        ),
                                      );
                                    case SteelLine(
                                      length: final double length,
                                      sectionSize: final double sectionSize,
                                    ):
                                      return Flexible(
                                        flex: 2,
                                        child: Row(
                                          children: [
                                            Flexible(
                                              child: CustomStatefulDoubleInput(
                                                key: UniqueKey(),
                                                title: 'Length [${units[3]}]',
                                                value: length,
                                                listener: (
                                                  hasFocus,
                                                  value,
                                                ) async {
                                                  if (!hasFocus) {
                                                    await ref
                                                        .read(
                                                          carbonInputControllerProvider
                                                              .notifier,
                                                        )
                                                        .updateTable(
                                                          input[index].id,
                                                          length: value,
                                                        );
                                                  }
                                                },
                                              ),
                                            ),
                                            SizedBox(width: 5.0),
                                            Flexible(
                                              child: CustomStatefulDoubleInput(
                                                key: UniqueKey(),
                                                title: 'Section [${units[6]}]',
                                                value: sectionSize,
                                                listener: (
                                                  hasFocus,
                                                  value,
                                                ) async {
                                                  if (!hasFocus) {
                                                    await ref
                                                        .read(
                                                          carbonInputControllerProvider
                                                              .notifier,
                                                        )
                                                        .updateTable(
                                                          input[index].id,
                                                          sectionSize: value,
                                                        );
                                                  }
                                                },
                                              ),
                                            ),
                                          ],
                                        ),
                                      );
                                    default:
                                      throw Exception('Invalid runtimeType');
                                  }
                                },
                              ),
                            ],
                          ),
                        );
                      },
                    ),
                  ),
                ),
              ],
            );
          },
          error: (error, stackTrace) => Text(error.toString()),
          loading: () => CircularProgressIndicator(),
        );
      },
      error: (error, stackTrace) => Text(error.toString()),
      loading: () => CircularProgressIndicator(),
    );
  }

  void _updateHeights() {
    final double screenHeight = MediaQuery.of(context).size.height;
    _maxHeight =
        (widget.maxHeight == null)
            ? screenHeight * 0.6
            : (widget.maxHeight! > screenHeight * 0.6)
            ? screenHeight * 0.6
            : widget.maxHeight!;

    _minHeight =
        (widget.minHeight == null)
            ? screenHeight * 0.05
            : (widget.minHeight! > screenHeight * 0.05)
            ? screenHeight * 0.05
            : widget.minHeight!;

    // Adjust _maxHeight if needed
    if (_maxHeight < _minHeight) {
      _maxHeight = _minHeight;
    }
  }
}

String _generateTaskID() {
  String newID = nanoid(alphabet: Alphabet.noDoppelgangerSafe);
  newID = nanoid(alphabet: Alphabet.noDoppelgangerSafe);
  return newID;
}
