// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'cantilever_scheme_data_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$cantileverSchemeDataControllerHash() =>
    r'86f7013ae968725f82a6d4dc8480a98a08db30a1';

/// See also [CantileverSchemeDataController].
@ProviderFor(CantileverSchemeDataController)
final cantileverSchemeDataControllerProvider = AutoDisposeAsyncNotifierProvider<
  CantileverSchemeDataController,
  CantileverSchemeData
>.internal(
  CantileverSchemeDataController.new,
  name: r'cantileverSchemeDataControllerProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$cantileverSchemeDataControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$CantileverSchemeDataController =
    AutoDisposeAsyncNotifier<CantileverSchemeData>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
