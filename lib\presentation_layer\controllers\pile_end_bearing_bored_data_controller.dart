import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:nanoid2/nanoid2.dart';
import 'dart:math';
import 'package:intl/intl.dart';
import 'package:structify/domain_layer/mixin/mixin_rc_str.dart';

//presentation layer
import '../../domain_layer/pile_end_bearing_bored_data.dart';
import '../../domain_layer/pile_end_bearing_bored_input.dart';
import '../screen/homescreen.dart';

// domain layer
import '../../domain_layer/global_data.dart';
import '../../domain_layer/preferences.dart';

part 'pile_end_bearing_bored_data_controller.g.dart';

@riverpod
class PileEndBearingBoredDataController
    extends _$PileEndBearingBoredDataController with RCStrHK {
  @override
  FutureOr<List<PileEndBearingBoredData>> build() async {
    // print('Build: Column Scheme Data');
    final pileEndBearingBoredDataList =
        await ref
            .read(appDatabaseControllerProvider.notifier)
            .queryPileEndBearingBoredData();

    final data1 = ref.watch(pileEndBearingBoredInputControllerProvider);
    return data1.when(
      data: (pileEndBearingBoredInput) async {
        return await pileEndBearingBoredScheming(
          existingSchemes: pileEndBearingBoredDataList,
          calledInBuild: true,
        );
      },
      error: (error, stackTrace) => <PileEndBearingBoredData>[],
      loading: () => <PileEndBearingBoredData>[],
    );
  }

  Future<void> addSchemeData(
    List<PileEndBearingBoredData> pileEndBearingBoredData,
  ) async {
    final x = await future;
    state = AsyncData([...x, ...pileEndBearingBoredData]);
  }

  Future<void> deleteTable(String id) async {
    final x = await future;
    x.removeWhere((item) => item.pileEndBearingBoredSchemeId == id);
    state = AsyncData(x);
  }

  Future<void> replaceEntireTable(
    List<PileEndBearingBoredData> pileEndBearingBoredData,
  ) async {
    state = AsyncData(pileEndBearingBoredData);
  }

  Future<List<PileEndBearingBoredData>> deleteTablesNotSelected({
    List<PileEndBearingBoredData>? existingScheme,
    bool? calledInBuild,
  }) async {
    late final List<PileEndBearingBoredData> schemes;
    if (existingScheme != null && existingScheme.isNotEmpty) {
      schemes = existingScheme;
    } else {
      if (calledInBuild != null && calledInBuild) {
        schemes = [];
      } else {
        schemes = await future;
      }
    }
    schemes.removeWhere((item) => !item.isSelected);
    state = AsyncData(schemes);
    return schemes;
  }

  Future<void> deleteAllTable() async {
    state = AsyncData([]);
  }

  Future<void> toggleSelectScheme(String pileEndBearingBoredSchemeId) async {
    final x = await future;
    final newState =
        x.map((item) {
          if (item.pileEndBearingBoredSchemeId == pileEndBearingBoredSchemeId) {
            return item.copyWith(isSelected: !item.isSelected);
          } else {
            return item.copyWith(isSelected: false);
          }
        }).toList();
    state = AsyncData(newState);
  }

  Future<String> _generateUniqueId({Set<String>? existingID}) async {
    late final Set<String> pileEndBearingBoredSchemeId;
    if (existingID == null) {
      final x = await future;
      pileEndBearingBoredSchemeId =
          x.map((item) => item.pileEndBearingBoredSchemeId).toSet();
    } else {
      pileEndBearingBoredSchemeId = existingID;
    }
    String newID = nanoid(alphabet: Alphabet.noDoppelgangerSafe);
    while (pileEndBearingBoredSchemeId.contains(newID)) {
      newID = nanoid(alphabet: Alphabet.noDoppelgangerSafe);
    }
    return newID;
  }

  Future<List<PileEndBearingBoredData>> pileEndBearingBoredScheming({
    List<PileEndBearingBoredData>? existingSchemes,
    bool? calledInBuild,
  }) async {
    //* keep selected schemes
    late final List<PileEndBearingBoredData> schemesLeft;
    final List<PileEndBearingBoredData> finalList = [];

    schemesLeft = await deleteTablesNotSelected(
      existingScheme: existingSchemes,
      calledInBuild: calledInBuild,
    );
    finalList.addAll(schemesLeft);
    final existingID =
        existingSchemes?.map((e) => e.pileEndBearingBoredSchemeId).toSet();
    final globalData = await ref.read(globalDataControllerProvider.future);
    final pileInput = await ref.read(
      pileEndBearingBoredInputControllerProvider.future,
    );

    //* Initialization
    // record params
    StringBuffer buffer = StringBuffer(); // record the cals

    // capacity param
    double baseCapacity = 0, totalGroundResistance = 0, strCapacity = 0;

    // material params
    final double fy = 500;

    // initial iteration param
    double pileDiaIntl = 600, rIntl = 0.005;

    // iteration param
    double rDelta = 0.005;

    // geometric param
    double clearS = 0, rActual = 0, cover = 75, assumedLinksDia = 12;
    List<int> rebar = [40];

    // limits
    double minClearS = 80, maxClearS = 260; //(~300c/c)

    // loop controls
    bool skipSteelRatio = false;

    final double fcu = pileInput.fcu;
    final double ratioOfBelloutDia = pileInput.ratioOfBelloutDia;

    // min rebar clear
    int n = 0;
    String mainBarCircle = '';
    //* get column capacity thru looping
    for (
      double pileDia = pileDiaIntl;
      pileDia <= pileInput.maxPileDiameter;
      pileDia += pileInput.diaIncrement
    ) {
      for (
        double rTarget = rIntl;
        rTarget < pileInput.maxSteelRatio;
        rTarget += rDelta
      ) {
        if (skipSteelRatio) {
          skipSteelRatio = false;
          break;
        }
        for (int i = 0; i < rebar.length; i++) {
          n = max(
            6,
            ((rTarget * pow(pileDia, 2) * pi / 4) / (pow(rebar[i], 2) * pi / 4))
                .ceil(),
          );
          clearS =
              ((pileDia / 2 - (cover + assumedLinksDia + rebar[i] / 2)) *
                      2 *
                      pi -
                  (n * rebar[i])) /
              (n - 1);
          rActual = n * pow(rebar[i], 2) * pi / 4 / (pow(pileDia, 2) * pi / 4);

          baseCapacity = getEndBearningCapacity(
            pileDia,
            pileInput.safeBearing,
            ratioOfBelloutDia: ratioOfBelloutDia,
            factorOfSafety: pileInput.fos,
          );

          totalGroundResistance = baseCapacity;

          strCapacity = getAxialCapacityColumnCircle(fcu, pileDia, fy, rActual);

          mainBarCircle = '${n}T${rebar[i]}';

          //* save scheme ONLY when criteria satisfied
          if (totalGroundResistance >= pileInput.slsLoad &&
              strCapacity >= pileInput.ulsLoad &&
              clearS >= minClearS &&
              clearS <= maxClearS &&
              rActual <= pileInput.maxSteelRatio) {
            _recordCalsResult(
              buffer,
              input: pileInput,
              globalData: globalData,
              size: pileDia,
              fcu: fcu,
              cover: cover,
              steelRatioCircle: rActual,
              mainBarCircle: mainBarCircle,
              diameter: pileDia,
              baseCapacity: baseCapacity,
              totalGroundResistance: totalGroundResistance,
              strCapacity: strCapacity,
            );

            finalList.add(
              PileEndBearingBoredData(
                safeBearing: pileInput.safeBearing,
                fos: pileInput.fos,
                fcu: pileInput.fcu,
                maxPileDiameter: pileInput.maxPileDiameter,
                maxSteelRatio: pileInput.maxSteelRatio,
                slsLoad: pileInput.slsLoad,
                ulsLoad: pileInput.ulsLoad,
                ratioOfBelloutDia: pileInput.ratioOfBelloutDia,
                diameter: pileDia,
                baseCapacity: baseCapacity,
                totalGroundResistance: totalGroundResistance,
                strCapacity: strCapacity,
                rebar: mainBarCircle,
                steelRatio: rActual,
                isSelected: false,
                calsLog: buffer.toString(),
                pileEndBearingBoredSchemeId: await _generateUniqueId(
                  existingID: existingID,
                ),
              ),
            );
            skipSteelRatio = true;
          }
        }
      }
    }
    state = AsyncData(finalList);
    return finalList;
  }

  void _recordCalsResult(
    StringBuffer buffer, {
    required PileEndBearingBoredInput input,
    required GlobalData globalData,
    required double size,
    required double fcu,
    required double cover,
    required double steelRatioCircle,
    required String mainBarCircle,
    required double diameter,
    required double baseCapacity,
    required double totalGroundResistance,
    required double strCapacity,
  }) {
    late final List<String> unit;
    NumberFormat f0 = NumberFormat('0'),
        f1 = NumberFormat('0.0'),
        f2 = NumberFormat('0.00');
    switch (globalData.unit) {
      case 'metrics':
        unit = PreferredUnit.metrics;
        break;
      case 'imperial':
        unit = PreferredUnit.imperial;
        break;
      default:
        unit = PreferredUnit.metrics;
        break;
    }
    buffer.clear();
    buffer.write('SLS Total: ${f0.format(input.slsLoad)} [${unit[0]}] | ');
    buffer.write('ULS Total: ${f0.format(input.ulsLoad)} [${unit[0]}]\n');
    buffer.write('Diameter: ${f0.format(size)} [${unit[4]}] | ');
    buffer.write('fcu: ${f0.format(input.fcu)} [${unit[5]}] | ');
    buffer.write('Cover: ${f0.format(40)} [${unit[4]}]\n');
    buffer.write('FOS: ${f2.format(input.fos)}\n');
    buffer.write(
      'Bellout Used: ${input.ratioOfBelloutDia > 1 ? 'Yes' : 'No'} | ',
    );
    buffer.write(
      'Bellout Diameter: ${input.ratioOfBelloutDia > 1 ? f0.format(diameter * input.ratioOfBelloutDia) : 'N.A.'} [${unit[4]}]\n',
    );
    buffer.write('Base Capacity: ${f0.format(baseCapacity)} [${unit[0]}] | ');
    buffer.write(
      'Total Ground Resistance: ${f0.format(totalGroundResistance)} [${unit[0]}]\n',
    );
    buffer.write('Rebar: $mainBarCircle | ');
    buffer.write(
      'Structural Capacity: ${f0.format(strCapacity)} [${unit[0]}]\n',
    );
    if (totalGroundResistance > input.slsLoad) {
      buffer.write('Ground Capacity: pass\n');
    } else {
      buffer.write('Ground Capacity: fail\n');
    }
    if (strCapacity > input.ulsLoad) {
      buffer.write('Structural Capacity: pass\n');
    } else {
      buffer.write('Structural Capacity: fail\n');
    }
  }
}
