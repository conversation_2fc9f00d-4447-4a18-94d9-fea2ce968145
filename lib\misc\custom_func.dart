import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:structify/presentation_layer/screen/summary_pane.dart';
import 'package:xml/xml.dart';

double roundTo(double value, int decimalPlaces) {
  final double mod = pow(10.0, decimalPlaces) as double;
  final double result = (value * mod).round() / mod;
  return (result);
}

T maxFromNum<T extends num>(List<T> numbers) {
  if (numbers.isEmpty) {
    throw Exception("The list cannot be empty.");
  }

  // Check if T is a number type
  if (T != int && T != double) {
    throw Exception("Type T must be either int or double.");
  }

  return numbers.reduce((a, b) => a >= b ? a : b);
}

T minFromNum<T extends num>(List<T> numbers) {
  if (numbers.isEmpty) {
    throw Exception("The list cannot be empty.");
  }

  // Check if T is a number type
  if (T != int && T != double) {
    throw Exception("Type T must be either int or double.");
  }

  return numbers.reduce((a, b) => a <= b ? a : b);
}

Future<void> showSummary(BuildContext context) async {
  await showGeneralDialog(
    context: context,
    barrierDismissible: true,
    barrierLabel: '',
    // barrierColor: Colors.transparent,
    transitionDuration: const Duration(milliseconds: 400),
    pageBuilder: (context, animation, secondaryAnimation) {
      return Align(
        alignment: Alignment(0.8, 0),
        child: Container(
          width: MediaQuery.of(context).size.width*0.4,
          height: MediaQuery.of(context).size.height*0.8,
          decoration: BoxDecoration(
            color: Colors.transparent,
            borderRadius: BorderRadius.circular(15.0),
            border: Border.all(color: Colors.grey.withAlpha(200)),
        
          ),
          child: SummaryPane(),
        ),
      );
    },
    transitionBuilder: (context, animation, secondaryAnimation, child) {
    final curvedAnimation = CurvedAnimation(
      parent: animation,
      curve: Curves.easeOut,
      reverseCurve: Curves.easeIn,
    );
    return SlideTransition(
      position: Tween<Offset>(
        begin: const Offset(0.1, 0), // Start off-screen right
        end: Offset.zero,          // End at center
      ).animate(curvedAnimation),
      child: FadeTransition(
        opacity: curvedAnimation,
        child: child),
    );
  },
  );
}

Future<void> showAssumption(BuildContext context, Widget assumption) async {
  ColorScheme colorScheme = Theme.of(context).colorScheme;
  await showGeneralDialog(
    context: context,
    barrierDismissible: true,
    barrierLabel: '',
    // barrierColor: Colors.transparent,
    transitionDuration: const Duration(milliseconds: 400),
    pageBuilder: (context, animation, secondaryAnimation) {
      return Align(
        alignment: Alignment(0.8, 0),
        child: Container(
          width: MediaQuery.of(context).size.width*0.4,
          height: MediaQuery.of(context).size.height*0.5,
          decoration: BoxDecoration(
            color: colorScheme.inverseSurface,
            borderRadius: BorderRadius.circular(10.0),
            border: Border.all(color: Colors.grey.withAlpha(200)),
        
          ),
          child: Padding(
            padding: const EdgeInsets.all(8.0),
            child: assumption,
          ),
        ),
      );
    },
    transitionBuilder: (context, animation, secondaryAnimation, child) {
    final curvedAnimation = CurvedAnimation(
      parent: animation,
      curve: Curves.easeOut,
      reverseCurve: Curves.easeIn,
    );
    return SlideTransition(
      position: Tween<Offset>(
        begin: const Offset(0.1, 0), // Start off-screen right
        end: Offset.zero,          // End at center
      ).animate(curvedAnimation),
      child: FadeTransition(
        opacity: curvedAnimation,
        child: child),
    );
  },
  );
}