// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'beam_scheme_data.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$BeamSchemeData {

 String get usage; double get finish; double get service; double get liveLoad; String get loadingTableId; double get shortSpan; double get longSpan; int get bays; double get mainStrZone; double get secStrZone; double get fcu; double get cover; double get mainWidth; double get secWidth; String get mainTopBar; String get mainBottomBar; String get mainLinks; String get secTopBar; String get secBottomBar; String get secLinks; String get beamSchemeId;//will be overriden  as soon as new instance created
 String get calsLog; bool get isSelected;
/// Create a copy of BeamSchemeData
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$BeamSchemeDataCopyWith<BeamSchemeData> get copyWith => _$BeamSchemeDataCopyWithImpl<BeamSchemeData>(this as BeamSchemeData, _$identity);

  /// Serializes this BeamSchemeData to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is BeamSchemeData&&(identical(other.usage, usage) || other.usage == usage)&&(identical(other.finish, finish) || other.finish == finish)&&(identical(other.service, service) || other.service == service)&&(identical(other.liveLoad, liveLoad) || other.liveLoad == liveLoad)&&(identical(other.loadingTableId, loadingTableId) || other.loadingTableId == loadingTableId)&&(identical(other.shortSpan, shortSpan) || other.shortSpan == shortSpan)&&(identical(other.longSpan, longSpan) || other.longSpan == longSpan)&&(identical(other.bays, bays) || other.bays == bays)&&(identical(other.mainStrZone, mainStrZone) || other.mainStrZone == mainStrZone)&&(identical(other.secStrZone, secStrZone) || other.secStrZone == secStrZone)&&(identical(other.fcu, fcu) || other.fcu == fcu)&&(identical(other.cover, cover) || other.cover == cover)&&(identical(other.mainWidth, mainWidth) || other.mainWidth == mainWidth)&&(identical(other.secWidth, secWidth) || other.secWidth == secWidth)&&(identical(other.mainTopBar, mainTopBar) || other.mainTopBar == mainTopBar)&&(identical(other.mainBottomBar, mainBottomBar) || other.mainBottomBar == mainBottomBar)&&(identical(other.mainLinks, mainLinks) || other.mainLinks == mainLinks)&&(identical(other.secTopBar, secTopBar) || other.secTopBar == secTopBar)&&(identical(other.secBottomBar, secBottomBar) || other.secBottomBar == secBottomBar)&&(identical(other.secLinks, secLinks) || other.secLinks == secLinks)&&(identical(other.beamSchemeId, beamSchemeId) || other.beamSchemeId == beamSchemeId)&&(identical(other.calsLog, calsLog) || other.calsLog == calsLog)&&(identical(other.isSelected, isSelected) || other.isSelected == isSelected));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hashAll([runtimeType,usage,finish,service,liveLoad,loadingTableId,shortSpan,longSpan,bays,mainStrZone,secStrZone,fcu,cover,mainWidth,secWidth,mainTopBar,mainBottomBar,mainLinks,secTopBar,secBottomBar,secLinks,beamSchemeId,calsLog,isSelected]);

@override
String toString() {
  return 'BeamSchemeData(usage: $usage, finish: $finish, service: $service, liveLoad: $liveLoad, loadingTableId: $loadingTableId, shortSpan: $shortSpan, longSpan: $longSpan, bays: $bays, mainStrZone: $mainStrZone, secStrZone: $secStrZone, fcu: $fcu, cover: $cover, mainWidth: $mainWidth, secWidth: $secWidth, mainTopBar: $mainTopBar, mainBottomBar: $mainBottomBar, mainLinks: $mainLinks, secTopBar: $secTopBar, secBottomBar: $secBottomBar, secLinks: $secLinks, beamSchemeId: $beamSchemeId, calsLog: $calsLog, isSelected: $isSelected)';
}


}

/// @nodoc
abstract mixin class $BeamSchemeDataCopyWith<$Res>  {
  factory $BeamSchemeDataCopyWith(BeamSchemeData value, $Res Function(BeamSchemeData) _then) = _$BeamSchemeDataCopyWithImpl;
@useResult
$Res call({
 String usage, double finish, double service, double liveLoad, String loadingTableId, double shortSpan, double longSpan, int bays, double mainStrZone, double secStrZone, double fcu, double cover, double mainWidth, double secWidth, String mainTopBar, String mainBottomBar, String mainLinks, String secTopBar, String secBottomBar, String secLinks, String beamSchemeId, String calsLog, bool isSelected
});




}
/// @nodoc
class _$BeamSchemeDataCopyWithImpl<$Res>
    implements $BeamSchemeDataCopyWith<$Res> {
  _$BeamSchemeDataCopyWithImpl(this._self, this._then);

  final BeamSchemeData _self;
  final $Res Function(BeamSchemeData) _then;

/// Create a copy of BeamSchemeData
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? usage = null,Object? finish = null,Object? service = null,Object? liveLoad = null,Object? loadingTableId = null,Object? shortSpan = null,Object? longSpan = null,Object? bays = null,Object? mainStrZone = null,Object? secStrZone = null,Object? fcu = null,Object? cover = null,Object? mainWidth = null,Object? secWidth = null,Object? mainTopBar = null,Object? mainBottomBar = null,Object? mainLinks = null,Object? secTopBar = null,Object? secBottomBar = null,Object? secLinks = null,Object? beamSchemeId = null,Object? calsLog = null,Object? isSelected = null,}) {
  return _then(_self.copyWith(
usage: null == usage ? _self.usage : usage // ignore: cast_nullable_to_non_nullable
as String,finish: null == finish ? _self.finish : finish // ignore: cast_nullable_to_non_nullable
as double,service: null == service ? _self.service : service // ignore: cast_nullable_to_non_nullable
as double,liveLoad: null == liveLoad ? _self.liveLoad : liveLoad // ignore: cast_nullable_to_non_nullable
as double,loadingTableId: null == loadingTableId ? _self.loadingTableId : loadingTableId // ignore: cast_nullable_to_non_nullable
as String,shortSpan: null == shortSpan ? _self.shortSpan : shortSpan // ignore: cast_nullable_to_non_nullable
as double,longSpan: null == longSpan ? _self.longSpan : longSpan // ignore: cast_nullable_to_non_nullable
as double,bays: null == bays ? _self.bays : bays // ignore: cast_nullable_to_non_nullable
as int,mainStrZone: null == mainStrZone ? _self.mainStrZone : mainStrZone // ignore: cast_nullable_to_non_nullable
as double,secStrZone: null == secStrZone ? _self.secStrZone : secStrZone // ignore: cast_nullable_to_non_nullable
as double,fcu: null == fcu ? _self.fcu : fcu // ignore: cast_nullable_to_non_nullable
as double,cover: null == cover ? _self.cover : cover // ignore: cast_nullable_to_non_nullable
as double,mainWidth: null == mainWidth ? _self.mainWidth : mainWidth // ignore: cast_nullable_to_non_nullable
as double,secWidth: null == secWidth ? _self.secWidth : secWidth // ignore: cast_nullable_to_non_nullable
as double,mainTopBar: null == mainTopBar ? _self.mainTopBar : mainTopBar // ignore: cast_nullable_to_non_nullable
as String,mainBottomBar: null == mainBottomBar ? _self.mainBottomBar : mainBottomBar // ignore: cast_nullable_to_non_nullable
as String,mainLinks: null == mainLinks ? _self.mainLinks : mainLinks // ignore: cast_nullable_to_non_nullable
as String,secTopBar: null == secTopBar ? _self.secTopBar : secTopBar // ignore: cast_nullable_to_non_nullable
as String,secBottomBar: null == secBottomBar ? _self.secBottomBar : secBottomBar // ignore: cast_nullable_to_non_nullable
as String,secLinks: null == secLinks ? _self.secLinks : secLinks // ignore: cast_nullable_to_non_nullable
as String,beamSchemeId: null == beamSchemeId ? _self.beamSchemeId : beamSchemeId // ignore: cast_nullable_to_non_nullable
as String,calsLog: null == calsLog ? _self.calsLog : calsLog // ignore: cast_nullable_to_non_nullable
as String,isSelected: null == isSelected ? _self.isSelected : isSelected // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}

}


/// Adds pattern-matching-related methods to [BeamSchemeData].
extension BeamSchemeDataPatterns on BeamSchemeData {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _BeamSchemeData value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _BeamSchemeData() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _BeamSchemeData value)  $default,){
final _that = this;
switch (_that) {
case _BeamSchemeData():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _BeamSchemeData value)?  $default,){
final _that = this;
switch (_that) {
case _BeamSchemeData() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String usage,  double finish,  double service,  double liveLoad,  String loadingTableId,  double shortSpan,  double longSpan,  int bays,  double mainStrZone,  double secStrZone,  double fcu,  double cover,  double mainWidth,  double secWidth,  String mainTopBar,  String mainBottomBar,  String mainLinks,  String secTopBar,  String secBottomBar,  String secLinks,  String beamSchemeId,  String calsLog,  bool isSelected)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _BeamSchemeData() when $default != null:
return $default(_that.usage,_that.finish,_that.service,_that.liveLoad,_that.loadingTableId,_that.shortSpan,_that.longSpan,_that.bays,_that.mainStrZone,_that.secStrZone,_that.fcu,_that.cover,_that.mainWidth,_that.secWidth,_that.mainTopBar,_that.mainBottomBar,_that.mainLinks,_that.secTopBar,_that.secBottomBar,_that.secLinks,_that.beamSchemeId,_that.calsLog,_that.isSelected);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String usage,  double finish,  double service,  double liveLoad,  String loadingTableId,  double shortSpan,  double longSpan,  int bays,  double mainStrZone,  double secStrZone,  double fcu,  double cover,  double mainWidth,  double secWidth,  String mainTopBar,  String mainBottomBar,  String mainLinks,  String secTopBar,  String secBottomBar,  String secLinks,  String beamSchemeId,  String calsLog,  bool isSelected)  $default,) {final _that = this;
switch (_that) {
case _BeamSchemeData():
return $default(_that.usage,_that.finish,_that.service,_that.liveLoad,_that.loadingTableId,_that.shortSpan,_that.longSpan,_that.bays,_that.mainStrZone,_that.secStrZone,_that.fcu,_that.cover,_that.mainWidth,_that.secWidth,_that.mainTopBar,_that.mainBottomBar,_that.mainLinks,_that.secTopBar,_that.secBottomBar,_that.secLinks,_that.beamSchemeId,_that.calsLog,_that.isSelected);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String usage,  double finish,  double service,  double liveLoad,  String loadingTableId,  double shortSpan,  double longSpan,  int bays,  double mainStrZone,  double secStrZone,  double fcu,  double cover,  double mainWidth,  double secWidth,  String mainTopBar,  String mainBottomBar,  String mainLinks,  String secTopBar,  String secBottomBar,  String secLinks,  String beamSchemeId,  String calsLog,  bool isSelected)?  $default,) {final _that = this;
switch (_that) {
case _BeamSchemeData() when $default != null:
return $default(_that.usage,_that.finish,_that.service,_that.liveLoad,_that.loadingTableId,_that.shortSpan,_that.longSpan,_that.bays,_that.mainStrZone,_that.secStrZone,_that.fcu,_that.cover,_that.mainWidth,_that.secWidth,_that.mainTopBar,_that.mainBottomBar,_that.mainLinks,_that.secTopBar,_that.secBottomBar,_that.secLinks,_that.beamSchemeId,_that.calsLog,_that.isSelected);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _BeamSchemeData extends BeamSchemeData {
   _BeamSchemeData({this.usage = '', this.finish = 0.0, this.service = 0.0, this.liveLoad = 0.0, this.loadingTableId = '', this.shortSpan = 5.0, this.longSpan = 12.0, this.bays = 2, this.mainStrZone = 500.0, this.secStrZone = 500.0, this.fcu = 45.0, this.cover = 40.0, this.mainWidth = 200.0, this.secWidth = 200.0, this.mainTopBar = '', this.mainBottomBar = '', this.mainLinks = '', this.secTopBar = '', this.secBottomBar = '', this.secLinks = '', this.beamSchemeId = '', this.calsLog = '', this.isSelected = false}): super._();
  factory _BeamSchemeData.fromJson(Map<String, dynamic> json) => _$BeamSchemeDataFromJson(json);

@override@JsonKey() final  String usage;
@override@JsonKey() final  double finish;
@override@JsonKey() final  double service;
@override@JsonKey() final  double liveLoad;
@override@JsonKey() final  String loadingTableId;
@override@JsonKey() final  double shortSpan;
@override@JsonKey() final  double longSpan;
@override@JsonKey() final  int bays;
@override@JsonKey() final  double mainStrZone;
@override@JsonKey() final  double secStrZone;
@override@JsonKey() final  double fcu;
@override@JsonKey() final  double cover;
@override@JsonKey() final  double mainWidth;
@override@JsonKey() final  double secWidth;
@override@JsonKey() final  String mainTopBar;
@override@JsonKey() final  String mainBottomBar;
@override@JsonKey() final  String mainLinks;
@override@JsonKey() final  String secTopBar;
@override@JsonKey() final  String secBottomBar;
@override@JsonKey() final  String secLinks;
@override@JsonKey() final  String beamSchemeId;
//will be overriden  as soon as new instance created
@override@JsonKey() final  String calsLog;
@override@JsonKey() final  bool isSelected;

/// Create a copy of BeamSchemeData
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$BeamSchemeDataCopyWith<_BeamSchemeData> get copyWith => __$BeamSchemeDataCopyWithImpl<_BeamSchemeData>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$BeamSchemeDataToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _BeamSchemeData&&(identical(other.usage, usage) || other.usage == usage)&&(identical(other.finish, finish) || other.finish == finish)&&(identical(other.service, service) || other.service == service)&&(identical(other.liveLoad, liveLoad) || other.liveLoad == liveLoad)&&(identical(other.loadingTableId, loadingTableId) || other.loadingTableId == loadingTableId)&&(identical(other.shortSpan, shortSpan) || other.shortSpan == shortSpan)&&(identical(other.longSpan, longSpan) || other.longSpan == longSpan)&&(identical(other.bays, bays) || other.bays == bays)&&(identical(other.mainStrZone, mainStrZone) || other.mainStrZone == mainStrZone)&&(identical(other.secStrZone, secStrZone) || other.secStrZone == secStrZone)&&(identical(other.fcu, fcu) || other.fcu == fcu)&&(identical(other.cover, cover) || other.cover == cover)&&(identical(other.mainWidth, mainWidth) || other.mainWidth == mainWidth)&&(identical(other.secWidth, secWidth) || other.secWidth == secWidth)&&(identical(other.mainTopBar, mainTopBar) || other.mainTopBar == mainTopBar)&&(identical(other.mainBottomBar, mainBottomBar) || other.mainBottomBar == mainBottomBar)&&(identical(other.mainLinks, mainLinks) || other.mainLinks == mainLinks)&&(identical(other.secTopBar, secTopBar) || other.secTopBar == secTopBar)&&(identical(other.secBottomBar, secBottomBar) || other.secBottomBar == secBottomBar)&&(identical(other.secLinks, secLinks) || other.secLinks == secLinks)&&(identical(other.beamSchemeId, beamSchemeId) || other.beamSchemeId == beamSchemeId)&&(identical(other.calsLog, calsLog) || other.calsLog == calsLog)&&(identical(other.isSelected, isSelected) || other.isSelected == isSelected));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hashAll([runtimeType,usage,finish,service,liveLoad,loadingTableId,shortSpan,longSpan,bays,mainStrZone,secStrZone,fcu,cover,mainWidth,secWidth,mainTopBar,mainBottomBar,mainLinks,secTopBar,secBottomBar,secLinks,beamSchemeId,calsLog,isSelected]);

@override
String toString() {
  return 'BeamSchemeData(usage: $usage, finish: $finish, service: $service, liveLoad: $liveLoad, loadingTableId: $loadingTableId, shortSpan: $shortSpan, longSpan: $longSpan, bays: $bays, mainStrZone: $mainStrZone, secStrZone: $secStrZone, fcu: $fcu, cover: $cover, mainWidth: $mainWidth, secWidth: $secWidth, mainTopBar: $mainTopBar, mainBottomBar: $mainBottomBar, mainLinks: $mainLinks, secTopBar: $secTopBar, secBottomBar: $secBottomBar, secLinks: $secLinks, beamSchemeId: $beamSchemeId, calsLog: $calsLog, isSelected: $isSelected)';
}


}

/// @nodoc
abstract mixin class _$BeamSchemeDataCopyWith<$Res> implements $BeamSchemeDataCopyWith<$Res> {
  factory _$BeamSchemeDataCopyWith(_BeamSchemeData value, $Res Function(_BeamSchemeData) _then) = __$BeamSchemeDataCopyWithImpl;
@override @useResult
$Res call({
 String usage, double finish, double service, double liveLoad, String loadingTableId, double shortSpan, double longSpan, int bays, double mainStrZone, double secStrZone, double fcu, double cover, double mainWidth, double secWidth, String mainTopBar, String mainBottomBar, String mainLinks, String secTopBar, String secBottomBar, String secLinks, String beamSchemeId, String calsLog, bool isSelected
});




}
/// @nodoc
class __$BeamSchemeDataCopyWithImpl<$Res>
    implements _$BeamSchemeDataCopyWith<$Res> {
  __$BeamSchemeDataCopyWithImpl(this._self, this._then);

  final _BeamSchemeData _self;
  final $Res Function(_BeamSchemeData) _then;

/// Create a copy of BeamSchemeData
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? usage = null,Object? finish = null,Object? service = null,Object? liveLoad = null,Object? loadingTableId = null,Object? shortSpan = null,Object? longSpan = null,Object? bays = null,Object? mainStrZone = null,Object? secStrZone = null,Object? fcu = null,Object? cover = null,Object? mainWidth = null,Object? secWidth = null,Object? mainTopBar = null,Object? mainBottomBar = null,Object? mainLinks = null,Object? secTopBar = null,Object? secBottomBar = null,Object? secLinks = null,Object? beamSchemeId = null,Object? calsLog = null,Object? isSelected = null,}) {
  return _then(_BeamSchemeData(
usage: null == usage ? _self.usage : usage // ignore: cast_nullable_to_non_nullable
as String,finish: null == finish ? _self.finish : finish // ignore: cast_nullable_to_non_nullable
as double,service: null == service ? _self.service : service // ignore: cast_nullable_to_non_nullable
as double,liveLoad: null == liveLoad ? _self.liveLoad : liveLoad // ignore: cast_nullable_to_non_nullable
as double,loadingTableId: null == loadingTableId ? _self.loadingTableId : loadingTableId // ignore: cast_nullable_to_non_nullable
as String,shortSpan: null == shortSpan ? _self.shortSpan : shortSpan // ignore: cast_nullable_to_non_nullable
as double,longSpan: null == longSpan ? _self.longSpan : longSpan // ignore: cast_nullable_to_non_nullable
as double,bays: null == bays ? _self.bays : bays // ignore: cast_nullable_to_non_nullable
as int,mainStrZone: null == mainStrZone ? _self.mainStrZone : mainStrZone // ignore: cast_nullable_to_non_nullable
as double,secStrZone: null == secStrZone ? _self.secStrZone : secStrZone // ignore: cast_nullable_to_non_nullable
as double,fcu: null == fcu ? _self.fcu : fcu // ignore: cast_nullable_to_non_nullable
as double,cover: null == cover ? _self.cover : cover // ignore: cast_nullable_to_non_nullable
as double,mainWidth: null == mainWidth ? _self.mainWidth : mainWidth // ignore: cast_nullable_to_non_nullable
as double,secWidth: null == secWidth ? _self.secWidth : secWidth // ignore: cast_nullable_to_non_nullable
as double,mainTopBar: null == mainTopBar ? _self.mainTopBar : mainTopBar // ignore: cast_nullable_to_non_nullable
as String,mainBottomBar: null == mainBottomBar ? _self.mainBottomBar : mainBottomBar // ignore: cast_nullable_to_non_nullable
as String,mainLinks: null == mainLinks ? _self.mainLinks : mainLinks // ignore: cast_nullable_to_non_nullable
as String,secTopBar: null == secTopBar ? _self.secTopBar : secTopBar // ignore: cast_nullable_to_non_nullable
as String,secBottomBar: null == secBottomBar ? _self.secBottomBar : secBottomBar // ignore: cast_nullable_to_non_nullable
as String,secLinks: null == secLinks ? _self.secLinks : secLinks // ignore: cast_nullable_to_non_nullable
as String,beamSchemeId: null == beamSchemeId ? _self.beamSchemeId : beamSchemeId // ignore: cast_nullable_to_non_nullable
as String,calsLog: null == calsLog ? _self.calsLog : calsLog // ignore: cast_nullable_to_non_nullable
as String,isSelected: null == isSelected ? _self.isSelected : isSelected // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}


}

// dart format on
