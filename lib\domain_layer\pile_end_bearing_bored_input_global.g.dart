// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'pile_end_bearing_bored_input_global.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_PileEndBearingBoredInputGlobal _$PileEndBearingBoredInputGlobalFromJson(
  Map<String, dynamic> json,
) => _PileEndBearingBoredInputGlobal(
  colLoadFactor: (json['colLoadFactor'] as num?)?.toDouble() ?? 1.0,
  id: json['id'] as String? ?? '1',
);

Map<String, dynamic> _$PileEndBearingBoredInputGlobalToJson(
  _PileEndBearingBoredInputGlobal instance,
) => <String, dynamic>{
  'colLoadFactor': instance.colLoadFactor,
  'id': instance.id,
};
