import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:structify/domain_layer/global_data.dart';
import 'package:structify/domain_layer/mixin/mixin_tools_for_ui.dart';
import 'package:structify/domain_layer/steel_beam_scheme_data.dart';
import 'package:structify/presentation_layer/small_elements/sketch/draw_steel_beam_scheme_sketch.dart';
import 'package:structify/presentation_layer/small_elements/button/function_button.dart';

//below for printing
// import 'dart:typed_data';
// import 'dart:ui';

//domain layer
import '../../domain_layer/mixin/mixin_steel_str.dart';
import '../../domain_layer/preferences.dart';

// presentation layer
import '../../domain_layer/steel_beam_scheme_input.dart';
import '../screen/homescreen.dart';

//misc

class SteelBeamSchemeSummary extends ConsumerStatefulWidget {
  const SteelBeamSchemeSummary({
    this.isExpanded,
    this.maxHeight,
    this.minHeight,
    super.key,
  });

  final bool? isExpanded;
  final double? maxHeight;
  final double? minHeight;

  @override
  ConsumerState<ConsumerStatefulWidget> createState() =>
      _SteelBeamSchemeSummaryState();
}

class _SteelBeamSchemeSummaryState extends ConsumerState<SteelBeamSchemeSummary>
    with WidgetsBindingObserver, MixinToolsForUI, SteelStrHK {
  late bool _isExpanded;
  late double _maxHeight;
  late double _minHeight;

  late ScrollController _scrollController;

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void initState() {
    WidgetsBinding.instance.addObserver(this);
    _isExpanded = widget.isExpanded ?? false;
    _maxHeight = widget.maxHeight ?? 300;
    _minHeight = widget.minHeight ?? 50;
    _scrollController = ScrollController();
    // _updateHeights();

    super.initState();
  }

  @override
  void didChangeDependencies() {
    _updateHeights();
    super.didChangeDependencies();
    // Update heights when dependencies change, including after initState
  }

  @override
  void didChangeMetrics() {
    // This method is called when the metrics change (like resizing the window)
    setState(() {
      _updateHeights();
    });
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    final steelBeamSchemeTables = ref.watch(
      steelBeamSchemeDataControllerProvider,
    );
    final globalData = ref.watch(globalDataControllerProvider);

    final TextStyle subtitleStyle = textTheme.labelMedium!;
    final TextStyle bodyStyle = textTheme.bodySmall!;

    return steelBeamSchemeTables.when(
      data: (tables) {
        //start reutnr real stuffs when loading tables available
        return globalData.when(
          //also, start reutnr real stuffs when global data tables available
          data: (data) {
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding: const EdgeInsets.fromLTRB(10, 0, 10, 0),
                  child: Divider(),
                ),

                Padding(
                  padding: const EdgeInsets.fromLTRB(10, 0, 0, 0),
                  child: Align(
                    alignment: Alignment.centerLeft,
                    child: Wrap(
                      crossAxisAlignment: WrapCrossAlignment.center,
                      children: [
                        IconButton(
                          icon: Icon(
                            _isExpanded ? Icons.expand_less : Icons.expand_more,
                          ),
                          color: colorScheme.onSurface,
                          onPressed: () {
                            setState(() {
                              _isExpanded = !_isExpanded;
                            });
                          },
                        ),
                        Text(
                          'Steel Beam Scheme Summary ',
                          style: textTheme.titleLarge!.copyWith(
                            color: colorScheme.onSurface,
                          ),
                        ),
                        Container(
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(5.0),
                            color: colorScheme.surfaceContainer.withAlpha(225),
                            border: Border.all(
                              color: Colors.black.withAlpha(125),
                            ),
                          ),
                          child: Padding(
                            padding: const EdgeInsets.all(2.0),
                            child: Text(
                              '(Total: ${tables.length})',
                              style: textTheme.labelMedium!.copyWith(
                                color: colorScheme.onSurfaceVariant.withAlpha(
                                  225,
                                ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),

                Padding(
                  padding: const EdgeInsets.fromLTRB(10, 0, 10, 0),
                  child: Divider(),
                ),

                ClipRect(
                  child: ConstrainedBox(
                    constraints: BoxConstraints(
                      maxHeight: _isExpanded ? _maxHeight : 0,
                    ),
                    child: Column(
                      children: [
                        Builder(
                          builder: (context) {
                            if (tables.isEmpty) {
                              return Padding(
                                padding: const EdgeInsets.fromLTRB(
                                  10,
                                  0,
                                  10,
                                  0,
                                ),
                                child: Container(
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(5.0),
                                    color: colorScheme.errorContainer.withAlpha(
                                      100,
                                    ),
                                    border: Border.all(
                                      width: 0.5,
                                      color: colorScheme.error.withAlpha(100),
                                    ),
                                  ),
                                  child: Padding(
                                    padding: const EdgeInsets.all(4.0),
                                    child: Text(
                                      'No Steel Beam Scheme Data',
                                      style: textTheme.labelMedium!.copyWith(
                                        color: colorScheme.error,
                                      ),
                                    ),
                                  ),
                                ),
                              );
                            } else {
                              return SizedBox(height: 0, width: 0);
                            }
                          },
                        ),

                        Flexible(
                          child: Scrollbar(
                            controller: _scrollController,
                            thumbVisibility: true,
                            child: DefaultTextStyle(
                              style: textTheme.bodySmall!.copyWith(
                                color: colorScheme.onSurface,
                              ),
                              child: ListView.builder(
                                controller: _scrollController,
                                itemCount: tables.length,
                                itemBuilder: (context, index) {
                                  late Widget mainBeamStatus;
                                  late Widget secBeamStatus;

                                  final RegExp checker1 = RegExp(
                                    r'fail',
                                    caseSensitive: false,
                                  );

                                  final bool mainBeamStatusBool =
                                      !checker1.hasMatch(
                                        tables[index].mainBeamSection,
                                      );
                                  final bool secBeamStatusBool =
                                      !checker1.hasMatch(
                                        tables[index].secBeamSection,
                                      );

                                  final int containerOpacity = 175;
                                  final int textOpacity = 255;

                                  if (mainBeamStatusBool) {
                                    mainBeamStatus = Container(
                                      decoration: BoxDecoration(
                                        borderRadius: BorderRadius.circular(
                                          4.0,
                                        ),
                                        color: colorScheme.primary.withAlpha(
                                          containerOpacity,
                                        ),
                                        border: Border.all(
                                          width: 0.5,
                                          color: colorScheme.primary.withAlpha(
                                            containerOpacity,
                                          ),
                                        ),
                                      ),
                                      child: Padding(
                                        padding: const EdgeInsets.all(4.0),
                                        child: Text(
                                          'Main Beam: pass',
                                          style: textTheme.bodySmall!.copyWith(
                                            color: colorScheme.onPrimary
                                                .withAlpha(textOpacity),
                                          ),
                                        ),
                                      ),
                                    );
                                  } else {
                                    mainBeamStatus = Container(
                                      decoration: BoxDecoration(
                                        borderRadius: BorderRadius.circular(
                                          4.0,
                                        ),
                                        color: colorScheme.error.withAlpha(
                                          containerOpacity,
                                        ),
                                        border: Border.all(
                                          width: 0.5,
                                          color: colorScheme.onError.withAlpha(
                                            containerOpacity,
                                          ),
                                        ),
                                      ),
                                      child: Padding(
                                        padding: const EdgeInsets.all(4.0),
                                        child: Text(
                                          'Main Beam: fail',
                                          style: textTheme.bodySmall!.copyWith(
                                            color: colorScheme.onError
                                                .withAlpha(textOpacity),
                                          ),
                                        ),
                                      ),
                                    );
                                  }
                                  if (secBeamStatusBool) {
                                    secBeamStatus = Container(
                                      decoration: BoxDecoration(
                                        borderRadius: BorderRadius.circular(
                                          4.0,
                                        ),
                                        color: colorScheme.primary.withAlpha(
                                          containerOpacity,
                                        ),
                                        border: Border.all(
                                          width: 0.5,
                                          color: colorScheme.primary.withAlpha(
                                            containerOpacity,
                                          ),
                                        ),
                                      ),
                                      child: Padding(
                                        padding: const EdgeInsets.all(4.0),
                                        child: Text(
                                          '2nd Beam: pass',
                                          style: textTheme.bodySmall!.copyWith(
                                            color: colorScheme.onPrimary
                                                .withAlpha(textOpacity),
                                          ),
                                        ),
                                      ),
                                    );
                                  } else {
                                    secBeamStatus = Container(
                                      decoration: BoxDecoration(
                                        borderRadius: BorderRadius.circular(
                                          4.0,
                                        ),
                                        color: colorScheme.error.withAlpha(
                                          containerOpacity,
                                        ),
                                        border: Border.all(
                                          width: 0.5,
                                          color: colorScheme.onError.withAlpha(
                                            containerOpacity,
                                          ),
                                        ),
                                      ),
                                      child: Padding(
                                        padding: const EdgeInsets.all(4.0),
                                        child: Text(
                                          '2nd Beam: fail',
                                          style: textTheme.bodySmall!.copyWith(
                                            color: colorScheme.onError
                                                .withAlpha(textOpacity),
                                          ),
                                        ),
                                      ),
                                    );
                                  }

                                  late List<String> unit;
                                  switch (data.unit) {
                                    case 'metrics':
                                      unit = PreferredUnit.metrics;
                                      break;
                                    case 'imperial':
                                      unit = PreferredUnit.imperial;
                                      break;
                                    default:
                                      unit = PreferredUnit.metrics;
                                  }

                                  final sdl =
                                      tables[index].service +
                                      tables[index].finish /
                                          1000 *
                                          data.finishUnitWeight;

                                  return Padding(
                                    padding: const EdgeInsets.fromLTRB(
                                      8.0,
                                      4.0,
                                      8.0,
                                      4.0,
                                    ),
                                    child: Column(
                                      children: [
                                        Row(
                                          mainAxisSize: MainAxisSize.min,
                                          children: [
                                            Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.center,
                                              children: [
                                                FunctionButton(
                                                  labelText: ' ${index + 1} ',
                                                  pressedColor: colorScheme
                                                      .tertiary
                                                      .withAlpha(150),
                                                ),
                                                SizedBox(height: 5.0),
                                                FunctionButton(
                                                  labelText: 'Show Cals',
                                                  pressedColor: colorScheme
                                                      .tertiary
                                                      .withAlpha(150),
                                                  onTap: (value) async {
                                                    await _presentCalsRecord(
                                                      unit,
                                                      tables,
                                                      index,
                                                    );
                                                  },
                                                ),
                                                SizedBox(height: 5.0),
                                                // SelectionButton(
                                                //   labelIcon: Icon(
                                                //     Icons.check,
                                                //     color:
                                                //         tables[index].isSelected
                                                //             ? colorScheme
                                                //                 .onTertiary
                                                //             : colorScheme
                                                //                 .onSurface
                                                //                 .withAlpha(100),
                                                //   ),
                                                //   labelText: '',
                                                //   // pressedColor:
                                                //   //     colorScheme.tertiary,
                                                //   bgColor:
                                                //       tables[index].isSelected
                                                //           ? colorScheme.tertiary
                                                //           : colorScheme
                                                //               .surfaceContainer
                                                //               .withAlpha(100),

                                                //   onTap: (value) async {
                                                //     final tables = await ref.read(
                                                //       beamSchemeDataControllerProvider
                                                //           .future,
                                                //     );
                                                //     await ref
                                                //         .read(
                                                //           beamSchemeDataControllerProvider
                                                //               .notifier,
                                                //         )
                                                //         .toggleSelectScheme(
                                                //           tables[index]
                                                //               .beamSchemeId,
                                                //         );
                                                //   },
                                                // ),
                                              ],
                                            ),

                                            SizedBox(width: 5.0),
                                            IconButton(
                                              icon: Icon(
                                                Icons.delete,
                                                color: colorScheme.onSurface,
                                              ),
                                              onPressed: () {
                                                ref
                                                    .read(
                                                      steelBeamSchemeDataControllerProvider
                                                          .notifier,
                                                    )
                                                    .deleteTable(
                                                      tables[index]
                                                          .steelBeamSchemeId,
                                                    );
                                              },
                                            ),
                                            Flexible(
                                              child: DefaultTextStyle(
                                                style: bodyStyle,
                                                child: Row(
                                                  children: [
                                                    Column(
                                                      children: [
                                                        SizedBox(height: 3.0),
                                                        mainBeamStatus,
                                                        SizedBox(height: 3.0),
                                                        secBeamStatus,
                                                      ],
                                                    ),
                                                    SizedBox(width: 10.0),
                                                    Column(
                                                      mainAxisSize:
                                                          MainAxisSize.min,
                                                      crossAxisAlignment:
                                                          CrossAxisAlignment
                                                              .start,
                                                      children: [
                                                        Text(
                                                          'Usage :',
                                                          style: subtitleStyle,
                                                        ),
                                                        Text(
                                                          'Finish [${unit[4]}]:',
                                                          style: subtitleStyle,
                                                        ),

                                                        Text(
                                                          'SDL [${unit[1]}]:',
                                                          style: subtitleStyle,
                                                        ),

                                                        Text(
                                                          'LL [${unit[1]}]:',
                                                          style: subtitleStyle,
                                                        ),
                                                        SizedBox(height: 5.0),
                                                        Text(
                                                          'Span [${unit[3]}]:',
                                                          style: subtitleStyle,
                                                        ),
                                                        Text(
                                                          'Bays:',
                                                          style: subtitleStyle,
                                                        ),
                                                        SizedBox(height: 5.0),
                                                        Text(
                                                          'Grade:',
                                                          style: subtitleStyle,
                                                        ),
                                                        Text(
                                                          'Main Beam:',
                                                          style: subtitleStyle,
                                                        ),
                                                        Text(
                                                          'Sec Beam:',
                                                          style: subtitleStyle,
                                                        ),
                                                      ],
                                                    ),
                                                    SizedBox(width: 5.0),
                                                    Column(
                                                      mainAxisSize:
                                                          MainAxisSize.min,
                                                      crossAxisAlignment:
                                                          CrossAxisAlignment
                                                              .start,
                                                      children: [
                                                        Text(
                                                          tables[index].usage,
                                                        ),
                                                        Text(
                                                          NumberFormat(
                                                            '0',
                                                          ).format(
                                                            tables[index]
                                                                .finish,
                                                          ),
                                                        ),

                                                        Text(
                                                          NumberFormat(
                                                            '0.0',
                                                          ).format(sdl),
                                                        ),

                                                        Text(
                                                          NumberFormat(
                                                            '0.0',
                                                          ).format(
                                                            tables[index]
                                                                .liveLoad,
                                                          ),
                                                        ),
                                                        SizedBox(height: 5.0),
                                                        Text(
                                                          '${NumberFormat('0.0').format(tables[index].shortSpan)}(S)x${NumberFormat('0.0').format(tables[index].longSpan)}(L)',
                                                        ),
                                                        Text(
                                                          NumberFormat(
                                                            '0',
                                                          ).format(
                                                            tables[index].bays,
                                                          ),
                                                        ),
                                                        SizedBox(height: 5.0),
                                                        Text(
                                                          'S${NumberFormat('0').format(tables[index].fsy)}',
                                                        ),
                                                        Text(
                                                          tables[index]
                                                              .mainBeamSection,
                                                        ),
                                                        Text(
                                                          tables[index]
                                                              .secBeamSection,
                                                        ),
                                                      ],
                                                    ),
                                                    SizedBox(width: 5.0),

                                                    SizedBox(width: 5.0),
                                                    GestureDetector(
                                                      onTap: () async {
                                                        await showDialog(
                                                          context: context,
                                                          builder: (context) {
                                                            return Dialog(
                                                              backgroundColor:
                                                                  colorScheme
                                                                      .surfaceContainer,
                                                              child: SizedBox(
                                                                width: 550,
                                                                height: 550,
                                                                child: DrawSteelBeamSchemeSketch(
                                                                  sketchWidth:
                                                                      500,
                                                                  sketchHeight:
                                                                      500,
                                                                  index: index,
                                                                  // fontSize: 9.0,
                                                                ),
                                                              ),
                                                            );
                                                          },
                                                        );
                                                      },
                                                      child:
                                                          DrawSteelBeamSchemeSketch(
                                                            sketchWidth: 150,
                                                            sketchHeight: 150,
                                                            index: index,
                                                            fontSize: 9.0,
                                                          ),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                            ),
                                            SizedBox(width: 5.0),
                                          ],
                                        ),
                                        Padding(
                                          padding: const EdgeInsets.fromLTRB(
                                            10,
                                            0,
                                            10,
                                            0,
                                          ),
                                          child: Divider(
                                            thickness: 0.2,
                                            color: colorScheme.primary,
                                          ),
                                        ),
                                      ],
                                    ),
                                  );
                                },
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            );
          },
          error: (error, stackTrace) => Text(error.toString()),
          loading: () => CircularProgressIndicator(),
        );
      },
      error: (error, stackTrace) => Text(error.toString()),
      loading: () => CircularProgressIndicator(),
    );
  }

  Future<void> _presentCalsRecord(
    List<String> unit,
    List<SteelBeamSchemeData> tables,
    int index,
  ) async {
    GlobalData globalData = await ref.read(globalDataControllerProvider.future);

    //* Convert the calslog to map
    Map<String, List<dynamic>> resultSec = {}, resultMain = {};
    _extractCalsLogModified(tables, index, resultSec, resultMain);

    //* handle secondary beam result first
    final mainBeamCals = await _writeBeamResult(
      resultMain, // input the main beam result
      tables,
      index,
      unit,
      presentOption: CalsPresentOptions.main,
    );
    final secBeamCals = await _writeBeamResult(
      resultSec, // input the secondary beam result
      tables,
      index,
      unit,
      presentOption: CalsPresentOptions.secondary,
    );

    if (mounted) {
      await showDialog(
        context: context,
        builder: (context) {
          ColorScheme colorScheme = Theme.of(context).colorScheme;
          TextTheme textTheme = Theme.of(context).textTheme;
          Color bgColor = colorScheme.surfaceContainer;
          Color onBgColor = colorScheme.onSurface;
          Color titleBgColor = colorScheme.primary.withAlpha(150);
          Color titleOnBgColor = colorScheme.onPrimary;
          TextStyle titleTextStyle = textTheme.labelSmall!.copyWith(
            color: titleOnBgColor,
          );
          ScrollController scrollController = ScrollController();

          late List<String> unit;
          switch (globalData.unit) {
            case 'metrics':
              unit = PreferredUnit.metrics;
              break;
            case 'imperial':
              unit = PreferredUnit.imperial;
              break;
            default:
              unit = PreferredUnit.metrics;
          }
          const double maxH = 550;
          const double maxW = 550;

          return Dialog(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(15),
            ),
            backgroundColor: bgColor,
            child: ConstrainedBox(
              constraints: const BoxConstraints(
                maxWidth: maxW,
                maxHeight: maxH,
              ),
              child: Column(
                children: [
                  Align(
                    alignment: Alignment.centerRight,
                    child: Padding(
                      padding: const EdgeInsets.fromLTRB(0, 10, 10, 0),
                      child: FunctionButton(
                        bgColor: titleBgColor,
                        labelTextColor: titleOnBgColor,
                        labelIcon: Icon(
                          Icons.print_outlined,
                          color: titleOnBgColor.withAlpha(175),
                        ),
                        labelText: '',
                        onTap: (value) {
                          exportListToPdf(context, ref, [
                            ...mainBeamCals.toString().split('\n'),
                            ...secBeamCals.toString().split('\n'),
                          ]);
                        },
                      ),
                    ),
                  ),

                  Scrollbar(
                    controller: scrollController,
                    thumbVisibility: true,
                    trackVisibility: true,
                    child: ConstrainedBox(
                      constraints: const BoxConstraints(
                        maxWidth: maxW,
                        maxHeight: maxH - 70,
                      ),
                      child: SingleChildScrollView(
                        controller: scrollController,
                        child: Padding(
                          padding: const EdgeInsets.fromLTRB(25.0, 5, 25.0, 5),
                          child: DefaultTextStyle(
                            style: textTheme.labelMedium!.copyWith(
                              color: onBgColor,
                            ),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                Container(
                                  decoration: BoxDecoration(
                                    color: colorScheme.tertiary,
                                    shape: BoxShape.circle,
                                    border: Border.all(color: titleOnBgColor),
                                  ),
                                  child: Padding(
                                    padding: const EdgeInsets.all(12.0),
                                    child: Text(
                                      '${index + 1}',
                                      style: titleTextStyle.copyWith(
                                        fontSize: 24,
                                        color: colorScheme.onTertiary,
                                      ),
                                    ),
                                  ),
                                ),
                                SizedBox(width: 10),
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Container(
                                      decoration: BoxDecoration(
                                        color: titleBgColor,
                                        shape: BoxShape.rectangle,
                                        borderRadius: BorderRadius.circular(
                                          10.0,
                                        ),
                                        border: Border.all(
                                          color: titleOnBgColor,
                                        ),
                                      ),
                                      child: Padding(
                                        padding: const EdgeInsets.all(4.0),
                                        child: Text(
                                          'Usage:\n${tables[index].usage}',
                                          style: titleTextStyle,
                                        ),
                                      ),
                                    ),
                                    SizedBox(width: 5),
                                    Container(
                                      decoration: BoxDecoration(
                                        color: titleBgColor,
                                        shape: BoxShape.rectangle,
                                        borderRadius: BorderRadius.circular(
                                          10.0,
                                        ),
                                        border: Border.all(
                                          color: titleOnBgColor,
                                        ),
                                      ),
                                      child: Padding(
                                        padding: const EdgeInsets.all(4.0),
                                        child: Text(
                                          'Finish [${unit[4]}]:\n${tables[index].finish} ',
                                          style: titleTextStyle,
                                        ),
                                      ),
                                    ),
                                    SizedBox(width: 5),
                                    Container(
                                      decoration: BoxDecoration(
                                        color: titleBgColor,
                                        shape: BoxShape.rectangle,
                                        borderRadius: BorderRadius.circular(
                                          10.0,
                                        ),
                                        border: Border.all(
                                          color: titleOnBgColor,
                                        ),
                                      ),
                                      child: Padding(
                                        padding: const EdgeInsets.all(4.0),
                                        child: Text(
                                          'Service [${unit[1]}]:\n${tables[index].service} ',
                                          style: titleTextStyle,
                                        ),
                                      ),
                                    ),
                                    SizedBox(width: 5),
                                    Container(
                                      decoration: BoxDecoration(
                                        color: titleBgColor,
                                        shape: BoxShape.rectangle,
                                        borderRadius: BorderRadius.circular(
                                          10.0,
                                        ),
                                        border: Border.all(
                                          color: titleOnBgColor,
                                        ),
                                      ),
                                      child: Padding(
                                        padding: const EdgeInsets.all(4.0),
                                        child: Text(
                                          'Live Load [${unit[1]}]:\n${tables[index].liveLoad}',
                                          style: titleTextStyle,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                                SizedBox(height: 15),
                                Container(
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(10.0),
                                    border: Border.all(color: onBgColor),
                                  ),
                                  child: Padding(
                                    padding: const EdgeInsets.all(8.0),
                                    child: Text(secBeamCals.toString()),
                                  ),
                                ),
                                SizedBox(height: 10),
                                Container(
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(10.0),
                                    border: Border.all(color: onBgColor),
                                  ),
                                  child: Padding(
                                    padding: const EdgeInsets.all(8.0),
                                    child: Text(mainBeamCals.toString()),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      );
    }
  }

  Future<StringBuffer> _writeBeamResult(
    Map<String, List<dynamic>> result,
    List<SteelBeamSchemeData> tables,
    int index,
    List<String> unit, {
    String presentOption = CalsPresentOptions.secondary,
  }) async {
    StringBuffer buffer = StringBuffer();
    final GlobalData globalData = await ref.read(
      globalDataControllerProvider.future,
    );
    final SteelBeamSchemeInput input = await ref.read(
      steelBeamSchemeInputControllerProvider.future,
    );

    double tempDouble = 0.0, tempDouble2 = 0.0, tempDouble3 = 0.0;
    double Es = 205000; // [MPa] Elastic modulus of steel

    //* Get the Section
    final List<Map<String, dynamic>> steelSections =
        await getAllSteelISection();
    final Map<String, dynamic> secSection, mainSection;

    //* Formatter
    final NumberFormat f0 = NumberFormat('0'), f1 = NumberFormat('0.0');

    //* Write Warning if necessary
    RegExp failReg = RegExp(r'fail', caseSensitive: false);
    if (!failReg.hasMatch(tables[index].secBeamSection)) {
      secSection = steelSections.firstWhere(
        (element) => element['name'] == tables[index].secBeamSection,
      );
    } else {
      secSection = steelSections.last;
    }

    if (!failReg.hasMatch(tables[index].mainBeamSection)) {
      mainSection = steelSections.firstWhere(
        (element) => element['name'] == tables[index].mainBeamSection,
      );
    } else {
      mainSection = steelSections.last;
    }

    //* Beam Size
    buffer.write('------------------------------\n');
    if (presentOption == CalsPresentOptions.secondary) {
      buffer.write('Secondary Beam (Along long span (L))\n');
      buffer.write('Grade: S${f0.format(tables[index].fsy)}\n');
      buffer.write('Section: ${secSection['name']}\n');
    } else {
      buffer.write('Main Beam (Along short span (S))\n');
      buffer.write('Grade: S${f0.format(tables[index].fsy)}\n');
      buffer.write('Section: ${mainSection['name']}\n');
    }
    buffer.write(
      'Grids: ${getDoubleValue(result, 'Short Span')}(S)x${getDoubleValue(result, 'Long Span')}(L) [${unit[3]}]\n',
    );
    buffer.write('------------------------------\n');
    buffer.write('*******************\n');
    buffer.write('Loading\n');
    buffer.write('*******************\n');
    buffer.write(
      'Compsoite Action Factor, f = ${f1.format(input.compositeActionFactor)}\n',
    );
    //* Secondary Beam Case: Loading
    if (presentOption == CalsPresentOptions.secondary) {
      //* Span
      buffer.write('Span,L = ');
      tempDouble = getDoubleValue(result, 'Long Span');
      buffer.write('${f1.format(tempDouble)} [${unit[3]}]\n');

      //*Live load (for defelction)
      tempDouble =
          getDoubleValue(result, 'LL') *
          getDoubleValue(result, 'Secondary Beam Load Width');
      buffer.write(
        'Live Load UDL, wL = ${f1.format(tempDouble)} [${unit[0]}/${unit[3]}]\n',
      );

      buffer.write('SW\n= slab + beam\n= ');
      buffer.write(
        '${globalData.rcUnitWeight}x${getDoubleValue(result, 'Slab Thickness') / 1000}x${getDoubleValue(result, 'Secondary Beam Load Width')}',
      );
      buffer.write(
        '+${globalData.steelUnitWeight}x${double.tryParse(secSection['A'])}/10000',
      );

      tempDouble =
          globalData.rcUnitWeight *
              getDoubleValue(result, 'Slab Thickness') /
              1000 *
              getDoubleValue(result, 'Secondary Beam Load Width') +
          globalData.steelUnitWeight *
              (double.tryParse(secSection['A']) ?? 0) /
              10000;
      buffer.write('\n= ${f1.format(tempDouble)} [${unit[0]}/${unit[3]}]\n');

      // * Loading: SDL, LL, ULS
      buffer.write('SDL\n=');
      buffer.write(
        '${getDoubleValue(result, 'SDL')}x${getDoubleValue(result, 'Secondary Beam Load Width')}\n',
      );
      tempDouble =
          getDoubleValue(result, 'SDL') *
          getDoubleValue(result, 'Secondary Beam Load Width');
      buffer.write('= ${f1.format(tempDouble)} [${unit[0]}/${unit[3]}]\n');

      buffer.write('LL\n=');
      buffer.write(
        '${getDoubleValue(result, 'LL')}x${getDoubleValue(result, 'Secondary Beam Load Width')}\n',
      );
      tempDouble =
          getDoubleValue(result, 'LL') *
          getDoubleValue(result, 'Secondary Beam Load Width');
      buffer.write('= ${f1.format(tempDouble)} [${unit[0]}/${unit[3]}]\n');

      buffer.write('ULS = ');
      tempDouble =
          getDoubleValue(result, 'ULS') *
              getDoubleValue(result, 'Secondary Beam Load Width') +
          globalData.sdlFactor *
              (globalData.rcUnitWeight *
                      getDoubleValue(result, 'Slab Thickness') /
                      1000 *
                      getDoubleValue(result, 'Secondary Beam Load Width') +
                  globalData.steelUnitWeight *
                      (double.tryParse(secSection['A']) ?? 0) /
                      10000);
      buffer.write('${f1.format(tempDouble)} [${unit[0]}/${unit[3]}]\n');

      //* Demand Moment and I (for live load deflection)
      tempDouble =
          getDoubleValue(result, 'LL') *
          getDoubleValue(result, 'Secondary Beam Load Width');

      buffer.write('M\n= w*L^2/8\n= ');
      tempDouble = getDoubleValue(result, 'M');
      buffer.write('${f0.format(tempDouble)} [${unit[2]}]\n');

      buffer.write('V\n= w*L/2\n= ');
      tempDouble = getDoubleValue(result, 'V');
      buffer.write('${f0.format(tempDouble)} [${unit[0]}]\n');

      buffer.write('I_req\n= 1250*wL*L^3/(384*Es*f)\n');
      tempDouble = getDoubleValue(result, 'I_req');
      buffer.write('= ${f0.format(tempDouble)} [${unit[8]}]\n');
    }
    //* Main Beam Case: Loading
    else {
      //* Span
      buffer.write('Span,L = ');
      tempDouble = getDoubleValue(result, 'Short Span');
      buffer.write('${f1.format(tempDouble)} [${unit[3]}]\n');

      //* Bays
      buffer.write('Bays, n = ${getValueAsInt(result, 'Bays')}\n');

      //* Live point load (for deflection)
      tempDouble =
          getDoubleValue(result, 'LL') *
          getDoubleValue(result, 'Secondary Beam Load Width') *
          getDoubleValue(result, 'Long Span');
      buffer.write('Live Point Load, PLL \n= ');
      buffer.write(
        '${getDoubleValue(result, 'LL')}x${getDoubleValue(result, 'Secondary Beam Load Width')}x${getDoubleValue(result, 'Long Span')}\n',
      );
      buffer.write('= ${f0.format(tempDouble)} [${unit[0]}]\n');
      //* Loading: BeamSelf Weight
      buffer.write('SW\n= beam SW\n= ');
      buffer.write(
        '${globalData.steelUnitWeight}x${double.tryParse(mainSection['A'])} / 10000\n',
      );
      tempDouble =
          globalData.steelUnitWeight *
          (double.tryParse(mainSection['A']) ?? 0) /
          10000;
      buffer.write('= ${f1.format(tempDouble)} [${unit[0]}/${unit[3]}]\n');

      //* Loading: ULS and P (Point Load)
      buffer.write('ULS = ');
      tempDouble =
          globalData.sdlFactor *
          (globalData.steelUnitWeight *
              (double.tryParse(mainSection['A']) ?? 0) /
              10000);
      buffer.write('${f1.format(tempDouble)} [${unit[0]}/${unit[3]}]\n');
      tempDouble3 = tempDouble; // beam factored SW UDL

      buffer.write('P = ');
      tempDouble =
          (globalData.sdlFactor *
                  (globalData.rcUnitWeight *
                          getDoubleValue(result, 'Slab Thickness') /
                          1000 *
                          getDoubleValue(result, 'Secondary Beam Load Width') +
                      globalData.steelUnitWeight *
                          (double.tryParse(secSection['A']) ?? 0) /
                          10000) +
              getDoubleValue(result, 'ULS') *
                  getDoubleValue(result, 'Secondary Beam Load Width')) *
          getDoubleValue(result, 'Long Span');

      tempDouble2 =
          tempDouble; // ULS point load on the main beam (from 2nd Beam)
      buffer.write('${f0.format(tempDouble)} [${unit[0]}]');
      buffer.write(' (From 2nd Beam shear)\n');

      //* Demand Moment and I (for live load deflection)
      //* Case 1: n (bays) is odd number
      if (getValueAsInt(result, 'Bays') % 2 != 0) {
        buffer.write('M\n= w*L^2/8 + (n^2-1)*P*L/(8*n)\n= ');
        buffer.write(
          '${f0.format(getDoubleValue(result, 'M'))} [${unit[2]}]\n',
        );
        buffer.write('I_req\n= 250*(5*n^4-4*n^2-1)*PLL*L^2/(384*n^3*Es*f)\n= ');
        buffer.write(
          '${f0.format(getDoubleValue(result, 'I_req'))} [${unit[4]}]\n',
        );
        //* Case 2: n (bays) is even number
      } else {
        buffer.write('M\n= w*L^2/8 + n*P*L/8\n= ');
        buffer.write(
          '${f0.format(getDoubleValue(result, 'M'))} [${unit[2]}]\n',
        );

        buffer.write('I_req\n= 250*(5*n^2-4)*PLL*L^2/(384*n*Es*f)\n= ');
        buffer.write(
          '${f0.format(getDoubleValue(result, 'I_req'))} [${unit[8]}]\n',
        );
      }
    }
    buffer.write('*******************\n');
    buffer.write('Check moement\n');
    buffer.write('*******************\n');

    buffer.write('M = ');
    tempDouble = getDoubleValue(result, 'M');
    buffer.write('${f0.format(tempDouble)} [${unit[2]}]\n');
    buffer.write('Mc = ');
    tempDouble2 = getDoubleValue(result, 'Mc');
    buffer.write('${f0.format(tempDouble2)} [${unit[2]}]\n');
    if (tempDouble2 > tempDouble) {
      buffer.write('Mc > M (OK)\n');
    } else {
      buffer.write('Mc < M (Fail)\n');
    }

    buffer.write('*******************\n');
    buffer.write('Check I_req (Live Load Deflection)\n');
    buffer.write('*******************\n');
    buffer.write('I_req = ');
    tempDouble = getDoubleValue(result, 'I_req');
    buffer.write('${f0.format(tempDouble)} [${unit[4]}]\n');
    buffer.write('I = ');
    tempDouble2 = getDoubleValue(result, 'I_pro');
    buffer.write('${f0.format(tempDouble2)} [${unit[4]}]\n');
    if (tempDouble2 > tempDouble) {
      buffer.write('I > I_req (OK)\n');
    } else {
      buffer.write('I < I_req (Fail)\n');
    }

    //* log any warning
    List<String> errors = [], warnings = [];

    if ((failReg.hasMatch(tables[index].secBeamSection) &&
            (presentOption == CalsPresentOptions.secondary)) ||
        (failReg.hasMatch(tables[index].mainBeamSection) &&
            (presentOption == CalsPresentOptions.main))) {
      errors.add('Result not reliable. Something fails.');
      warnings.add('1. Consider to adjust the grid');
      warnings.add('2. Consider to use larger composite factor');
    }

    if (failReg.hasMatch(tables[index].secBeamSection) &&
        !failReg.hasMatch(tables[index].mainBeamSection) &&
        presentOption == CalsPresentOptions.main) {
      warnings.add(
        'Secondary beam fails, so main beam design is not reliable.',
      );
    }

    // add all erros and warnings in one go
    if (warnings.isNotEmpty) {
      buffer = addWarningHeader(buffer, warnings);
    }
    if (errors.isNotEmpty) {
      buffer = addErrorHeader(buffer, errors);
    }
    return buffer;
  }

  void _updateHeights() {
    final double screenHeight = MediaQuery.of(context).size.height;
    _maxHeight =
        (widget.maxHeight == null)
            ? screenHeight * 0.6
            : (widget.maxHeight! > screenHeight * 0.6)
            ? screenHeight * 0.6
            : widget.maxHeight!;

    _minHeight =
        (widget.minHeight == null)
            ? screenHeight * 0.1
            : (widget.minHeight! > screenHeight * 0.1)
            ? screenHeight * 0.1
            : widget.minHeight!;

    // Adjust _maxHeight if needed

    if (_maxHeight < _minHeight) {
      _maxHeight = _minHeight;
    }
  }

  void _extractCalsLogModified(
    List<SteelBeamSchemeData> tables,
    int index,
    Map<String, List> resultSec,
    Map<String, List> resultMain,
  ) {
    List<String> recordForSec = <String>[], recordForMain = <String>[];
    bool stopExtractSec = false;

    final record = tables[index].calsLog;
    List<String> lines = record.split('\n');

    RegExp checker = RegExp(r'Design For: Main Beam');
    for (String line in lines) {
      if (checker.hasMatch(line)) {
        stopExtractSec = true;
      }

      if (!stopExtractSec) {
        recordForSec.add(line);
      } else {
        recordForMain.add(line);
      }
    }
    resultSec.addAll(extractCalsLog(recordForSec.join('\n')));
    resultMain.addAll(extractCalsLog(recordForMain.join('\n')));
  }
}
