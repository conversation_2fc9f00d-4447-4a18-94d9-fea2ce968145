import 'package:flutter/material.dart';

class CustomPopup extends StatelessWidget {
  const CustomPopup({
    super.key,
    this.widgetList,
    this.popupWidth = 550,
    this.popupHeight = 550,
    this.backgroundColor,
    this.iconColor,
    this.iconHOverColor,
    this.icon,
  });

  final List<Widget>? widgetList;
  final double popupWidth;
  final double popupHeight;
  final Color? backgroundColor;
  final Color? iconColor;
  final Color? iconHOverColor;
  final Icon? icon;

  @override
  Widget build(BuildContext context) {
    //* Initialization
    final colorScheme = Theme.of(context).colorScheme;
    final textStyle = Theme.of(context).textTheme;
    final _backgroundColor =
        this.backgroundColor ?? colorScheme.surfaceContainer;
    final _iconColor = this.iconColor ?? colorScheme.onSurface;
    final _iconHOverColor =
        this.iconHOverColor ?? colorScheme.primaryContainer.withAlpha(150);
    final _icon = this.icon ?? Icon(Icons.info_outline);

    //* Build things now
    return IconButton(
      color: _iconColor,
      hoverColor: _iconHOverColor,
      onPressed: () async {
        await showDialog(
          context: context,
          builder: (context) {
            return Dialog(
              backgroundColor: _backgroundColor,
              child: SizedBox(
                width: popupWidth,
                height: popupHeight,
                child: SingleChildScrollView(
                  child: Padding(
                    padding: const EdgeInsets.all(15.0),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: widgetList ?? [Placeholder()],
                    ),
                  ),
                ),
              ),
            );
          },
        );
      },
      icon: _icon,
    );
  }
}
