// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'beam_scheme_input.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_BeamSchemeInput _$BeamSchemeInputFromJson(
  Map<String, dynamic> json,
) => _BeamSchemeInput(
  id: json['id'] as String? ?? '1',
  shortSpan: (json['shortSpan'] as num?)?.toDouble() ?? 5.0,
  longSpan: (json['longSpan'] as num?)?.toDouble() ?? 12.0,
  bays: (json['bays'] as num?)?.toInt() ?? 2,
  mainStrZone: (json['mainStrZone'] as num?)?.toDouble() ?? 500.0,
  secStrZone: (json['secStrZone'] as num?)?.toDouble() ?? 500.0,
  fcu: (json['fcu'] as num?)?.toDouble() ?? 45.0,
  cover: (json['cover'] as num?)?.toDouble() ?? 40.0,
  mainKValue: (json['mainKValue'] as num?)?.toDouble() ?? 0.156,
  mainSteelRatio: (json['mainSteelRatio'] as num?)?.toDouble() ?? 0.025,
  secKValue: (json['secKValue'] as num?)?.toDouble() ?? 0.156,
  secSteelRatio: (json['secSteelRatio'] as num?)?.toDouble() ?? 0.040,
  minS: (json['minS'] as num?)?.toInt() ?? 100,
  maxS: (json['maxS'] as num?)?.toInt() ?? 300,
  maxWidth: (json['maxWidth'] as num?)?.toDouble() ?? 1000.0,
  maxLayers: (json['maxLayers'] as num?)?.toInt() ?? 2,
  shortSpanIncreament: (json['shortSpanIncreament'] as num?)?.toDouble() ?? 0.0,
  longSpanIncreament: (json['longSpanIncreament'] as num?)?.toDouble() ?? 0.0,
  baysIncreament: (json['baysIncreament'] as num?)?.toInt() ?? 1,
  iterationSteps: (json['iterationSteps'] as num?)?.toInt() ?? 4,
  usage: json['usage'] as String? ?? '',
  slabThickness: (json['slabThickness'] as num?)?.toDouble() ?? 150.0,
  useSlabSelected: json['useSlabSelected'] as bool? ?? false,
);

Map<String, dynamic> _$BeamSchemeInputToJson(_BeamSchemeInput instance) =>
    <String, dynamic>{
      'id': instance.id,
      'shortSpan': instance.shortSpan,
      'longSpan': instance.longSpan,
      'bays': instance.bays,
      'mainStrZone': instance.mainStrZone,
      'secStrZone': instance.secStrZone,
      'fcu': instance.fcu,
      'cover': instance.cover,
      'mainKValue': instance.mainKValue,
      'mainSteelRatio': instance.mainSteelRatio,
      'secKValue': instance.secKValue,
      'secSteelRatio': instance.secSteelRatio,
      'minS': instance.minS,
      'maxS': instance.maxS,
      'maxWidth': instance.maxWidth,
      'maxLayers': instance.maxLayers,
      'shortSpanIncreament': instance.shortSpanIncreament,
      'longSpanIncreament': instance.longSpanIncreament,
      'baysIncreament': instance.baysIncreament,
      'iterationSteps': instance.iterationSteps,
      'usage': instance.usage,
      'slabThickness': instance.slabThickness,
      'useSlabSelected': instance.useSlabSelected,
    };
