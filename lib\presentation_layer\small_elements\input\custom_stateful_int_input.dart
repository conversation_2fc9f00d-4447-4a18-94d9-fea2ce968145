import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter/services.dart';

//presentation layer
// import '../screen/homescreen.dart';

class CustomStatefulIntInput extends ConsumerStatefulWidget {
  CustomStatefulIntInput({
    this.title = 'Int Input',
    this.value = 0,
    this.sizeScale = 1.0,
    this.onChanged,
    this.listener,
    this.helperText,
    this.tooltipText,
    this.titleStyle,
    super.key,
  });

  final String? title;
  int? value;
  double? sizeScale;
  void Function(int)? onChanged;
  void Function(bool, int)? listener;
  String? helperText;
  String? tooltipText;

  TextStyle? titleStyle;

  @override
  ConsumerState<ConsumerStatefulWidget> createState() =>
      _CustomStatefulIntInputState();
}

class _CustomStatefulIntInputState
    extends ConsumerState<CustomStatefulIntInput> {
  //corresponding to widget part
  late String _title;
  late int _value;
  late double _sizeScale;
  late void Function(int)? _onChanged;
  late void Function(bool, int)? _listener;
  late FocusNode _focusNode;
  late TextEditingController _controller;
  late String? _errorMsg;
  late String? _helperText;
  late String? _tooltipText;

  late TextStyle? _titleStyle;
  @override
  void dispose() {
    _controller.dispose();
    _focusNode.removeListener(() {});
    _focusNode.dispose();
    super.dispose();
  }

  @override
  void initState() {
    _title = widget.title ?? '';
    _value = widget.value ?? 0;
    _sizeScale = widget.sizeScale ?? 1.0;
    _onChanged = widget.onChanged;
    _listener = widget.listener;

    _controller = TextEditingController(text: widget.value.toString());
    _errorMsg = null;
    _helperText = widget.helperText;
    _tooltipText = widget.tooltipText;

    _titleStyle = widget.titleStyle;

    _focusNode = FocusNode();
    _focusNode.addListener(() {
      final hasFocuse = _focusNode.hasFocus;
      try {
        _value = int.parse(_controller.text);
        widget.value = _value;
        if (_listener != null) {
          _listener!(hasFocuse, _value);
          // print('Listening: $hasFocuse | $_value');
        }
      } catch (e) {
        _errorMsg = null;
      }
    });
    super.initState();
  }

  @override
  void didUpdateWidget(covariant CustomStatefulIntInput oldWidget) {
    super.didUpdateWidget(oldWidget);
    //Refresh the state from widget
    if (widget.value != oldWidget.value) {
      _title = widget.title ?? _title;
      final newText = widget.value?.toString() ?? '';
      // Avoid updating controller if text is already correct
      if (int.tryParse(_controller.text) != int.tryParse(newText)) {
        _controller.text = newText;
        // Preserve cursor position when possible
        final newOffset = _controller.text.length;
        _controller.selection = TextSelection.collapsed(offset: newOffset);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final colorScheme = Theme.of(context).colorScheme;
    return Transform.scale(
      scale: widget.sizeScale,
      child: TextField(
        controller: _controller,
        focusNode: _focusNode,
        style: textTheme.bodyMedium,
        decoration: InputDecoration(
          helperText: _helperText,
          suffix:
              _tooltipText != null
                  ? Tooltip(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10.0),
                      color: colorScheme.tertiaryContainer.withAlpha(225),
                    ),
                    textStyle: textTheme.labelLarge!.copyWith(
                      color: colorScheme.onTertiaryContainer.withAlpha(225),
                    ),
                    message: _tooltipText,
                    child: Icon(
                      Icons.info_outline,
                      size: textTheme.titleMedium?.fontSize,
                      color: colorScheme.onPrimaryContainer,
                    ),
                  )
                  : null,
          border: OutlineInputBorder(),
          label: Text(_title),
          labelStyle: _titleStyle ?? textTheme.labelLarge,
          errorText: _errorMsg,
          isDense: true,
        ),
        keyboardType: TextInputType.number,
        inputFormatters: <TextInputFormatter>[
          FilteringTextInputFormatter.allow(RegExp(r'[0-9]')),
        ],
        onChanged: (value) {
          try {
            _value = int.parse(value);
            widget.value = _value; //update the widgetvalue
            if (_onChanged != null) {
              _onChanged!(_value);
            }
            _errorMsg = null;
          } catch (e) {
            _errorMsg = "Invalid Number";
          }
          setState(() {});
        },
      ),
    );
  }
}
