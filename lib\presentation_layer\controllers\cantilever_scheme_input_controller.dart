import 'package:nanoid2/nanoid2.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

//presentation layer
import '../../domain_layer/cantilever_scheme_input.dart';
import '../../domain_layer/column_scheme_data.dart';
import '../../domain_layer/transfer_beam_scheme_input.dart';
import '../screen/homescreen.dart';

// domain layer

part 'cantilever_scheme_input_controller.g.dart';

@riverpod
class CantileverSchemeInputController
    extends _$CantileverSchemeInputController {
  @override
  FutureOr<List<CantileverSchemeInput>> build() async {
    // print('Build: Transfer Beam Scheme Input');
    final List<CantileverSchemeInput> cantileverSchemeInputs =
        await ref
            .read(appDatabaseControllerProvider.notifier)
            .queryCantileverSchemeInput();
    final data1 = ref.watch(columnSchemeDataControllerProvider);
    return data1.when(
      data: (colData) async {
        final List<CantileverSchemeInput> finalList = [];
        //* Validate the load (if using column load)
        await Future.forEach(cantileverSchemeInputs, (input) async {
          if (input.loadFromSelectedCol) {
            final selectedCol = colData.firstWhere(
              (scheme) => scheme.isSelected,
              orElse: () => ColumnSchemeData(),
            );
            input = input.copyWith(pointLoad: selectedCol.ulsLoad);
          }
          finalList.add(input);
        });
        return finalList;
      },
      error: (error, stackTrace) => [],
      loading: () => [],
    );
  }

  Future<void> addEmptytable() async {
    final x = await future;
    final id = await _generateTaskID();
    state = AsyncData([
      ...x,
      CantileverSchemeInput(cantileverSchemeInputId: id),
    ]);
  }

  Future<void> deleteTable(String id) async {
    // print("Before deletion: ${state.map((item) => item.usage).toList()}");
    final x = await future;
    x.removeWhere((item) => item.cantileverSchemeInputId == id);
    // print("After deletion: ${x.map((item) => item.usage).toList()}");
    state = AsyncData(x);
  }

  Future<void> deleteAllTable() async {
    // print("Before deletion: ${state.map((item) => item.usage).toList()}");
    state = AsyncData(<CantileverSchemeInput>[]);
  }

  Future<void> insertEmptyTable(int index) async {
    final x = await future;
    final id = await _generateTaskID();
    x.insert(index + 1, CantileverSchemeInput(cantileverSchemeInputId: id));
    state = AsyncData(x);
  }

  Future<void> copyAndInsertTable(int index) async {
    final x = await future;
    final id = await _generateTaskID();
    x.insert(index + 1, x[index].copyWith(cantileverSchemeInputId: id));
    state = AsyncData(x);
  }

  Future<String> _generateTaskID() async {
    String newID = nanoid(alphabet: Alphabet.noDoppelgangerSafe);
    final x = await future;
    final Set<String> transferBeamSchemeInputIds =
        x.map((item) => item.cantileverSchemeInputId).toSet();
    // final Set<String> taskIDs = ref.read(_taskIDsProvider);
    while (transferBeamSchemeInputIds.contains(newID)) {
      newID = nanoid(alphabet: Alphabet.noDoppelgangerSafe);
    }
    return newID;
  }

  Future<void> updateTable({
    double? pointLoad,
    double? distA,
    bool? loadFromSelectedCol,
    required String cantileverSchemeInputId,
  }) async {
    final x = await future;
    List<CantileverSchemeInput> finalList = [];
    late CantileverSchemeInput data;
    // Create a list of updates
    await Future.forEach((x), (x1) {
      if (x1.cantileverSchemeInputId == cantileverSchemeInputId) {
        data = x1.copyWith(
          pointLoad: pointLoad ?? x1.pointLoad,
          distA: distA ?? x1.distA,
          loadFromSelectedCol: loadFromSelectedCol ?? x1.loadFromSelectedCol,
          cantileverSchemeInputId: cantileverSchemeInputId,
        );
      } else {
        data = x1;
      }
      finalList.add(data);
    });
    // Update the state only if there are changes
    state = AsyncData(finalList);
  }
}
