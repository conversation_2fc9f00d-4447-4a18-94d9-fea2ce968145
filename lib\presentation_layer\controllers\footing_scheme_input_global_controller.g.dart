// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'footing_scheme_input_global_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$footingSchemeInputGlobalControllerHash() =>
    r'ff061784471cffdd2d728e111fa7da2cc15cb68a';

/// See also [FootingSchemeInputGlobalController].
@ProviderFor(FootingSchemeInputGlobalController)
final footingSchemeInputGlobalControllerProvider =
    AutoDisposeAsyncNotifierProvider<
      FootingSchemeInputGlobalController,
      FootingSchemeInputGlobal
    >.internal(
      FootingSchemeInputGlobalController.new,
      name: r'footingSchemeInputGlobalControllerProvider',
      debugGetCreateSourceHash:
          const bool.fromEnvironment('dart.vm.product')
              ? null
              : _$footingSchemeInputGlobalControllerHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$FootingSchemeInputGlobalController =
    AutoDisposeAsyncNotifier<FootingSchemeInputGlobal>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
