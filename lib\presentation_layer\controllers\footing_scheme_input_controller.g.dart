// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'footing_scheme_input_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$footingSchemeInputControllerHash() =>
    r'07ac4d578ab4099d3369bad3516f9f80f324237d';

/// See also [FootingSchemeInputController].
@ProviderFor(FootingSchemeInputController)
final footingSchemeInputControllerProvider = AutoDisposeAsyncNotifierProvider<
  FootingSchemeInputController,
  FootingSchemeInput
>.internal(
  FootingSchemeInputController.new,
  name: r'footingSchemeInputControllerProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$footingSchemeInputControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$FootingSchemeInputController =
    AutoDisposeAsyncNotifier<FootingSchemeInput>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
