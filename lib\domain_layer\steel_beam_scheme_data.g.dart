// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'steel_beam_scheme_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_SteelBeamSchemeData _$SteelBeamSchemeDataFromJson(Map<String, dynamic> json) =>
    _SteelBeamSchemeData(
      usage: json['usage'] as String? ?? '',
      finish: (json['finish'] as num?)?.toDouble() ?? 0.0,
      service: (json['service'] as num?)?.toDouble() ?? 0.0,
      liveLoad: (json['liveLoad'] as num?)?.toDouble() ?? 0.0,
      loadingTableId: json['loadingTableId'] as String? ?? '',
      slabThickness: (json['slabThickness'] as num?)?.toDouble() ?? 0.0,
      compositeActionFactor:
          (json['compositeActionFactor'] as num?)?.toDouble() ?? 0.0,
      shortSpan: (json['shortSpan'] as num?)?.toDouble() ?? 5.0,
      longSpan: (json['longSpan'] as num?)?.toDouble() ?? 12.0,
      bays: (json['bays'] as num?)?.toInt() ?? 2,
      strZone: (json['strZone'] as num?)?.toDouble() ?? 500.0,
      fsy: (json['fsy'] as num?)?.toDouble() ?? 355.0,
      mainBeamSection: json['mainBeamSection'] as String? ?? '',
      secBeamSection: json['secBeamSection'] as String? ?? '',
      steelBeamSchemeId: json['steelBeamSchemeId'] as String? ?? '',
      calsLog: json['calsLog'] as String? ?? '',
    );

Map<String, dynamic> _$SteelBeamSchemeDataToJson(
  _SteelBeamSchemeData instance,
) => <String, dynamic>{
  'usage': instance.usage,
  'finish': instance.finish,
  'service': instance.service,
  'liveLoad': instance.liveLoad,
  'loadingTableId': instance.loadingTableId,
  'slabThickness': instance.slabThickness,
  'compositeActionFactor': instance.compositeActionFactor,
  'shortSpan': instance.shortSpan,
  'longSpan': instance.longSpan,
  'bays': instance.bays,
  'strZone': instance.strZone,
  'fsy': instance.fsy,
  'mainBeamSection': instance.mainBeamSection,
  'secBeamSection': instance.secBeamSection,
  'steelBeamSchemeId': instance.steelBeamSchemeId,
  'calsLog': instance.calsLog,
};
