import 'package:freezed_annotation/freezed_annotation.dart';
// import 'package:flutter/foundation.dart';
part 'basement_wall_scheme_data.freezed.dart';
part 'basement_wall_scheme_data.g.dart';

@freezed
abstract class BasementWallSchemeData with _$BasementWallSchemeData {
  const BasementWallSchemeData._();
  factory BasementWallSchemeData({
    @Default(0.0) double strZone,
    @Default(45.0) double fcu,
    @Default(75.0) double cover,
    @Default('') String mainTopBar,
    @Default('') String mainBottomBar,
    @Default('') String mainLinks,
    @Default(0.0) double wallTopLevel,
    @Default(-3.0) double wallBottomLevel,
    @Default(0.0) double soilTopLevel,
    @Default(0.0) double waterTopLevel, 
    @Default('1') String basementWallSchemeId,
    @Default('') String calsLog,
    @Default('') String wallForceULS, // format is slab force / beam force 
    @Default('') String wallForceSLS, // format is slab force / beam force 

  }) = _BasementWallSchemeData; 

  factory BasementWallSchemeData.fromJson(Map<String, Object?> json) =>
      _$BasementWallSchemeDataFromJson(json);
}
