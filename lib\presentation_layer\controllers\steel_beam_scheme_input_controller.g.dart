// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'steel_beam_scheme_input_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$steelBeamSchemeInputControllerHash() =>
    r'b15facb9cb550a68044c4dfaab8ec9b3205d5a12';

/// See also [SteelBeamSchemeInputController].
@ProviderFor(SteelBeamSchemeInputController)
final steelBeamSchemeInputControllerProvider = AutoDisposeAsyncNotifierProvider<
  SteelBeamSchemeInputController,
  SteelBeamSchemeInput
>.internal(
  SteelBeamSchemeInputController.new,
  name: r'steelBeamSchemeInputControllerProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$steelBeamSchemeInputControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$SteelBeamSchemeInputController =
    AutoDisposeAsyncNotifier<SteelBeamSchemeInput>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
