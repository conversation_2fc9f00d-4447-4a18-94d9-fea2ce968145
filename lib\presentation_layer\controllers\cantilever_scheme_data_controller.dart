import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'dart:math';
import 'package:intl/intl.dart';
import 'package:structify/domain_layer/data_struct/str_force_struct.dart';
import 'package:structify/misc/custom_func.dart';
import 'dart:convert';

//presentation layer
import '../../domain_layer/cantilever_scheme_data.dart';
import '../../domain_layer/cantilever_scheme_input.dart';
import '../../domain_layer/cantilever_scheme_input_global.dart';
import '../../domain_layer/mixin/mixin_rc_str.dart';
import '../../domain_layer/mixin/mixin_str_general_cals.dart';
import '../../domain_layer/mixin/mixin_tools_for_ui.dart';
import '../screen/homescreen.dart';

// domain layer
import '../../domain_layer/global_data.dart';
import '../../domain_layer/loading_table.dart';
import '../../domain_layer/preferences.dart';

part 'cantilever_scheme_data_controller.g.dart';

@riverpod
class CantileverSchemeDataController extends _$CantileverSchemeDataController
    with MixinToolsForUI, StrGeneralCals, RCStrHK {
  @override
  FutureOr<CantileverSchemeData> build() async {
    final cantileverSchemeData =
        await ref
            .read(appDatabaseControllerProvider.notifier)
            .queryCantileverSchemeData();
    final data1 = ref.watch(loadingTablesControllerProvider);
    final data2 = ref.watch(cantileverSchemeInputControllerProvider);
    final data3 = ref.watch(cantileverSchemeInputGlobalControllerProvider);
    return data1.when(
      data: (loadingTables) async {
        return data2.when(
          data: (inputs) async {
            return data3.when(
              data: (inputGlobal) async {
                //* Validate the loading
                final usages = loadingTables.map((x) => x.usage).toList();
                CantileverSchemeData schemeData;
                if (usages.contains(cantileverSchemeData.usage)) {
                  schemeData = cantileverSchemeData;
                } else {
                  schemeData = cantileverSchemeData.copyWith(
                    usage: usages.first,
                  );
                }
                return await cantileverBeamScheming();
              },
              error: (error, stackTrace) => CantileverSchemeData(),
              loading: () => CantileverSchemeData(),
            );
          },
          error: (error, stackTrace) => CantileverSchemeData(),
          loading: () => CantileverSchemeData(),
        );
      },
      error: (error, stackTrace) => CantileverSchemeData(),
      loading: () => CantileverSchemeData(),
    );
  }

  Future<void> updateTransferBeamSchemeData(CantileverSchemeData data) async {
    state = AsyncLoading();
    state = AsyncData(data);
  }

  Future<CantileverSchemeData> cantileverBeamScheming() async {
    // * Read Necessary data
    final GlobalData globalData = await ref.read(
      globalDataControllerProvider.future,
    );
    final List<LoadingTable> loadingTables = await ref.read(
      loadingTablesControllerProvider.future,
    );
    final List<CantileverSchemeInput> inputs = await ref.read(
      cantileverSchemeInputControllerProvider.future,
    );
    final CantileverSchemeInputGlobal inputGlobal = await ref.read(
      cantileverSchemeInputGlobalControllerProvider.future,
    );
    // ********************************
    // initialization
    // ********************************
    // iteration status
    late bool shouldRedesign;
    bool skipIteration = false;
    bool redesignacT = true, redesignacC = true, reDesignLinks = true;
    int linksOptimizationSteps = 0;
    int linksOptimizationStepsLimit = 10;

    // rebar diameters
    final List<int> diaT = [12, 16, 20, 25, 32, 40]; // tension bar diameter
    final List<int> diaC = [12, 16, 20, 25, 32, 40]; // tension bar diameter
    final List<int> diaL = [10, 12, 16, 20, 25]; // links diameter
    final int spacerBar = 16;
    //inddex for iteration for rebar diameters and links diameter
    int i1 = 0, i2 = 0, j = 0;

    //Initialize the width
    double width = 200;

    //k-value
    late double k, kLimit;

    //effective depth and lever-arm
    late double d, dC, z;

    //nos of rebar
    late int nRPro;

    // links provided: legs nos and spacing
    late int nLPro;
    int sPro = 300;

    //nos of layer of tension and compression steel
    int layerT = 1, layerC = 1;

    // Some Limit for reasonable design
    int layerLimit = inputGlobal.maxLayers; // max layer of rebars
    int sMin = inputGlobal.minS, sMax = inputGlobal.maxS; // links spacing
    double widthLimit = inputGlobal.maxWidth;
    double minBarS = 80; // min bar clear spacing

    //req'd tension, compression steel, and links
    late double asT, asC, lReq;

    //provided tension, compression steel and links
    late double asTPro, asCPro, lPro;
    String asTProText = '', asCProText = '', lProText = '';

    //revised loading (incorproate Self Weight)
    late double ulsUDL;

    // design moment and shear
    late double mD, vD;

    // design capacity
    double fy = 500; // [MPa]

    late double vC_, vD_, vR_;

    // record
    StringBuffer buffer = StringBuffer();
    List<bool> status = [false, false, false];
    LoadingTable loadingTable;

    //Some global data for easy use
    final double f1 = globalData.sdlFactor, f2 = globalData.llFactor;
    double sdl, ll;

    // //For Calculating internal force
    //For Calculating internal force
    List<double> listMd = [], listVd = [], listX = [];
    double xMaxMd = 0, xMaxVd = 0;
    double tempMd = 0, tempVd = 0, tempP = 0, span = 0;
    int division = 100;
    Set<double> setA = {}, setB = {}, setC = {};
    Map<String, dynamic> beamForceMap = {};
    String beamForceInString; //JSON String version
    // ********************************
    // Calculation
    // ********************************
    shouldRedesign = true; // at least run the first
    // bool widthAdjused = false;

    // initialize nos of rebars and links legs
    nRPro =
        ((width - 2 * (inputGlobal.cover + diaL[j]) + minBarS) /
                (max(diaT.last, diaC.last) + minBarS))
            .floor();
    if (nRPro < 2) {
      nRPro = 2;
    }

    if (nRPro % 2 == 0) {
      nLPro = (nRPro / 4).ceil() * 2;
    } else {
      nLPro = (((nRPro - 1) / 4).ceil()) * 2;
    }
    while (shouldRedesign) {
      // ********************************
      // todo: design moment and shear adjusted with beam self-weight
      // ********************************

      //Reset For Calculating internal force
      listMd = [];
      listVd = [];
      listX = [];
      xMaxMd = 0;
      xMaxVd = 0;
      tempMd = 0;
      tempVd = 0;
      tempP = 0;
      span = 0;
      setA = {};
      setB = {};
      listX = [];

      loadingTable = loadingTables.firstWhere(
        (item) => item.usage == inputGlobal.usage,
      );
      sdl =
          (loadingTable.finish * globalData.finishUnitWeight * pow(10, -3) +
              loadingTable.service) *
          inputGlobal.loadWidth;
      ll = loadingTable.liveLoad * inputGlobal.loadWidth;

      ulsUDL =
          f1 * sdl + // Floor load: SDL
          f2 * ll + // Floor Load: LL
          f1 *
              getSelfWeight(
                globalData.rcUnitWeight,
                width * inputGlobal.strZone,
              ) + //beam Self weight
          f1 *
              getPressureFromThick(
                inputGlobal.slabThickness,
                globalData.rcUnitWeight,
              ) *
              inputGlobal.loadWidth; // slab self-weight

      listX = List.generate(division + 1, (i) {
        return i * (inputGlobal.span / division);
      });

      // !we add point load location + small distance  into x
      // !coz the shear exactly at point load location is undefined.
      listX.insertAll(
        listX.length,
        inputs.map((e) {
          if (e.distA == 0 || e.distA == inputGlobal.span) {
            return e.distA;
          }
          if (e.distA + inputGlobal.span / 200 < inputGlobal.span) {
            return e.distA + inputGlobal.span / 200;
          } else {
            return inputGlobal.span;
          }
        }).toSet(),
      );

      // !make sure list X does not contain any value same as inputs.distA
      // !coz the shear exactly at point load location is undefined.
      setA = listX.toSet();
      setC = {0, inputGlobal.span};
      setB = inputs.map((e) => e.distA).toSet();
      setB = setB.difference(setC); // make sure end point is not in Set B
      setA = setA.difference(setB);

      // convert the location back to list and sort it to ascending order
      listX = setA.toList();
      listX.sort();

      span = inputGlobal.span;
      for (double x in listX) {
        //* 1.  Md and Vd due to UDL
        tempMd += getMomentAtXBeamUDL(
          ulsUDL,
          span,
          x,
          beamType: BeamType.Cantilever,
        );
        tempVd += getShearAtXBeamUDL(
          ulsUDL,
          span,
          x,
          beamType: BeamType.Cantilever,
        );

        //* 2. Md and Vd due to point load
        for (int i = 0; i < inputs.length; i++) {
          tempP = inputs[i].pointLoad;
          if (inputs[i].distA == 0 || inputs[i].distA == span) {
            continue;
          }
          tempMd += getMomentAtXBeamPointLoad(
            tempP,
            span,
            x,
            inputs[i].distA,
            beamType: BeamType.Cantilever,
          );
          tempVd += getShearAtXBeamPointLoad(
            tempP,
            span,
            x,
            inputs[i].distA,
            beamType: BeamType.Cantilever,
          );
        }

        //store the result
        listMd.add(tempMd);
        listVd.add(tempVd);

        //reset
        tempMd = 0;
        tempVd = 0;
      }
      // get the design Moment and shear
      mD = max(maxFromNum(listMd).abs(), minFromNum(listMd).abs());
      vD = max(maxFromNum(listVd).abs(), minFromNum(listVd).abs());

      // Location of max moment & shear
      // xMaxMd = listX[listMd.indexOf(mD)];
      if (mD == maxFromNum(listMd).abs()) {
        xMaxVd = listX[listMd.indexOf(maxFromNum(listMd))];
      } else {
        xMaxVd = listX[listMd.indexOf(minFromNum(listMd))];
      }

      if (vD == maxFromNum(listVd).abs()) {
        xMaxVd = listX[listVd.indexOf(maxFromNum(listVd))];
      } else {
        xMaxVd = listX[listVd.indexOf(minFromNum(listVd))];
      }
      // effective depth
      d =
          inputGlobal.strZone -
          inputGlobal.cover -
          diaL[j] -
          (diaT[i1] + (diaT.last + spacerBar) * (layerT - 1)) / 2;

      dC =
          inputGlobal.cover +
          diaL[j] +
          (diaC[i2] + (diaC.last + spacerBar) * (layerC - 1)) / 2;

      k = getkValueRectangle(mD, width, d, inputGlobal.fcu);
      kLimit = getkValueLimitRectangle(inputGlobal.fcu);
      z = getLeverArmRectangle(k, kLimit, d);
      asC = getRecBeamReqCompressionBar(
        mD,
        width,
        d,
        dC,
        inputGlobal.fcu,
        fy: fy,
      );
      asT = getRecBeamReqTensionBar(mD, width, d, dC, inputGlobal.fcu, fy: fy);
      asTPro = getRecBeamSteelProvided(diaT, layerT, diaT[i1], nRPro);
      asCPro = getRecBeamSteelProvided(diaC, layerC, diaC[i2], nRPro);

      // -----------------------------
      // todo: design links
      // -----------------------------
      vD_ = vD * pow(10, 3) / (width * d);
      vC_ = getConcreteShearStrength(
        asTPro,
        inputGlobal.fcu,
        width,
        d,
        withMinLinks: true,
      );
      vR_ = getConcreteShearParams_vR(inputGlobal.fcu);
      lReq = getRecBeamReqLinks(vD, asTPro, inputGlobal.fcu, width, d, fy: fy);
      lPro = pow(diaL[j], 2) * pi / 4 * nLPro / sPro;

      // ********************************
      // Design validaiton in each loop
      // ********************************
      //* concept of adjustmernt
      //* Flexural: increase bar size, then layer | reset bar size to min when layer increases
      //* Shear: increase legs nos, then decrease spacing, and finally increase bar size.
      //* The legs nos reset to 2 if spacing decreases
      //* the legs resets to 2 and spacing reset to 300 if bar size increases

      // ! if the width exceeds the max width, directly escape and declare the design failed
      if (width > inputGlobal.maxWidth) {
        status[0] = false;
        status[1] = false;
        status[2] = false;
        // width -= 100;
        break;
      }

      //! if too narrow, increase width, skip to next iteration.
      if (nRPro <= 1) {
        width += 100;
        skipIteration = true;
      }

      //! if the k-value exceeds preferred k-value/steel ratio, skip iteration
      if (k > inputGlobal.mainKValue ||
          asTPro > inputGlobal.mainSteelRatio * inputGlobal.strZone * width ||
          asCPro > inputGlobal.mainSteelRatio * inputGlobal.strZone * width) {
        width += 100;
        // no need to run rest of design process
        skipIteration = true;
      }

      //! Skip iteration if necessary
      if (skipIteration) {
        //reset diameters, layers, spacing, nos of rebars
        i1 = 0;
        i2 = 0;
        layerT = 1;
        layerC = 1;
        nRPro =
            ((width - 2 * (inputGlobal.cover + diaL[j]) + minBarS) /
                    (max(diaT.last, diaC.last) + minBarS))
                .floor();
        j = 0;
        sPro = sMax;
        nLPro = 2;

        if (nRPro % 2 == 0) {
          nLPro = (nRPro / 4).ceil() * 2;
        } else {
          nLPro = (((nRPro - 1) / 4).ceil()) * 2;
        }
        skipIteration = false;
        continue;
      }

      // Todo: check tension bars
      if (asTPro < asT) {
        redesignacT = true;
        if (i1 + 1 <= diaT.length - 1) {
          i1 += 1;
        } else if (i1 == diaT.length - 1 && layerT + 1 <= layerLimit) {
          i1 = 0;
          layerT += 1;
        } else if (i1 == diaT.length - 1 &&
            layerT == layerLimit &&
            width + 100 <= widthLimit) {
          width += 100;

          //reset rebars and links arrangement
          i1 = 0;
          i2 = 0;
          layerT = 1;
          layerC = 1;
          nRPro =
              ((width - 2 * (inputGlobal.cover + diaL[j]) + minBarS) /
                      (max(diaT.last, diaC.last) + minBarS))
                  .floor();
          j = 0;
          sPro = sMax;
          nLPro = 2;

          if (nRPro % 2 == 0) {
            nLPro = (nRPro / 4).ceil() * 2;
          } else {
            nLPro = (((nRPro - 1) / 4).ceil()) * 2;
          }
          continue; // no need to run rest of design process
        } else {
          redesignacT = false;
        }
      } else {
        redesignacT = false;
        status[0] = true;
      }

      // todo:  check compression bars
      if (asCPro < asC) {
        redesignacC = true;
        if (i2 + 1 <= diaC.length - 1) {
          i2 += 1;
        } else if (i2 == diaC.length - 1 && layerC + 1 <= layerLimit) {
          i2 = 0;
          layerC += 1;
        } else if ((i2 == diaC.length - 1 &&
            layerC == layerLimit &&
            width + 100 <= widthLimit)) {
          width += 100;
          //reset rebars and links
          i1 = 0;
          i2 = 0;
          layerT = 1;
          layerC = 1;
          nRPro =
              ((width - 2 * (inputGlobal.cover + diaL[j]) + minBarS) /
                      (max(diaT.last, diaC.last) + minBarS))
                  .floor();
          j = 0;
          sPro = sMax;
          nLPro = 2;

          if (nRPro % 2 == 0) {
            nLPro = (nRPro / 4).ceil() * 2;
          } else {
            nLPro = (((nRPro - 1) / 4).ceil()) * 2;
          }
          continue; // no need to run rest of design process
        } else {
          redesignacC = false; //escape loop and no need to design anymore
        }
      } else {
        redesignacC = false;
        status[1] = true;
      }

      //todo: check shear
      if (lPro < lReq) {
        reDesignLinks = true;
        if (nLPro + 1 <= nRPro) {
          nLPro += 1;
        } else if (nLPro == nRPro && sPro - 25 >= sMin) {
          nLPro = 2;
          sPro -= 25;
        } else if (nLPro == nRPro && sPro == sMin && j + 1 <= diaL.length - 1) {
          nLPro = 2;
          sPro = sMax;
          j += 1;
        } else {
          reDesignLinks = false;
        }
      } else {
        // there could be situation links pass, rebar not
        // then the width and effective depth keeps change
        // then req'd links keep reducing while provided links kept at high amount
        // Therefore, we redesign in case the provided links are 20% more than required

        if (lPro > lReq * 1.2 &&
            linksOptimizationSteps < linksOptimizationStepsLimit) {
          linksOptimizationSteps += 1;
          reDesignLinks = true;
          nLPro = 2;
          sPro = sMax;
          j = 0;
        } else {
          reDesignLinks = false;
          status[2] = true;
        }
      }
      shouldRedesign = redesignacT || redesignacC || reDesignLinks;
    }

    // todo: record the internal force for return
    for (int i = 0; i < listX.length; i++) {
      final beamForce = StrForce(x: listX[i], Md: listMd[i], Vd: listVd[i]);
      beamForceMap[i.toString()] = beamForce;
    }
    beamForceInString = jsonEncode(
      beamForceMap,
      toEncodable:
          (value) =>
              value is StrForce
                  ? value.toJson()
                  : throw UnsupportedError('Cannot convert to JSON: $value'),
    );

    // ********************************
    // Return Design
    // ********************************
    // form tension bar designation
    asTProText = getRecBeamSteelProvidedText(diaT, layerT, diaT[i1], nRPro);
    if (!status[0]) {
      asTProText = '(Fail) $asTProText';
    }
    asCProText = getRecBeamSteelProvidedText(diaC, layerC, diaC[i2], nRPro);
    if (!status[1]) {
      asCProText = '(Fail) $asCProText';
    }
    lProText = getRecBeamLinksProvidedText(diaL[j], sPro, nLPro);
    if (!status[2]) {
      lProText = '(Fail) $lProText';
    }

    // before return, record the cals result
    _recordCalsResult(
      buffer,
      inputGlobal,
      globalData,
      ulsUDL: ulsUDL,
      xMaxMd: xMaxMd,
      xMaxVd: xMaxVd,
      k: k,
      k_limit: kLimit,
      n_r_pro: nRPro,
      As_t_pro: asTPro,
      As_t: asT,
      As_c_pro: asCPro,
      As_c: asC,
      M_d: mD,
      V_d: vD,
      width: width,
      layer_t: layerT,
      layer_c: layerC,
      dia_t: diaT[i1],
      dia_c: diaC[i2],
      dia_l: diaL[j],
      d: d,
      d_c: dC,
      z: z,
      As_t_pro_text: asTProText,
      As_c_pro_text: asCProText,
      l_pro_text: lProText,
      l_pro: lPro,
      l_req: lReq,
      n_l_pro: nLPro,
      v_d: vD_,
      v_c: vC_,
      v_r: vR_,
      s_pro: sPro,
      s_min: sMin,
      s_max: sMax,
      status: status,
    );

    // ! Finally assign the result
    final newScheme = CantileverSchemeData(
      usage: inputGlobal.usage,
      slabThickness: inputGlobal.slabThickness,
      span: inputGlobal.span,
      loadWidth: inputGlobal.loadWidth,
      strZone: inputGlobal.strZone,
      fcu: inputGlobal.fcu,
      cover: inputGlobal.cover,
      mainWidth: width,
      mainTopBar: asTProText,
      mainBottomBar: asCProText,
      mainLinks: lProText,
      id: '1',
      calsLog: buffer.toString(),
      beamForce: beamForceInString,
    );

    state = AsyncData(newScheme);
    return newScheme;
  }

  void _recordCalsResult(
    StringBuffer buffer,
    CantileverSchemeInputGlobal input,
    GlobalData globalData, {
    double xMaxMd = 0.0,
    double xMaxVd = 0.0,
    double ulsUDL = 0.0,
    double k_limit = 0.0,
    double k = 0.0,
    int n_r_pro = 0,
    double As_t_pro = 0.0,
    double As_t = 0.0,
    double As_c_pro = 0.0,
    double As_c = 0.0,
    double M_d = 0.0,
    double V_d = 0.0,
    double width = 0.0,
    int layer_t = 0,
    int layer_c = 0,
    int dia_t = 0,
    int dia_c = 0,
    int dia_l = 0,
    double d = 0.0,
    double d_c = 0.0,
    double z = 0.0,
    String As_t_pro_text = '',
    String As_c_pro_text = '',
    String l_pro_text = '',
    double l_pro = 0.0,
    double l_req = 0.0,
    int n_l_pro = 0,
    double v_d = 0.0,
    double v_c = 0.0,
    double v_r = 0.0,
    int s_pro = 0,
    int s_min = 0,
    int s_max = 0,
    List<bool> status = const [],
  }) {
    final x = globalData.unit;
    late final List<String> unit;
    switch (x) {
      case 'metrics':
        unit = PreferredUnit.metrics;
        break;
      case 'imperial':
        unit = PreferredUnit.imperial;
        break;
      default:
        unit = PreferredUnit.metrics;
        break;
    }
    // final unit = globalData.unit;
    NumberFormat f0 = NumberFormat('0'),
        f1 = NumberFormat('0.0'),
        f3 = NumberFormat('0.000');
    buffer.clear();
    buffer.write('Span: ${f1.format(input.span)} [${unit[3]}] | ');

    //get loading

    buffer.write('Load Width: ${f1.format(input.loadWidth)} [${unit[3]}] | ');
    buffer.write(
      'Slab Thickness: ${f1.format(input.slabThickness)} [${unit[4]}]\n',
    );

    buffer.write('width: ${f0.format(width)} [${unit[4]}] | ');
    buffer.write('ULS UDL: ${f1.format(ulsUDL)} [${unit[0]}/${unit[3]}]\n');
    buffer.write('M: ${f0.format(M_d)} [${unit[2]}] | ');
    buffer.write('xMaxMd: ${f3.format(xMaxMd)} [${unit[3]}] | ');
    buffer.write('V: ${f0.format(V_d)} [${unit[0]}] | ');
    buffer.write('xMaxVd: ${f3.format(xMaxVd)} [${unit[3]}]\n');

    buffer.write('k: ${f3.format(k)} | ');
    buffer.write('k_limit: ${f3.format(k_limit)} | ');
    buffer.write('preferred k: ${f3.format(input.mainKValue)}\n');

    buffer.write('v_d: ${f3.format(v_d)} [${unit[5]}] | ');
    buffer.write('v_c: ${f3.format(v_c)} [${unit[5]}] | ');
    buffer.write('v_r: ${f3.format(v_r)} [${unit[5]}]\n');

    buffer.write('dia_t: ${f0.format(dia_t)} [${unit[4]}] | ');
    buffer.write('dia_c: ${f0.format(dia_c)} [${unit[4]}] | ');
    buffer.write('dia_l: ${f0.format(dia_l)} [${unit[4]}]\n');

    buffer.write('n_r_pro: ${f0.format(n_r_pro)} [bars] | ');
    buffer.write('n_l_pro: ${f0.format(n_l_pro)} [legs]\n');

    buffer.write('s_pro: ${f0.format(s_pro)} [${unit[4]}] | ');
    buffer.write('s_min: ${f0.format(s_min)} [${unit[4]}] | ');
    buffer.write('s_max: ${f0.format(s_max)} [${unit[4]}]\n');

    buffer.write('layer_t: ${f0.format(layer_t)} | ');
    buffer.write('layer_c: ${f0.format(layer_c)} | ');
    buffer.write('d: ${f0.format(d)} [${unit[4]}] | ');
    buffer.write('d_c: ${f0.format(d_c)} [${unit[4]}] | ');
    buffer.write('z: ${f0.format(z)} [${unit[4]}]\n');

    buffer.write('As_t_pro: ${f0.format(As_t_pro)} [${unit[6]}] | ');
    buffer.write('As_t: ${f0.format(As_t)} [${unit[6]}]| ');
    buffer.write(
      'As_t limit: ${f0.format((input.mainSteelRatio) * (width) * (input.strZone))} [${unit[6]}]\n',
    );
    buffer.write('As_c_pro: ${f0.format(As_c_pro)} [${unit[6]}] | ');
    buffer.write('As_c: ${f0.format(As_c)} [${unit[6]}] | ');
    buffer.write(
      'As_c limit: ${f0.format((input.mainSteelRatio) * (width) * (input.strZone))} [${unit[6]}]\n',
    );

    buffer.write('l_pro: ${f3.format(l_pro)} [${unit[6]}/${unit[4]}] | ');
    buffer.write('l_req: ${f3.format(l_req)} [${unit[6]}/${unit[4]}]\n');

    buffer.write('As_t_pro Designation: $As_t_pro_text\n');
    buffer.write('As_c_pro Designation: $As_c_pro_text\n');
    buffer.write('l_pro Designation: $l_pro_text\n');

    if (status[0]) {
      buffer.write('Tension bars: pass | ');
    } else {
      buffer.write('Tension bars: fail | ');
    }
    if (status[1]) {
      buffer.write('Compression bars: pass | ');
    } else {
      buffer.write('Compression bars: fail | ');
    }
    if (status[2]) {
      buffer.write('Links: pass\n');
    } else {
      buffer.write('Links: fail\n');
    }
  }
}
