import 'dart:math';

import '../data_struct/crack_width_struct.dart';

mixin RCStrHK {
  /// Modulus
  /// input:
  ///
  /// fcu =  concrete strength [MPa]
  ///
  /// output:
  ///
  /// elastic modulus [MPa]
  double getElasticModulus(double fcu) {
    return (3.46 * pow(fcu, 0.5) + 3.21) * pow(10, 3); // [MPa]
  }

  //* ---------------------------
  //*
  //* BEAM CALS
  //*
  //* ---------------------------

  //* k-value
  /// input:
  ///
  /// m = moment [kNm]
  ///
  /// b = width [mm]
  ///
  /// d = effective depth [mm]
  ///
  /// fcu =  concrete strength [MPa]
  ///
  /// output:
  ///
  /// k-value [-]
  double getkValueRectangle(double m, double b, double d, double fcu) {
    return m * pow(10, 6) / (b * pow(d, 2) * fcu);
  }

  /// input:
  ///
  /// fcu =  concrete strength [MPa]
  ///
  /// (optional) beta = moment after redistribution / moment before redistribution [-]
  ///
  /// output:
  ///
  /// k-limit [-]
  double getkValueLimitRectangle(double fcu, {double beta = 0.9}) {
    /// beta = moment after redistribution / moment before redistribution
    ///
    /// for beta = 0.9, it means 10% moment redistribution
    if (fcu <= 45) {
      return (0.402 * (beta - 0.4) - 0.18 * pow((beta - 0.4), 2));
    } else if (fcu > 45 && fcu <= 70) {
      return 0.357 * (beta - 0.5) - 0.143 * pow((beta - 0.5), 2);
    } else {
      return 0.094;
    }
  }

  //* Lever arm
  ///
  /// input:
  ///
  /// d = effective depth [mm]
  ///
  /// kValue = k-value [-]
  ///
  /// kLimit = k-limit [-]
  ///
  /// output:
  ///
  /// lever arm [mm]
  double getLeverArmRectangle(double kValue, double kLimit, double d) {
    late final double z;
    if (kValue <= kLimit) {
      z = min(0.95 * d, d * (0.5 + pow(0.25 - kValue / 0.9, 0.5)));
    } else {
      z = d * (0.5 + pow(0.25 - kLimit / 0.9, 0.5));
    }
    return z;
  }

  //* ultimate concrete strain
  /// input:
  ///
  /// fcu =  concrete strength [MPa]
  ///
  /// output:
  ///
  /// ultimate concrete strain [-]
  double getConcreteStrainRectangle(double fcu) {
    if (fcu <= 60) {
      return 0.0035;
    } else {
      return 0.0035 - 0.00006 * sqrt(fcu - 60);
    }
  }

  //* neutral axis
  /// input:
  ///
  /// d = effective depth [mm]
  ///
  /// fcu =  concrete strength [MPa]
  ///
  /// kValue = k-value [-]
  ///
  /// (optional) beta = moment after redistribution / moment before redistribution [-]
  ///
  /// output:
  ///
  /// neutral axis [mm]
  double getNeutralAxisULSStateRectangle(
    double d,
    double fcu,
    double kValue, {
    double beta = 0.9,
  }) {
    final double z = getLeverArmRectangle(
      kValue,
      getkValueLimitRectangle(beta),
      d,
    );
    if (fcu <= 45) {
      return min((d - z) / 0.45, (beta - 0.4) * d);
    } else if (fcu > 45 && fcu <= 70) {
      return min((d - z) / 0.40, (beta - 0.5) * d);
    } else {
      return (d - z) / 0.36;
    }
  }

  /// @brief assume the section is cracked under SLS state(Fct = 0)
  /// input:
  ///
  /// b = width [mm]
  ///
  /// d = effective depth [mm]
  ///
  /// fcu =  concrete strength [MPa]
  ///
  /// As = area of tension steel [mm2]
  ///
  /// (optional) Es = steel elastic modulus [MPa]
  ///
  /// output:
  ///
  /// neutral axis [mm]
  double getNeutralAxisSLSStateRectangle(
    double b,
    double d,
    double fcu,
    double As, {
    double Es = 200000,
  }) {
    double Ec = getElasticModulus(fcu);
    double alpha = Es / Ec;
    final double x1 =
        (-alpha * As + sqrt(pow(alpha * As, 2) + 2 * b * alpha * As * d)) /
        b; // always > 0from Reinforced Concrete Design , Mosley
    // final double x2 = -alpha*As - sqrt(pow(alpha*As,2)+2*b*alpha*As*d)/b; // Always < 0
    return x1;
  }

  //* Service Stress
  /// @brief assume the section is cracked under SLS state(Fct = 0)
  /// input:
  ///
  /// M = moment [kNm]
  ///
  /// b = width [mm]
  ///
  /// d = effective depth [mm]
  ///
  /// fcu =  concrete strength [MPa]
  ///
  /// As = area of tension steel [mm2]
  ///
  /// (optional) Es = steel elastic modulus [MPa]
  ///
  /// output:
  ///
  /// concrete service stress [MPa]
  double getConcreteServiceStress(
    double M,
    double b,
    double d,
    double fcu,
    double As, {
    double Es = 200000,
  }) {
    double x = getNeutralAxisSLSStateRectangle(b, d, fcu, As, Es: Es);
    return M * pow(10, 6) / (0.5 * b * x * (d - x / 3));
  }

  /// @brief assume the section is cracked under SLS state(Fct = 0)
  /// input:
  ///
  /// M = moment [kNm]
  ///
  /// b = width [mm]
  ///
  /// d = effective depth [mm]
  ///
  /// fcu =  concrete strength [MPa]
  ///
  /// As = area of tension steel [mm2]
  ///
  /// (optional) Es = steel elastic modulus [MPa]
  ///
  /// output:
  ///
  /// rebar service stress [MPa]
  double getRebarServiceStress(
    double M,
    double b,
    double d,
    double fcu,
    double As, {
    double Es = 200000,
  }) {
    double x = getNeutralAxisSLSStateRectangle(b, d, fcu, As, Es: Es);
    double fcc = getConcreteServiceStress(M, b, d, fcu, As, Es: Es);
    if (fcc <= 0) {
      return 0;
    }
    return (0.5 * b * x * fcc) / As;
  }

  /// @brief assume the section is cracked under SLS state(Fct = 0)
  /// and the strain at the
  /// input:
  ///
  /// M = moment [kNm]
  ///
  /// h = height of section [mm]
  ///
  /// b = width [mm]
  ///
  /// d = effective depth [mm]
  ///
  /// fcu =  concrete strength [MPa]
  ///
  /// As = area of tension steel [mm2]
  ///
  /// cover = cover to tension steel [mm]
  ///
  /// dia_l = diameter of links [mm]
  ///
  /// Ast_text = text of tension steel (e.g. 2T16+2T16+2T16)
  ///
  /// (optional) Es = steel elastic modulus [MPa]
  ///
  ///
  //* Crack Width
  CrackWidth getSlabCrackWidth(
    double M,
    double h,
    double b,
    double d,
    double fcu,
    double As,
    double c_min,
    int dia_l, {
    double Es = 200000,
  }) {
    if (M <= 0) {
      return CrackWidth();
    }
    double x = getNeutralAxisSLSStateRectangle(b, d, fcu, As, Es: Es);
    double fcc = getConcreteServiceStress(M, b, d, fcu, As, Es: Es);
    double fst = getRebarServiceStress(M, b, d, fcu, As, Es: Es);
    double a_pi = h;
    double y = h - x;
    double e_1 = y * (fst / Es) / (d - x);
    double e_m = e_1 - (b * (h - x) * (a_pi - x)) / (3 * Es * As * (d - x));
    double a_cr = c_min + dia_l;
    double crackWidth = (3 * a_cr * e_m) / (1 + 2 * (a_cr - c_min) / (h - x));
    return CrackWidth(
      M: M,
      h: h,
      b: b,
      d: d,
      fcu: fcu,
      As: As,
      x: x,
      c_min: c_min,
      dia_l: dia_l,
      Es: Es,
      a_cr: a_cr,
      fcc: fcc,
      fst: fst,
      a_pi: a_pi,
      e_1: e_1,
      e_m: e_m,
      crackWidth: crackWidth,
    );
  }
  //* Require Steel

  /// input:
  ///
  /// m = moment [kNm]
  ///
  /// b = width [mm]
  ///
  /// d = effective depth [mm]
  ///
  /// dc = compression steel  effective depth [mm]
  ///
  /// fcu =  concrete strength [MPa]
  ///
  /// fy = yield strength of steel [MPa]
  ///
  /// (optional) beta = moment after redistribution / moment before redistribution [-]
  ///
  /// output:
  ///
  /// area of compression steel required [mm2]
  double getRecBeamReqCompressionBar(
    double m,
    double b,
    double d,
    double dc,
    double fcu, {
    double beta = 0.9,
    double fy = 500,
  }) {
    final double kValue = getkValueRectangle(m, b, d, fcu);
    final double kLimit = getkValueLimitRectangle(fcu, beta: beta);
    if (kValue <= kLimit) {
      return 0;
    } else {
      return (kValue - kLimit) * fcu * b * pow(d, 2) / (0.87 * fy * (d - dc));
    }
  }

  /// input:
  ///
  /// m = moment [kNm]
  ///
  /// b = width [mm]
  ///
  /// d = effective depth [mm]
  ///
  /// dc = compression steel  effective depth [mm]
  ///
  /// fcu =  concrete strength [MPa]
  ///
  /// fy = yield strength of steel [MPa]
  ///
  /// beta = moment after redistribution / moment before redistribution [-]
  ///
  /// output:
  ///
  /// area of tension steel required [mm2]
  double getRecBeamReqTensionBar(
    double m,
    double b,
    double d,
    double dc,
    double fcu, {
    double beta = 0.9,
    double fy = 500,
  }) {
    late final Asc;
    final double kValue = getkValueRectangle(m, b, d, fcu);
    final double kLimit = getkValueLimitRectangle(fcu, beta: beta);
    final double z = getLeverArmRectangle(kValue, kLimit, d);
    if (kValue <= kLimit) {
      return m * pow(10, 6) / (0.87 * fy * z);
    } else {
      Asc = getRecBeamReqCompressionBar(m, b, d, dc, fcu);
      return kLimit * fcu * b * pow(d, 2) / (0.87 * fy * z) + Asc;
    }
  }

  /// input:
  ///
  /// possibleDia = list of possible diameter to be used [mm]
  ///
  /// layerNos = number of layer of rebar currently [-]
  ///
  /// currentDia = current diameter of rebar at the hgihest layer [mm]
  ///
  /// currentNosOfBar = current number of bar across section width [-]
  ///
  /// output:
  ///
  /// area of steel provided [mm2]
  double getRecBeamSteelProvided(
    List<int> possibleDia,
    int layerNos,
    int currentDia,
    int currentNosOfBar,
  ) {
    late final double aSPro;
    if (layerNos > 1) {
      aSPro =
          pow(currentDia, 2) * pi / 4 * currentNosOfBar +
          pow(possibleDia.last, 2) * pi / 4 * currentNosOfBar * (layerNos - 1);
    } else {
      aSPro = pow(currentDia, 2) * pi / 4 * currentNosOfBar;
    }
    return aSPro;
  }

  /// input:
  ///
  /// possibleDia = list of possible diameter to be used [mm]
  ///
  /// layerNos = number of layer of rebar currently [-]
  ///
  /// currentDia = current diameter of rebar at the hgihest layer [mm]
  ///
  /// currentNosOfBar = current number of bar across section width [-]
  ///
  /// output:
  ///
  /// steel provided in text format
  String getRecBeamSteelProvidedText(
    List<int> possibleDia,
    int layerNos,
    int currentDia,
    int currentNosOfBar,
  ) {
    String asTProText = '';
    for (int layer = 1; layer <= layerNos; layer++) {
      if (layer < layerNos) {
        asTProText = '$asTProText${currentNosOfBar}T${possibleDia.last}+';
      } else {
        asTProText = '$asTProText${currentNosOfBar}T${currentDia}';
      }
    }
    return asTProText;
  }

  /// input:
  ///
  /// Vd = design shear force [kN]
  ///
  /// aST = provided tension steel [mm2]
  ///
  /// fcu = concrete strength [MPa]
  ///
  /// w = width of section [mm]. Typically 1000 [mm]
  ///
  /// d = effective depth of section [mm]
  ///
  /// output:
  ///
  /// area of links required [mm2/mm]
  double getRecBeamReqLinks(
    double Vd,
    double aST,
    double fcu,
    double w,
    double d, {
    double? fy,
  }) {
    late final double lReq;
    fy ??= 500;

    final double vD_ = Vd * pow(10, 3) / (w * d);
    final double vC = getConcreteShearStrength(aST, fcu, w, d);
    final double vR = getConcreteShearParams_vR(fcu);

    if (vD_ <= (0.5 * vC)) {
      lReq = 0;
    } else if (vD_ > (0.5 * vC) && vD_ <= (vC + vR)) {
      lReq = vR * w / (0.87 * fy);
    } else if (vD_ > (vC + vR) && vD_ <= (0.8 * pow(fcu, 0.5))) {
      lReq = (vD_ - vC) * w / (0.87 * fy);
    } else {
      lReq = double.infinity;
    }
    return lReq;
  }

  /// input:
  ///
  /// dia: diameter of links [mm]
  ///
  /// spacing: spacing of links [mm]
  ///
  /// nosOfLegs: number of legs of links [-]
  ///
  /// output:
  ///
  /// area of links provided [mm2/mm]
  String getRecBeamLinksProvidedText(int dia, int spacing, int nosOfLegs) {
    late String lProText;
    lProText = '$dia-$spacing(${nosOfLegs}L)';
    return lProText;
  }

  //* ---------------------------
  //*
  //* SLAB CALS
  //*
  //* ---------------------------
  /// input:
  ///
  /// possibleDia = list of possible diameter to be used [mm]
  ///
  /// layerNos = number of layer of rebar currently [-]
  ///
  /// currentDia = current diameter of rebar at the hgihest layer [mm]
  ///
  /// barSpacing = spacing of rebar [mm]
  ///
  /// barMinSpacing = minimum  spacing of rebar [mm]
  ///
  /// width = width of section [mm]. Typically 1000 [mm]
  ///
  /// output:
  ///
  /// area of steel provided [mm2]
  double getOneWaySlabSteelProvided(
    List<int> possibleDia,
    int layerNos,
    int currentDia,
    int barSpacing,
    int barMinSpacing,
    double secWidth,
  ) {
    if (layerNos > 1) {
      return (pow(currentDia, 2) * pi / 4 * (secWidth / barSpacing) +
          pow(possibleDia.last, 2) *
              pi /
              4 *
              (secWidth / barMinSpacing) *
              (layerNos - 1));
    } else {
      return (pow(currentDia, 2) * pi / 4 * (secWidth / barSpacing));
    }
  }

  /// possibleDia = list of possible diameter to be used
  ///
  /// layerNos = number of layer of rebar currently
  ///
  /// currentDia = current diameter of rebar at the hgihest layer
  ///
  /// barSpacing = spacing of rebar
  ///
  /// barMinSpacing = minimum  spacing of rebar
  ///
  /// width = width of section [mm]. Typically 1000 [mm]
  String getOneWaySlabSteelProvidedText(
    List<int> possibleDia,
    int layerNos,
    int currentDia,
    int barSpacing,
    int barMinSpacing,
  ) {
    String asTProText = '';
    for (int layer = 1; layer <= layerNos; layer++) {
      if (layer < layerNos) {
        asTProText = '${asTProText}T${possibleDia.last}-${barMinSpacing}+';
      } else {
        asTProText = '${asTProText}T${currentDia}-$barSpacing';
      }
    }
    return asTProText;
  }
  //* Links
  /// input:
  ///
  /// currentDia = current diameter of links  [mm]
  ///
  /// barSpacingAcross = spacing of links across section width [mm]
  ///
  /// barSpacingAlong = spacing of links along section length [mm]
  ///
  /// secWidth = width of section [mm]. Typically 1000 [mm]

  /// output:
  ///
  /// area of links provided, Asv/s [mm2/mm]
  double getRecSlabLinksProvided(
    int currentDia,
    int barSpacingAcross,
    int barSpacingAlong,
    double secWidth,
  ) {
    return pow(currentDia, 2) *
        pi /
        4 *
        (secWidth / barSpacingAcross) /
        barSpacingAlong;
  }

  /// input:
  ///
  /// currentDia = current diameter of links [mm]
  ///
  /// barSpacingAcross = spacing of links across section width [mm]
  ///
  /// barSpacingAlong = spacing of links along section length [mm]
  ///
  /// output:
  ///
  /// links provided in text format
  String getRecSlabLinksProvidedText(
    int currentDia,
    int barSpacingAcross,
    int barSpacingAlong,
  ) {
    return 'T${currentDia}-$barSpacingAcross(Across)-$barSpacingAlong(Along)';
  }

  /// input:
  ///
  /// Vd = design shear force [kN]
  ///
  /// aST = provided tension steel [mm2]
  ///
  /// fcu = concrete strength [MPa]
  ///
  /// w = width of section [mm]. Typically 1000 [mm]
  ///
  /// d = effective depth of section [mm]
  ///
  /// output:
  ///
  /// area of links required, Asv/s [mm2/mm]
  double getRecSlabLinksRequired(
    double Vd,
    double asTPro,
    double fcu,
    double w,
    double d, {
    double? fy,
  }) {
    late final double lReq;
    fy ??= 500;

    final double vD_ = Vd * pow(10, 3) / (w * d);
    final double vC = getConcreteShearStrength(asTPro, fcu, w, d);
    final double vR = getConcreteShearParams_vR(fcu);

    if (vD_ <= (vC)) {
      lReq = 0;
    } else if (vD_ > (vC) && vD_ <= (vC + vR)) {
      lReq = vR * w / (0.87 * fy);
    } else if (vD_ > (vC + vR) && vD_ <= (0.8 * pow(fcu, 0.5))) {
      lReq = (vD_ - vC) * w / (0.87 * fy);
    } else {
      lReq = double.infinity;
    }
    return lReq;
  }
  //* ---------------------------
  //*
  //* COLUMN CALS
  //*
  //* ---------------------------

  /// input:
  ///
  /// fcu = concrete strength [MPa]
  ///
  /// columnSize = size of column [mm]
  ///
  /// fy = yield strength of steel [MPa]
  ///
  /// steelRatio = steel ratio [-]
  ///
  /// output:
  ///
  /// axial capacity of short column (pure axial, stocky column) [kN]
  double getAxialCapacityColumnCircle(
    double fcu,
    double columnSize,
    double fy,
    double steelRatio,
  ) {
    return (0.35 * fcu * (pow(columnSize, 2) * pi / 4) * (1 - steelRatio) +
            0.67 * fy * (pow(columnSize, 2) * pi / 4) * steelRatio) /
        1000;
  }

  /// input:
  ///
  /// fcu = concrete strength [MPa]
  ///
  /// columnSize = size of column [mm]
  ///
  /// fy = yield strength of steel [MPa]
  ///
  /// steelRatio = steel ratio [-]
  ///
  /// output:
  ///
  /// axial capacity of column [kN]
  double getAxialCapacityColumnSquare(
    double fcu,
    double columnSize,
    double fy,
    double steelRatio,
  ) {
    return (0.35 * fcu * (pow(columnSize, 2)) * (1 - steelRatio) +
            0.67 * fy * (pow(columnSize, 2)) * steelRatio) /
        1000;
  }

  //* ---------------------------
  //*
  //* PILE CALS
  //*
  //* ---------------------------
  /// input:
  ///
  /// pileDia = diameter of pile [mm]
  ///
  /// safeBearing = safe bearing capacity [kPa]
  ///
  /// ratioOfBelloutDia = ratio of bellout dia to pile dia [-]
  ///
  /// output:
  ///
  /// end bearing capacity [kN]
  double getEndBearningCapacity(
    double pileDia,
    double safeBearing, {
    double ratioOfBelloutDia = 1.0,
    double factorOfSafety = 1.0,
  }) {
    return safeBearing * // pressure
        (pi * pow(((pileDia * ratioOfBelloutDia) * pow(10, -3)), 2) / 4) /
        factorOfSafety; // area
  }

  /// input:
  ///
  /// pileDia = diameter of pile [mm]
  ///
  /// pileLength = length of pile [m]
  ///
  /// soilUnitWeight = unit weight of soil [kN/m3]
  ///
  /// kTan = tan of friction angle [-]
  ///
  /// output:
  ///
  /// shaft capacity [kN]
  double getFrictionalPileShaftCapacity(
    double pileDia,
    double pileLength,
    double soilUnitWeight,
    double kTan, {
    double factorOfSafety = 1.0,
  }) {
    return (pi * pileDia * pow(10, -3) * pileLength) * // shaft area
        (0.5 * soilUnitWeight * pileLength * kTan) /
        factorOfSafety; //shaft pressure
  }

  //* ---------------------------
  //*
  //* FOOTING CALS
  //*
  //* ---------------------------
  double getGroundBearingCapacity(
    double depth,
    double footingTopLevel,
    double waterTableLevel, {
    String groundType = 'Soil',
    double soilNValue = 10,
    double rockCapacity = 1000,
  }) {
    final double pc;
    switch (groundType) {
      case 'Soil':
        switch (soilNValue) {
          case > 50:
            if (footingTopLevel - depth / 1000 > waterTableLevel) {
              pc = 500; // [kPa]
            } else {
              pc = 250; // [kPa]. Submerged Case.
            }
            break;
          case > 30 && <= 50:
            if (footingTopLevel - depth / 1000 > waterTableLevel) {
              pc = 300; // [kPa]
            } else {
              pc = 150; // [kPa]. Submerged Case.
            }
            break;
          case > 10 && <= 30:
            if (footingTopLevel - depth / 1000 > waterTableLevel) {
              pc = 100; // [kPa]
            } else {
              pc = 50; // [kPa]. Submerged Case.
            }
            break;
          default:
            pc = 50;
            break;
        }
        break;
      default:
        pc = rockCapacity;
        break;
    }
    return pc;
  }

  //* ---------------------------
  //*
  //* MISC
  //*
  //* ---------------------------

  /// input:
  ///
  /// dia = diameter of rebar [mm]
  ///
  /// output:
  ///
  /// area of rebar [mm2]
  double getRebarArea(int dia) {
    return pi * pow(dia, 2) / 4;
  }

  /// input:
  ///
  /// aST = provided tension steel [mm2]
  ///
  /// fcu = concrete strength [MPa]
  ///
  /// w = width of section [mm].
  ///
  /// d = effective depth of section [mm]
  ///
  /// output:
  ///
  /// concrete shear strengt, vc [MPa]

  double getConcreteShearStrength(
    double aST,
    double fcu,
    double w,
    double d, {
    bool withMinLinks = false,
  }) {
    return (0.79 *
        pow(min(3, (100 * aST / (w * d))), 1 / 3) *
        max(withMinLinks ? 1.0 : 0.67, pow(400 / d, 1.0 / 4.0)) *
        max(pow((min(fcu, 80) / 25), 1 / 3), 1) /
        1.25);
  }

  /// input:
  ///
  /// fcu = concrete strength [MPa]
  ///
  /// output:
  ///
  /// concrete shear strength param, vr[MPa]

  double getConcreteShearParams_vR(double fcu) {
    return max(0.4 * pow(min(fcu, 80) / 40, 2 / 3), 0.4);
  }
}
