// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'steel_column_scheme_input.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$SteelColumnSchemeInput {

 String get usage; double get slabThickness; double get loadWidth; double get loadLength; int get nosOfFloor; String get steelColumnSchemeInputId;
/// Create a copy of SteelColumnSchemeInput
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$SteelColumnSchemeInputCopyWith<SteelColumnSchemeInput> get copyWith => _$SteelColumnSchemeInputCopyWithImpl<SteelColumnSchemeInput>(this as SteelColumnSchemeInput, _$identity);

  /// Serializes this SteelColumnSchemeInput to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is SteelColumnSchemeInput&&(identical(other.usage, usage) || other.usage == usage)&&(identical(other.slabThickness, slabThickness) || other.slabThickness == slabThickness)&&(identical(other.loadWidth, loadWidth) || other.loadWidth == loadWidth)&&(identical(other.loadLength, loadLength) || other.loadLength == loadLength)&&(identical(other.nosOfFloor, nosOfFloor) || other.nosOfFloor == nosOfFloor)&&(identical(other.steelColumnSchemeInputId, steelColumnSchemeInputId) || other.steelColumnSchemeInputId == steelColumnSchemeInputId));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,usage,slabThickness,loadWidth,loadLength,nosOfFloor,steelColumnSchemeInputId);

@override
String toString() {
  return 'SteelColumnSchemeInput(usage: $usage, slabThickness: $slabThickness, loadWidth: $loadWidth, loadLength: $loadLength, nosOfFloor: $nosOfFloor, steelColumnSchemeInputId: $steelColumnSchemeInputId)';
}


}

/// @nodoc
abstract mixin class $SteelColumnSchemeInputCopyWith<$Res>  {
  factory $SteelColumnSchemeInputCopyWith(SteelColumnSchemeInput value, $Res Function(SteelColumnSchemeInput) _then) = _$SteelColumnSchemeInputCopyWithImpl;
@useResult
$Res call({
 String usage, double slabThickness, double loadWidth, double loadLength, int nosOfFloor, String steelColumnSchemeInputId
});




}
/// @nodoc
class _$SteelColumnSchemeInputCopyWithImpl<$Res>
    implements $SteelColumnSchemeInputCopyWith<$Res> {
  _$SteelColumnSchemeInputCopyWithImpl(this._self, this._then);

  final SteelColumnSchemeInput _self;
  final $Res Function(SteelColumnSchemeInput) _then;

/// Create a copy of SteelColumnSchemeInput
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? usage = null,Object? slabThickness = null,Object? loadWidth = null,Object? loadLength = null,Object? nosOfFloor = null,Object? steelColumnSchemeInputId = null,}) {
  return _then(_self.copyWith(
usage: null == usage ? _self.usage : usage // ignore: cast_nullable_to_non_nullable
as String,slabThickness: null == slabThickness ? _self.slabThickness : slabThickness // ignore: cast_nullable_to_non_nullable
as double,loadWidth: null == loadWidth ? _self.loadWidth : loadWidth // ignore: cast_nullable_to_non_nullable
as double,loadLength: null == loadLength ? _self.loadLength : loadLength // ignore: cast_nullable_to_non_nullable
as double,nosOfFloor: null == nosOfFloor ? _self.nosOfFloor : nosOfFloor // ignore: cast_nullable_to_non_nullable
as int,steelColumnSchemeInputId: null == steelColumnSchemeInputId ? _self.steelColumnSchemeInputId : steelColumnSchemeInputId // ignore: cast_nullable_to_non_nullable
as String,
  ));
}

}


/// Adds pattern-matching-related methods to [SteelColumnSchemeInput].
extension SteelColumnSchemeInputPatterns on SteelColumnSchemeInput {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _SteelColumnSchemeInput value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _SteelColumnSchemeInput() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _SteelColumnSchemeInput value)  $default,){
final _that = this;
switch (_that) {
case _SteelColumnSchemeInput():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _SteelColumnSchemeInput value)?  $default,){
final _that = this;
switch (_that) {
case _SteelColumnSchemeInput() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String usage,  double slabThickness,  double loadWidth,  double loadLength,  int nosOfFloor,  String steelColumnSchemeInputId)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _SteelColumnSchemeInput() when $default != null:
return $default(_that.usage,_that.slabThickness,_that.loadWidth,_that.loadLength,_that.nosOfFloor,_that.steelColumnSchemeInputId);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String usage,  double slabThickness,  double loadWidth,  double loadLength,  int nosOfFloor,  String steelColumnSchemeInputId)  $default,) {final _that = this;
switch (_that) {
case _SteelColumnSchemeInput():
return $default(_that.usage,_that.slabThickness,_that.loadWidth,_that.loadLength,_that.nosOfFloor,_that.steelColumnSchemeInputId);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String usage,  double slabThickness,  double loadWidth,  double loadLength,  int nosOfFloor,  String steelColumnSchemeInputId)?  $default,) {final _that = this;
switch (_that) {
case _SteelColumnSchemeInput() when $default != null:
return $default(_that.usage,_that.slabThickness,_that.loadWidth,_that.loadLength,_that.nosOfFloor,_that.steelColumnSchemeInputId);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _SteelColumnSchemeInput extends SteelColumnSchemeInput {
   _SteelColumnSchemeInput({this.usage = '', this.slabThickness = 130.0, this.loadWidth = 8, this.loadLength = 12, this.nosOfFloor = 1, this.steelColumnSchemeInputId = ''}): super._();
  factory _SteelColumnSchemeInput.fromJson(Map<String, dynamic> json) => _$SteelColumnSchemeInputFromJson(json);

@override@JsonKey() final  String usage;
@override@JsonKey() final  double slabThickness;
@override@JsonKey() final  double loadWidth;
@override@JsonKey() final  double loadLength;
@override@JsonKey() final  int nosOfFloor;
@override@JsonKey() final  String steelColumnSchemeInputId;

/// Create a copy of SteelColumnSchemeInput
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$SteelColumnSchemeInputCopyWith<_SteelColumnSchemeInput> get copyWith => __$SteelColumnSchemeInputCopyWithImpl<_SteelColumnSchemeInput>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$SteelColumnSchemeInputToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _SteelColumnSchemeInput&&(identical(other.usage, usage) || other.usage == usage)&&(identical(other.slabThickness, slabThickness) || other.slabThickness == slabThickness)&&(identical(other.loadWidth, loadWidth) || other.loadWidth == loadWidth)&&(identical(other.loadLength, loadLength) || other.loadLength == loadLength)&&(identical(other.nosOfFloor, nosOfFloor) || other.nosOfFloor == nosOfFloor)&&(identical(other.steelColumnSchemeInputId, steelColumnSchemeInputId) || other.steelColumnSchemeInputId == steelColumnSchemeInputId));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,usage,slabThickness,loadWidth,loadLength,nosOfFloor,steelColumnSchemeInputId);

@override
String toString() {
  return 'SteelColumnSchemeInput(usage: $usage, slabThickness: $slabThickness, loadWidth: $loadWidth, loadLength: $loadLength, nosOfFloor: $nosOfFloor, steelColumnSchemeInputId: $steelColumnSchemeInputId)';
}


}

/// @nodoc
abstract mixin class _$SteelColumnSchemeInputCopyWith<$Res> implements $SteelColumnSchemeInputCopyWith<$Res> {
  factory _$SteelColumnSchemeInputCopyWith(_SteelColumnSchemeInput value, $Res Function(_SteelColumnSchemeInput) _then) = __$SteelColumnSchemeInputCopyWithImpl;
@override @useResult
$Res call({
 String usage, double slabThickness, double loadWidth, double loadLength, int nosOfFloor, String steelColumnSchemeInputId
});




}
/// @nodoc
class __$SteelColumnSchemeInputCopyWithImpl<$Res>
    implements _$SteelColumnSchemeInputCopyWith<$Res> {
  __$SteelColumnSchemeInputCopyWithImpl(this._self, this._then);

  final _SteelColumnSchemeInput _self;
  final $Res Function(_SteelColumnSchemeInput) _then;

/// Create a copy of SteelColumnSchemeInput
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? usage = null,Object? slabThickness = null,Object? loadWidth = null,Object? loadLength = null,Object? nosOfFloor = null,Object? steelColumnSchemeInputId = null,}) {
  return _then(_SteelColumnSchemeInput(
usage: null == usage ? _self.usage : usage // ignore: cast_nullable_to_non_nullable
as String,slabThickness: null == slabThickness ? _self.slabThickness : slabThickness // ignore: cast_nullable_to_non_nullable
as double,loadWidth: null == loadWidth ? _self.loadWidth : loadWidth // ignore: cast_nullable_to_non_nullable
as double,loadLength: null == loadLength ? _self.loadLength : loadLength // ignore: cast_nullable_to_non_nullable
as double,nosOfFloor: null == nosOfFloor ? _self.nosOfFloor : nosOfFloor // ignore: cast_nullable_to_non_nullable
as int,steelColumnSchemeInputId: null == steelColumnSchemeInputId ? _self.steelColumnSchemeInputId : steelColumnSchemeInputId // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

// dart format on
