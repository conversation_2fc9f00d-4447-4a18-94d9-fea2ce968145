import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:structify/domain_layer/global_data.dart';
import 'package:structify/domain_layer/slab_scheme_data.dart';
import 'package:structify/presentation_layer/small_elements/sketch/draw_slab_scheme_sketch.dart';
import 'package:structify/presentation_layer/small_elements/button/function_button.dart';

//below for printing
// import 'dart:typed_data';
// import 'dart:ui';
// import 'package:structify/presentation_layer/small_elements/loadcals_summary_ui.dart';

//domain layer
import '../../domain_layer/mixin/mixin_rc_str.dart';
import '../../domain_layer/mixin/mixin_str_general_cals.dart';
import '../../domain_layer/mixin/mixin_tools_for_ui.dart';
import '../../domain_layer/preferences.dart';

// presentation layer
import '../screen/homescreen.dart';
// import '../small_elements/custom_stateful_double_input.dart';
// import '../small_elements/custom_stateful_text_input.dart';
// import '../small_elements/global_data_ui.dart';

//misc
import 'button/selection_button.dart';

class SlabSchemeSummary extends ConsumerStatefulWidget {
  const SlabSchemeSummary({
    this.isExpanded,
    this.maxHeight,
    this.minHeight,
    super.key,
  });

  final bool? isExpanded;
  final double? maxHeight;
  final double? minHeight;

  @override
  ConsumerState<ConsumerStatefulWidget> createState() =>
      _SlabSchemeSummaryState();
}

class _SlabSchemeSummaryState extends ConsumerState<SlabSchemeSummary>
    with WidgetsBindingObserver, MixinToolsForUI, StrGeneralCals, RCStrHK {
  late bool _isExpanded;
  late double _maxHeight;
  late double _minHeight;
  late GlobalKey _seletctionButtonKey;
  late ScrollController _scrollController;

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void initState() {
    WidgetsBinding.instance.addObserver(this);
    _isExpanded = widget.isExpanded ?? false;
    _maxHeight = widget.maxHeight ?? 300;
    _minHeight = widget.minHeight ?? 50;
    _scrollController = ScrollController();
    _seletctionButtonKey = GlobalKey();

    // _updateHeights();

    super.initState();
  }

  @override
  void didChangeDependencies() {
    _updateHeights();
    super.didChangeDependencies();
    // Update heights when dependencies change, including after initState
  }

  @override
  void didChangeMetrics() {
    // This method is called when the metrics change (like resizing the window)
    setState(() {
      _updateHeights();
    });
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    final slabSchemeTables = ref.watch(slabSchemeDataControllerProvider);
    final globalData = ref.watch(globalDataControllerProvider);

    final TextStyle subtitleStyle = textTheme.labelMedium!;
    final TextStyle bodyStyle = textTheme.bodySmall!;

    // return Placeholder();

    return slabSchemeTables.when(
      data: (tables) {
        //start reutnr real stuffs when loading tables available
        return globalData.when(
          //also, start reutnr real stuffs when global data tables available
          data: (data) {
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding: const EdgeInsets.fromLTRB(10, 0, 10, 0),
                  child: Divider(),
                ),

                Padding(
                  padding: const EdgeInsets.fromLTRB(10, 0, 0, 0),
                  child: Align(
                    alignment: Alignment.centerLeft,
                    child: Wrap(
                      crossAxisAlignment: WrapCrossAlignment.center,
                      children: [
                        IconButton(
                          icon: Icon(
                            _isExpanded ? Icons.expand_less : Icons.expand_more,
                          ),
                          color: colorScheme.onSurface,
                          onPressed: () {
                            setState(() {
                              _isExpanded = !_isExpanded;
                            });
                          },
                        ),
                        Text(
                          'Slab Scheme Summary ',
                          style: textTheme.titleLarge!.copyWith(
                            color: colorScheme.onSurface,
                          ),
                        ),
                        Container(
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(5.0),
                            color: colorScheme.surfaceContainer.withAlpha(225),
                            border: Border.all(
                              color: Colors.black.withAlpha(125),
                            ),
                          ),
                          child: Padding(
                            padding: const EdgeInsets.all(2.0),
                            child: Text(
                              '(Total: ${tables.length})',
                              style: textTheme.labelMedium!.copyWith(
                                color: colorScheme.onSurfaceVariant.withAlpha(
                                  225,
                                ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),

                Padding(
                  padding: const EdgeInsets.fromLTRB(10, 0, 10, 0),
                  child: Divider(),
                ),

                ClipRect(
                  child: ConstrainedBox(
                    constraints: BoxConstraints(
                      maxHeight: _isExpanded ? _maxHeight : 0,
                    ),
                    child: Column(
                      children: [
                        Builder(
                          builder: (context) {
                            if (tables.isEmpty) {
                              return Padding(
                                padding: const EdgeInsets.fromLTRB(
                                  10,
                                  0,
                                  10,
                                  0,
                                ),
                                child: Container(
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(5.0),
                                    color: colorScheme.errorContainer.withAlpha(
                                      100,
                                    ),
                                    border: Border.all(
                                      width: 0.5,
                                      color: colorScheme.error.withAlpha(100),
                                    ),
                                  ),
                                  child: Padding(
                                    padding: const EdgeInsets.all(4.0),
                                    child: Text(
                                      'No Slab Scheme Data',
                                      style: textTheme.labelMedium!.copyWith(
                                        color: colorScheme.error,
                                      ),
                                    ),
                                  ),
                                ),
                              );
                            } else {
                              return SizedBox(height: 0, width: 0);
                            }
                          },
                        ),

                        Flexible(
                          child: Scrollbar(
                            controller: _scrollController,
                            thumbVisibility: true,
                            child: DefaultTextStyle(
                              style: textTheme.bodySmall!.copyWith(
                                color: colorScheme.onSurface,
                              ),
                              child: ListView.builder(
                                controller: _scrollController,
                                itemCount: tables.length,
                                itemBuilder: (context, index) {
                                  late Widget slabStatus;
                                  RegExp checker1 = RegExp(
                                    r'fail',
                                    caseSensitive: false,
                                  );
                                  final calsLogMap = extractCalsLog(
                                    tables[index].calsLog,
                                  );

                                  final bool designStatus =
                                      !checker1.hasMatch(
                                        tables[index].calsLog,
                                      ) ;

                                  final int containerOpacity = 175;
                                  final int textOpacity = 255;

                                  if (designStatus) {
                                    slabStatus = Container(
                                      decoration: BoxDecoration(
                                        borderRadius: BorderRadius.circular(
                                          4.0,
                                        ),
                                        color: colorScheme.primary.withAlpha(
                                          containerOpacity,
                                        ),
                                        border: Border.all(
                                          width: 0.5,
                                          color: colorScheme.primary.withAlpha(
                                            containerOpacity,
                                          ),
                                        ),
                                      ),
                                      child: Padding(
                                        padding: const EdgeInsets.all(4.0),
                                        child: Text(
                                          'Pass',
                                          style: textTheme.bodySmall!.copyWith(
                                            color: colorScheme.onPrimary
                                                .withAlpha(textOpacity),
                                          ),
                                        ),
                                      ),
                                    );
                                  } else {
                                    slabStatus = Container(
                                      decoration: BoxDecoration(
                                        borderRadius: BorderRadius.circular(
                                          4.0,
                                        ),
                                        color: colorScheme.error.withAlpha(
                                          containerOpacity,
                                        ),
                                        border: Border.all(
                                          width: 0.5,
                                          color: colorScheme.onError.withAlpha(
                                            containerOpacity,
                                          ),
                                        ),
                                      ),
                                      child: Padding(
                                        padding: const EdgeInsets.all(4.0),
                                        child: Text(
                                          'Fail',
                                          style: textTheme.bodySmall!.copyWith(
                                            color: colorScheme.onError
                                                .withAlpha(textOpacity),
                                          ),
                                        ),
                                      ),
                                    );
                                  }

                                  late List<String> unit;
                                  switch (data.unit) {
                                    case 'metrics':
                                      unit = PreferredUnit.metrics;
                                      break;
                                    case 'imperial':
                                      unit = PreferredUnit.imperial;
                                      break;
                                    default:
                                      unit = PreferredUnit.metrics;
                                  }

                                  final sdl =
                                      tables[index].service +
                                      tables[index].finish /
                                          1000 *
                                          data.finishUnitWeight;

                                  return Padding(
                                    padding: const EdgeInsets.fromLTRB(
                                      8.0,
                                      4.0,
                                      8.0,
                                      4.0,
                                    ),
                                    child: Column(
                                      children: [
                                        Row(
                                          mainAxisSize: MainAxisSize.min,
                                          children: [
                                            Column(
                                              children: [
                                                FunctionButton(
                                                  labelText: ' ${index + 1} ',
                                                  pressedColor: colorScheme
                                                      .tertiary
                                                      .withAlpha(150),
                                                ),

                                                SizedBox(height: 5.0),
                                                SelectionButton(
                                                  labelIcon: Icon(
                                                    Icons.check,
                                                    color:
                                                        tables[index].isSelected
                                                            ? colorScheme
                                                                .onTertiary
                                                            : colorScheme
                                                                .onSurface
                                                                .withAlpha(100),
                                                  ),
                                                  labelText: '',
                                                  // pressedColor:
                                                  //     colorScheme.tertiary,
                                                  bgColor:
                                                      tables[index].isSelected
                                                          ? colorScheme.tertiary
                                                          : colorScheme
                                                              .surfaceContainer
                                                              .withAlpha(100),

                                                  onTap: (value) async {
                                                    final slabSchemes =
                                                        await ref.read(
                                                          slabSchemeDataControllerProvider
                                                              .future,
                                                        );
                                                    await ref
                                                        .read(
                                                          slabSchemeDataControllerProvider
                                                              .notifier,
                                                        )
                                                        .toggleSelectScheme(
                                                          slabSchemes[index]
                                                              .slabSchemeId,
                                                        );

                                                    final newSlabSchemes =
                                                        await ref.read(
                                                          slabSchemeDataControllerProvider
                                                              .future,
                                                        );
                                                    if (newSlabSchemes[index]
                                                        .isSelected) {
                                                      final removedTable =
                                                          newSlabSchemes
                                                              .removeAt(index);
                                                      newSlabSchemes.insert(
                                                        0,
                                                        removedTable,
                                                      );
                                                      await ref
                                                          .read(
                                                            slabSchemeDataControllerProvider
                                                                .notifier,
                                                          )
                                                          .replaceEntireTable(
                                                            newSlabSchemes,
                                                          );
                                                      if (mounted) {
                                                        showSlidingFadingMessage(
                                                          context,
                                                          'Selected slab scheme  (original index: ${index + 1}) pushed to top',
                                                        );
                                                      }
                                                    }
                                                  },
                                                ),
                                                SizedBox(height: 5.0),
                                                FunctionButton(
                                                  key: ValueKey(
                                                    'slabSchemeId_${tables[index].slabSchemeId}',
                                                  ),
                                                  labelText: 'Show Cals',
                                                  pressedColor: colorScheme
                                                      .tertiary
                                                      .withAlpha(150),
                                                  onTap: (value) async {
                                                    final globalData = await ref
                                                        .read(
                                                          globalDataControllerProvider
                                                              .future,
                                                        );
                                                    _presentCalsRecord(
                                                      globalData,
                                                      unit,
                                                      tables,
                                                      index,
                                                    );
                                                  },
                                                ),
                                              ],
                                            ),

                                            IconButton(
                                              icon: Icon(
                                                Icons.delete,
                                                color: colorScheme.onSurface,
                                              ),
                                              onPressed: () {
                                                ref
                                                    .read(
                                                      slabSchemeDataControllerProvider
                                                          .notifier,
                                                    )
                                                    .deleteTable(
                                                      tables[index]
                                                          .slabSchemeId,
                                                    );
                                              },
                                            ),
                                            Flexible(
                                              child: DefaultTextStyle(
                                                style: bodyStyle,
                                                child: Row(
                                                  children: [
                                                    Column(
                                                      children: [slabStatus],
                                                    ),
                                                    SizedBox(width: 10.0),
                                                    Column(
                                                      mainAxisSize:
                                                          MainAxisSize.min,
                                                      crossAxisAlignment:
                                                          CrossAxisAlignment
                                                              .start,
                                                      children: [
                                                        Text(
                                                          'Usage :',
                                                          style: subtitleStyle,
                                                        ),
                                                        Text(
                                                          'Finish [${unit[4]}]:',
                                                          style: subtitleStyle,
                                                        ),

                                                        Text(
                                                          'SDL [${unit[1]}]:',
                                                          style: subtitleStyle,
                                                        ),

                                                        Text(
                                                          'LL [${unit[1]}]:',
                                                          style: subtitleStyle,
                                                        ),
                                                        SizedBox(height: 5.0),
                                                        Text(
                                                          'Span [${unit[3]}]:',
                                                          style: subtitleStyle,
                                                        ),
                                                        Text(
                                                          'Depth [${unit[3]}]:',
                                                          style: subtitleStyle,
                                                        ),
                                                        SizedBox(height: 5.0),
                                                        Text(
                                                          'Top Bar (Compression): ',
                                                          style: subtitleStyle,
                                                        ),
                                                        Text(
                                                          'Bottom Bar (Tension): ',
                                                          style: subtitleStyle,
                                                        ),
                                                        Text(
                                                          'Links: ',
                                                          style: subtitleStyle,
                                                        ),
                                                      ],
                                                    ),
                                                    SizedBox(width: 10.0),
                                                    Column(
                                                      mainAxisSize:
                                                          MainAxisSize.min,
                                                      crossAxisAlignment:
                                                          CrossAxisAlignment
                                                              .start,
                                                      children: [
                                                        Text(
                                                          tables[index].usage,
                                                        ),
                                                        Text(
                                                          NumberFormat(
                                                            '0',
                                                          ).format(
                                                            tables[index]
                                                                .finish,
                                                          ),
                                                        ),

                                                        Text(
                                                          NumberFormat(
                                                            '0',
                                                          ).format(sdl),
                                                        ),
                                                        Text(
                                                          NumberFormat(
                                                            '0',
                                                          ).format(
                                                            tables[index]
                                                                .liveLoad,
                                                          ),
                                                        ),
                                                        SizedBox(height: 5.0),
                                                        Text(
                                                          NumberFormat(
                                                            '0.0',
                                                          ).format(
                                                            tables[index].span,
                                                          ),
                                                        ),
                                                        Text(
                                                          NumberFormat(
                                                            '0',
                                                          ).format(
                                                            tables[index]
                                                                .strZone,
                                                          ),
                                                        ),
                                                        SizedBox(height: 5.0),
                                                        Text(
                                                          tables[index]
                                                              .mainTopBar,
                                                        ),
                                                        Text(
                                                          tables[index]
                                                              .mainBottomBar,
                                                        ),
                                                        Text(
                                                          tables[index]
                                                              .mainLinks,
                                                        ),
                                                      ],
                                                    ),
                                                    SizedBox(width: 5.0),

                                                    GestureDetector(
                                                      onTap: () async {
                                                        await showDialog(
                                                          context: context,
                                                          builder: (context) {
                                                            return Dialog(
                                                              backgroundColor:
                                                                  colorScheme
                                                                      .surfaceContainer,
                                                              child: SizedBox(
                                                                width: 550,
                                                                height: 550,
                                                                child: DrawSlabSchemeSketch(
                                                                  sketchWidth:
                                                                      500,
                                                                  sketchHeight:
                                                                      500,
                                                                  index: index,
                                                                  // fontSize: 9.0,
                                                                ),
                                                              ),
                                                            );
                                                          },
                                                        );
                                                      },
                                                      child:
                                                          DrawSlabSchemeSketch(
                                                            sketchWidth: 150,
                                                            sketchHeight: 150,
                                                            index: index,
                                                          ),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                            ),
                                            SizedBox(width: 5.0),
                                          ],
                                        ),
                                        Padding(
                                          padding: const EdgeInsets.fromLTRB(
                                            10,
                                            0,
                                            10,
                                            0,
                                          ),
                                          child: Divider(
                                            thickness: 0.2,
                                            color: colorScheme.primary,
                                          ),
                                        ),
                                      ],
                                    ),
                                  );
                                },
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            );
          },
          error: (error, stackTrace) => Text(error.toString()),
          loading: () => CircularProgressIndicator(),
        );
      },
      error: (error, stackTrace) => Text(error.toString()),
      loading: () => CircularProgressIndicator(),
    );
  }

  Future<void> _presentCalsRecord(
    GlobalData globalData,
    List<String> unit,
    List<SlabSchemeData> tables,
    int index,
  ) async {
    final result = extractCalsLog(tables[index].calsLog);
    // 1. handle secondary beam result first
    final slabCals = _writeSlabResult(
      globalData,
      result, // input the main beam result
      tables,
      index,
      unit,
      presentOption: CalsPresentOptions.main,
    );

    await showDialog(
      context: context,
      builder: (context) {
        ColorScheme colorScheme = Theme.of(context).colorScheme;
        TextTheme textTheme = Theme.of(context).textTheme;
        Color bgColor = colorScheme.surfaceContainer;
        Color onBgColor = colorScheme.onSurface;
        Color titleBgColor = colorScheme.primary.withAlpha(150);
        Color titleOnBgColor = colorScheme.onPrimary;
        TextStyle titleTextStyle = textTheme.labelSmall!.copyWith(
          color: titleOnBgColor,
        );
        ScrollController scrollController = ScrollController();

        late List<String> unit;
        switch (globalData.unit) {
          case 'metrics':
            unit = PreferredUnit.metrics;
            break;
          case 'imperial':
            unit = PreferredUnit.imperial;
            break;
          default:
            unit = PreferredUnit.metrics;
        }
        const double maxH = 550;
        const double maxW = 400;

        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(15),
          ),
          backgroundColor: bgColor,
          child: ConstrainedBox(
            constraints: const BoxConstraints(maxWidth: maxW, maxHeight: maxH),
            child: Column(
              children: [
                Align(
                  alignment: Alignment.centerRight,
                  child: Padding(
                    padding: const EdgeInsets.fromLTRB(0, 10, 10, 0),
                    child: FunctionButton(
                      bgColor: titleBgColor,
                      labelTextColor: titleOnBgColor,
                      labelIcon: Icon(
                        Icons.print_outlined,
                        color: titleOnBgColor.withAlpha(175),
                      ),
                      labelText: '',
                      onTap: (value) {
                        exportListToPdf(
                          context,
                          ref,
                          slabCals.toString().split('\n'),
                        );
                      },
                    ),
                  ),
                ),

                Scrollbar(
                  controller: scrollController,
                  thumbVisibility: true,
                  trackVisibility: true,
                  child: ConstrainedBox(
                    constraints: const BoxConstraints(
                      maxWidth: maxW,
                      maxHeight: maxH - 70,
                    ),
                    child: SingleChildScrollView(
                      controller: scrollController,
                      child: Padding(
                        padding: const EdgeInsets.fromLTRB(25.0, 5, 25.0, 5),
                        child: DefaultTextStyle(
                          style: textTheme.labelMedium!.copyWith(
                            color: onBgColor,
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              Container(
                                decoration: BoxDecoration(
                                  color: colorScheme.tertiary,
                                  shape: BoxShape.circle,
                                  border: Border.all(color: titleOnBgColor),
                                ),
                                child: Padding(
                                  padding: const EdgeInsets.all(12.0),
                                  child: Text(
                                    '${index + 1}',
                                    style: titleTextStyle.copyWith(
                                      fontSize: 24,
                                      color: colorScheme.onTertiary,
                                    ),
                                  ),
                                ),
                              ),
                              SizedBox(width: 10),
                              Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Container(
                                    decoration: BoxDecoration(
                                      color: titleBgColor,
                                      shape: BoxShape.rectangle,
                                      borderRadius: BorderRadius.circular(10.0),
                                      border: Border.all(color: titleOnBgColor),
                                    ),
                                    child: Padding(
                                      padding: const EdgeInsets.all(4.0),
                                      child: Text(
                                        'Usage:\n${tables[index].usage}',
                                        style: titleTextStyle,
                                      ),
                                    ),
                                  ),
                                  SizedBox(width: 5),
                                  Container(
                                    decoration: BoxDecoration(
                                      color: titleBgColor,
                                      shape: BoxShape.rectangle,
                                      borderRadius: BorderRadius.circular(10.0),
                                      border: Border.all(color: titleOnBgColor),
                                    ),
                                    child: Padding(
                                      padding: const EdgeInsets.all(4.0),
                                      child: Text(
                                        'Finish [${unit[4]}]:\n${tables[index].finish} ',
                                        style: titleTextStyle,
                                      ),
                                    ),
                                  ),
                                  SizedBox(width: 5),
                                  Container(
                                    decoration: BoxDecoration(
                                      color: titleBgColor,
                                      shape: BoxShape.rectangle,
                                      borderRadius: BorderRadius.circular(10.0),
                                      border: Border.all(color: titleOnBgColor),
                                    ),
                                    child: Padding(
                                      padding: const EdgeInsets.all(4.0),
                                      child: Text(
                                        'Service [${unit[1]}]:\n${tables[index].service} ',
                                        style: titleTextStyle,
                                      ),
                                    ),
                                  ),
                                  SizedBox(width: 5),
                                  Container(
                                    decoration: BoxDecoration(
                                      color: titleBgColor,
                                      shape: BoxShape.rectangle,
                                      borderRadius: BorderRadius.circular(10.0),
                                      border: Border.all(color: titleOnBgColor),
                                    ),
                                    child: Padding(
                                      padding: const EdgeInsets.all(4.0),
                                      child: Text(
                                        'Live Load [${unit[1]}]:\n${tables[index].liveLoad}',
                                        style: titleTextStyle,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                              SizedBox(height: 15),
                              Container(
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(10.0),
                                  border: Border.all(color: onBgColor),
                                ),
                                child: Padding(
                                  padding: const EdgeInsets.all(8.0),
                                  child: Text(slabCals.toString()),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  StringBuffer _writeSlabResult(
    GlobalData globalData,
    Map<String, List<dynamic>> result,
    List<SlabSchemeData> tables,
    int index,
    List<String> unit, {
    String presentOption = CalsPresentOptions.secondary,
  }) {
    StringBuffer buffer = StringBuffer();
    double tempDouble = 0.0, tempDouble2 = 0.0, tempDouble3 = 0.0;
    NumberFormat f0 = NumberFormat('0'),
        f1 = NumberFormat('0.0'),
        f3 = NumberFormat('0.000');
    String tempString = '';

    buffer.write('------------------------------\n');
    tempDouble = getDoubleValue(result, 'depth');
    buffer.write('Slab ');
    buffer.write('(${f0.format(tempDouble)} thk)\n');
    buffer.write('------------------------------\n');
    buffer.write('*******************\n');
    buffer.write('Loading\n');
    buffer.write('*******************\n');

    buffer.write('SW\n=');
    buffer.write(
      '${globalData.rcUnitWeight}x${getDoubleValue(result, 'Slab Thickness') / 1000}',
    );

    tempDouble = getPressureFromThick(
      getDoubleValue(result, 'Slab Thickness'),
      globalData.rcUnitWeight,
    );
    buffer.write('\n= ${f1.format(tempDouble)} [${unit[0]}/${unit[3]}]\n');

    buffer.write('SDL\n=');
    buffer.write('${getDoubleValue(result, 'SDL')}x1\n');
    buffer.write(
      '= ${f1.format(getDoubleValue(result, 'SDL'))} [${unit[0]}/${unit[3]}]\n',
    );

    buffer.write('LL\n=');
    buffer.write('${getDoubleValue(result, 'LL')}x1\n');
    buffer.write(
      '= ${f1.format(getDoubleValue(result, 'LL'))} [${unit[0]}/${unit[3]}]\n',
    );

    buffer.write('SLS = ');
    buffer.write(
      '${f1.format(getDoubleValue(result, 'SLS'))} [${unit[0]}/${unit[3]}]\n',
    );

    buffer.write('ULS = ');
    buffer.write(
      '${f1.format(getDoubleValue(result, 'ULS'))} [${unit[0]}/${unit[3]}]\n',
    );

    buffer.write('Span = ');
    buffer.write('${f1.format(getDoubleValue(result, 'Span'))} [${unit[3]}]\n');

    buffer.write('M\n= wL^2/8\n= ');
    buffer.write('${f0.format(getDoubleValue(result, 'M'))} [${unit[2]}]\n');

    buffer.write('V\n= wL/2\n= ');
    buffer.write('${f0.format(getDoubleValue(result, 'V'))} [${unit[0]}]\n');

    buffer.write('*******************\n');
    buffer.write('Check moement\n');
    buffer.write('*******************\n');

    buffer.write('d = ');
    buffer.write('${f0.format(getDoubleValue(result, 'd'))} [${unit[4]}]\n');

    buffer.write('d_c = ');
    buffer.write('${f0.format(getDoubleValue(result, 'd_c'))} [${unit[4]}]\n');

    buffer.write('k = ');
    buffer.write('${f3.format(getDoubleValue(result, 'k'))} \n');

    buffer.write('z = ');
    buffer.write('${f0.format(getDoubleValue(result, 'z'))} [${unit[4]}]\n');

    buffer.write('As_t,req = ');
    buffer.write('${f0.format(getDoubleValue(result, 'As_t'))} [${unit[6]}]\n');

    buffer.write('As_t,pro');
    tempString = getStringValue(result, 'As_t_pro Designation');

    if (tempString != 'null') {
      buffer.write(' = $tempString');
      tempDouble = getDoubleValue(result, 'As_t_pro');
      buffer.write(' = ${f0.format(tempDouble)} [${unit[6]}]\n');
    } else {
      buffer.write(' = $tempString\n');
    }

    buffer.write('As_c,req = ');
    buffer.write('${f0.format(getDoubleValue(result, 'As_c'))} [${unit[6]}]\n');

    buffer.write('As_c,pro');
    tempString = getStringValue(result, 'As_c_pro Designation');

    if (tempString != 'null') {
      buffer.write(' = $tempString');
      tempDouble = getDoubleValue(result, 'As_c_pro');
      buffer.write(' = ${f0.format(tempDouble)} [${unit[6]}]\n');
    } else {
      buffer.write(' = $tempString\n');
    }

    buffer.write('*******************\n');
    buffer.write('Check shear\n');
    buffer.write('*******************\n');

    buffer.write('v = ');
    buffer.write('${f3.format(getDoubleValue(result, 'v_d'))} [${unit[5]}]\n');

    buffer.write('v_r = ');
    buffer.write('${f3.format(getDoubleValue(result, 'v_r'))} [${unit[5]}]\n');

    buffer.write('v_c = ');
    buffer.write('${f3.format(getDoubleValue(result, 'v_c'))} [${unit[5]}]\n');

    buffer.write('Asv_req');
    if (getDoubleValue(result, 'l_req') > 0.0) {
      buffer.write(
        ' = ${getDoubleValue(result, 'l_req')} [${unit[6]}/${unit[4]}]\n',
      );
    } else {
      buffer.write(' = N.A.');
      buffer.write(
        ' = ${getDoubleValue(result, 'l_req')} [${unit[6]}/${unit[4]}]\n',
      );
    }

    buffer.write('Asv_pro');
    tempString = getStringValue(result, 'l_pro Designation');
    if (tempString != 'null') {
      buffer.write(' = $tempString');
      buffer.write(
        ' = ${getDoubleValue(result, 'l_pro')} [${unit[6]}/${unit[4]}]\n',
      );
    } else {
      buffer.write(' = $tempString\n');
    }
    buffer.write('*******************\n');
    buffer.write('Check Deflection\n');
    buffer.write('*******************\n');
    buffer.write(
      'SLS Load, w = ${f1.format(getDoubleValue(result, 'SLS'))} [${unit[0]}/${unit[3]}]\n',
    );
    buffer.write(
      'E = ${f0.format(getElasticModulus(tables[index].fcu))} [${unit[5]}]\n',
    );
    buffer.write(
      'I = ${f0.format(getSecondAreaMomentRectangle(getDoubleValue(result, 'width'), getDoubleValue(result, 'depth')) * pow(10, -4))} [${unit[8]}]\n',
    );
    buffer.write(
      'Max Deflection = (5/384)(wL^4)/(EI) = ${f3.format(getDoubleValue(result, 'Max Deflection'))} [${unit[4]}]\n',
    );
    buffer.write(
      'Limit = L/250 = ${f3.format(getDoubleValue(result, 'Deflection Limit'))} [${unit[4]}] ',
    );
    if (getDoubleValue(result, 'Max Deflection') <
        getDoubleValue(result, 'Deflection Limit')) {
      buffer.write('(OK)\n');
    } else {
      buffer.write('(Fail)\n');
    }

    //* log any error / warnings
    List<String> errors = [], warnings = [];
    RegExp failReg = RegExp(r'fail', caseSensitive: false);
    if (failReg.hasMatch(buffer.toString())) {
      errors.add('Result not reliable. Something fails.');
      warnings.add('1. Consider to adjust the slab span (2nd beam spacing)');
      warnings.add('2. Consider to adjust the slab str zone (if possible)');
      warnings.add('3. Consider to adjust preferred k-value');
    }

    if (failReg.hasMatch(getStringValue(result, 'As_t_pro Designation'))) {
      errors.add('Tension bar fails.');
    }
    if (failReg.hasMatch(getStringValue(result, 'As_c_pro Designation'))) {
      errors.add('Compression bar fails.');
    }
    if (failReg.hasMatch(getStringValue(result, 'l_pro Designation'))) {
      errors.add('Links fails.');
    }

    if (warnings.isNotEmpty) {
      buffer = addWarningHeader(buffer, warnings);
    }
    if (errors.isNotEmpty) {
      buffer = addErrorHeader(buffer, errors);
    }

    return buffer;
  }

  // Method to update heights based on the current media query
  void _updateHeights() {
    final double screenHeight = MediaQuery.of(context).size.height;
    _maxHeight =
        (widget.maxHeight == null)
            ? screenHeight * 0.65
            : (widget.maxHeight! > screenHeight * 0.65)
            ? screenHeight * 0.65
            : widget.maxHeight!;

    _minHeight =
        (widget.minHeight == null)
            ? screenHeight * 0.1
            : (widget.minHeight! > screenHeight * 0.1)
            ? screenHeight * 0.1
            : widget.minHeight!;

    // Adjust _maxHeight if needed

    if (_maxHeight < _minHeight) {
      _maxHeight = _minHeight;
    }
  }
}
