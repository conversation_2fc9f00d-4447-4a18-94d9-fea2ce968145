// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'slab_scheme_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_SlabSchemeData _$SlabSchemeDataFromJson(Map<String, dynamic> json) =>
    _SlabSchemeData(
      usage: json['usage'] as String? ?? '',
      finish: (json['finish'] as num?)?.toDouble() ?? 0.0,
      service: (json['service'] as num?)?.toDouble() ?? 0.0,
      liveLoad: (json['liveLoad'] as num?)?.toDouble() ?? 0.0,
      loadingTableId: json['loadingTableId'] as String? ?? '',
      span: (json['span'] as num?)?.toDouble() ?? 5.0,
      strZone: (json['strZone'] as num?)?.toDouble() ?? 0.0,
      fcu: (json['fcu'] as num?)?.toDouble() ?? 45.0,
      cover: (json['cover'] as num?)?.toDouble() ?? 35.0,
      mainTopBar: json['mainTopBar'] as String? ?? '',
      mainBottomBar: json['mainBottomBar'] as String? ?? '',
      mainLinks: json['mainLinks'] as String? ?? '',
      slabSchemeId: json['slabSchemeId'] as String? ?? '',
      calsLog: json['calsLog'] as String? ?? '',
      isSelected: json['isSelected'] as bool? ?? false,
    );

Map<String, dynamic> _$SlabSchemeDataToJson(_SlabSchemeData instance) =>
    <String, dynamic>{
      'usage': instance.usage,
      'finish': instance.finish,
      'service': instance.service,
      'liveLoad': instance.liveLoad,
      'loadingTableId': instance.loadingTableId,
      'span': instance.span,
      'strZone': instance.strZone,
      'fcu': instance.fcu,
      'cover': instance.cover,
      'mainTopBar': instance.mainTopBar,
      'mainBottomBar': instance.mainBottomBar,
      'mainLinks': instance.mainLinks,
      'slabSchemeId': instance.slabSchemeId,
      'calsLog': instance.calsLog,
      'isSelected': instance.isSelected,
    };
