import 'package:nanoid2/nanoid2.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

//presentation layer
import '../../domain_layer/column_scheme_data.dart';
import '../../domain_layer/transfer_beam_scheme_input.dart';
import '../screen/homescreen.dart';

// domain layer

part 'transfer_beam_scheme_input_controller.g.dart';

@riverpod
class TransferBeamSchemeInputController
    extends _$TransferBeamSchemeInputController {
  @override
  FutureOr<List<TransferBeamSchemeInput>> build() async {
    // print('Build: Transfer Beam Scheme Input');
    final List<TransferBeamSchemeInput> transferBeamSchemeInputs =
        await ref
            .read(appDatabaseControllerProvider.notifier)
            .queryTransferBeamSchemeInput();
    final data1 = ref.watch(columnSchemeDataControllerProvider);
    return data1.when(
      data: (colData) async {
        final List<TransferBeamSchemeInput> finalList = [];
        //* Validate the load (if using column load)
        await Future.forEach(transferBeamSchemeInputs, (input) async {
          if (input.loadFromSelectedCol) {
            final selectedCol = colData.firstWhere(
              (scheme) => scheme.isSelected,
              orElse: () => ColumnSchemeData(),
            );
            input = input.copyWith(pointLoad: selectedCol.ulsLoad);
          }
          finalList.add(input);
        });
        return finalList;
      },
      error: (error, stackTrace) => [],
      loading: () => [],
    );
  }

  // final globalData =
  //     ref.read(appDatabaseControllerProvider.notifier).queryGlobalData();
  // return globalData;

  Future<void> addEmptytable() async {
    final x = await future;
    final id = await _generateTaskID();
    state = AsyncData([
      ...x,
      TransferBeamSchemeInput(transferBeamSchemeInputId: id),
    ]);
  }

  Future<void> deleteTable(String id) async {
    // print("Before deletion: ${state.map((item) => item.usage).toList()}");
    final x = await future;
    x.removeWhere((item) => item.transferBeamSchemeInputId == id);
    // print("After deletion: ${x.map((item) => item.usage).toList()}");
    state = AsyncData(x);
  }

  Future<void> deleteAllTable() async {
    // print("Before deletion: ${state.map((item) => item.usage).toList()}");
    state = AsyncData(<TransferBeamSchemeInput>[]);
  }

  Future<void> insertEmptyTable(int index) async {
    final x = await future;
    final id = await _generateTaskID();
    x.insert(index + 1, TransferBeamSchemeInput(transferBeamSchemeInputId: id));
    state = AsyncData(x);
  }

  Future<void> copyAndInsertTable(int index) async {
    final x = await future;
    final id = await _generateTaskID();
    x.insert(index + 1, x[index].copyWith(transferBeamSchemeInputId: id));
    state = AsyncData(x);
  }

  Future<String> _generateTaskID() async {
    String newID = nanoid(alphabet: Alphabet.noDoppelgangerSafe);
    final x = await future;
    final Set<String> transferBeamSchemeInputIds =
        x.map((item) => item.transferBeamSchemeInputId).toSet();
    // final Set<String> taskIDs = ref.read(_taskIDsProvider);
    while (transferBeamSchemeInputIds.contains(newID)) {
      newID = nanoid(alphabet: Alphabet.noDoppelgangerSafe);
    }
    return newID;
  }

  Future<void> updateTable({
    double? pointLoad,
    double? distA,
    bool? loadFromSelectedCol,
    required String transferBeamSchemeInputId,
  }) async {
    final x = await future;
    List<TransferBeamSchemeInput> finalList = [];
    late TransferBeamSchemeInput data;
    // Create a list of updates
    await Future.forEach((x), (x1) {
      if (x1.transferBeamSchemeInputId == transferBeamSchemeInputId) {
        data = x1.copyWith(
          pointLoad: pointLoad ?? x1.pointLoad,
          distA: distA ?? x1.distA,
          loadFromSelectedCol: loadFromSelectedCol ?? x1.loadFromSelectedCol,
          transferBeamSchemeInputId: transferBeamSchemeInputId,
        );
      } else {
        data = x1;
      }
      finalList.add(data);
    });
    // Update the state only if there are changes
    state = AsyncData(finalList);
  }
}
