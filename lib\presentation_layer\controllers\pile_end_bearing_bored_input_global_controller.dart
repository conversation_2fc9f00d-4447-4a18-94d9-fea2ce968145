import 'package:riverpod_annotation/riverpod_annotation.dart';

//presentation layer
import '../../domain_layer/column_scheme_data.dart';
import '../../domain_layer/pile_end_bearing_bored_input.dart';
import '../../domain_layer/pile_end_bearing_bored_input_global.dart';
import '../screen/homescreen.dart';

// domain layer

part 'pile_end_bearing_bored_input_global_controller.g.dart';

@riverpod
class PileEndBearingBoredInputGlobalController
    extends _$PileEndBearingBoredInputGlobalController {
  @override
  FutureOr<PileEndBearingBoredInputGlobal> build() async {
    // print('Build: Column Scheme Input Global');
    PileEndBearingBoredInputGlobal pileEndBearingBoredInputGlobal =
        await ref
            .read(appDatabaseControllerProvider.notifier)
            . queryPileEndBearingBoredInputGlobal();

    return pileEndBearingBoredInputGlobal;
  }

  Future<void> updateTable({
    double? colLoadFactor,
  }) async {
    final x = await future;
     PileEndBearingBoredInputGlobal newState = x.copyWith(
      colLoadFactor: colLoadFactor ?? x.colLoadFactor,
      id: '1',
    );
    // Update the state only if there are changes
    state = AsyncData(newState);
  }
}
