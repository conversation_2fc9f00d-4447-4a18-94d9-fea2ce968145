import 'package:nanoid2/nanoid2.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

//presentation layer
import '../../domain_layer/steel_transfer_truss_scheme_input.dart';
import '../screen/homescreen.dart';

// domain layer

part 'steel_transfer_truss_scheme_input_controller.g.dart';

@riverpod
class SteelTransferTrussSchemeInputController
    extends _$SteelTransferTrussSchemeInputController {
  @override
  FutureOr<List<SteelTransferTrussSchemeInput>> build() async {
    // print('Build: Transfer Beam Scheme Input');
    final List<SteelTransferTrussSchemeInput> steelTransferTrussSchemeInputs =
        await ref
            .read(appDatabaseControllerProvider.notifier)
            .querySteelTransferTrussSchemeInput();
    if (steelTransferTrussSchemeInputs.isEmpty) {
      String newID = nanoid(alphabet: Alphabet.noDoppelgangerSafe);
      steelTransferTrussSchemeInputs.add(
        SteelTransferTrussSchemeInput(steelTransferTrussSchemeInputId: newID),
      );
    }
    return steelTransferTrussSchemeInputs;
  }

  // final globalData =
  //     ref.read(appDatabaseControllerProvider.notifier).queryGlobalData();
  // return globalData;

  Future<void> addEmptytable() async {
    final x = await future;
    final id = await _generateTaskID();
    state = AsyncData([
      ...x,
      SteelTransferTrussSchemeInput(steelTransferTrussSchemeInputId: id),
    ]);
  }

  Future<void> deleteTable(String id) async {
    // print("Before deletion: ${state.map((item) => item.usage).toList()}");
    final x = await future;
    x.removeWhere((item) => item.steelTransferTrussSchemeInputId == id);
    // print("After deletion: ${x.map((item) => item.usage).toList()}");
    state = AsyncData(x);
  }

  Future<void> deleteAllTable() async {
    // print("Before deletion: ${state.map((item) => item.usage).toList()}");
    state = AsyncData(<SteelTransferTrussSchemeInput>[]);
  }

  Future<void> insertEmptyTable(int index) async {
    final x = await future;
    final id = await _generateTaskID();
    x.insert(index + 1, SteelTransferTrussSchemeInput(steelTransferTrussSchemeInputId: id));
    state = AsyncData(x);
  }

  Future<void> copyAndInsertTable(int index) async {
    final x = await future;
    final id = await _generateTaskID();
    x.insert(index + 1, x[index].copyWith(steelTransferTrussSchemeInputId: id));
    state = AsyncData(x);
  }

  Future<String> _generateTaskID() async {
    String newID = nanoid(alphabet: Alphabet.noDoppelgangerSafe);
    final x = await future;
    final Set<String> transferBeamSchemeInputIds =
        x.map((item) => item.steelTransferTrussSchemeInputId).toSet();
    // final Set<String> taskIDs = ref.read(_taskIDsProvider);
    while (transferBeamSchemeInputIds.contains(newID)) {
      newID = nanoid(alphabet: Alphabet.noDoppelgangerSafe);
    }
    return newID;
  }

  Future<void> updateTable({
    double? pointLoad,
    double? distA,
    required String steelTransferTrussSchemeInputId,
  }) async {
    final x = await future;
    List<SteelTransferTrussSchemeInput> finalList = [];
    late SteelTransferTrussSchemeInput data;
    // Create a list of updates
    await Future.forEach((x), (x1) {
      if (x1.steelTransferTrussSchemeInputId == steelTransferTrussSchemeInputId) {
        data = x1.copyWith(
          pointLoad: pointLoad ?? x1.pointLoad,
          distA: distA ?? x1.distA,
          steelTransferTrussSchemeInputId: steelTransferTrussSchemeInputId,
        );
      } else {
        data = x1;
      }
      finalList.add(data);
    });
    // Update the state only if there are changes
    state = AsyncData(finalList);
  }
}
