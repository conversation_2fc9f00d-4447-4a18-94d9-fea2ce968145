import 'package:freezed_annotation/freezed_annotation.dart';
// import 'package:flutter/foundation.dart';
part 'slab_scheme_input.freezed.dart';
part 'slab_scheme_input.g.dart';

@freezed
abstract class SlabSchemeInput with _$SlabSchemeInput {
  const SlabSchemeInput._();
  factory SlabSchemeInput({
    @Default('1') String id,
    @Default(5.0) double span,
    @Default(45.0) double fcu,
    @Default(35.0) double cover,
    @Default(0.156) double mainKValue,
    @Default(0.04) double mainSteelRatio,
    @Default(100) int minS,
    @Default(300) int maxS,
    @Default(500) double maxDepth,
    @Default(150.0) double minDepth,
    @Default(2) int maxLayers,
    @Default(0.5) double spanIncreament,
    @Default(4) int iterationSteps,
    @Default('') String usage,
  }) = _SlabSchemeInput;

  factory SlabSchemeInput.fromJson(Map<String, Object?> json) =>
      _$SlabSchemeInputFromJson(json);
}
