import 'package:freezed_annotation/freezed_annotation.dart';
// import 'package:flutter/foundation.dart';
part 'cantilever_scheme_input.freezed.dart';
part 'cantilever_scheme_input.g.dart';

@freezed
abstract class CantileverSchemeInput with _$CantileverSchemeInput {
  const CantileverSchemeInput._();
  factory CantileverSchemeInput({
    @Default(0.0) double pointLoad,
    @Default(3.0) double distA,
    @Default(false) bool loadFromSelectedCol,
    @Default('')
    String
    cantileverSchemeInputId, //will be overriden  as soon as new instance created
  }) = _CantileverSchemeInput;

  factory CantileverSchemeInput.fromJson(Map<String, Object?> json) =>
      _$CantileverSchemeInputFromJson(json);
}
