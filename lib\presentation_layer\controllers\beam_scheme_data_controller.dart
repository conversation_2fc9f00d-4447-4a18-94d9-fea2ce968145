import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:nanoid2/nanoid2.dart';
import 'dart:math';
import 'package:intl/intl.dart';
import 'package:structify/domain_layer/mixin/mixin_str_general_cals.dart';
import 'package:structify/domain_layer/mixin/mixin_tools_for_ui.dart';

//presentation layer
import '../../domain_layer/mixin/mixin_rc_str.dart';
import '../screen/homescreen.dart';

// domain layer
import '../../domain_layer/design_options.dart';
import '../../domain_layer/beam_scheme_input.dart';
import '../../domain_layer/beam_scheme_data.dart';
import '../../domain_layer/global_data.dart';
import '../../domain_layer/loading_table.dart';
import '../../domain_layer/preferences.dart';

part 'beam_scheme_data_controller.g.dart';

@riverpod
class BeamSchemeDataController extends _$BeamSchemeDataController
    with StrGeneralCals, RCStrHK, MixinToolsForUI {
  @override
  FutureOr<List<BeamSchemeData>> build() async {
    final beamSchemeDataList =
        await ref
            .read(appDatabaseControllerProvider.notifier)
            .queryBeamSchemeData();

    final data1 = ref.watch(globalDataControllerProvider);
    final data2 = ref.watch(loadingTablesControllerProvider);
    final data3 = ref.watch(beamSchemeInputControllerProvider);
    return data1.when(
      data: (globalData) {
        return data2.when(
          data: (loadingTables) {
            return data3.when(
              data: (inputs) async {
                return await bacthTypBayScheming(
                  existingSchemes: beamSchemeDataList,
                  calledInBuild: true,
                );
              },
              error: (error, stackTrace) => <BeamSchemeData>[],
              loading: () => <BeamSchemeData>[],
            );
          },
          error: (error, stackTrace) => <BeamSchemeData>[],
          loading: () => <BeamSchemeData>[],
        );
      },
      error: (error, stackTrace) => <BeamSchemeData>[],
      loading: () => <BeamSchemeData>[],
    );
  }

  Future<void> addBeamSchemeData(List<BeamSchemeData> beamSchemeData) async {
    final x = await future;
    state = AsyncData([...x, ...beamSchemeData]);
  }

  Future<void> deleteTable(String id) async {
    final x = await future;
    x.removeWhere((item) => item.beamSchemeId == id);
    state = AsyncData(x);
  }

  Future<List<BeamSchemeData>> deleteTablesNotSelected({
    List<BeamSchemeData>? existingScheme,
    bool? calledInBuild,
  }) async {
    late final List<BeamSchemeData> schemes;
    if (existingScheme != null && existingScheme.isNotEmpty) {
      schemes = existingScheme;
    } else {
      if (calledInBuild != null && calledInBuild) {
        schemes = [];
      } else {
        schemes = await future;
      }
    }
    schemes.removeWhere((item) => !item.isSelected);
    state = AsyncData(schemes);
    return schemes;
  }

  Future<void> deleteAllTable() async {
    state = AsyncData([]);
  }

  Future<void> replaceEntireTable(List<BeamSchemeData> newBeamSchemes) async {
    state = AsyncData(newBeamSchemes);
  }

  Future<void> toggleSelectScheme(String beamSchemeId) async {
    final x = await future;
    final newState =
        x.map((item) {
          if (item.beamSchemeId == beamSchemeId) {
            return item.copyWith(isSelected: !item.isSelected);
          } else {
            return item.copyWith(isSelected: false);
          }
        }).toList();
    state = AsyncData(newState);
  }

  Future<String> _generateUniqueId({Set<String>? existingID}) async {
    late final Set<String> beamSchemeDataId;
    if (existingID == null) {
      final x = await future;
      beamSchemeDataId = x.map((item) => item.beamSchemeId).toSet();
    } else {
      beamSchemeDataId = existingID!;
    }
    String newID = nanoid(alphabet: Alphabet.noDoppelgangerSafe);
    while (beamSchemeDataId.contains(newID)) {
      newID = nanoid(alphabet: Alphabet.noDoppelgangerSafe);
    }
    return newID;
  }

  Future<List<BeamSchemeData>> bacthTypBayScheming({
    List<BeamSchemeData>? existingSchemes,
    bool? calledInBuild,
  }) async {
    //* keep selected schemes
    final List<BeamSchemeData> finalList = [];
    late final List<BeamSchemeData> schemesLeft;
    schemesLeft = await deleteTablesNotSelected(
      existingScheme: existingSchemes,
      calledInBuild: calledInBuild,
    );
    finalList.addAll(schemesLeft);
    final existingID = existingSchemes?.map((e) => e.beamSchemeId).toSet();
    final globalData = await ref.read(globalDataControllerProvider.future);
    final loadingTables = await ref.read(
      loadingTablesControllerProvider.future,
    );
    final beamInput = await ref.read(beamSchemeInputControllerProvider.future);

    //* Loading
    final String usage = beamInput.usage;
    final LoadingTable load = loadingTables.firstWhere(
      (item) => item.usage == usage,
      orElse: () => LoadingTable(usage: 'No imposed load'),
    );
    final double sdl =
        getPressureFromThick(load.finish, globalData.finishUnitWeight) +
        load.service; // in [kPa]
    final double ll = load.liveLoad; // in [kPa]
    final double ulsLoad =
        globalData.sdlFactor * sdl + globalData.llFactor * ll; // in [kPa]

    for (var i = 0; i < beamInput.iterationSteps; i++) {
      // new input refers updated spans and bays after each iteration
      //
      final newInput = beamInput.copyWith(
        bays: beamInput.bays + beamInput.baysIncreament * i,
        shortSpan: beamInput.shortSpan + beamInput.shortSpanIncreament * i,
        longSpan: beamInput.longSpan + beamInput.longSpanIncreament * i,
      );
      final double w =
          newInput.shortSpan / newInput.bays; //  revised load width
      final double ulsUDL = ulsLoad * w; // in [kN/m]

      //* we separate two schemes for main and secondary beam
      //* then later combine info from them into variable 'result' (see below)
      final secScheme = await _typBayScheming(
        newInput,
        ulsUDL,
        BeamDesignOptions.secBeam,
        globalData,
        loadingTables,
      );

      final mainScheme = await _typBayScheming(
        newInput,
        ulsUDL,
        BeamDesignOptions.mainBeam,
        globalData,
        loadingTables,
        secScheme: secScheme,
      );

      final String id = await _generateUniqueId(existingID: existingID);

      final finalLog = StringBuffer();
      finalLog.write(secScheme.calsLog);
      finalLog.write(mainScheme.calsLog);

      final adoptedLoading = loadingTables.firstWhere(
        (tbl) => tbl.usage == newInput.usage,
      );

      final result = BeamSchemeData(
        usage: newInput.usage,
        finish: adoptedLoading.finish,
        service: adoptedLoading.service,
        liveLoad: adoptedLoading.liveLoad,
        loadingTableId: adoptedLoading.loadingTableId,
        shortSpan: newInput.shortSpan,
        longSpan: newInput.longSpan,
        bays: newInput.bays,
        mainWidth: mainScheme.mainWidth, // use main scheme
        secWidth: secScheme.secWidth, // use sec scheme
        mainStrZone: newInput.mainStrZone,
        secStrZone: newInput.secStrZone,
        fcu: newInput.fcu,
        cover: newInput.cover,
        mainTopBar: mainScheme.mainTopBar, // use main scheme
        mainBottomBar: mainScheme.mainBottomBar, // use main scheme
        mainLinks: mainScheme.mainLinks, // use main scheme
        secTopBar: secScheme.secTopBar, // use sec scheme
        secBottomBar: secScheme.secBottomBar, // use sec scheme
        secLinks: secScheme.secLinks, // use sec scheme
        beamSchemeId: id,
        calsLog: finalLog.toString(),
        isSelected: false,
      );
      finalList.add(result);
    }
    state = AsyncData(finalList);
    return finalList;
  }

  Future<BeamSchemeData> _typBayScheming(
    BeamSchemeInput input,
    double ulsUDL,
    String beamDesignOption,
    GlobalData globalData,
    List<LoadingTable> loadingTables, {
    BeamSchemeData? secScheme,
  }) async {
    // ********************************
    // initialization
    // ********************************
    // iteration status
    late bool shouldRedesign;
    bool skipIteration = false;
    bool redesignacT = true, redesignacC = true, reDesignLinks = true;
    int linksOptimizationSteps = 0;
    int linksOptimizationStepsLimit = 10;

    // rebar diameters
    final List<int> diaT = [12, 16, 20, 25, 32, 40]; // tension bar diameter
    final List<int> diaC = [12, 16, 20, 25, 32, 40]; // tension bar diameter
    final List<int> diaL = [10, 12, 16, 20, 25]; // links diameter
    final int spacerBar = 16;
    //inddex for iteration for rebar diameters and links diameter
    int i1 = 0, i2 = 0, j = 0;

    //Initialize the width
    double width = 200;
    double secWidth = 200;
    double mainWidth = 200;

    //k-value
    late double k;
    late double kLimit;

    //effective depth and lever-arm
    late double d, dC;
    late double z;

    //nos of rebar
    late int nRPro;

    // links provided: legs nos and spacing
    late int nLPro;
    int sPro = 300;

    //nos of layer of tension and compression steel
    int layerT = 1, layerC = 1;

    // Some Limit for reasonable design
    int layerLimit = input.maxLayers; // max layer of rebars
    int sMin = input.minS, sMax = input.maxS; // links spacing
    double widthLimit = input.maxWidth;
    double minBarS = 80; // min bar clear spacing

    //req'd tension, compression steel, and links
    late double asT, asC, lReq;

    //provided tension, compression steel and links
    late double asTPro, asCPro, lPro;
    String asTProText = '', asCProText = '', lProText = '';

    //revised loading (incorproate Self Weight)
    late double newUlsudl, swMainFactored;

    // design moment and shear
    late double mD, vD;

    // design capacity
    double fy = 500; // [MPa]

    late double vC_, vD_, vR_;

    // record
    StringBuffer buffer = StringBuffer();
    List<bool> status = [false, false, false];
    LoadingTable loadingTable;

    // Some global data
    final double f1 = globalData.sdlFactor;

    // ********************************
    // Calculation
    // ********************************
    shouldRedesign = true; // at least run the first
    // bool widthAdjused = false;
    if (beamDesignOption == BeamDesignOptions.secBeam) {
      width = secWidth;
    } else {
      width = mainWidth;
    }

    // initialize nos of rebars and links legs
    nRPro =
        ((width - 2 * (input.cover + diaL[j]) + minBarS) /
                (max(diaT.last, diaC.last) + minBarS))
            .floor();
    if (nRPro < 2) {
      nRPro = 2;
    }

    if (nRPro % 2 == 0) {
      nLPro = (nRPro / 4).ceil() * 2;
    } else {
      nLPro = (((nRPro - 1) / 4).ceil()) * 2;
    }
    while (shouldRedesign) {
      // ********************************
      // design moment and shear adjusted with beam self-weight
      // ********************************
      if (beamDesignOption == BeamDesignOptions.secBeam) {
        final double loadWidth = input.shortSpan / input.bays; // load width
        newUlsudl =
            ulsUDL +
            f1 *
                getPressureFromThick(
                  input.slabThickness,
                  globalData.rcUnitWeight,
                ) *
                loadWidth + // slab self weight
            f1 *
                getSelfWeight(
                  globalData.rcUnitWeight,
                  secWidth * input.secStrZone,
                ); // beam Self weight

        mD = getMaxMomentBeamUDL(newUlsudl, input.longSpan);
        vD = getMaxShearUDLSimpleBeam(newUlsudl, input.longSpan);
      } else {
        if (secScheme != null) {
          secWidth = secScheme.secWidth;
        }
        final double loadWidth =
            input.shortSpan / input.bays; // load width of secondary beam

        newUlsudl =
            ulsUDL +
            f1 *
                getPressureFromThick(
                  input.slabThickness,
                  globalData.rcUnitWeight,
                ) *
                loadWidth + // slab SW as UDL
            f1 *
                getSelfWeight(
                  globalData.rcUnitWeight,
                  secWidth * input.secStrZone,
                ); // beam SW as UDL

        final pD = newUlsudl * input.longSpan;
        swMainFactored =
            f1 *
            getSelfWeight(
              globalData.rcUnitWeight,
              mainWidth * input.mainStrZone,
            ); // UDL from main beam SW
        mD =
            getMaxMomentBeamUDL(swMainFactored, input.shortSpan) +
            getMaxMomentPointLoadSimpleBeam(
              pD,
              input.shortSpan,
              n: input.bays - 1,
            );
        vD =
            getMaxShearUDLSimpleBeam(swMainFactored, input.shortSpan) +
            getMaxShearPointLoadSimpleBeam(
              pD,
              input.shortSpan,
              n: input.bays - 1,
            );
      }

      // ********************************
      // Design beam width
      // ********************************
      // Which beam width we are designing

      // ********************************
      // Calculations
      // ********************************

      // get effective depth
      if (beamDesignOption == BeamDesignOptions.secBeam) {
        d =
            input.secStrZone -
            input.cover -
            diaL[j] -
            (diaT[i1] + (diaT.last + spacerBar) * (layerT - 1)) / 2;
      } else {
        d =
            input.mainStrZone -
            input.cover -
            diaL[j] -
            (diaT[i1] + (diaT.last + spacerBar) * (layerT - 1)) / 2;
      }

      dC =
          input.cover +
          diaL[j] +
          (diaC[i2] + (diaC.last + spacerBar) * (layerC - 1)) / 2;

      k = getkValueRectangle(mD, width, d, input.fcu);
      kLimit = getkValueLimitRectangle(input.fcu);
      z = getLeverArmRectangle(k, kLimit, d);
      asC = getRecBeamReqCompressionBar(mD, width, d, dC, input.fcu, fy: fy);
      asT = getRecBeamReqTensionBar(mD, width, d, dC, input.fcu, fy: fy);

      // -----------------------------
      // provided steels
      // -----------------------------
      asTPro = getRecBeamSteelProvided(diaT, layerT, diaT[i1], nRPro);
      asCPro = getRecBeamSteelProvided(diaC, layerC, diaC[i2], nRPro);

      // -----------------------------
      // provided links
      // -----------------------------
      vD_ = vD * pow(10, 3) / (width * d);
      vC_ = getConcreteShearStrength(
        asTPro,
        input.fcu,
        width,
        d,
        withMinLinks: true,
      );
      vR_ = getConcreteShearParams_vR(input.fcu);
      lReq = getRecBeamReqLinks(
        vD,
        asTPro,
        input.fcu,
        width,
        d,
        fy: fy,
      );
      lPro = pow(diaL[j], 2) * pi / 4 * nLPro / sPro;

      // ********************************
      // Design validaiton in each loop
      // ********************************
      //* concept of adjustmernt
      //* Flexural: increase bar size, then layer | reset bar size to min when layer increases
      //* Shear: increase legs nos, then decrease spacing, and finally increase bar size.
      //* The legs nos reset to 2 if spacing decreases
      //* the legs resets to 2 and spacing reset to 300 if bar size increases

      // ! if the width exceeds the max width, directly escape and declare the design failed
      if (width > input.maxWidth) {
        status[0] = false;
        status[1] = false;
        status[2] = false;
        break;
      }

      //! if too narrow , increase width, skip to next iteration.
      if (nRPro <= 1) {
        width += 100;
        if (beamDesignOption == BeamDesignOptions.secBeam) {
          secWidth += 100;
        } else {
          mainWidth += 100;
        }
        skipIteration = true;
      }

      //! if shear fails (>0.8sqrt(fcu)), lreq will be double.infinity, and skip iteration
      if (lReq == double.infinity) {
        width += 100;
        if (beamDesignOption == BeamDesignOptions.secBeam) {
          secWidth += 100;
        } else {
          mainWidth += 100;
        }
        skipIteration = true;
      }

      //! if the k-value exceeds preferred k-value/steel ratio, skip iteration
      if (beamDesignOption == BeamDesignOptions.secBeam) {
        if (k > input.secKValue ||
            asTPro > input.secSteelRatio * input.secStrZone * width) {
          width += 100;
          secWidth += 100;
          // no need to run rest of design process
          skipIteration = true;
        }
      } else {
        if (k > input.mainKValue ||
            asTPro > input.mainSteelRatio * input.mainStrZone * width) {
          width += 100;
          mainWidth += 100;
          skipIteration = true;
        }
      }

      //! Skip iteration if necessary
      if (skipIteration) {
        //reset diameters, layers, spacing, nos of rebars
        i1 = 0;
        i2 = 0;
        layerT = 1;
        layerC = 1;
        nRPro =
            ((width - 2 * (input.cover + diaL[j]) + minBarS) /
                    (max(diaT.last, diaC.last) + minBarS))
                .floor();
        j = 0;
        sPro = sMax;
        if (nRPro % 2 == 0) {
          nLPro = (nRPro / 4).ceil() * 2;
        } else {
          nLPro = (((nRPro - 1) / 4).ceil()) * 2;
        }
        skipIteration = false;
        continue;
      }

      // Todo: check tension bars
      if (asTPro < asT ||
          asTPro >
              (beamDesignOption == BeamDesignOptions.secBeam
                  ? input.secSteelRatio * input.secStrZone * width
                  : input.mainSteelRatio * input.mainStrZone * width)) {
        redesignacT = true;
        if (i1 + 1 <= diaT.length - 1) {
          i1 += 1;
        } else if (i1 == diaT.length - 1 && layerT + 1 <= layerLimit) {
          i1 = 0;
          layerT += 1;
        } else if (i1 == diaT.length - 1 &&
            layerT == layerLimit &&
            width + 100 <= widthLimit) {
          width += 100;
          if (beamDesignOption == BeamDesignOptions.secBeam) {
            secWidth += 100;
          } else {
            mainWidth += 100;
          }
          //reset rebars and links arrangement
          i1 = 0;
          i2 = 0;
          layerT = 1;
          layerC = 1;
          nRPro =
              ((width - 2 * (input.cover + diaL[j]) + minBarS) /
                      (max(diaT.last, diaC.last) + minBarS))
                  .floor();
          j = 0;
          sPro = sMax;
          if (nRPro % 2 == 0) {
            nLPro = (nRPro / 4).ceil() * 2;
          } else {
            nLPro = (((nRPro - 1) / 4).ceil()) * 2;
          }
          continue; // no need to run rest of design process
        } else {
          redesignacT = false;
        }
      } else {
        redesignacT = false;
        status[0] = true;
      }

      // todo:  check compression bars
      if (asCPro < asC ||
          asCPro >
              (beamDesignOption == BeamDesignOptions.secBeam
                  ? input.secSteelRatio * input.secStrZone * width
                  : input.mainSteelRatio * input.mainStrZone * width)) {
        redesignacC = true;
        if (i2 + 1 <= diaC.length - 1) {
          i2 += 1;
        } else if (i2 == diaC.length - 1 && layerC + 1 <= layerLimit) {
          i2 = 0;
          layerC += 1;
        } else if ((i2 == diaC.length - 1 &&
            layerC == layerLimit &&
            width + 100 <= widthLimit)) {
          width += 100;
          if (beamDesignOption == BeamDesignOptions.secBeam) {
            secWidth += 100;
          } else {
            mainWidth += 100;
          }
          //reset rebars and links
          i1 = 0;
          i2 = 0;
          layerT = 1;
          layerC = 1;
          nRPro =
              ((width - 2 * (input.cover + diaL[j]) + minBarS) /
                      (max(diaT.last, diaC.last) + minBarS))
                  .floor();
          j = 0;
          sPro = sMax;
          if (nRPro % 2 == 0) {
            nLPro = (nRPro / 4).ceil() * 2;
          } else {
            nLPro = (((nRPro - 1) / 4).ceil()) * 2;
          }
          continue; // no need to run rest of design process
        } else {
          redesignacC = false; //escape loop and no need to design anymore
        }
      } else {
        redesignacC = false;
        status[1] = true;
      }

      //todo: check shear
      if (lPro < lReq) {
        reDesignLinks = true;
        if (nLPro + 1 <= nRPro) {
          nLPro += 1;
        } else if (nLPro == nRPro && sPro - 25 >= sMin) {
          nLPro = 2;
          sPro -= 25;
        } else if (nLPro == nRPro && sPro == sMin && j + 1 <= diaL.length - 1) {
          nLPro = 2;
          sPro = sMax;
          j += 1;
        } else {
          reDesignLinks = false;
        }
      } else {
        // there could be situation links pass, rebar not
        // then the width and effective depth keeps change
        // then req'd links keep reducing while provided links kept at high amount
        // Therefore, we redesign in case the provided links are 20% more than required

        if (lPro > lReq * 1.2 &&
            linksOptimizationSteps < linksOptimizationStepsLimit) {
          linksOptimizationSteps += 1;
          reDesignLinks = true;
          nLPro = 2;
          sPro = sMax;
          j = 0;
        } else {
          reDesignLinks = false;
          status[2] = true;
        }
      }
      shouldRedesign = redesignacT || redesignacC || reDesignLinks;
    }

    // ********************************
    // Return Design
    // ********************************
    // tension bar designation
    asTProText = getRecBeamSteelProvidedText(diaT, layerT, diaT[i1], nRPro);
    if (!status[0]) {
      asTProText = '(Fail) $asTProText';
    }

    // compression bar designation
    if (asC > 0) {
      asCProText = getRecBeamSteelProvidedText(diaC, layerC, diaC[i2], nRPro);
      if (!status[1]) {
        asCProText = '(Fail) $asCProText';
      }
    } else {
      asCProText = 'N.A.';
    }
    // links designation
    if (lPro > 0) {
      lProText = getRecBeamLinksProvidedText(diaL[j], sPro, nLPro);
      if (!status[2]) {
        lProText = '(Fail) $lProText';
      }
    } else {
      lProText = 'N.A.';
    }

    // before return, record the cals result
    await _recordCalsResult(
      buffer,
      input,
      globalData,
      beamDesignOption: beamDesignOption,
      k: k,
      n_r_pro: nRPro,
      As_t_pro: asTPro,
      As_t: asT,
      secWidth: secWidth,
      mainWidth: mainWidth,
      As_c_pro: asCPro,
      As_c: asC,
      M_d: mD,
      V_d: vD,
      width: width,
      layer_t: layerT,
      layer_c: layerC,
      dia_t: diaT[i1],
      dia_c: diaC[i2],
      dia_l: diaL[j],
      d: d,
      d_c: dC,
      z: z,
      As_t_pro_text: asTProText,
      As_c_pro_text: asCProText,
      l_pro_text: lProText,
      l_pro: lPro,
      l_req: lReq,
      n_l_pro: nLPro,
      v_d: vD_,
      v_c: vC_,
      v_r: vR_,
      s_pro: sPro,
      s_min: sMin,
      s_max: sMax, // todo: check if this is needed
      status: status,
    );

    loadingTable = loadingTables.firstWhere((tbl) => tbl.usage == input.usage);

    // ! Finally assign the result
    return BeamSchemeData(
      usage: input.usage,
      finish: loadingTable.finish,
      service: loadingTable.service,
      liveLoad: loadingTable.liveLoad,
      loadingTableId: loadingTable.loadingTableId,
      shortSpan: input.shortSpan,
      longSpan: input.longSpan,
      bays: input.bays,
      mainWidth: mainWidth,
      secWidth: secWidth,
      mainStrZone: input.mainStrZone,
      secStrZone: input.secStrZone,
      fcu: input.fcu,
      cover: input.cover,
      mainTopBar:
          beamDesignOption == BeamDesignOptions.secBeam ? '' : asCProText,
      mainBottomBar:
          beamDesignOption == BeamDesignOptions.secBeam ? '' : asTProText,
      mainLinks: beamDesignOption == BeamDesignOptions.secBeam ? '' : lProText,
      secTopBar:
          beamDesignOption == BeamDesignOptions.secBeam ? asCProText : '',
      secBottomBar:
          beamDesignOption == BeamDesignOptions.secBeam ? asTProText : '',
      secLinks: beamDesignOption == BeamDesignOptions.secBeam ? lProText : '',
      beamSchemeId: 'temp',
      calsLog: buffer.toString(),
      isSelected: false,
    );
  }

  Future<void> _recordCalsResult(
    StringBuffer buffer,
    BeamSchemeInput input,
    GlobalData globalData, {
    String beamDesignOption = '',
    double k = 0.0,
    int n_r_pro = 0,
    double As_t_pro = 0.0,
    double As_t = 0.0,
    double secWidth = 0.0,
    double mainWidth = 0.0,
    double As_c_pro = 0.0,
    double As_c = 0.0,
    double M_d = 0.0,
    double V_d = 0.0,
    double width = 0.0,
    int layer_t = 0,
    int layer_c = 0,
    int dia_t = 0,
    int dia_c = 0,
    int dia_l = 0,
    double d = 0.0,
    double d_c = 0.0,
    double z = 0.0,
    String As_t_pro_text = '',
    String As_c_pro_text = '',
    String l_pro_text = '',
    double l_pro = 0.0,
    double l_req = 0.0,
    int n_l_pro = 0,
    double v_d = 0.0,
    double v_c = 0.0,
    double v_r = 0.0,
    int s_pro = 0,
    int s_min = 0,
    int s_max = 0,
    List<bool> status = const [],
  }) async {
    final x = globalData.unit;
    late final List<String> unit;
    late final double sdl;
    late final double ll;
    late final double slsLoad;
    late final double ulsLoad;
    switch (x) {
      case 'metrics':
        unit = PreferredUnit.metrics;
        break;
      case 'imperial':
        unit = PreferredUnit.imperial;
        break;
      default:
        unit = PreferredUnit.metrics;
        break;
    }
    NumberFormat f0 = NumberFormat('0'),
        f1 = NumberFormat('0.0'),
        f3 = NumberFormat('0.000'),
        f5 = NumberFormat('0.00000');

    buffer.clear();
    buffer.write('Design For: $beamDesignOption\n');
    buffer.write('Short Span: ${f1.format(input.shortSpan)} [${unit[3]}] | ');
    buffer.write('Long Span: ${f1.format(input.longSpan)} [${unit[3]}] | ');
    buffer.write('Bays: ${f0.format(input.bays)}\n');

    //* loading
    final loadingTable = await ref.read(loadingTablesControllerProvider.future);
    final loading = loadingTable.firstWhere((tbl) => tbl.usage == input.usage);
    sdl =
        getPressureFromThick(loading.finish, globalData.finishUnitWeight) +
        loading.service;
    ll = loading.liveLoad;
    slsLoad = sdl + ll;
    ulsLoad = globalData.sdlFactor * sdl + globalData.llFactor * ll;
    buffer.write('SDL: ${f1.format(sdl)} [${unit[1]}] | ');
    buffer.write('LL: ${f1.format(ll)} [${unit[1]}] | ');
    buffer.write('SLS: ${f1.format(slsLoad)} [${unit[1]}] | ');
    buffer.write('ULS: ${f1.format(ulsLoad)} [${unit[1]}]\n');

    buffer.write(
      'Secondary Beam Load Width: ${f3.format(input.shortSpan / input.bays)} [${unit[3]}] | ',
    );
    buffer.write(
      'Slab Thickness: ${f1.format(input.slabThickness)} [${unit[4]}]\n',
    );
    buffer.write('Main Width: ${f1.format(mainWidth)} [${unit[4]}] | ');
    buffer.write('Secondary Width: ${f1.format(secWidth)} [${unit[4]}]\n');

    buffer.write('M: ${f0.format(M_d)} [${unit[2]}] | ');
    buffer.write('V: ${f0.format(V_d)} [${unit[0]}] | ');
    buffer.write('width: ${f0.format(width)} [${unit[4]}]\n');

    buffer.write('k: ${f5.format(k)} | ');
    buffer.write('k_limit: ${f3.format(input.secKValue)} | ');
    buffer.write('preferred k_sec: ${f3.format(input.secKValue)} | ');
    buffer.write('preferred k_main: ${f3.format(input.mainKValue)}\n');

    buffer.write('v_d: ${f3.format(v_d)} [${unit[5]}] | ');
    buffer.write('v_c: ${f3.format(v_c)} [${unit[5]}] | ');
    buffer.write('v_r: ${f3.format(v_r)} [${unit[5]}]\n');

    buffer.write('dia_t: ${f0.format(dia_t)} [${unit[4]}] | ');
    buffer.write('dia_c: ${f0.format(dia_c)} [${unit[4]}] | ');
    buffer.write('dia_l: ${f0.format(dia_l)} [${unit[4]}]\n');

    buffer.write('n_r_pro: ${f0.format(n_r_pro)} [bars] | ');
    buffer.write('n_l_pro: ${f0.format(n_l_pro)} [legs]\n');

    buffer.write('s_pro: ${f0.format(s_pro)} [${unit[4]}] | ');
    buffer.write('s_min: ${f0.format(s_min)} [${unit[4]}] | ');
    buffer.write('s_max: ${f0.format(s_max)} [${unit[4]}]\n');

    buffer.write('layer_t: ${f0.format(layer_t)} | ');
    buffer.write('layer_c: ${f0.format(layer_c)} | ');
    buffer.write('d: ${f0.format(d)} [${unit[4]}] | ');
    buffer.write('d_c: ${f0.format(d_c)} [${unit[4]}] | ');
    buffer.write('z: ${f0.format(z)} [${unit[4]}]\n');

    buffer.write('As_t_pro: ${f0.format(As_t_pro)} [${unit[6]}] | ');
    buffer.write('As_t: ${f0.format(As_t)} [${unit[6]}]| ');
    if (beamDesignOption == BeamDesignOptions.secBeam) {
      buffer.write(
        'As_t limit: ${f0.format(input.secSteelRatio * secWidth * input.secStrZone)} [${unit[6]}]\n',
      );
    } else {
      buffer.write(
        'As_t limit: ${f0.format(input.mainSteelRatio * mainWidth * input.mainStrZone)} [${unit[6]}]\n',
      );
    }
    buffer.write('As_c_pro: ${f0.format(As_c_pro)} [${unit[6]}] | ');
    buffer.write('As_c: ${f0.format(As_c)} [${unit[6]}] | ');
    if (beamDesignOption == BeamDesignOptions.secBeam) {
      buffer.write(
        'As_c limit: ${f0.format(input.secSteelRatio * secWidth * input.secStrZone)} [${unit[6]}]\n',
      );
    } else {
      buffer.write(
        'As_c limit: ${f0.format(input.mainSteelRatio * mainWidth * input.mainStrZone)} [${unit[6]}]\n',
      );
    }

    buffer.write('l_pro: ${f3.format(l_pro)} [${unit[6]}/${unit[4]}] | ');
    buffer.write('l_req: ${f3.format(l_req)} [${unit[6]}/${unit[4]}]\n');

    buffer.write('As_t_pro Designation: $As_t_pro_text\n');
    buffer.write('As_c_pro Designation: $As_c_pro_text\n');
    buffer.write('l_pro Designation: $l_pro_text\n');

    if (status[0]) {
      buffer.write('Tension bars: pass | ');
    } else {
      buffer.write('Tension bars: fail | ');
    }
    if (status[1]) {
      buffer.write('Compression bars: pass | ');
    } else {
      buffer.write('Compression bars: fail | ');
    }
    if (status[2]) {
      buffer.write('Links: pass\n');
    } else {
      buffer.write('Links: fail\n');
    }
  }
}
