import 'package:nanoid2/nanoid2.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:structify/domain_layer/wind_load_global.dart';

//presentation layer
import '../../domain_layer/column_scheme_data.dart';
import '../../domain_layer/wind_load.dart';
import '../../domain_layer/transfer_beam_scheme_input.dart';
import '../screen/homescreen.dart';

// domain layer

part 'windload_input_global_controller.g.dart';

@riverpod
class WindLoadInputGlobalController extends _$WindLoadInputGlobalController {
  @override
  FutureOr<WindLoadGlobal> build() async {
    // print('Build: Transfer Beam Scheme Input');
    final WindLoadGlobal windLoadInputsGlobal =
        await ref
            .read(appDatabaseControllerProvider.notifier)
            .queryWindLoadInputGlobal();
    return windLoadInputsGlobal;
  }

  Future<void> updateTable({
    double? h,
    double? bTop,
    double? dTop,
    double? sS,
    String? bldgType,
    double? amplificationFactor,
    String? id,
  }) async {
    final x = await future;
    // Create a list of updates
    final newState = x.copyWith(
      h: h ?? x.h,
      bTop: bTop ?? x.bTop,
      dTop: dTop ?? x.dTop,
      sS: sS ?? x.sS,
      bldgType: bldgType ?? x.bldgType,
      amplificationFactor: amplificationFactor ?? x.amplificationFactor,
    );
    // Update the state only if there are changes
    state = AsyncData(newState);
  }
}
