// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'column_scheme_input_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$columnSchemeInputControllerHash() =>
    r'd2ee23afd25cc271225f7688c87489e5bd38b9f6';

/// See also [ColumnSchemeInputController].
@ProviderFor(ColumnSchemeInputController)
final columnSchemeInputControllerProvider = AutoDisposeAsyncNotifierProvider<
  ColumnSchemeInputController,
  List<ColumnSchemeInput>
>.internal(
  ColumnSchemeInputController.new,
  name: r'columnSchemeInputControllerProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$columnSchemeInputControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$ColumnSchemeInputController =
    AutoDisposeAsyncNotifier<List<ColumnSchemeInput>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
