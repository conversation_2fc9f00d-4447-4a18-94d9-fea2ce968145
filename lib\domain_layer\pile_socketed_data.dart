import 'package:freezed_annotation/freezed_annotation.dart';
// import 'package:flutter/foundation.dart';
part 'pile_socketed_data.freezed.dart';
part 'pile_socketed_data.g.dart';

@freezed
abstract class PileSocketedData with _$PileSocketedData {
  const PileSocketedData._();
  factory PileSocketedData({
    
    @Default(false) bool isSelected,
    @Default('1') String pileSocketedSchemeId,
  }) = _PileSocketedData;

  factory PileSocketedData.fromJson(Map<String, Object?> json) =>
      _$PileSocketedDataFromJson(json);
}
