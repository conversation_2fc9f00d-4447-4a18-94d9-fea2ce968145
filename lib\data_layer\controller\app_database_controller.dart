import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:structify/domain_layer/basement_wall_scheme_input.dart';
import 'package:structify/domain_layer/cantilever_scheme_input_global.dart';
import 'package:structify/domain_layer/column_scheme_input.dart';
import 'package:structify/domain_layer/column_scheme_input_global.dart';
import 'package:structify/domain_layer/data_struct/carbon_struct.dart';
import 'package:structify/domain_layer/footing_scheme_input_global.dart';
import 'package:structify/domain_layer/pile_driven_input.dart';
import 'package:structify/domain_layer/pile_driven_input_global.dart';
import 'package:structify/domain_layer/pile_end_bearing_bored_input.dart';
import 'package:structify/domain_layer/pile_end_bearing_bored_input_global.dart';
import 'package:structify/domain_layer/pile_frictional_bored_input.dart';
import 'package:structify/domain_layer/pile_frictional_bored_input_global.dart';
import 'package:structify/domain_layer/slab_scheme_data.dart';
import 'package:structify/domain_layer/slab_scheme_input.dart';
import 'package:structify/domain_layer/steel_cantilever_truss_scheme_input.dart';
import 'package:structify/domain_layer/transfer_beam_scheme_input_global.dart';
// import 'package:structify/domain_layer/beam_scheme_input.dart';
// import 'package:structify/domain_layer/global_data.dart';

//data layer
import '../../domain_layer/basement_wall_scheme_data.dart';
import '../../domain_layer/cantilever_scheme_data.dart';
import '../../domain_layer/cantilever_scheme_input.dart';
import '../../domain_layer/column_scheme_data.dart';
import '../../domain_layer/data_struct/recommend_load.dart';
import '../../domain_layer/footing_scheme_data.dart';
import '../../domain_layer/footing_scheme_input.dart';
import '../../domain_layer/pile_driven_data.dart';
import '../../domain_layer/pile_end_bearing_bored_data.dart';
import '../../domain_layer/pile_frictional_bored_data.dart';
import '../../domain_layer/pile_socketed_data.dart';
import '../../domain_layer/programme_item.dart';
import '../../domain_layer/steel_beam_scheme_data.dart';
import '../../domain_layer/steel_beam_scheme_input.dart';
import '../../domain_layer/steel_cantilever_truss_scheme_data.dart';
import '../../domain_layer/steel_cantilever_truss_scheme_input_global.dart';
import '../../domain_layer/steel_column_scheme_data.dart';
import '../../domain_layer/steel_column_scheme_input.dart';
import '../../domain_layer/steel_column_scheme_input_global.dart';
import '../../domain_layer/steel_transfer_truss_scheme_data.dart';
import '../../domain_layer/steel_transfer_truss_scheme_input.dart';
import '../../domain_layer/steel_transfer_truss_scheme_input_global.dart';
import '../../domain_layer/strzone_table.dart';
import '../../domain_layer/transfer_beam_scheme_data.dart';
import '../../domain_layer/transfer_beam_scheme_input.dart';
import '../../domain_layer/wind_load.dart';
import '../../domain_layer/wind_load_global.dart';
import '../app_database.dart';

//domain layer
import '../../domain_layer/loading_table.dart';
import '../../domain_layer/global_data.dart';
import '../../domain_layer/beam_scheme_input.dart';
import '../../domain_layer/beam_scheme_data.dart';

part 'app_database_controller.g.dart';

@riverpod
class AppDatabaseController extends _$AppDatabaseController {
  @override
  FutureOr<AppDatabase> build() {
    return AppDatabase();
  }

  Future<void> saveAllLoadingTables(List<LoadingTable> loadingTableList) async {
    return state.when(
      data: (appDataBase) async {
        return await appDataBase.saveAllLoadingTables(loadingTableList);
      },
      error: (error, loading) => error.toString(),
      loading: () {},
    );
  }

  Future<void> deleteAllLoadingTables() async {
    return state.when(
      data: (appDataBase) async {
        return await appDataBase.deleteAllLoadingTables();
      },
      error: (error, loading) => error.toString(),
      loading: () {},
    );
  }

  Future<List<LoadingTable>> queryAllLoadingTables() async {
    return state.when(
      data: (appDataBase) async {
        return await appDataBase.queryAllLoadingTables();
      },
      error: (error, loading) => [],
      loading: () => [],
    );
  }

  Future<void> saveGlobalData(GlobalData data) async {
    return state.when(
      data: (appDataBase) async {
        return await appDataBase.saveGlobalData(data);
      },
      error: (error, loading) => error.toString(),
      loading: () {},
    );
  }

  FutureOr<GlobalData> queryGlobalData() async {
    return state.when(
      data: (appDataBase) async {
        return await appDataBase.queryGlobalData();
      },
      error: (error, loading) => GlobalData(),
      loading: () => GlobalData(),
    );
  }

  Future<void> saveBeamSchemeInput(BeamSchemeInput data) async {
    return state.when(
      data: (appDataBase) async {
        return await appDataBase.saveBeamSchemeInput(data);
      },
      error: (error, loading) => error.toString(),
      loading: () {},
    );
  }

  FutureOr<BeamSchemeInput> queryBeamSchemeInput() async {
    return state.when(
      data: (appDataBase) async {
        return await appDataBase.queryBeamSchemeInput();
      },
      error: (error, loading) => BeamSchemeInput(),
      loading: () => BeamSchemeInput(),
    );
  }

  FutureOr<void> saveBeamSchemeData(List<BeamSchemeData> beamSchemes) async {
    return state.when(
      data: (appDataBase) async {
        return await appDataBase.saveBeamSchemeData(beamSchemes);
      },
      error: (error, loading) => error.toString(),
      loading: () {},
    );
  }

  FutureOr<List<BeamSchemeData>> queryBeamSchemeData() async {
    return state.when(
      data: (appDataBase) async {
        return await appDataBase.queryBeamSchemeData();
      },
      error: (error, loading) => [BeamSchemeData()],
      loading: () => [BeamSchemeData()],
    );
  }

  FutureOr<SlabSchemeInput> querySlabSchemeInput() async {
    return state.when(
      data: (appDataBase) async {
        return await appDataBase.querySlabSchemeInput();
      },
      error: (error, loading) => SlabSchemeInput(),
      loading: () => SlabSchemeInput(),
    );
  }

  FutureOr<List<SlabSchemeData>> querySlabSchemeData() async {
    return state.when(
      data: (appDataBase) async {
        return await appDataBase.querySlabSchemeData();
      },
      error: (error, loading) => [SlabSchemeData()],
      loading: () => [SlabSchemeData()],
    );
  }

  Future<void> saveSlabSchemeInput(SlabSchemeInput data) async {
    return state.when(
      data: (appDataBase) async {
        return await appDataBase.saveSlabSchemeInput(data);
      },
      error: (error, loading) => error.toString(),
      loading: () {},
    );
  }

  FutureOr<void> saveSlabSchemeData(List<SlabSchemeData> slabSchemes) async {
    return state.when(
      data: (appDataBase) async {
        return await appDataBase.saveSlabSchemeData(slabSchemes);
      },
      error: (error, loading) => error.toString(),
      loading: () {},
    );
  }

  FutureOr<List<ColumnSchemeInput>> queryColumnSchemeInput() async {
    return state.when(
      data: (appDataBase) async {
        return await appDataBase.queryColumnSchemeInput();
      },
      error: (error, loading) => [ColumnSchemeInput()],
      loading: () => [ColumnSchemeInput()],
    );
  }

  FutureOr<void> saveColumnSchemeInput(List<ColumnSchemeInput> tables) async {
    return state.when(
      data: (appDataBase) async {
        return await appDataBase.saveColumnSchemeInput(tables);
      },
      error: (error, loading) => error.toString(),
      loading: () {},
    );
  }

  FutureOr<ColumnSchemeInputGlobal> queryColumnSchemeInputGlobal() async {
    return state.when(
      data: (appDataBase) async {
        return await appDataBase.queryColumnSchemeInputGlobal();
      },
      error: (error, loading) => ColumnSchemeInputGlobal(),
      loading: () => ColumnSchemeInputGlobal(),
    );
  }

  FutureOr<void> saveColumnSchemeInputGlobal(
    ColumnSchemeInputGlobal columnSchemeInputGlobal,
  ) async {
    return state.when(
      data: (appDataBase) async {
        return await appDataBase.saveColumnSchemeInputGlobal(
          columnSchemeInputGlobal,
        );
      },
      error: (error, loading) => error.toString(),
      loading: () {},
    );
  }

  FutureOr<List<ColumnSchemeData>> queryColumnSchemeData() async {
    return state.when(
      data: (appDataBase) async {
        return await appDataBase.queryColumnSchemeData();
      },
      error: (error, loading) => [ColumnSchemeData()],
      loading: () => [ColumnSchemeData()],
    );
  }

  FutureOr<void> saveColumnSchemeData(List<ColumnSchemeData> tables) async {
    return state.when(
      data: (appDataBase) async {
        return await appDataBase.saveColumnSchemeData(tables);
      },
      error: (error, loading) => error.toString(),
      loading: () {},
    );
  }

  FutureOr<void> saveTransferBeamSchemeInputGlobal(
    TransferBeamSchemeInputGlobal transferBeamSchemeInputGlobal,
  ) async {
    return state.when(
      data: (appDataBase) async {
        return await appDataBase.saveTransferBeamSchemeInputGlobal(
          transferBeamSchemeInputGlobal,
        );
      },
      error: (error, loading) => error.toString(),
      loading: () {},
    );
  }

  FutureOr<TransferBeamSchemeInputGlobal>
  queryTransferBeamSchemeInputGlobal() async {
    return state.when(
      data: (appDataBase) async {
        return await appDataBase.queryTransferBeamSchemeInputGlobal();
      },
      error: (error, loading) => TransferBeamSchemeInputGlobal(),
      loading: () => TransferBeamSchemeInputGlobal(),
    );
  }

  FutureOr<void> saveTransferBeamSchemeInput(
    List<TransferBeamSchemeInput> tables,
  ) async {
    return state.when(
      data: (appDataBase) async {
        return await appDataBase.saveTransferBeamSchemeInput(tables);
      },
      error: (error, loading) => error.toString(),
      loading: () {},
    );
  }

  FutureOr<List<TransferBeamSchemeInput>> queryTransferBeamSchemeInput() async {
    return state.when(
      data: (appDataBase) async {
        return await appDataBase.queryTransferBeamSchemeInput();
      },
      error: (error, loading) => [TransferBeamSchemeInput()],
      loading: () => [TransferBeamSchemeInput()],
    );
  }

  FutureOr<TransferBeamSchemeData> queryTransferBeamSchemeData() {
    return state.when(
      data: (appDataBase) async {
        return await appDataBase.queryTransferBeamSchemeData();
      },
      error: (error, loading) => TransferBeamSchemeData(),
      loading: () => TransferBeamSchemeData(),
    );
  }

  FutureOr<void> saveTransferBeamSchemeData(
    TransferBeamSchemeData transferBeamSchemeData,
  ) async {
    return state.when(
      data: (appDataBase) async {
        return await appDataBase.saveTransferBeamSchemeData(
          transferBeamSchemeData,
        );
      },
      error: (error, loading) => error.toString(),
      loading: () {},
    );
  }

  FutureOr<SteelBeamSchemeInput> querySteelBeamSchemeInput() async {
    return state.when(
      data: (appDataBase) async {
        return await appDataBase.querySteelBeamSchemeInput();
      },
      error: (error, loading) => SteelBeamSchemeInput(),
      loading: () => SteelBeamSchemeInput(),
    );
  }

  FutureOr<void> saveSteelBeamSchemeInput(
    SteelBeamSchemeInput steelBeamSchemeInput,
  ) async {
    return state.when(
      data: (appDataBase) async {
        return await appDataBase.saveSteelBeamSchemeInput(steelBeamSchemeInput);
      },
      error: (error, loading) => error.toString(),
      loading: () {},
    );
  }

  FutureOr<List<SteelBeamSchemeData>> querySteelBeamSchemeData() async {
    return state.when(
      data: (appDataBase) async {
        return await appDataBase.querySteelBeamSchemeData();
      },
      error: (error, loading) => [SteelBeamSchemeData()],
      loading: () => [SteelBeamSchemeData()],
    );
  }

  FutureOr<void> saveSteelBeamSchemeData(
    List<SteelBeamSchemeData> stealBeamSchemes,
  ) async {
    return state.when(
      data: (appDataBase) async {
        return await appDataBase.saveSteelBeamSchemeData(stealBeamSchemes);
      },
      error: (error, loading) => error.toString(),
      loading: () {},
    );
  }

  FutureOr<List<SteelColumnSchemeInput>> querySteelColumnSchemeInput() async {
    return state.when(
      data: (appDataBase) async {
        return await appDataBase.querySteelColumnSchemeInput();
      },
      error: (error, loading) => [SteelColumnSchemeInput()],
      loading: () => [SteelColumnSchemeInput()],
    );
  }

  FutureOr<void> saveSteelColumnSchemeInput(
    List<SteelColumnSchemeInput> stealColumnSchemes,
  ) async {
    return state.when(
      data: (appDataBase) async {
        return await appDataBase.saveSteelColumnSchemeInput(stealColumnSchemes);
      },
      error: (error, loading) => error.toString(),
      loading: () {},
    );
  }

  FutureOr<SteelColumnSchemeInputGlobal>
  querySteelColumnSchemeInputGlobal() async {
    return state.when(
      data: (appDataBase) async {
        return await appDataBase.querySteelColumnSchemeInputGlobal();
      },
      error: (error, loading) => SteelColumnSchemeInputGlobal(),
      loading: () => SteelColumnSchemeInputGlobal(),
    );
  }

  FutureOr<void> saveSteelColumnSchemeInputGlobal(
    SteelColumnSchemeInputGlobal steelColumnSchemeInputGlobal,
  ) async {
    return state.when(
      data: (appDataBase) async {
        return await appDataBase.saveSteelColumnSchemeInputGlobal(
          steelColumnSchemeInputGlobal,
        );
      },
      error: (error, loading) => error.toString(),
      loading: () {},
    );
  }

  FutureOr<List<SteelColumnSchemeData>> querySteelColumnSchemeData() async {
    return state.when(
      data: (appDataBase) async {
        return await appDataBase.querySteelColumnSchemeData();
      },
      error: (error, loading) => [SteelColumnSchemeData()],
      loading: () => [SteelColumnSchemeData()],
    );
  }

  FutureOr<void> saveSteelColumnSchemeData(
    List<SteelColumnSchemeData> stealColumnSchemes,
  ) async {
    return state.when(
      data: (appDataBase) async {
        return await appDataBase.saveSteelColumnSchemeData(stealColumnSchemes);
      },
      error: (error, loading) => error.toString(),
      loading: () {},
    );
  }

  FutureOr<List<SteelTransferTrussSchemeInput>>
  querySteelTransferTrussSchemeInput() async {
    return state.when(
      data: (appDataBase) async {
        return await appDataBase.querySteelTransferTrussSchemeInput();
      },
      error: (error, loading) => [SteelTransferTrussSchemeInput()],
      loading: () => [SteelTransferTrussSchemeInput()],
    );
  }

  FutureOr<void> saveSteelTransferTrussSchemeInput(
    List<SteelTransferTrussSchemeInput> steelTransferTrussSchemeInput,
  ) async {
    return state.when(
      data: (appDataBase) async {
        return await appDataBase.saveSteelTransferTrussSchemeInput(
          steelTransferTrussSchemeInput,
        );
      },
      error: (error, loading) => error.toString(),
      loading: () {},
    );
  }

  FutureOr<SteelTransferTrussSchemeInputGlobal>
  querySteelTransferTrussSchemeInputGlobal() async {
    return state.when(
      data: (appDataBase) async {
        return await appDataBase.querySteelTransferTrussSchemeInputGlobal();
      },
      error: (error, loading) => SteelTransferTrussSchemeInputGlobal(),
      loading: () => SteelTransferTrussSchemeInputGlobal(),
    );
  }

  FutureOr<void> saveSteelTransferTrussSchemeInputGlobal(
    SteelTransferTrussSchemeInputGlobal steelTransferTrussSchemeInputGlobal,
  ) async {
    return state.when(
      data: (appDataBase) async {
        return await appDataBase.saveSteelTransferTrussSchemeInputGlobal(
          steelTransferTrussSchemeInputGlobal,
        );
      },
      error: (error, loading) => error.toString(),
      loading: () {},
    );
  }

  FutureOr<SteelTransferTrussSchemeData>
  querySteelTransferTrussSchemeData() async {
    return state.when(
      data: (appDataBase) async {
        return await appDataBase.querySteelTransferTrussSchemeData();
      },
      error: (error, loading) => SteelTransferTrussSchemeData(),
      loading: () => SteelTransferTrussSchemeData(),
    );
  }

  FutureOr<void> saveSteelTransferTrussSchemeData(
    SteelTransferTrussSchemeData steelTransferTrussSchemeData,
  ) async {
    return state.when(
      data: (appDataBase) async {
        return await appDataBase.saveSteelTransferTrussSchemeData(
          steelTransferTrussSchemeData,
        );
      },
      error: (error, loading) => error.toString(),
      loading: () {},
    );
  }

  Future<PileFrictionalBoredInput> queryPileFrictionalBoredInput() async {
    return state.when(
      data: (appDataBase) async {
        return await appDataBase.queryPileFrictionalBoredInput();
      },
      error: (error, loading) => PileFrictionalBoredInput(),
      loading: () => PileFrictionalBoredInput(),
    );
  }

  Future<void> savePileFrictionalBoredInput(
    PileFrictionalBoredInput pileFrictionalBoredInput,
  ) async {
    return state.when(
      data: (appDataBase) async {
        return await appDataBase.savePileFrictionalBoredInput(
          pileFrictionalBoredInput,
        );
      },
      error: (error, loading) => error.toString(),
      loading: () {},
    );
  }

  Future<List<PileFrictionalBoredData>> queryPileFrictionalBoredData() async {
    return state.when(
      data: (appDataBase) async {
        return await appDataBase.queryPileFrictionalBoredData();
      },
      error: (error, loading) => [PileFrictionalBoredData()],
      loading: () => [PileFrictionalBoredData()],
    );
  }

  Future<void> savePileFrictionalBoredData(
    List<PileFrictionalBoredData> pileFrictionalBoredData,
  ) async {
    return state.when(
      data: (appDataBase) async {
        return await appDataBase.savePileFrictionalBoredData(
          pileFrictionalBoredData,
        );
      },
      error: (error, loading) => error.toString(),
      loading: () {},
    );
  }

  Future<PileSocketedData> queryPileSocketedData() async {
    return state.when(
      data: (appDataBase) async {
        return await appDataBase.queryPileSocketedData();
      },
      error: (error, loading) => PileSocketedData(),
      loading: () => PileSocketedData(),
    );
  }

  Future<void> savePileSocketedData(PileSocketedData pileSocketedData) async {
    return state.when(
      data: (appDataBase) async {
        return await appDataBase.savePileSocketedData(pileSocketedData);
      },
      error: (error, loading) => error.toString(),
      loading: () {},
    );
  }

  Future<PileDrivenInput> queryPileDrivenInput() async {
    return state.when(
      data: (appDataBase) async {
        return await appDataBase.queryPileDrivenInput();
      },
      error: (error, loading) => PileDrivenInput(),
      loading: () => PileDrivenInput(),
    );
  }

  Future<void> savePileDrivenInput(PileDrivenInput pileDrivenInput) async {
    return state.when(
      data: (appDataBase) async {
        return await appDataBase.savePileDrivenInput(pileDrivenInput);
      },
      error: (error, loading) => error.toString(),
      loading: () {},
    );
  }

  Future<List<PileDrivenData>> queryPileDrivenData() async {
    return state.when(
      data: (appDataBase) async {
        return await appDataBase.queryPileDrivenData();
      },
      error: (error, loading) => [PileDrivenData()],
      loading: () => [PileDrivenData()],
    );
  }

  Future<void> savePileDrivenData(List<PileDrivenData> pileDrivenData) async {
    return state.when(
      data: (appDataBase) async {
        return await appDataBase.savePileDrivenData(pileDrivenData);
      },
      error: (error, loading) => error.toString(),
      loading: () {},
    );
  }

  Future<FootingSchemeInput> queryFootingSchemeInput() async {
    return state.when(
      data: (appDataBase) async {
        return await appDataBase.queryFootingSchemeInput();
      },
      error: (error, loading) => FootingSchemeInput(),
      loading: () => FootingSchemeInput(),
    );
  }

  Future<void> saveFootingSchemeInput(
    FootingSchemeInput footingSchemeInput,
  ) async {
    return state.when(
      data: (appDataBase) async {
        return await appDataBase.saveFootingSchemeInput(footingSchemeInput);
      },
      error: (error, loading) => error.toString(),
      loading: () {},
    );
  }

  Future<List<FootingSchemeData>> queryFootingSchemeData() async {
    return state.when(
      data: (appDataBase) async {
        return await appDataBase.queryFootingSchemeData();
      },
      error: (error, loading) => [FootingSchemeData()],
      loading: () => [FootingSchemeData()],
    );
  }

  Future<void> saveFootingSchemeData(
    List<FootingSchemeData> footingSchemeData,
  ) async {
    return state.when(
      data: (appDataBase) async {
        return await appDataBase.saveFootingSchemeData(footingSchemeData);
      },
      error: (error, loading) => error.toString(),
      loading: () {},
    );
  }

  Future<List<StrZoneTable>> queryStrZoneTables() async {
    return state.when(
      data: (appDataBase) async {
        return await appDataBase.queryStrZoneTables();
      },
      error: (error, loading) => [StrZoneTable()],
      loading: () => [StrZoneTable()],
    );
  }

  Future<void> saveStrZoneTables(List<StrZoneTable> strZoneTable) async {
    return state.when(
      data: (appDataBase) async {
        return await appDataBase.saveStrZoneTables(strZoneTable);
      },
      error: (error, loading) => error.toString(),
      loading: () {},
    );
  }

  Future<void> saveRecommendedLoad(
    List<RecommendedLoad> recommendedLoad,
  ) async {
    return state.when(
      data: (appDataBase) async {
        return await appDataBase.saveRecommendedLoad(recommendedLoad);
      },
      error: (error, loading) => error.toString(),
      loading: () {},
    );
  }

  Future<PileEndBearingBoredInput> queryPileEndBearingBoredInput() async {
    return state.when(
      data: (appDataBase) async {
        return await appDataBase.queryPileEndBearingBoredInput();
      },
      error: (error, loading) => PileEndBearingBoredInput(),
      loading: () => PileEndBearingBoredInput(),
    );
  }

  Future<void> savePileEndBearingBoredInput(
    PileEndBearingBoredInput pileEndBearingBoredInput,
  ) async {
    return state.when(
      data: (appDataBase) async {
        return await appDataBase.savePileEndBearingBoredInput(
          pileEndBearingBoredInput,
        );
      },
      error: (error, loading) => error.toString(),
      loading: () {},
    );
  }

  Future<List<PileEndBearingBoredData>> queryPileEndBearingBoredData() async {
    return state.when(
      data: (appDataBase) async {
        return await appDataBase.queryPileEndBearingBoredData();
      },
      error: (error, loading) => <PileEndBearingBoredData>[],
      loading: () => <PileEndBearingBoredData>[],
    );
  }

  Future<void> savePileEndBearingBoredData(
    List<PileEndBearingBoredData> pileEndBearingBoredData,
  ) async {
    return state.when(
      data: (appDataBase) async {
        return await appDataBase.savePileEndBearingBoredData(
          pileEndBearingBoredData,
        );
      },
      error: (error, loading) => error.toString(),
      loading: () {},
    );
  }

  Future<PileEndBearingBoredInputGlobal>
  queryPileEndBearingBoredInputGlobal() async {
    return state.when(
      data: (appDataBase) async {
        return await appDataBase.queryPileEndBearingBoredInputGlobal();
      },
      error: (error, loading) => PileEndBearingBoredInputGlobal(),
      loading: () => PileEndBearingBoredInputGlobal(),
    );
  }

  Future<void> savePileEndBearingBoredInputGlobal(
    PileEndBearingBoredInputGlobal pileEndBearingBoredInputGlobal,
  ) async {
    return state.when(
      data: (appDataBase) async {
        return await appDataBase.savePileEndBearingBoredInputGlobal(
          pileEndBearingBoredInputGlobal,
        );
      },
      error: (error, loading) => error.toString(),
      loading: () {},
    );
  }

  Future<PileFrictionalBoredInputGlobal>
  queryPileFrictionalBoredInputGlobal() async {
    return state.when(
      data: (appDataBase) async {
        return await appDataBase.queryPileFrictionalBoredInputGlobal();
      },
      error: (error, loading) => PileFrictionalBoredInputGlobal(),
      loading: () => PileFrictionalBoredInputGlobal(),
    );
  }

  Future<void> savePileFrictionalBoredInputGlobal(
    PileFrictionalBoredInputGlobal pileFrictionalBoredInputGlobal,
  ) async {
    return state.when(
      data: (appDataBase) async {
        return await appDataBase.savePileFrictionalBoredInputGlobal(
          pileFrictionalBoredInputGlobal,
        );
      },
      error: (error, loading) => error.toString(),
      loading: () {},
    );
  }

  Future<PileDrivenInputGlobal> queryPileDrivenInputGlobal() async {
    return state.when(
      data: (appDataBase) async {
        return await appDataBase.queryPileDrivenInputGlobal();
      },
      error: (error, loading) => PileDrivenInputGlobal(),
      loading: () => PileDrivenInputGlobal(),
    );
  }

  Future<void> savePileDrivenInputGlobal(
    PileDrivenInputGlobal pileDrivenInputGlobal,
  ) async {
    return state.when(
      data: (appDataBase) async {
        return await appDataBase.savePileDrivenInputGlobal(
          pileDrivenInputGlobal,
        );
      },
      error: (error, loading) => error.toString(),
      loading: () {},
    );
  }

  Future<FootingSchemeInputGlobal> queryFootingSchemeInputGlobal() async {
    return state.when(
      data: (appDataBase) async {
        return await appDataBase.queryFootingSchemeInputGlobal();
      },
      error: (error, loading) => FootingSchemeInputGlobal(),
      loading: () => FootingSchemeInputGlobal(),
    );
  }

  Future<void> saveFootingSchemeInputGlobal(
    FootingSchemeInputGlobal footingSchemeInputGlobal,
  ) async {
    return state.when(
      data: (appDataBase) async {
        return await appDataBase.saveFootingSchemeInputGlobal(
          footingSchemeInputGlobal,
        );
      },
      error: (error, loading) => error.toString(),
      loading: () {},
    );
  }

  Future<CantileverSchemeInputGlobal> queryCantileverSchemeInputGlobal() async {
    return state.when(
      data: (appDataBase) async {
        return await appDataBase.queryCantileverSchemeInputGlobal();
      },
      error: (error, loading) => CantileverSchemeInputGlobal(),
      loading: () => CantileverSchemeInputGlobal(),
    );
  }

  Future<void> saveCantileverSchemeInputGlobal(
    CantileverSchemeInputGlobal cantileverSchemeInputGlobal,
  ) async {
    return state.when(
      data: (appDataBase) async {
        return await appDataBase.saveCantileverSchemeInputGlobal(
          cantileverSchemeInputGlobal,
        );
      },
      error: (error, loading) => error.toString(),
      loading: () {},
    );
  }

  Future<List<CantileverSchemeInput>> queryCantileverSchemeInput() async {
    return state.when(
      data: (appDataBase) async {
        return await appDataBase.queryCantileverSchemeInput();
      },
      error: (error, loading) => [CantileverSchemeInput()],
      loading: () => [CantileverSchemeInput()],
    );
  }

  Future<void> saveCantileverSchemeInput(
    List<CantileverSchemeInput> cantileverSchemeInput,
  ) async {
    return state.when(
      data: (appDataBase) async {
        return await appDataBase.saveCantileverSchemeInput(
          cantileverSchemeInput,
        );
      },
      error: (error, loading) => error.toString(),
      loading: () {},
    );
  }

  Future<CantileverSchemeData> queryCantileverSchemeData() async {
    return state.when(
      data: (appDataBase) async {
        return await appDataBase.queryCantileverSchemeData();
      },
      error: (error, loading) => CantileverSchemeData(),
      loading: () => CantileverSchemeData(),
    );
  }

  Future<void> saveCantileverSchemeData(
    CantileverSchemeData cantileverSchemeData,
  ) async {
    return state.when(
      data: (appDataBase) async {
        return await appDataBase.saveCantileverSchemeData(cantileverSchemeData);
      },
      error: (error, loading) => error.toString(),
      loading: () {},
    );
  }

  Future<SteelCantileverTrussSchemeInputGlobal>
  querySteelCantileverTrussSchemeInputGlobal() async {
    return state.when(
      data: (appDataBase) async {
        return await appDataBase.querySteelCantileverTrussSchemeInputGlobal();
      },
      error: (error, loading) => SteelCantileverTrussSchemeInputGlobal(),
      loading: () => SteelCantileverTrussSchemeInputGlobal(),
    );
  }

  Future<void> saveSteelCantileverTrussSchemeInputGlobal(
    SteelCantileverTrussSchemeInputGlobal steelCantileverTrussSchemeInputGlobal,
  ) async {
    return state.when(
      data: (appDataBase) async {
        return await appDataBase.saveSteelCantileverTrussSchemeInputGlobal(
          steelCantileverTrussSchemeInputGlobal,
        );
      },
      error: (error, loading) => error.toString(),
      loading: () {},
    );
  }

  Future<List<SteelCantileverTrussSchemeInput>>
  querySteelCantileverTrussSchemeInput() async {
    return state.when(
      data: (appDataBase) async {
        return await appDataBase.querySteelCantileverTrussSchemeInput();
      },
      error: (error, loading) => [SteelCantileverTrussSchemeInput()],
      loading: () => [SteelCantileverTrussSchemeInput()],
    );
  }

  Future<void> saveSteelCantileverTrussSchemeInput(
    List<SteelCantileverTrussSchemeInput> steelCantileverTrussSchemeInput,
  ) async {
    return state.when(
      data: (appDataBase) async {
        return await appDataBase.saveSteelCantileverTrussSchemeInput(
          steelCantileverTrussSchemeInput,
        );
      },
      error: (error, loading) => error.toString(),
      loading: () {},
    );
  }

  Future<SteelCantileverTrussSchemeData>
  querySteelCantileverTrussSchemeData() async {
    return state.when(
      data: (appDataBase) async {
        return await appDataBase.querySteelCantileverTrussSchemeData();
      },
      error: (error, loading) => SteelCantileverTrussSchemeData(),
      loading: () => SteelCantileverTrussSchemeData(),
    );
  }

  Future<void> saveSteelCantileverTrussSchemeData(
    SteelCantileverTrussSchemeData steelCantileverTrussSchemeData,
  ) async {
    return state.when(
      data: (appDataBase) async {
        return await appDataBase.saveSteelCantileverTrussSchemeData(
          steelCantileverTrussSchemeData,
        );
      },
      error: (error, loading) => error.toString(),
      loading: () {},
    );
  }

  Future<BasementWallSchemeInput> queryBasementWallSchemeInput() async {
    return state.when(
      data: (appDataBase) async {
        return await appDataBase.queryBasementWallSchemeInput();
      },
      error: (error, loading) => BasementWallSchemeInput(),
      loading: () => BasementWallSchemeInput(),
    );
  }

  Future<void> saveBasementWallSchemeInput(
    BasementWallSchemeInput basementWallSchemeInput,
  ) async {
    return state.when(
      data: (appDataBase) async {
        return await appDataBase.saveBasementWallSchemeInput(
          basementWallSchemeInput,
        );
      },
      error: (error, loading) => error.toString(),
      loading: () {},
    );
  }

  Future<BasementWallSchemeData> queryBasementWallSchemeData() async {
    return state.when(
      data: (appDataBase) async {
        return await appDataBase.queryBasementWallSchemeData();
      },
      error: (error, loading) => BasementWallSchemeData(),
      loading: () => BasementWallSchemeData(),
    );
  }

  Future<void> saveBasementWallSchemeData(
    BasementWallSchemeData basementWallSchemeData,
  ) async {
    return state.when(
      data: (appDataBase) async {
        return await appDataBase.saveBasementWallSchemeData(
          basementWallSchemeData,
        );
      },
      error: (error, loading) => error.toString(),
      loading: () {},
    );
  }

  Future<List<ProgrammeItem>> queryAllProgrammeItems() async {
    return state.when(
      data: (appDataBase) async {
        return await appDataBase.queryAllProgrammeItems();
      },
      error: (error, loading) => [ProgrammeItem()],
      loading: () => [ProgrammeItem()],
    );
  }

  Future<void> saveProgrammeItems(List<ProgrammeItem> programmeItems) async {
    return state.when(
      data: (appDataBase) async {
        return await appDataBase.saveProgrammeItems(programmeItems);
      },
      error: (error, loading) => error.toString(),
      loading: () {},
    );
  }

  Future<List<WindLoad>> queryWindLoadInput() async {
    return state.when(
      data: (appDataBase) async {
        return await appDataBase.queryWindLoadInput();
      },
      error: (error, loading) => [WindLoad()],
      loading: () => [WindLoad()],
    );
  }

  Future<void> saveWindLoad(List<WindLoad> windLoad) async {
    return state.when(
      data: (appDataBase) async {
        return await appDataBase.saveAllWindLoad(windLoad);
      },
      error: (error, loading) => error.toString(),
      loading: () {},
    );
  }

  Future<WindLoadGlobal> queryWindLoadInputGlobal() async {
    return state.when(
      data: (appDataBase) async {
        return await appDataBase.queryWindLoadInputGlobal();
      },
      error: (error, loading) => WindLoadGlobal(),
      loading: () => WindLoadGlobal(),
    );
  }

  Future<void> saveWindLoadGlobal(WindLoadGlobal windLoadGlobal) async {
    return state.when(
      data: (appDataBase) async {
        return await appDataBase.saveWindLoadGlobal(windLoadGlobal);
      },
      error: (error, loading) => error.toString(),
      loading: () {},
    );
  }

  Future<List<Carbon>> queryCarbonInput() async {
    return state.when(
      data: (appDataBase) async {
        return await appDataBase.queryCarbonInput();
      },
      error: (error, loading) => [Carbon.create(carbonType: CarbonType.concreteArea , id: '')],
      loading: () => [Carbon.create(carbonType: CarbonType.concreteArea , id: '')],
    );
  }

  Future<void> saveCarbonInput(List<Carbon> carbon) async {
    return state.when(
      data: (appDataBase) async {
        return await appDataBase.saveCarbonInput(carbon);
      },
      error: (error, loading) => error.toString(),
      loading: () {},
    );
  }
}
