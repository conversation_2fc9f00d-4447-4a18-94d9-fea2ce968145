// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'cantilever_scheme_input_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$cantileverSchemeInputControllerHash() =>
    r'f7e61d57646adce4306cb9b097524cd64bd5d57c';

/// See also [CantileverSchemeInputController].
@ProviderFor(CantileverSchemeInputController)
final cantileverSchemeInputControllerProvider =
    AutoDisposeAsyncNotifierProvider<
      CantileverSchemeInputController,
      List<CantileverSchemeInput>
    >.internal(
      CantileverSchemeInputController.new,
      name: r'cantileverSchemeInputControllerProvider',
      debugGetCreateSourceHash:
          const bool.fromEnvironment('dart.vm.product')
              ? null
              : _$cantileverSchemeInputControllerHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$CantileverSchemeInputController =
    AutoDisposeAsyncNotifier<List<CantileverSchemeInput>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
