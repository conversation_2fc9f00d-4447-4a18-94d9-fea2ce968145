// import 'dart:nativewrappers/_internal/vm/lib/math_patch.dart';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

//below for printing
// import 'dart:typed_data';
// import 'dart:ui';

//domain layer

// presentation layer
import '../screen/homescreen.dart';
// import '../small_elements/custom_stateful_double_input.dart';
// import '../small_elements/custom_stateful_text_input.dart';
// import '../small_elements/global_data_ui.dart';

//misc

import 'button/selection_button.dart';

class PileSocketedSchemeSummary extends ConsumerStatefulWidget {
  const PileSocketedSchemeSummary({
    this.isExpanded,
    this.maxHeight,
    this.minHeight,
    super.key,
  });

  final bool? isExpanded;
  final double? maxHeight;
  final double? minHeight;

  @override
  ConsumerState<ConsumerStatefulWidget> createState() =>
      _PileSocketedSchemeSummaryState();
}

class _PileSocketedSchemeSummaryState
    extends ConsumerState<PileSocketedSchemeSummary>
    with WidgetsBindingObserver {
  late bool _isExpanded;
  late double _maxHeight;
  late double _minHeight;
  late ScrollController _scrollController;

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void initState() {
    WidgetsBinding.instance.addObserver(this);
    _isExpanded = widget.isExpanded ?? false;
    _maxHeight = widget.maxHeight ?? 300;
    _minHeight = widget.minHeight ?? 50;
    _scrollController = ScrollController();
    // _updateHeights();

    super.initState();
  }

  @override
  void didChangeDependencies() {
    _updateHeights();
    super.didChangeDependencies();
    // Update heights when dependencies change, including after initState
  }

  @override
  void didChangeMetrics() {
    // This method is called when the metrics change (like resizing the window)
    setState(() {
      _updateHeights();
    });
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;
    final globalData = ref.watch(globalDataControllerProvider);
    final pileSocketedScheme = ref.watch(pileSocketedDataControllerProvider);
    final TextStyle titleTextStyle = textTheme.titleMedium!.copyWith(
      color: colorScheme.onSurface,
    );
    final TextStyle bodyTextStyle = textTheme.bodyMedium!.copyWith(
      color: colorScheme.onSurface,
    );
    final TextStyle highlightText = textTheme.labelLarge!.copyWith(
      color: colorScheme.primary,
    );

    return globalData.when(
      data: (data) {
        return pileSocketedScheme.when(
          data: (scheme) {
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding: const EdgeInsets.fromLTRB(10, 0, 10, 0),
                  child: Divider(),
                ),
                Padding(
                  padding: const EdgeInsets.fromLTRB(10, 0, 0, 0),
                  child: Align(
                    alignment: Alignment.centerLeft,
                    child: Wrap(
                      crossAxisAlignment: WrapCrossAlignment.center,
                      children: [
                        IconButton(
                          icon: Icon(
                            _isExpanded ? Icons.expand_less : Icons.expand_more,
                          ),
                          color: colorScheme.onSurface,
                          onPressed: () {
                            setState(() {
                              _isExpanded = !_isExpanded;
                            });
                          },
                        ),
                        Text(
                          'Socketed Steel H Pile Scheme Summary ',
                          style: textTheme.titleLarge!.copyWith(
                            color: colorScheme.onSurface,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.fromLTRB(10, 0, 10, 0),
                  child: Divider(),
                ),

                ClipRect(
                  child: ConstrainedBox(
                    constraints: BoxConstraints(
                      maxHeight: _isExpanded ? _maxHeight : 0,
                    ),
                    child: Scrollbar(
                      controller: _scrollController,
                      thumbVisibility: true,
                      trackVisibility: false,
                      child: SingleChildScrollView(
                        scrollDirection: Axis.vertical,
                        controller: _scrollController,
                        child: Padding(
                          padding: const EdgeInsets.fromLTRB(10, 0, 0, 0),
                          child: DefaultTextStyle(
                            style: textTheme.bodyMedium!.copyWith(
                              color: colorScheme.onSurface,
                            ),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Row(
                                  children: [
                                    Text(
                                      'Select this Scheme?: ',
                                      style: titleTextStyle,
                                    ),
                                    SelectionButton(
                                      labelIcon: Icon(
                                        Icons.check,
                                        color:
                                            scheme.isSelected
                                                ? colorScheme.onTertiary
                                                : colorScheme.onSurface
                                                    .withAlpha(100),
                                      ),
                                      labelText: '',
                                      // pressedColor:
                                      //     colorScheme.tertiary,
                                      bgColor:
                                          scheme.isSelected
                                              ? colorScheme.tertiary
                                              : colorScheme.surfaceContainer
                                                  .withAlpha(100),

                                      onTap: (value) async {
                                        final scheme = await ref.read(
                                          pileSocketedDataControllerProvider
                                              .future,
                                        );
                                        await ref
                                            .read(
                                              pileSocketedDataControllerProvider
                                                  .notifier,
                                            )
                                            .toggleSelectScheme();
                                      },
                                    ),
                                  ],
                                ),
                                Row(
                                  children: [
                                    Text('Section: ', style: titleTextStyle),
                                    Text('UBP 305x305x223'),
                                  ],
                                ),
                                Row(
                                  children: [
                                    Text('Diameter: ', style: titleTextStyle),
                                    Text('610 mm'),
                                  ],
                                ),
                                Row(
                                  children: [
                                    Text('Length: ', style: titleTextStyle),
                                    Text('11.5 m'),
                                  ],
                                ),
                                Row(
                                  children: [
                                    Text(
                                      'fcu (grout): ',
                                      style: titleTextStyle,
                                    ),
                                    Text('45 MPa'),
                                  ],
                                ),
                                //* Pile Capacity
                                Divider(),
                                Text(
                                  'Pile Capacity (ULS Load):',
                                  style: titleTextStyle,
                                ),
                                Text('= Fy*A'),
                                Text('= 430*284*100/1000'),
                                Text(
                                  '= 12212 kN/pile',
                                  style: textTheme.labelLarge!.copyWith(
                                    color: colorScheme.error,
                                  ),
                                ),
                                Divider(),
                                Text(
                                  'Pile Capacity (SLS Load):',
                                  style: titleTextStyle,
                                ),
                                Text('= min(6106,6611,6955) '),
                                Text(
                                  '= 6106 kN/pile (see below cals)',
                                  style: textTheme.labelLarge!.copyWith(
                                    color: colorScheme.error,
                                  ),
                                ),

                                //* Structural Capacity
                                Text(
                                  'Structural Capacity Limit:',
                                  style: titleTextStyle,
                                ),
                                Text('0.5*Fy*A'),
                                Text('= 0.5*430*284*100/1000'),
                                Text('= 6106 kN/pile', style: highlightText),
                                Text(
                                  '(reduced Fy as 16 mm < max(tw,Tf) < 40 mm)',
                                ),
                                SizedBox(height: 10),
                                //* Shaft Capacity: Groutn to Rock
                                Text(
                                  'Shaft Capacity (grount and rock):',
                                  style: titleTextStyle,
                                ),
                                Text('\u03C3*\u03C0*D*L,'),
                                Text('= 300*\u03C0*0.61*11.5 '),
                                Text('= 6611 kN/pile', style: highlightText),
                                SizedBox(height: 10),
                                //* Shaft Capacity: Groutn to Steel
                                Text(
                                  'Shaft Capacity (grount and steel):',
                                  style: titleTextStyle,
                                ),
                                Text(
                                  '\u03C3*\u03C0*p*L, (p = surface area per meter)',
                                ),
                                Text('= 320*\u03C0*1.89*11.5'),
                                Text('= 6955 kN/pile', style: highlightText),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            );
          },
          error: (error, stackTrace) => Text(error.toString()),
          loading: () => CircularProgressIndicator(),
        );
      },
      error: (error, stackTrace) => Text(error.toString()),
      loading: () => CircularProgressIndicator(),
    );
  }

  // Method to update heights based on the current media query
  void _updateHeights() {
    final double screenHeight = MediaQuery.of(context).size.height;
    _maxHeight =
        (widget.maxHeight == null)
            ? screenHeight * 0.7
            : (widget.maxHeight! > screenHeight * 0.7)
            ? screenHeight * 0.7
            : widget.maxHeight!;

    _minHeight =
        (widget.minHeight == null)
            ? screenHeight * 0.1
            : (widget.minHeight! > screenHeight * 0.1)
            ? screenHeight * 0.1
            : widget.minHeight!;

    // Adjust _maxHeight if needed

    if (_maxHeight < _minHeight) {
      _maxHeight = _minHeight;
    }
  }
}
