// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'footing_scheme_input_global.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$FootingSchemeInputGlobal {

 String get id; double get colLoadFactor;
/// Create a copy of FootingSchemeInputGlobal
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$FootingSchemeInputGlobalCopyWith<FootingSchemeInputGlobal> get copyWith => _$FootingSchemeInputGlobalCopyWithImpl<FootingSchemeInputGlobal>(this as FootingSchemeInputGlobal, _$identity);

  /// Serializes this FootingSchemeInputGlobal to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is FootingSchemeInputGlobal&&(identical(other.id, id) || other.id == id)&&(identical(other.colLoadFactor, colLoadFactor) || other.colLoadFactor == colLoadFactor));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,colLoadFactor);

@override
String toString() {
  return 'FootingSchemeInputGlobal(id: $id, colLoadFactor: $colLoadFactor)';
}


}

/// @nodoc
abstract mixin class $FootingSchemeInputGlobalCopyWith<$Res>  {
  factory $FootingSchemeInputGlobalCopyWith(FootingSchemeInputGlobal value, $Res Function(FootingSchemeInputGlobal) _then) = _$FootingSchemeInputGlobalCopyWithImpl;
@useResult
$Res call({
 String id, double colLoadFactor
});




}
/// @nodoc
class _$FootingSchemeInputGlobalCopyWithImpl<$Res>
    implements $FootingSchemeInputGlobalCopyWith<$Res> {
  _$FootingSchemeInputGlobalCopyWithImpl(this._self, this._then);

  final FootingSchemeInputGlobal _self;
  final $Res Function(FootingSchemeInputGlobal) _then;

/// Create a copy of FootingSchemeInputGlobal
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? colLoadFactor = null,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,colLoadFactor: null == colLoadFactor ? _self.colLoadFactor : colLoadFactor // ignore: cast_nullable_to_non_nullable
as double,
  ));
}

}


/// Adds pattern-matching-related methods to [FootingSchemeInputGlobal].
extension FootingSchemeInputGlobalPatterns on FootingSchemeInputGlobal {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _FootingSchemeInputGlobal value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _FootingSchemeInputGlobal() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _FootingSchemeInputGlobal value)  $default,){
final _that = this;
switch (_that) {
case _FootingSchemeInputGlobal():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _FootingSchemeInputGlobal value)?  $default,){
final _that = this;
switch (_that) {
case _FootingSchemeInputGlobal() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  double colLoadFactor)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _FootingSchemeInputGlobal() when $default != null:
return $default(_that.id,_that.colLoadFactor);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  double colLoadFactor)  $default,) {final _that = this;
switch (_that) {
case _FootingSchemeInputGlobal():
return $default(_that.id,_that.colLoadFactor);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  double colLoadFactor)?  $default,) {final _that = this;
switch (_that) {
case _FootingSchemeInputGlobal() when $default != null:
return $default(_that.id,_that.colLoadFactor);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _FootingSchemeInputGlobal extends FootingSchemeInputGlobal {
   _FootingSchemeInputGlobal({this.id = '1', this.colLoadFactor = 1.0}): super._();
  factory _FootingSchemeInputGlobal.fromJson(Map<String, dynamic> json) => _$FootingSchemeInputGlobalFromJson(json);

@override@JsonKey() final  String id;
@override@JsonKey() final  double colLoadFactor;

/// Create a copy of FootingSchemeInputGlobal
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$FootingSchemeInputGlobalCopyWith<_FootingSchemeInputGlobal> get copyWith => __$FootingSchemeInputGlobalCopyWithImpl<_FootingSchemeInputGlobal>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$FootingSchemeInputGlobalToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _FootingSchemeInputGlobal&&(identical(other.id, id) || other.id == id)&&(identical(other.colLoadFactor, colLoadFactor) || other.colLoadFactor == colLoadFactor));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,colLoadFactor);

@override
String toString() {
  return 'FootingSchemeInputGlobal(id: $id, colLoadFactor: $colLoadFactor)';
}


}

/// @nodoc
abstract mixin class _$FootingSchemeInputGlobalCopyWith<$Res> implements $FootingSchemeInputGlobalCopyWith<$Res> {
  factory _$FootingSchemeInputGlobalCopyWith(_FootingSchemeInputGlobal value, $Res Function(_FootingSchemeInputGlobal) _then) = __$FootingSchemeInputGlobalCopyWithImpl;
@override @useResult
$Res call({
 String id, double colLoadFactor
});




}
/// @nodoc
class __$FootingSchemeInputGlobalCopyWithImpl<$Res>
    implements _$FootingSchemeInputGlobalCopyWith<$Res> {
  __$FootingSchemeInputGlobalCopyWithImpl(this._self, this._then);

  final _FootingSchemeInputGlobal _self;
  final $Res Function(_FootingSchemeInputGlobal) _then;

/// Create a copy of FootingSchemeInputGlobal
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? colLoadFactor = null,}) {
  return _then(_FootingSchemeInputGlobal(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,colLoadFactor: null == colLoadFactor ? _self.colLoadFactor : colLoadFactor // ignore: cast_nullable_to_non_nullable
as double,
  ));
}


}

// dart format on
