import 'package:riverpod_annotation/riverpod_annotation.dart';

//presentation layer
import '../screen/homescreen.dart';

// domain layer
import '../../domain_layer/slab_scheme_input.dart';

part 'slab_scheme_input_controller.g.dart';

@riverpod
class SlabSchemeInputController extends _$SlabSchemeInputController {
  @override
  FutureOr<SlabSchemeInput> build() async {
    // print('Build: Slab Scheme Input');
    SlabSchemeInput slabSchemeInput =
        await ref
            .read(appDatabaseControllerProvider.notifier)
            .querySlabSchemeInput();
    final data = ref.watch(loadingTablesControllerProvider);
    return data.when(
      data: (loadingTables) async {
        final usages = loadingTables.map((x) => x.usage).toList();
        if (!usages.contains(slabSchemeInput.usage)) {
          // if input's usage is deleted from loading table,
          // update input's usage to first usage then return again
          // to avoid error in its UI counterpart: usage dropdownlist

          // await updateTable(usage: usages[0]); // this won't work as infinite waiting results
          slabSchemeInput = slabSchemeInput.copyWith(usage: usages[0]);
        }
        return slabSchemeInput;
      },
      error: (error, stackTrace) => SlabSchemeInput(),
      loading: () => SlabSchemeInput(),
    );
  }

  // final globalData =
  //     ref.read(appDatabaseControllerProvider.notifier).queryGlobalData();
  // return globalData;

  // Future<void> addEmptytable() async {
  //   final x = await future;
  //   final id = await _generateTaskID();
  //   state = AsyncData([...x, LoadingTable(loadingTableId: id)]);
  // }

  // Future<void> deleteTable(String id) async {
  //   // print("Before deletion: ${state.map((item) => item.usage).toList()}");
  //   final x = await future;
  //   x.removeWhere((item) => item.loadingTableId == id);
  //   // print("After deletion: ${x.map((item) => item.usage).toList()}");
  //   state = AsyncData(x);
  // }

  // Future<void> insertEmptyTable(int index) async {
  //   final x = await future;
  //   final id = await _generateTaskID();
  //   x.insert(index+1, LoadingTable(loadingTableId: id));
  //   state = AsyncData(x);
  // }

  // Future<void> copyAndInsertTable(int index) async {
  //   final x = await future;
  //   final id = await _generateTaskID();
  //   x.insert(index+1, x[index].copyWith(loadingTableId: id));
  //   state = AsyncData(x);
  // }

  Future<void> updateTable({
    String? id,
    double? span,
    double? fcu,
    double? cover,
    double? mainKValue,
    double? mainSteelRatio,
    int? minS,
    int? maxS,
    double? maxDepth,
    double? minDepth,
    int? maxLayers,
    double? spanIncreament,
    int? iterationSteps,
    String? usage,
  }) async {
    final x = await future;
    // Create a list of updates
    final newState = x.copyWith(
      id: id ?? x.id,
      span: span ?? x.span,
      fcu: fcu ?? x.fcu,
      cover: cover ?? x.cover,
      mainKValue: mainKValue ?? x.mainKValue,
      mainSteelRatio: mainSteelRatio ?? x.mainSteelRatio,
      minS: minS ?? x.minS,
      maxS: maxS ?? x.maxS,
      maxDepth: maxDepth ?? x.maxDepth,
      minDepth: minDepth ?? x.minDepth,
      maxLayers: maxLayers ?? x.maxLayers,
      spanIncreament: spanIncreament ?? x.spanIncreament,
      iterationSteps: iterationSteps ?? x.iterationSteps,
      usage: usage ?? x.usage,
    );
    // Update the state only if there are changes
    state = AsyncData(newState);
  }
}
