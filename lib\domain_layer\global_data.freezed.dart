// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'global_data.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$GlobalData {

 String get unit; double get sdlFactor; double get llFactor; double get rcUnitWeight; double get finishUnitWeight; double get steelUnitWeight; double get soilUnitWeight; double get waterUnitWeight;
/// Create a copy of GlobalData
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$GlobalDataCopyWith<GlobalData> get copyWith => _$GlobalDataCopyWithImpl<GlobalData>(this as GlobalData, _$identity);

  /// Serializes this GlobalData to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is GlobalData&&(identical(other.unit, unit) || other.unit == unit)&&(identical(other.sdlFactor, sdlFactor) || other.sdlFactor == sdlFactor)&&(identical(other.llFactor, llFactor) || other.llFactor == llFactor)&&(identical(other.rcUnitWeight, rcUnitWeight) || other.rcUnitWeight == rcUnitWeight)&&(identical(other.finishUnitWeight, finishUnitWeight) || other.finishUnitWeight == finishUnitWeight)&&(identical(other.steelUnitWeight, steelUnitWeight) || other.steelUnitWeight == steelUnitWeight)&&(identical(other.soilUnitWeight, soilUnitWeight) || other.soilUnitWeight == soilUnitWeight)&&(identical(other.waterUnitWeight, waterUnitWeight) || other.waterUnitWeight == waterUnitWeight));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,unit,sdlFactor,llFactor,rcUnitWeight,finishUnitWeight,steelUnitWeight,soilUnitWeight,waterUnitWeight);

@override
String toString() {
  return 'GlobalData(unit: $unit, sdlFactor: $sdlFactor, llFactor: $llFactor, rcUnitWeight: $rcUnitWeight, finishUnitWeight: $finishUnitWeight, steelUnitWeight: $steelUnitWeight, soilUnitWeight: $soilUnitWeight, waterUnitWeight: $waterUnitWeight)';
}


}

/// @nodoc
abstract mixin class $GlobalDataCopyWith<$Res>  {
  factory $GlobalDataCopyWith(GlobalData value, $Res Function(GlobalData) _then) = _$GlobalDataCopyWithImpl;
@useResult
$Res call({
 String unit, double sdlFactor, double llFactor, double rcUnitWeight, double finishUnitWeight, double steelUnitWeight, double soilUnitWeight, double waterUnitWeight
});




}
/// @nodoc
class _$GlobalDataCopyWithImpl<$Res>
    implements $GlobalDataCopyWith<$Res> {
  _$GlobalDataCopyWithImpl(this._self, this._then);

  final GlobalData _self;
  final $Res Function(GlobalData) _then;

/// Create a copy of GlobalData
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? unit = null,Object? sdlFactor = null,Object? llFactor = null,Object? rcUnitWeight = null,Object? finishUnitWeight = null,Object? steelUnitWeight = null,Object? soilUnitWeight = null,Object? waterUnitWeight = null,}) {
  return _then(_self.copyWith(
unit: null == unit ? _self.unit : unit // ignore: cast_nullable_to_non_nullable
as String,sdlFactor: null == sdlFactor ? _self.sdlFactor : sdlFactor // ignore: cast_nullable_to_non_nullable
as double,llFactor: null == llFactor ? _self.llFactor : llFactor // ignore: cast_nullable_to_non_nullable
as double,rcUnitWeight: null == rcUnitWeight ? _self.rcUnitWeight : rcUnitWeight // ignore: cast_nullable_to_non_nullable
as double,finishUnitWeight: null == finishUnitWeight ? _self.finishUnitWeight : finishUnitWeight // ignore: cast_nullable_to_non_nullable
as double,steelUnitWeight: null == steelUnitWeight ? _self.steelUnitWeight : steelUnitWeight // ignore: cast_nullable_to_non_nullable
as double,soilUnitWeight: null == soilUnitWeight ? _self.soilUnitWeight : soilUnitWeight // ignore: cast_nullable_to_non_nullable
as double,waterUnitWeight: null == waterUnitWeight ? _self.waterUnitWeight : waterUnitWeight // ignore: cast_nullable_to_non_nullable
as double,
  ));
}

}


/// Adds pattern-matching-related methods to [GlobalData].
extension GlobalDataPatterns on GlobalData {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _GlobalData value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _GlobalData() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _GlobalData value)  $default,){
final _that = this;
switch (_that) {
case _GlobalData():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _GlobalData value)?  $default,){
final _that = this;
switch (_that) {
case _GlobalData() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String unit,  double sdlFactor,  double llFactor,  double rcUnitWeight,  double finishUnitWeight,  double steelUnitWeight,  double soilUnitWeight,  double waterUnitWeight)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _GlobalData() when $default != null:
return $default(_that.unit,_that.sdlFactor,_that.llFactor,_that.rcUnitWeight,_that.finishUnitWeight,_that.steelUnitWeight,_that.soilUnitWeight,_that.waterUnitWeight);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String unit,  double sdlFactor,  double llFactor,  double rcUnitWeight,  double finishUnitWeight,  double steelUnitWeight,  double soilUnitWeight,  double waterUnitWeight)  $default,) {final _that = this;
switch (_that) {
case _GlobalData():
return $default(_that.unit,_that.sdlFactor,_that.llFactor,_that.rcUnitWeight,_that.finishUnitWeight,_that.steelUnitWeight,_that.soilUnitWeight,_that.waterUnitWeight);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String unit,  double sdlFactor,  double llFactor,  double rcUnitWeight,  double finishUnitWeight,  double steelUnitWeight,  double soilUnitWeight,  double waterUnitWeight)?  $default,) {final _that = this;
switch (_that) {
case _GlobalData() when $default != null:
return $default(_that.unit,_that.sdlFactor,_that.llFactor,_that.rcUnitWeight,_that.finishUnitWeight,_that.steelUnitWeight,_that.soilUnitWeight,_that.waterUnitWeight);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _GlobalData extends GlobalData {
   _GlobalData({this.unit = 'metrics', this.sdlFactor = 1.4, this.llFactor = 1.6, this.rcUnitWeight = 24.5, this.finishUnitWeight = 24.0, this.steelUnitWeight = 78.5, this.soilUnitWeight = 19.0, this.waterUnitWeight = 10.0}): super._();
  factory _GlobalData.fromJson(Map<String, dynamic> json) => _$GlobalDataFromJson(json);

@override@JsonKey() final  String unit;
@override@JsonKey() final  double sdlFactor;
@override@JsonKey() final  double llFactor;
@override@JsonKey() final  double rcUnitWeight;
@override@JsonKey() final  double finishUnitWeight;
@override@JsonKey() final  double steelUnitWeight;
@override@JsonKey() final  double soilUnitWeight;
@override@JsonKey() final  double waterUnitWeight;

/// Create a copy of GlobalData
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$GlobalDataCopyWith<_GlobalData> get copyWith => __$GlobalDataCopyWithImpl<_GlobalData>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$GlobalDataToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _GlobalData&&(identical(other.unit, unit) || other.unit == unit)&&(identical(other.sdlFactor, sdlFactor) || other.sdlFactor == sdlFactor)&&(identical(other.llFactor, llFactor) || other.llFactor == llFactor)&&(identical(other.rcUnitWeight, rcUnitWeight) || other.rcUnitWeight == rcUnitWeight)&&(identical(other.finishUnitWeight, finishUnitWeight) || other.finishUnitWeight == finishUnitWeight)&&(identical(other.steelUnitWeight, steelUnitWeight) || other.steelUnitWeight == steelUnitWeight)&&(identical(other.soilUnitWeight, soilUnitWeight) || other.soilUnitWeight == soilUnitWeight)&&(identical(other.waterUnitWeight, waterUnitWeight) || other.waterUnitWeight == waterUnitWeight));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,unit,sdlFactor,llFactor,rcUnitWeight,finishUnitWeight,steelUnitWeight,soilUnitWeight,waterUnitWeight);

@override
String toString() {
  return 'GlobalData(unit: $unit, sdlFactor: $sdlFactor, llFactor: $llFactor, rcUnitWeight: $rcUnitWeight, finishUnitWeight: $finishUnitWeight, steelUnitWeight: $steelUnitWeight, soilUnitWeight: $soilUnitWeight, waterUnitWeight: $waterUnitWeight)';
}


}

/// @nodoc
abstract mixin class _$GlobalDataCopyWith<$Res> implements $GlobalDataCopyWith<$Res> {
  factory _$GlobalDataCopyWith(_GlobalData value, $Res Function(_GlobalData) _then) = __$GlobalDataCopyWithImpl;
@override @useResult
$Res call({
 String unit, double sdlFactor, double llFactor, double rcUnitWeight, double finishUnitWeight, double steelUnitWeight, double soilUnitWeight, double waterUnitWeight
});




}
/// @nodoc
class __$GlobalDataCopyWithImpl<$Res>
    implements _$GlobalDataCopyWith<$Res> {
  __$GlobalDataCopyWithImpl(this._self, this._then);

  final _GlobalData _self;
  final $Res Function(_GlobalData) _then;

/// Create a copy of GlobalData
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? unit = null,Object? sdlFactor = null,Object? llFactor = null,Object? rcUnitWeight = null,Object? finishUnitWeight = null,Object? steelUnitWeight = null,Object? soilUnitWeight = null,Object? waterUnitWeight = null,}) {
  return _then(_GlobalData(
unit: null == unit ? _self.unit : unit // ignore: cast_nullable_to_non_nullable
as String,sdlFactor: null == sdlFactor ? _self.sdlFactor : sdlFactor // ignore: cast_nullable_to_non_nullable
as double,llFactor: null == llFactor ? _self.llFactor : llFactor // ignore: cast_nullable_to_non_nullable
as double,rcUnitWeight: null == rcUnitWeight ? _self.rcUnitWeight : rcUnitWeight // ignore: cast_nullable_to_non_nullable
as double,finishUnitWeight: null == finishUnitWeight ? _self.finishUnitWeight : finishUnitWeight // ignore: cast_nullable_to_non_nullable
as double,steelUnitWeight: null == steelUnitWeight ? _self.steelUnitWeight : steelUnitWeight // ignore: cast_nullable_to_non_nullable
as double,soilUnitWeight: null == soilUnitWeight ? _self.soilUnitWeight : soilUnitWeight // ignore: cast_nullable_to_non_nullable
as double,waterUnitWeight: null == waterUnitWeight ? _self.waterUnitWeight : waterUnitWeight // ignore: cast_nullable_to_non_nullable
as double,
  ));
}


}

// dart format on
