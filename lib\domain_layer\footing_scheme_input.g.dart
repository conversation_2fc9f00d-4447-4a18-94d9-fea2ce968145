// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'footing_scheme_input.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_FootingSchemeInput _$FootingSchemeInputFromJson(Map<String, dynamic> json) =>
    _FootingSchemeInput(
      id: json['id'] as String? ?? '1',
      fcu: (json['fcu'] as num?)?.toDouble() ?? 45.0,
      cover: (json['cover'] as num?)?.toDouble() ?? 75.0,
      mainKValue: (json['mainKValue'] as num?)?.toDouble() ?? 0.156,
      mainSteelRatio: (json['mainSteelRatio'] as num?)?.toDouble() ?? 0.04,
      minS: (json['minS'] as num?)?.toInt() ?? 100,
      maxS: (json['maxS'] as num?)?.toInt() ?? 300,
      maxDepth: (json['maxDepth'] as num?)?.toDouble() ?? 2000,
      minDepth: (json['minDepth'] as num?)?.toDouble() ?? 500.0,
      maxLayers: (json['maxLayers'] as num?)?.toInt() ?? 4,
      groundType: json['groundType'] as String? ?? 'Soil',
      soilNValue: (json['soilNValue'] as num?)?.toDouble() ?? 50,
      footingTopLevel: (json['footingTopLevel'] as num?)?.toDouble() ?? 0,
      waterTableLevel: (json['waterTableLevel'] as num?)?.toDouble() ?? -3,
      rockCapacity: (json['rockCapacity'] as num?)?.toDouble() ?? 1000,
      colShape: json['colShape'] as String? ?? 'Square',
      columnSize: (json['columnSize'] as num?)?.toDouble() ?? 1000.0,
      slsLoad: (json['slsLoad'] as num?)?.toDouble() ?? 1500.0,
      ulsLoad: (json['ulsLoad'] as num?)?.toDouble() ?? 3000.0,
      useSelectColLoad: json['useSelectColLoad'] as bool? ?? false,
    );

Map<String, dynamic> _$FootingSchemeInputToJson(_FootingSchemeInput instance) =>
    <String, dynamic>{
      'id': instance.id,
      'fcu': instance.fcu,
      'cover': instance.cover,
      'mainKValue': instance.mainKValue,
      'mainSteelRatio': instance.mainSteelRatio,
      'minS': instance.minS,
      'maxS': instance.maxS,
      'maxDepth': instance.maxDepth,
      'minDepth': instance.minDepth,
      'maxLayers': instance.maxLayers,
      'groundType': instance.groundType,
      'soilNValue': instance.soilNValue,
      'footingTopLevel': instance.footingTopLevel,
      'waterTableLevel': instance.waterTableLevel,
      'rockCapacity': instance.rockCapacity,
      'colShape': instance.colShape,
      'columnSize': instance.columnSize,
      'slsLoad': instance.slsLoad,
      'ulsLoad': instance.ulsLoad,
      'useSelectColLoad': instance.useSelectColLoad,
    };
