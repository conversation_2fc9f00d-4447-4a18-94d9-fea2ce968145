import 'package:riverpod_annotation/riverpod_annotation.dart';

//presentation layer
import '../../domain_layer/column_scheme_data.dart';
import '../../domain_layer/pile_frictional_bored_input.dart';
import '../../domain_layer/pile_frictional_bored_input_global.dart';
import '../screen/homescreen.dart';

// domain layer

part 'pile_frictional_bored_input_global_controller.g.dart';

@riverpod
class PileFrictionalBoredInputGlobalController
    extends _$PileFrictionalBoredInputGlobalController {
  @override
  FutureOr<PileFrictionalBoredInputGlobal> build() async {
    // print('Build: Column Scheme Input Global');
    PileFrictionalBoredInputGlobal pileFrictionalBoredInputGlobal =
        await ref
            .read(appDatabaseControllerProvider.notifier)
            .queryPileFrictionalBoredInputGlobal();
    return pileFrictionalBoredInputGlobal;
  }

  Future<void> updateTable({double? colLoadFactor}) async {
    final x = await future;
    PileFrictionalBoredInputGlobal newState = x.copyWith(
      colLoadFactor: colLoadFactor ?? x.colLoadFactor,
      id: '1',
    );
    // Update the state only if there are changes
    state = AsyncData(newState);
  }
  
}
