import 'dart:math' as math;
import 'dart:ui' as ui;

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:structify/domain_layer/slab_scheme_data.dart';
import 'package:structify/presentation_layer/screen/homescreen.dart';
import 'package:vector_math/vector_math.dart' as vec;

import '../../../domain_layer/footing_scheme_data.dart';
import '../../../domain_layer/global_data.dart';
import '../../../domain_layer/preferences.dart';

class DrawFootingSchemeSketch extends ConsumerWidget {
  DrawFootingSchemeSketch({
    required this.sketchWidth,
    required this.sketchHeight,
    required this.index,
    this.fontSize,
    super.key,
  });

  final int index;
  final double sketchWidth;
  final double sketchHeight;
  double? fontSize;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final data1 = ref.watch(footingSchemeDataControllerProvider);
    final data2 = ref.watch(globalDataControllerProvider);
    final ColorScheme colorScheme = Theme.of(context).colorScheme;
    final TextTheme textTheme = Theme.of(context).textTheme;

    fontSize = fontSize ?? math.min(sketchWidth, sketchHeight) / 15;
    return data1.when(
      data: (footingSchemedata) {
        return data2.when(
          data: (globalData) {
            final constraints = BoxConstraints(
              maxWidth: sketchWidth,
              maxHeight: sketchHeight,
              minHeight: 100,
              minWidth: 100,
            );
            return Center(
              child: Container(
                decoration: BoxDecoration(
                  color: colorScheme.surfaceContainer,
                  borderRadius: BorderRadius.circular(10.0),
                  border: Border.all(color: Colors.black.withAlpha(100)),
                ),
                width:
                    constraints.maxWidth == double.infinity
                        ? 100
                        : constraints.maxWidth,
                height:
                    constraints.maxHeight == double.infinity
                        ? 100
                        : constraints.maxHeight,
                child: CustomPaint(
                  painter: DrawFootingSchemeSketchPainter(
                    footingSchemedata: footingSchemedata[index],
                    globalData: globalData,
                    boxConstraints: constraints,
                    fontSize: fontSize,
                    context: context,
                  ),
                ),
              ),
            );
          },

          error: (error, stackTrace) {
            return Text(error.toString());
          },
          loading: () {
            return const CircularProgressIndicator();
          },
        );
      },
      error: (error, stackTrace) {
        return Text(error.toString());
      },
      loading: () {
        return const CircularProgressIndicator();
      },
    );
  }
}

class DrawFootingSchemeSketchPainter extends CustomPainter {
  final FootingSchemeData footingSchemedata;
  final GlobalData globalData;
  final BoxConstraints boxConstraints;
  double? fontSize;
  final BuildContext context;

  DrawFootingSchemeSketchPainter({
    required this.footingSchemedata,
    required this.globalData,
    required this.boxConstraints,
    this.fontSize,
    required this.context,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final ColorScheme colorScheme = Theme.of(context).colorScheme;
    final TextTheme textTheme = Theme.of(context).textTheme;
    late final double secBeamSpacing;

    fontSize = fontSize ?? math.min(size.width, size.height) / 40;

    Paint slabPaint =
        Paint()
          ..color = colorScheme.onSurface
          ..strokeCap = StrokeCap.round
          ..style = PaintingStyle.stroke
          ..strokeWidth = 2.0;

    Paint rebarPaint =
        Paint()
          ..color = colorScheme.onSurface
          ..strokeCap = StrokeCap.round
          ..style = PaintingStyle.stroke
          ..strokeWidth = 2.0;

    final double minDim = math.min(
      boxConstraints.maxWidth,
      boxConstraints.maxHeight,
    );
    final double ratio =
        minDim /
        math.max(footingSchemedata.strZone, footingSchemedata.strZone * 2) *
        4 /
        5;

    late Offset startP, endP, startP2, endP2, startP3, endP3;
    Path path = Path();
    // ******************
    // draw slab
    // ******************
    startP = Offset(
      0.5 * (boxConstraints.maxWidth - footingSchemedata.strZone * 2 * ratio),
      0.5 * (boxConstraints.maxHeight - footingSchemedata.strZone * ratio),
    );
    path.moveTo(startP.dx, startP.dy);
    endP = startP + Offset(footingSchemedata.strZone * 2 * ratio, 0);
    path.lineTo(endP.dx, endP.dy);
    canvas.drawPath(path, slabPaint);

    startP2 = startP + Offset(0, footingSchemedata.strZone * ratio);
    endP2 = startP2 + Offset(footingSchemedata.strZone * 2 * ratio, 0);
    path.moveTo(startP2.dx, startP2.dy);
    path.lineTo(endP2.dx, endP2.dy);
    canvas.drawPath(path, slabPaint);

    _drawDimLine(
      canvas,
      fontSize!,
      startP,
      startP2,
      0,
      fontSize! / 2,
      footingSchemedata.strZone,
      globalData,
      context,
    );

    // ******************
    // draw rebars
    // ******************

    final botBar = footingSchemedata.mainBottomBar;
    final topBar = footingSchemedata.mainTopBar;
    final links = footingSchemedata.mainLinks;

    int spacerBar = 16;
    late List<int> rebarDia = [];
    late List<int> rebarSpacing = [];
    final RegExp regex1 = RegExp(r"(?<=T)\d+");
    final RegExp regex2 = RegExp(r"(?<=-)\d+");
    final Color fontColor = Colors.red.withAlpha(200);

    // -------------------
    // Draw Bottom bar
    // -------------------
    rebarDia =
        regex1.allMatches(botBar).map((e) {
          return int.parse(e.group(0)!);
        }).toList();

    rebarSpacing =
        regex2.allMatches(botBar).map((e) => int.parse(e.group(0)!)).toList();
    for (int i = 0; i < rebarDia.length; i++) {
      final nosOfRebar =
          ((endP2.dx - startP2.dx) / (rebarSpacing[i] * ratio)).floor();
      for (int j = 0; j < nosOfRebar; j++) {
        startP3 =
            startP2 +
            Offset(
              j * rebarSpacing[i] * ratio,
              -(footingSchemedata.cover + rebarDia[i] / 2) * ratio,
            );

        if (i > 0) {
          for (int j1 = 0; j1 < i; j1++) {
            startP3 = startP3 - Offset(0, (rebarDia[j1] + spacerBar) * ratio);
          }
        }
        canvas.drawCircle(startP3, rebarDia[i] / 2 * ratio, rebarPaint);
      }
      TextSpan textSpan = TextSpan(
          text: 'T${rebarDia[i]}-${rebarSpacing[i]}',
          style: TextStyle(
            fontSize: fontSize,
            color: fontColor,
            fontWeight: FontWeight.bold,
          ),
        );

        TextPainter textPainter = TextPainter(
          text: textSpan,
          textAlign: TextAlign.center,
          textDirection: TextDirection.ltr,
        )..layout();

        textPainter.paint(
          canvas,
          endP2 +
              Offset(- textPainter.width*2, -size.height*0.2 - i * textPainter.size.height*3 ) *
                  ratio,
        );
    }
    
    // -------------------
    // Draw Top bar
    // -------------------
    rebarDia =
        regex1.allMatches(topBar).map((e) {
          return int.parse(e.group(0)!);
        }).toList();

    rebarSpacing =
        regex2.allMatches(botBar).map((e) => int.parse(e.group(0)!)).toList();
    for (int i = 0; i < rebarDia.length; i++) {
      final nosOfRebar =
          ((endP.dx - startP.dx)  / (rebarSpacing[i] * ratio)).floor();
      for (int j = 0; j < nosOfRebar; j++) {
        startP3 =
            startP +
            Offset(
              j * rebarSpacing[i] * ratio,
              (footingSchemedata.cover - rebarDia[i] / 2) * ratio,
            );
        if (i > 0) {
          for (int j1 = 0; j1 < i; j1++) {
            startP3 = startP3 + Offset(0, (rebarDia[j1] + spacerBar) * ratio);
          }
        }
        canvas.drawCircle(startP3, rebarDia[i] / 2 * ratio, rebarPaint);
      }
      TextSpan textSpan = TextSpan(
          text: 'T${rebarDia[i]}-${rebarSpacing[i]}',
          style: TextStyle(
            fontSize: fontSize,
            color: fontColor,
            fontWeight: FontWeight.bold,
          ),
        );

        TextPainter textPainter = TextPainter(
          text: textSpan,
          textAlign: TextAlign.center,
          textDirection: TextDirection.ltr,
        )..layout();

        textPainter.paint(
          canvas,
          startP  +
              Offset(0, size.height*0.1 + i * textPainter.size.height*3 ) *
                  ratio,
        );
    }



    // for (int i = 0; i < rebarDia.length; i++) {
    //   startP3 =
    //       startP +
    //       Offset(0, (footingSchemedata.cover + rebarDia[i] / 2) * ratio) +
    //       Offset(
    //         (footingSchemedata.strZone * 2 - rebarSpacing[i]) / 2 * ratio,
    //         0,
    //       );
    //   if (i > 0) {
    //     for (int j = 0; j < i; j++) {
    //       startP3 = startP3 - Offset(0, (rebarDia[j] + spacerBar) * ratio);
    //     }
    //   }
    //   endP3 = startP3 + Offset(rebarSpacing[i] * ratio, 0);
    //   canvas.drawCircle(startP3, rebarDia[i] / 2 * ratio, rebarPaint);
    //   canvas.drawCircle(endP3, rebarDia[i] / 2 * ratio, rebarPaint);

    //   TextSpan textSpan = TextSpan(
    //     text: 'T${rebarDia[i]}-${rebarSpacing[i]}',
    //     style: TextStyle(
    //       fontSize: fontSize,
    //       color: fontColor,
    //       fontWeight: FontWeight.bold,
    //     ),
    //   );

    //   TextPainter textPainter = TextPainter(
    //     text: textSpan,
    //     textAlign: TextAlign.center,
    //     textDirection: TextDirection.ltr,
    //   )..layout();

    //   textPainter.paint(
    //     canvas,
    //     endP3 + Offset(10, -15 - i * textPainter.size.height / 2) * ratio,
    //   );
    // }
    // -------------------
    // Draw Links
    // -------------------
    // for simplicity, jsut show the designation
    TextSpan textSpan = TextSpan(
      text: 'Links:\n${footingSchemedata.mainLinks}',
      style: TextStyle(
        fontSize: fontSize,
        color: fontColor,
        fontWeight: FontWeight.bold,
      ),
    );

    TextPainter textPainter = TextPainter(
      text: textSpan,
      textAlign: TextAlign.center,
      textDirection: TextDirection.ltr,
    )..layout();

    textPainter.paint(canvas, startP2 + Offset(0, 0));
  }

  void _rebarDesignation(
    List<int> rebarDia,
    int i,
    List<int> rebarSpacing,
    ui.Color fontColor,
    ui.Canvas canvas,
    ui.Offset endP3,
  ) {
    TextSpan textSpan = TextSpan(
      text: 'T${rebarDia[i]}-${rebarSpacing[i]}',
      style: TextStyle(
        fontSize: fontSize,
        color: fontColor,
        fontWeight: FontWeight.bold,
      ),
    );

    TextPainter textPainter = TextPainter(
      text: textSpan,
      textAlign: TextAlign.center,
      textDirection: TextDirection.ltr,
    )..layout();

    textPainter.paint(canvas, endP3 + Offset(7, -7));
  }

  void _drawDimLine(
    Canvas canvas,
    double fontSize,
    Offset start,
    Offset end,
    double offsetDistance,
    double dimExtension,
    double slabDepth,
    GlobalData globalData,
    BuildContext context,
  ) {
    double tempOffset = 10; // forthe dim line end
    late Offset tempEnd;
    late Offset tempStart;
    late Offset textCenter;

    late double angle;

    late List<String> unit;

    Color fontColor = Colors.red.withAlpha(200);

    late final vec.Vector2 u;
    late final vec.Vector2 uUnit;
    late final vec.Vector2 vUnit;
    late final vec.Vector2 vUnit45;
    late final vec.Matrix2 m;

    final ColorScheme colorScheme = Theme.of(context).colorScheme;
    final TextTheme textTheme = Theme.of(context).textTheme;

    // get the unit
    switch (globalData.unit) {
      case 'metrics':
        unit = PreferredUnit.metrics;
        break;
      case 'imperial':
        unit = PreferredUnit.imperial;
        break;
      default:
        unit = PreferredUnit.metrics;
    }

    // double dimExtension = offsetDistance.abs() / 2;

    final Paint paint =
        Paint()
          ..color = fontColor
          ..strokeWidth = 1.0
          ..style = PaintingStyle.stroke
          ..strokeJoin = StrokeJoin.round;

    // line vector
    u = vec.Vector2(end.dx - start.dx, end.dy - start.dy);
    uUnit = u / u.length;

    // orthogonal vector
    vUnit = vec.Vector2(uUnit.y, uUnit.x); //rotate aniti-clockwise

    // Vector of parallel line with Offset
    Offset newStart = Offset(
      start.dx - vUnit.x * offsetDistance - uUnit.x * dimExtension,
      start.dy - vUnit.y * offsetDistance - uUnit.y * dimExtension,
    );
    Offset newEnd = Offset(
      end.dx - vUnit.x * offsetDistance + uUnit.x * dimExtension,
      end.dy - vUnit.y * offsetDistance + uUnit.y * dimExtension,
    );
    canvas.drawLine(newStart, newEnd, paint);

    // draw start crossing
    tempStart = Offset(
      (start.dx - vUnit.x * offsetDistance) - vUnit.x * dimExtension,
      (start.dy - vUnit.y * offsetDistance) - vUnit.y * dimExtension,
    );
    tempEnd = Offset(
      (start.dx - vUnit.x * offsetDistance) + vUnit.x * dimExtension,
      (start.dy - vUnit.y * offsetDistance) + vUnit.y * dimExtension,
    );
    canvas.drawLine(tempStart, tempEnd, paint);

    m = vec.Matrix2.rotation(-45 * math.pi / 180);
    vUnit45 = m * vUnit;
    tempStart = Offset(
      (start.dx - vUnit.x * offsetDistance) - vUnit45.x * dimExtension,
      (start.dy - vUnit.y * offsetDistance) - vUnit45.y * dimExtension,
    );
    tempEnd = Offset(
      (start.dx - vUnit.x * offsetDistance) + vUnit45.x * dimExtension,
      (start.dy - vUnit.y * offsetDistance) + vUnit45.y * dimExtension,
    );
    canvas.drawLine(tempStart, tempEnd, paint);

    // draw end crossing
    tempStart = Offset(
      (end.dx - vUnit.x * offsetDistance) - vUnit.x * dimExtension,
      (end.dy - vUnit.y * offsetDistance) - vUnit.y * dimExtension,
    );
    tempEnd = Offset(
      (end.dx - vUnit.x * offsetDistance) + vUnit.x * dimExtension,
      (end.dy - vUnit.y * offsetDistance) + vUnit.y * dimExtension,
    );
    canvas.drawLine(tempStart, tempEnd, paint);

    tempStart = Offset(
      (end.dx - vUnit.x * offsetDistance) - vUnit45.x * dimExtension,
      (end.dy - vUnit.y * offsetDistance) - vUnit45.y * dimExtension,
    );
    tempEnd = Offset(
      (end.dx - vUnit.x * offsetDistance) + vUnit45.x * dimExtension,
      (end.dy - vUnit.y * offsetDistance) + vUnit45.y * dimExtension,
    );
    canvas.drawLine(tempStart, tempEnd, paint);

    // indicate span length and beam size

    TextSpan textSpan = TextSpan(
      text: slabDepth.toStringAsFixed(0),
      style: TextStyle(
        fontSize: fontSize,
        color: fontColor,
        fontWeight: FontWeight.bold,
      ),
    );

    TextPainter textPainter = TextPainter(
      text: textSpan,
      textAlign: TextAlign.center,
      textDirection: TextDirection.ltr,
    )..layout();

    textCenter = Offset(
      (start.dx + end.dx) / 2 -
          vUnit.x * offsetDistance * 2 -
          uUnit.x * textPainter.width / 2,
      (start.dy + end.dy) / 2 -
          vUnit.y * offsetDistance * 2 -
          uUnit.y * textPainter.width / 2,
    );

    angle = math.atan2(end.dy - start.dy, end.dx - start.dx) * 180 / math.pi;

    canvas.save();
    canvas.translate(textCenter.dx, textCenter.dy);
    canvas.rotate(angle * math.pi / 180);
    textPainter.paint(canvas, Offset.zero);
    canvas.restore();
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    // TODO: implement shouldRepaint
    return false;
  }
}
