// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'pile_driven_data.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$PileDrivenData {

 double get sptNValue; double get fos; double get maxPileLength; double get slsLoad; double get ulsLoad; String get section; double get length; double get shaftCapacity; double get strSLSCapacuity; double get totalGroundResistance; double get strULSCapacity; bool get isSelected; String get calsLog; String get pileDrivenSchemeId;
/// Create a copy of PileDrivenData
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$PileDrivenDataCopyWith<PileDrivenData> get copyWith => _$PileDrivenDataCopyWithImpl<PileDrivenData>(this as PileDrivenData, _$identity);

  /// Serializes this PileDrivenData to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is PileDrivenData&&(identical(other.sptNValue, sptNValue) || other.sptNValue == sptNValue)&&(identical(other.fos, fos) || other.fos == fos)&&(identical(other.maxPileLength, maxPileLength) || other.maxPileLength == maxPileLength)&&(identical(other.slsLoad, slsLoad) || other.slsLoad == slsLoad)&&(identical(other.ulsLoad, ulsLoad) || other.ulsLoad == ulsLoad)&&(identical(other.section, section) || other.section == section)&&(identical(other.length, length) || other.length == length)&&(identical(other.shaftCapacity, shaftCapacity) || other.shaftCapacity == shaftCapacity)&&(identical(other.strSLSCapacuity, strSLSCapacuity) || other.strSLSCapacuity == strSLSCapacuity)&&(identical(other.totalGroundResistance, totalGroundResistance) || other.totalGroundResistance == totalGroundResistance)&&(identical(other.strULSCapacity, strULSCapacity) || other.strULSCapacity == strULSCapacity)&&(identical(other.isSelected, isSelected) || other.isSelected == isSelected)&&(identical(other.calsLog, calsLog) || other.calsLog == calsLog)&&(identical(other.pileDrivenSchemeId, pileDrivenSchemeId) || other.pileDrivenSchemeId == pileDrivenSchemeId));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,sptNValue,fos,maxPileLength,slsLoad,ulsLoad,section,length,shaftCapacity,strSLSCapacuity,totalGroundResistance,strULSCapacity,isSelected,calsLog,pileDrivenSchemeId);

@override
String toString() {
  return 'PileDrivenData(sptNValue: $sptNValue, fos: $fos, maxPileLength: $maxPileLength, slsLoad: $slsLoad, ulsLoad: $ulsLoad, section: $section, length: $length, shaftCapacity: $shaftCapacity, strSLSCapacuity: $strSLSCapacuity, totalGroundResistance: $totalGroundResistance, strULSCapacity: $strULSCapacity, isSelected: $isSelected, calsLog: $calsLog, pileDrivenSchemeId: $pileDrivenSchemeId)';
}


}

/// @nodoc
abstract mixin class $PileDrivenDataCopyWith<$Res>  {
  factory $PileDrivenDataCopyWith(PileDrivenData value, $Res Function(PileDrivenData) _then) = _$PileDrivenDataCopyWithImpl;
@useResult
$Res call({
 double sptNValue, double fos, double maxPileLength, double slsLoad, double ulsLoad, String section, double length, double shaftCapacity, double strSLSCapacuity, double totalGroundResistance, double strULSCapacity, bool isSelected, String calsLog, String pileDrivenSchemeId
});




}
/// @nodoc
class _$PileDrivenDataCopyWithImpl<$Res>
    implements $PileDrivenDataCopyWith<$Res> {
  _$PileDrivenDataCopyWithImpl(this._self, this._then);

  final PileDrivenData _self;
  final $Res Function(PileDrivenData) _then;

/// Create a copy of PileDrivenData
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? sptNValue = null,Object? fos = null,Object? maxPileLength = null,Object? slsLoad = null,Object? ulsLoad = null,Object? section = null,Object? length = null,Object? shaftCapacity = null,Object? strSLSCapacuity = null,Object? totalGroundResistance = null,Object? strULSCapacity = null,Object? isSelected = null,Object? calsLog = null,Object? pileDrivenSchemeId = null,}) {
  return _then(_self.copyWith(
sptNValue: null == sptNValue ? _self.sptNValue : sptNValue // ignore: cast_nullable_to_non_nullable
as double,fos: null == fos ? _self.fos : fos // ignore: cast_nullable_to_non_nullable
as double,maxPileLength: null == maxPileLength ? _self.maxPileLength : maxPileLength // ignore: cast_nullable_to_non_nullable
as double,slsLoad: null == slsLoad ? _self.slsLoad : slsLoad // ignore: cast_nullable_to_non_nullable
as double,ulsLoad: null == ulsLoad ? _self.ulsLoad : ulsLoad // ignore: cast_nullable_to_non_nullable
as double,section: null == section ? _self.section : section // ignore: cast_nullable_to_non_nullable
as String,length: null == length ? _self.length : length // ignore: cast_nullable_to_non_nullable
as double,shaftCapacity: null == shaftCapacity ? _self.shaftCapacity : shaftCapacity // ignore: cast_nullable_to_non_nullable
as double,strSLSCapacuity: null == strSLSCapacuity ? _self.strSLSCapacuity : strSLSCapacuity // ignore: cast_nullable_to_non_nullable
as double,totalGroundResistance: null == totalGroundResistance ? _self.totalGroundResistance : totalGroundResistance // ignore: cast_nullable_to_non_nullable
as double,strULSCapacity: null == strULSCapacity ? _self.strULSCapacity : strULSCapacity // ignore: cast_nullable_to_non_nullable
as double,isSelected: null == isSelected ? _self.isSelected : isSelected // ignore: cast_nullable_to_non_nullable
as bool,calsLog: null == calsLog ? _self.calsLog : calsLog // ignore: cast_nullable_to_non_nullable
as String,pileDrivenSchemeId: null == pileDrivenSchemeId ? _self.pileDrivenSchemeId : pileDrivenSchemeId // ignore: cast_nullable_to_non_nullable
as String,
  ));
}

}


/// Adds pattern-matching-related methods to [PileDrivenData].
extension PileDrivenDataPatterns on PileDrivenData {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _PileDrivenData value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _PileDrivenData() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _PileDrivenData value)  $default,){
final _that = this;
switch (_that) {
case _PileDrivenData():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _PileDrivenData value)?  $default,){
final _that = this;
switch (_that) {
case _PileDrivenData() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( double sptNValue,  double fos,  double maxPileLength,  double slsLoad,  double ulsLoad,  String section,  double length,  double shaftCapacity,  double strSLSCapacuity,  double totalGroundResistance,  double strULSCapacity,  bool isSelected,  String calsLog,  String pileDrivenSchemeId)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _PileDrivenData() when $default != null:
return $default(_that.sptNValue,_that.fos,_that.maxPileLength,_that.slsLoad,_that.ulsLoad,_that.section,_that.length,_that.shaftCapacity,_that.strSLSCapacuity,_that.totalGroundResistance,_that.strULSCapacity,_that.isSelected,_that.calsLog,_that.pileDrivenSchemeId);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( double sptNValue,  double fos,  double maxPileLength,  double slsLoad,  double ulsLoad,  String section,  double length,  double shaftCapacity,  double strSLSCapacuity,  double totalGroundResistance,  double strULSCapacity,  bool isSelected,  String calsLog,  String pileDrivenSchemeId)  $default,) {final _that = this;
switch (_that) {
case _PileDrivenData():
return $default(_that.sptNValue,_that.fos,_that.maxPileLength,_that.slsLoad,_that.ulsLoad,_that.section,_that.length,_that.shaftCapacity,_that.strSLSCapacuity,_that.totalGroundResistance,_that.strULSCapacity,_that.isSelected,_that.calsLog,_that.pileDrivenSchemeId);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( double sptNValue,  double fos,  double maxPileLength,  double slsLoad,  double ulsLoad,  String section,  double length,  double shaftCapacity,  double strSLSCapacuity,  double totalGroundResistance,  double strULSCapacity,  bool isSelected,  String calsLog,  String pileDrivenSchemeId)?  $default,) {final _that = this;
switch (_that) {
case _PileDrivenData() when $default != null:
return $default(_that.sptNValue,_that.fos,_that.maxPileLength,_that.slsLoad,_that.ulsLoad,_that.section,_that.length,_that.shaftCapacity,_that.strSLSCapacuity,_that.totalGroundResistance,_that.strULSCapacity,_that.isSelected,_that.calsLog,_that.pileDrivenSchemeId);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _PileDrivenData extends PileDrivenData {
   _PileDrivenData({this.sptNValue = 50, this.fos = 3, this.maxPileLength = 30, this.slsLoad = 1000, this.ulsLoad = 2000, this.section = '', this.length = 0, this.shaftCapacity = 0, this.strSLSCapacuity = 0, this.totalGroundResistance = 0, this.strULSCapacity = 0, this.isSelected = false, this.calsLog = '', this.pileDrivenSchemeId = ''}): super._();
  factory _PileDrivenData.fromJson(Map<String, dynamic> json) => _$PileDrivenDataFromJson(json);

@override@JsonKey() final  double sptNValue;
@override@JsonKey() final  double fos;
@override@JsonKey() final  double maxPileLength;
@override@JsonKey() final  double slsLoad;
@override@JsonKey() final  double ulsLoad;
@override@JsonKey() final  String section;
@override@JsonKey() final  double length;
@override@JsonKey() final  double shaftCapacity;
@override@JsonKey() final  double strSLSCapacuity;
@override@JsonKey() final  double totalGroundResistance;
@override@JsonKey() final  double strULSCapacity;
@override@JsonKey() final  bool isSelected;
@override@JsonKey() final  String calsLog;
@override@JsonKey() final  String pileDrivenSchemeId;

/// Create a copy of PileDrivenData
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$PileDrivenDataCopyWith<_PileDrivenData> get copyWith => __$PileDrivenDataCopyWithImpl<_PileDrivenData>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$PileDrivenDataToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _PileDrivenData&&(identical(other.sptNValue, sptNValue) || other.sptNValue == sptNValue)&&(identical(other.fos, fos) || other.fos == fos)&&(identical(other.maxPileLength, maxPileLength) || other.maxPileLength == maxPileLength)&&(identical(other.slsLoad, slsLoad) || other.slsLoad == slsLoad)&&(identical(other.ulsLoad, ulsLoad) || other.ulsLoad == ulsLoad)&&(identical(other.section, section) || other.section == section)&&(identical(other.length, length) || other.length == length)&&(identical(other.shaftCapacity, shaftCapacity) || other.shaftCapacity == shaftCapacity)&&(identical(other.strSLSCapacuity, strSLSCapacuity) || other.strSLSCapacuity == strSLSCapacuity)&&(identical(other.totalGroundResistance, totalGroundResistance) || other.totalGroundResistance == totalGroundResistance)&&(identical(other.strULSCapacity, strULSCapacity) || other.strULSCapacity == strULSCapacity)&&(identical(other.isSelected, isSelected) || other.isSelected == isSelected)&&(identical(other.calsLog, calsLog) || other.calsLog == calsLog)&&(identical(other.pileDrivenSchemeId, pileDrivenSchemeId) || other.pileDrivenSchemeId == pileDrivenSchemeId));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,sptNValue,fos,maxPileLength,slsLoad,ulsLoad,section,length,shaftCapacity,strSLSCapacuity,totalGroundResistance,strULSCapacity,isSelected,calsLog,pileDrivenSchemeId);

@override
String toString() {
  return 'PileDrivenData(sptNValue: $sptNValue, fos: $fos, maxPileLength: $maxPileLength, slsLoad: $slsLoad, ulsLoad: $ulsLoad, section: $section, length: $length, shaftCapacity: $shaftCapacity, strSLSCapacuity: $strSLSCapacuity, totalGroundResistance: $totalGroundResistance, strULSCapacity: $strULSCapacity, isSelected: $isSelected, calsLog: $calsLog, pileDrivenSchemeId: $pileDrivenSchemeId)';
}


}

/// @nodoc
abstract mixin class _$PileDrivenDataCopyWith<$Res> implements $PileDrivenDataCopyWith<$Res> {
  factory _$PileDrivenDataCopyWith(_PileDrivenData value, $Res Function(_PileDrivenData) _then) = __$PileDrivenDataCopyWithImpl;
@override @useResult
$Res call({
 double sptNValue, double fos, double maxPileLength, double slsLoad, double ulsLoad, String section, double length, double shaftCapacity, double strSLSCapacuity, double totalGroundResistance, double strULSCapacity, bool isSelected, String calsLog, String pileDrivenSchemeId
});




}
/// @nodoc
class __$PileDrivenDataCopyWithImpl<$Res>
    implements _$PileDrivenDataCopyWith<$Res> {
  __$PileDrivenDataCopyWithImpl(this._self, this._then);

  final _PileDrivenData _self;
  final $Res Function(_PileDrivenData) _then;

/// Create a copy of PileDrivenData
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? sptNValue = null,Object? fos = null,Object? maxPileLength = null,Object? slsLoad = null,Object? ulsLoad = null,Object? section = null,Object? length = null,Object? shaftCapacity = null,Object? strSLSCapacuity = null,Object? totalGroundResistance = null,Object? strULSCapacity = null,Object? isSelected = null,Object? calsLog = null,Object? pileDrivenSchemeId = null,}) {
  return _then(_PileDrivenData(
sptNValue: null == sptNValue ? _self.sptNValue : sptNValue // ignore: cast_nullable_to_non_nullable
as double,fos: null == fos ? _self.fos : fos // ignore: cast_nullable_to_non_nullable
as double,maxPileLength: null == maxPileLength ? _self.maxPileLength : maxPileLength // ignore: cast_nullable_to_non_nullable
as double,slsLoad: null == slsLoad ? _self.slsLoad : slsLoad // ignore: cast_nullable_to_non_nullable
as double,ulsLoad: null == ulsLoad ? _self.ulsLoad : ulsLoad // ignore: cast_nullable_to_non_nullable
as double,section: null == section ? _self.section : section // ignore: cast_nullable_to_non_nullable
as String,length: null == length ? _self.length : length // ignore: cast_nullable_to_non_nullable
as double,shaftCapacity: null == shaftCapacity ? _self.shaftCapacity : shaftCapacity // ignore: cast_nullable_to_non_nullable
as double,strSLSCapacuity: null == strSLSCapacuity ? _self.strSLSCapacuity : strSLSCapacuity // ignore: cast_nullable_to_non_nullable
as double,totalGroundResistance: null == totalGroundResistance ? _self.totalGroundResistance : totalGroundResistance // ignore: cast_nullable_to_non_nullable
as double,strULSCapacity: null == strULSCapacity ? _self.strULSCapacity : strULSCapacity // ignore: cast_nullable_to_non_nullable
as double,isSelected: null == isSelected ? _self.isSelected : isSelected // ignore: cast_nullable_to_non_nullable
as bool,calsLog: null == calsLog ? _self.calsLog : calsLog // ignore: cast_nullable_to_non_nullable
as String,pileDrivenSchemeId: null == pileDrivenSchemeId ? _self.pileDrivenSchemeId : pileDrivenSchemeId // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

// dart format on
