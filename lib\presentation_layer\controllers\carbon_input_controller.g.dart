// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'carbon_input_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$carbonInputControllerHash() =>
    r'a5288d0ddc29e55d94478cd888fe5187881e7461';

/// See also [CarbonInputController].
@ProviderFor(CarbonInputController)
final carbonInputControllerProvider = AutoDisposeAsyncNotifierProvider<
  CarbonInputController,
  List<Carbon>
>.internal(
  CarbonInputController.new,
  name: r'carbonInputControllerProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$carbonInputControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$CarbonInputController = AutoDisposeAsyncNotifier<List<Carbon>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
