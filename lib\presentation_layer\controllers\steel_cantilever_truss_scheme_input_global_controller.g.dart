// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'steel_cantilever_truss_scheme_input_global_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$steelCantileverTrussSchemeInputGlobalControllerHash() =>
    r'5714496fa70261ede87a9f6c660c8078c3e463dd';

/// See also [SteelCantileverTrussSchemeInputGlobalController].
@ProviderFor(SteelCantileverTrussSchemeInputGlobalController)
final steelCantileverTrussSchemeInputGlobalControllerProvider =
    AutoDisposeAsyncNotifierProvider<
      SteelCantileverTrussSchemeInputGlobalController,
      SteelCantileverTrussSchemeInputGlobal
    >.internal(
      SteelCantileverTrussSchemeInputGlobalController.new,
      name: r'steelCantileverTrussSchemeInputGlobalControllerProvider',
      debugGetCreateSourceHash:
          const bool.fromEnvironment('dart.vm.product')
              ? null
              : _$steelCantileverTrussSchemeInputGlobalControllerHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$SteelCantileverTrussSchemeInputGlobalController =
    AutoDisposeAsyncNotifier<SteelCantileverTrussSchemeInputGlobal>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
