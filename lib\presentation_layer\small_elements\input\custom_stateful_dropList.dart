import 'package:flutter/material.dart';

class CustomStatefulDropDown extends StatefulWidget {
  CustomStatefulDropDown({
    this.items,
    this.selectedValue,
    this.onTap,
    super.key,
  });

  final List<dynamic>? items;
  String? selectedValue;

  void Function(String)? onTap;
  @override
  State<CustomStatefulDropDown> createState() => _CustomStatefulDropDownState();
}

class _CustomStatefulDropDownState extends State<CustomStatefulDropDown> {
  late List<dynamic> _items;
  late List<DropdownMenuItem> _entries;
  late String _selectedValue;
  late final void Function(String)? _onTap;

  @override
  void initState() {
    _items = widget.items ?? [];
    _onTap = widget.onTap;
    _entries =
        _items
            .map(
              (e) => DropdownMenuItem(
                value: e.toString(),
                child: Text(e.toString()),
                onTap: () {
                  if (_onTap != null) {
                    _onTap(e.toString());
                  }
                },
              ),
            )
            .toList();
    _selectedValue =
        widget.selectedValue ??
        (_items.isEmpty ? '' : _items[0].toString());
    super.initState();
  }

  @override
  void didUpdateWidget(covariant CustomStatefulDropDown oldWidget) {
    _items = widget.items ?? [];
    _entries =
        _items
            .map(
              (e) => DropdownMenuItem(
                value: e.toString(),
                child: Text(e.toString()),
                onTap: () {
                  if (_onTap != null) {
                    _onTap(e.toString());
                  }
                },
              ),
            )
            .toList();
    _selectedValue = widget.selectedValue ?? _selectedValue;
    super.didUpdateWidget(oldWidget);
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;
    return DropdownButton(
      focusColor: Colors.transparent,
      value: _selectedValue,
      menuMaxHeight: 300,
      dropdownColor: colorScheme.surfaceContainer,
      underline: Container(color: colorScheme.primary, height: 0.5),
      style: textTheme.labelLarge!.copyWith(color: colorScheme.onSurface),
      items: _entries,
      onTap: () {
        // print('onTapped');
      },
      onChanged: (value) {
        setState(() {
          _selectedValue = value.toString();
          widget.selectedValue = _selectedValue;
        });
      },
    );
  }
}
