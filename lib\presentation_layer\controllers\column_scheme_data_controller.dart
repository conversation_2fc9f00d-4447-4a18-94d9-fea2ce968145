import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:nanoid2/nanoid2.dart';
import 'dart:math';
import 'package:intl/intl.dart';
import 'package:structify/domain_layer/column_scheme_input.dart';
import 'package:structify/domain_layer/mixin/mixin_str_general_cals.dart';

//presentation layer
import '../../domain_layer/column_scheme_input_global.dart';
import '../../domain_layer/mixin/mixin_rc_str.dart';
import '../../misc/custom_func.dart';
import '../screen/homescreen.dart';

// domain layer
import '../../domain_layer/global_data.dart';
import '../../domain_layer/loading_table.dart';
import '../../domain_layer/preferences.dart';
import '../../domain_layer/column_scheme_data.dart';

part 'column_scheme_data_controller.g.dart';

@riverpod
class ColumnSchemeDataController extends _$ColumnSchemeDataController
    with StrGeneralCals, RCStrHK {
  @override
  FutureOr<List<ColumnSchemeData>> build() async {
    List<ColumnSchemeData> columnSchemeDataList =
        await ref
            .read(appDatabaseControllerProvider.notifier)
            .queryColumnSchemeData();
    final data1 = ref.watch(globalDataControllerProvider);
    final data2 = ref.watch(loadingTablesControllerProvider);
    final data3 = ref.watch(columnSchemeInputControllerProvider);
    final data4 = ref.watch(columnSchemeInputGlobalControllerProvider);
    return data1.when(
      data: (globalData) async {
        return data2.when(
          data: (loadingTables) async {
            return data3.when(
              data: (inputs) async {
                return data4.when(
                  data: (inputsGlobal) async {
                    return await bacthColumnScheming(
                      existingSchemes: columnSchemeDataList,
                      calledInBuild: true,
                    );
                  },
                  error: (error, stackTrace) => <ColumnSchemeData>[],
                  loading: () => <ColumnSchemeData>[],
                );
              },
              error: (error, stackTrace) => <ColumnSchemeData>[],
              loading: () => <ColumnSchemeData>[],
            );
          },
          error: (error, stackTrace) => <ColumnSchemeData>[],
          loading: () => <ColumnSchemeData>[],
        );
      },
      error: (error, stackTrace) => <ColumnSchemeData>[],
      loading: () => <ColumnSchemeData>[],
    );
  }

  Future<void> addColumnSchemeData(
    List<ColumnSchemeData> columnSchemeData,
  ) async {
    final x = await future;
    state = AsyncData([...x, ...columnSchemeData]);

    // state.when(
    //   data: (data) {
    //     state = AsyncData([...data, ...beamSchemeData]);
    //   },
    //   error: (error, stackTrace) => state = AsyncError(error, stackTrace),
    //   loading: () {},
    // );
  }

  Future<void> deleteTable(String id) async {
    final x = await future;
    x.removeWhere((item) => item.columnSchemeDataId == id);
    state = AsyncData(x);
  }

  Future<List<ColumnSchemeData>> deleteTablesNotSelected({
    List<ColumnSchemeData>? existingScheme,
    bool? calledInBuild,
  }) async {
    late final List<ColumnSchemeData> schemes;
    if (existingScheme != null && existingScheme.isNotEmpty) {
      schemes = existingScheme;
    } else {
      if (calledInBuild != null && calledInBuild) {
        schemes = [];
      } else {
        schemes = await future;
      }
    }
    schemes.removeWhere((item) => !item.isSelected);
    state = AsyncData(schemes);
    return schemes;
  }

  Future<void> deleteAllTable() async {
    state = AsyncData([]);
  }

  Future<void> replaceEntireTable(List<ColumnSchemeData> newColSchemes) async {
    state = AsyncData(newColSchemes);
  }

  Future<void> toggleSelectScheme(String columnSchemeId) async {
    final x = await future;
    final newState =
        x.map((item) {
          if (item.columnSchemeDataId == columnSchemeId) {
            return item.copyWith(isSelected: !item.isSelected);
          } else {
            return item.copyWith(isSelected: false);
          }
        }).toList();
    state = AsyncData(newState);
  }

  Future<String> _generateUniqueId({Set<String>? existingID}) async {
    late final Set<String> columnSchemeDataId;
    if (existingID == null) {
      final x = await future;
      columnSchemeDataId = x.map((item) => item.columnSchemeDataId).toSet();
    } else {
      columnSchemeDataId = existingID;
    }
    String newID = nanoid(alphabet: Alphabet.noDoppelgangerSafe);
    while (columnSchemeDataId.contains(newID)) {
      newID = nanoid(alphabet: Alphabet.noDoppelgangerSafe);
    }
    return newID;
  }

  Future<List<ColumnSchemeData>> bacthColumnScheming({
    List<ColumnSchemeData>? existingSchemes,
    bool? calledInBuild,
  }) async {
    late final List<ColumnSchemeData> schemesLeft;
    final List<ColumnSchemeData> finalList = [];
    schemesLeft = await deleteTablesNotSelected(
      existingScheme: existingSchemes,
      calledInBuild: calledInBuild,
    );
    finalList.addAll(schemesLeft);
    final existingID =
        existingSchemes?.map((e) => e.columnSchemeDataId).toSet();
    final tables = await ref.read(loadingTablesControllerProvider.future);
    final globalData = await ref.read(globalDataControllerProvider.future);
    final inputs = await ref.read(columnSchemeInputControllerProvider.future);
    final inputsGlobal = await ref.read(
      columnSchemeInputGlobalControllerProvider.future,
    );

    final List<double> fcu;
    final RegExp regExp1 = RegExp(r'(?<=C)[\d]+');
    double clearS, minClearS = inputsGlobal.minClearS, assumedLinksDia = 12;

    int n, n1;
    String mainBarSquare, mainBarCircle;
    double sdl,
        ll,
        total,
        factoredTotal,
        fy = 500,
        columnSize = inputsGlobal.minColumnSize,
        steelRatioSqaure,
        steelRatioCircle;

    List<int> rebar = [12, 16, 20, 25, 32, 40];

    StringBuffer buffer = StringBuffer(); // record the cals

    fcu =
        inputsGlobal.concreteGrade.split(',').map((e) {
          try {
            final x = regExp1.allMatches(e).first.group(0);
            return double.parse(x ?? '');
          } catch (e) {
            return 0.0;
          }
        }).toList();
    fcu.sort();

    //* Initial Capacity
    double trailCapSquare = getAxialCapacityColumnSquare(
      fcu.first,
      columnSize,
      fy,
      inputsGlobal.maxSteelRatio,
    );
    double trailCapCircle = getAxialCapacityColumnCircle(
      fcu.first,
      columnSize,
      fy,
      inputsGlobal.maxSteelRatio,
    );

    //* Loading
    sdl = inputs.fold(0, (double sum, input) {
      final tbl = tables.firstWhere((table) {
        return table.usage == input.usage;
      });
      sum +=
          (getPressureFromThick(tbl.finish, globalData.finishUnitWeight) +
              tbl.service +
              getPressureFromThick(
                input.slabThickness,
                globalData.rcUnitWeight,
              )) *
          input.loadWidth *
          input.loadLength *
          input.nosOfFloor;
      return sum;
    });
    ll = inputs.fold(0, (double sum, input) {
      final tbl = tables.firstWhere((table) {
        return table.usage == input.usage;
      });
      sum +=
          (tbl.liveLoad) *
          input.loadWidth *
          input.loadLength *
          input.nosOfFloor;
      return sum;
    });
    total = sdl + ll;
    factoredTotal = globalData.sdlFactor * sdl + globalData.llFactor * ll;

    //* iterate using max steel ratio to find min col size
    while (trailCapSquare < inputsGlobal.safetyFactor * factoredTotal ||
        trailCapCircle < inputsGlobal.safetyFactor * factoredTotal) {
      columnSize += inputsGlobal.sizeIncrement;

      trailCapSquare = getAxialCapacityColumnSquare(
        fcu.first,
        columnSize,
        fy,
        inputsGlobal.maxSteelRatio,
      );

      trailCapCircle = getAxialCapacityColumnCircle(
        fcu.first,
        columnSize,
        fy,
        inputsGlobal.maxSteelRatio,
      );
    }

    for (int i = 0; i < inputsGlobal.iterationSteps; i++) {
      for (
        double steelRatio = inputsGlobal.minSteelRatio;
        steelRatio <= inputsGlobal.maxSteelRatio;
        steelRatio += 0.005
      ) {
        for (int j = 0; j < fcu.length; j++) {
          steelRatio = roundTo(steelRatio, 3);
          double trailCapSquare = getAxialCapacityColumnSquare(
            fcu[j],
            columnSize,
            fy,
            steelRatio,
          );
          double trailCapCircle = getAxialCapacityColumnCircle(
            fcu[j],
            columnSize,
            fy,
            steelRatio,
          );
          for (int dia in rebar) {
            //* sqaure column design
            // get approx nos of bar for preferred steel ratio
            if (trailCapSquare > factoredTotal) {
              n = max(
                8,
                (steelRatio * getAreaCircle(columnSize) / getRebarArea(dia))
                    .ceil(),
              );
              if ((n - 4) % 4 != 0) {
                n = n - ((n - 4) % 4) + 4;
              }
              n1 = n - 4;
              clearS =
                  (columnSize -
                      2 * (inputsGlobal.cover + assumedLinksDia) -
                      ((n1 / 4 + 2) * dia)) /
                  ((n1 / 4 + 2) - 1);
              if (clearS > minClearS) {
                mainBarSquare = '4T$dia+${n1}T$dia';

                //* update using actual steel ratio and capacity
                steelRatioSqaure =
                    n * pow(dia, 2) * pi / 4 / pow(columnSize, 2);

                trailCapSquare = getAxialCapacityColumnSquare(
                  fcu[j],
                  columnSize,
                  fy,
                  steelRatioSqaure,
                );
              } else {
                mainBarSquare = 'Congested rebar';
                steelRatioSqaure = 0;
              }
            } else {
              mainBarSquare = 'fail';
              steelRatioSqaure = 0;
            }

            //* Circular column design
            if (trailCapCircle > factoredTotal) {
              n = max(
                6,
                ((steelRatio * pow(columnSize, 2) * pi / 4) /
                        (pow(dia, 2) * pi / 4))
                    .ceil(),
              );
              clearS =
                  ((columnSize / 2 -
                              (inputsGlobal.cover +
                                  assumedLinksDia +
                                  dia / 2)) *
                          2 *
                          pi -
                      (n * dia)) /
                  (n - 1);
              if (clearS > minClearS) {
                mainBarCircle = '${n}T$dia';

                //! renew to actual steel ratio and capacity
                steelRatioCircle =
                    n * pow(dia, 2) * pi / 4 / (pow(columnSize, 2) * pi / 4);

                trailCapCircle = getAxialCapacityColumnCircle(
                  fcu[j],
                  columnSize,
                  fy,
                  steelRatioCircle,
                );
              } else {
                mainBarCircle = 'Congested rebar';
                steelRatioCircle = 0;
              }
            } else {
              mainBarCircle = 'fail';
              steelRatioCircle = 0;
            }

            final id = await _generateUniqueId(existingID: existingID);

            if (steelRatioSqaure > 0 &&
                steelRatioCircle > 0 &&
                steelRatioSqaure < inputsGlobal.maxSteelRatio &&
                steelRatioCircle < inputsGlobal.maxSteelRatio) {
              _recordCalsResult(
                buffer,
                inputs: inputs,
                inputsGlobal: inputsGlobal,
                globalData: globalData,
                loadingTables: tables,
                size: columnSize,
                fcu: fcu[j],
                cover: inputsGlobal.cover,
                axialCapacitySquare: trailCapSquare,
                axialCapacityCircle: trailCapCircle,
                steelRatioSqaure: steelRatioSqaure,
                steelRatioCircle: steelRatioCircle,
                mainBarSquare: mainBarSquare,
                mainBarCircle: mainBarCircle,
              );
              finalList.add(
                ColumnSchemeData(
                  columnSchemeDataId: id,
                  sdl: roundTo(sdl, 0),
                  ll: roundTo(ll, 0),
                  slsLoad: roundTo(total, 0),
                  ulsLoad: roundTo(factoredTotal, 0),
                  size: roundTo(columnSize, 0),
                  fcu: roundTo(fcu[j], 0),
                  cover: roundTo(inputsGlobal.cover, 0),
                  axialCapacitySquare: roundTo(trailCapSquare, 0),
                  mainBarSquare: mainBarSquare,
                  steelRatioSqaure: roundTo(steelRatioSqaure, 3),
                  axialCapacityCircle: roundTo(trailCapCircle, 0),
                  mainBarCircle: mainBarCircle,
                  steelRatioCircle: roundTo(steelRatioCircle, 3),
                  calsLog: buffer.toString(),
                  isSelected: false,
                ),
              );
            }
          }
        }
      }
      columnSize += inputsGlobal.sizeIncrement;
    }
    state = AsyncData(finalList);
    return finalList;
  }

  void _recordCalsResult(
    StringBuffer buffer, {
    required List<ColumnSchemeInput> inputs,
    required ColumnSchemeInputGlobal inputsGlobal,
    required GlobalData globalData,
    required List<LoadingTable> loadingTables,
    required double size,
    required double fcu,
    required double cover,
    required double axialCapacitySquare,
    required double axialCapacityCircle,
    required double steelRatioSqaure,
    required double steelRatioCircle,
    required String mainBarSquare,
    required String mainBarCircle,
  }) {
    late final List<String> unit;
    late double sdl, ll, ulsLoad, slsLoad, loadArea;
    late LoadingTable loadingTable;

    double slsTotal = 0, ulsTotal = 0;
    switch (globalData.unit) {
      case 'metrics':
        unit = PreferredUnit.metrics;
        break;
      case 'imperial':
        unit = PreferredUnit.imperial;
        break;
      default:
        unit = PreferredUnit.metrics;
        break;
    }

    buffer.clear();

    for (int i = 0; i < inputs.length; i++) {
      loadingTable = loadingTables.firstWhere(
        (tbl) => tbl.usage == inputs[i].usage,
      );
      loadArea =
          inputs[i].loadLength * inputs[i].loadWidth * inputs[i].nosOfFloor;
      sdl =
          (inputs[i].slabThickness * globalData.rcUnitWeight * pow(10, -3) +
              loadingTable.finish * globalData.finishUnitWeight * pow(10, -3) +
              loadingTable.service) *
          loadArea;
      ll = loadingTable.liveLoad * loadArea;
      slsLoad = sdl + ll;
      ulsLoad = globalData.sdlFactor * sdl + globalData.llFactor * ll;
      slsTotal += slsLoad;
      ulsTotal += ulsLoad;
      // buffer.write('Loading Input (${i + 1}): ');
      buffer.write(
        'LoadArea (${i + 1}): ${NumberFormat('0').format(loadArea)} [${unit[7]}] | ',
      );
      buffer.write(
        'SDL (${i + 1}): ${NumberFormat('0').format(sdl)} [${unit[0]}] | ',
      );
      buffer.write(
        'LL (${i + 1}): ${NumberFormat('0').format(ll)} [${unit[0]}] | ',
      );
      buffer.write(
        'SLS Load (${i + 1}): ${NumberFormat('0').format(slsLoad)} [${unit[0]}] | ',
      );
      buffer.write(
        'ULS Load (${i + 1}): ${NumberFormat('0').format(ulsLoad)} [${unit[0]}]\n',
      );
    }
    buffer.write(
      'SLS Total: ${NumberFormat('0').format(slsTotal)} [${unit[0]}] | ',
    );
    buffer.write(
      'ULS Total: ${NumberFormat('0').format(ulsTotal)} [${unit[0]}]\n',
    );

    buffer.write('Size: ${NumberFormat('0').format(size)} [${unit[4]}] | ');
    buffer.write('fcu: ${NumberFormat('0').format(fcu)} [${unit[5]}] | ');
    buffer.write('Cover: ${NumberFormat('0').format(cover)} [${unit[4]}]\n');
    buffer.write(
      'Axial Capacity Square: ${NumberFormat('0').format(axialCapacitySquare)} [${unit[0]}] | ',
    );
    buffer.write(
      'Steel Ratio Square: ${NumberFormat('0.000').format(steelRatioSqaure)} | ',
    );
    buffer.write('Main Bar Square: $mainBarSquare\n');
    buffer.write(
      'Axial Capacity Circle: ${NumberFormat('0').format(axialCapacityCircle)} [${unit[0]}] | ',
    );
    buffer.write(
      'Steel Ratio Circle: ${NumberFormat('0.000').format(steelRatioCircle)} | ',
    );
    buffer.write('Main Bar Circle: $mainBarCircle\n');

    if (axialCapacitySquare > ulsTotal) {
      buffer.write('Square Scheme: pass | ');
    } else {
      buffer.write('Square Scheme: fail | ');
    }
    if (axialCapacityCircle > ulsTotal) {
      buffer.write('Circle Scheme: pass\n');
    } else {
      buffer.write('Circle Scheme: fail\n');
    }
  }
}
