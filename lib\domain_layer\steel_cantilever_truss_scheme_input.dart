import 'package:freezed_annotation/freezed_annotation.dart';
// import 'package:flutter/foundation.dart';
part 'steel_cantilever_truss_scheme_input.freezed.dart';
part 'steel_cantilever_truss_scheme_input.g.dart';

@freezed
abstract class SteelCantileverTrussSchemeInput with _$SteelCantileverTrussSchemeInput {
  const SteelCantileverTrussSchemeInput._();
  factory SteelCantileverTrussSchemeInput({
    @Default(0.0) double pointLoad,
    @Default(3.0) double distA,
    @Default('')
    String
    steelCantileverTrussSchemeInputId, //will be overriden  as soon as new instance created
  }) = _SteelCantileverTrussSchemeInput;

  factory SteelCantileverTrussSchemeInput.fromJson(Map<String, Object?> json) =>
      _$SteelCantileverTrussSchemeInputFromJson(json);
}
