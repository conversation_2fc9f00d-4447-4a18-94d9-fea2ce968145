// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'steel_transfer_truss_scheme_input.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$SteelTransferTrussSchemeInput {

 double get pointLoad; double get distA; String get steelTransferTrussSchemeInputId;
/// Create a copy of SteelTransferTrussSchemeInput
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$SteelTransferTrussSchemeInputCopyWith<SteelTransferTrussSchemeInput> get copyWith => _$SteelTransferTrussSchemeInputCopyWithImpl<SteelTransferTrussSchemeInput>(this as SteelTransferTrussSchemeInput, _$identity);

  /// Serializes this SteelTransferTrussSchemeInput to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is SteelTransferTrussSchemeInput&&(identical(other.pointLoad, pointLoad) || other.pointLoad == pointLoad)&&(identical(other.distA, distA) || other.distA == distA)&&(identical(other.steelTransferTrussSchemeInputId, steelTransferTrussSchemeInputId) || other.steelTransferTrussSchemeInputId == steelTransferTrussSchemeInputId));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,pointLoad,distA,steelTransferTrussSchemeInputId);

@override
String toString() {
  return 'SteelTransferTrussSchemeInput(pointLoad: $pointLoad, distA: $distA, steelTransferTrussSchemeInputId: $steelTransferTrussSchemeInputId)';
}


}

/// @nodoc
abstract mixin class $SteelTransferTrussSchemeInputCopyWith<$Res>  {
  factory $SteelTransferTrussSchemeInputCopyWith(SteelTransferTrussSchemeInput value, $Res Function(SteelTransferTrussSchemeInput) _then) = _$SteelTransferTrussSchemeInputCopyWithImpl;
@useResult
$Res call({
 double pointLoad, double distA, String steelTransferTrussSchemeInputId
});




}
/// @nodoc
class _$SteelTransferTrussSchemeInputCopyWithImpl<$Res>
    implements $SteelTransferTrussSchemeInputCopyWith<$Res> {
  _$SteelTransferTrussSchemeInputCopyWithImpl(this._self, this._then);

  final SteelTransferTrussSchemeInput _self;
  final $Res Function(SteelTransferTrussSchemeInput) _then;

/// Create a copy of SteelTransferTrussSchemeInput
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? pointLoad = null,Object? distA = null,Object? steelTransferTrussSchemeInputId = null,}) {
  return _then(_self.copyWith(
pointLoad: null == pointLoad ? _self.pointLoad : pointLoad // ignore: cast_nullable_to_non_nullable
as double,distA: null == distA ? _self.distA : distA // ignore: cast_nullable_to_non_nullable
as double,steelTransferTrussSchemeInputId: null == steelTransferTrussSchemeInputId ? _self.steelTransferTrussSchemeInputId : steelTransferTrussSchemeInputId // ignore: cast_nullable_to_non_nullable
as String,
  ));
}

}


/// Adds pattern-matching-related methods to [SteelTransferTrussSchemeInput].
extension SteelTransferTrussSchemeInputPatterns on SteelTransferTrussSchemeInput {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _SteelTransferTrussSchemeInput value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _SteelTransferTrussSchemeInput() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _SteelTransferTrussSchemeInput value)  $default,){
final _that = this;
switch (_that) {
case _SteelTransferTrussSchemeInput():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _SteelTransferTrussSchemeInput value)?  $default,){
final _that = this;
switch (_that) {
case _SteelTransferTrussSchemeInput() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( double pointLoad,  double distA,  String steelTransferTrussSchemeInputId)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _SteelTransferTrussSchemeInput() when $default != null:
return $default(_that.pointLoad,_that.distA,_that.steelTransferTrussSchemeInputId);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( double pointLoad,  double distA,  String steelTransferTrussSchemeInputId)  $default,) {final _that = this;
switch (_that) {
case _SteelTransferTrussSchemeInput():
return $default(_that.pointLoad,_that.distA,_that.steelTransferTrussSchemeInputId);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( double pointLoad,  double distA,  String steelTransferTrussSchemeInputId)?  $default,) {final _that = this;
switch (_that) {
case _SteelTransferTrussSchemeInput() when $default != null:
return $default(_that.pointLoad,_that.distA,_that.steelTransferTrussSchemeInputId);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _SteelTransferTrussSchemeInput extends SteelTransferTrussSchemeInput {
   _SteelTransferTrussSchemeInput({this.pointLoad = 0.0, this.distA = 3.0, this.steelTransferTrussSchemeInputId = ''}): super._();
  factory _SteelTransferTrussSchemeInput.fromJson(Map<String, dynamic> json) => _$SteelTransferTrussSchemeInputFromJson(json);

@override@JsonKey() final  double pointLoad;
@override@JsonKey() final  double distA;
@override@JsonKey() final  String steelTransferTrussSchemeInputId;

/// Create a copy of SteelTransferTrussSchemeInput
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$SteelTransferTrussSchemeInputCopyWith<_SteelTransferTrussSchemeInput> get copyWith => __$SteelTransferTrussSchemeInputCopyWithImpl<_SteelTransferTrussSchemeInput>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$SteelTransferTrussSchemeInputToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _SteelTransferTrussSchemeInput&&(identical(other.pointLoad, pointLoad) || other.pointLoad == pointLoad)&&(identical(other.distA, distA) || other.distA == distA)&&(identical(other.steelTransferTrussSchemeInputId, steelTransferTrussSchemeInputId) || other.steelTransferTrussSchemeInputId == steelTransferTrussSchemeInputId));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,pointLoad,distA,steelTransferTrussSchemeInputId);

@override
String toString() {
  return 'SteelTransferTrussSchemeInput(pointLoad: $pointLoad, distA: $distA, steelTransferTrussSchemeInputId: $steelTransferTrussSchemeInputId)';
}


}

/// @nodoc
abstract mixin class _$SteelTransferTrussSchemeInputCopyWith<$Res> implements $SteelTransferTrussSchemeInputCopyWith<$Res> {
  factory _$SteelTransferTrussSchemeInputCopyWith(_SteelTransferTrussSchemeInput value, $Res Function(_SteelTransferTrussSchemeInput) _then) = __$SteelTransferTrussSchemeInputCopyWithImpl;
@override @useResult
$Res call({
 double pointLoad, double distA, String steelTransferTrussSchemeInputId
});




}
/// @nodoc
class __$SteelTransferTrussSchemeInputCopyWithImpl<$Res>
    implements _$SteelTransferTrussSchemeInputCopyWith<$Res> {
  __$SteelTransferTrussSchemeInputCopyWithImpl(this._self, this._then);

  final _SteelTransferTrussSchemeInput _self;
  final $Res Function(_SteelTransferTrussSchemeInput) _then;

/// Create a copy of SteelTransferTrussSchemeInput
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? pointLoad = null,Object? distA = null,Object? steelTransferTrussSchemeInputId = null,}) {
  return _then(_SteelTransferTrussSchemeInput(
pointLoad: null == pointLoad ? _self.pointLoad : pointLoad // ignore: cast_nullable_to_non_nullable
as double,distA: null == distA ? _self.distA : distA // ignore: cast_nullable_to_non_nullable
as double,steelTransferTrussSchemeInputId: null == steelTransferTrussSchemeInputId ? _self.steelTransferTrussSchemeInputId : steelTransferTrussSchemeInputId // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

// dart format on
