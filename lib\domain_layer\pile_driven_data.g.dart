// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'pile_driven_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_PileDrivenData _$PileDrivenDataFromJson(Map<String, dynamic> json) =>
    _PileDrivenData(
      sptNValue: (json['sptNValue'] as num?)?.toDouble() ?? 50,
      fos: (json['fos'] as num?)?.toDouble() ?? 3,
      maxPileLength: (json['maxPileLength'] as num?)?.toDouble() ?? 30,
      slsLoad: (json['slsLoad'] as num?)?.toDouble() ?? 1000,
      ulsLoad: (json['ulsLoad'] as num?)?.toDouble() ?? 2000,
      section: json['section'] as String? ?? '',
      length: (json['length'] as num?)?.toDouble() ?? 0,
      shaftCapacity: (json['shaftCapacity'] as num?)?.toDouble() ?? 0,
      strSLSCapacuity: (json['strSLSCapacuity'] as num?)?.toDouble() ?? 0,
      totalGroundResistance:
          (json['totalGroundResistance'] as num?)?.toDouble() ?? 0,
      strULSCapacity: (json['strULSCapacity'] as num?)?.toDouble() ?? 0,
      isSelected: json['isSelected'] as bool? ?? false,
      calsLog: json['calsLog'] as String? ?? '',
      pileDrivenSchemeId: json['pileDrivenSchemeId'] as String? ?? '',
    );

Map<String, dynamic> _$PileDrivenDataToJson(_PileDrivenData instance) =>
    <String, dynamic>{
      'sptNValue': instance.sptNValue,
      'fos': instance.fos,
      'maxPileLength': instance.maxPileLength,
      'slsLoad': instance.slsLoad,
      'ulsLoad': instance.ulsLoad,
      'section': instance.section,
      'length': instance.length,
      'shaftCapacity': instance.shaftCapacity,
      'strSLSCapacuity': instance.strSLSCapacuity,
      'totalGroundResistance': instance.totalGroundResistance,
      'strULSCapacity': instance.strULSCapacity,
      'isSelected': instance.isSelected,
      'calsLog': instance.calsLog,
      'pileDrivenSchemeId': instance.pileDrivenSchemeId,
    };
