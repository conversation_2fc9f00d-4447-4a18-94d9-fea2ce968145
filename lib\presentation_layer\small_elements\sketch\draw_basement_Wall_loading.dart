import 'dart:math' as math;
import 'dart:typed_data';
import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:structify/domain_layer/basement_wall_scheme_input.dart';
import 'package:structify/domain_layer/transfer_beam_scheme_input.dart';
import 'package:structify/domain_layer/transfer_beam_scheme_input_global.dart';
import 'package:structify/misc/custom_func.dart';
import 'package:structify/presentation_layer/screen/homescreen.dart';
import 'package:vector_math/vector_math.dart' as vec;

import '../../../domain_layer/global_data.dart';
import '../../../domain_layer/loading_data.dart';
import '../../../domain_layer/preferences.dart';
import '../../../domain_layer/transfer_beam_scheme_data.dart';

class DrawBasementWallLoading extends ConsumerWidget {
  DrawBasementWallLoading({
    required this.sketchWidth,
    required this.sketchHeight,
    this.fontSize,
    super.key,
  });

  final double sketchWidth;
  final double sketchHeight;
  double? fontSize;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final data1 = ref.watch(basementWallSchemeInputControllerProvider);
    final data2 = ref.watch(globalDataControllerProvider);
    final data3 = ref.watch(transferBeamSchemeDataControllerProvider);
    final ColorScheme colorScheme = Theme.of(context).colorScheme;
    final TextTheme textTheme = Theme.of(context).textTheme;

    return data1.when(
      data: (input) {
        return data2.when(
          data: (globalData) {
            return data3.when(
              data: (transferBeamSchemeData) {
                final constraints = BoxConstraints(
                  maxWidth: sketchWidth,
                  maxHeight: sketchHeight,
                  minHeight: 100,
                  minWidth: 100,
                );

                return Center(
                  child: Container(
                    decoration: BoxDecoration(
                      color: colorScheme.surfaceContainer.withAlpha(100),
                      borderRadius: BorderRadius.circular(10.0),
                      border: Border.all(color: Colors.black.withAlpha(100)),
                    ),
                    width:
                        constraints.maxWidth == double.infinity
                            ? 100
                            : constraints.maxWidth,
                    height:
                        constraints.maxHeight == double.infinity
                            ? 100
                            : constraints.maxHeight,
                    child: CustomPaint(
                      painter: DrawBasementWallLoadingPainter(
                        input: input,
                        globalData: globalData,
                        boxConstraints: constraints,
                        fontSize: fontSize,
                        context: context,
                        transferBeamSchemeData: transferBeamSchemeData,
                      ),
                    ),
                  ),
                );
              },
              error: (error, stackTrace) {
                return Text(error.toString());
              },
              loading: () {
                return const CircularProgressIndicator();
              },
            );
          },
          error: (error, stackTrace) {
            return Text(error.toString());
          },
          loading: () {
            return const CircularProgressIndicator();
          },
        );
      },
      error: (error, stackTrace) => Text(error.toString()),
      loading: () => CircularProgressIndicator(),
    );
  }
}

class DrawBasementWallLoadingPainter extends CustomPainter {
  DrawBasementWallLoadingPainter({
    required this.input,
    required this.globalData,
    required this.boxConstraints,
    this.fontSize,
    required this.context,
    required this.transferBeamSchemeData,
  });

  final BasementWallSchemeInput input;
  final GlobalData globalData;
  final BoxConstraints boxConstraints;
  double? fontSize;
  final BuildContext context;
  final TransferBeamSchemeData transferBeamSchemeData;

  @override
  void paint(Canvas canvas, Size size) {
    final ColorScheme colorScheme = Theme.of(context).colorScheme;
    final TextTheme textTheme = Theme.of(context).textTheme;
    double offsetCounter = 15;

    //* Get Ratio for drawing

    final span = input.wallTopLevel - input.wallBottomLevel;

    final double ratio = size.width / span * 4 / 5;
    late Offset startP1, endP1, startP2, endP2, startP3, endP3;

    fontSize = fontSize ?? math.min(12, math.min(size.width, size.height) / 8);

    Paint wallPaint =
        Paint()
          ..color = colorScheme.onSurface
          ..strokeCap = StrokeCap.round
          ..style = PaintingStyle.stroke
          ..strokeWidth = 2.0;

    Paint supportPaint =
        Paint()
          ..color = colorScheme.onSurface
          ..strokeCap = StrokeCap.round
          ..style = PaintingStyle.fill
          ..strokeWidth = 2;

    Paint loadPaint =
        Paint()
          ..color = colorScheme.primary
          ..strokeCap = StrokeCap.round
          ..style = PaintingStyle.fill
          ..strokeWidth = 1.0;

    Paint soilPaint =
        Paint()
          ..color = Colors.brown.withAlpha(200)
          ..strokeCap = StrokeCap.round
          ..style = PaintingStyle.fill
          ..strokeWidth = 1.5;
    Paint waterPaint =
        Paint()
          ..color = Colors.blueAccent.withAlpha(200)
          ..strokeCap = StrokeCap.round
          ..style = PaintingStyle.fill
          ..strokeWidth = 1.5;

    Path path = Path();

    // ******************
    // todo: draw wall
    // ******************
    startP1 = Offset(0.5 * (size.width), 0.85 * (size.height));

    startP2 = startP1 + Offset(-0.5 * span * ratio, 0);
    endP2 = startP2 + Offset(1 * span * ratio, 0);

    canvas.drawLine(startP2, endP2, wallPaint);

    // ******************
    // todo: draw support
    // ******************
    final supportLeft = Float32List.fromList(
      [
        startP2,
        startP2 + Offset(10, 10),
        startP2 + Offset(-10, 10),
        startP2,
      ].expand((i) => [i.dx, i.dy]).toList(),
    );
    final supportRight = Float32List.fromList(
      [
        endP2,
        endP2 + Offset(10, 10),
        endP2 + Offset(-10, 10),
        endP2,
      ].expand((i) => [i.dx, i.dy]).toList(),
    );

    canvas.drawRawPoints(PointMode.polygon, supportLeft, supportPaint);
    canvas.drawRawPoints(PointMode.polygon, supportRight, supportPaint);
    _drawDimLine(
      canvas,
      fontSize!,
      startP2,
      endP2,
      offsetCounter,
      fontSize! / 2,
      span.toStringAsFixed(0),
      context,
    );
    offsetCounter += 5;
    // ******************
    // todo: draw loading
    // ******************

    //* Draw UDL
    // Calculate UDL

    final double soilLoadTop =
        globalData.sdlFactor *
        globalData.soilUnitWeight *
        (math.max(input.wallTopLevel, input.soilTopLevel) - input.wallTopLevel);
    final double waterLoadTop =
        globalData.llFactor *
        globalData.waterUnitWeight *
        (math.max(input.wallTopLevel, input.waterTopLevel) -
            input.wallTopLevel);
    final double soilLoadBot =
        globalData.sdlFactor *
        globalData.soilUnitWeight *
        (math.max(input.wallTopLevel, input.soilTopLevel) -
            input.wallBottomLevel);
    final double waterLoadBot =
        globalData.llFactor *
        globalData.waterUnitWeight *
        (math.max(input.wallTopLevel, input.waterTopLevel) -
            input.wallBottomLevel);

    final double ratio2 =
        size.height /
        maxFromNum<double>([size.height, soilLoadBot, waterLoadBot]) *
        2.5 /
        5;

    // soil load
    double soilStart =
        (math.max(0, input.wallTopLevel - input.soilTopLevel)) * ratio;
    startP3 = Offset(startP2.dx + soilStart, startP2.dy - offsetCounter - 10);
    endP3 = Offset(endP2.dx, endP2.dy - offsetCounter - 10);
    final soilLoadDrawn = Float32List.fromList(
      [
        startP3,
        endP3,
        endP3 + Offset(0, -1 * soilLoadBot * ratio2),
        startP3 + Offset(0, -1 * soilLoadTop * ratio2),
        startP3,
      ].expand((i) => [i.dx, i.dy]).toList(),
    );
    canvas.drawRawPoints(PointMode.polygon, soilLoadDrawn, soilPaint);
    _drawText(
      canvas,
      fontSize!,
       startP2 + Offset(soilStart, -offsetCounter - 10 - 15),
       startP2 + Offset( soilStart+size.width / 10, -offsetCounter - 10 - 15),
      0,
      0,
      ( soilLoadTop).toStringAsFixed(1),
      context,
      fontColor: soilPaint.color,
    );
    _drawText(
      canvas,
      fontSize!,
      endP2 + Offset(-size.width / 10, -offsetCounter - 10 - 15),
      endP2 + Offset(0, -offsetCounter - 10 - 15),
      0,
      0,
      (soilLoadBot).toStringAsFixed(1),
      context,
      fontColor: soilPaint.color,
    );

    offsetCounter += size.height / 15;
    // water load
    double waterStart =
        (math.max(0, input.wallTopLevel - input.waterTopLevel)) * ratio;
    startP3 = Offset(startP2.dx + waterStart, startP2.dy - offsetCounter - 10);
    endP3 = Offset(endP2.dx, endP2.dy - offsetCounter - 10);
    final waterLoadDrawn = Float32List.fromList(
      [
        startP3,
        endP3,
        endP3 + Offset(0, -1 * waterLoadBot * ratio2),
        startP3 + Offset(0, -1 * waterLoadTop * ratio2),
        startP3,
      ].expand((i) => [i.dx, i.dy]).toList(),
    );
    canvas.drawRawPoints(PointMode.polygon, waterLoadDrawn, waterPaint);
    _drawText(
      canvas,
      fontSize!,
      startP2 + Offset( waterStart, -offsetCounter - 10 - 15),
      startP2 + Offset(waterStart + size.width / 10, -offsetCounter - 10 - 15),
      0,
      0,
      (waterLoadTop).toStringAsFixed(1),
      context,
      fontColor: waterPaint.color,
    );
    _drawText(
      canvas,
      fontSize!,
      endP2 + Offset(-size.width / 10, -offsetCounter - 10 - 15),
      endP2 + Offset(0, -offsetCounter - 10 - 15),
      0,
      0,
      (waterLoadBot).toStringAsFixed(1),
      context,
      fontColor: waterPaint.color,
    );
    // int segmentCounts = 10;
    // double segmentLength = (endP2.dx - startP2.dx) / segmentCounts;
    // Offset centerP;
    // for (int i = 0; i < segmentCounts; i++) {
    //   startP3 = Offset(startP2.dx + segmentLength * i, startP3.dy);
    //   endP3 = startP3 + Offset(segmentLength, 0);
    //   centerP = (startP3 + endP3) / 2;
    //   Rect rec1 = Rect.fromCenter(
    //     center: centerP,
    //     width: segmentLength,
    //     height: segmentLength,
    //   );
    //   canvas.drawArc(rec1, math.pi, math.pi, true, UDLPaint);
    // }
  }

  void _drawDimLine(
    Canvas canvas,
    double fontSize,
    Offset start,
    Offset end,
    double offsetDistance,
    double dimExtension,
    String label,
    BuildContext context,
  ) {
    double tempOffset = 10; // forthe dim line end
    late Offset tempEnd;
    late Offset tempStart;
    late Offset textCenter;

    late double angle;

    late List<String> unit;

    Color fontColor = Colors.red.withAlpha(200);

    late final vec.Vector2 u;
    late final vec.Vector2 uUnit;
    late final vec.Vector2 vUnit;
    late final vec.Vector2 vUnit45;
    late final vec.Matrix2 m;

    final ColorScheme colorScheme = Theme.of(context).colorScheme;
    final TextTheme textTheme = Theme.of(context).textTheme;

    // get the unit
    switch (globalData.unit) {
      case 'metrics':
        unit = PreferredUnit.metrics;
        break;
      case 'imperial':
        unit = PreferredUnit.imperial;
        break;
      default:
        unit = PreferredUnit.metrics;
    }

    // double dimExtension = offsetDistance.abs() / 2;

    final Paint paint =
        Paint()
          ..color = fontColor
          ..strokeWidth = 1.0
          ..style = PaintingStyle.stroke
          ..strokeJoin = StrokeJoin.round;

    // line vector
    u = vec.Vector2(end.dx - start.dx, end.dy - start.dy);
    uUnit = u / u.length;

    // orthogonal vector
    vUnit = vec.Vector2(uUnit.y, uUnit.x); //rotate aniti-clockwise

    // Vector of parallel line with Offset
    Offset newStart = Offset(
      start.dx - vUnit.x * offsetDistance - uUnit.x * dimExtension,
      start.dy - vUnit.y * offsetDistance - uUnit.y * dimExtension,
    );
    Offset newEnd = Offset(
      end.dx - vUnit.x * offsetDistance + uUnit.x * dimExtension,
      end.dy - vUnit.y * offsetDistance + uUnit.y * dimExtension,
    );
    canvas.drawLine(newStart, newEnd, paint);

    // draw start crossing
    tempStart = Offset(
      (start.dx - vUnit.x * offsetDistance) - vUnit.x * dimExtension,
      (start.dy - vUnit.y * offsetDistance) - vUnit.y * dimExtension,
    );
    tempEnd = Offset(
      (start.dx - vUnit.x * offsetDistance) + vUnit.x * dimExtension,
      (start.dy - vUnit.y * offsetDistance) + vUnit.y * dimExtension,
    );
    canvas.drawLine(tempStart, tempEnd, paint);

    m = vec.Matrix2.rotation(-45 * math.pi / 180);
    vUnit45 = m * vUnit;
    tempStart = Offset(
      (start.dx - vUnit.x * offsetDistance) - vUnit45.x * dimExtension,
      (start.dy - vUnit.y * offsetDistance) - vUnit45.y * dimExtension,
    );
    tempEnd = Offset(
      (start.dx - vUnit.x * offsetDistance) + vUnit45.x * dimExtension,
      (start.dy - vUnit.y * offsetDistance) + vUnit45.y * dimExtension,
    );
    canvas.drawLine(tempStart, tempEnd, paint);

    // draw end crossing
    tempStart = Offset(
      (end.dx - vUnit.x * offsetDistance) - vUnit.x * dimExtension,
      (end.dy - vUnit.y * offsetDistance) - vUnit.y * dimExtension,
    );
    tempEnd = Offset(
      (end.dx - vUnit.x * offsetDistance) + vUnit.x * dimExtension,
      (end.dy - vUnit.y * offsetDistance) + vUnit.y * dimExtension,
    );
    canvas.drawLine(tempStart, tempEnd, paint);

    tempStart = Offset(
      (end.dx - vUnit.x * offsetDistance) - vUnit45.x * dimExtension,
      (end.dy - vUnit.y * offsetDistance) - vUnit45.y * dimExtension,
    );
    tempEnd = Offset(
      (end.dx - vUnit.x * offsetDistance) + vUnit45.x * dimExtension,
      (end.dy - vUnit.y * offsetDistance) + vUnit45.y * dimExtension,
    );
    canvas.drawLine(tempStart, tempEnd, paint);

    // indicate span length and beam size

    TextSpan textSpan = TextSpan(
      text: label,
      style: TextStyle(
        fontSize: fontSize,
        color: fontColor,
        fontWeight: FontWeight.bold,
      ),
    );

    TextPainter textPainter = TextPainter(
      text: textSpan,
      textAlign: TextAlign.center,
      textDirection: TextDirection.ltr,
    )..layout();

    textCenter = Offset(
      (start.dx + end.dx) / 2 -
          vUnit.x * (offsetDistance * 1.0 + 12) -
          uUnit.x * textPainter.width / 2,
      (start.dy + end.dy) / 2 -
          vUnit.y * (offsetDistance * 1.0 + 12) -
          uUnit.y * textPainter.width / 2,
    );
    angle = math.atan2(end.dy - start.dy, end.dx - start.dx) * 180 / math.pi;

    canvas.save();
    canvas.translate(textCenter.dx, textCenter.dy);
    canvas.rotate(angle * math.pi / 180);
    textPainter.paint(canvas, Offset.zero);
    canvas.restore();
  }

  void _drawText(
    Canvas canvas,
    double fontSize,
    Offset start,
    Offset end,
    double offsetDistance,
    double dimExtension,
    String label,
    BuildContext context, {
    Color? fontColor,
  }) {
    double tempOffset = 10; // forthe dim line end
    late Offset tempEnd;
    late Offset tempStart;
    late Offset textCenter;

    late double angle;

    late List<String> unit;

    fontColor ??= Theme.of(context).colorScheme.primary;

    late final vec.Vector2 u;
    late final vec.Vector2 uUnit;
    late final vec.Vector2 vUnit;
    late final vec.Vector2 vUnit45;
    late final vec.Matrix2 m;

    final ColorScheme colorScheme = Theme.of(context).colorScheme;
    final TextTheme textTheme = Theme.of(context).textTheme;

    // get the unit
    switch (globalData.unit) {
      case 'metrics':
        unit = PreferredUnit.metrics;
        break;
      case 'imperial':
        unit = PreferredUnit.imperial;
        break;
      default:
        unit = PreferredUnit.metrics;
    }

    // double dimExtension = offsetDistance.abs() / 2;

    final Paint paint =
        Paint()
          ..color = fontColor
          ..strokeWidth = 1.0
          ..style = PaintingStyle.stroke
          ..strokeJoin = StrokeJoin.round;

    // line vector
    u = vec.Vector2(end.dx - start.dx, end.dy - start.dy);
    uUnit = u / u.length;

    // orthogonal vector
    vUnit = vec.Vector2(uUnit.y, uUnit.x); //rotate aniti-clockwise

    // Vector of parallel line with Offset
    Offset newStart = Offset(
      start.dx - vUnit.x * offsetDistance - uUnit.x * dimExtension,
      start.dy - vUnit.y * offsetDistance - uUnit.y * dimExtension,
    );
    Offset newEnd = Offset(
      end.dx - vUnit.x * offsetDistance + uUnit.x * dimExtension,
      end.dy - vUnit.y * offsetDistance + uUnit.y * dimExtension,
    );

    // indicate span length and beam size

    TextSpan textSpan = TextSpan(
      text: label,
      style: TextStyle(
        fontSize: fontSize,
        color: fontColor,
        fontWeight: FontWeight.bold,
      ),
    );

    TextPainter textPainter = TextPainter(
      text: textSpan,
      textAlign: TextAlign.center,
      textDirection: TextDirection.ltr,
    )..layout();

    textCenter = Offset(
      (start.dx + end.dx) / 2 -
          vUnit.x * (offsetDistance * 1.0 + 12) -
          uUnit.x * textPainter.width / 2,
      (start.dy + end.dy) / 2 -
          vUnit.y * (offsetDistance * 1.0 + 12) -
          uUnit.y * textPainter.width / 2,
    );
    angle = math.atan2(end.dy - start.dy, end.dx - start.dx) * 180 / math.pi;

    canvas.save();
    canvas.translate(textCenter.dx, textCenter.dy);
    canvas.rotate(angle * math.pi / 180);
    textPainter.paint(canvas, Offset.zero);
    canvas.restore();
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    // TODO: implement shouldRepaint
    return false;
  }
}
