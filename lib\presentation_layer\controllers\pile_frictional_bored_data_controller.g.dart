// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'pile_frictional_bored_data_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$pileFrictionalBoredDataControllerHash() =>
    r'53453752722a1f42e3816aa547a882315ae71907';

/// See also [PileFrictionalBoredDataController].
@ProviderFor(PileFrictionalBoredDataController)
final pileFrictionalBoredDataControllerProvider =
    AutoDisposeAsyncNotifierProvider<
      PileFrictionalBoredDataController,
      List<PileFrictionalBoredData>
    >.internal(
      PileFrictionalBoredDataController.new,
      name: r'pileFrictionalBoredDataControllerProvider',
      debugGetCreateSourceHash:
          const bool.fromEnvironment('dart.vm.product')
              ? null
              : _$pileFrictionalBoredDataControllerHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$PileFrictionalBoredDataController =
    AutoDisposeAsyncNotifier<List<PileFrictionalBoredData>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
