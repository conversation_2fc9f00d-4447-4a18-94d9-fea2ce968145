import 'package:flutter/material.dart';
import 'package:structify/presentation_layer/small_elements/popup/custom_tooltip.dart';

// --- NEW STANDALONE WIDGET ---
// This widget is now responsible for showing the dialog.
class ProgrammeConfigButton extends StatelessWidget {
  final List<dynamic> programmeItems;
  final Function(Map<String, dynamic>?) onConfigChanged;
  final int? index;
  const ProgrammeConfigButton({
    required this.programmeItems,
    required this.onConfigChanged,
    this.index,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return IconButton(
      onPressed: () => _showConfigDialog(context, programmeItems, index: index),
      icon: const Icon(Icons.adjust_outlined),
      style: ElevatedButton.styleFrom(iconSize: 18.0),
    );
    // return ElevatedButton.icon(
    //   icon: const Icon(Icons.settings),
    //   label: const Text(''),
    //   onPressed: () => _showConfigDialog(context, programmeItems),
    //   style: ElevatedButton.styleFrom(iconSize: 15.0),
    // );
  }

  Future<void> _showConfigDialog(
    BuildContext context,
    List<dynamic> programmeItems,
    {int? index}
  ) async {
    final result = await showDialog<Map<String, dynamic>>(
      context: context,
      barrierDismissible: false, // User must tap a button to close
      builder: (BuildContext context) {
        // The dialog content widget is still the same.
        return ProgrammeConfigDialog(programmeItems,index: index,);
      },
    );
    // Instead of setting state, we call the callback function.
    onConfigChanged(result);
  }
}

// This widget for the dialog's content remains completely unchanged.
// A StatefulWidget is used for the dialog's content to manage its internal state.
class ProgrammeConfigDialog extends StatefulWidget {
  final List<dynamic> programmeItems;
  final List<dynamic>? options;
  final int? index;
  const ProgrammeConfigDialog(
    this.programmeItems, {
    this.options,
    this.index,
    super.key,
  });

  @override
  State<ProgrammeConfigDialog> createState() => _ProgrammeConfigDialogState();
}

class _ProgrammeConfigDialogState extends State<ProgrammeConfigDialog> {
  // State variables for the dialog's controls
  late final List<dynamic> _options1;
  late final List<dynamic> _programmeItems;
  late final List<dynamic> _isChecked;
  late final List<dynamic> _values;
  late final int? _index;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    _isChecked = [true, true];
    _options1 = widget.options ?? ['Start', 'End'];
    _programmeItems = widget.programmeItems;
    _index = widget.index;
    _values = [_options1[0], _options1[0], _programmeItems[_index ?? 0]];
  }

  @override
  void didUpdateWidget(covariant ProgrammeConfigDialog oldWidget) {
    // TODO: implement didUpdateWidget
    super.didUpdateWidget(oldWidget);
    _programmeItems = widget.programmeItems;
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final colorScheme = Theme.of(context).colorScheme;

    TextStyle optionStyle = textTheme.labelMedium!.copyWith(
      color: colorScheme.onSurface,
    );
    TextStyle labelStyle = textTheme.labelLarge!.copyWith(
      color: colorScheme.onSurface,
    );
    return AlertDialog(
      title: const Text('Configure Options'),
      content: SingleChildScrollView(
        child: DefaultTextStyle(
          style: labelStyle,
          child: ListBody(
            children: <Widget>[
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Checkbox(
                    value: _isChecked[0],
                    onChanged: (bool? value) {
                      setState(() {
                        _isChecked[0] = value ?? false;
                      });
                    },
                  ),
                  const SizedBox(width: 5),
                  Text('Align '),
                  const SizedBox(width: 5),
                  DropdownButton<dynamic>(
                    value: _values[0],
                    onChanged: (dynamic newValue) {
                      setState(() {
                        _values[0] = newValue!;
                      });
                    },
                    items: _buildOptions(_options1, labelStyle: optionStyle),
                  ),
                  const SizedBox(width: 5),
                  Text('to the '),
                  const SizedBox(width: 5),
                  DropdownButton<dynamic>(
                    value: _values[1],
                    onChanged: (dynamic newValue) {
                      setState(() {
                        _values[1] = newValue!;
                      });
                    },
                    items: _buildOptions(_options1, labelStyle: optionStyle),
                  ),
                  const SizedBox(width: 5),
                  Text('of item'),
                  const SizedBox(width: 5),
                  Flexible(
                    child: DropdownButton<dynamic>(
                      value: _values[2],
                      onChanged: (dynamic newValue) {
                        setState(() {
                          _values[2] = newValue!;
                        });
                      },
                      items: _buildOptions(
                        _programmeItems,
                        labelStyle: optionStyle,
                      ),
                    ),
                  ),
                ],
              ),
              Row(
                children: [
                  Checkbox(
                    value: _isChecked[1],
                    onChanged: (bool? value) {
                      setState(() {
                        _isChecked[1] = value ?? false;
                      });
                    },
                  ),
                  const SizedBox(width: 5),
                  Text('Subsequent items offset accordingly'),
                  const SizedBox(width: 5),
                  CustomTooltip(
                    tooltipText:
                        'Items below move their start at the same\n'
                        'amount as the selected item did',
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
      actions: <Widget>[
        TextButton(
          child: const Text('Cancel'),
          onPressed: () {
            // Closes the dialog, returning null
            Navigator.of(context).pop();
          },
        ),
        TextButton(
          child: const Text('OK'),
          onPressed: () {
            // 4. This is the callback that returns the selected data
            final result = {'isChecked': _isChecked, 'values': _values};
            // Closes the dialog and returns the result map
            Navigator.of(context).pop(result);
          },
        ),
      ],
    );
  }

  List<DropdownMenuItem<dynamic>> _buildOptions(
    List<dynamic> options, {
    TextStyle? labelStyle,
  }) {
    return options.map<DropdownMenuItem<dynamic>>((dynamic value) {
      return DropdownMenuItem<dynamic>(
        value: value,
        child: Text(value, style: labelStyle),
      );
    }).toList();
  }
}
