// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'recommend_load.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_RecommendedLoad _$RecommendedLoadFromJson(Map<String, dynamic> json) =>
    _RecommendedLoad(
      usage: json['usage'] as String? ?? '',
      finish: (json['finish'] as num?)?.toDouble() ?? 50.0,
      service: (json['service'] as num?)?.toDouble() ?? 1.0,
      partitionLoad: (json['partitionLoad'] as num?)?.toDouble() ?? 0.0,
      sdl: (json['sdl'] as num?)?.toDouble() ?? 0.0,
      ll: (json['ll'] as num?)?.toDouble() ?? 0.0,
      frp: (json['frp'] as num?)?.toDouble() ?? 0.0,
    );

Map<String, dynamic> _$RecommendedLoadToJson(_RecommendedLoad instance) =>
    <String, dynamic>{
      'usage': instance.usage,
      'finish': instance.finish,
      'service': instance.service,
      'partitionLoad': instance.partitionLoad,
      'sdl': instance.sdl,
      'll': instance.ll,
      'frp': instance.frp,
    };
