import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:structify/domain_layer/footing_scheme_input.dart';
import '../../domain_layer/column_scheme_data.dart';
import '../screen/homescreen.dart';

part 'footing_scheme_input_controller.g.dart';

@riverpod
class FootingSchemeInputController extends _$FootingSchemeInputController {
  @override
  FutureOr<FootingSchemeInput> build() async {
    // print('Build: Slab Scheme Input');
    FootingSchemeInput footingSchemeInput =
        await ref
            .read(appDatabaseControllerProvider.notifier)
            .queryFootingSchemeInput();
    final data1 = ref.watch(columnSchemeDataControllerProvider);
    final data2 = ref.watch(footingSchemeInputGlobalControllerProvider);
    return data1.when(
      data: (colData) async {
        return data2.when(
          data: (inputGlobal) async {
            //* Validate the load (if using column load)
            if (footingSchemeInput.useSelectColLoad) {
              final selectedColumn = colData.firstWhere(
                (scheme) => scheme.isSelected,
                orElse: () => ColumnSchemeData(),
              );
              if (inputGlobal.colLoadFactor == 0) {
                inputGlobal = inputGlobal.copyWith(colLoadFactor: 1.0);
                await ref
                    .read(footingSchemeInputGlobalControllerProvider.notifier)
                    .updateTable(colLoadFactor: 1.0);
              }
              footingSchemeInput = footingSchemeInput.copyWith(
                slsLoad: selectedColumn.slsLoad / inputGlobal.colLoadFactor,
                ulsLoad: selectedColumn.ulsLoad / inputGlobal.colLoadFactor,
              );
            }
            return footingSchemeInput;
          },
          error: (error, stackTrace) => FootingSchemeInput(),
          loading: () => FootingSchemeInput(),
        );
      },
      error: (error, stackTrace) => FootingSchemeInput(),
      loading: () => FootingSchemeInput(),
    );
  }

  Future<void> updateTable({
    double? fcu,
    double? cover,
    double? mainKValue,
    double? mainSteelRatio,
    int? minS,
    int? maxS,
    double? maxDepth,
    double? minDepth,
    int? maxLayers,
    String? groundType,
    double? soilNValue,
    double? footingTopLevel,
    double? waterTableLevel,
    double? rockCapacity,
    String? colShape,
    double? columnSize,
    double? slsLoad,
    double? ulsLoad,
    bool? useSelectColLoad,
  }) async {
    final x = await future;
    // Create a list of updates
    final newState = x.copyWith(
      fcu: fcu ?? x.fcu,
      cover: cover ?? x.cover,
      mainKValue: mainKValue ?? x.mainKValue,
      mainSteelRatio: mainSteelRatio ?? x.mainSteelRatio,
      minS: minS ?? x.minS,
      maxS: maxS ?? x.maxS,
      maxDepth: maxDepth ?? x.maxDepth,
      minDepth: minDepth ?? x.minDepth,
      maxLayers: maxLayers ?? x.maxLayers,
      groundType: groundType ?? x.groundType,
      soilNValue: soilNValue ?? x.soilNValue,
      footingTopLevel: footingTopLevel ?? x.footingTopLevel,
      waterTableLevel: waterTableLevel ?? x.waterTableLevel,
      rockCapacity: rockCapacity ?? x.rockCapacity,
      colShape: colShape ?? x.colShape,
      columnSize: columnSize ?? x.columnSize,
      slsLoad: slsLoad ?? x.slsLoad,
      ulsLoad: ulsLoad ?? x.ulsLoad,
      useSelectColLoad: useSelectColLoad ?? x.useSelectColLoad,
    );
    // Update the state only if there are changes
    state = AsyncData(newState);
  }
}
