// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'global_data_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$globalDataControllerHash() =>
    r'77aead2f703887e2ad39c33b4888dee891581716';

/// See also [GlobalDataController].
@ProviderFor(GlobalDataController)
final globalDataControllerProvider =
    AutoDisposeAsyncNotifierProvider<GlobalDataController, GlobalData>.internal(
      GlobalDataController.new,
      name: r'globalDataControllerProvider',
      debugGetCreateSourceHash:
          const bool.fromEnvironment('dart.vm.product')
              ? null
              : _$globalDataControllerHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$GlobalDataController = AutoDisposeAsyncNotifier<GlobalData>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
