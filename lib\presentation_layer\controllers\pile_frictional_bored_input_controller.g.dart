// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'pile_frictional_bored_input_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$pileFrictionalBoredInputControllerHash() =>
    r'6630ad18f24eddb90028447c6758f2ebd64690da';

/// See also [PileFrictionalBoredInputController].
@ProviderFor(PileFrictionalBoredInputController)
final pileFrictionalBoredInputControllerProvider =
    AutoDisposeAsyncNotifierProvider<
      PileFrictionalBoredInputController,
      PileFrictionalBoredInput
    >.internal(
      PileFrictionalBoredInputController.new,
      name: r'pileFrictionalBoredInputControllerProvider',
      debugGetCreateSourceHash:
          const bool.fromEnvironment('dart.vm.product')
              ? null
              : _$pileFrictionalBoredInputControllerHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$PileFrictionalBoredInputController =
    AutoDisposeAsyncNotifier<PileFrictionalBoredInput>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
