import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:structify/presentation_layer/small_elements/popup/custom_tooltip.dart';

//domain layer
import '../../domain_layer/preferences.dart';

// presentation layer
import '../screen/homescreen.dart';
import 'input/custom_stateful_double_input.dart';
import 'input/custom_stateful_text_input.dart';
import 'input/custom_stateful_int_input.dart';
// import '../small_elements/global_data_ui.dart';
// import '../small_elements/loadcals_summary_ui.dart';

class StrZoneTableUi extends ConsumerStatefulWidget {
  const StrZoneTableUi({
    this.isExpanded,
    this.maxHeight,
    this.minHeight,
    super.key,
  });

  final bool? isExpanded;
  final double? maxHeight;
  final double? minHeight;

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _StrZoneTableUiState();
}

class _StrZoneTableUiState extends ConsumerState<StrZoneTableUi>
    with WidgetsBindingObserver {
  late bool _isExpanded;
  late double _maxHeight;
  late double _minHeight;
  late CustomStatefulTextInput _usageInput;
  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void initState() {
    WidgetsBinding.instance.addObserver(this);
    _isExpanded = widget.isExpanded ?? false;
    _maxHeight = widget.maxHeight ?? 300;
    _minHeight = widget.minHeight ?? 50;
    _usageInput = CustomStatefulTextInput();

    // _updateHeights();

    super.initState();
  }

  @override
  void didChangeDependencies() {
    _updateHeights();
    super.didChangeDependencies();
    // Update heights when dependencies change, including after initState
  }

  @override
  void didChangeMetrics() {
    // This method is called when the metrics change (like resizing the window)
    setState(() {
      _updateHeights();
    });
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;
    final globalData = ref.watch(globalDataControllerProvider);
    final strZoneTables = ref.watch(strZoneTablesControllerProvider);
    final scrollController = ScrollController();

    return globalData.when(
      data: (data) {
        late final List<String> unit;
        switch (data.unit) {
          case 'metrics':
            unit = PreferredUnit.metrics;
            break;
          case 'imperial':
            unit = PreferredUnit.imperial;
            break;
          default:
            unit = PreferredUnit.metrics;
            break;
        }

        return strZoneTables.when(
          data: (tables) {
            return Column(
              children: [
                Padding(
                  padding: const EdgeInsets.fromLTRB(10, 0, 10, 0),
                  child: Divider(),
                ),
                Padding(
                  padding: const EdgeInsets.fromLTRB(10, 0, 0, 0),
                  child: Align(
                    alignment: Alignment.centerLeft,
                    child: Wrap(
                      crossAxisAlignment: WrapCrossAlignment.center,
                      children: [
                        Wrap(
                          children: [
                            IconButton(
                              icon: Icon(
                                _isExpanded
                                    ? Icons.keyboard_arrow_up
                                    : Icons.keyboard_arrow_down,
                                color: colorScheme.onSurface,
                              ),
                              onPressed: () {
                                setState(() {
                                  _isExpanded = !_isExpanded;
                                });
                              },
                            ),
                            Text(
                              'Structural Zone Calculations',
                              style: textTheme.titleLarge!.copyWith(
                                color: colorScheme.onSurface,
                              ),
                            ),
                          ],
                        ),
                        SizedBox(width: 10.0),
                        Container(
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(5.0),
                            color: colorScheme.surfaceContainer.withAlpha(225),
                            border: Border.all(
                              color: Colors.black.withAlpha(125),
                            ),
                          ),
                          child: Padding(
                            padding: const EdgeInsets.all(2.0),
                            child: Text(
                              '(Total: ${tables.length})',
                              style: textTheme.labelMedium!.copyWith(
                                color: colorScheme.onSurfaceVariant.withAlpha(
                                  225,
                                ),
                              ),
                            ),
                          ),
                        ),
                        SizedBox(width: 10.0),
                        CustomTooltip(
                          tooltipText: 'Never address the lowest floor.'
                          )
                      ],
                    ),
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.fromLTRB(10, 0, 10, 0),
                  child: Divider(),
                ),
                ClipRect(
                  child: ConstrainedBox(
                    constraints: BoxConstraints(
                      maxHeight: _isExpanded ? _maxHeight : 0,
                    ),
                    child: Scrollbar(
                      controller: scrollController,
                      thumbVisibility: true,
                      child: ListView.builder(
                        controller: scrollController,
                        itemCount: tables.length,
                        itemBuilder: (context, index) {
                          return Padding(
                            padding: const EdgeInsets.fromLTRB(
                              8.0,
                              4.0,
                              8.0,
                              4.0,
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              mainAxisAlignment: MainAxisAlignment.start,
                              children: [
                                Container(
                                  decoration: BoxDecoration(
                                    shape: BoxShape.circle,
                                    color: colorScheme.surfaceContainer
                                        .withAlpha(200),
                                    // borderRadius: BorderRadius.circular(5),
                                    border: Border.all(color: Colors.black),
                                  ),
                                  child: Padding(
                                    padding: const EdgeInsets.all(4.0),
                                    child: Text(
                                      '${index + 1}',
                                      style: textTheme.labelLarge!.copyWith(
                                        color: colorScheme.onSurfaceVariant
                                            .withAlpha(250),
                                      ),
                                    ),
                                  ),
                                ),
                                IconButton(
                                  icon: Icon(
                                    Icons.copy_all_outlined,
                                    color: colorScheme.onSurface,
                                  ),
                                  onPressed: () {
                                    ref
                                        .read(
                                          strZoneTablesControllerProvider
                                              .notifier,
                                        )
                                        .copyAndInsertTable(index);
                                  },
                                ),
                                IconButton(
                                  icon: Icon(
                                    Icons.delete,
                                    color: colorScheme.onSurface,
                                  ),
                                  onPressed: () {
                                    ref
                                        .read(
                                          strZoneTablesControllerProvider
                                              .notifier,
                                        )
                                        .deleteTable(tables[index].strZoneId);
                                  },
                                ),
                                SizedBox(width: 5.0),
                                Flexible(
                                  child: CustomStatefulTextInput(
                                    title: 'Floor',
                                    key: ValueKey(
                                      '${tables[index].strZoneId}_Floor',
                                    ),
                                    value: tables[index].floor,
                                    onChanged: (value) {
                                      ref
                                          .read(
                                            strZoneTablesControllerProvider
                                                .notifier,
                                          )
                                          .updateTable(
                                            tables[index].strZoneId,
                                            floor: value,
                                          );
                                    },
                                  ),
                                ),
                                SizedBox(width: 5.0),
                                Flexible(
                                  child: CustomStatefulIntInput(
                                    title: 'No. of Floors',
                                    key: ValueKey(
                                      '${tables[index].strZoneId}_NosOfFloor',
                                    ),
                                    value: tables[index].nosOfFloor,
                                    onChanged: (value) {
                                      ref
                                          .read(
                                            strZoneTablesControllerProvider
                                                .notifier,
                                          )
                                          .updateTable(
                                            tables[index].strZoneId,
                                            nosOfFloor: value,
                                          );
                                    },
                                  ),
                                ),
                                SizedBox(width: 5.0),
                                Flexible(
                                  child: CustomStatefulDoubleInput(
                                    title: 'Height [${unit[4]}]',
                                    key: ValueKey(
                                      '${tables[index].strZoneId}_Height',
                                    ),
                                    value: tables[index].height,
                                    listener: (hasFocus, value) {
                                      if (!hasFocus) {
                                        ref
                                            .read(
                                              strZoneTablesControllerProvider
                                                  .notifier,
                                            )
                                            .updateTable(
                                              tables[index].strZoneId,
                                              height: value,
                                            );
                                      }
                                    },
                                  ),
                                ),
                                SizedBox(width: 5.0),
                                Flexible(
                                  child: CustomStatefulDoubleInput(
                                    title: 'Clear [${unit[4]}]',
                                    key: ValueKey(
                                      '${tables[index].strZoneId}_Clear',
                                    ),
                                    value: tables[index].clear,
                                    listener: (hasFocus, value) {
                                      if (!hasFocus) {
                                        ref
                                            .read(
                                              strZoneTablesControllerProvider
                                                  .notifier,
                                            )
                                            .updateTable(
                                              tables[index].strZoneId,
                                              clear: value,
                                            );
                                      }
                                    },
                                  ),
                                ),
                                SizedBox(width: 5.0),
                                Flexible(
                                  child: CustomStatefulDoubleInput(
                                    title: 'Finish [${unit[4]}]',
                                    key: ValueKey(
                                      '${tables[index].strZoneId}_Finish',
                                    ),
                                    value: tables[index].finish,
                                    listener: (hasFocus, value) {
                                      if (!hasFocus) {
                                        ref
                                            .read(
                                              strZoneTablesControllerProvider
                                                  .notifier,
                                            )
                                            .updateTable(
                                              tables[index].strZoneId,
                                              finish: value,
                                            );
                                      }
                                    },
                                  ),
                                ),

                                SizedBox(width: 5.0),
                                Flexible(
                                  child: CustomStatefulDoubleInput(
                                    title: 'Service [${unit[4]}]',
                                    key: ValueKey(
                                      '${tables[index].strZoneId}_Service',
                                    ),
                                    value: tables[index].service,
                                    listener: (hasFocus, value) {
                                      if (!hasFocus) {
                                        ref
                                            .read(
                                              strZoneTablesControllerProvider
                                                  .notifier,
                                            )
                                            .updateTable(
                                              tables[index].strZoneId,
                                              service: value,
                                            );
                                      }
                                    },
                                  ),
                                ),
                              ],
                            ),
                          );
                        },
                      ),
                    ),
                  ),
                ),
              ],
            );
          },
          error: (error, stackTrace) => Text(error.toString()),
          loading: () => CircularProgressIndicator(),
        );
      },
      error: (error, stackTrace) {
        return Text('Error: $error');
      },
      loading: () {
        return const CircularProgressIndicator();
      },
    );
  }

  void _updateHeights() {
    final double screenHeight = MediaQuery.of(context).size.height;
    _maxHeight =
        (widget.maxHeight == null)
            ? screenHeight * 0.25
            : (widget.maxHeight! > screenHeight * 0.25)
            ? screenHeight * 0.25
            : widget.maxHeight!;

    _minHeight =
        (widget.minHeight == null)
            ? screenHeight * 0.1
            : (widget.minHeight! > screenHeight * 0.1)
            ? screenHeight * 0.1
            : widget.minHeight!;

    // Adjust _maxHeight if needed
    if (_maxHeight < _minHeight) {
      _maxHeight = _minHeight;
    }
  }
}
