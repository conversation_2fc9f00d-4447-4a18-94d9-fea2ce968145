// import 'dart:nativewrappers/_internal/vm/lib/math_patch.dart';

import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

//below for printing
// import 'dart:typed_data';
// import 'dart:ui';

//domain layer

// presentation layer
import '../../domain_layer/preferences.dart';
import '../screen/homescreen.dart';
// import '../small_elements/custom_stateful_double_input.dart';
// import '../small_elements/custom_stateful_text_input.dart';
// import '../small_elements/global_data_ui.dart';

//misc


class SchemeComparisonSummary extends ConsumerStatefulWidget {
  const SchemeComparisonSummary({
    this.isExpanded,
    this.maxHeight,
    this.minHeight,
    super.key,
  });

  final bool? isExpanded;
  final double? maxHeight;
  final double? minHeight;

  @override
  ConsumerState<ConsumerStatefulWidget> createState() =>
      _SchemeComparisonSummaryState();
}

class _SchemeComparisonSummaryState
    extends ConsumerState<SchemeComparisonSummary>
    with WidgetsBindingObserver {
  late bool _isExpanded;
  late double _maxHeight;
  late double _minHeight;
  late ScrollController _scrollController;

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void initState() {
    WidgetsBinding.instance.addObserver(this);
    _isExpanded = widget.isExpanded ?? false;
    _maxHeight = widget.maxHeight ?? 300;
    _minHeight = widget.minHeight ?? 50;
    _scrollController = ScrollController();
    // _updateHeights();

    super.initState();
  }

  @override
  void didChangeDependencies() {
    _updateHeights();
    super.didChangeDependencies();
    // Update heights when dependencies change, including after initState
  }

  @override
  void didChangeMetrics() {
    // This method is called when the metrics change (like resizing the window)
    setState(() {
      _updateHeights();
    });
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;
    final globalData = ref.watch(globalDataControllerProvider);
    final TextStyle titleTextStyle = textTheme.titleMedium!.copyWith(
      color: colorScheme.onSurface,
    );
    final TextStyle bodyTextStyle = textTheme.bodyMedium!.copyWith(
      color: colorScheme.onSurface,
    );
    final TextStyle highlightText = textTheme.labelLarge!.copyWith(
      color: colorScheme.primary,
    );
    late final List<String> unit;
    return globalData.when(
      data: (data) {
        if (data.unit == 'metrics') {
          unit = PreferredUnit.metrics;
        } else {
          unit = PreferredUnit.imperial;
        }
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.fromLTRB(10, 0, 10, 0),
              child: Divider(),
            ),
            Padding(
              padding: const EdgeInsets.fromLTRB(10, 0, 0, 0),
              child: Align(
                alignment: Alignment.centerLeft,
                child: Wrap(
                  crossAxisAlignment: WrapCrossAlignment.center,
                  children: [
                    IconButton(
                      icon: Icon(
                        _isExpanded ? Icons.expand_less : Icons.expand_more,
                      ),
                      color: colorScheme.onSurface,
                      onPressed: () {
                        setState(() {
                          _isExpanded = !_isExpanded;
                        });
                      },
                    ),
                    Text(
                      'Recommended Loading',
                      style: textTheme.titleLarge!.copyWith(
                        color: colorScheme.onSurface,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            Padding(
              padding: const EdgeInsets.fromLTRB(10, 0, 10, 0),
              child: Divider(),
            ),
            ClipRect(
              child: ConstrainedBox(
                constraints: BoxConstraints(
                  maxHeight: _isExpanded ? _maxHeight : 0,
                ),
                child: Scrollbar(
                  controller: _scrollController,
                  thumbVisibility: true,
                  trackVisibility: false,
                  child: SingleChildScrollView(
                    scrollDirection: Axis.vertical,
                    controller: _scrollController,
                    child: Padding(
                      padding: const EdgeInsets.fromLTRB(10, 0, 0, 0),
                      child: DefaultTextStyle(
                        style: textTheme.bodyMedium!.copyWith(
                          color: colorScheme.onSurface,
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            _itemBlockWithTable(
                              backgroundColor: Colors.transparent,
                              borderColor: Colors.transparent,
                              'RC vs Steel',
                              ['Item', '\u2705RC', 'Steel'],
                              [
                                [
                                  'Material',
                                  '\u2611Readily available',
                                  'Procurement/pre-fabrication costs time',
                                ],
                                [
                                  'Workers',
                                  '\u2611Normal labour',
                                  'Skilled labour (welding, coating, connection)',
                                ],
                                [
                                  'Fire and corrosion',
                                  '\u2611Durable',
                                  'Special coating required',
                                ],
                                [
                                  'Human comfort',
                                  '\u2611Stiffer with less vibration',
                                  'More flexible with more vibration',
                                ],
                                [
                                  'Structural efficiency',
                                  'Heavior',
                                  '\u2611Lighter',
                                ],
                                [
                                  'Aesthetic',
                                  'Closer column grid',
                                  '\u2611Sparse column grid',
                                ],
                                [
                                  'Maintenance Cost',
                                  '\u2611Low',
                                  'High (coating)',
                                ],
                              ],
                            ),
                            _itemBlockWithTable(
                              backgroundColor: Colors.transparent,
                              borderColor: Colors.transparent,
                              'RC vs RC',
                              [
                                'Item',
                                '\u27051. RC Beam/Column\n\u27052. RC Transfer Beam',
                                '1. RC Flat Slab\n2. RC Transfer Plate',
                              ],
                              [
                                [
                                  'Flexibility',
                                  '\u2611Allow future slab opening',
                                  'Limited size and shape of future slab opening',
                                ],
                                [
                                  'Structural efficiency',
                                  '\u2611Lighter',
                                  'Heavier due to thick plate',
                                ],
                                [
                                  'Foudnation Cost',
                                  '\u2611Less',
                                  'More expensive due to heavier superstructure',
                                ],
                                [
                                  'Temporary Works ',
                                  '\u2611Less',
                                  'Extensive due to thick plate across entire floor ',
                                ],
                                [
                                  'Human comfort',
                                  'More flexible with more vibration',
                                  '\u2611Stiffer with less vibration',
                                ],
                                [
                                  'Heat and sound isolation',
                                  'Normal floor thickness with less heat/sound isolation',
                                  '\u2611Better with thick plate',
                                ],
                              ],
                            ),
                            _itemBlockWithTable(
                              backgroundColor: Colors.transparent,
                              borderColor: Colors.transparent,
                              'Pile vs Pile',
                              [
                                'Item',
                                '\u2705Frictional Bored Pile',
                                'Socketed H Steel Pile',
                              ],
                              [
                                [
                                  'Material',
                                  '\u2611Readily available',
                                  'Procurement/pre-fabrication costs time',
                                ],
                                [
                                  'Workers',
                                  '\u2611Normal labour',
                                  'Skilled labour (steel pile verticality control)',
                                ],
                                [
                                  'Settlement',
                                  '\u2611Stiffer with less settlement',
                                  'More flexible with more settlement',
                                ],
                                [
                                  'Corrosion',
                                  '\u2611Durable',
                                  'Special coating required',
                                ],
                                [
                                  'Environment',
                                  'Soil disposal',
                                  '\u2611Less spoil',
                                ],
                                [
                                  'Structural efficiency',
                                  '\u2611Less piles',
                                  'More piles',
                                ],
                                [
                                  'Time',
                                  '\u2611Shorter',
                                  'Longer due to extra rock drilling',
                                ],
                              ],
                            ),
                            _itemBlockWithTable(
                              backgroundColor: Colors.transparent,
                              borderColor: Colors.transparent,
                              'Pile vs Pile',
                              [
                                'Item',
                                '\u2705Frictional Bored Pile',
                                'Driven H Steel Pile',
                              ],
                              [
                                [
                                  'Material',
                                  '\u2611Readily available',
                                  'Procurement/pre-fabrication costs time',
                                ],
                                [
                                  'Workers',
                                  '\u2611Normal labour',
                                  'Skilled labour (steel pile verticality control)',
                                ],
                                [
                                  'Settlement',
                                  '\u2611Stiffer with less settlement',
                                  'More flexible with more settlement',
                                ],
                                [
                                  'Corrosion',
                                  '\u2611Durable',
                                  'Special coating required',
                                ],
                                ['Spoil', 'Soil disposal', '\u2611Less spoil'],
                                [
                                  'Nuisance',
                                  '\u2611Silent operation',
                                  'Noise from hammering',
                                ],
                                [
                                  'Structural efficiency',
                                  '\u2611Less piles',
                                  'More piles',
                                ],
                                [
                                  'Time',
                                  'Longer due to pile excavation',
                                  '\u2611Shorter',
                                ],
                              ],
                            ),
                            _itemBlockWithTable(
                              backgroundColor: Colors.transparent,
                              borderColor: Colors.transparent,
                              'Pile vs Footing',
                              [
                                'Item',
                                '\u2705Frictional Bored Pile',
                                'Pad/Raft Footing',
                              ],
                              [
                                [
                                  'Workers',
                                  'Skilled labour (piling machine operator)',
                                  '\u2611Normal labour',
                                ],
                                [
                                  'Settlement',
                                  '\u2611Stiffer with less settlement',
                                  'More flexible with more settlement',
                                ],

                                [
                                  'Environment',
                                  '\u2611Less soil disposal',
                                  'More spoil from bulk excavation',
                                ],
                                [
                                  'Time',
                                  '\u2611Shorter',
                                  'Longer due to bulk excavation and possibly temporary lateral support system',
                                ],
                              ],
                            ),
                            _itemBlockWithTable(
                              backgroundColor: Colors.transparent,
                              borderColor: Colors.transparent,
                              'Pile vs Footing',
                              [
                                'Item',
                                'Frictional Bored Pile',
                                '\u2705Pad/Raft Footing',
                              ],
                              [
                                [
                                  'Workers',
                                  'Skilled labour (piling machine operator)',
                                  '\u2611Normal labour',
                                ],
                                [
                                  'Settlement',
                                  '\u2611Stiffer with less settlement',
                                  'More flexible with more settlement',
                                ],

                                [
                                  'Construction Tolerance',
                                  'Less (pile verticality)',
                                  '\u2611More',
                                ],
                                [
                                  'Time',
                                  'Longer (pile head trimming with thick caps)',
                                  '\u2611Shorter (open cut then use footing as basement floor)',
                                ],
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ],
        );
      },
      error: (error, stackTrace) => Text(error.toString()),
      loading: () => CircularProgressIndicator(),
    );
  }

  Widget _itemBlockWithTable(
    String requirementTitle,
    List<String> requirementSutitle,
    List<List<String>> requirementDetails, {
    Color? backgroundColor,
    Color? borderColor,
    TextStyle? requirementTitleStyle,
  }) {
    //*initialize
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;
    backgroundColor ??= colorScheme.primaryContainer.withAlpha(50);
    borderColor ??= colorScheme.primary.withAlpha(50);
    requirementTitleStyle ??= textTheme.titleMedium!.copyWith(
      color: colorScheme.onTertiaryContainer,
    );

    return Container(
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(8.0),
        border: Border.all(width: 1.5, color: borderColor),
      ),
      child: Padding(
        padding: const EdgeInsets.fromLTRB(8.0, 0, 8.0, 0.0),
        child: DefaultTextStyle(
          style: textTheme.bodyMedium!.copyWith(
            color: colorScheme.onPrimaryContainer,
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(5.0),
                  color: colorScheme.tertiaryContainer.withAlpha(150),
                  border: Border.all(
                    color: colorScheme.tertiary.withAlpha(200),
                    width: 1.0,
                  ),
                ),
                child: Padding(
                  padding: const EdgeInsets.all(5.0),
                  child: Text(requirementTitle, style: requirementTitleStyle),
                ),
              ),
              SizedBox(height: 5.0),
              Row(
                children: [
                  Expanded(
                    flex: 2,
                    child: Table(
                      defaultVerticalAlignment:
                          TableCellVerticalAlignment.middle,
                      border: TableBorder.all(
                        color: colorScheme.onPrimaryContainer.withAlpha(50),
                        width: 0.75,
                      ),
                      // columnWidths: const {
                      //   0: FlexColumnWidth(2),
                      //   1: FlexColumnWidth(3),
                      // },
                      children: [
                        TableRow(
                          decoration: BoxDecoration(
                            color: colorScheme.primaryContainer.withAlpha(200),
                          ),
                          children: [
                            ...List.generate(requirementSutitle.length, (
                              index,
                            ) {
                              return Align(
                                child: Padding(
                                  padding: const EdgeInsets.all(4.0),
                                  child: Text(
                                    requirementSutitle[index],
                                    style: textTheme.labelLarge!.copyWith(
                                      color: colorScheme.onPrimaryContainer,
                                    ),
                                  ),
                                ),
                              );
                            }),
                          ],
                        ),
                        ...List.generate(requirementDetails.length, (index) {
                          return TableRow(
                            decoration: BoxDecoration(
                              color: colorScheme.primaryContainer.withAlpha(50),
                            ),
                            children: [
                              ...List.generate(
                                requirementDetails[index].length,
                                (index2) {
                                  return Align(
                                    child: Padding(
                                      padding: const EdgeInsets.all(4.0),
                                      child: Text(
                                        requirementDetails[index][index2],
                                        style: textTheme.bodyMedium!.copyWith(
                                          color: colorScheme.onPrimaryContainer,
                                        ),
                                      ),
                                    ),
                                  );
                                },
                              ),
                            ],
                          );
                        }),
                      ],
                    ),
                  ),
                  SizedBox(width: 10.0),
                ],
              ),
              SizedBox(height: 5.0),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildVerticalDividor(
    List<String> requirement,
    List<String> solution, {
    Color dividorColor = Colors.black,
    double dividorWidth = 1.5,
  }) {
    return Container(
      width: 10,
      decoration: BoxDecoration(
        border: BorderDirectional(
          start: BorderSide(color: dividorColor, width: dividorWidth),
        ),
      ),
      // child: LayoutBuilder(
      //   builder: (context, constraints) {
      //     return SizedBox(
      //       height: constraints.maxHeight,
      //     );
      //   },
      child: Column(
        children: [
          ...List.generate(max(requirement.length + 1, solution.length), (
            index,
          ) {
            return Text('');
          }),
        ],
      ),
    );
  }

  // Method to update heights based on the current media query
  void _updateHeights() {
    final double screenHeight = MediaQuery.of(context).size.height;
    _maxHeight =
        (widget.maxHeight == null)
            ? screenHeight * 0.7
            : (widget.maxHeight! > screenHeight * 0.7)
            ? screenHeight * 0.7
            : widget.maxHeight!;

    _minHeight =
        (widget.minHeight == null)
            ? screenHeight * 0.1
            : (widget.minHeight! > screenHeight * 0.1)
            ? screenHeight * 0.1
            : widget.minHeight!;

    // Adjust _maxHeight if needed

    if (_maxHeight < _minHeight) {
      _maxHeight = _minHeight;
    }
  }
}
