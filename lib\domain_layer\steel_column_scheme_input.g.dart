// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'steel_column_scheme_input.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_SteelColumnSchemeInput _$SteelColumnSchemeInputFromJson(
  Map<String, dynamic> json,
) => _SteelColumnSchemeInput(
  usage: json['usage'] as String? ?? '',
  slabThickness: (json['slabThickness'] as num?)?.toDouble() ?? 130.0,
  loadWidth: (json['loadWidth'] as num?)?.toDouble() ?? 8,
  loadLength: (json['loadLength'] as num?)?.toDouble() ?? 12,
  nosOfFloor: (json['nosOfFloor'] as num?)?.toInt() ?? 1,
  steelColumnSchemeInputId: json['steelColumnSchemeInputId'] as String? ?? '',
);

Map<String, dynamic> _$SteelColumnSchemeInputToJson(
  _SteelColumnSchemeInput instance,
) => <String, dynamic>{
  'usage': instance.usage,
  'slabThickness': instance.slabThickness,
  'loadWidth': instance.loadWidth,
  'loadLength': instance.loadLength,
  'nosOfFloor': instance.nosOfFloor,
  'steelColumnSchemeInputId': instance.steelColumnSchemeInputId,
};
