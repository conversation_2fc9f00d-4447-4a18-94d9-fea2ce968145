// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'column_scheme_input_global_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$columnSchemeInputGlobalControllerHash() =>
    r'00b764f12162f9f329d157d414133980aebebed7';

/// See also [ColumnSchemeInputGlobalController].
@ProviderFor(ColumnSchemeInputGlobalController)
final columnSchemeInputGlobalControllerProvider =
    AutoDisposeAsyncNotifierProvider<
      ColumnSchemeInputGlobalController,
      ColumnSchemeInputGlobal
    >.internal(
      ColumnSchemeInputGlobalController.new,
      name: r'columnSchemeInputGlobalControllerProvider',
      debugGetCreateSourceHash:
          const bool.fromEnvironment('dart.vm.product')
              ? null
              : _$columnSchemeInputGlobalControllerHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$ColumnSchemeInputGlobalController =
    AutoDisposeAsyncNotifier<ColumnSchemeInputGlobal>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
