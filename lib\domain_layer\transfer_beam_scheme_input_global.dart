import 'package:freezed_annotation/freezed_annotation.dart';
// import 'package:flutter/foundation.dart';
part 'transfer_beam_scheme_input_global.freezed.dart';
part 'transfer_beam_scheme_input_global.g.dart';

@freezed
abstract class TransferBeamSchemeInputGlobal
    with _$TransferBeamSchemeInputGlobal {
  const TransferBeamSchemeInputGlobal._();
  factory TransferBeamSchemeInputGlobal({
    @Default('1') String id,
    @Default(20.0) double span,
    @Default(10.0) double loadWidth,
    @Default(1000.0) double strZone,
    @Default(45.0) double fcu,
    @Default(40.0) double cover,
    @Default(0.25) double mainKValue,
    @Default(0.025) double mainSteelRatio,
    @Default(100) int minS,
    @Default(300) int maxS,
    @Default(2000.0) double maxWidth,
    @Default(4) int maxLayers,
    @Default('') String usage,
    @Default(150.0) double slabThickness,
    @Default(false) bool useSlabSelected,
  }) = _TransferBeamSchemeInputGlobal;
  

  factory TransferBeamSchemeInputGlobal.fromJson(Map<String, Object?> json) =>
      _$TransferBeamSchemeInputGlobalFromJson(json);
}
