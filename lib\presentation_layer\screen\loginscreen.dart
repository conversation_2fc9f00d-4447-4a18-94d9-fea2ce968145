import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

//main
import '../../main.dart';

//domain layer
import '../../domain_layer/route_names.dart';

// presentation layer
// import '../presentation_layer/custom_stateful_double_input.dart';
import '../small_elements/button/function_button.dart';

class LoginScreen extends ConsumerWidget {
  const LoginScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // final CustomStatefulTextInput input = CustomStatefulTextInput(
    //   title: 'License Key',
    // );

    return Scaffold(
      appBar: AppBar(title: const Text('Login')),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            FunctionButton(
              labelText: 'Login',
              onTap: (value) async {
                final x = await licenseKeyValidation(context);
                if (x) {
                  context.pushNamed(RouteNames.home);
                } else {
                  //do nothing
                }
              },
            ),
          ],
        ),
      ),
    );
  }
}

Future<bool> licenseKeyValidation(BuildContext context) async {
  //produce license key
  RegExp regExp = RegExp(r'\{(.*?)\}');
  Match? match = regExp.firstMatch(deviceData['deviceId']);
  String extracted = '';
  int licenseKey = 1;
  if (match != null) {
    // Extract the matched group
    extracted = match.group(1)!;
    extracted = extracted.replaceAll(r'-', '');
    for (int i = 0; i < 5; i++) {
      final x = stringToHex(extracted[extracted.length - 1 - i]);
      licenseKey *= x[0];
    }
  }

  //ask user to input license key
  final feedback = await showDialog<String>(
    context: context,
    builder: (BuildContext context) {
      final controller = TextEditingController();
      return AlertDialog(
        title: Text('Enter License Key'),
        content: TextField(
          controller: controller,
          decoration: InputDecoration(hintText: "License Key"),
        ),
        actions: <Widget>[
          TextButton(
            child: Text('Cancel'),
            onPressed: () {
              Navigator.of(context).pop(); // Close the dialog
            },
          ),
          TextButton(
            child: Text('OK'),
            onPressed: () {
              Navigator.of(context).pop(controller.text); // Return the input
            },
          ),
        ],
      );
    },
  );

  if (feedback != null) {
    if (int.tryParse(feedback) != null && int.parse(feedback) == licenseKey) {
      return true;
    } else {
      return false;
    }
  } else {
    return false;
  }
}

List<int> stringToHex(String input) {
  List<int> bytes = utf8.encode(input);
  return bytes;
}
