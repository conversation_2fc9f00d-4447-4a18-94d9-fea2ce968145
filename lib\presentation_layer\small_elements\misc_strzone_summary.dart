import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:structify/presentation_layer/small_elements/popup/custom_popup.dart';

//domain layer
import '../../domain_layer/preferences.dart';

// presentation layer
import '../screen/homescreen.dart';
import 'sketch/draw_strzone_info.dart';
// import '../small_elements/custom_stateful_double_input.dart';
// import '../small_elements/custom_stateful_text_input.dart';
// import '../small_elements/global_data_ui.dart';

//misc

class StrZoneCalsSummary extends ConsumerStatefulWidget {
  const StrZoneCalsSummary({
    this.isExpanded,
    this.maxHeight,
    this.minHeight,
    super.key,
  });

  final bool? isExpanded;
  final double? maxHeight;
  final double? minHeight;

  @override
  ConsumerState<ConsumerStatefulWidget> createState() =>
      _StrZoneCalsSummaryState();
}

class _StrZoneCalsSummaryState extends ConsumerState<StrZoneCalsSummary>
    with WidgetsBindingObserver {
  late bool _isExpanded;
  late double _maxHeight;
  late double _minHeight;

  late final ScrollController _scrollController;
  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _scrollController.dispose();
    super.dispose();
  }

  @override
  void initState() {
    WidgetsBinding.instance.addObserver(this);
    _isExpanded = widget.isExpanded ?? false;
    _maxHeight = widget.maxHeight ?? 300;
    _minHeight = widget.minHeight ?? 50;
    _scrollController = ScrollController();
    super.initState();
  }

  @override
  void didChangeDependencies() {
    _updateHeights();
    super.didChangeDependencies();
    // Update heights when dependencies change, including after initState
  }

  @override
  void didChangeMetrics() {
    // This method is called when the metrics change (like resizing the window)
    setState(() {
      _updateHeights();
    });
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    late final double totalHeight;
    NumberFormat f3 = NumberFormat('0.000');
    final TextStyle titleTextStyle = textTheme.titleSmall!.copyWith(
      color: colorScheme.onSurface,
    );
    final TextStyle bodyTextStyle = textTheme.bodyMedium!.copyWith(
      color: colorScheme.onSurface,
    );
    final TextStyle highlightText = textTheme.labelLarge!.copyWith(
      color: colorScheme.primary,
    );

    final strZoneTables = ref.watch(strZoneTablesControllerProvider);
    final globalData = ref.watch(globalDataControllerProvider);

    late final List<String> unit;
    return globalData.when(
      data: (data) {
        if (data.unit == 'metrics') {
          unit = PreferredUnit.metrics;
        } else {
          unit = PreferredUnit.imperial;
        }
        return strZoneTables.when(
          data: (tables) {
            totalHeight =
                tables.fold(
                  0.0,
                  (previousValue, element) =>
                      previousValue + element.height * element.nosOfFloor,
                ) /
                1000;
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding: const EdgeInsets.fromLTRB(10, 0, 10, 0),
                  child: Divider(),
                ),
                Padding(
                  padding: const EdgeInsets.fromLTRB(10, 0, 0, 0),
                  child: Align(
                    alignment: Alignment.centerLeft,
                    child: Wrap(
                      crossAxisAlignment: WrapCrossAlignment.center,
                      children: [
                        IconButton(
                          icon: Icon(
                            _isExpanded ? Icons.expand_less : Icons.expand_more,
                          ),
                          color: colorScheme.onSurface,
                          onPressed: () {
                            setState(() {
                              _isExpanded = !_isExpanded;
                            });
                          },
                        ),
                        Text(
                          'Structural Zone Summary',
                          style: textTheme.titleLarge!.copyWith(
                            color: colorScheme.onSurface,
                          ),
                        ),
                        CustomPopup(
                          popupWidth: 550,
                          popupHeight: 550,
                          widgetList: [
                            Padding(
                              padding: const EdgeInsets.all(8.0),
                              child: Text(
                                '1. Remember not include lowest floor for total height.',
                                style: textTheme.titleMedium!.copyWith(
                                  color: colorScheme.onSurface,
                                ),
                              ),
                            ),

                            DrawStrZoneInfo(
                              sketchWidth: 500,
                              sketchHeight: 500,
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.fromLTRB(10, 0, 10, 0),
                  child: Divider(),
                ),

                ClipRect(
                  child: ConstrainedBox(
                    constraints: BoxConstraints(
                      maxHeight: _isExpanded ? _maxHeight : 0,
                    ),
                    child: Scrollbar(
                      controller: _scrollController,
                      thumbVisibility: true,
                      trackVisibility: false,
                      child: SingleChildScrollView(
                        scrollDirection: Axis.vertical,
                        controller: _scrollController,
                        child: Padding(
                          padding: const EdgeInsets.fromLTRB(10, 0, 0, 0),
                          child: DefaultTextStyle(
                            style: textTheme.bodyMedium!.copyWith(
                              color: colorScheme.onSurface,
                            ),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Padding(
                                  padding: const EdgeInsets.all(8.0),
                                  child: Container(
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(5.0),
                                      color: colorScheme.tertiaryContainer
                                          .withAlpha(150),
                                      border: Border.all(
                                        color: colorScheme.tertiaryContainer
                                            .withAlpha(200),
                                        width: 1.0,
                                      ),
                                    ),
                                    child: Padding(
                                      padding: const EdgeInsets.all(5.0),
                                      child: Text(
                                        'Total Height = ${f3.format(totalHeight)} [${unit[3]}] (exclude roof parapet)',
                                        style: titleTextStyle.copyWith(
                                          color:
                                              colorScheme.onTertiaryContainer,
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                                _itemBlockWithTable(
                                  backgroundColor: Colors.transparent,
                                  borderColor: Colors.transparent,
                                  '',
                                  [
                                    'ID',
                                    'Floor',
                                    'Nos of Floors',
                                    'Str Zone [${unit[4]}]',
                                    'Height [${unit[4]}]',
                                    'Finish [${unit[4]}]',
                                    'Service [${unit[4]}]',
                                    'Clear [${unit[4]}]',
                                  ],
                                  [
                                    ...List.generate(tables.length, (index) {
                                      final double strZone =
                                          tables[index].height -
                                          tables[index].finish -
                                          tables[index].service -
                                          tables[index].clear;
                                      return [
                                        '${index + 1}',
                                        '${tables[index].floor}',
                                        '${tables[index].nosOfFloor}',
                                        '${strZone}',
                                        '${tables[index].height}',
                                        '${tables[index].finish}',
                                        '${tables[index].service}',
                                        '${tables[index].clear}',
                                      ];
                                    }),
                                  ],
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            );
          },
          error: (error, stackTrace) => Text(error.toString()),
          loading: () => CircularProgressIndicator(),
        );
      },
      error: (error, stackTrace) => Text(error.toString()),
      loading: () => CircularProgressIndicator(),
    );
  }

  Widget _itemBlockWithTable(
    String requirementTitle,
    List<String> requirementSutitle,
    List<List<String>> requirementDetails, {
    Color? backgroundColor,
    Color? borderColor,
  }) {
    //*initialize
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;
    backgroundColor ??= colorScheme.primaryContainer.withAlpha(50);
    borderColor ??= colorScheme.primary.withAlpha(50);

    return Padding(
      padding: const EdgeInsets.fromLTRB(0, 8, 0, 8),
      child: Container(
        decoration: BoxDecoration(
          color: backgroundColor,
          borderRadius: BorderRadius.circular(8.0),
          border: Border.all(width: 1.5, color: borderColor),
        ),
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: DefaultTextStyle(
            style: textTheme.bodyMedium!.copyWith(
              color: colorScheme.onPrimaryContainer,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                requirementTitle.isEmpty
                    ? SizedBox.shrink()
                    : Text(
                      requirementTitle,
                      style: textTheme.labelLarge!.copyWith(
                        color: colorScheme.onPrimaryContainer,
                      ),
                    ),

                SizedBox(height: 5.0),

                Row(
                  children: [
                    Expanded(
                      flex: 2,
                      child: Table(
                        defaultVerticalAlignment:
                            TableCellVerticalAlignment.middle,
                        border: TableBorder.all(
                          color: colorScheme.onPrimaryContainer.withAlpha(50),
                          width: 0.75,
                        ),
                        // columnWidths: const {
                        //   0: FlexColumnWidth(2),
                        //   1: FlexColumnWidth(3),
                        // },
                        children: [
                          TableRow(
                            decoration: BoxDecoration(
                              color: colorScheme.primaryContainer.withAlpha(
                                200,
                              ),
                            ),
                            children: [
                              ...List.generate(requirementSutitle.length, (
                                index,
                              ) {
                                return Align(
                                  child: Padding(
                                    padding: const EdgeInsets.all(4.0),
                                    child: Text(
                                      requirementSutitle[index],
                                      style: textTheme.labelLarge!.copyWith(
                                        color: colorScheme.onPrimaryContainer,
                                      ),
                                    ),
                                  ),
                                );
                              }),
                            ],
                          ),
                          ...List.generate(requirementDetails.length, (index) {
                            return TableRow(
                              decoration: BoxDecoration(
                                color: colorScheme.primaryContainer.withAlpha(
                                  50,
                                ),
                              ),
                              children: [
                                ...List.generate(
                                  requirementDetails[index].length,
                                  (index2) {
                                    return Align(
                                      child: Padding(
                                        padding: const EdgeInsets.all(4.0),
                                        child: Text(
                                          requirementDetails[index][index2],
                                          style: textTheme.bodyMedium!.copyWith(
                                            color:
                                                colorScheme.onPrimaryContainer,
                                          ),
                                        ),
                                      ),
                                    );
                                  },
                                ),
                              ],
                            );
                          }),
                        ],
                      ),
                    ),
                    SizedBox(width: 10.0),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // Method to update heights based on the current media query
  void _updateHeights() {
    final double screenHeight = MediaQuery.of(context).size.height;
    _maxHeight =
        (widget.maxHeight == null)
            ? screenHeight * 0.28
            : (widget.maxHeight! > screenHeight * 0.28)
            ? screenHeight * 0.28
            : widget.maxHeight!;

    _minHeight =
        (widget.minHeight == null)
            ? screenHeight * 0.1
            : (widget.minHeight! > screenHeight * 0.1)
            ? screenHeight * 0.1
            : widget.minHeight!;

    // Adjust _maxHeight if needed

    if (_maxHeight < _minHeight) {
      _maxHeight = _minHeight;
    }
  }
}
