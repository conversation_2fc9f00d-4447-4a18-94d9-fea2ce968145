// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'steel_column_scheme_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_SteelColumnSchemeData _$SteelColumnSchemeDataFromJson(
  Map<String, dynamic> json,
) => _SteelColumnSchemeData(
  steelColumnSchemeDataId: json['steelColumnSchemeDataId'] as String? ?? '',
  sdl: (json['sdl'] as num?)?.toDouble() ?? 0.0,
  ll: (json['ll'] as num?)?.toDouble() ?? 0.0,
  slsLoad: (json['slsLoad'] as num?)?.toDouble() ?? 0.0,
  ulsLoad: (json['ulsLoad'] as num?)?.toDouble() ?? 0.0,
  fsy: (json['fsy'] as num?)?.toDouble() ?? 355.0,
  unbracedLength: (json['unbracedLength'] as num?)?.toDouble() ?? 5.0,
  axialCapacity: (json['axialCapacity'] as num?)?.toDouble() ?? 0.0,
  steelSection: json['steelSection'] as String? ?? '',
  calsLog: json['calsLog'] as String? ?? '',
);

Map<String, dynamic> _$SteelColumnSchemeDataToJson(
  _SteelColumnSchemeData instance,
) => <String, dynamic>{
  'steelColumnSchemeDataId': instance.steelColumnSchemeDataId,
  'sdl': instance.sdl,
  'll': instance.ll,
  'slsLoad': instance.slsLoad,
  'ulsLoad': instance.ulsLoad,
  'fsy': instance.fsy,
  'unbracedLength': instance.unbracedLength,
  'axialCapacity': instance.axialCapacity,
  'steelSection': instance.steelSection,
  'calsLog': instance.calsLog,
};
