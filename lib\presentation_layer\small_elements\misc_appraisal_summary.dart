// import 'dart:nativewrappers/_internal/vm/lib/math_patch.dart';

import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:structify/domain_layer/pile_frictional_bored_input.dart';

//below for printing
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:printing/printing.dart';
// import 'dart:typed_data';
// import 'dart:ui';

//domain layer
import '../../domain_layer/global_data.dart';
import '../../domain_layer/pile_frictional_bored_data.dart';

// presentation layer
import '../../domain_layer/preferences.dart';
import '../screen/homescreen.dart';
// import '../small_elements/custom_stateful_double_input.dart';
// import '../small_elements/custom_stateful_text_input.dart';
// import '../small_elements/global_data_ui.dart';

//misc

import 'button/selection_button.dart';

class AppraisalSummary extends ConsumerStatefulWidget {
  const AppraisalSummary({
    this.isExpanded,
    this.maxHeight,
    this.minHeight,
    super.key,
  });

  final bool? isExpanded;
  final double? maxHeight;
  final double? minHeight;

  @override
  ConsumerState<ConsumerStatefulWidget> createState() =>
      _AppraisalSummaryState();
}

class _AppraisalSummaryState extends ConsumerState<AppraisalSummary>
    with WidgetsBindingObserver {
  late bool _isExpanded;
  late double _maxHeight;
  late double _minHeight;
  late ScrollController _scrollController;

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void initState() {
    WidgetsBinding.instance.addObserver(this);
    _isExpanded = widget.isExpanded ?? false;
    _maxHeight = widget.maxHeight ?? 300;
    _minHeight = widget.minHeight ?? 50;
    _scrollController = ScrollController();
    // _updateHeights();

    super.initState();
  }

  @override
  void didChangeDependencies() {
    _updateHeights();
    super.didChangeDependencies();
    // Update heights when dependencies change, including after initState
  }

  @override
  void didChangeMetrics() {
    // This method is called when the metrics change (like resizing the window)
    setState(() {
      _updateHeights();
    });
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;
    final globalData = ref.watch(globalDataControllerProvider);
    final TextStyle titleTextStyle = textTheme.titleMedium!.copyWith(
      color: colorScheme.onSurface,
    );
    final TextStyle bodyTextStyle = textTheme.bodyMedium!.copyWith(
      color: colorScheme.onSurface,
    );
    final TextStyle highlightText = textTheme.labelLarge!.copyWith(
      color: colorScheme.primary,
    );
    late final List<String> unit;
    return globalData.when(
      data: (data) {
        if (data.unit == 'metrics') {
          unit = PreferredUnit.metrics;
        } else {
          unit = PreferredUnit.imperial;
        }

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.fromLTRB(10, 0, 10, 0),
              child: Divider(),
            ),
            Padding(
              padding: const EdgeInsets.fromLTRB(10, 0, 0, 0),
              child: Align(
                alignment: Alignment.centerLeft,
                child: Wrap(
                  crossAxisAlignment: WrapCrossAlignment.center,
                  children: [
                    IconButton(
                      icon: Icon(
                        _isExpanded ? Icons.expand_less : Icons.expand_more,
                      ),
                      color: colorScheme.onSurface,
                      onPressed: () {
                        setState(() {
                          _isExpanded = !_isExpanded;
                        });
                      },
                    ),
                    Text(
                      'Appraisal',
                      style: textTheme.titleLarge!.copyWith(
                        color: colorScheme.onSurface,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            Padding(
              padding: const EdgeInsets.fromLTRB(10, 0, 10, 0),
              child: Divider(),
            ),

            ClipRect(
              child: ConstrainedBox(
                constraints: BoxConstraints(
                  maxHeight: _isExpanded ? _maxHeight : 0,
                ),
                child: Scrollbar(
                  controller: _scrollController,
                  thumbVisibility: true,
                  trackVisibility: false,
                  child: SingleChildScrollView(
                    scrollDirection: Axis.vertical,
                    controller: _scrollController,
                    child: Padding(
                      padding: const EdgeInsets.fromLTRB(10, 0, 0, 0),
                      child: DefaultTextStyle(
                        style: textTheme.bodyMedium!.copyWith(
                          color: colorScheme.onSurface,
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            _itemBlock(
                              'Story',
                              ['Case A: < 5 Stories', 'Case B: >= 5 Stories'],
                              [
                                'Case A: Consider progressive Collapse',
                                'Case B: Progressive collpase is unlikely',
                              ],
                            ),
                            _itemBlock(
                              'Building Nature',
                              [
                                'Case A: Slender (H/min(W,L) > 10)',
                                'Case B: Non-slender (H/min(W,L) <= 10)',
                              ],
                              [
                                'Case A: check building sway and deflection in detailed design',
                                'Case B: provide bracing or shear wall to control lateral movement',
                              ],
                            ),
                            _itemBlock(
                              'Perimeter Wall',
                              [
                                'Case A: Fully glazed on ... face',
                                'Case B: Extensive glazed on ... face ',
                                'Case C: External cavity wall on ... face',
                                'Case D: External brick wall / block wall on ... face',
                              ],
                              [
                                'Case A: curtain wall with structral set back and no peripheral bracing/wall',
                                'Case B: window wall without structural set back but no bracing/peripheral wall',
                                'Case C: Edge beam support with column setback',
                                'Case D: Edge beam support without column setback',
                              ],
                            ),
                            _itemBlockWithTable(
                              'Loading\n(LL [${unit[1]}] refers to question)',
                              [
                                'Floor',
                                'Finish [${unit[4]}]',
                                'Service [${unit[1]}]',
                                'SDL [${unit[1]}]',
                                'LL [${unit[1]}]',
                              ],
                              [
                                ['B3/F', '50', '1.0', '2.2', '5.0'],
                                ['...', '...', '...', '...', '...'],
                                ['...', '...', '...', '...', '...'],
                              ],
                              ['Design to suit\n\n'],
                            ),
                            _itemBlockWithTable(
                              'Structural Zone',
                              [
                                'Floor',
                                'Height [${unit[4]}]',
                                'Service [${unit[4]}]',
                                'Finish [${unit[4]}]',
                                'Clear Height [${unit[4]}]',
                                'Str Zone [${unit[4]}]',
                              ],
                              [
                                ['B3/F', '3000', '300', '50', '2000', '650'],
                                ['...', '...', '...', '...', '...', '...'],
                                ['...', '...', '...', '...', '...', '...'],
                              ],
                              ['Design to suit\n\n'],
                            ),
                            _itemBlock(
                              'Wind Load (CP3:Chapter V-2)',
                              [
                                'w=0.613(S\u2081S\u2082S\u2083V)^2',
                                'S\u2081 = 1.0 (cl. 5.4, no local topographic effect / 1.36 with local topographic effect)',
                                'S\u2082 = xx (cl. 5.5 and table 3)',
                                'S\u2083 = 1.0 (cl. 5.6)                   ',
                                'V = xx m/s (from question)',
                                '>>w = ... / 1000 = xx [${unit[1]}]',
                              ],
                              ['Design to Suit\n\n\n\n\n'],
                            ),
                            _itemBlock(
                              'Column Spacing',
                              [
                                'Column Spacing at xx floor = 8.0 m',
                                'External Column Spacing = 10.0 m',
                                'No column zone as shown on floor xxx',
                              ],
                              ['Design Column grid to suit\n\n'],
                            ),
                            _itemBlock(
                              'Existing Entity',
                              ['Existing Footing/Piles/Utilities'],
                              [
                                'New foudnation with setback to avoid loading on existing footing/piles/utilities.',
                              ],
                            ),
                            _itemBlock(
                              'Staircase Core Wall',
                              [
                                'Case A: Exist',
                                'Case B: Not Exist',
                                'Case C: Assymetic on Plan',
                              ],
                              [
                                'Case A: Used for lateral stability.',
                                'Case B: Lateral stability replies on frames and possibly braces.',
                                'Case C: Lateral stability replies on frames and possibly braces, and consider building torsion.',
                              ],
                            ),
                            _itemBlock(
                              'Building max Length',
                              [
                                'Case A: max lenght < 60m',
                                'Case B: max length > 60m',
                              ],
                              [
                                'Case A: No movement joint required.',
                                'Case B: provide movement joint for loading from shrinkage and temperature.',
                              ],
                            ),
                            _itemBlock(
                              'Basement',
                              [
                                'Case A: Exists and surmerged',
                                'Case B: Exists and not surmerged',
                              ],
                              [
                                'Case A: Adopt cavity wall and water-tight concrete for water-proofing. Dewatering during excavation stage.',
                                'Case B: Adopt cavity wall and water-tight concrete for water-proofing.',
                              ],
                            ),
                            _itemBlock(
                              'Soil Type',
                              [
                                'Case A: high rockhead',
                                'Case B: Low rockhead',
                                'Case C: No rockhead',
                              ],
                              [
                                'Case A: Possible to use shallow foundation (pad footing, raft foundation)',
                                'Case B: Likely to use pile foundation (frictional bored piles, rock socketed piles)',
                                'Case C: Likely to use piles replying on shaft friction (frictional bored piles, driven steel H-piles)',
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ],
        );
      },
      error: (error, stackTrace) => Text(error.toString()),
      loading: () => CircularProgressIndicator(),
    );
  }

  Widget _itemBlock(
    String requirementTitle,
    List<String> requirement,
    List<String> solution, {
    Color? backgroundColor,
    Color? borderColor,
    Color? textColor,
  }) {
    //*initialize
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;
    backgroundColor ??= colorScheme.primaryContainer.withAlpha(50);
    borderColor ??= colorScheme.primary.withAlpha(50);
    textColor ??= colorScheme.onPrimaryContainer;
    return Padding(
      padding: const EdgeInsets.fromLTRB(0, 8, 0, 8),
      child: Container(
        decoration: BoxDecoration(
          color: backgroundColor,
          borderRadius: BorderRadius.circular(8.0),
          border: Border.all(width: 1.5, color: borderColor),
        ),
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: DefaultTextStyle(
            style: textTheme.bodyMedium!.copyWith(color: textColor),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(width: 10.0),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '$requirementTitle',
                        style: textTheme.labelLarge!.copyWith(color: textColor),
                      ),
                      Wrap(
                        direction: Axis.horizontal,
                        crossAxisAlignment: WrapCrossAlignment.start,
                        alignment: WrapAlignment.start,
                        runAlignment: WrapAlignment.start,
                        verticalDirection: VerticalDirection.down,
                        children: [
                          ...List.generate(requirement.length, (index) {
                            return Text.rich(
                              TextSpan(
                                children: [
                                  TextSpan(
                                    text:
                                        requirement.length == 1
                                            ? ''
                                            : '${index + 1}. ',
                                  ),
                                  TextSpan(text: '${requirement[index]}'),
                                ],
                              ),
                            );
                          }),
                        ],
                      ),
                    ],
                  ),
                ),

                SizedBox(width: 10.0),

                // _buildVerticalDividor(
                //   requirement,
                //   solution,
                //   dividorColor: colorScheme.onPrimaryContainer.withAlpha(75),
                // ),

                // SizedBox(width: 10.0),
                Expanded(
                  child: Container(
                    padding: EdgeInsets.fromLTRB(10, 0, 0, 0),
                    decoration: BoxDecoration(
                      border: BorderDirectional(
                        start: BorderSide(
                          color: colorScheme.onPrimaryContainer.withAlpha(75),
                          width: 1.5,
                        ),
                      ),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(''),
                        Wrap(
                          direction: Axis.horizontal,
                          crossAxisAlignment: WrapCrossAlignment.start,
                          alignment: WrapAlignment.start,
                          runAlignment: WrapAlignment.start,
                          verticalDirection: VerticalDirection.down,
                          children: [
                            ...List.generate(solution.length, (index) {
                              return Text.rich(
                                TextSpan(
                                  children: [
                                    TextSpan(
                                      text:
                                          solution.length == 1
                                              ? ''
                                              : '${index + 1}. ',
                                    ),
                                    TextSpan(text: solution[index]),
                                  ],
                                ),
                              );
                            }),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _itemBlockWithTable(
    String requirementTitle,
    List<String> requirementSutitle,
    List<List<String>> requirementDetails,
    List<String> solution, {
    Color? backgroundColor,
    Color? borderColor,
  }) {
    //*initialize
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;
    backgroundColor ??= colorScheme.primaryContainer.withAlpha(50);
    borderColor ??= colorScheme.primary.withAlpha(50);

    return Padding(
      padding: const EdgeInsets.fromLTRB(0, 8, 0, 8),
      child: Container(
        decoration: BoxDecoration(
          color: backgroundColor,
          borderRadius: BorderRadius.circular(8.0),
          border: Border.all(width: 1.5, color: borderColor),
        ),
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: DefaultTextStyle(
            style: textTheme.bodyMedium!.copyWith(
              color: colorScheme.onPrimaryContainer,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  requirementTitle,
                  style: textTheme.labelLarge!.copyWith(
                    color: colorScheme.onPrimaryContainer,
                  ),
                ),
                SizedBox(height: 5.0),
                Row(
                  children: [
                    Expanded(
                      flex: 2,
                      child: Table(
                        defaultVerticalAlignment:
                            TableCellVerticalAlignment.middle,
                        border: TableBorder.all(
                          color: colorScheme.onPrimaryContainer.withAlpha(50),
                          width: 0.75,
                        ),
                        // columnWidths: const {
                        //   0: FlexColumnWidth(2),
                        //   1: FlexColumnWidth(3),
                        // },
                        children: [
                          TableRow(
                            children: [
                              ...List.generate(requirementSutitle.length, (
                                index,
                              ) {
                                return Padding(
                                  padding: const EdgeInsets.all(4.0),
                                  child: Text(
                                    requirementSutitle[index],
                                    style: textTheme.labelLarge!.copyWith(
                                      color: colorScheme.onPrimaryContainer,
                                    ),
                                  ),
                                );
                              }),
                            ],
                          ),
                          ...List.generate(requirementDetails.length, (index) {
                            return TableRow(
                              children: [
                                ...List.generate(
                                  requirementDetails[index].length,
                                  (index2) {
                                    return Padding(
                                      padding: const EdgeInsets.all(4.0),
                                      child: Text(
                                        requirementDetails[index][index2],
                                        style: textTheme.bodyMedium!.copyWith(
                                          color: colorScheme.onPrimaryContainer,
                                        ),
                                      ),
                                    );
                                  },
                                ),
                              ],
                            );
                          }),
                        ],
                      ),
                    ),
                    SizedBox(width: 10.0),
                    Expanded(
                      child: Container(
                        padding: EdgeInsets.fromLTRB(10, 0, 0, 0),
                        decoration: BoxDecoration(
                          border: BorderDirectional(
                            start: BorderSide(
                              color: colorScheme.onPrimaryContainer.withAlpha(
                                75,
                              ),
                              width: 1.5,
                            ),
                          ),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(''),
                            Wrap(
                              direction: Axis.horizontal,
                              crossAxisAlignment: WrapCrossAlignment.start,
                              alignment: WrapAlignment.start,
                              runAlignment: WrapAlignment.start,
                              verticalDirection: VerticalDirection.down,

                              // mainAxisSize: MainAxisSize.min,
                              // mainAxisAlignment: MainAxisAlignment.start,
                              // crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                ...List.generate(solution.length, (index) {
                                  return Text.rich(
                                    TextSpan(
                                      children: [
                                        TextSpan(
                                          text:
                                              solution.length == 1
                                                  ? ''
                                                  : '${index + 1}. ',
                                        ),
                                        TextSpan(text: solution[index]),
                                      ],
                                    ),
                                  );
                                }),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildVerticalDividor(
    List<String> requirement,
    List<String> solution, {
    Color dividorColor = Colors.black,
    double dividorWidth = 1.5,
  }) {
    return Container(
      width: 10,
      decoration: BoxDecoration(
        border: BorderDirectional(
          start: BorderSide(color: dividorColor, width: dividorWidth),
        ),
      ),
      // child: LayoutBuilder(
      //   builder: (context, constraints) {
      //     return SizedBox(
      //       height: constraints.maxHeight,
      //     );
      //   },
      child: Column(
        children: [
          ...List.generate(max(requirement.length + 1, solution.length), (
            index,
          ) {
            return Text('');
          }),
        ],
      ),
    );
  }

  // Method to update heights based on the current media query
  void _updateHeights() {
    final double screenHeight = MediaQuery.of(context).size.height;
    _maxHeight =
        (widget.maxHeight == null)
            ? screenHeight * 0.5
            : (widget.maxHeight! > screenHeight * 0.5)
            ? screenHeight * 0.5
            : widget.maxHeight!;

    _minHeight =
        (widget.minHeight == null)
            ? screenHeight * 0.1
            : (widget.minHeight! > screenHeight * 0.1)
            ? screenHeight * 0.1
            : widget.minHeight!;

    // Adjust _maxHeight if needed

    if (_maxHeight < _minHeight) {
      _maxHeight = _minHeight;
    }
  }
}
