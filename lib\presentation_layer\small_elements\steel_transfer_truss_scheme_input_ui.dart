import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:nanoid2/nanoid2.dart';
import 'package:structify/presentation_layer/small_elements/sketch/draw_steel_transfer_truss_input_info.dart';

//domain layer
import '../../domain_layer/preferences.dart';

// presentation layer
import '../screen/homescreen.dart';
import 'input/custom_stateful_double_input.dart';
import 'input/custom_stateful_text_input.dart';
import 'popup/custom_popup.dart';
import 'input/custom_stateful_dropList.dart';
import 'sketch/draw_steel_truss_global_info.dart';
import 'button/function_button.dart';

class SteelTransferTrussSchemeInputUi extends ConsumerStatefulWidget {
  const SteelTransferTrussSchemeInputUi({
    this.isExpanded,
    this.maxHeight,
    this.minHeight,
    super.key,
  });

  final bool? isExpanded;
  final double? maxHeight;
  final double? minHeight;

  @override
  ConsumerState<ConsumerStatefulWidget> createState() =>
      _SteelTransferTrussSchemeInputUiState();
}

class _SteelTransferTrussSchemeInputUiState
    extends ConsumerState<SteelTransferTrussSchemeInputUi>
    with WidgetsBindingObserver {
  late bool _isExpanded;
  late double _maxHeight;
  late double _minHeight;
  late CustomStatefulTextInput _usageInput;
  late String _id;
  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void initState() {
    WidgetsBinding.instance.addObserver(this);
    _isExpanded = widget.isExpanded ?? false;
    _maxHeight = widget.maxHeight ?? 300;
    _minHeight = widget.minHeight ?? 50;
    _usageInput = CustomStatefulTextInput();
    _id = _generateTaskID();

    // _updateHeights();

    super.initState();
  }

  @override
  void didChangeDependencies() {
    _updateHeights();
    super.didChangeDependencies();
    // Update heights when dependencies change, including after initState
  }

  @override
  void didChangeMetrics() {
    // This method is called when the metrics change (like resizing the window)
    setState(() {
      _updateHeights();
    });
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;
    final globalData = ref.watch(globalDataControllerProvider);
    final loadingTables = ref.watch(loadingTablesControllerProvider);
    final steelTransferTrussSchemeInput = ref.watch(
      steelTransferTrussSchemeInputControllerProvider,
    );
    final steelTransferTrussSchemeInputGlobal = ref.watch(
      steelTransferTrussSchemeInputGlobalControllerProvider,
    );
    final scrollController = ScrollController();

    return globalData.when(
      data: (data) {
        late final List<String> units;
        switch (data.unit) {
          case 'metrics':
            units = PreferredUnit.metrics;
            break;
          case 'imperial':
            units = PreferredUnit.imperial;
            break;
          default:
            units = PreferredUnit.metrics;
            break;
        }

        return loadingTables.when(
          data: (tables) {
            return steelTransferTrussSchemeInputGlobal.when(
              data: (inputGlobal) {
                return steelTransferTrussSchemeInput.when(
                  data: (input) {
                    return Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Padding(
                          padding: const EdgeInsets.fromLTRB(10, 0, 10, 0),
                          child: Divider(),
                        ),
                        Padding(
                          padding: const EdgeInsets.fromLTRB(10, 0, 0, 0),
                          child: Align(
                            alignment: Alignment.centerLeft,
                            child: Wrap(
                              crossAxisAlignment: WrapCrossAlignment.center,
                              children: [
                                Wrap(
                                  crossAxisAlignment: WrapCrossAlignment.center,
                                  children: [
                                    IconButton(
                                      icon: Icon(
                                        _isExpanded
                                            ? Icons.keyboard_arrow_up
                                            : Icons.keyboard_arrow_down,
                                        color: colorScheme.onSurface,
                                      ),
                                      onPressed: () {
                                        setState(() {
                                          _isExpanded = !_isExpanded;
                                        });
                                      },
                                    ),
                                    Text(
                                      'Steel Transfer Truss Scheme Inputs ',
                                      style: textTheme.titleLarge!.copyWith(
                                        color: colorScheme.onSurface,
                                      ),
                                    ),
                                  ],
                                ),
                                Container(
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(5.0),
                                    color: colorScheme.surfaceContainer
                                        .withAlpha(225),
                                    border: Border.all(
                                      color: Colors.black.withAlpha(125),
                                    ),
                                  ),
                                  child: Padding(
                                    padding: const EdgeInsets.all(2.0),
                                    child: Text(
                                      '(Total: ${input.length})',
                                      style: textTheme.labelMedium!.copyWith(
                                        color: colorScheme.onSurfaceVariant
                                            .withAlpha(225),
                                      ),
                                    ),
                                  ),
                                ),
                                SizedBox(width: 5.0),
                                CustomPopup(
                                  popupWidth: 550,
                                  popupHeight: 550,
                                  widgetList: [
                                    DrawSteelTrussGlobalInfo(
                                      sketchWidth: 500,
                                      sketchHeight: 500,
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                        ),
                        Padding(
                          padding: const EdgeInsets.fromLTRB(10, 0, 10, 0),
                          child: Divider(),
                        ),
                        Padding(
                          padding: const EdgeInsets.fromLTRB(
                            8.0,
                            4.0,
                            8.0,
                            4.0,
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  Flexible(
                                    child: CustomStatefulDoubleInput(
                                      title: 'Span [${units[3]}]',
                                      value: inputGlobal.span,
                                      onChanged: (value) async {
                                        ref
                                            .read(
                                              steelTransferTrussSchemeInputGlobalControllerProvider
                                                  .notifier,
                                            )
                                            .updateTable(span: value);
                                      },
                                    ),
                                  ),
                                  SizedBox(width: 5.0),
                                  Flexible(
                                    child: CustomStatefulDoubleInput(
                                      title: 'Str Zone [${units[4]}]',
                                      tooltipText:
                                          'Remember slab takes up portion of the str zone:\nStr Zone = Slab depth + Chord Sizes * 2 + Clear between chords',
                                      value: inputGlobal.strZone,
                                      onChanged: (value) async {
                                        ref
                                            .read(
                                              steelTransferTrussSchemeInputGlobalControllerProvider
                                                  .notifier,
                                            )
                                            .updateTable(strZone: value);
                                      },
                                    ),
                                  ),

                                  Flexible(child: SizedBox(width: 5.0)),
                                  Flexible(child: SizedBox(width: 5.0)),
                                ],
                              ),
                              SizedBox(height: 10),
                              Row(
                                mainAxisSize: MainAxisSize.min,
                                mainAxisAlignment: MainAxisAlignment.start,
                                children: [
                                  Flexible(
                                    child: CustomStatefulDoubleInput(
                                      title: 'Slab Thickness [${units[4]}]',
                                      key: ValueKey('${_id}_slabThickness'),
                                      value: inputGlobal.slabThickness,
                                      listener: (hasFocus, value) async {
                                        if (!hasFocus) {
                                          await ref
                                              .read(
                                                steelTransferTrussSchemeInputGlobalControllerProvider
                                                    .notifier,
                                              )
                                              .updateTable(
                                                id: '1',
                                                slabThickness: value,
                                              );
                                        }
                                      },
                                    ),
                                  ),
                                  SizedBox(width: 5.0),
                                  Flexible(
                                    child: CustomStatefulDoubleInput(
                                      title: 'Load Width [${units[3]}]',
                                      value: inputGlobal.loadWidth,
                                      onChanged: (value) async {
                                        ref
                                            .read(
                                              steelTransferTrussSchemeInputGlobalControllerProvider
                                                  .notifier,
                                            )
                                            .updateTable(loadWidth: value);
                                      },
                                    ),
                                  ),
                                  SizedBox(width: 10.0),
                                  Flexible(
                                    child: CustomStatefulDoubleInput(
                                      title: 'Max Chord Segment [${units[3]}]',
                                      tooltipText:
                                          'design unbraced length for designing the compression chord\n(and hence the tension chord as well)',
                                      value: inputGlobal.unbracedLength,
                                      onChanged: (value) async {
                                        ref
                                            .read(
                                              steelTransferTrussSchemeInputGlobalControllerProvider
                                                  .notifier,
                                            )
                                            .updateTable(unbracedLength: value);
                                      },
                                    ),
                                  ),
                                  Flexible(child: SizedBox(width: 10.0)),
                                ],
                              ),
                            ],
                          ),
                        ),
                        Padding(
                          padding: const EdgeInsets.fromLTRB(
                            8.0,
                            4.0,
                            8.0,
                            4.0,
                          ),
                          child: Row(
                            children: [
                              Flexible(
                                child: ClipRect(
                                  child: SizedBox(
                                    width: 150,
                                    child: CustomStatefulDropDown(
                                      items:
                                          tables.map((e) => e.usage).toList(),
                                      selectedValue:
                                          inputGlobal.usage == ''
                                              ? null
                                              : inputGlobal.usage,
                                      onTap: (selectedValue) async {
                                        await ref
                                            .read(
                                              steelTransferTrussSchemeInputGlobalControllerProvider
                                                  .notifier,
                                            )
                                            .updateTable(
                                              id: '1',
                                              usage: selectedValue,
                                            );
                                      },
                                    ),
                                  ),
                                ),
                              ),
                              SizedBox(width: 5.0),
                              FunctionButton(
                                labelIcon: Icon(Icons.add_box_outlined),
                                labelText: 'Add',
                                onTap: (isPressed) async {
                                  await ref
                                      .read(
                                        steelTransferTrussSchemeInputControllerProvider
                                            .notifier,
                                      )
                                      .addEmptytable();
                                },
                              ),
                              SizedBox(width: 5.0),
                              CustomPopup(
                                popupWidth: 550,
                                popupHeight: 300,
                                widgetList: [
                                  DrawSteelTransferTrussInputInfo(
                                    sketchWidth: 500,
                                    sketchHeight: 250,
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                        SizedBox(height: 5.0),
                        ClipRect(
                          child: ConstrainedBox(
                            constraints: BoxConstraints(
                              maxHeight: _isExpanded ? _maxHeight : 0,
                            ),
                            child: Scrollbar(
                              controller: scrollController,
                              thumbVisibility: true,
                              child: ListView.builder(
                                controller: scrollController,
                                itemCount: input.length,
                                itemBuilder: (context, index) {
                                  return Padding(
                                    padding: const EdgeInsets.fromLTRB(
                                      8.0,
                                      4.0,
                                      8.0,
                                      4.0,
                                    ),
                                    child: Row(
                                      mainAxisSize: MainAxisSize.min,
                                      mainAxisAlignment:
                                          MainAxisAlignment.start,
                                      children: [
                                        Container(
                                          decoration: BoxDecoration(
                                            shape: BoxShape.circle,
                                            color: colorScheme.surfaceContainer
                                                .withAlpha(200),
                                            // borderRadius: BorderRadius.circular(5),
                                            border: Border.all(
                                              color: Colors.black,
                                            ),
                                          ),
                                          child: Padding(
                                            padding: const EdgeInsets.all(4.0),
                                            child: Text(
                                              '${index + 1}',
                                              style: textTheme.labelLarge!
                                                  .copyWith(
                                                    color: colorScheme
                                                        .onSurfaceVariant
                                                        .withAlpha(250),
                                                  ),
                                            ),
                                          ),
                                        ),
                                        IconButton(
                                          icon: Icon(
                                            Icons.copy_all_outlined,
                                            color: colorScheme.onSurface,
                                          ),
                                          onPressed: () async {
                                            await ref
                                                .read(
                                                  steelTransferTrussSchemeInputControllerProvider
                                                      .notifier,
                                                )
                                                .copyAndInsertTable(index);
                                          },
                                        ),
                                        IconButton(
                                          icon: Icon(
                                            Icons.delete_outline,
                                            color: colorScheme.onSurface,
                                          ),
                                          onPressed: () async {
                                            await ref
                                                .read(
                                                  steelTransferTrussSchemeInputControllerProvider
                                                      .notifier,
                                                )
                                                .deleteTable(
                                                  input[index]
                                                      .steelTransferTrussSchemeInputId,
                                                );
                                          },
                                        ),
                                        SizedBox(width: 5.0),
                                        Flexible(
                                          child: CustomStatefulDoubleInput(
                                            title: 'Point Load [${units[0]}]',
                                            key: ValueKey(
                                              '${input[index].steelTransferTrussSchemeInputId}_pointLoad',
                                            ),
                                            value: input[index].pointLoad,
                                            listener: (hasFocus, value) async {
                                              if (!hasFocus) {
                                                await ref
                                                    .read(
                                                      steelTransferTrussSchemeInputControllerProvider
                                                          .notifier,
                                                    )
                                                    .updateTable(
                                                      steelTransferTrussSchemeInputId:
                                                          input[index]
                                                              .steelTransferTrussSchemeInputId,
                                                      pointLoad: value,
                                                    );
                                              }
                                            },
                                          ),
                                        ),
                                        SizedBox(width: 5.0),
                                        Flexible(
                                          child: CustomStatefulDoubleInput(
                                            title: 'Distance a [${units[3]}]',
                                            key: ValueKey(
                                              '${input[index].steelTransferTrussSchemeInputId}_distA',
                                            ),
                                            value: input[index].distA,
                                            maxValue: inputGlobal.span,
                                            listener: (hasFocus, value) async {
                                              if (!hasFocus) {
                                                await ref
                                                    .read(
                                                      steelTransferTrussSchemeInputControllerProvider
                                                          .notifier,
                                                    )
                                                    .updateTable(
                                                      steelTransferTrussSchemeInputId:
                                                          input[index]
                                                              .steelTransferTrussSchemeInputId,
                                                      distA: value,
                                                    );
                                              }
                                            },
                                          ),
                                        ),
                                        Flexible(child: SizedBox(width: 10)),
                                        Flexible(child: SizedBox(width: 10)),
                                      ],
                                    ),
                                  );
                                },
                              ),
                            ),
                          ),
                        ),
                      ],
                    );
                  },
                  error: (error, stackTrace) {
                    return Text('Error: $error');
                  },
                  loading: () {
                    return const CircularProgressIndicator();
                  },
                );
              },
              error: (error, stackTrace) => Text(error.toString()),
              loading: () => CircularProgressIndicator(),
            );
          },
          error: (error, stackTrace) => Text(error.toString()),
          loading: () => CircularProgressIndicator(),
        );
      },
      error: (error, stackTrace) => Text(error.toString()),
      loading: () => CircularProgressIndicator(),
    );
  }

  void _updateHeights() {
    final double screenHeight = MediaQuery.of(context).size.height;
    _maxHeight =
        (widget.maxHeight == null)
            ? screenHeight * 0.5
            : (widget.maxHeight! > screenHeight * 0.5)
            ? screenHeight * 0.5
            : widget.maxHeight!;

    _minHeight =
        (widget.minHeight == null)
            ? screenHeight * 0.05
            : (widget.minHeight! > screenHeight * 0.05)
            ? screenHeight * 0.05
            : widget.minHeight!;

    // Adjust _maxHeight if needed
    if (_maxHeight < _minHeight) {
      _maxHeight = _minHeight;
    }
  }
}

String _generateTaskID() {
  String newID = nanoid(alphabet: Alphabet.noDoppelgangerSafe);
  newID = nanoid(alphabet: Alphabet.noDoppelgangerSafe);
  return newID;
}
