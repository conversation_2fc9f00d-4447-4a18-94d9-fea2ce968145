import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';

//domain layer
import '../../domain_layer/data_struct/preferences.dart';

// presentation layer
import '../screen/homescreen.dart';
// import '../small_elements/custom_stateful_double_input.dart';
// import '../small_elements/custom_stateful_text_input.dart';
// import '../small_elements/global_data_ui.dart';

//misc

class WindLoadSummary extends ConsumerStatefulWidget {
  const WindLoadSummary({
    this.isExpanded,
    this.maxHeight,
    this.minHeight,
    super.key,
  });

  final bool? isExpanded;
  final double? maxHeight;
  final double? minHeight;

  @override
  ConsumerState<ConsumerStatefulWidget> createState() =>
      _WindLoadSummaryState();
}

class _WindLoadSummaryState extends ConsumerState<WindLoadSummary>
    with WidgetsBindingObserver {
  late bool _isExpanded;
  late double _maxHeight;
  late double _minHeight;
  late ScrollController _scrollController;

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void initState() {
    WidgetsBinding.instance.addObserver(this);
    _isExpanded = widget.isExpanded ?? false;
    _maxHeight = widget.maxHeight ?? 300;
    _minHeight = widget.minHeight ?? 50;
    _scrollController = ScrollController();
    // _updateHeights();

    super.initState();
  }

  @override
  void didChangeDependencies() {
    _updateHeights();
    super.didChangeDependencies();
    // Update heights when dependencies change, including after initState
  }

  @override
  void didChangeMetrics() {
    // This method is called when the metrics change (like resizing the window)
    setState(() {
      _updateHeights();
    });
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    final windLoadInput = ref.watch(windLoadInputControllerProvider);
    final globalData = ref.watch(globalDataControllerProvider);
    final scrollController = ScrollController();

    final NumberFormat formatter1 = NumberFormat('0.0');
    late List<String> unit;

    final NumberFormat f0 = NumberFormat('0'),
        f1 = NumberFormat('0.0'),
        f3 = NumberFormat('0.000');
    double totalWindLoad = 0;

    return windLoadInput.when(
      data: (inputs) {
        //start reutnr real stuffs when loading tables available
        return globalData.when(
          //also, start reutnr real stuffs when global data tables available
          data: (data) {
            //* tricky here: the return from ref.watch is a Reference of object pointed by the state of the controller
            //* it means if we do sort() here, the state will literally change
            //* so we need to copy the list before sorting
            final sortedInput = [...inputs];
            sortedInput.sort((a, b) => a.z.compareTo(b.z));
            late int indexOfMaxTable;
            switch (data.unit) {
              case 'metrics':
                unit = PreferredUnit.metrics;
                break;
              case 'imperial':
                unit = PreferredUnit.imperial;
                break;
              default:
                unit = PreferredUnit.metrics;
            }
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding: const EdgeInsets.fromLTRB(10, 0, 10, 0),
                  child: Divider(),
                ),
                Padding(
                  padding: const EdgeInsets.fromLTRB(10, 0, 0, 0),
                  child: Align(
                    alignment: Alignment.centerLeft,
                    child: Row(
                      children: [
                        IconButton(
                          icon: Icon(
                            _isExpanded ? Icons.expand_less : Icons.expand_more,
                          ),
                          color: colorScheme.onSurface,
                          onPressed: () {
                            setState(() {
                              _isExpanded = !_isExpanded;
                            });
                          },
                        ),
                        Text(
                          'Wind Load Summary ',
                          style: textTheme.titleLarge!.copyWith(
                            color: colorScheme.onSurface,
                          ),
                        ),
                        Container(
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(5.0),
                            color: colorScheme.surfaceContainer.withAlpha(225),
                            border: Border.all(
                              color: Colors.black.withAlpha(125),
                            ),
                          ),
                          child: Padding(
                            padding: const EdgeInsets.all(2.0),
                            child: Text(
                              '(Total: ${sortedInput.length})',
                              style: textTheme.labelMedium!.copyWith(
                                color: colorScheme.onSurfaceVariant.withAlpha(
                                  225,
                                ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.fromLTRB(10, 0, 10, 0),
                  child: Divider(),
                ),
                Padding(
                  padding: const EdgeInsets.fromLTRB(10, 0, 10, 0),
                  child: ClipRect(
                    child: ConstrainedBox(
                      constraints: BoxConstraints(
                        maxHeight: _isExpanded ? _maxHeight : 0,
                      ),
                      child: Scrollbar(
                        controller: _scrollController,
                        thumbVisibility: true,
                        trackVisibility: false,
                        child: SingleChildScrollView(
                          controller: _scrollController,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Builder(
                                builder: (context) {
                                  if (inputs.isEmpty) {
                                    return CircularProgressIndicator();
                                  } else {
                                    return DefaultTextStyle(
                                      style: textTheme.labelLarge!.copyWith(
                                        color: colorScheme.onSurface,
                                      ),
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            'Topographic Factor, sT = ${f3.format(sortedInput.first.sT)} [-]',
                                          ),
                                          Text(
                                            'Directional Factor, s\u03B8 = ${f3.format(sortedInput.first.sTheta)} [-]',
                                          ),
                                          Text(
                                            'Fundamental Frequency, nX = ${f3.format(sortedInput.first.nX)} [Hz]',
                                          ),
                                          Text(
                                            'Damping Ratio, \u03be = ${f3.format(sortedInput.first.xI)} [-]',
                                          ),
                                          Text(
                                            'Size Factor at Top, Sqh = ${f3.format(sortedInput.first.sQh)} [-]',
                                          ),
                                          Text(
                                            'Force Coefficient, cF = ${f3.format(sortedInput.first.cF)} [-]',
                                          ),
                                        ],
                                      ),
                                    );
                                  }
                                },
                              ),
                              _itemBlockWithTable(
                                backgroundColor: Colors.transparent,
                                borderColor: Colors.transparent,
                                '',
                                [
                                  'z [${unit[3]}]',
                                  'qz,o[${unit[1]}]',
                                  'qz[${unit[1]}]',
                                  'qz,factored[${unit[1]}]',
                                  'sqz',
                                  'Load Width [${unit[3]}]',
                                  'Load Height [${unit[3]}]',
                                  'Total Load [${unit[0]}]',
                                ],
                                [
                                  ...List.generate(sortedInput.length+1, (index) {
                                    final double loadHeight;
                                    if (index == 0) {
                                      loadHeight = sortedInput[index].z;
                                    } else if ( sortedInput.length >= 1 && index == sortedInput.length) {
                                      return [
                                        '',
                                        '',
                                        '',
                                        '',
                                        '',
                                        '',
                                        'Total',
                                        '${f0.format(totalWindLoad)}',
                                      ];
                                    } else {
                                      loadHeight =
                                          sortedInput[index].z -
                                          sortedInput[index - 1].z;
                                    }
                                    final double totalLoad =
                                        sortedInput[index].qZFactored *
                                        sortedInput[index].cF *
                                        sortedInput[index].sQz *
                                        sortedInput[index].b *
                                        loadHeight;
                                    totalWindLoad += totalLoad;
                                    return [
                                      '${f1.format(sortedInput[index].z)}',
                                      '${f1.format(sortedInput[index].qZo)}',
                                      '${f3.format(sortedInput[index].qZ)}',
                                      '${f3.format(sortedInput[index].qZFactored)}',
                                      '${f3.format(sortedInput[index].sQz)}',
                                      '${f1.format(sortedInput[index].b)}',
                                      '${f1.format(loadHeight)}',
                                      '${f0.format(totalLoad)}',
                                    ];
                                  }),
                                ],
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            );
          },
          error: (error, stackTrace) => Text(error.toString()),
          loading: () => CircularProgressIndicator(),
        );
      },
      error: (error, stackTrace) => Text(error.toString()),
      loading: () => CircularProgressIndicator(),
    );
  }

  Widget _itemBlockWithTable(
    String requirementTitle,
    List<String> requirementSutitle,
    List<List<String>> requirementDetails, {
    Color? backgroundColor,
    Color? borderColor,
  }) {
    //*initialize
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;
    backgroundColor ??= colorScheme.primaryContainer.withAlpha(50);
    borderColor ??= colorScheme.primary.withAlpha(50);

    return Padding(
      padding: const EdgeInsets.fromLTRB(0, 8, 0, 8),
      child: Container(
        decoration: BoxDecoration(
          color: backgroundColor,
          borderRadius: BorderRadius.circular(8.0),
          border: Border.all(width: 1.5, color: borderColor),
        ),
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: DefaultTextStyle(
            style: textTheme.bodyMedium!.copyWith(
              color: colorScheme.onPrimaryContainer,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                requirementTitle.isEmpty
                    ? SizedBox.shrink()
                    : Text(
                      requirementTitle,
                      style: textTheme.labelLarge!.copyWith(
                        color: colorScheme.onPrimaryContainer,
                      ),
                    ),

                SizedBox(height: 5.0),

                Row(
                  children: [
                    Expanded(
                      flex: 2,
                      child: Table(
                        defaultVerticalAlignment:
                            TableCellVerticalAlignment.middle,
                        border: TableBorder.all(
                          color: colorScheme.onPrimaryContainer.withAlpha(50),
                          width: 0.75,
                        ),
                        // columnWidths: const {
                        //   0: FlexColumnWidth(2),
                        //   1: FlexColumnWidth(3),
                        // },
                        children: [
                          TableRow(
                            decoration: BoxDecoration(
                              color: colorScheme.primaryContainer.withAlpha(
                                200,
                              ),
                            ),
                            children: [
                              ...List.generate(requirementSutitle.length, (
                                index,
                              ) {
                                return Align(
                                  child: Padding(
                                    padding: const EdgeInsets.all(4.0),
                                    child: Text(
                                      requirementSutitle[index],
                                      style: textTheme.labelLarge!.copyWith(
                                        color: colorScheme.onPrimaryContainer,
                                      ),
                                    ),
                                  ),
                                );
                              }),
                            ],
                          ),
                          ...List.generate(requirementDetails.length, (index) {
                            return TableRow(
                              decoration: BoxDecoration(
                                color: colorScheme.primaryContainer.withAlpha(
                                  50,
                                ),
                              ),
                              children: [
                                ...List.generate(
                                  requirementDetails[index].length,
                                  (index2) {
                                    return Align(
                                      child: Padding(
                                        padding: const EdgeInsets.all(4.0),
                                        child: Text(
                                          requirementDetails[index][index2],
                                          style: textTheme.bodyMedium!.copyWith(
                                            color:
                                                colorScheme.onPrimaryContainer,
                                          ),
                                        ),
                                      ),
                                    );
                                  },
                                ),
                              ],
                            );
                          }),
                        ],
                      ),
                    ),
                    SizedBox(width: 10.0),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // Method to update heights based on the current media query
  void _updateHeights() {
    final double screenHeight = MediaQuery.of(context).size.height;
    _maxHeight =
        (widget.maxHeight == null)
            ? screenHeight * 0.6
            : (widget.maxHeight! > screenHeight * 0.6)
            ? screenHeight * 0.6
            : widget.maxHeight!;

    _minHeight =
        (widget.minHeight == null)
            ? screenHeight * 0.1
            : (widget.minHeight! > screenHeight * 0.1)
            ? screenHeight * 0.1
            : widget.minHeight!;

    // Adjust _maxHeight if needed

    if (_maxHeight < _minHeight) {
      _maxHeight = _minHeight;
    }
  }
}
