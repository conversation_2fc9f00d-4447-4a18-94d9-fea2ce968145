import 'package:flutter/material.dart';

class CustomTooltip extends StatelessWidget {
  const CustomTooltip({
    super.key,
    this.tooltipText = '',
    this.opacity = 255,
    this.icon = const Icon(Icons.info_outline),
    this.textstyle,
    this.backgroundColor,
  });
  final String tooltipText;
  final int? opacity;
  final Icon icon;
  final TextStyle? textstyle;
  final Color? backgroundColor;

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final colorScheme = Theme.of(context).colorScheme;
    return Tooltip(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10.0),
        color:
            backgroundColor ?? colorScheme.tertiaryContainer.withAlpha(opacity ?? 255),
      ),
      textStyle:
          textstyle ??
          textTheme.labelLarge!.copyWith(
            color: colorScheme.onTertiaryContainer.withAlpha(opacity ?? 255),
          ),
      message: tooltipText,
      child: icon,
    );
  }
}
