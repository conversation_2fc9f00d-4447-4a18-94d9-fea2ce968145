import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'dart:math';
import 'package:intl/intl.dart';
import 'package:structify/domain_layer/mixin/mixin_str_general_cals.dart';

//presentation layer
import '../../domain_layer/mixin/mixin_rc_str.dart';
import '../../domain_layer/footing_scheme_data.dart';
import '../../domain_layer/footing_scheme_input.dart';
import '../../domain_layer/mixin/mixin_tools_for_ui.dart';
import '../screen/homescreen.dart';

// domain layer
import '../../domain_layer/global_data.dart';
import '../../domain_layer/preferences.dart';

part 'footing_scheme_data_controller.g.dart';

@riverpod
class FootingSchemeDataController extends _$FootingSchemeDataController
    with StrGeneralCals, RCStrHK, MixinToolsForUI {
  @override
  FutureOr<List<FootingSchemeData>> build() async {
    // print('Build: Slab Scheme Data');
    final footingSchemeData =
        await ref
            .read(appDatabaseControllerProvider.notifier)
            .queryFootingSchemeData();
    final data1 = ref.watch(footingSchemeInputControllerProvider);
    return data1.when(
      data: (inputs) async {
        return await footingScheming();
      },
      error: (error, stackTrace) => <FootingSchemeData>[],
      loading: () => <FootingSchemeData>[],
    );
  }

  Future<void> addFootingSchemeData(
    List<FootingSchemeData> footingSchemeData,
  ) async {
    final x = await future;
    state = AsyncData([...x, ...footingSchemeData]);
  }

  Future<void> deleteTable(String footingSchemeDataId) async {
    final x = await future;
    x.removeWhere((item) => item.footingSchemeDataId == footingSchemeDataId);
    state = AsyncData(x);
  }

  Future<void> deleteAllTable() async {
    state = AsyncData([]);
  }

  Future<void> toggleSelectScheme(String footingSchemeId) async {
    final x = await future;
    final newState =
        x.map((item) {
          if (item.footingSchemeDataId == footingSchemeId) {
            return item.copyWith(isSelected: !item.isSelected);
          } else {
            return item.copyWith(isSelected: false);
          }
        }).toList();
    state = AsyncData(newState);
  }

  Future<List<FootingSchemeData>> footingScheming() async {
    // ********************************
    // initialization
    // ********************************
    final input = await ref.read(footingSchemeInputControllerProvider.future);
    final globalData = await ref.read(globalDataControllerProvider.future);
    // iteration status
    late bool shouldRedesign;
    bool skipIteration = false;
    bool redesignacT = true,
        redesignacC = true,
        reDesignLinks = true,
        reDesignSize = true,
        reDesignPunching = true,
        reDesignDelection = true;

    int linksOptimizationSteps = 0;
    int linksOptimizationStepsLimit = 10;

    int punchinglinksOptimizationSteps = 0;
    int punchinglinksOptimizationStepsLimit = 10;

    int compSteelOptimizationSteps = 0;
    int compSteelOptimizationStepsLimit = 10;

    double depthDelta = 50, sizeDelta = 100;
    // rebar diameters
    late List<int> diaT; // tension bar diameter
    late List<int> diaC; // tension bar diameter
    late List<int> diaL; // links diameter
    late List<int> diaP; // punching links diameter
    final int spacerBar = 16; // spacer bar diameter for >1 rebar layer
    //inddex for iteration for rebar diameters and links diameter
    int i1 = 0, i2 = 0, j1 = 0, j2 = 0;

    //Initialize the width
    final double width = 1000;
    double depth = input.minDepth, size = input.columnSize + 200, span;

    //k-value
    late double k;
    late double kLimit;

    //effective depth and lever-arm
    late double d, dC;
    late double z;

    // rebar provided
    int sTPro = input.maxS;
    int sCPro = input.maxS;
    late int sPro;

    // links provided: legs nos and spacing
    int sProX = input.maxS; // links, if any, spacing across section widht
    int sProY = input.maxS; // links, if any, spacing along span

    //nos of layer of tension and compression steel
    int layerT = 1, layerC = 1;

    // Some Limit for reasonable design
    int sMin = input.minS, sMax = input.maxS; // links spacing

    //req'd tension, compression steel, and links
    late double asT, asC;
    double lReq = 0;

    //provided tension, compression steel and links
    late double asTPro, asCPro, lPro;
    String asTProText = '', asCProText = '', lProText = '';

    //revised loading (incorproate Self Weight)
    late double ulsUDL;

    // design moment and shear
    late double mD, vD;
    late double p;
    double pc = 0; // SLS soil pressure

    // design capacity
    double fy = 500; // [MPa]

    late double vC_, vD_, vR_;

    // design for punching
    late double vDPunch, vDPunch15, vCPunch, lProPunch, lReqPunch, uo;
    int sProPunch = input.maxS;
    String lProPunchText = '';
    // record
    StringBuffer buffer = StringBuffer();
    List<bool> status = [false, false, false, false, false];
    List<FootingSchemeData> finalList = [];

    //design for deflection
    double maxDel = 0, maxDelLimit = 0 ;

    // Some global data
    final double f1 = globalData.sdlFactor;

    // ********************************
    // Calculation
    // ********************************
    shouldRedesign = true; // at least run the first
    // bool widthAdjused = false;

    while (shouldRedesign) {
      if (depth <= 175) {
        diaT = [12, 16, 20];
        diaC = diaT;
        diaL = [12, 16, 20];
      } else if (depth > 175 && depth <= 200) {
        diaT = [12, 16, 20, 25];
        diaC = diaT;
        diaL = [12, 16, 20];
      } else if (depth > 200 && depth <= 250) {
        diaT = [12, 16, 20, 25, 32];
        diaC = diaT;
        diaL = [12, 16, 20];
      } else {
        diaT = [12, 16, 20, 25, 32, 40];
        diaC = diaT;
        diaL = [12, 16, 20, 25];
      }
      diaP = diaL; // punching links diameter

      //* ULS design moment and shear adjusted with beam self-weight
      span = (size - input.columnSize) / 2 / 1000;

      ulsUDL =
          f1 *
              depth *
              pow(10, -3) *
              globalData
                  .rcUnitWeight //slab self-weight
                  +
          input.ulsLoad / (pow(size, 2) * pow(10, -6));
      mD = ulsUDL * pow(span, 2) / 2;
      vD = ulsUDL * span;

      //* SLS soil Pressure
      pc = getGroundBearingCapacity(
        depth,
        input.footingTopLevel,
        input.waterTableLevel,
        groundType: input.groundType,
        soilNValue: input.soilNValue,
        rockCapacity: input.rockCapacity,
      );
      p =
          input.slsLoad / (pow(size, 2) * pow(10, -6)) + // point load
          globalData.rcUnitWeight * depth * pow(10, -3); // footing self-weight

      //* effective depth
      if (lReq <= 0) {
        d =
            depth -
            input.cover -
            (diaT[i1] + (diaT.last + spacerBar) * (layerT - 1)) / 2;

        dC =
            input.cover +
            (diaC[i2] + (diaC.last + spacerBar) * (layerC - 1)) / 2;
      } else {
        d =
            depth -
            input.cover -
            diaL[j1] -
            (diaT[i1] + (diaT.last + spacerBar) * (layerT - 1)) / 2;

        dC =
            input.cover +
            diaL[j1] +
            (diaC[i2] + (diaC.last + spacerBar) * (layerC - 1)) / 2;
      }

      //* k Value
      k = getkValueRectangle(mD, width, d, input.fcu);
      kLimit = getkValueLimitRectangle(input.fcu);

      //* lever arm
      z = getLeverArmRectangle(k, kLimit, d);

      //* Required Steel
      asC = getRecBeamReqCompressionBar(mD, width, d, dC, input.fcu, fy: fy);
      asT = getRecBeamReqTensionBar(mD, width, d, dC, input.fcu, fy: fy);

      //* Provided tension bars
      asTPro = getOneWaySlabSteelProvided(
        diaT,
        layerT,
        diaT[i1],
        sTPro,
        input.minS,
        width,
      );

      //* Provided compression bars
      asCPro = getOneWaySlabSteelProvided(
        diaC,
        layerC,
        diaC[i2],
        sCPro,
        input.minS,
        width,
      );

      //* design links
      vD_ = vD * pow(10, 3) / (width * d);
      vC_ =
          0.79 *
          pow((100 * asTPro / (width * d)), 1 / 3) *
          pow(400 / d, 1.0 / 4.0) *
          max(pow((min(input.fcu, 80) / 25), 1 / 3), 1) /
          1.25;
      vR_ = max(0.4 * pow(min(input.fcu, 80) / 40, 2 / 3), 0.4);
      lReq = getRecSlabLinksRequired(vD, asTPro, input.fcu, width, d);

      if (lReq <= 0) {
        lPro = 0;
      } else {
        lPro = pow(diaL[j1], 2) * pi / 4 * (width / sProX) / sProY;
      }

      //* design for punching shear
      //* design Punching at column face
      if (input.colShape == 'Square') {
        uo = input.columnSize * 4;
      } else {
        uo = pi * input.columnSize;
      }
      vDPunch = (input.ulsLoad * 1000) / (uo * d);
      vCPunch = min(0.8 * sqrt(input.fcu), 7);

      //* design Punching links at 1.5d from column face
      //* if punching shear at column face is acceptable
      if (input.colShape == 'Square') {
        uo = (input.columnSize + 3 * d) * 4;
        uo = min(uo, size * 4);
      } else {
        uo = (input.columnSize + 3 * d) * pi;
        uo = min(uo, size * pi);
      }
      vDPunch15 = (input.ulsLoad * 1000) / (uo * d);
      lReqPunch = (vDPunch15 - vC_) * uo * d / (0.87 * fy);

      if (lReqPunch <= 0) {
        lReqPunch = 0;
        lProPunch = 0;
      } else {
        lProPunch = pow(diaP[j2], 2) * pi / 4 * (uo / sProPunch);
      }

      // ********************************
      // Design validaiton in each loop
      // ********************************
      // concept of adjustmernt
      // Flexural: decrease spacing, increase bar size, then increase layer
      // Shear: increase legs nos (reduce spacing across), then decrease along spacing, and finally increase bar size.

      //! if the k-value exceeds preferred k-value/steel ratio, skip iteration
      if (k > input.mainKValue ||
          asTPro > input.mainSteelRatio * depth * width ||
          asCPro > input.mainSteelRatio * depth * width) {
        depth += depthDelta;
        skipIteration = true;
      }

      // ! if the depth exceeds the max width, directly escape and declare the design failed
      if (depth > input.maxDepth) {
        status[0] = false;
        status[1] = false;
        status[2] = false;
        depth -= depthDelta;
        break;
      }

      //! Skip iteration if necessary
      if (skipIteration) {
        //reset diameters, layers, spacing, nos of rebars
        i1 = 0;
        i2 = 0;
        layerT = 1;
        layerC = 1;
        j1 = 0;
        sTPro = input.maxS;
        sCPro = input.maxS;
        sProX = input.maxS;
        sProY = input.maxS;
        skipIteration = false;
        continue;
      }

      //* check SLS pressure first. If size (on-plan) changes,
      if (pc < p) {
        reDesignSize = true;
        //* the entire RC design needs review as well since the span and hence
        //* demand force is different
        redesignacT = true;
        redesignacC = true;
        reDesignLinks = true;
        reDesignPunching = true;
        size += sizeDelta;

        //reset rebars and links arrangement
        i1 = 0;
        i2 = 0;
        layerT = 1;
        layerC = 1;
        sTPro = input.maxS;
        sCPro = input.maxS;

        j1 = 0;
        sProX = input.maxS;
        sProY = input.maxS;

        j2 = 0;
        sProPunch = input.maxS;

        continue;
      } else {
        reDesignSize = false;
        status[4] = true;
      }

      //* check  deflection
      final double elasticModulus = getElasticModulus(input.fcu); //[N/mm2]
      final double secondAreaMoment = getSecondAreaMomentRectangle(
        width,
        depth,
      ); //[mm4]
      maxDel = getMaxDeflectionSimpleBeamUDL(
        p,
        span,
        elasticModulus,
        secondAreaMoment,
        beamType: BeamType.Cantilever,
      );
      maxDelLimit = span * pow(10, 3) / 250;
      if (maxDel > maxDelLimit) {
        //* the entire RC design needs review as well since the span and hence
        //* demand force is different
        redesignacT = true;
        redesignacC = true;
        reDesignLinks = true;
        reDesignPunching = true;
        depth += depthDelta;

        //reset rebars and links arrangement
        i1 = 0;
        i2 = 0;
        layerT = 1;
        layerC = 1;
        sTPro = input.maxS;
        sCPro = input.maxS;

        j1 = 0;
        sProX = input.maxS;
        sProY = input.maxS;

        j2 = 0;
        sProPunch = input.maxS;

        continue; // no need to run rest of design process
      } else {
        reDesignDelection = false;
      }

      // Todo: check tension bars
      if (asTPro < asT) {
        redesignacT = true;
        if (sTPro - 25 >= input.minS) {
          sTPro -= 25;
        } else if ((sTPro == input.minS) && (i1 + 1 <= diaT.length - 1)) {
          sTPro = input.maxS;
          i1 += 1;
        } else if ((sTPro == input.minS) &&
            (i1 == diaT.length - 1) &&
            (layerT + 1 <= input.maxLayers)) {
          sTPro = input.maxS;
          i1 = 0;
          layerT += 1;
        } else if ((sTPro == input.minS) &&
            (i1 == diaT.length - 1) &&
            (layerT == input.maxLayers) &&
            (depth + depthDelta <= input.maxDepth)) {
          depth += depthDelta;
          //reset rebars and links arrangement
          i1 = 0;
          i2 = 0;
          layerT = 1;
          layerC = 1;
          sTPro = input.maxS;
          sCPro = input.maxS;

          j1 = 0;
          sProX = input.maxS;
          sProY = input.maxS;

          j2 = 0;
          sProPunch = input.maxS;
          redesignacT = true;

          continue; // no need to run rest of design process
        } else {
          redesignacT = false;
        }
      } else {
        redesignacT = false;
        status[0] = true;
      }

      // todo:  check compression bars
      if (asCPro < asC) {
        redesignacC = true;
        if (sCPro - 25 >= input.minS) {
          sCPro -= 25;
        } else if ((sCPro == input.minS) && (i2 + 1 <= diaC.length - 1)) {
          sCPro = input.maxS;
          i2 += 1;
        } else if ((sCPro == input.minS) &&
            (i2 == diaC.length - 1) &&
            (layerC + 1 <= input.maxLayers)) {
          sCPro = input.maxS;
          i2 = 0;
          layerC += 1;
        } else if (sCPro == input.minS &&
            i2 == diaT.length - 1 &&
            layerC == input.maxLayers &&
            depth + 25 <= input.maxDepth) {
          depth += 25;
          //reset rebars and links arrangement
          i1 = 0;
          i2 = 0;
          layerT = 1;
          layerC = 1;
          sTPro = input.maxS;
          sCPro = input.maxS;

          j1 = 0;
          sProX = input.maxS;
          sProY = input.maxS;

          j2 = 0;
          sProPunch = input.maxS;
          redesignacC = true;
          continue; // no need to run rest of design process
        } else {
          redesignacC = false;
        }
      } else {
        // there could be situation links pass, rebar not
        // then the width and effective depth keeps change
        // then req'd links keep reducing while provided links kept at high amount
        // Therefore, we redesign in case the provided links are 20% more than required

        if (asCPro > asC * 1.2 &&
            compSteelOptimizationSteps < compSteelOptimizationStepsLimit) {
          compSteelOptimizationSteps += 1;
          reDesignLinks = true;
          sCPro = input.maxS;
          i2 = 0;
        } else {
          redesignacC = false;
          status[1] = true;
        }
      }

      //todo: check shear
      if (lPro < lReq) {
        reDesignLinks = true;
        if (sProX - 25 >= input.minS) {
          sProX -= 25;
        } else if (sProX == input.minS && sProY - 25 >= input.minS) {
          sProX = input.maxS;
          sProY -= 25;
        } else if (sProX == input.minS &&
            sProY == input.minS &&
            j1 + 1 <= diaL.length - 1) {
          sProX = input.maxS;
          sProY = input.maxS;
          j1 += 1;
        } else if (sProX == input.minS &&
            sProY == input.minS &&
            j1 == diaL.length - 1 &&
            depth + depthDelta <= input.maxDepth) {
          depth += depthDelta;
          //reset rebars and links arrangement
          i1 = 0;
          i2 = 0;
          layerT = 1;
          layerC = 1;
          sTPro = input.maxS;
          sCPro = input.maxS;

          j1 = 0;
          sProX = input.maxS;
          sProY = input.maxS;

          j2 = 0;
          sProPunch = input.maxS;
          reDesignLinks = true;
          continue; // no need to run rest of design process
        } else {
          reDesignLinks = false;
        }
      } else {
        // there could be situation links pass, rebar not
        // then the width and effective depth keeps change
        // then req'd links keep reducing while provided links kept at high amount
        // Therefore, we redesign in case the provided links are 20% more than required

        if (lReq > 0 &&
            (lPro > lReq * 1.2 &&
                linksOptimizationSteps < linksOptimizationStepsLimit)) {
          linksOptimizationSteps += 1;
          reDesignLinks = true;
          sProX = input.maxS;
          sProY = input.maxS;
          j1 = 0;
        } else {
          reDesignLinks = false;
          status[2] = true;
        }
      }
      // todo: check punching shear
      //* 1. check punching at column face
      if (vCPunch < vDPunch) {
        reDesignPunching = true;
        if (depth + depthDelta <= input.maxDepth) {
          depth += depthDelta;
          //reset rebars and links arrangement
          i1 = 0;
          i2 = 0;
          layerT = 1;
          layerC = 1;
          sTPro = input.maxS;
          sCPro = input.maxS;

          j1 = 0;
          sProX = input.maxS;
          sProY = input.maxS;

          j2 = 0;
          sProPunch = input.maxS;
          continue; // no need to run rest of design process
        } else {
          reDesignPunching = false;
        }
      } else {
        //* 2. check 1.5d from the column face
        if ((lReqPunch > 0) && (lProPunch <= lReqPunch)) {
          reDesignPunching = true;
          if (sProPunch - 25 >= input.minS) {
            sProPunch -= 25;
          } else if (sProPunch == input.minS && j2 + 1 <= diaP.length - 1) {
            sProPunch = input.maxS;
            j2 += 1;
          } else if (sProPunch == input.minS &&
              j2 == diaP.length - 1 &&
              depth + depthDelta <= input.maxDepth) {
            depth += depthDelta;
            //reset rebars and links arrangement
            i1 = 0;
            i2 = 0;
            layerT = 1;
            layerC = 1;
            sTPro = input.maxS;
            sCPro = input.maxS;

            j1 = 0;
            sProX = input.maxS;
            sProY = input.maxS;

            j2 = 0;
            sProPunch = input.maxS;

            reDesignPunching = true;
            continue; // no need to run rest of design process
          } else {
            reDesignPunching = false;
          }
        } else {
          if (lReqPunch > 0 &&
              (lProPunch > lReqPunch * 1.2 &&
                  punchinglinksOptimizationSteps <
                      punchinglinksOptimizationStepsLimit)) {
            punchinglinksOptimizationSteps += 1;

            reDesignPunching = true;
            sProPunch = input.maxS;
            j2 = 0;
          } else {
            reDesignPunching = false;
            status[3] = true;
          }
        }
      }
      shouldRedesign =
          redesignacT ||
          redesignacC ||
          reDesignLinks ||
          reDesignPunching ||
          reDesignSize ||
          reDesignDelection;
    }

    // ********************************
    //* Unify the spacing and steel provided
    // ********************************
    // across section, the link, tension bar, and compression bar
    // spacing should be the same for constructability
    sPro = min(sProX, min(sTPro, sCPro));
    if (lPro > 0) {
      if (sPro == sProX) {
        sTPro = sPro;
        sCPro = sPro;
      } else {
        if (sProX % sTPro != 0 || sProX % sCPro != 0) {
          sProX = sPro;
          sCPro = sPro;
          sTPro = sPro;
        }
      }
    }

    // re-calcualte the provided steel
    asTPro = getOneWaySlabSteelProvided(
      diaT,
      layerT,
      diaT[i1],
      sTPro,
      input.minS,
      width,
    );
    asCPro = getOneWaySlabSteelProvided(
      diaC,
      layerC,
      diaC[i2],
      sCPro,
      input.minS,
      width,
    );
    if (lReq <= 0) {
      lPro = 0;
    } else {
      lPro = pow(diaL[j1], 2) * pi / 4 * (width / sProX) / sProY;
    }
    // ********************************
    //* Return Design
    // ********************************
    asTProText = getOneWaySlabSteelProvidedText(
      diaT,
      layerT,
      diaT[i1],
      sTPro,
      input.minS,
    );
    if (!status[0]) {
      asTProText = '(Fail) $asTProText';
    }
    asCProText = getOneWaySlabSteelProvidedText(
      diaC,
      layerC,
      diaC[i2],
      sCPro,
      input.minS,
    );
    if (!status[1]) {
      asCProText = '(Fail) $asCProText';
    }
    lProText = getRecSlabLinksProvidedText(diaL[j1], sProX, sProY);
    if (!status[2]) {
      lProText = '(Fail) $lProText';
    }
    // form punching links designation
    if (status[3]) {
      if (lProPunch != 0) {
        lProPunchText = 'T${diaL[j2]}-$sProPunch';
      } else {
        lProPunchText = 'N.A.';
      }
    } else {
      lProPunchText = '(Fail) T${diaL[j2]}-$sProPunch';
    }

    // before return, record the cals result
    _recordCalsResult(
      buffer,
      input,
      globalData,
      size: size,
      p: p,
      pc: pc,
      k: k,
      s_t_pro: sTPro,
      s_c_pro: sCPro,
      As_t_pro: asTPro,
      As_t: asT,
      As_c_pro: asCPro,
      As_c: asC,
      M_d: mD,
      V_d: vD,
      depth: depth,
      layer_t: layerT,
      layer_c: layerC,
      dia_t: diaT[i1],
      dia_c: diaC[i2],
      dia_l: diaL[j1],
      d: d,
      d_c: dC,
      z: z,
      As_t_pro_text: asTProText,
      As_c_pro_text: asCProText,
      l_pro_text: lProText,
      l_pro: lPro,
      l_req: lReq,
      v_d: vD_,
      v_c: vC_,
      v_r: vR_,
      vDPunch: vDPunch,
      vCPunch: vCPunch,
      vDPunch15: vDPunch15,
      lReqPunch: lReqPunch,
      lProPunch: lProPunch,
      lProPunchText: lProPunchText,
      s_pro_x: sProX,
      s_pro_y: sProY,
      s_min: sMin,
      s_max: sMax,
      maxDeflection: maxDel,
      deflectionLimit: maxDelLimit,
      status: status,
    );

    //* log any error and warning
    List<String> errors = [], warnings = [];
    RegExp failReg = RegExp(r'fail', caseSensitive: false);
    if (failReg.hasMatch(buffer.toString())) {
      errors.add('Result not reliable. Something fails.');
      warnings.add('Try adjust the footing depth limit.');
    }
    if (depth > input.maxDepth) {
      errors.add('Footing depth exceeds limit.');
    }
    if (failReg.hasMatch(asTProText)) {
      errors.add('Tension bar fails.');
    }
    if (failReg.hasMatch(asCProText)) {
      errors.add('Compression bar fails.');
    }
    if (failReg.hasMatch(lProText)) {
      errors.add('Links fails.');
    }
    if (errors.isNotEmpty) {
      buffer = addErrorHeader(buffer, errors);
    }
    if (warnings.isNotEmpty) {
      buffer = addWarningHeader(buffer, warnings);
    }

    //* return scheme
    finalList.add(
      FootingSchemeData(
        size: size,
        strZone: depth,
        fcu: input.fcu,
        cover: input.cover,
        groundType: input.groundType,
        soilNValue: input.soilNValue,
        footingTopLevel: input.footingTopLevel,
        waterTableLevel: input.waterTableLevel,
        rockCapacity: input.rockCapacity,
        colShape: input.colShape,
        columnSize: input.columnSize,
        slsLoad: input.slsLoad,
        ulsLoad: input.ulsLoad,
        mainTopBar: asCProText,
        mainBottomBar: asTProText,
        mainLinks: lProText,
        punchingLinks: lProPunchText,
        calsLog: buffer.toString(),
        isSelected: false,
        footingSchemeDataId: '1',
      ),
    );
    state = AsyncData(finalList);
    return finalList;
  }

  void _recordCalsResult(
    StringBuffer buffer,
    FootingSchemeInput input,
    GlobalData globalData, {
    required double size,
    required double p,
    required double pc,
    required double k,
    required int s_t_pro,
    required int s_c_pro,
    required double As_t_pro,
    required double As_t,
    required double As_c_pro,
    required double As_c,
    required double M_d,
    required double V_d,
    required double depth,
    required int layer_t,
    required int layer_c,
    required int dia_t,
    required int dia_c,
    required int dia_l,
    required double d,
    required double d_c,
    required double z,
    required String As_t_pro_text,
    required String As_c_pro_text,
    required String l_pro_text,
    required double l_pro,
    required double l_req,
    required double v_d,
    required double v_c,
    required double v_r,
    required double vDPunch,
    required double vCPunch,
    required double vDPunch15,
    required double lReqPunch,
    required double lProPunch,
    required String lProPunchText,
    required int s_pro_x,
    required int s_pro_y,
    required int s_min,
    required int s_max,
    required maxDeflection,
    required deflectionLimit,
    required List<bool> status,
  }) {
    //* Initialization
    final x = globalData.unit;
    late final List<String> unit;
    final double span, pULS;
    final f0 = NumberFormat('0.0'),
        f1 = NumberFormat('0.0'),
        f3 = NumberFormat('0.000');
    //* Calculates parameters
    switch (x) {
      case 'metrics':
        unit = PreferredUnit.metrics;
        break;
      case 'imperial':
        unit = PreferredUnit.imperial;
        break;
      default:
        unit = PreferredUnit.metrics;
        break;
    }
    span = (size - input.columnSize) / 2;

    buffer.clear();
    buffer.write('Size: ${f1.format(size)} [${unit[4]}] | ');
    buffer.write('Span: ${f1.format(span)} [${unit[4]}] | ');
    buffer.write('SLS Load: ${f1.format(input.slsLoad)} [${unit[0]}] | ');
    buffer.write('ULS Load: ${f1.format(input.ulsLoad)} [${unit[0]}]\n');

    buffer.write('Footing Depth: ${f1.format(depth)} [${unit[4]}] | ');
    buffer.write('Cover: ${f1.format(input.cover)} [${unit[4]}]\n');

    buffer.write('M: ${f0.format(M_d)} [${unit[2]}/${unit[3]}] | ');
    buffer.write('V: ${f0.format(V_d)} [${unit[0]}/${unit[3]}] | ');
    buffer.write('Ground Pressure (SLS): ${f0.format(p)} [${unit[1]}]\n');
    buffer.write('Ground Capacity (SLS): ${f0.format(pc)} [${unit[1]}]\n');
    pULS =
        input.ulsLoad / (pow(size, 2) * pow(10, -6)) +
        globalData.sdlFactor * globalData.rcUnitWeight * depth * pow(10, -3);
    buffer.write('Ground Pressure (ULS): ${f0.format(pULS)} [${unit[1]}]\n');

    buffer.write('depth: $depth [${unit[4]}] | ');
    buffer.write('design strip width: 1000 [${unit[4]}]\n');
    buffer.write('k: ${f3.format(k)} | ');
    buffer.write('k_limit: ${f3.format(input.mainKValue)} | ');
    buffer.write('preferred k: ${f3.format(input.mainKValue)}\n');

    buffer.write('v_d: ${f3.format(v_d)} [${unit[5]}] | ');
    buffer.write('v_c: ${f3.format(v_c)} [${unit[5]}] | ');
    buffer.write('v_r: ${f3.format(v_r)} [${unit[5]}]\n');

    buffer.write('dia_t: ${f0.format(dia_t)} [${unit[4]}] | ');
    buffer.write('dia_c: ${f0.format(dia_c)} [${unit[4]}] | ');
    if (l_pro_text != 'N.A.') {
      buffer.write('dia_l: ${f0.format(dia_l)} [${unit[4]}]\n');
    } else {
      buffer.write('dia_l: N.A.\n');
    }

    buffer.write('s_t_pro: ${f0.format(s_t_pro)} [${unit[4]}] | ');
    buffer.write('s_c_pro: ${f0.format(s_c_pro)} [${unit[4]}] | ');

    if (l_pro_text != 'N.A.') {
      buffer.write('s_pro_x: ${f0.format(s_pro_x)} [${unit[4]}] | ');
      buffer.write('s_pro_y: ${f0.format(s_pro_y)} [${unit[4]}]\n');
    } else {
      buffer.write('s_pro_x: N.A. | ');
      buffer.write('s_pro_y: N.A.\n');
    }

    buffer.write('s_min: ${f0.format(s_min)} [${unit[4]}] | ');
    buffer.write('s_max: ${f0.format(s_max)} [${unit[4]}]\n');
    buffer.write('layer_t: ${f0.format(layer_t)} | ');
    buffer.write('layer_c: ${f0.format(layer_c)} | ');

    buffer.write('d: ${f0.format(d)} [${unit[4]}] | ');
    buffer.write('d_c: ${f0.format(d_c)} [${unit[4]}] | ');
    buffer.write('z: ${f0.format(z)} [${unit[4]}]\n');
    buffer.write('As_t_pro: ${f0.format(As_t_pro)} [${unit[6]}/${unit[3]}] | ');
    buffer.write('As_t: ${f0.format(As_t)} [${unit[6]}/${unit[3]}]| ');
    buffer.write(
      'As_t limit: ${f0.format((input.mainSteelRatio) * (1000) * (depth))} [${unit[6]}/${unit[3]}]\n',
    );
    buffer.write('As_c_pro: ${f0.format(As_c_pro)} [${unit[6]}/${unit[3]}] | ');
    buffer.write('As_c: ${f0.format(As_c)} [${unit[6]}/${unit[3]}]| ');
    buffer.write(
      'As_c limit: ${f0.format((input.mainSteelRatio) * (1000) * (depth))} [${unit[6]}/${unit[3]}]\n',
    );

    buffer.write('l_pro: ${f3.format(l_pro)} [${unit[6]}/${unit[4]}] | ');
    buffer.write('l_req: ${f3.format(l_req)} [${unit[6]}/${unit[4]}]\n');
    buffer.write('vDPunch: ${f3.format(vDPunch)} [${unit[5]}] | ');
    buffer.write('vCPunch: ${f3.format(vCPunch)} [${unit[5]}] | ');
    buffer.write('vDPunch15: ${f3.format(vDPunch15)} [${unit[5]}]\n');
    buffer.write(
      'lReqPunch: ${f3.format(lReqPunch)} [${unit[6]}/${unit[4]}] | ',
    );
    buffer.write(
      'lProPunch: ${f3.format(lProPunch)} [${unit[6]}/${unit[4]}]\n',
    );

    //* Deflection
    buffer.write(
      'Max Deflection (Cantilever): ${f3.format(maxDeflection)} [${unit[4]}] | ',
    );
    buffer.write(
      'Deflection Limit (Cantilever): ${f3.format(deflectionLimit)} [${unit[4]}]\n',
    );
    buffer.write('As_t_pro Designation: $As_t_pro_text\n');
    buffer.write('As_c_pro Designation: $As_c_pro_text\n');
    buffer.write('l_pro Designation: $l_pro_text\n');
    buffer.write('l_pro Punching Designation: $lProPunchText\n');

    if (status[0]) {
      buffer.write('Tension bars: pass | ');
    } else {
      buffer.write('Tension bars: fail | ');
    }
    if (status[1]) {
      buffer.write('Compression bars: pass | ');
    } else {
      buffer.write('Compression bars: fail | ');
    }
    if (status[2]) {
      buffer.write('Links: pass\n');
    } else {
      buffer.write('Links: fail\n');
    }
    if (status[3]) {
      buffer.write('SLS Ground Pressure: pass\n');
    } else {
      buffer.write('SLS Ground Pressure: fail\n');
    }
    if (status[4]) {
      buffer.write('Punching Shear: pass\n');
    } else {
      buffer.write('Punching Shear: fail\n');
    }
  }
}
