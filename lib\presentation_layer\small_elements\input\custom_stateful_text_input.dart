import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter/services.dart';

class CustomStatefulTextInput extends ConsumerStatefulWidget {
  CustomStatefulTextInput({
    this.title = 'Text Input',
    this.value = '',
    this.sizeScale = 1.0,
    this.onChanged,
    this.listener,
    this.helperText,
    this.tooltipText,
    this.readOnly,
    super.key,
  });

  final String? title;
  String? value;
  double? sizeScale;
  dynamic Function(String)? onChanged;
  dynamic Function(bool, String)? listener;
  String? errorMsg;
  String? helperText;
  String? tooltipText;
  bool? readOnly;
  @override
  ConsumerState<ConsumerStatefulWidget> createState() =>
      _CustomStatefulTextInputState();
}

class _CustomStatefulTextInputState
    extends ConsumerState<CustomStatefulTextInput> {
  //corresponding to widget part
  late String _title;
  late String _value;
  late double _sizeScale;
  late dynamic Function(String)? _onChanged;
  late dynamic Function(bool, String)? _listener;

  late FocusNode _focusNode;

  late TextEditingController _controller;
  late String? _errorMsg;
  late String? _helperText;
  late String? _tooltipText;
  late bool _readOnly;
  @override
  void dispose() {
    _controller.dispose();
    _focusNode.removeListener(() {});
    _focusNode.dispose();
    super.dispose();
  }

  @override
  void initState() {
    _errorMsg = null;
    widget.errorMsg = _errorMsg;

    _title = widget.title ?? '';
    _value = widget.value ?? '';
    _sizeScale = widget.sizeScale ?? 1.0;
    _onChanged = widget.onChanged;
    _listener = widget.listener;
    _helperText = widget.helperText;
    _tooltipText = widget.tooltipText;
    _controller = TextEditingController(text: widget.value.toString());
    _readOnly = widget.readOnly ?? false;
    _focusNode = FocusNode();
    _focusNode.addListener(() {
      final hasFocus = _focusNode.hasFocus;
      if (_listener != null) {
        _listener!(hasFocus, _value);
      }
      setState(() {});
    });

    super.initState();
  }

  @override
  void didUpdateWidget(covariant CustomStatefulTextInput oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.value != oldWidget.value) {
      _title = widget.title ?? '';
      _readOnly = widget.readOnly ?? _readOnly;
      _onChanged = widget.onChanged ?? _onChanged;
      _tooltipText = widget.tooltipText ?? _tooltipText;
      if (_controller.text != widget.value?.toString()) {
        _controller.text = widget.value?.toString() ?? _controller.text;
      }
    }
    // //additional logic
    // final p0 = _controller.selection;
    // _value = widget.value ?? _value;
    // _controller.text =
    //     widget.value ??
    //     _controller.text; //since widget.value changes means _value changes

    // try {
    //   _controller.selection = p0;
    // } catch (e) {
    //   _controller.selection = TextSelection.fromPosition(
    //     TextPosition(offset: _controller.text.length),
    //   );
    // }
  }

  @override
  Widget build(BuildContext context) {
    // ref.watch(_errorMsgProvider);

    final textTheme = Theme.of(context).textTheme;
    final colorScheme = Theme.of(context).colorScheme;

    final fillColor =
        _readOnly ? colorScheme.surfaceDim.withAlpha(150) : colorScheme.surface;
    final textColor =
        _readOnly
            ? colorScheme.onSurfaceVariant.withAlpha(150)
            : textTheme.bodyMedium?.color;
    return Transform.scale(
      scale: _sizeScale,
      child: TextField(
        readOnly: _readOnly ?? false,
        keyboardType: TextInputType.number,
        focusNode: _focusNode,
        controller: _controller,
        style: textTheme.bodyMedium!.copyWith(color: textColor),
        decoration: InputDecoration(
          filled: true,
          fillColor: fillColor,
          helperText: _helperText,
          suffix:
              _tooltipText != null
                  ? Tooltip(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10.0),
                      color: colorScheme.tertiaryContainer.withAlpha(225),
                    ),
                    textStyle: textTheme.labelLarge!.copyWith(
                      color: colorScheme.onTertiaryContainer.withAlpha(225),
                    ),
                    message: _tooltipText,
                    child: Icon(
                      Icons.info_outline,
                      size: textTheme.titleMedium?.fontSize,
                      color: colorScheme.onPrimaryContainer,
                    ),
                  )
                  : null,
          isDense: true,
          border: OutlineInputBorder(),
          label: Text(_title),
          labelStyle: textTheme.labelLarge,
          errorText: _errorMsg,
        ),
        inputFormatters: <TextInputFormatter>[
          FilteringTextInputFormatter.allow(RegExp(r'[\w ]')),
        ],
        onChanged: (value) {
          _errorMsg = null;
          widget.errorMsg = _errorMsg;
          _value = value;
          widget.value = _value; //update the widgetvalue

          if (_onChanged != null) {
            final x = _onChanged!(_value);
            if (x is String || x == null) {
              _errorMsg = x;
            }
          }
          setState(() {});
          // print('onChanged: ${_controller.text}');
        },
      ),
    );
  }
}
