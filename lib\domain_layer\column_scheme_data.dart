import 'package:freezed_annotation/freezed_annotation.dart';
// import 'package:flutter/foundation.dart';
part 'column_scheme_data.freezed.dart';
part 'column_scheme_data.g.dart';

@freezed
abstract class ColumnSchemeData with _$ColumnSchemeData {
  const ColumnSchemeData._();
  factory ColumnSchemeData({
    @Default('') String columnSchemeDataId, //will be overriden  as soon as new instance created
    @Default(0.0) double sdl,
    @Default(0.0) double ll,
    @Default(0.0) double slsLoad,
    @Default(0.0) double ulsLoad,
    @Default(500.0) double size,
    @Default(45.0) double fcu,
    @Default(40.0) double cover,
    @Default(0.0) double axialCapacitySquare,
    @Default('') String mainBarSquare,
    @Default(0.0) double steelRatioSqaure,
    @Default(0.0) double axialCapacityCircle,
    @Default('') String mainBarCircle,
    @Default(0.0) double steelRatioCircle,
    @Default('') String calsLog,
    @Default(false) bool isSelected,

  }) = _ColumnSchemeData; 


  factory ColumnSchemeData.fromJson(Map<String, Object?> json) =>
      _$ColumnSchemeDataFromJson(json);
}
