// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'transfer_beam_scheme_data_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$transferBeamSchemeDataControllerHash() =>
    r'19037af470a66cd5c07a7837bbe1c90957f37f0e';

/// See also [TransferBeamSchemeDataController].
@ProviderFor(TransferBeamSchemeDataController)
final transferBeamSchemeDataControllerProvider =
    AutoDisposeAsyncNotifierProvider<
      TransferBeamSchemeDataController,
      TransferBeamSchemeData
    >.internal(
      TransferBeamSchemeDataController.new,
      name: r'transferBeamSchemeDataControllerProvider',
      debugGetCreateSourceHash:
          const bool.fromEnvironment('dart.vm.product')
              ? null
              : _$transferBeamSchemeDataControllerHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$TransferBeamSchemeDataController =
    AutoDisposeAsyncNotifier<TransferBeamSchemeData>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
