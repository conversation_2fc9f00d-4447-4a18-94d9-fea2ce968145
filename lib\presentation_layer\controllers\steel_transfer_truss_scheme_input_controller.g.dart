// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'steel_transfer_truss_scheme_input_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$steelTransferTrussSchemeInputControllerHash() =>
    r'ed3f5ee6292c8ee51f50cf7952b36f32b7735a17';

/// See also [SteelTransferTrussSchemeInputController].
@ProviderFor(SteelTransferTrussSchemeInputController)
final steelTransferTrussSchemeInputControllerProvider =
    AutoDisposeAsyncNotifierProvider<
      SteelTransferTrussSchemeInputController,
      List<SteelTransferTrussSchemeInput>
    >.internal(
      SteelTransferTrussSchemeInputController.new,
      name: r'steelTransferTrussSchemeInputControllerProvider',
      debugGetCreateSourceHash:
          const bool.fromEnvironment('dart.vm.product')
              ? null
              : _$steelTransferTrussSchemeInputControllerHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$SteelTransferTrussSchemeInputController =
    AutoDisposeAsyncNotifier<List<SteelTransferTrussSchemeInput>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
