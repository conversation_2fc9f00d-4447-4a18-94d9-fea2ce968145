import 'package:freezed_annotation/freezed_annotation.dart';
// import 'package:flutter/foundation.dart';
part 'pile_end_bearing_bored_input_global.freezed.dart';
part 'pile_end_bearing_bored_input_global.g.dart';

@freezed
abstract class PileEndBearingBoredInputGlobal with _$PileEndBearingBoredInputGlobal{
  const PileEndBearingBoredInputGlobal._();
  factory PileEndBearingBoredInputGlobal({
    @Default(1.0) double colLoadFactor,
    @Default('1') String id, //will be overriden  as soon as new instance created
  }) = _PileEndBearingBoredInputGlobal;

  factory PileEndBearingBoredInputGlobal.fromJson(Map<String, Object?> json) =>
      _$PileEndBearingBoredInputGlobalFromJson(json); 
}
