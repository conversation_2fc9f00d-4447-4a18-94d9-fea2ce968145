// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'crack_width_struct.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$CrackWidth {

 double get M; double get h; double get b; double get d; double get fcu; double get As; double get x; double get c_min; int get dia_l; double get Es; double get a_cr; double get fcc; double get fst; double get a_pi; double get e_1; double get e_m; double get crackWidth;
/// Create a copy of CrackWidth
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$CrackWidthCopyWith<CrackWidth> get copyWith => _$CrackWidthCopyWithImpl<CrackWidth>(this as CrackWidth, _$identity);

  /// Serializes this CrackWidth to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is CrackWidth&&(identical(other.M, M) || other.M == M)&&(identical(other.h, h) || other.h == h)&&(identical(other.b, b) || other.b == b)&&(identical(other.d, d) || other.d == d)&&(identical(other.fcu, fcu) || other.fcu == fcu)&&(identical(other.As, As) || other.As == As)&&(identical(other.x, x) || other.x == x)&&(identical(other.c_min, c_min) || other.c_min == c_min)&&(identical(other.dia_l, dia_l) || other.dia_l == dia_l)&&(identical(other.Es, Es) || other.Es == Es)&&(identical(other.a_cr, a_cr) || other.a_cr == a_cr)&&(identical(other.fcc, fcc) || other.fcc == fcc)&&(identical(other.fst, fst) || other.fst == fst)&&(identical(other.a_pi, a_pi) || other.a_pi == a_pi)&&(identical(other.e_1, e_1) || other.e_1 == e_1)&&(identical(other.e_m, e_m) || other.e_m == e_m)&&(identical(other.crackWidth, crackWidth) || other.crackWidth == crackWidth));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,M,h,b,d,fcu,As,x,c_min,dia_l,Es,a_cr,fcc,fst,a_pi,e_1,e_m,crackWidth);

@override
String toString() {
  return 'CrackWidth(M: $M, h: $h, b: $b, d: $d, fcu: $fcu, As: $As, x: $x, c_min: $c_min, dia_l: $dia_l, Es: $Es, a_cr: $a_cr, fcc: $fcc, fst: $fst, a_pi: $a_pi, e_1: $e_1, e_m: $e_m, crackWidth: $crackWidth)';
}


}

/// @nodoc
abstract mixin class $CrackWidthCopyWith<$Res>  {
  factory $CrackWidthCopyWith(CrackWidth value, $Res Function(CrackWidth) _then) = _$CrackWidthCopyWithImpl;
@useResult
$Res call({
 double M, double h, double b, double d, double fcu, double As, double x, double c_min, int dia_l, double Es, double a_cr, double fcc, double fst, double a_pi, double e_1, double e_m, double crackWidth
});




}
/// @nodoc
class _$CrackWidthCopyWithImpl<$Res>
    implements $CrackWidthCopyWith<$Res> {
  _$CrackWidthCopyWithImpl(this._self, this._then);

  final CrackWidth _self;
  final $Res Function(CrackWidth) _then;

/// Create a copy of CrackWidth
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? M = null,Object? h = null,Object? b = null,Object? d = null,Object? fcu = null,Object? As = null,Object? x = null,Object? c_min = null,Object? dia_l = null,Object? Es = null,Object? a_cr = null,Object? fcc = null,Object? fst = null,Object? a_pi = null,Object? e_1 = null,Object? e_m = null,Object? crackWidth = null,}) {
  return _then(_self.copyWith(
M: null == M ? _self.M : M // ignore: cast_nullable_to_non_nullable
as double,h: null == h ? _self.h : h // ignore: cast_nullable_to_non_nullable
as double,b: null == b ? _self.b : b // ignore: cast_nullable_to_non_nullable
as double,d: null == d ? _self.d : d // ignore: cast_nullable_to_non_nullable
as double,fcu: null == fcu ? _self.fcu : fcu // ignore: cast_nullable_to_non_nullable
as double,As: null == As ? _self.As : As // ignore: cast_nullable_to_non_nullable
as double,x: null == x ? _self.x : x // ignore: cast_nullable_to_non_nullable
as double,c_min: null == c_min ? _self.c_min : c_min // ignore: cast_nullable_to_non_nullable
as double,dia_l: null == dia_l ? _self.dia_l : dia_l // ignore: cast_nullable_to_non_nullable
as int,Es: null == Es ? _self.Es : Es // ignore: cast_nullable_to_non_nullable
as double,a_cr: null == a_cr ? _self.a_cr : a_cr // ignore: cast_nullable_to_non_nullable
as double,fcc: null == fcc ? _self.fcc : fcc // ignore: cast_nullable_to_non_nullable
as double,fst: null == fst ? _self.fst : fst // ignore: cast_nullable_to_non_nullable
as double,a_pi: null == a_pi ? _self.a_pi : a_pi // ignore: cast_nullable_to_non_nullable
as double,e_1: null == e_1 ? _self.e_1 : e_1 // ignore: cast_nullable_to_non_nullable
as double,e_m: null == e_m ? _self.e_m : e_m // ignore: cast_nullable_to_non_nullable
as double,crackWidth: null == crackWidth ? _self.crackWidth : crackWidth // ignore: cast_nullable_to_non_nullable
as double,
  ));
}

}


/// Adds pattern-matching-related methods to [CrackWidth].
extension CrackWidthPatterns on CrackWidth {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _CrackWidth value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _CrackWidth() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _CrackWidth value)  $default,){
final _that = this;
switch (_that) {
case _CrackWidth():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _CrackWidth value)?  $default,){
final _that = this;
switch (_that) {
case _CrackWidth() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( double M,  double h,  double b,  double d,  double fcu,  double As,  double x,  double c_min,  int dia_l,  double Es,  double a_cr,  double fcc,  double fst,  double a_pi,  double e_1,  double e_m,  double crackWidth)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _CrackWidth() when $default != null:
return $default(_that.M,_that.h,_that.b,_that.d,_that.fcu,_that.As,_that.x,_that.c_min,_that.dia_l,_that.Es,_that.a_cr,_that.fcc,_that.fst,_that.a_pi,_that.e_1,_that.e_m,_that.crackWidth);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( double M,  double h,  double b,  double d,  double fcu,  double As,  double x,  double c_min,  int dia_l,  double Es,  double a_cr,  double fcc,  double fst,  double a_pi,  double e_1,  double e_m,  double crackWidth)  $default,) {final _that = this;
switch (_that) {
case _CrackWidth():
return $default(_that.M,_that.h,_that.b,_that.d,_that.fcu,_that.As,_that.x,_that.c_min,_that.dia_l,_that.Es,_that.a_cr,_that.fcc,_that.fst,_that.a_pi,_that.e_1,_that.e_m,_that.crackWidth);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( double M,  double h,  double b,  double d,  double fcu,  double As,  double x,  double c_min,  int dia_l,  double Es,  double a_cr,  double fcc,  double fst,  double a_pi,  double e_1,  double e_m,  double crackWidth)?  $default,) {final _that = this;
switch (_that) {
case _CrackWidth() when $default != null:
return $default(_that.M,_that.h,_that.b,_that.d,_that.fcu,_that.As,_that.x,_that.c_min,_that.dia_l,_that.Es,_that.a_cr,_that.fcc,_that.fst,_that.a_pi,_that.e_1,_that.e_m,_that.crackWidth);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _CrackWidth extends CrackWidth {
   _CrackWidth({this.M = 0.0, this.h = 0.0, this.b = 0.0, this.d = 0.0, this.fcu = 0.0, this.As = 0.0, this.x = 0.0, this.c_min = 0.0, this.dia_l = 0, this.Es = 200000.0, this.a_cr = 0.0, this.fcc = 0.0, this.fst = 0.0, this.a_pi = 0.0, this.e_1 = 0.0, this.e_m = 0.0, this.crackWidth = 0.0}): super._();
  factory _CrackWidth.fromJson(Map<String, dynamic> json) => _$CrackWidthFromJson(json);

@override@JsonKey() final  double M;
@override@JsonKey() final  double h;
@override@JsonKey() final  double b;
@override@JsonKey() final  double d;
@override@JsonKey() final  double fcu;
@override@JsonKey() final  double As;
@override@JsonKey() final  double x;
@override@JsonKey() final  double c_min;
@override@JsonKey() final  int dia_l;
@override@JsonKey() final  double Es;
@override@JsonKey() final  double a_cr;
@override@JsonKey() final  double fcc;
@override@JsonKey() final  double fst;
@override@JsonKey() final  double a_pi;
@override@JsonKey() final  double e_1;
@override@JsonKey() final  double e_m;
@override@JsonKey() final  double crackWidth;

/// Create a copy of CrackWidth
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$CrackWidthCopyWith<_CrackWidth> get copyWith => __$CrackWidthCopyWithImpl<_CrackWidth>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$CrackWidthToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _CrackWidth&&(identical(other.M, M) || other.M == M)&&(identical(other.h, h) || other.h == h)&&(identical(other.b, b) || other.b == b)&&(identical(other.d, d) || other.d == d)&&(identical(other.fcu, fcu) || other.fcu == fcu)&&(identical(other.As, As) || other.As == As)&&(identical(other.x, x) || other.x == x)&&(identical(other.c_min, c_min) || other.c_min == c_min)&&(identical(other.dia_l, dia_l) || other.dia_l == dia_l)&&(identical(other.Es, Es) || other.Es == Es)&&(identical(other.a_cr, a_cr) || other.a_cr == a_cr)&&(identical(other.fcc, fcc) || other.fcc == fcc)&&(identical(other.fst, fst) || other.fst == fst)&&(identical(other.a_pi, a_pi) || other.a_pi == a_pi)&&(identical(other.e_1, e_1) || other.e_1 == e_1)&&(identical(other.e_m, e_m) || other.e_m == e_m)&&(identical(other.crackWidth, crackWidth) || other.crackWidth == crackWidth));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,M,h,b,d,fcu,As,x,c_min,dia_l,Es,a_cr,fcc,fst,a_pi,e_1,e_m,crackWidth);

@override
String toString() {
  return 'CrackWidth(M: $M, h: $h, b: $b, d: $d, fcu: $fcu, As: $As, x: $x, c_min: $c_min, dia_l: $dia_l, Es: $Es, a_cr: $a_cr, fcc: $fcc, fst: $fst, a_pi: $a_pi, e_1: $e_1, e_m: $e_m, crackWidth: $crackWidth)';
}


}

/// @nodoc
abstract mixin class _$CrackWidthCopyWith<$Res> implements $CrackWidthCopyWith<$Res> {
  factory _$CrackWidthCopyWith(_CrackWidth value, $Res Function(_CrackWidth) _then) = __$CrackWidthCopyWithImpl;
@override @useResult
$Res call({
 double M, double h, double b, double d, double fcu, double As, double x, double c_min, int dia_l, double Es, double a_cr, double fcc, double fst, double a_pi, double e_1, double e_m, double crackWidth
});




}
/// @nodoc
class __$CrackWidthCopyWithImpl<$Res>
    implements _$CrackWidthCopyWith<$Res> {
  __$CrackWidthCopyWithImpl(this._self, this._then);

  final _CrackWidth _self;
  final $Res Function(_CrackWidth) _then;

/// Create a copy of CrackWidth
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? M = null,Object? h = null,Object? b = null,Object? d = null,Object? fcu = null,Object? As = null,Object? x = null,Object? c_min = null,Object? dia_l = null,Object? Es = null,Object? a_cr = null,Object? fcc = null,Object? fst = null,Object? a_pi = null,Object? e_1 = null,Object? e_m = null,Object? crackWidth = null,}) {
  return _then(_CrackWidth(
M: null == M ? _self.M : M // ignore: cast_nullable_to_non_nullable
as double,h: null == h ? _self.h : h // ignore: cast_nullable_to_non_nullable
as double,b: null == b ? _self.b : b // ignore: cast_nullable_to_non_nullable
as double,d: null == d ? _self.d : d // ignore: cast_nullable_to_non_nullable
as double,fcu: null == fcu ? _self.fcu : fcu // ignore: cast_nullable_to_non_nullable
as double,As: null == As ? _self.As : As // ignore: cast_nullable_to_non_nullable
as double,x: null == x ? _self.x : x // ignore: cast_nullable_to_non_nullable
as double,c_min: null == c_min ? _self.c_min : c_min // ignore: cast_nullable_to_non_nullable
as double,dia_l: null == dia_l ? _self.dia_l : dia_l // ignore: cast_nullable_to_non_nullable
as int,Es: null == Es ? _self.Es : Es // ignore: cast_nullable_to_non_nullable
as double,a_cr: null == a_cr ? _self.a_cr : a_cr // ignore: cast_nullable_to_non_nullable
as double,fcc: null == fcc ? _self.fcc : fcc // ignore: cast_nullable_to_non_nullable
as double,fst: null == fst ? _self.fst : fst // ignore: cast_nullable_to_non_nullable
as double,a_pi: null == a_pi ? _self.a_pi : a_pi // ignore: cast_nullable_to_non_nullable
as double,e_1: null == e_1 ? _self.e_1 : e_1 // ignore: cast_nullable_to_non_nullable
as double,e_m: null == e_m ? _self.e_m : e_m // ignore: cast_nullable_to_non_nullable
as double,crackWidth: null == crackWidth ? _self.crackWidth : crackWidth // ignore: cast_nullable_to_non_nullable
as double,
  ));
}


}

// dart format on
