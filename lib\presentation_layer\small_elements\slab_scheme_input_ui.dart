import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

//presentation layer
// import '../controllers/slab_scheme_data_controller.dart';
import 'button/function_button.dart';
import 'input/custom_stateful_double_input.dart';
import 'input/custom_stateful_int_input.dart';
import '../screen/homescreen.dart';
import 'input/custom_stateful_dropList.dart';

//domain layer
import '../../domain_layer/preferences.dart';

class SlabSchemeInputUi extends ConsumerStatefulWidget {
  const SlabSchemeInputUi({
    this.isExpanded,
    this.maxHeight,
    this.minHeight,
    super.key,
  });

  final bool? isExpanded;
  final double? maxHeight;
  final double? minHeight;

  @override
  ConsumerState<ConsumerStatefulWidget> createState() =>
      _SlabSchemeInputState();
}

class _SlabSchemeInputState extends ConsumerState<SlabSchemeInputUi>
    with WidgetsBindingObserver {
  late bool _isExpanded;
  late double _maxHeight;
  late double _minHeight;
  late ScrollController _controller;

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _controller.dispose();
    super.dispose();
  }

  @override
  void initState() {
    WidgetsBinding.instance.addObserver(this);
    _isExpanded = widget.isExpanded ?? false;
    _maxHeight = widget.maxHeight ?? 210;
    _minHeight = widget.minHeight ?? 50;
    _controller = ScrollController();
    // _updateHeights();

    super.initState();
  }

  @override
  void didChangeDependencies() {
    _updateHeights();
    super.didChangeDependencies();
    // Update heights when dependencies change, including after initState
  }

  @override
  void didChangeMetrics() {
    // This method is called when the metrics change (like resizing the window)
    setState(() {
      _updateHeights();
    });
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;
    final globalData = ref.watch(globalDataControllerProvider);
    final loadingTables = ref.watch(loadingTablesControllerProvider);
    final slabSchemeInput = ref.watch(slabSchemeInputControllerProvider);
    // final slabSchemeData = ref.watch(slabSchemeDataControllerProvider);
    return loadingTables.when(
      data: (tables) {
        return globalData.when(
          data: (data) {
            late final List<String> preferredUnit;
            switch (data.unit) {
              case 'metrics':
                preferredUnit = PreferredUnit.metrics;
                break;
              case 'imperial':
                preferredUnit = PreferredUnit.imperial;
                break;
              default:
                preferredUnit = PreferredUnit.metrics;
                break;
            }
            return slabSchemeInput.when(
              data: (input) {
                final int containerOpacity = 175;
                final int textOpacity = 225;
                return Column(
                  children: [
                    Padding(
                      padding: const EdgeInsets.fromLTRB(10, 0, 10, 0),
                      child: Divider(),
                    ),
                    Padding(
                      padding: const EdgeInsets.fromLTRB(10, 0, 0, 0),
                      child: Align(
                        alignment: Alignment.centerLeft,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Wrap(
                              crossAxisAlignment: WrapCrossAlignment.center,
                              children: [
                                IconButton(
                                  icon: Icon(
                                    _isExpanded
                                        ? Icons.expand_less
                                        : Icons.expand_more,
                                  ),
                                  color: colorScheme.onSurface,
                                  onPressed: () {
                                    setState(() {
                                      _isExpanded = !_isExpanded;
                                    });
                                  },
                                ),
                                Text(
                                  'Slab Scheme Input',
                                  style: textTheme.titleLarge!.copyWith(
                                    color: colorScheme.onSurface,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.fromLTRB(10, 0, 10, 0),
                      child: Divider(),
                    ),
                    ClipRect(
                      child: ConstrainedBox(
                        constraints: BoxConstraints(
                          maxHeight: _isExpanded ? _maxHeight : 0,
                        ),
                        child: Scrollbar(
                          controller: _controller,
                          thumbVisibility: true,
                          trackVisibility: false,
                          child: SingleChildScrollView(
                            controller: _controller,
                            child: Column(
                              mainAxisSize: MainAxisSize.min,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Padding(
                                  padding: const EdgeInsets.fromLTRB(
                                    8.0,
                                    0.0,
                                    8.0,
                                    0.0,
                                  ),
                                  child: Row(
                                    children: [
                                      Expanded(
                                        flex: 3,
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,

                                          children: [
                                            SizedBox(height: 5.0),
                                            Container(
                                              decoration: BoxDecoration(
                                                borderRadius:
                                                    BorderRadius.circular(10.0),
                                                border: Border.all(
                                                  color: Colors.grey.withAlpha(
                                                    150,
                                                  ),
                                                ),
                                                color: colorScheme
                                                    .secondaryContainer
                                                    .withAlpha(
                                                      containerOpacity,
                                                    ),
                                              ),
                                              child: Padding(
                                                padding: const EdgeInsets.all(
                                                  4.0,
                                                ),
                                                child: Text(
                                                  'Iteration',
                                                  style: textTheme.titleSmall!
                                                      .copyWith(
                                                        color: colorScheme
                                                            .onSecondaryContainer
                                                            .withAlpha(
                                                              textOpacity,
                                                            ),
                                                      ),
                                                ),
                                              ),
                                            ),
                                            SizedBox(height: 10.0),
                                            Row(
                                              children: [
                                                Flexible(
                                                  child: CustomStatefulDoubleInput(
                                                    title:
                                                        'Initial Span [${preferredUnit[3]}]',
                                                    value: input.span,
                                                    onChanged: (value) async {
                                                      await ref
                                                          .read(
                                                            slabSchemeInputControllerProvider
                                                                .notifier,
                                                          )
                                                          .updateTable(
                                                            span: value,
                                                          );
                                                    },
                                                  ),
                                                ),
                                                SizedBox(width: 5.0),
                                                Flexible(
                                                  child: CustomStatefulDoubleInput(
                                                    title:
                                                        'Delta Span [${preferredUnit[3]}]',
                                                    value: input.spanIncreament,
                                                    tooltipText:
                                                        'Change in span length\n'
                                                        'in each iteration step',
                                                    onChanged: (value) {
                                                      ref
                                                          .read(
                                                            slabSchemeInputControllerProvider
                                                                .notifier,
                                                          )
                                                          .updateTable(
                                                            spanIncreament:
                                                                value,
                                                          );
                                                    },
                                                  ),
                                                ),
                                                SizedBox(width: 5.0),
                                                Flexible(
                                                  child: CustomStatefulIntInput(
                                                    title: 'Steps',
                                                    value: input.iterationSteps,
                                                    onChanged: (value) {
                                                      ref
                                                          .read(
                                                            slabSchemeInputControllerProvider
                                                                .notifier,
                                                          )
                                                          .updateTable(
                                                            iterationSteps:
                                                                value,
                                                          );
                                                    },
                                                  ),
                                                ),
                                                // Flexible(
                                                //   child: SizedBox(width: 5.0),
                                                // ),
                                              ],
                                            ),
                                          ],
                                        ),
                                      ),
                                      SizedBox(width: 5.0),
                                      Expanded(
                                        flex: 1,
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,

                                          children: [
                                            SizedBox(height: 5.0),
                                            Container(
                                              decoration: BoxDecoration(
                                                borderRadius:
                                                    BorderRadius.circular(10.0),
                                                border: Border.all(
                                                  color: Colors.grey.withAlpha(
                                                    150,
                                                  ),
                                                ),
                                                color: colorScheme
                                                    .secondaryContainer
                                                    .withAlpha(
                                                      containerOpacity,
                                                    ),
                                              ),
                                              child: Padding(
                                                padding: const EdgeInsets.all(
                                                  4.0,
                                                ),
                                                child: Text(
                                                  'Other',
                                                  style: textTheme.titleSmall!
                                                      .copyWith(
                                                        color: colorScheme
                                                            .onSecondaryContainer
                                                            .withAlpha(
                                                              textOpacity,
                                                            ),
                                                      ),
                                                ),
                                              ),
                                            ),
                                            SizedBox(height: 10.0),
                                            Row(
                                              children: [
                                                Flexible(
                                                  child: CustomStatefulDoubleInput(
                                                    titleStyle: textTheme
                                                        .labelLarge!
                                                        .copyWith(
                                                          color: colorScheme
                                                              .error
                                                              .withAlpha(200),
                                                        ),
                                                    title: 'Preferred k-value',
                                                    value: input.mainKValue,
                                                    tooltipText:
                                                        'design will be as close to this k-value\n'
                                                        'as possible while considering the code limit as well',
                                                    onChanged: (value) async{
                                                      await ref
                                                          .read(
                                                            slabSchemeInputControllerProvider
                                                                .notifier,
                                                          )
                                                          .updateTable(
                                                            mainKValue: value,
                                                          );
                                                    },
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                Padding(
                                  padding: const EdgeInsets.fromLTRB(
                                    8.0,
                                    0.0,
                                    8.0,
                                    0.0,
                                  ),
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,

                                    children: [
                                      SizedBox(height: 5.0),
                                      Container(
                                        decoration: BoxDecoration(
                                          borderRadius: BorderRadius.circular(
                                            10.0,
                                          ),
                                          border: Border.all(
                                            color: Colors.grey.withAlpha(150),
                                          ),
                                          color: colorScheme.secondaryContainer
                                              .withAlpha(containerOpacity),
                                        ),
                                        child: Padding(
                                          padding: const EdgeInsets.all(4.0),
                                          child: Text(
                                            'Limit',
                                            style: textTheme.titleSmall!
                                                .copyWith(
                                                  color: colorScheme
                                                      .onSecondaryContainer
                                                      .withAlpha(textOpacity),
                                                ),
                                          ),
                                        ),
                                      ),
                                      SizedBox(height: 10.0),
                                      Row(
                                        children: [
                                          Flexible(
                                            child: CustomStatefulIntInput(
                                              titleStyle: textTheme.labelLarge!
                                                  .copyWith(
                                                    color: colorScheme.error
                                                        .withAlpha(200),
                                                  ),
                                              title:
                                                  'Min Steel Spacing [${preferredUnit[4]}]',
                                              value: input.minS,
                                              onChanged: (value) async {
                                                await ref
                                                    .read(
                                                      slabSchemeInputControllerProvider
                                                          .notifier,
                                                    )
                                                    .updateTable(minS: value);
                                              },
                                            ),
                                          ),
                                          SizedBox(width: 5.0),
                                          Flexible(
                                            child: CustomStatefulIntInput(
                                              titleStyle: textTheme.labelLarge!
                                                  .copyWith(
                                                    color: colorScheme.error
                                                        .withAlpha(200),
                                                  ),
                                              title:
                                                  'Max Steel Spacing [${preferredUnit[4]}]',
                                              value: input.maxS,
                                              onChanged: (value) async {
                                                 await ref
                                                    .read(
                                                      slabSchemeInputControllerProvider
                                                          .notifier,
                                                    )
                                                    .updateTable(maxS: value);
                                              },
                                            ),
                                          ),

                                          SizedBox(width: 5.0),
                                          Flexible(
                                            child: CustomStatefulDoubleInput(
                                              titleStyle: textTheme.labelLarge!
                                                  .copyWith(
                                                    color: colorScheme.error
                                                        .withAlpha(200),
                                                  ),
                                              title:
                                                  'Min Slab Depth [${preferredUnit[4]}]',
                                              value: input.minDepth,
                                              onChanged: (value)  async{
                                                await ref
                                                    .read(
                                                      slabSchemeInputControllerProvider
                                                          .notifier,
                                                    )
                                                    .updateTable(
                                                      minDepth: value,
                                                    );
                                              },
                                            ),
                                          ),
                                          SizedBox(width: 5.0),
                                          Flexible(
                                            child: CustomStatefulDoubleInput(
                                              titleStyle: textTheme.labelLarge!
                                                  .copyWith(
                                                    color: colorScheme.error
                                                        .withAlpha(200),
                                                  ),
                                              title:
                                                  'Max Slab Depth [${preferredUnit[4]}]',
                                              value: input.maxDepth,
                                              onChanged: (value)  async{
                                                await ref
                                                    .read(
                                                      slabSchemeInputControllerProvider
                                                          .notifier,
                                                    )
                                                    .updateTable(
                                                      maxDepth: value,
                                                    );
                                              },
                                            ),
                                          ),
                                          SizedBox(width: 5.0),
                                          Flexible(
                                            child: CustomStatefulIntInput(
                                              titleStyle: textTheme.labelLarge!
                                                  .copyWith(
                                                    color: colorScheme.error
                                                        .withAlpha(200),
                                                  ),
                                              title: 'Max Rebar Layer',
                                              value: input.maxLayers,
                                              onChanged: (value)  async{
                                                await ref
                                                    .read(
                                                      slabSchemeInputControllerProvider
                                                          .notifier,
                                                    )
                                                    .updateTable(
                                                      maxLayers: value,
                                                    );
                                              },
                                            ),
                                          ),
                                        ],
                                      ),
                                    ],
                                  ),
                                ),
                                Padding(
                                  padding: const EdgeInsets.fromLTRB(
                                    8.0,
                                    0.0,
                                    8.0,
                                    0.0,
                                  ),
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      SizedBox(height: 5.0),
                                      Container(
                                        decoration: BoxDecoration(
                                          borderRadius: BorderRadius.circular(
                                            10.0,
                                          ),
                                          border: Border.all(
                                            color: Colors.grey.withAlpha(150),
                                          ),
                                          color: colorScheme.secondaryContainer
                                              .withAlpha(containerOpacity),
                                        ),
                                        child: Padding(
                                          padding: const EdgeInsets.all(4.0),
                                          child: Text(
                                            'Other Info',
                                            style: textTheme.titleSmall!
                                                .copyWith(
                                                  color: colorScheme
                                                      .onSecondaryContainer
                                                      .withAlpha(textOpacity),
                                                ),
                                          ),
                                        ),
                                      ),
                                      // SizedBox(height: 10.0),
                                      Row(
                                        mainAxisSize: MainAxisSize.min,
                                        mainAxisAlignment:
                                            MainAxisAlignment.start,
                                        children: [
                                          Flexible(
                                            child: CustomStatefulDropDown(
                                              items:
                                                  tables
                                                      .map((e) => e.usage)
                                                      .toList(),
                                              selectedValue:
                                                  input.usage == ''
                                                      ? null
                                                      : input.usage,
                                              onTap: (selectedValue) {
                                                ref
                                                    .read(
                                                      slabSchemeInputControllerProvider
                                                          .notifier,
                                                    )
                                                    .updateTable(
                                                      usage: selectedValue,
                                                    );
                                              },
                                            ),
                                          ),
                                          SizedBox(width: 5.0),
                                          FunctionButton(
                                            labelIcon: Icon(
                                              Icons.calculate_outlined,
                                            ),
                                            labelText: 'Run',
                                            onTap: (isPressed) async {
                                              await ref
                                                  .read(
                                                    slabSchemeDataControllerProvider
                                                        .notifier,
                                                  )
                                                  .batchSlabScheming();
                                            },
                                          ),
                                          SizedBox(width: 5.0),
                                          FunctionButton(
                                            labelIcon: Icon(
                                              Icons.delete_outlined,
                                            ),
                                            labelText: 'Clear Schemes',
                                            onTap: (isPressed) async {
                                              await ref
                                                  .read(
                                                    slabSchemeDataControllerProvider
                                                        .notifier,
                                                  )
                                                  .deleteAllTable();
                                            },
                                          ),
                                        ],
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                );
              },
              error: (error, stackTrace) => Text(error.toString()),
              loading: () => const CircularProgressIndicator(),
            );
          },
          error: (error, stackTrace) => Text(error.toString()),
          loading: () => Center(child: CircularProgressIndicator()),
        );
      },
      error: (error, stackTrace) => Text(error.toString()),
      loading: () => Center(child: CircularProgressIndicator()),
    );
  }

  void _updateHeights() {
    final double screenHeight = MediaQuery.of(context).size.height;
    _maxHeight =
        (widget.maxHeight == null)
            ? screenHeight * 0.65
            : (widget.maxHeight! > screenHeight * 0.65)
            ? screenHeight * 0.65
            : widget.maxHeight!;

    _minHeight =
        (widget.minHeight == null)
            ? screenHeight * 0.1
            : (widget.minHeight! > screenHeight * 0.1)
            ? screenHeight * 0.1
            : widget.minHeight!;

    // Adjust _maxHeight if needed

    if (_maxHeight < _minHeight) {
      _maxHeight = _minHeight;
    }
  }
}
