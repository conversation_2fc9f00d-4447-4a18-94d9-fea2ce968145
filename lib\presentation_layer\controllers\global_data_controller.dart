import 'package:riverpod_annotation/riverpod_annotation.dart';

//presentation layer
import '../screen/homescreen.dart';

// domain layer
import '../../domain_layer/global_data.dart';

part 'global_data_controller.g.dart';

@riverpod
class GlobalDataController extends _$GlobalDataController {
  @override
  FutureOr<GlobalData> build() async {
    // print('Build: Global Data');
    final globalData =
        await ref
            .read(appDatabaseControllerProvider.notifier)
            .queryGlobalData();
    return globalData;
  }

  Future<double> getsdlFactor() async {
    final x = await future;
    return x.sdlFactor;
  }

  Future<void> updateGlobalData({
    double? sdlFactor,
    double? llFactor,
    String? unit,
    double? rcUnitWeight,
    double? finishUnitWeight,
    double? steelUnitWeight,
    double? soilUnitWeight,
    double? waterUnitWeight,
    bool? showGlobalDataUiInSubPage,
  }) async {
    final x = await future;
    state = AsyncLoading();
    final newState = x.copyWith(
      sdlFactor: sdlFactor ?? x.sdlFactor,
      llFactor: llFactor ?? x.llFactor,
      unit: unit ?? x.unit,
      rcUnitWeight: rcUnitWeight ?? x.rcUnitWeight,
      finishUnitWeight: finishUnitWeight ?? x.finishUnitWeight,
      steelUnitWeight: steelUnitWeight ?? x.steelUnitWeight,
      soilUnitWeight: soilUnitWeight ?? x.soilUnitWeight,
      waterUnitWeight: waterUnitWeight ?? x.waterUnitWeight,
      // showGlobalDataUiInSubPage:
      //     showGlobalDataUiInSubPage ?? x.showGlobalDataUiInSubPage,
    );
    state = AsyncData(newState);
  }
}
