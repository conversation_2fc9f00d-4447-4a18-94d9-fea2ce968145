import 'package:freezed_annotation/freezed_annotation.dart';
part 'recommend_load.freezed.dart';
part 'recommend_load.g.dart';

@freezed
abstract class RecommendedLoad with _$RecommendedLoad {
  const RecommendedLoad._();
  factory RecommendedLoad({
    @Default('') String usage,
    @Default(50.0) double finish,//[mm]
    @Default(1.0) double service, //[kPa]
    @Default(0.0) double partitionLoad, //[kPa]
    @Default(0.0) double sdl, //[kPa]
    @Default(0.0) double ll, //[kPa]
    @Default(0.0) double frp, //[hr]
  }) = _RecommendedLoad; 

  factory RecommendedLoad.fromJson(Map<String, Object?> json) =>
      _$RecommendedLoadFromJson(json);
}
