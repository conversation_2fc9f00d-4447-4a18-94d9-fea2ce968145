import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:structify/data_layer/app_database.dart';
import 'package:drift_db_viewer/drift_db_viewer.dart';

//below for printing
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:printing/printing.dart';
import 'package:structify/presentation_layer/small_elements/dessign_assumption.dart';
// import 'dart:typed_data';
// import 'dart:ui';
import 'package:structify/presentation_layer/small_elements/slab_scheme_input_ui.dart';
import 'package:structify/presentation_layer/small_elements/slab_scheme_summary.dart';
// import 'package:structify/presentation_layer/small_elements/loadcals_summary_ui.dart';

// import 'package:flutter/services.dart';

//domain layer
import '../../domain_layer/preferences.dart';

// presentation layer
// import '../presentation_layer/homescreen.dart';
import '../../misc/custom_func.dart';
import '../small_elements/button/function_button.dart';
import 'homescreen.dart';
// import '../small_elements/custom_stateful_double_input.dart';
// import '../small_elements/custom_stateful_text_input.dart';
// import '../small_elements/loadcals_summary_ui.dart';

class SlabSchemeCalculator extends ConsumerWidget {
  SlabSchemeCalculator({super.key});

  final GlobalKey _globalKey = GlobalKey();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final colorScheme = Theme.of(context).colorScheme;
    final TextTheme textTheme = Theme.of(context).textTheme;
    final globalData = ref.watch(globalDataControllerProvider);
    ref.watch(appWatcherProvider);
    return globalData.when(
      data: (data) {
        return Scaffold(
          backgroundColor: colorScheme.surface,
          body: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Padding(
                padding: const EdgeInsets.all(8.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    FunctionButton(
                      labelIcon: Icon(Icons.print_outlined),
                      labelText: 'Assumption',
                      onTap: (isPressed) async {
                        showAssumption(
                          context,
                          DesignAssumption(
                            isExpanded: true,
                            titleStyle: textTheme.titleLarge!.copyWith(
                              color: colorScheme.onInverseSurface,
                            ),
                            textStyle: textTheme.bodyMedium!.copyWith(
                              color: colorScheme.onInverseSurface,
                            ),
                            title: 'Assumption',
                            assumptions: [
                              'HKCoP for Structural Use of Concrete 2013 (HKCoPSUC2013)',
                              'fy = 500 MPa',
                              'Flexural design: cl.*******(c) | k-limit: <10% moment redistribution',
                              'Shear design: cl.******* | vc: table 6.3, notes 1 to 3',
                              'Deflection Limit: L/250 (simply-supoorted beam)',
                              'effective d = str zone - cover - links dia. - (highest layer bar dia. + (layersNos - 1 )*(T40 dia + spacer dia.))/2',
                              'spacer dia. = T16 dia.',
                            ],
                            tooltipText:
                                '------if flexural fails------\n-adjust preferred k-value OR\n-adjust max slab depth OR\n-adjust max rebar Layer OR\n-adjust concrete grade\n'
                                '------if shear fails------\n-adjust the min links spacing OR\n-adjust max slab depth\n'
                                '------If deflection fails------\n-use pre-chamber OR\n-add compression bar in detailed design stage OR\n-check actual deflection in detailed design stage OR\n-consider pre-stressed slab in detail design stage',
                          ),
                        );
                      },
                    ),
                    SizedBox(width: 5.0),
                    FunctionButton(
                      labelIcon: Icon(Icons.print_outlined),
                      labelText: 'Summary',
                      onTap: (isPressed) async {
                        showSummary(context);
                      },
                    ),
                    SizedBox(width: 5.0),
                    FunctionButton(
                      labelIcon: Icon(Icons.print_outlined),
                      labelText: 'Print',

                      onTap: (isPressed) async {
                        await _exportListToPdf(context, ref, rowsPerPage: 9);
                      },
                    ),
                    SizedBox(width: 5.0),
                    FunctionButton(
                      labelIcon: Icon(Icons.data_array_outlined),
                      labelText: 'Data',

                      onTap: (isPressed) async {
                        final db = AppDatabase(); //This should be a singleton
                        Navigator.of(context).push(
                          MaterialPageRoute(
                            builder: (context) => DriftDbViewer(db),
                          ),
                        );
                      },
                    ),
                  ],
                ),
              ),

              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Flexible(child: SlabSchemeInputUi(isExpanded: true)),
                  Flexible(child: SlabSchemeSummary(isExpanded: true)),
                ],
              ),
            ],
          ),
        );
      },
      error: (error, stackTrace) {
        return Text('Error: $error');
      },
      loading: () {
        return CircularProgressIndicator();
      },
    );
  }

  Future<void> _exportListToPdf(
    BuildContext context,
    WidgetRef ref, {
    int rowsPerPage = 10,
  }) async {
    final pdf = pw.Document();
    final schemes = ref.read(slabSchemeDataControllerProvider);
    schemes.when(
      data: (data) {
        int counter = 0;
        do {
          if (counter + rowsPerPage <= data.length) {
            pdf.addPage(
              _customPage(
                context,
                ref,
                startFrom: counter,
                rowsPerPage: rowsPerPage,
                contentScale: 0.85,
              ),
            );
          } else {
            pdf.addPage(
              _customPage(
                context,
                ref,
                startFrom: counter,
                rowsPerPage: data.length - counter,
                contentScale: 0.85,
              ),
            );
          }
          counter += rowsPerPage;
        } while (counter <= data.length);
      },
      error: (error, stackTrace) => {},
      loading: () {},
    );

    await Printing.layoutPdf(
      onLayout: (PdfPageFormat format) async => pdf.save(),
    );
  }

  pw.Widget _customVerticalDivider() {
    return pw.Row(
      children: [
        pw.SizedBox(width: 15.0),
        pw.Container(width: 1.0, height: 30, color: PdfColors.grey400),
        pw.SizedBox(width: 15.0),
      ],
    );
  }

  pw.Widget _customDivider() {
    return pw.Divider(color: PdfColors.grey400);
  }

  pw.Page _customPage(
    BuildContext context,
    WidgetRef ref, {
    int startFrom = 0,
    int? rowsPerPage,
    double contentScale = 1.0,
  }) {
    final textStyle = Theme.of(context).textTheme.labelSmall;
    return pw.Page(
      margin: pw.EdgeInsets.fromLTRB(25, 35, 25, 35),
      pageFormat: PdfPageFormat.a4,
      orientation: pw.PageOrientation.portrait,
      build: (pw.Context context) {
        final schemes = ref.read(slabSchemeDataControllerProvider);
        final globaldata = ref.read(globalDataControllerProvider);

        return schemes.when(
          data: (tables) {
            return globaldata.when(
              data: (data) {
                return pw.Center(
                  child: pw.Column(
                    mainAxisAlignment: pw.MainAxisAlignment.start,
                    children: [
                      pw.Align(
                        alignment: pw.Alignment.center,
                        child: pw.Text(
                          'RC Slab Schemes Summary',
                          style: pw.TextStyle(
                            fontSize: 20,
                            fontWeight: pw.FontWeight.bold,
                          ),
                        ),
                      ),
                      pw.SizedBox(height: 10.0),
                      ...List.generate(rowsPerPage ?? tables.length - startFrom, (
                        index,
                      ) {
                        final bool slabStatusBool =
                            tables[index].mainTopBar != 'Fail' ||
                            tables[index].mainBottomBar != 'Fail' ||
                            tables[index].mainLinks != 'Fail';

                        final double finish = tables[index + startFrom].finish;
                        final double sdl =
                            tables[index + startFrom].service +
                            tables[index + startFrom].finish *
                                data.finishUnitWeight /
                                1000;
                        final double ll = tables[index + startFrom].liveLoad;
                        late List<String> unit;
                        switch (data.unit) {
                          case 'metrics':
                            unit = PreferredUnit.metrics;
                            break;
                          case 'imperial':
                            unit = PreferredUnit.imperial;
                            break;
                          default:
                            unit = PreferredUnit.metrics;
                        }
                        return pw.Transform.scale(
                          scale: contentScale,
                          child: pw.DefaultTextStyle(
                            style: pw.TextStyle(fontSize: textStyle!.fontSize),
                            child: pw.Column(
                              children: [
                                pw.Row(
                                  children: [
                                    pw.Text(
                                      '${index + startFrom + 1}',
                                      style: pw.TextStyle(
                                        fontSize: textStyle.fontSize,
                                        fontWeight: pw.FontWeight.bold,
                                      ),
                                    ),

                                    _customVerticalDivider(),

                                    pw.Column(
                                      children: [
                                        pw.Text(
                                          tables[index + startFrom].usage,
                                          style: pw.TextStyle(
                                            fontSize: textStyle.fontSize,
                                            fontWeight: pw.FontWeight.bold,
                                          ),
                                        ),
                                        pw.Builder(
                                          builder: (context) {
                                            if (slabStatusBool) {
                                              return pw.Text(
                                                'Slab: Pass',
                                                style: pw.TextStyle(
                                                  fontWeight:
                                                      pw.FontWeight.bold,
                                                  color: PdfColors.green,
                                                ),
                                              );
                                            } else {
                                              return pw.Text(
                                                'Slab: Fail',
                                                style: pw.TextStyle(
                                                  fontWeight:
                                                      pw.FontWeight.bold,
                                                  color: PdfColors.red,
                                                ),
                                              );
                                            }
                                          },
                                        ),
                                      ],
                                    ),

                                    _customVerticalDivider(),

                                    pw.Column(
                                      crossAxisAlignment:
                                          pw.CrossAxisAlignment.start,

                                      children: [
                                        pw.Text('Finish: '),
                                        pw.Text('SDL: '),
                                        pw.Text('LL: '),
                                      ],
                                    ),
                                    pw.SizedBox(width: 10.0),

                                    pw.Column(
                                      crossAxisAlignment:
                                          pw.CrossAxisAlignment.start,

                                      children: [
                                        pw.Text(
                                          '${NumberFormat('0.0').format(finish)} [${unit[1]}]',
                                        ),
                                        pw.Text(
                                          '${NumberFormat('0.0').format(sdl)} [${unit[1]}]',
                                        ),
                                        pw.Text(
                                          '${NumberFormat('0.0').format(ll)} [${unit[1]}]',
                                        ),
                                      ],
                                    ),

                                    _customVerticalDivider(),

                                    pw.Column(
                                      crossAxisAlignment:
                                          pw.CrossAxisAlignment.start,
                                      children: [
                                        pw.Text('Span: '),
                                        pw.Text('Slab Thickness: '),
                                        pw.Text('Bottom Bar (Tension): '),
                                        pw.Text('Top Bar (Compression): '),
                                        pw.Text('Links: '),
                                      ],
                                    ),
                                    pw.SizedBox(width: 10.0),
                                    pw.Column(
                                      crossAxisAlignment:
                                          pw.CrossAxisAlignment.start,
                                      children: [
                                        pw.Text(
                                          '${NumberFormat('0.0').format(tables[index + startFrom].span)} [${unit[3]}]',
                                        ),
                                        pw.Text(
                                          '${NumberFormat('0').format(tables[index + startFrom].strZone)} [${unit[4]}]',
                                        ),
                                        pw.Text(
                                          '${tables[index + startFrom].mainBottomBar} [${unit[6]}/${unit[4]}]',
                                        ),
                                        pw.Text(
                                          '${tables[index + startFrom].mainTopBar} [${unit[6]}/${unit[4]}]',
                                        ),
                                        pw.Text(
                                          '${tables[index + startFrom].mainLinks} [${unit[6]}/${unit[4]}]',
                                        ),
                                      ],
                                    ),
                                  ],
                                ),
                                _customDivider(),
                              ],
                            ),
                          ),
                        );
                      }),
                    ],
                  ),
                );
              },
              error: (error, stacktrace) => pw.Text('Error: $error'),
              loading: () => pw.Text('Loading'),
            );
          },
          error: (error, stackTrace) => pw.Text('Error: $error'),
          loading: () => pw.Text('loading'),
        );
      },
    );
  }
}
