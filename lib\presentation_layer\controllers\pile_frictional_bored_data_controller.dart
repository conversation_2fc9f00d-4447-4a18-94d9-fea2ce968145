import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:nanoid2/nanoid2.dart';
import 'dart:math';
import 'package:intl/intl.dart';

//presentation layer
import '../../domain_layer/mixin/mixin_rc_str.dart';
import '../../domain_layer/pile_frictional_bored_data.dart';
import '../screen/homescreen.dart';

// domain layer
import '../../domain_layer/global_data.dart';
import '../../domain_layer/preferences.dart';
import 'package:structify/domain_layer/pile_frictional_bored_input.dart';

part 'pile_frictional_bored_data_controller.g.dart';

@riverpod
class PileFrictionalBoredDataController
    extends _$PileFrictionalBoredDataController
    with RCStrHK {
  @override
  FutureOr<List<PileFrictionalBoredData>> build() async {
    // print('Build: Column Scheme Data');
    final pileFrictionalBoredDataList =
        await ref
            .read(appDatabaseControllerProvider.notifier)
            .queryPileFrictionalBoredData();

    final data1 = ref.watch(pileFrictionalBoredInputControllerProvider);
    return data1.when(
      data: (pileInput) async {
        return await pileFrictionalBoredScheming(
          existingSchemes: pileFrictionalBoredDataList,
          calledInBuild: true,
        );
      },
      error: (error, stackTrace) => <PileFrictionalBoredData>[],
      loading: () => <PileFrictionalBoredData>[],
    );
  }

  Future<void> addSchemeData(
    List<PileFrictionalBoredData> pileFrictionalBoredData,
  ) async {
    final x = await future;
    state = AsyncData([...x, ...pileFrictionalBoredData]);
  }

  Future<void> deleteTable(String id) async {
    final x = await future;
    x.removeWhere((item) => item.pileFrictionalBoredSchemeId == id);
    state = AsyncData(x);
  }

  Future<List<PileFrictionalBoredData>> deleteTablesNotSelected({
    List<PileFrictionalBoredData>? existingScheme,
    bool? calledInBuild,
  }) async {
    late final List<PileFrictionalBoredData> schemes;
    if (existingScheme != null && existingScheme.isNotEmpty) {
      schemes = existingScheme;
    } else {
      if (calledInBuild != null && calledInBuild) {
        schemes = [];
      } else {
        schemes = await future;
      }
    }
    schemes.removeWhere((item) => !item.isSelected);
    state = AsyncData(schemes);
    return schemes;
  }

  Future<void> deleteAllTable() async {
    state = AsyncData([]);
  }

  Future<void> replaceEntireTable(
    List<PileFrictionalBoredData> newPileSchemes,
  ) async {
    state = AsyncData(newPileSchemes);
  }

  Future<void> toggleSelectScheme(String pileFrictionalBoredSchemeId) async {
    final x = await future;
    final newState =
        x.map((item) {
          if (item.pileFrictionalBoredSchemeId == pileFrictionalBoredSchemeId) {
            return item.copyWith(isSelected: !item.isSelected);
          } else {
            return item.copyWith(isSelected: false);
          }
        }).toList();
    state = AsyncData(newState);
  }

  Future<String> _generateUniqueId({Set<String>? existingID}) async {
    late final Set<String> pileFrictionalBoredSchemeId;
    if (existingID == null) {
      final x = await future;
      pileFrictionalBoredSchemeId =
          x.map((item) => item.pileFrictionalBoredSchemeId).toSet();
    } else {
      pileFrictionalBoredSchemeId = existingID;
    }
    String newID = nanoid(alphabet: Alphabet.noDoppelgangerSafe);
    while (pileFrictionalBoredSchemeId.contains(newID)) {
      newID = nanoid(alphabet: Alphabet.noDoppelgangerSafe);
    }
    return newID;
  }

  Future<List<PileFrictionalBoredData>> pileFrictionalBoredScheming({
    List<PileFrictionalBoredData>? existingSchemes,
    bool? calledInBuild,
  }) async {
    //* keep selected schemes
    final List<PileFrictionalBoredData> finalList = [];
    late final List<PileFrictionalBoredData> schemesLeft;
    schemesLeft = await deleteTablesNotSelected(
      existingScheme: existingSchemes,
      calledInBuild: calledInBuild,
    );
    finalList.addAll(schemesLeft);
    final existingID =
        existingSchemes?.map((e) => e.pileFrictionalBoredSchemeId).toSet();

    final globalData = await ref.read(globalDataControllerProvider.future);
    final pileInput = await ref.read(
      pileFrictionalBoredInputControllerProvider.future,
    );

    //* Initialization
    // record params
    StringBuffer buffer = StringBuffer(); // record the cals

    // capacity param
    double shaftCapacity = 0,
        baseCapacity = 0,
        totalGroundResistance = 0,
        strCapacity = 0;

    // material params
    final double fy = 500;

    // initial iteration param
    double pileDiaIntl = 600, lengthIntl = 5, rIntl = 0.005;

    // iteration param
    double lengthDelta = 1, rDelta = 0.005;

    // geometric param
    double clearS = 0, rActual = 0, cover = 75, assumedLinksDia = 12;
    List<int> rebar = [40];

    // limits
    double minClearS = 80, maxClearS = 260; //(~300c/c)

    // loop controls
    bool skipPileLength = false, skipSteelRatio = false;

    final double fcu = pileInput.fcu;
    final double ratioOfBelloutDia = pileInput.ratioOfBelloutDia;

    // min rebar clear
    int n = 0;
    String mainBarCircle = '';
    //* get column capacity thru looping
    for (
      double pileDia = pileDiaIntl;
      pileDia <= pileInput.maxPileDiameter;
      pileDia += pileInput.diaIncrement
    ) {
      for (
        double length = lengthIntl;
        length <= pileInput.maxPileLength;
        length += lengthDelta
      ) {
        if (skipPileLength) {
          skipPileLength = false;
          break;
        }
        for (
          double rTarget = rIntl;
          rTarget < pileInput.maxSteelRatio;
          rTarget += rDelta
        ) {
          if (skipSteelRatio) {
            skipSteelRatio = false;
            break;
          }
          for (int i = 0; i < rebar.length; i++) {
            n = max(
              6,
              ((rTarget * pow(pileDia, 2) * pi / 4) /
                      (pow(rebar[i], 2) * pi / 4))
                  .ceil(),
            );
            clearS =
                ((pileDia / 2 - (cover + assumedLinksDia + rebar[i] / 2)) *
                        2 *
                        pi -
                    (n * rebar[i])) /
                (n - 1);
            rActual =
                n * pow(rebar[i], 2) * pi / 4 / (pow(pileDia, 2) * pi / 4);

            shaftCapacity = getFrictionalPileShaftCapacity(
              pileDia,
              length,
              pileInput.soilUnitWeight,
              pileInput.kTan,
              factorOfSafety: pileInput.fos,
            );
            baseCapacity = getEndBearningCapacity(
              pileDia,
              pileInput.soilUnitWeight * length * pileInput.sptNValue,
              ratioOfBelloutDia: ratioOfBelloutDia,
              factorOfSafety: pileInput.fos,
            );
            totalGroundResistance = shaftCapacity + baseCapacity;
            strCapacity = getAxialCapacityColumnCircle(
              fcu,
              pileDia,
              fy,
              rActual,
            );
            mainBarCircle = '${n}T${rebar[i]}';
            //* save scheme ONLY when criteria satisfied
            if (totalGroundResistance >= pileInput.slsLoad &&
                strCapacity >= pileInput.ulsLoad &&
                clearS >= minClearS &&
                clearS <= maxClearS &&
                rActual <= pileInput.maxSteelRatio) {
              _recordCalsResult(
                buffer,
                input: pileInput,
                globalData: globalData,
                size: pileDia,
                fcu: fcu,
                cover: cover,
                steelRatioCircle: rActual,
                mainBarCircle: mainBarCircle,
                diameter: pileDia,
                length: length,
                shaftCapacity: shaftCapacity,
                baseCapacity: baseCapacity,
                totalGroundResistance: totalGroundResistance,
                strCapacity: strCapacity,
              );

              finalList.add(
                PileFrictionalBoredData(
                  sptNValue: pileInput.sptNValue,
                  soilUnitWeight: pileInput.soilUnitWeight,
                  kTan: pileInput.kTan,
                  fos: pileInput.fos,
                  fcu: pileInput.fcu,
                  maxPileLength: pileInput.maxPileLength,
                  maxPileDiameter: pileInput.maxPileDiameter,
                  maxSteelRatio: pileInput.maxSteelRatio,
                  slsLoad: pileInput.slsLoad,
                  ulsLoad: pileInput.ulsLoad,
                  ratioOfBelloutDia: pileInput.ratioOfBelloutDia,
                  diameter: pileDia,
                  length: length,
                  shaftCapacity: shaftCapacity,
                  baseCapacity: baseCapacity,
                  totalGroundResistance: totalGroundResistance,
                  strCapacity: strCapacity,
                  rebar: mainBarCircle,
                  steelRatio: rActual,
                  isSelected: false,
                  calsLog: buffer.toString(),
                  pileFrictionalBoredSchemeId: await _generateUniqueId(
                    existingID: existingID,
                  ),
                ),
              );
              skipPileLength = true;
              skipSteelRatio = true;
            }
          }
        }
      }
    }
    state = AsyncData(finalList);
    return finalList;
  }

  void _recordCalsResult(
    StringBuffer buffer, {
    required PileFrictionalBoredInput input,
    required GlobalData globalData,
    required double size,
    required double fcu,
    required double cover,
    required double steelRatioCircle,
    required String mainBarCircle,
    required double diameter,
    required double length,
    required double shaftCapacity,
    required double baseCapacity,
    required double totalGroundResistance,
    required double strCapacity,
  }) {
    late final List<String> unit;

    switch (globalData.unit) {
      case 'metrics':
        unit = PreferredUnit.metrics;
        break;
      case 'imperial':
        unit = PreferredUnit.imperial;
        break;
      default:
        unit = PreferredUnit.metrics;
        break;
    }
    buffer.clear();
    buffer.write(
      'SLS Total: ${NumberFormat('0').format(input.slsLoad)} [${unit[0]}] | ',
    );
    buffer.write(
      'ULS Total: ${NumberFormat('0').format(input.ulsLoad)} [${unit[0]}]\n',
    );
    buffer.write('Diameter: ${NumberFormat('0').format(size)} [${unit[4]}] | ');
    buffer.write('Length: ${NumberFormat('0').format(length)} [${unit[4]}] | ');
    buffer.write('fcu: ${NumberFormat('0').format(input.fcu)} [${unit[5]}] | ');
    buffer.write('Cover: ${NumberFormat('0').format(40)} [${unit[4]}]\n');
    buffer.write(
      'SPT N-value: ${NumberFormat('0').format(input.sptNValue)} | ',
    );
    buffer.write(
      'Soil Unit Weight: ${NumberFormat('0').format(input.soilUnitWeight)} [${unit[0]}/${unit[4]}] | ',
    );
    buffer.write('ktan\u03B8: ${NumberFormat('0.00').format(input.kTan)} | ');
    buffer.write('FOS: ${NumberFormat('0.00').format(input.fos)}\n');
    buffer.write(
      'Bellout Used: ${input.ratioOfBelloutDia > 1 ? 'Yes' : 'No'} | ',
    );
    buffer.write(
      'Bellout Diameter: ${input.ratioOfBelloutDia > 1 ? NumberFormat('0').format(diameter * input.ratioOfBelloutDia) : 'N.A.'} [${unit[4]}]\n',
    );
    buffer.write(
      'Shaft Capacity: ${NumberFormat('0').format(shaftCapacity)} [${unit[0]}] | ',
    );
    buffer.write(
      'Base Capacity: ${NumberFormat('0').format(baseCapacity)} [${unit[0]}] | ',
    );
    buffer.write(
      'Total Ground Resistance: ${NumberFormat('0').format(totalGroundResistance)} [${unit[0]}]\n',
    );
    buffer.write('Rebar: $mainBarCircle | ');
    buffer.write(
      'Structural Capacity: ${NumberFormat('0').format(strCapacity)} [${unit[0]}]\n',
    );
    if (totalGroundResistance > input.slsLoad) {
      buffer.write('Ground Capacity: pass\n');
    } else {
      buffer.write('Ground Capacity: fail\n');
    }
    if (strCapacity > input.ulsLoad) {
      buffer.write('Structural Capacity: pass\n');
    } else {
      buffer.write('Structural Capacity: fail\n');
    }
  }
}
