// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'pile_end_bearing_bored_input.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$PileEndBearingBoredInput {

 double get safeBearing; double get fos; double get fcu; double get maxPileLength; double get maxPileDiameter; double get ratioOfBelloutDia; double get maxSteelRatio; double get slsLoad; double get ulsLoad; double get diaIncrement; bool get useSelectColLoad; double get colLoadFactor; String get id;
/// Create a copy of PileEndBearingBoredInput
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$PileEndBearingBoredInputCopyWith<PileEndBearingBoredInput> get copyWith => _$PileEndBearingBoredInputCopyWithImpl<PileEndBearingBoredInput>(this as PileEndBearingBoredInput, _$identity);

  /// Serializes this PileEndBearingBoredInput to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is PileEndBearingBoredInput&&(identical(other.safeBearing, safeBearing) || other.safeBearing == safeBearing)&&(identical(other.fos, fos) || other.fos == fos)&&(identical(other.fcu, fcu) || other.fcu == fcu)&&(identical(other.maxPileLength, maxPileLength) || other.maxPileLength == maxPileLength)&&(identical(other.maxPileDiameter, maxPileDiameter) || other.maxPileDiameter == maxPileDiameter)&&(identical(other.ratioOfBelloutDia, ratioOfBelloutDia) || other.ratioOfBelloutDia == ratioOfBelloutDia)&&(identical(other.maxSteelRatio, maxSteelRatio) || other.maxSteelRatio == maxSteelRatio)&&(identical(other.slsLoad, slsLoad) || other.slsLoad == slsLoad)&&(identical(other.ulsLoad, ulsLoad) || other.ulsLoad == ulsLoad)&&(identical(other.diaIncrement, diaIncrement) || other.diaIncrement == diaIncrement)&&(identical(other.useSelectColLoad, useSelectColLoad) || other.useSelectColLoad == useSelectColLoad)&&(identical(other.colLoadFactor, colLoadFactor) || other.colLoadFactor == colLoadFactor)&&(identical(other.id, id) || other.id == id));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,safeBearing,fos,fcu,maxPileLength,maxPileDiameter,ratioOfBelloutDia,maxSteelRatio,slsLoad,ulsLoad,diaIncrement,useSelectColLoad,colLoadFactor,id);

@override
String toString() {
  return 'PileEndBearingBoredInput(safeBearing: $safeBearing, fos: $fos, fcu: $fcu, maxPileLength: $maxPileLength, maxPileDiameter: $maxPileDiameter, ratioOfBelloutDia: $ratioOfBelloutDia, maxSteelRatio: $maxSteelRatio, slsLoad: $slsLoad, ulsLoad: $ulsLoad, diaIncrement: $diaIncrement, useSelectColLoad: $useSelectColLoad, colLoadFactor: $colLoadFactor, id: $id)';
}


}

/// @nodoc
abstract mixin class $PileEndBearingBoredInputCopyWith<$Res>  {
  factory $PileEndBearingBoredInputCopyWith(PileEndBearingBoredInput value, $Res Function(PileEndBearingBoredInput) _then) = _$PileEndBearingBoredInputCopyWithImpl;
@useResult
$Res call({
 double safeBearing, double fos, double fcu, double maxPileLength, double maxPileDiameter, double ratioOfBelloutDia, double maxSteelRatio, double slsLoad, double ulsLoad, double diaIncrement, bool useSelectColLoad, double colLoadFactor, String id
});




}
/// @nodoc
class _$PileEndBearingBoredInputCopyWithImpl<$Res>
    implements $PileEndBearingBoredInputCopyWith<$Res> {
  _$PileEndBearingBoredInputCopyWithImpl(this._self, this._then);

  final PileEndBearingBoredInput _self;
  final $Res Function(PileEndBearingBoredInput) _then;

/// Create a copy of PileEndBearingBoredInput
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? safeBearing = null,Object? fos = null,Object? fcu = null,Object? maxPileLength = null,Object? maxPileDiameter = null,Object? ratioOfBelloutDia = null,Object? maxSteelRatio = null,Object? slsLoad = null,Object? ulsLoad = null,Object? diaIncrement = null,Object? useSelectColLoad = null,Object? colLoadFactor = null,Object? id = null,}) {
  return _then(_self.copyWith(
safeBearing: null == safeBearing ? _self.safeBearing : safeBearing // ignore: cast_nullable_to_non_nullable
as double,fos: null == fos ? _self.fos : fos // ignore: cast_nullable_to_non_nullable
as double,fcu: null == fcu ? _self.fcu : fcu // ignore: cast_nullable_to_non_nullable
as double,maxPileLength: null == maxPileLength ? _self.maxPileLength : maxPileLength // ignore: cast_nullable_to_non_nullable
as double,maxPileDiameter: null == maxPileDiameter ? _self.maxPileDiameter : maxPileDiameter // ignore: cast_nullable_to_non_nullable
as double,ratioOfBelloutDia: null == ratioOfBelloutDia ? _self.ratioOfBelloutDia : ratioOfBelloutDia // ignore: cast_nullable_to_non_nullable
as double,maxSteelRatio: null == maxSteelRatio ? _self.maxSteelRatio : maxSteelRatio // ignore: cast_nullable_to_non_nullable
as double,slsLoad: null == slsLoad ? _self.slsLoad : slsLoad // ignore: cast_nullable_to_non_nullable
as double,ulsLoad: null == ulsLoad ? _self.ulsLoad : ulsLoad // ignore: cast_nullable_to_non_nullable
as double,diaIncrement: null == diaIncrement ? _self.diaIncrement : diaIncrement // ignore: cast_nullable_to_non_nullable
as double,useSelectColLoad: null == useSelectColLoad ? _self.useSelectColLoad : useSelectColLoad // ignore: cast_nullable_to_non_nullable
as bool,colLoadFactor: null == colLoadFactor ? _self.colLoadFactor : colLoadFactor // ignore: cast_nullable_to_non_nullable
as double,id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,
  ));
}

}


/// Adds pattern-matching-related methods to [PileEndBearingBoredInput].
extension PileEndBearingBoredInputPatterns on PileEndBearingBoredInput {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _PileEndBearingBoredInput value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _PileEndBearingBoredInput() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _PileEndBearingBoredInput value)  $default,){
final _that = this;
switch (_that) {
case _PileEndBearingBoredInput():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _PileEndBearingBoredInput value)?  $default,){
final _that = this;
switch (_that) {
case _PileEndBearingBoredInput() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( double safeBearing,  double fos,  double fcu,  double maxPileLength,  double maxPileDiameter,  double ratioOfBelloutDia,  double maxSteelRatio,  double slsLoad,  double ulsLoad,  double diaIncrement,  bool useSelectColLoad,  double colLoadFactor,  String id)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _PileEndBearingBoredInput() when $default != null:
return $default(_that.safeBearing,_that.fos,_that.fcu,_that.maxPileLength,_that.maxPileDiameter,_that.ratioOfBelloutDia,_that.maxSteelRatio,_that.slsLoad,_that.ulsLoad,_that.diaIncrement,_that.useSelectColLoad,_that.colLoadFactor,_that.id);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( double safeBearing,  double fos,  double fcu,  double maxPileLength,  double maxPileDiameter,  double ratioOfBelloutDia,  double maxSteelRatio,  double slsLoad,  double ulsLoad,  double diaIncrement,  bool useSelectColLoad,  double colLoadFactor,  String id)  $default,) {final _that = this;
switch (_that) {
case _PileEndBearingBoredInput():
return $default(_that.safeBearing,_that.fos,_that.fcu,_that.maxPileLength,_that.maxPileDiameter,_that.ratioOfBelloutDia,_that.maxSteelRatio,_that.slsLoad,_that.ulsLoad,_that.diaIncrement,_that.useSelectColLoad,_that.colLoadFactor,_that.id);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( double safeBearing,  double fos,  double fcu,  double maxPileLength,  double maxPileDiameter,  double ratioOfBelloutDia,  double maxSteelRatio,  double slsLoad,  double ulsLoad,  double diaIncrement,  bool useSelectColLoad,  double colLoadFactor,  String id)?  $default,) {final _that = this;
switch (_that) {
case _PileEndBearingBoredInput() when $default != null:
return $default(_that.safeBearing,_that.fos,_that.fcu,_that.maxPileLength,_that.maxPileDiameter,_that.ratioOfBelloutDia,_that.maxSteelRatio,_that.slsLoad,_that.ulsLoad,_that.diaIncrement,_that.useSelectColLoad,_that.colLoadFactor,_that.id);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _PileEndBearingBoredInput extends PileEndBearingBoredInput {
   _PileEndBearingBoredInput({this.safeBearing = 1000, this.fos = 1, this.fcu = 45, this.maxPileLength = 30, this.maxPileDiameter = 2000, this.ratioOfBelloutDia = 1.65, this.maxSteelRatio = 0.04, this.slsLoad = 1000, this.ulsLoad = 2000, this.diaIncrement = 200, this.useSelectColLoad = false, this.colLoadFactor = 1.0, this.id = '1'}): super._();
  factory _PileEndBearingBoredInput.fromJson(Map<String, dynamic> json) => _$PileEndBearingBoredInputFromJson(json);

@override@JsonKey() final  double safeBearing;
@override@JsonKey() final  double fos;
@override@JsonKey() final  double fcu;
@override@JsonKey() final  double maxPileLength;
@override@JsonKey() final  double maxPileDiameter;
@override@JsonKey() final  double ratioOfBelloutDia;
@override@JsonKey() final  double maxSteelRatio;
@override@JsonKey() final  double slsLoad;
@override@JsonKey() final  double ulsLoad;
@override@JsonKey() final  double diaIncrement;
@override@JsonKey() final  bool useSelectColLoad;
@override@JsonKey() final  double colLoadFactor;
@override@JsonKey() final  String id;

/// Create a copy of PileEndBearingBoredInput
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$PileEndBearingBoredInputCopyWith<_PileEndBearingBoredInput> get copyWith => __$PileEndBearingBoredInputCopyWithImpl<_PileEndBearingBoredInput>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$PileEndBearingBoredInputToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _PileEndBearingBoredInput&&(identical(other.safeBearing, safeBearing) || other.safeBearing == safeBearing)&&(identical(other.fos, fos) || other.fos == fos)&&(identical(other.fcu, fcu) || other.fcu == fcu)&&(identical(other.maxPileLength, maxPileLength) || other.maxPileLength == maxPileLength)&&(identical(other.maxPileDiameter, maxPileDiameter) || other.maxPileDiameter == maxPileDiameter)&&(identical(other.ratioOfBelloutDia, ratioOfBelloutDia) || other.ratioOfBelloutDia == ratioOfBelloutDia)&&(identical(other.maxSteelRatio, maxSteelRatio) || other.maxSteelRatio == maxSteelRatio)&&(identical(other.slsLoad, slsLoad) || other.slsLoad == slsLoad)&&(identical(other.ulsLoad, ulsLoad) || other.ulsLoad == ulsLoad)&&(identical(other.diaIncrement, diaIncrement) || other.diaIncrement == diaIncrement)&&(identical(other.useSelectColLoad, useSelectColLoad) || other.useSelectColLoad == useSelectColLoad)&&(identical(other.colLoadFactor, colLoadFactor) || other.colLoadFactor == colLoadFactor)&&(identical(other.id, id) || other.id == id));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,safeBearing,fos,fcu,maxPileLength,maxPileDiameter,ratioOfBelloutDia,maxSteelRatio,slsLoad,ulsLoad,diaIncrement,useSelectColLoad,colLoadFactor,id);

@override
String toString() {
  return 'PileEndBearingBoredInput(safeBearing: $safeBearing, fos: $fos, fcu: $fcu, maxPileLength: $maxPileLength, maxPileDiameter: $maxPileDiameter, ratioOfBelloutDia: $ratioOfBelloutDia, maxSteelRatio: $maxSteelRatio, slsLoad: $slsLoad, ulsLoad: $ulsLoad, diaIncrement: $diaIncrement, useSelectColLoad: $useSelectColLoad, colLoadFactor: $colLoadFactor, id: $id)';
}


}

/// @nodoc
abstract mixin class _$PileEndBearingBoredInputCopyWith<$Res> implements $PileEndBearingBoredInputCopyWith<$Res> {
  factory _$PileEndBearingBoredInputCopyWith(_PileEndBearingBoredInput value, $Res Function(_PileEndBearingBoredInput) _then) = __$PileEndBearingBoredInputCopyWithImpl;
@override @useResult
$Res call({
 double safeBearing, double fos, double fcu, double maxPileLength, double maxPileDiameter, double ratioOfBelloutDia, double maxSteelRatio, double slsLoad, double ulsLoad, double diaIncrement, bool useSelectColLoad, double colLoadFactor, String id
});




}
/// @nodoc
class __$PileEndBearingBoredInputCopyWithImpl<$Res>
    implements _$PileEndBearingBoredInputCopyWith<$Res> {
  __$PileEndBearingBoredInputCopyWithImpl(this._self, this._then);

  final _PileEndBearingBoredInput _self;
  final $Res Function(_PileEndBearingBoredInput) _then;

/// Create a copy of PileEndBearingBoredInput
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? safeBearing = null,Object? fos = null,Object? fcu = null,Object? maxPileLength = null,Object? maxPileDiameter = null,Object? ratioOfBelloutDia = null,Object? maxSteelRatio = null,Object? slsLoad = null,Object? ulsLoad = null,Object? diaIncrement = null,Object? useSelectColLoad = null,Object? colLoadFactor = null,Object? id = null,}) {
  return _then(_PileEndBearingBoredInput(
safeBearing: null == safeBearing ? _self.safeBearing : safeBearing // ignore: cast_nullable_to_non_nullable
as double,fos: null == fos ? _self.fos : fos // ignore: cast_nullable_to_non_nullable
as double,fcu: null == fcu ? _self.fcu : fcu // ignore: cast_nullable_to_non_nullable
as double,maxPileLength: null == maxPileLength ? _self.maxPileLength : maxPileLength // ignore: cast_nullable_to_non_nullable
as double,maxPileDiameter: null == maxPileDiameter ? _self.maxPileDiameter : maxPileDiameter // ignore: cast_nullable_to_non_nullable
as double,ratioOfBelloutDia: null == ratioOfBelloutDia ? _self.ratioOfBelloutDia : ratioOfBelloutDia // ignore: cast_nullable_to_non_nullable
as double,maxSteelRatio: null == maxSteelRatio ? _self.maxSteelRatio : maxSteelRatio // ignore: cast_nullable_to_non_nullable
as double,slsLoad: null == slsLoad ? _self.slsLoad : slsLoad // ignore: cast_nullable_to_non_nullable
as double,ulsLoad: null == ulsLoad ? _self.ulsLoad : ulsLoad // ignore: cast_nullable_to_non_nullable
as double,diaIncrement: null == diaIncrement ? _self.diaIncrement : diaIncrement // ignore: cast_nullable_to_non_nullable
as double,useSelectColLoad: null == useSelectColLoad ? _self.useSelectColLoad : useSelectColLoad // ignore: cast_nullable_to_non_nullable
as bool,colLoadFactor: null == colLoadFactor ? _self.colLoadFactor : colLoadFactor // ignore: cast_nullable_to_non_nullable
as double,id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

// dart format on
