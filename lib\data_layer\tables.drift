CREATE TABLE  ListLoadingTable(
    usage TEXT NOT NULL,
    finish REAL NOT NULL,
    service REAL NOT NULL,
    liveLoad REAL NOT NULL,
    loadingTableId TEXT NOT NULL PRIMARY KEY
) AS RowLoadingTable;

CREATE TABLE  ListGlobalData(
    id TEXT NOT NULL PRIMARY KEY,
    unit TEXT NOT NULL,
    sdlFactor REAL NOT NULL,
    llFactor REAL NOT NULL,
    rcUnitWeight REAL NOT NULL,
    finishUnitWeight REAL NOT NULL,
    steelUnitWeight REAL NOT NULL,
    soilUnitWeight REAL NOT NULL,
    waterUnitWeight REAL NOT NULL
) AS RowGlobalData;

CREATE TABLE  ListBeamSchemeInput(
    id TEXT NOT NULL PRIMARY KEY, 
    shortSpan REAL NOT NULL,
    longSpan REAL NOT NULL,
    bays INT NOT NULL,
    mainStrZone REAL NOT NULL,
    secStrZone REAL NOT NULL,
    fcu REAL NOT NULL,
    cover REAL NOT NULL,
    mainKValue REAL NOT NULL,
    mainSteelRatio REAL NOT NULL,
    secKValue REAL NOT NULL,
    secSteelRatio REAL NOT NULL,
    minS INT NOT NULL,
    maxS INT NOT NULL,
    maxWidth REAL NOT NULL,
    maxLayers INT NOT NULL,
    shortSpanIncreament REAL NOT NULL,
    longSpanIncreament REAL NOT NULL,
    baysIncreament INT NOT NULL,
    iterationSteps INT NOT NULL,
    usage TEXT NOT NULL,
    slabThickness REAL NOT NULL,
    useSlabSelected BOOL NOT NULL
) AS RowBeamSchemeInput;


CREATE TABLE  ListBeamSchemeData(
    usage TEXT NOT NULL,
    finish REAL NOT NULL,
    service REAL NOT NULL,
    liveLoad REAL NOT NULL,
    loadingTableId TEXT NOT NULL,
    shortSpan REAL NOT NULL,
    longSpan REAL NOT NULL,
    bays INT NOT NULL,
    mainStrZone REAL NOT NULL,
    secStrZone REAL NOT NULL,
    fcu REAL NOT NULL,
    cover REAL NOT NULL,
    mainWidth REAL NOT NULL,
    secWidth REAL NOT NULL,
    mainTopBar TEXT NOT NULL,
    mainBottomBar TEXT NOT NULL,
    mainLinks TEXT NOT NULL,
    secTopBar TEXT NOT NULL,
    secBottomBar TEXT NOT NULL,
    secLinks TEXT NOT NULL,
    beamSchemeId TEXT NOT NULL PRIMARY KEY,
    calsLog TEXT NOT NULL,
    isSelected BOOL NOT NULL
) AS RowBeamSchemeData;

CREATE TABLE  ListSlabSchemeInput(
    id TEXT NOT NULL PRIMARY KEY, 
    span REAL NOT NULL,
    fcu REAL NOT NULL,
    cover REAL NOT NULL,
    mainKValue REAL NOT NULL,
    mainSteelRatio REAL NOT NULL,
    minS INT NOT NULL,
    maxS INT NOT NULL,
    maxDepth REAL NOT NULL,
    minDepth REAL NOT NULL,
    maxLayers INT NOT NULL,
    spanIncreament REAL NOT NULL,
    iterationSteps INT NOT NULL,
    usage TEXT NOT NULL
) AS RowSlabSchemeInput;

CREATE TABLE  ListSlabSchemeData(
    usage TEXT NOT NULL,
    finish REAL NOT NULL,
    service REAL NOT NULL,
    liveLoad REAL NOT NULL,
    loadingTableId TEXT NOT NULL,
    span REAL NOT NULL,
    strZone REAL NOT NULL,
    fcu REAL NOT NULL,
    cover REAL NOT NULL,
    mainTopBar TEXT NOT NULL,
    mainBottomBar TEXT NOT NULL,
    mainLinks TEXT NOT NULL,
    slabSchemeId TEXT NOT NULL PRIMARY KEY,
    calsLog TEXT NOT NULL,
    isSelected BOOL NOT NULL
) AS RowSlabSchemeData;


CREATE TABLE  ListBeamSchemeAndLoading(
    beamSchemeId TEXT NOT NULL,
    loadingTableId TEXT NOT NULL
) AS RowBeamSchemeAndLoading;

CREATE TABLE  ListColumnSchemeInput(
    usage TEXT NOT NULL,
    slabThickness REAL NOT NULL,
    loadWidth REAL NOT NULL,
    loadLength REAL NOT NULL,
    nosOfFloor INT NOT NULL,
    columnSchemeInputId TEXT NOT NULL PRIMARY KEY
) AS RowColumnSchemeInput;

CREATE TABLE  ListColumnSchemeInputGlobal(
    columnSchemeInputGlobalId INT NOT NULL PRIMARY KEY,
    cover REAL NOT NULL,
    minColumnSize REAL NOT NULL,
    sizeIncrement REAL NOT NULL,
    iterationSteps INT NOT NULL,
    concreteGrade TEXT NOT NULL,
    minSteelRatio REAL NOT NULL,
    maxSteelRatio REAL NOT NULL,
    safetyFactor REAL NOT NULL,
    minClearS REAL NOT NULL,
    useSlabSelected BOOL NOT NULL
) AS RowColumnSchemeInputGlobal;

CREATE TABLE  ListColumnSchemeData(
    columnSchemeDataId TEXT NOT NULL PRIMARY KEY,
    sdl REAL NOT NULL,
    ll REAL NOT NULL,
    slsLoad REAL NOT NULL,
    ulsLoad REAL NOT NULL,
    size REAL NOT NULL,
    fcu REAL NOT NULL,
    cover REAL NOT NULL,
    axialCapacitySquare REAL NOT NULL,
    mainBarSquare TEXT NOT NULL,
    steelRatioSqaure REAL NOT NULL,
    axialCapacityCircle REAL NOT NULL,
    mainBarCircle TEXT NOT NULL,
    steelRatioCircle REAL NOT NULL,
    calsLog TEXT NOT NULL,
    isSelected BOOL NOT NULL
) AS RowColumnSchemeData;

CREATE TABLE  ListTransferBeamSchemeInputGlobal(
    id TEXT NOT NULL PRIMARY KEY,
    span REAL NOT NULL,
    loadWidth REAL NOT NULL,
    strZone REAL NOT NULL,
    fcu REAL NOT NULL,
    cover REAL NOT NULL,
    mainKValue REAL NOT NULL,
    mainSteelRatio REAL NOT NULL,
    minS INT NOT NULL,
    maxS INT NOT NULL,
    maxWidth REAL NOT NULL,
    maxLayers INT NOT NULL,
    usage TEXT NOT NULL,
    slabThickness REAL NOT NULL,
    useSlabSelected BOOL NOT NULL
) AS RowTransferBeamSchemeInputGlobal;

CREATE TABLE  ListTransferBeamSchemeInput(
    pointLoad REAL NOT NULL,
    distA REAL NOT NULL,
    loadFromSelectedCol BOOL NOT NULL,
    transferBeamSchemeInputId TEXT NOT NULL PRIMARY KEY
) AS RowTransferBeamSchemeInput;

CREATE TABLE  ListTransferBeamSchemeData(
    usage TEXT NOT NULL,
    slabThickness REAL NOT NULL,
    span REAL NOT NULL,
    loadWidth REAL NOT NULL,
    strZone REAL NOT NULL,
    fcu REAL NOT NULL,
    cover REAL NOT NULL,
    mainWidth REAL NOT NULL,
    mainTopBar TEXT NOT NULL,
    mainBottomBar TEXT NOT NULL,
    mainLinks TEXT NOT NULL,
    id TEXT NOT NULL PRIMARY KEY,
    calsLog TEXT NOT NULL,
    beamForce TEXT NOT NULL
) AS RowTransferBeamSchemeData;

CREATE TABLE  ListCantileverSchemeInputGlobal(
    id TEXT NOT NULL PRIMARY KEY,
    span REAL NOT NULL,
    loadWidth REAL NOT NULL,
    strZone REAL NOT NULL,
    fcu REAL NOT NULL,
    cover REAL NOT NULL,
    mainKValue REAL NOT NULL,
    mainSteelRatio REAL NOT NULL,
    minS INT NOT NULL,
    maxS INT NOT NULL,
    maxWidth REAL NOT NULL,
    maxLayers INT NOT NULL,
    usage TEXT NOT NULL,
    slabThickness REAL NOT NULL,
    useSlabSelected BOOL NOT NULL
) AS RowCantileverSchemeInputGlobal;

CREATE TABLE  ListCantileverSchemeInput(
    pointLoad REAL NOT NULL,
    distA REAL NOT NULL,
    loadFromSelectedCol BOOL NOT NULL,
    cantileverSchemeInputId TEXT NOT NULL PRIMARY KEY
) AS RowCantileverSchemeInput;

CREATE TABLE  ListCantileverSchemeData(
    usage TEXT NOT NULL,
    slabThickness REAL NOT NULL,
    span REAL NOT NULL,
    loadWidth REAL NOT NULL,
    strZone REAL NOT NULL,
    fcu REAL NOT NULL,
    cover REAL NOT NULL,
    mainWidth REAL NOT NULL,
    mainTopBar TEXT NOT NULL,
    mainBottomBar TEXT NOT NULL,
    mainLinks TEXT NOT NULL,
    id TEXT NOT NULL PRIMARY KEY,
    calsLog TEXT NOT NULL,
    beamForce TEXT NOT NULL
) AS RowCantileverSchemeData;

CREATE TABLE  ListSteelBeamSchemeInput(
    id TEXT NOT NULL PRIMARY KEY, 
    shortSpan REAL NOT NULL,
    longSpan REAL NOT NULL,
    bays INT NOT NULL,
    strZone REAL NOT NULL,
    fsy REAL NOT NULL,
    shortSpanIncreament REAL NOT NULL,
    longSpanIncreament REAL NOT NULL,
    baysIncreament INT NOT NULL,
    iterationSteps INT NOT NULL,
    usage TEXT NOT NULL,
    slabThickness REAL NOT NULL,
    compositeActionFactor REAL NOT NULL
) AS RowSteelBeamSchemeInput;

CREATE TABLE  ListSteelBeamSchemeData(
    usage TEXT NOT NULL,
    finish REAL NOT NULL,
    service REAL NOT NULL,
    liveLoad REAL NOT NULL,
    loadingTableId TEXT NOT NULL,
    slabThickness REAL NOT NULL,
    compositeActionFactor REAL NOT NULL,
    shortSpan REAL NOT NULL,
    longSpan REAL NOT NULL,
    bays INT NOT NULL,
    strZone REAL NOT NULL,
    fsy REAL NOT NULL,
    mainBeamSection TEXT NOT NULL,
    secBeamSection TEXT NOT NULL,
    steelBeamSchemeId TEXT NOT NULL PRIMARY KEY,
    calsLog TEXT NOT NULL
) AS RowSteelBeamSchemeData;

CREATE TABLE  ListSteelColumnSchemeInput(
    usage TEXT NOT NULL,
    slabThickness REAL NOT NULL,
    loadWidth REAL NOT NULL,
    loadLength REAL NOT NULL,
    nosOfFloor INT NOT NULL,
    steelColumnSchemeInputId TEXT NOT NULL PRIMARY KEY
) AS RowSteelColumnSchemeInput;

CREATE TABLE  ListSteelColumnSchemeInputGlobal(
    id TEXT NOT NULL PRIMARY KEY, 
    steelGrade TEXT NOT NULL,
    unbracedLength REAL NOT NULL
) AS RowSteelColumnSchemeInputGlobal;

CREATE TABLE  ListSteelColumnSchemeData(
    steelColumnSchemeDataId TEXT NOT NULL PRIMARY KEY,
    sdl REAL NOT NULL,
    ll REAL NOT NULL,
    slsLoad REAL NOT NULL,
    ulsLoad REAL NOT NULL,
    fsy REAL NOT NULL,
    unbracedLength REAL NOT NULL,
    axialCapacity REAL NOT NULL,
    steelSection TEXT NOT NULL,
    calsLog TEXT NOT NULL
) AS RowSteelColumnSchemeData;
 
CREATE TABLE  ListSteelTransferTrussSchemeInput(
    pointLoad REAL NOT NULL,
    distA REAL NOT NULL,
    steelTransferTrussSchemeInputId TEXT NOT NULL PRIMARY KEY
) AS RowSteelTransferTrussSchemeInput;

CREATE TABLE  ListSteelTransferTrussSchemeInputGlobal(
    id TEXT NOT NULL PRIMARY KEY,
    span REAL NOT NULL,
    loadWidth REAL NOT NULL,
    strZone REAL NOT NULL,
    fsy REAL NOT NULL,
    unbracedLength REAL NOT NULL,
    usage TEXT NOT NULL,
    slabThickness REAL NOT NULL
) AS RowSteelTransferTrussSchemeInputGlobal;

CREATE TABLE  ListSteelTransferTrussSchemeData(
    usage TEXT NOT NULL,
    slabThickness REAL NOT NULL,
    span REAL NOT NULL,
    loadWidth REAL NOT NULL,
    strZone REAL NOT NULL,
    fsy REAL NOT NULL,
    unbracedLength REAL NOT NULL,
    steelSection TEXT NOT NULL,
    chordAxialCapacity REAL NOT NULL,
    leverArm REAL NOT NULL,
    momentCapacity REAL NOT NULL,
    liveLoadDeflection TEXT NOT NULL,
    id TEXT NOT NULL PRIMARY KEY,
    calsLog TEXT NOT NULL,
    beamForce TEXT NOT NULL
) AS RowSteelTransferTrussSchemeData;

CREATE TABLE  ListSteelCantileverTrussSchemeInput(
    pointLoad REAL NOT NULL,
    distA REAL NOT NULL,
    steelCantileverTrussSchemeInputId TEXT NOT NULL PRIMARY KEY
) AS RowSteelCantileverTrussSchemeInput;

CREATE TABLE  ListSteelCantileverTrussSchemeInputGlobal(
    id TEXT NOT NULL PRIMARY KEY,
    span REAL NOT NULL,
    loadWidth REAL NOT NULL,
    strZone REAL NOT NULL,
    fsy REAL NOT NULL,
    unbracedLength REAL NOT NULL,
    usage TEXT NOT NULL,
    slabThickness REAL NOT NULL
) AS RowSteelCantileverTrussSchemeInputGlobal;

CREATE TABLE  ListSteelCantileverTrussSchemeData(
    usage TEXT NOT NULL,
    slabThickness REAL NOT NULL,
    span REAL NOT NULL,
    loadWidth REAL NOT NULL,
    strZone REAL NOT NULL,
    fsy REAL NOT NULL,
    unbracedLength REAL NOT NULL,
    steelSection TEXT NOT NULL,
    chordAxialCapacity REAL NOT NULL,
    leverArm REAL NOT NULL,
    momentCapacity REAL NOT NULL,
    liveLoadDeflection TEXT NOT NULL,
    id TEXT NOT NULL PRIMARY KEY,
    calsLog TEXT NOT NULL,
    beamForce TEXT NOT NULL
) AS RowSteelCantileverTrussSchemeData;

CREATE TABLE  ListPileEndBearingBoredInput(
    safeBearing REAL NOT NULL,
    fos REAL NOT NULL,
    fcu REAL NOT NULL,
    maxPileDiameter REAL NOT NULL,
    ratioOfBelloutDia REAL NOT NULL,
    maxSteelRatio REAL NOT NULL,
    slsLoad REAL NOT NULL,
    ulsLoad REAL NOT NULL,
    diaIncrement REAL NOT NULL,
    useSelectColLoad BOOL NOT NULL,
    id TEXT NOT NULL PRIMARY KEY
) AS RowPileEndBearingBoredInput; 

CREATE TABLE  ListPileEndBearingBoredInputGlobal(
    colLoadFactor REAL NOT NULL,
    id TEXT NOT NULL PRIMARY KEY
) AS RowPileEndBearingBoredInputGlobal;

CREATE TABLE  ListPileEndBearingBoredData(
    safeBearing REAL NOT NULL,
    fos REAL NOT NULL,
    fcu REAL NOT NULL,
    maxPileDiameter REAL NOT NULL,
    ratioOfBelloutDia REAL NOT NULL,
    maxSteelRatio REAL NOT NULL,
    diameter REAL NOT NULL,
    length REAL NOT NULL,
    baseCapacity REAL NOT NULL,
    totalGroundResistance REAL NOT NULL,
    strCapacity REAL NOT NULL, 
    rebar TEXT NOT NULL,
    steelRatio REAL NOT NULL, 
    slsLoad REAL NOT NULL,
    ulsLoad REAL NOT NULL,
    isSelected BOOL NOT NULL,
    calsLog TEXT NOT NULL,
    pileEndBearingBoredSchemeId TEXT NOT NULL PRIMARY KEY
) AS RowPileEndBearingBoredData;

CREATE TABLE  ListPileFrictionalBoredInput(
    sptNValue REAL NOT NULL,
    soilUnitWeight REAL NOT NULL,
    kTan REAL NOT NULL,
    fos REAL NOT NULL,
    fcu REAL NOT NULL,
    maxPileLength REAL NOT NULL,
    maxPileDiameter REAL NOT NULL,
    ratioOfBelloutDia REAL NOT NULL,
    maxSteelRatio REAL NOT NULL,
    slsLoad REAL NOT NULL,
    ulsLoad REAL NOT NULL,
    diaIncrement REAL NOT NULL, 
    useSelectColLoad BOOL NOT NULL,
    id TEXT NOT NULL PRIMARY KEY
) AS RowPileFrictionalBoredInput; 

CREATE TABLE  ListPileFrictionalBoredInputGlobal(
    colLoadFactor REAL NOT NULL,
    id TEXT NOT NULL PRIMARY KEY
) AS RowPileFrictionalBoredInputGlobal;

CREATE TABLE  ListPileFrictionalBoredData(
    sptNValue REAL NOT NULL,
    soilUnitWeight REAL NOT NULL,
    kTan REAL NOT NULL,
    fos REAL NOT NULL,
    fcu REAL NOT NULL,
    maxPileLength REAL NOT NULL,
    maxPileDiameter REAL NOT NULL,
    ratioOfBelloutDia REAL NOT NULL,
    maxSteelRatio REAL NOT NULL,
    diameter REAL NOT NULL,
    length REAL NOT NULL,
    shaftCapacity REAL NOT NULL,
    baseCapacity REAL NOT NULL,
    totalGroundResistance REAL NOT NULL,
    strCapacity REAL NOT NULL, 
    rebar TEXT NOT NULL,
    steelRatio REAL NOT NULL, 
    slsLoad REAL NOT NULL,
    ulsLoad REAL NOT NULL,
    isSelected BOOL NOT NULL,
    calsLog TEXT NOT NULL,
    pileFrictionalBoredSchemeId TEXT NOT NULL PRIMARY KEY
) AS RowPileFrictionalBoredData;

CREATE TABLE  ListPileSocketedData(
    isSelected BOOL NOT NULL,
    pileSocketedSchemeId TEXT NOT NULL PRIMARY KEY
) AS RowPileSocketedData;

CREATE TABLE  ListPileDrivenInput(
    sptNValue REAL NOT NULL,
    fos REAL NOT NULL,
    maxPileLength REAL NOT NULL,
    slsLoad REAL NOT NULL,
    ulsLoad REAL NOT NULL,
    useSelectColLoad BOOL NOT NULL,
    id TEXT NOT NULL PRIMARY KEY
) AS RowPileDrivenInput; 

 CREATE TABLE  ListPileDrivenInputGlobal(
    colLoadFactor REAL NOT NULL,
    id TEXT NOT NULL PRIMARY KEY
) AS RowPileDrivenInputGlobal;

CREATE TABLE  ListPileDrivenData(
    sptNValue REAL NOT NULL,
    fos REAL NOT NULL,
    maxPileLength REAL NOT NULL,
    slsLoad REAL NOT NULL,
    ulsLoad REAL NOT NULL,
    section TEXT NOT NULL,
    length REAL NOT NULL,
    shaftCapacity REAL NOT NULL,
    strSLSCapacuity REAL NOT NULL,
    totalGroundResistance REAL NOT NULL,
    strULSCapacity REAL NOT NULL,
    isSelected BOOL NOT NULL,
    calsLog TEXT NOT NULL,
    pileDrivenSchemeId TEXT NOT NULL PRIMARY KEY
) AS RowPileDrivenData;

CREATE TABLE  ListFootingSchemeInput(
    id TEXT NOT NULL PRIMARY KEY, 
    fcu REAL NOT NULL,
    cover REAL NOT NULL,
    mainKValue REAL NOT NULL,
    mainSteelRatio REAL NOT NULL,
    minS INT NOT NULL,
    maxS INT NOT NULL,
    maxDepth REAL NOT NULL,
    minDepth REAL NOT NULL,
    maxLayers INT NOT NULL,
    groundType TEXT NOT NULL,
    soilNValue REAL NOT NULL,
    footingTopLevel REAL NOT NULL,
    waterTableLevel REAL NOT NULL,
    rockCapacity REAL NOT NULL,
    colShape TEXT NOT NULL,
    columnSize REAL NOT NULL,
    slsLoad REAL NOT NULL,
    ulsLoad REAL NOT NULL,
    useSelectColLoad BOOL NOT NULL
) AS RowFootingSchemeInput;

 CREATE TABLE  ListFootingSchemeInputGlobal(
    id TEXT NOT NULL PRIMARY KEY, 
    colLoadFactor REAL NOT NULL
) AS RowFootingSchemeInputGlobal;

CREATE TABLE  ListFootingSchemeData(
    size REAL NOT NULL,
    strZone REAL NOT NULL,
    fcu REAL NOT NULL,
    cover REAL NOT NULL,
    groundType TEXT NOT NULL,
    soilNValue REAL NOT NULL,
    footingTopLevel REAL NOT NULL,
    waterTableLevel REAL NOT NULL,
    rockCapacity REAL NOT NULL,
    colShape TEXT NOT NULL,
    columnSize REAL NOT NULL,
    slsLoad REAL NOT NULL,
    ulsLoad REAL NOT NULL,
    mainTopBar TEXT NOT NULL,
    mainBottomBar TEXT NOT NULL,
    mainLinks TEXT NOT NULL,
    punchingLinks TEXT NOT NULL,
    calsLog TEXT NOT NULL,
    isSelected BOOL NOT NULL,
    footingSchemeDataId TEXT NOT NULL PRIMARY KEY
) AS RowFootingSchemeData;

CREATE TABLE ListStrZoneTable (
    floor TEXT NOT NULL,
    height REAL NOT NULL,
    finish REAL NOT NULL,
    service REAL NOT NULL,
    clear REAL NOT NULL, 
    nosOfFloor INT NOT NULL,
    strZoneId TEXT NOT NULL PRIMARY KEY
) AS RowStrZoneTable;

CREATE TABLE ListRecommendedLoad (
    usage TEXT NOT NULL PRIMARY KEY,
    finish REAL NOT NULL,
    service REAL NOT NULL,
    partitionLoad REAL NOT NULL,
    sdl REAL NOT NULL,
    ll REAL NOT NULL,
    frp REAL NOT NULL
) AS RowRecommendedLoad;

 CREATE TABLE ListBasementWallSchemeInput(
    id TEXT NOT NULL PRIMARY KEY, 
    fcu REAL NOT NULL,
    cover REAL NOT NULL,
    mainKValue REAL NOT NULL,
    mainSteelRatio REAL NOT NULL,
    minS INT NOT NULL,
    maxS INT NOT NULL,
    maxDepth REAL NOT NULL,
    minDepth REAL NOT NULL,
    maxLayers INT NOT NULL,
    wallTopLevel REAL NOT NULL,
    wallBottomLevel REAL NOT NULL,
    soilTopLevel REAL NOT NULL,
    waterTopLevel REAL NOT NULL
) AS RowBasementWallSchemeInput;

CREATE TABLE  ListBasementWallSchemeData(
    strZone REAL NOT NULL,
    fcu REAL NOT NULL,
    cover REAL NOT NULL,
    mainTopBar TEXT NOT NULL,
    mainBottomBar TEXT NOT NULL,
    mainLinks TEXT NOT NULL,
    wallTopLevel REAL NOT NULL,
    wallBottomLevel REAL NOT NULL,
    soilTopLevel REAL NOT NULL,
    waterTopLevel REAL NOT NULL,
    basementWallSchemeId TEXT NOT NULL PRIMARY KEY,
    calsLog TEXT NOT NULL,
    wallForceULS TEXT NOT NULL,
    wallForceSLS TEXT NOT NULL
) AS RowBasementWallSchemeData;

CREATE TABLE  ListProgrammeItem(
    itemName TEXT NOT NULL,
    start REAL NOT NULL,
    duration REAL NOT NULL,
    isTouched BOOL NOT NULL,
    id TEXT NOT NULL PRIMARY KEY
) AS RowProgrammeItem;