import 'package:freezed_annotation/freezed_annotation.dart';
part 'programme_item.freezed.dart';
part 'programme_item.g.dart';

@freezed
abstract class ProgrammeItem with _$ProgrammeItem {
  const ProgrammeItem._();
  factory ProgrammeItem({
    @Default('item') String itemName,
    @Default(0.0) double start,
    @Default(4.0) double duration,
    @Default(false) bool isTouched,
    @Default('') String id,
  }) = _ProgrammeItem;

  factory ProgrammeItem.fromJson(Map<String, Object?> json) =>
      _$ProgrammeItemFromJson(json);

  double get end => start + duration;
}
