// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'steel_beam_scheme_data_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$steelBeamSchemeDataControllerHash() =>
    r'cc9d77d362c59cd772399ab6db1c5eb64abd1d59';

/// See also [SteelBeamSchemeDataController].
@ProviderFor(SteelBeamSchemeDataController)
final steelBeamSchemeDataControllerProvider = AutoDisposeAsyncNotifierProvider<
  SteelBeamSchemeDataController,
  List<SteelBeamSchemeData>
>.internal(
  SteelBeamSchemeDataController.new,
  name: r'steelBeamSchemeDataControllerProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$steelBeamSchemeDataControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$SteelBeamSchemeDataController =
    AutoDisposeAsyncNotifier<List<SteelBeamSchemeData>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
