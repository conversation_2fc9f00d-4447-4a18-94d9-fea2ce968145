import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:nanoid2/nanoid2.dart';

//presentation layer
import '../../domain_layer/strzone_table.dart';
import '../screen/homescreen.dart';

// domain layer
part 'strzone_tables_controller.g.dart';

@riverpod
class StrZoneTablesController extends _$StrZoneTablesController {
  @override
  FutureOr<List<StrZoneTable>> build() async {
    // print('Build: Loading Tables');
    final strZoneTables =
        await ref
            .read(appDatabaseControllerProvider.notifier)
            .queryStrZoneTables();
    if (strZoneTables.isEmpty) {
      String newID = nanoid(alphabet: Alphabet.noDoppelgangerSafe);
      return [StrZoneTable(strZoneId: newID)];
    } else {
      return strZoneTables;
    }
  }

  Future<void> addEmptytable() async {
    final x = await future;
    final id = await _generateTaskID();
    final floors = x.map((x1) => x1.floor).toSet();
    if (floors.isEmpty) {
      state = AsyncData([...x, StrZoneTable(strZoneId: id)]);
    } else {
      String newFloor = '${floors.last}_1';
      while (floors.contains(newFloor)) {
        newFloor = '${newFloor}_1';
      }
      state = AsyncData([...x, StrZoneTable(floor: newFloor, strZoneId: id)]);
    }
  }

  Future<void> deleteTable(String id) async {
    // print("Before deletion: ${state.map((item) => item.usage).toList()}");
    final x = await future;
    x.removeWhere((item) => item.strZoneId == id);
    // print("After deletion: ${x.map((item) => item.usage).toList()}");
    state = AsyncData(x);
  }

  // Future<void> insertEmptyTable(int index) async {
  //   final x = await future;
  //   final id = await _generateTaskID();
  //   x.insert(index + 1, LoadingTable(loadingTableId: id));
  //   state = AsyncData(x);
  // }

  Future<void> copyAndInsertTable(int index) async {
    final x = await future;
    final newId = await _generateTaskID();
    final floors = x.map((x1) => x1.floor).toList();
    String newFloor = '${floors[index]}_1';
    while (floors.contains(newFloor)) {
      newFloor = '${newFloor}_1';
    }
    x.insert(index + 1, x[index].copyWith(floor: newFloor, strZoneId: newId));
    state = AsyncData(x);
  }

  Future<String> _generateTaskID() async {
    String newID = nanoid(alphabet: Alphabet.noDoppelgangerSafe);
    final x = await future;
    final Set<String> strZoneTableIds = x.map((item) => item.strZoneId).toSet();
    // final Set<String> taskIDs = ref.read(_taskIDsProvider);
    while (strZoneTableIds.contains(newID)) {
      newID = nanoid(alphabet: Alphabet.noDoppelgangerSafe);
    }
    return newID;
  }

  Future<void> updateTable(
    String id, {
    String? floor,
    double? height,
    double? finish,
    double? service,
    double? clear,
    int? nosOfFloor,
  }) async {
    final x = await future;
    final newState =
        x.map((item) {
          if (item.strZoneId == id) {
            return item.copyWith(
              floor: floor ?? item.floor,
              height: height ?? item.height,
              finish: finish ?? item.finish,
              service: service ?? item.service,
              clear: clear ?? item.clear,
              nosOfFloor: nosOfFloor ?? item.nosOfFloor,
              strZoneId: id,
            );
          } else {
            return item;
          }
        }).toList();
    state = AsyncData(newState);
  }
}
