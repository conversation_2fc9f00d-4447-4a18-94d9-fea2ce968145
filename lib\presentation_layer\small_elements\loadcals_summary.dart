import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';

//domain layer
import '../../domain_layer/preferences.dart';

// presentation layer
import '../screen/homescreen.dart';
// import '../small_elements/custom_stateful_double_input.dart';
// import '../small_elements/custom_stateful_text_input.dart';
// import '../small_elements/global_data_ui.dart';

//misc

class LoadCalsSummary extends ConsumerStatefulWidget {
  const LoadCalsSummary({
    this.isExpanded,
    this.maxHeight,
    this.minHeight,
    super.key,
  });

  final bool? isExpanded;
  final double? maxHeight;
  final double? minHeight;

  @override
  ConsumerState<ConsumerStatefulWidget> createState() =>
      _LoadCalsSummaryState();
}

class _LoadCalsSummaryState extends ConsumerState<LoadCalsSummary>
    with WidgetsBindingObserver {
  late bool _isExpanded;
  late double _maxHeight;
  late double _minHeight;
  late ScrollController _scrollController;

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void initState() {
    WidgetsBinding.instance.addObserver(this);
    _isExpanded = widget.isExpanded ?? false;
    _maxHeight = widget.maxHeight ?? 300;
    _minHeight = widget.minHeight ?? 50;
    _scrollController = ScrollController();
    // _updateHeights();

    super.initState();
  }

  @override
  void didChangeDependencies() {
    _updateHeights();
    super.didChangeDependencies();
    // Update heights when dependencies change, including after initState
  }

  @override
  void didChangeMetrics() {
    // This method is called when the metrics change (like resizing the window)
    setState(() {
      _updateHeights();
    });
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    final loadingTables = ref.watch(loadingTablesControllerProvider);
    final globalData = ref.watch(globalDataControllerProvider);
    final scrollController = ScrollController();

    final NumberFormat formatter1 = NumberFormat('0.0');
    late List<String> unit;

    return loadingTables.when(
      data: (tables) {
        //start reutnr real stuffs when loading tables available
        return globalData.when(
          //also, start reutnr real stuffs when global data tables available
          data: (data) {
            late int indexOfMaxTable;
            final String f1 = data.sdlFactor.toString();
            final String f2 = data.llFactor.toString();
            switch (data.unit) {
              case 'metrics':
                unit = PreferredUnit.metrics;
                break;
              case 'imperial':
                unit = PreferredUnit.imperial;
                break;
              default:
                unit = PreferredUnit.metrics;
            }
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding: const EdgeInsets.fromLTRB(10, 0, 10, 0),
                  child: Divider(),
                ),
                Padding(
                  padding: const EdgeInsets.fromLTRB(10, 0, 0, 0),
                  child: Align(
                    alignment: Alignment.centerLeft,
                    child: Row(
                      children: [
                        IconButton(
                          icon: Icon(
                            _isExpanded ? Icons.expand_less : Icons.expand_more,
                          ),
                          color: colorScheme.onSurface,
                          onPressed: () {
                            setState(() {
                              _isExpanded = !_isExpanded;
                            });
                          },
                        ),
                        Text(
                          'Loading Summary ',
                          style: textTheme.titleLarge!.copyWith(
                            color: colorScheme.onSurface,
                          ),
                        ),
                        Container(
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(5.0),
                            color: colorScheme.surfaceContainer.withAlpha(225),
                            border: Border.all(
                              color: Colors.black.withAlpha(125),
                            ),
                          ),
                          child: Padding(
                            padding: const EdgeInsets.all(2.0),
                            child: Text(
                              '(Total: ${tables.length})',
                              style: textTheme.labelMedium!.copyWith(
                                color: colorScheme.onSurfaceVariant.withAlpha(
                                  225,
                                ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.fromLTRB(10, 0, 10, 0),
                  child: Divider(),
                ),

                ClipRect(
                  child: ConstrainedBox(
                    constraints: BoxConstraints(
                      maxHeight: _isExpanded ? _maxHeight : 0,
                    ),
                    child: Scrollbar(
                      controller: _scrollController,
                      thumbVisibility: true,
                      trackVisibility: false,
                      child: SingleChildScrollView(
                        controller: _scrollController,
                        child: Column(
                          children: [
                            Builder(
                              builder: (context) {
                                if (tables.isNotEmpty) {
                                  final w = data.finishUnitWeight;
                                  final f1 = data.sdlFactor;
                                  final f2 = data.llFactor;
                                  final maxTable = tables.reduce((current, next) {
                                    final currentLoad =
                                        f1 *
                                            (current.finish * w / 1000 +
                                                current.service) +
                                        f2 * current.liveLoad;
                                    final nextLoad =
                                        f1 *
                                            (next.finish * w / 1000 +
                                                next.service) +
                                        f2 * next.liveLoad;
                                    return currentLoad < nextLoad ? next : current;
                                  });
                                  indexOfMaxTable = tables.indexOf(maxTable);
                                  late List<String> unit;
                                  switch (data.unit) {
                                    case 'metrics':
                                      unit = PreferredUnit.metrics;
                                      break;
                                    case 'imperial':
                                      unit = PreferredUnit.imperial;
                                      break;
                                    default:
                                      unit = PreferredUnit.metrics;
                                  }
                                  final double sdl =
                                      maxTable.service +
                                      maxTable.finish *
                                          data.finishUnitWeight /
                                          1000;
                                  final double ll = maxTable.liveLoad;
                                  final double total = sdl + ll;
                                  final double factoredTotal =
                                      data.sdlFactor * sdl + data.llFactor * ll;
                                  return DefaultTextStyle(
                                    style: textTheme.labelMedium!.copyWith(
                                      color: colorScheme.onErrorContainer,
                                    ),
                                    child: Padding(
                                      padding: const EdgeInsets.fromLTRB(
                                        10,
                                        0,
                                        10,
                                        0,
                                      ),
                                      child: Container(
                                        decoration: BoxDecoration(
                                          borderRadius: BorderRadius.circular(5.0),
                                          color: colorScheme.errorContainer
                                              .withAlpha(100),
                                          border: Border.all(
                                            width: 0.5,
                                            color: colorScheme.error.withAlpha(100),
                                          ),
                                        ),
                                        child: Padding(
                                          padding: const EdgeInsets.all(4.0),
                                          child: Row(
                                            mainAxisAlignment:
                                                MainAxisAlignment.start,
                                            mainAxisSize: MainAxisSize.min,
                                            children: [
                                              Container(
                                                decoration: BoxDecoration(
                                                  shape: BoxShape.circle,
                                                  border: Border.all(
                                                    color:
                                                        colorScheme
                                                            .onErrorContainer,
                                                  ),
                                                ),
                                                child: Padding(
                                                  padding: const EdgeInsets.all(
                                                    8.0,
                                                  ),
                                                  child: Text(
                                                    '${indexOfMaxTable + 1}',
                                                    style: textTheme.titleSmall!
                                                        .copyWith(
                                                          color:
                                                              colorScheme
                                                                  .onErrorContainer,
                                                        ),
                                                  ),
                                                ),
                                              ),
                                              SizedBox(width: 15.0),
                                              Column(
                                                children: [
                                                  Text(
                                                    'Max Load',
                                                    style: textTheme.labelMedium!
                                                        .copyWith(
                                                          color: colorScheme.error,
                                                        ),
                                                  ),
                                                  Text(
                                                    maxTable.usage,
                                                    style: textTheme.labelMedium!
                                                        .copyWith(
                                                          color: colorScheme.error,
                                                        ),
                                                  ),
                                                ],
                                              ),
                                              SizedBox(width: 15.0),
                                              Column(
                                                mainAxisSize: MainAxisSize.min,
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                children: [
                                                  Text(
                                                    '| SDL: ${NumberFormat('0.0').format(sdl)} [${unit[1]}]',
                                                  ),
                                                  Text(
                                                    '| LL: ${NumberFormat('0.0').format(ll)} [${unit[1]}]',
                                                  ),
                                                ],
                                              ),
                                              SizedBox(width: 15.0),
                                              Column(
                                                mainAxisSize: MainAxisSize.min,
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                children: [
                                                  Text(
                                                    '| SDL+LL: ${NumberFormat('0.0').format(total)} [${unit[1]}]',
                                                  ),
                                                  Text(
                                                    '| ${f1}SDL+${f2}LL: ${NumberFormat('0.0').format(factoredTotal)} [${unit[1]}]',
                                                    style: textTheme.labelMedium!
                                                        .copyWith(
                                                          color: colorScheme.error,
                                                        ),
                                                  ),
                                                ],
                                              ),
                                            ],
                                          ),
                                        ),
                                      ),
                                    ),
                                  );
                                } else {
                                  return Padding(
                                    padding: const EdgeInsets.fromLTRB(
                                      10,
                                      0,
                                      10,
                                      0,
                                    ),
                                    child: Container(
                                      decoration: BoxDecoration(
                                        borderRadius: BorderRadius.circular(5.0),
                                        color: colorScheme.errorContainer.withAlpha(
                                          100,
                                        ),
                                        border: Border.all(
                                          width: 0.5,
                                          color: colorScheme.error.withAlpha(100),
                                        ),
                                      ),
                                      child: Padding(
                                        padding: const EdgeInsets.all(4.0),
                                        child: Text(
                                          'No Loading Data',
                                          style: textTheme.labelMedium!.copyWith(
                                            color: colorScheme.error,
                                          ),
                                        ),
                                      ),
                                    ),
                                  );
                                }
                              },
                            ),
                        
                            _itemBlockWithTable(
                              backgroundColor: Colors.transparent,
                              borderColor: Colors.transparent,
                              '',
                              [
                                'ID',
                                'Usage',
                                'SDL [${unit[1]}]',
                                'LL [${unit[1]}]',
                                'SDL+LL [${unit[1]}]',
                                '${f1}SDL+${f2}LL [${unit[1]}]',
                              ],
                              [
                                ...List.generate(tables.length, (index) {
                                  final double sdl =
                                      tables[index].service +
                                      tables[index].finish *
                                          data.finishUnitWeight /
                                          1000;
                                  final double ll = tables[index].liveLoad;
                                  final double total = sdl + ll;
                                  final double factoredTotal =
                                      data.sdlFactor * sdl + data.llFactor * ll;
                                  return [
                                    '${index + 1}',
                                    '${tables[index].usage}',
                                    '${formatter1.format(sdl)}',
                                    '${formatter1.format(ll)}',
                                    '${formatter1.format(total)}',
                                    '${formatter1.format(factoredTotal)}',
                                  ];
                                }),
                              ],
                            ),
                        
                            // Flexible(
                            //   child: Scrollbar(
                            //     controller: scrollController,
                            //     thumbVisibility: true,
                            //     child: DefaultTextStyle(
                            //       style: textTheme.labelMedium!.copyWith(
                            //         color: colorScheme.onSurface,
                            //       ),
                            //       child: ListView.builder(
                            //         controller: scrollController,
                            //         itemCount: tables.length,
                            //         itemBuilder: (context, index) {
                            //           final double sdl =
                            //               tables[index].service +
                            //               tables[index].finish *
                            //                   data.finishUnitWeight /
                            //                   1000;
                            //           final double ll = tables[index].liveLoad;
                            //           final double total = sdl + ll;
                            //           final double factoredTotal =
                            //               data.sdlFactor * sdl + data.llFactor * ll;
                            //           return Padding(
                            //             padding: const EdgeInsets.fromLTRB(
                            //               8.0,
                            //               4.0,
                            //               8.0,
                            //               4.0,
                            //             ),
                            //             child: Row(
                            //               children: [
                            //                 Container(
                            //                   decoration: BoxDecoration(
                            //                     shape: BoxShape.circle,
                            //                     color: colorScheme.surfaceContainer
                            //                         .withAlpha(200),
                            //                     // borderRadius: BorderRadius.circular(5),
                            //                     border: Border.all(
                            //                       color: Colors.black,
                            //                     ),
                            //                   ),
                            //                   child: Padding(
                            //                     padding: const EdgeInsets.all(4.0),
                            //                     child: Text(
                            //                       '${index + 1}',
                            //                       style: textTheme.labelLarge!
                            //                           .copyWith(
                            //                             color: colorScheme
                            //                                 .onSurfaceVariant
                            //                                 .withAlpha(250),
                            //                           ),
                            //                     ),
                            //                   ),
                            //                 ),
                            //                 IconButton(
                            //                   icon: Icon(
                            //                     Icons.delete,
                            //                     color: colorScheme.onSurface,
                            //                   ),
                            //                   onPressed: () {
                            //                     ref
                            //                         .read(
                            //                           loadingTablesControllerProvider
                            //                               .notifier,
                            //                         )
                            //                         .deleteTable(
                            //                           tables[index].loadingTableId,
                            //                         );
                            //                   },
                            //                 ),
                            //                 Flexible(
                            //                   child: Row(
                            //                     children: [
                            //                       Text(tables[index].usage),
                            //                       SizedBox(width: 15.0),
                            //                       Column(
                            //                         mainAxisSize: MainAxisSize.min,
                            //                         crossAxisAlignment:
                            //                             CrossAxisAlignment.start,
                            //                         children: [
                            //                           Text(
                            //                             '| SDL: ${sdl.toStringAsFixed(2)} [${unit[1]}]',
                            //                           ),
                            //                           Text(
                            //                             '| LL: ${ll.toStringAsFixed(2)} [${unit[1]}]',
                            //                           ),
                            //                         ],
                            //                       ),
                            //                       SizedBox(width: 15.0),
                            //                       Column(
                            //                         mainAxisSize: MainAxisSize.min,
                            //                         crossAxisAlignment:
                            //                             CrossAxisAlignment.start,
                            //                         children: [
                            //                           Text(
                            //                             '| SDL+LL: ${total.toStringAsFixed(2)} [${unit[1]}]',
                            //                           ),
                            //                           Text(
                            //                             '| ${f1}SDL+${f2}LL: ${factoredTotal.toStringAsFixed(2)} [${unit[1]}]',
                            //                           ),
                            //                         ],
                            //                       ),
                            //                     ],
                            //                   ),
                            //                 ),
                            //                 SizedBox(width: 5.0),
                            //               ],
                            //             ),
                            //           );
                            //         },
                            //       ),
                            //     ),
                            //   ),
                            // ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            );
          },
          error: (error, stackTrace) => Text(error.toString()),
          loading: () => CircularProgressIndicator(),
        );
      },
      error: (error, stackTrace) => Text(error.toString()),
      loading: () => CircularProgressIndicator(),
    );
  }

  Widget _itemBlockWithTable(
    String requirementTitle,
    List<String> requirementSutitle,
    List<List<String>> requirementDetails, {
    Color? backgroundColor,
    Color? borderColor,
  }) {
    //*initialize
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;
    backgroundColor ??= colorScheme.primaryContainer.withAlpha(50);
    borderColor ??= colorScheme.primary.withAlpha(50);

    return Padding(
      padding: const EdgeInsets.fromLTRB(0, 8, 0, 8),
      child: Container(
        decoration: BoxDecoration(
          color: backgroundColor,
          borderRadius: BorderRadius.circular(8.0),
          border: Border.all(width: 1.5, color: borderColor),
        ),
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: DefaultTextStyle(
            style: textTheme.bodyMedium!.copyWith(
              color: colorScheme.onPrimaryContainer,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                requirementTitle.isEmpty
                    ? SizedBox.shrink()
                    : Text(
                      requirementTitle,
                      style: textTheme.labelLarge!.copyWith(
                        color: colorScheme.onPrimaryContainer,
                      ),
                    ),

                SizedBox(height: 5.0),

                Row(
                  children: [
                    Expanded(
                      flex: 2,
                      child: Table(
                        defaultVerticalAlignment:
                            TableCellVerticalAlignment.middle,
                        border: TableBorder.all(
                          color: colorScheme.onPrimaryContainer.withAlpha(50),
                          width: 0.75,
                        ),
                        // columnWidths: const {
                        //   0: FlexColumnWidth(2),
                        //   1: FlexColumnWidth(3),
                        // },
                        children: [
                          TableRow(
                            decoration: BoxDecoration(
                              color: colorScheme.primaryContainer.withAlpha(
                                200,
                              ),
                            ),
                            children: [
                              ...List.generate(requirementSutitle.length, (
                                index,
                              ) {
                                return Align(
                                  child: Padding(
                                    padding: const EdgeInsets.all(4.0),
                                    child: Text(
                                      requirementSutitle[index],
                                      style: textTheme.labelLarge!.copyWith(
                                        color: colorScheme.onPrimaryContainer,
                                      ),
                                    ),
                                  ),
                                );
                              }),
                            ],
                          ),
                          ...List.generate(requirementDetails.length, (index) {
                            return TableRow(
                              decoration: BoxDecoration(
                                color: colorScheme.primaryContainer.withAlpha(
                                  50,
                                ),
                              ),
                              children: [
                                ...List.generate(
                                  requirementDetails[index].length,
                                  (index2) {
                                    return Align(
                                      child: Padding(
                                        padding: const EdgeInsets.all(4.0),
                                        child: Text(
                                          requirementDetails[index][index2],
                                          style: textTheme.bodyMedium!.copyWith(
                                            color:
                                                colorScheme.onPrimaryContainer,
                                          ),
                                        ),
                                      ),
                                    );
                                  },
                                ),
                              ],
                            );
                          }),
                        ],
                      ),
                    ),
                    SizedBox(width: 10.0),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // Method to update heights based on the current media query
  void _updateHeights() {
    final double screenHeight = MediaQuery.of(context).size.height;
    _maxHeight =
        (widget.maxHeight == null)
            ? screenHeight * 0.6
            : (widget.maxHeight! > screenHeight * 0.6)
            ? screenHeight * 0.6
            : widget.maxHeight!;

    _minHeight =
        (widget.minHeight == null)
            ? screenHeight * 0.1
            : (widget.minHeight! > screenHeight * 0.1)
            ? screenHeight * 0.1
            : widget.minHeight!;

    // Adjust _maxHeight if needed

    if (_maxHeight < _minHeight) {
      _maxHeight = _minHeight;
    }
  }
}
