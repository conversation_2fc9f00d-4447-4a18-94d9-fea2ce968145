import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:structify/domain_layer/column_scheme_data.dart';
import 'package:structify/domain_layer/preferences.dart';

//presentation layer
import '../../misc/show_overlay_msg.dart';
import 'button/function_button.dart';
import 'input/custom_stateful_double_input.dart';
import '../screen/homescreen.dart';
import 'button/selection_button.dart';

class PileEndBearingBoredInputUi extends ConsumerStatefulWidget {
  const PileEndBearingBoredInputUi({
    this.isExpanded,
    this.maxHeight,
    this.minHeight,
    super.key,
  });

  final bool? isExpanded;
  final double? maxHeight;
  final double? minHeight;

  @override
  ConsumerState<ConsumerStatefulWidget> createState() =>
      _PileEndBearingBoredInputUiState();
}

class _PileEndBearingBoredInputUiState
    extends ConsumerState<PileEndBearingBoredInputUi>
    with WidgetsBindingObserver {
  late bool _isExpanded;
  late double _maxHeight;
  late double _minHeight;

  late GlobalKey _buttonKey;

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void initState() {
    WidgetsBinding.instance.addObserver(this);
    _isExpanded = widget.isExpanded ?? false;
    _maxHeight = widget.maxHeight ?? 210;
    _minHeight = widget.minHeight ?? 50;
    _buttonKey = GlobalKey();
    // _updateHeights();

    super.initState();
  }

  @override
  void didChangeDependencies() {
    _updateHeights();
    super.didChangeDependencies();
    // Update heights when dependencies change, including after initState
  }

  @override
  void didChangeMetrics() {
    // This method is called when the metrics change (like resizing the window)
    setState(() {
      _updateHeights();
    });
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    final data1 = ref.watch(globalDataControllerProvider);
    final data2 = ref.watch(pileEndBearingBoredInputControllerProvider);
    final data3 = ref.watch(columnSchemeDataControllerProvider);
    final data4 = ref.watch(pileEndBearingBoredInputGlobalControllerProvider);
    final int containerOpacity = 175;
    final int textOpacity = 225;

    return data1.when(
      data: (globalData) {
        return data2.when(
          data: (pileInput) {
            return data3.when(
              data: (columnData) {
                return data4.when(
                  data: (pileInputGlobal) {
                    late final List<String> unit;
                    switch (globalData.unit) {
                      case 'metrics':
                        unit = PreferredUnit.metrics;
                        break;
                      case 'imperial':
                        unit = PreferredUnit.imperial;
                        break;
                      default:
                        unit = PreferredUnit.metrics;
                        break;
                    }

                    return Column(
                      children: [
                        Padding(
                          padding: const EdgeInsets.fromLTRB(10, 0, 10, 0),
                          child: Divider(),
                        ),
                        Padding(
                          padding: const EdgeInsets.fromLTRB(10, 0, 0, 0),
                          child: Align(
                            alignment: Alignment.centerLeft,
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Wrap(
                                  crossAxisAlignment: WrapCrossAlignment.center,
                                  children: [
                                    IconButton(
                                      icon: Icon(
                                        _isExpanded
                                            ? Icons.expand_less
                                            : Icons.expand_more,
                                      ),
                                      color: colorScheme.onSurface,
                                      onPressed: () {
                                        setState(() {
                                          _isExpanded = !_isExpanded;
                                        });
                                      },
                                    ),
                                    Text(
                                      'End Bearing Bored Pile Scheme Input',
                                      style: textTheme.titleLarge!.copyWith(
                                        color: colorScheme.onSurface,
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                        ),
                        Padding(
                          padding: const EdgeInsets.fromLTRB(10, 0, 10, 0),
                          child: Divider(),
                        ),
                        ClipRect(
                          child: ConstrainedBox(
                            constraints: BoxConstraints(
                              maxHeight: _isExpanded ? _maxHeight : 0,
                            ),
                            child: SingleChildScrollView(
                              child: Column(
                                mainAxisSize: MainAxisSize.min,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Padding(
                                    padding: const EdgeInsets.fromLTRB(
                                      8.0,
                                      0.0,
                                      8.0,
                                      0.0,
                                    ),
                                    child: Container(
                                      decoration: BoxDecoration(
                                        borderRadius: BorderRadius.circular(
                                          10.0,
                                        ),
                                        border: Border.all(
                                          color: Colors.grey.withAlpha(150),
                                        ),
                                        color: colorScheme.secondaryContainer
                                            .withAlpha(containerOpacity),
                                      ),
                                      child: Padding(
                                        padding: const EdgeInsets.all(4.0),
                                        child: Text(
                                          'Pile Data',
                                          style: textTheme.titleSmall!.copyWith(
                                            color: colorScheme
                                                .onSecondaryContainer
                                                .withAlpha(textOpacity),
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                                  SizedBox(height: 5.0),
                                  Padding(
                                    padding: const EdgeInsets.fromLTRB(
                                      8.0,
                                      4.0,
                                      8.0,
                                      4.0,
                                    ),
                                    child: Row(
                                      mainAxisSize: MainAxisSize.min,
                                      mainAxisAlignment:
                                          MainAxisAlignment.start,
                                      children: [
                                        Flexible(
                                          child: CustomStatefulDoubleInput(
                                            title: 'Safe Bearing [${unit[1]}]',
                                            value: pileInput.safeBearing,
                                            tooltipText:
                                                'If using HK Code 2017:\n'
                                                'cat(1a): 10000 [kPa] | Grade I (Fresh to slightly decomposed strong granite/volcanic rock) | TCR 100% | UCS >= 75MPa or PLI50 >= 3MPa\n'
                                                'cat(1b): 7500 [kPa] | Grade II  (Fresh to slightly decomposed strong granite/volcanic rock ) | TCR 95% | UCS >= 50MPa or PLI50 >= 2MPa\n'
                                                'cat(1c): 5000 [kPa] | Grade III (Slightly to moderately decomposed moderately string granite/volcanic rock) | TCR 85% | UCS >= 25MPa or PLI50 >= 1MPa\n'
                                                'cat(1d): 3000 [kPa] | Grade III (Moderately decomposed moderately strong to moderately weak granite/volcanic rock) | TCR 50% \n'
                                                'cat(2): 3000 [kPa] | Grade III (Moderately decomposed moderately strong to moderately weak meta-sedimentary rock) | TCR 85%\n'
                                                'cat(3): 1000 [kPa] | Grade V (Highly to completely decomposed moderately weak to weak rock) | SPT N-Value >= 200\n',
                                            onChanged: (value) async {
                                              await ref
                                                  .read(
                                                    pileEndBearingBoredInputControllerProvider
                                                        .notifier,
                                                  )
                                                  .updateTable(
                                                    safeBearing: value,
                                                  );
                                            },
                                          ),
                                        ),
                                        SizedBox(width: 5.0),
                                        Flexible(
                                          child: CustomStatefulDoubleInput(
                                            title: 'FOS',
                                            value: pileInput.fos,
                                            onChanged: (value) async {
                                              await ref
                                                  .read(
                                                    pileEndBearingBoredInputControllerProvider
                                                        .notifier,
                                                  )
                                                  .updateTable(fos: value);
                                            },
                                          ),
                                        ),
                                        Flexible(child: SizedBox(width: 5.0)),
                                        Flexible(child: SizedBox(width: 5.0)),
                                      ],
                                    ),
                                  ),
                                  SizedBox(height: 5.0),
                                  Padding(
                                    padding: const EdgeInsets.fromLTRB(
                                      8.0,
                                      4.0,
                                      8.0,
                                      4.0,
                                    ),
                                    child: Row(
                                      mainAxisSize: MainAxisSize.min,
                                      mainAxisAlignment:
                                          MainAxisAlignment.start,
                                      children: [
                                        Flexible(
                                          child: CustomStatefulDoubleInput(
                                            title: 'fcu [${unit[5]}]',
                                            value: pileInput.fcu,
                                            onChanged: (value) async {
                                              await ref
                                                  .read(
                                                    pileEndBearingBoredInputControllerProvider
                                                        .notifier,
                                                  )
                                                  .updateTable(fcu: value);
                                            },
                                          ),
                                        ),
                                        SizedBox(width: 5.0),

                                        Flexible(
                                          child: CustomStatefulDoubleInput(
                                            title:
                                                'Max Pile Diameter [${unit[4]}]',
                                            value: pileInput.maxPileDiameter,
                                            onChanged: (value) async {
                                              await ref
                                                  .read(
                                                    pileEndBearingBoredInputControllerProvider
                                                        .notifier,
                                                  )
                                                  .updateTable(
                                                    maxPileDiameter: value,
                                                  );
                                            },
                                          ),
                                        ),
                                        SizedBox(width: 5.0),
                                        Flexible(
                                          child: CustomStatefulDoubleInput(
                                            title: 'Max Steel Ratio',
                                            value: pileInput.maxSteelRatio,
                                            onChanged: (value) async {
                                              await ref
                                                  .read(
                                                    pileEndBearingBoredInputControllerProvider
                                                        .notifier,
                                                  )
                                                  .updateTable(
                                                    maxSteelRatio: value,
                                                  );
                                            },
                                          ),
                                        ),
                                        Flexible(child: SizedBox(width: 5.0)),
                                      ],
                                    ),
                                  ),
                                  Padding(
                                    padding: const EdgeInsets.fromLTRB(
                                      8.0,
                                      4.0,
                                      8.0,
                                      4.0,
                                    ),
                                    child: Row(
                                      mainAxisSize: MainAxisSize.min,
                                      mainAxisAlignment:
                                          MainAxisAlignment.start,
                                      children: [
                                        Flexible(
                                          child: CustomStatefulDoubleInput(
                                            title: 'Ratio of Bellout to Shaft',
                                            tooltipText:
                                                'Ratio of Bellout Diameter to Shaft Diameter.\nHK Code Limit: 1.65',
                                            value: pileInput.ratioOfBelloutDia,
                                            onChanged: (value) async {
                                              await ref
                                                  .read(
                                                    pileEndBearingBoredInputControllerProvider
                                                        .notifier,
                                                  )
                                                  .updateTable(
                                                    ratioOfBelloutDia: value,
                                                  );
                                            },
                                          ),
                                        ),
                                        SizedBox(width: 5.0),
                                        Flexible(
                                          child: CustomStatefulDoubleInput(
                                            readOnly:
                                                pileInput.useSelectColLoad,
                                            title: 'SLS Load [${unit[0]}]',
                                            value: pileInput.slsLoad,
                                            tooltipText:
                                                'load per pile, not total Load',
                                            onChanged: (value) async {
                                              await ref
                                                  .read(
                                                    pileEndBearingBoredInputControllerProvider
                                                        .notifier,
                                                  )
                                                  .updateTable(slsLoad: value);
                                            },
                                          ),
                                        ),
                                        SizedBox(width: 5.0),
                                        Flexible(
                                          child: CustomStatefulDoubleInput(
                                            title: 'ULS Load [${unit[0]}]',
                                            readOnly:
                                                pileInput.useSelectColLoad,
                                            tooltipText:
                                                'load per pile, not total Load',
                                            value: pileInput.ulsLoad,
                                            onChanged: (value) async {
                                              await ref
                                                  .read(
                                                    pileEndBearingBoredInputControllerProvider
                                                        .notifier,
                                                  )
                                                  .updateTable(ulsLoad: value);
                                            },
                                          ),
                                        ),
                                        Flexible(child: SizedBox(width: 5.0)),
                                      ],
                                    ),
                                  ),
                                  SizedBox(height: 5),
                                  Padding(
                                    padding: const EdgeInsets.fromLTRB(
                                      8.0,
                                      0.0,
                                      8.0,
                                      0.0,
                                    ),
                                    child: Container(
                                      decoration: BoxDecoration(
                                        borderRadius: BorderRadius.circular(
                                          10.0,
                                        ),
                                        border: Border.all(
                                          color: Colors.grey.withAlpha(150),
                                        ),
                                        color: colorScheme.secondaryContainer
                                            .withAlpha(containerOpacity),
                                      ),
                                      child: Padding(
                                        padding: const EdgeInsets.all(4.0),
                                        child: Text(
                                          'Iteration Parameters',
                                          style: textTheme.titleSmall!.copyWith(
                                            color: colorScheme
                                                .onSecondaryContainer
                                                .withAlpha(textOpacity),
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                                  SizedBox(height: 5),
                                  Padding(
                                    padding: const EdgeInsets.fromLTRB(
                                      8.0,
                                      4.0,
                                      8.0,
                                      4.0,
                                    ),
                                    child: Row(
                                      mainAxisSize: MainAxisSize.min,
                                      mainAxisAlignment:
                                          MainAxisAlignment.start,
                                      children: [
                                        Flexible(
                                          child: CustomStatefulDoubleInput(
                                            title:
                                                'Diameter Delta [${unit[4]}]',
                                            tooltipText:
                                                'Increament of Pile Diameter for each iteration',
                                            value: pileInput.diaIncrement,
                                            onChanged: (value) async {
                                              await ref
                                                  .read(
                                                    pileEndBearingBoredInputControllerProvider
                                                        .notifier,
                                                  )
                                                  .updateTable(
                                                    diaIncrement: value,
                                                  );
                                            },
                                          ),
                                        ),

                                        Flexible(child: SizedBox(width: 5.0)),
                                        Flexible(child: SizedBox(width: 5.0)),
                                        Flexible(child: SizedBox(width: 5.0)),
                                      ],
                                    ),
                                  ),
                                  SizedBox(height: 5),
                                  Padding(
                                    padding: const EdgeInsets.fromLTRB(
                                      8.0,
                                      0.0,
                                      8.0,
                                      0.0,
                                    ),
                                    child: Row(
                                      children: [
                                        SelectionButton(
                                          labelTextStyle:
                                              pileInput.useSelectColLoad
                                                  ? textTheme.labelLarge!.copyWith(
                                                    color:
                                                        colorScheme
                                                            .onTertiaryContainer,
                                                  )
                                                  : textTheme.labelLarge!
                                                      .copyWith(
                                                        color:
                                                            colorScheme
                                                                .onSurface,
                                                      ),
                                          labelText:
                                              'Use Load from\nSelected Column',
                                          pressedColor:
                                              colorScheme.tertiaryContainer,
                                          bgColor:
                                              pileInput.useSelectColLoad
                                                  ? colorScheme
                                                      .tertiaryContainer
                                                  : colorScheme
                                                      .surfaceContainer,

                                          onTap: (value) async {
                                            final input = await ref.read(
                                              pileEndBearingBoredInputControllerProvider
                                                  .future,
                                            );
                                            final colData = await ref.read(
                                              columnSchemeDataControllerProvider
                                                  .future,
                                            );

                                            await ref
                                                .read(
                                                  pileEndBearingBoredInputControllerProvider
                                                      .notifier,
                                                )
                                                .updateTable(
                                                  useSelectColLoad:
                                                      !input.useSelectColLoad,
                                                );
                                            //* Tricky here: if the new toggled value (!input.useSlabSelected) is true,
                                            //* we run below logic
                                            if (!input.useSelectColLoad) {
                                              final selectedColumn = colData
                                                  .firstWhere(
                                                    (scheme) =>
                                                        scheme.isSelected,
                                                    orElse:
                                                        () =>
                                                            ColumnSchemeData(),
                                                  );
                                              await ref
                                                  .read(
                                                    pileEndBearingBoredInputControllerProvider
                                                        .notifier,
                                                  )
                                                  .updateTable(
                                                    slsLoad:
                                                        selectedColumn.slsLoad /
                                                        pileInputGlobal
                                                            .colLoadFactor,
                                                    ulsLoad:
                                                        selectedColumn.ulsLoad /
                                                        pileInputGlobal
                                                            .colLoadFactor,
                                                  );
                                            }
                                          },
                                        ),
                                        SizedBox(width: 5.0),
                                        Flexible(
                                          child: CustomStatefulDoubleInput(
                                            readOnly:
                                                !pileInput.useSelectColLoad,
                                            title: 'Col Load Dividor',
                                            tooltipText:
                                                'Factor to adjust the col load\n(and hence the load per pile)',
                                            value:
                                                pileInputGlobal.colLoadFactor,
                                            listener: (hasFocuse, value) async {
                                              if (!hasFocuse) {
                                                await ref
                                                    .read(
                                                      pileEndBearingBoredInputGlobalControllerProvider
                                                          .notifier,
                                                    )
                                                    .updateTable(
                                                      colLoadFactor: value,
                                                    );
                                              }
                                            },
                                          ),
                                        ),
                                        SizedBox(width: 5.0),
                                        Flexible(
                                          child: FunctionButton(
                                            key: _buttonKey,
                                            labelIcon: Icon(
                                              Icons.calculate_outlined,
                                            ),
                                            labelText: 'Run',
                                            onTap: (isPressed) async {
                                              final pileEndBearingBoredSchemeDataList =
                                                  await ref
                                                      .read(
                                                        pileEndBearingBoredDataControllerProvider
                                                            .notifier,
                                                      )
                                                      .pileEndBearingBoredScheming();
                                              if (pileEndBearingBoredSchemeDataList
                                                  .isEmpty) {
                                                showGentleMessageBox(
                                                  context,
                                                  _buttonKey,
                                                  'No scheme Available!',
                                                  containerColor: colorScheme
                                                      .errorContainer
                                                      .withAlpha(175),
                                                  textStyle: textTheme
                                                      .labelMedium!
                                                      .copyWith(
                                                        color: colorScheme
                                                            .onErrorContainer
                                                            .withAlpha(175),
                                                      ),
                                                );
                                              }
                                            },
                                          ),
                                        ),
                                        SizedBox(width: 5.0),
                                        Flexible(
                                          child: FunctionButton(
                                            labelIcon: Icon(
                                              Icons.delete_outlined,
                                            ),
                                            labelText: 'Clear Schemes',
                                            onTap: (isPressed) async {
                                              await ref
                                                  .read(
                                                    pileEndBearingBoredDataControllerProvider
                                                        .notifier,
                                                  )
                                                  .deleteAllTable();
                                            },
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ],
                    );
                  },
                  error: (error, loading) {
                    return Text('Error: $error');
                  },
                  loading: () {
                    return Text('Loading...');
                  },
                );
              },
              error: (error, loading) {
                return Text('Error: $error');
              },
              loading: () {
                return Text('Loading...');
              },
            );
          },
          error: (error, loading) {
            return Text('Error: $error');
          },
          loading: () {
            return Text('Loading...');
          },
        );
      },
      error: (error, loading) {
        return Text('Error: $error');
      },
      loading: () {
        return Text('Loading...');
      },
    );
  }

  void _updateHeights() {
    final double screenHeight = MediaQuery.of(context).size.height;
    _maxHeight =
        (widget.maxHeight == null)
            ? screenHeight * 0.6
            : (widget.maxHeight! > screenHeight * 0.6)
            ? screenHeight * 0.6
            : widget.maxHeight!;

    _minHeight =
        (widget.minHeight == null)
            ? screenHeight * 0.1
            : (widget.minHeight! > screenHeight * 0.1)
            ? screenHeight * 0.1
            : widget.minHeight!;

    // Adjust _maxHeight if needed

    if (_maxHeight < _minHeight) {
      _maxHeight = _minHeight;
    }
  }
}
