// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'cantilever_scheme_input.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_CantileverSchemeInput _$CantileverSchemeInputFromJson(
  Map<String, dynamic> json,
) => _CantileverSchemeInput(
  pointLoad: (json['pointLoad'] as num?)?.toDouble() ?? 0.0,
  distA: (json['distA'] as num?)?.toDouble() ?? 3.0,
  loadFromSelectedCol: json['loadFromSelectedCol'] as bool? ?? false,
  cantileverSchemeInputId: json['cantileverSchemeInputId'] as String? ?? '',
);

Map<String, dynamic> _$CantileverSchemeInputToJson(
  _CantileverSchemeInput instance,
) => <String, dynamic>{
  'pointLoad': instance.pointLoad,
  'distA': instance.distA,
  'loadFromSelectedCol': instance.loadFromSelectedCol,
  'cantileverSchemeInputId': instance.cantileverSchemeInputId,
};
