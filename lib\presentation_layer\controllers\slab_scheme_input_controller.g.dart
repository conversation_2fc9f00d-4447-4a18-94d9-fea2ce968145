// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'slab_scheme_input_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$slabSchemeInputControllerHash() =>
    r'692be4781e5c9a3d774b82441bcb022e204cc36f';

/// See also [SlabSchemeInputController].
@ProviderFor(SlabSchemeInputController)
final slabSchemeInputControllerProvider = AutoDisposeAsyncNotifierProvider<
  SlabSchemeInputController,
  SlabSchemeInput
>.internal(
  SlabSchemeInputController.new,
  name: r'slabSchemeInputControllerProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$slabSchemeInputControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$SlabSchemeInputController = AutoDisposeAsyncNotifier<SlabSchemeInput>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
