import 'dart:math';
import 'dart:convert';
// import 'dart:nativewrappers/_internal/vm/lib/math_patch.dart';

import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:structify/domain_layer/data_struct/str_force_struct.dart';

//below for printing
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:printing/printing.dart';

//domain layer
import '../../domain_layer/global_data.dart';
import '../../domain_layer/mixin/mixin_steel_str.dart';
import '../../domain_layer/mixin/mixin_tools_for_ui.dart';
import '../../domain_layer/preferences.dart';

// presentation layer
import '../../domain_layer/steel_cantilever_truss_scheme_data.dart';
import '../../domain_layer/steel_cantilever_truss_scheme_input.dart';
import '../../domain_layer/steel_cantilever_truss_scheme_input_global.dart';
import '../../domain_layer/steel_transfer_truss_scheme_data.dart';
import '../../domain_layer/steel_transfer_truss_scheme_input.dart';
import '../../domain_layer/steel_transfer_truss_scheme_input_global.dart';
import '../screen/homescreen.dart';
// import '../small_elements/custom_stateful_double_input.dart';
// import '../small_elements/custom_stateful_text_input.dart';
// import '../small_elements/global_data_ui.dart';

//misc
import '../../misc/custom_func.dart';

import 'chart/chart_transfer_beam_scheme.dart';
import 'sketch/draw_steel_cantilever_truss_loading.dart';
import 'sketch/draw_steel_transfer_truss_loading.dart';
import 'button/function_button.dart';

class SteelCantileverTrussSchemeSummary extends ConsumerStatefulWidget {
  const SteelCantileverTrussSchemeSummary({
    this.isExpanded,
    this.maxHeight,
    this.minHeight,
    super.key,
  });

  final bool? isExpanded;
  final double? maxHeight;
  final double? minHeight;

  @override
  ConsumerState<ConsumerStatefulWidget> createState() =>
      _SteelCantileverTrussSchemeSummaryState();
}

class _SteelCantileverTrussSchemeSummaryState
    extends ConsumerState<SteelCantileverTrussSchemeSummary>
    with WidgetsBindingObserver, SteelStrHK, MixinToolsForUI {
  late bool _isExpanded;
  late double _maxHeight;
  late double _minHeight;
  late ScrollController _scrollController;
  late final List<Map<String, dynamic>> _steelSections;

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void initState() {
    WidgetsBinding.instance.addObserver(this);
    _isExpanded = widget.isExpanded ?? false;
    _maxHeight = widget.maxHeight ?? 300;
    _minHeight = widget.minHeight ?? 50;
    _scrollController = ScrollController();
    // _updateHeights();

    super.initState();
  }

  @override
  void didChangeDependencies() {
    _updateHeights();
    super.didChangeDependencies();
    // Update heights when dependencies change, including after initState
  }

  @override
  void didChangeMetrics() {
    // This method is called when the metrics change (like resizing the window)
    setState(() {
      _updateHeights();
    });
  }

  @override
  Widget build(BuildContext context) {
    //* providers needs to be watched
    final loadingTables = ref.watch(loadingTablesControllerProvider);
    final globalData = ref.watch(globalDataControllerProvider);
    final steelCantileverTrussInputs = ref.watch(
      steelCantileverTrussSchemeInputControllerProvider,
    );
    final steelCantileverTrussInputsGlobal = ref.watch(
      steelCantileverTrussSchemeInputGlobalControllerProvider,
    );
    final steelCantileverTrussScheme = ref.watch(
      steelCantileverTrussSchemeDataControllerProvider,
    );

    //* initialize variables for easy use
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;
    late final Map<String, dynamic> calsLogMap;
    late final List<String> units;

    NumberFormat f0 = NumberFormat('0'),
        f1 = NumberFormat('0.0'),
        f3 = NumberFormat('0.000');

    return loadingTables.when(
      data: (tables) {
        //start reutnr real stuffs when loading tables available
        return globalData.when(
          //also, start reutnr real stuffs when global data tables available
          data: (data) {
            return steelCantileverTrussInputsGlobal.when(
              data: (inputsGlobal) {
                return steelCantileverTrussInputs.when(
                  data: (inputs) {
                    return steelCantileverTrussScheme.when(
                      data: (scheme) {
                        switch (data.unit) {
                          case 'metrics':
                            units = PreferredUnit.metrics;
                            break;
                          case 'imperial':
                            units = PreferredUnit.imperial;
                            break;
                          default:
                            units = PreferredUnit.metrics;
                            break;
                        }
                        //get calsLog Map
                        calsLogMap = extractCalsLog(scheme.calsLog);

                        // convert the beam force to FlSpot
                        final List<FlSpot> beamMd = [];
                        final List<FlSpot> beamVd = [];
                        final Map<String, dynamic> y = jsonDecode(
                          scheme.beamForce,
                        );
                        y.map((key, value) {
                          y[key] = StrForce.fromJson(value);
                          return MapEntry(key, value);
                        });

                        for (StrForce beamForce in y.values) {
                          beamMd.add(
                            FlSpot(
                              roundTo(beamForce.x, 1),
                              roundTo(beamForce.Md, 0),
                            ),
                          );
                          beamVd.add(
                            FlSpot(
                              roundTo(beamForce.x, 1),
                              roundTo(beamForce.Vd, 0),
                            ),
                          );
                        }
                        final failRegExp = RegExp(
                          r'fail',
                          caseSensitive: false,
                        );
                        return Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Padding(
                              padding: const EdgeInsets.fromLTRB(10, 0, 10, 0),
                              child: Divider(),
                            ),
                            Padding(
                              padding: const EdgeInsets.fromLTRB(10, 0, 0, 0),
                              child: Align(
                                alignment: Alignment.centerLeft,
                                child: Wrap(
                                  crossAxisAlignment: WrapCrossAlignment.center,
                                  children: [
                                    IconButton(
                                      icon: Icon(
                                        _isExpanded
                                            ? Icons.expand_less
                                            : Icons.expand_more,
                                      ),
                                      color: colorScheme.onSurface,
                                      onPressed: () {
                                        setState(() {
                                          _isExpanded = !_isExpanded;
                                        });
                                      },
                                    ),
                                    Text(
                                      'Steel Cantilever Truss Scheme Summary ',
                                      style: textTheme.titleLarge!.copyWith(
                                        color: colorScheme.onSurface,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                            Padding(
                              padding: const EdgeInsets.fromLTRB(10, 0, 10, 0),
                              child: Divider(),
                            ),

                            ConstrainedBox(
                              constraints: BoxConstraints(
                                maxHeight: _isExpanded ? _maxHeight : 0,
                              ),
                              child: Scrollbar(
                                controller: _scrollController,
                                thumbVisibility: true,
                                child: SingleChildScrollView(
                                  controller: _scrollController,
                                  child: Column(
                                    mainAxisSize: MainAxisSize.min,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      DefaultTextStyle(
                                        style: textTheme.labelMedium!.copyWith(
                                          color: colorScheme.onSurface,
                                        ),
                                        child: Row(
                                          mainAxisSize: MainAxisSize.max,
                                          mainAxisAlignment:
                                              MainAxisAlignment.start,
                                          children: [
                                            SizedBox(width: 10.0),
                                            Column(
                                              mainAxisSize: MainAxisSize.min,
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                SizedBox(width: 10.0),
                                                Builder(
                                                  builder: (context) {
                                                    if (!failRegExp.hasMatch(
                                                      scheme.calsLog,
                                                    )) {
                                                      return Text(
                                                        'STATUS:',
                                                        style: textTheme
                                                            .titleSmall!
                                                            .copyWith(
                                                              color:
                                                                  Colors
                                                                      .green[700],
                                                            ),
                                                      );
                                                    } else {
                                                      return Text(
                                                        'STATUS:',
                                                        style: textTheme
                                                            .titleSmall!
                                                            .copyWith(
                                                              color:
                                                                  colorScheme
                                                                      .error,
                                                            ),
                                                      );
                                                    }
                                                  },
                                                ),
                                                Column(
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.start,
                              
                                                  children: [
                                                    Text('Section: '),
                                                    Text(
                                                      'Str Zone Limit [${units[4]}]:',
                                                    ),
                                                    Text(
                                                      'Slab Thickness [${units[4]}]:',
                                                    ),
                                                    Text(
                                                      'Occupied Str Zone [${units[4]}]:',
                                                    ),
                                                    Text(
                                                      'Chord Axial Capacity [${units[0]}]:',
                                                    ),
                                                    Text(
                                                      'Lever Arm [${units[4]}]:',
                                                    ),
                                                    Text(
                                                      'Moment Demand [${units[2]}]:',
                                                    ),
                                                    Text(
                                                      'Moment Capacity [${units[2]}]:',
                                                    ),
                                                  ],
                                                ),
                                              ],
                                            ),
                                            SizedBox(width: 10.0),
                                            Column(
                                              mainAxisSize: MainAxisSize.min,
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                              
                                              children: [
                                                Builder(
                                                  builder: (context) {
                                                    if (!failRegExp.hasMatch(
                                                      scheme.calsLog,
                                                    )) {
                                                      return Text(
                                                        'PASS',
                                                        style: textTheme
                                                            .titleSmall!
                                                            .copyWith(
                                                              color:
                                                                  Colors
                                                                      .green[700],
                                                            ),
                                                      );
                                                    } else {
                                                      return Text(
                                                        'FAIL',
                                                        style: textTheme
                                                            .titleSmall!
                                                            .copyWith(
                                                              color:
                                                                  colorScheme
                                                                      .error,
                                                            ),
                                                      );
                                                    }
                                                  },
                                                ),
                                                Column(
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.start,
                              
                                                  children: [
                                                    Text(scheme.steelSection),
                              
                                                    Text(
                                                      f0.format(scheme.strZone),
                                                    ),
                                                    Text(
                                                      f0.format(
                                                        scheme.slabThickness,
                                                      ),
                                                    ),
                                                    Text(
                                                      f0.format(
                                                        double.tryParse(
                                                          calsLogMap['Occupied Str Zone'][0],
                                                        ),
                                                      ),
                                                    ),
                                                    Text(
                                                      f0.format(
                                                        scheme
                                                            .chordAxialCapacity,
                                                      ),
                                                    ),
                                                    Text(
                                                      f0.format(
                                                        scheme.leverArm,
                                                      ),
                                                    ),
                                                    Text(
                                                      f0.format(
                                                        double.tryParse(
                                                          calsLogMap['M'][0],
                                                        ),
                                                      ),
                                                    ),
                              
                                                    Text(
                                                      f0.format(
                                                        scheme.momentCapacity,
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ],
                                            ),
                                            SizedBox(width: 10.0),
                                            GestureDetector(
                                              onTap: () async {
                                                await showDialog(
                                                  context: context,
                                                  builder: (context) {
                                                    return Dialog(
                                                      backgroundColor:
                                                          colorScheme
                                                              .surfaceContainer,
                                                      child: SizedBox(
                                                        width: 550,
                                                        height: 300,
                                                        child:
                                                            DrawSteelCantileverTrussLoading(
                                                              sketchWidth: 500,
                                                              sketchHeight: 250,
                                                            ),
                                                      ),
                                                    );
                                                  },
                                                );
                                              },
                                              child:
                                                  DrawSteelCantileverTrussLoading(
                                                    sketchWidth: 300,
                                                    sketchHeight: 125,
                                                  ),
                                            ),
                                          ],
                                        ),
                                      ),
                                      SizedBox(height: 10),
                                      Row(
                                        children: [
                                          SizedBox(width: 10),
                                          FunctionButton(
                                            labelIcon: Icon(
                                              Icons.calculate_outlined,
                                            ),
                                            labelText: 'Show Cals',
                                            onTap: (isPressed) async {
                                              await _presentCalsRecord();
                                            },
                                          ),
                                        ],
                                      ),
                                      Padding(
                                        padding: const EdgeInsets.fromLTRB(
                                          10,
                                          0,
                                          10,
                                          0,
                                        ),
                                        child: ChartTransferBeamScheme(
                                          Md: beamMd,
                                          Vd: beamVd,
                                          showVd: false,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                          ],
                        );
                      },
                      error: (error, stackTrace) => Text(error.toString()),
                      loading: () => CircularProgressIndicator(),
                    );
                  },
                  error: (error, stackTrace) => Text(error.toString()),
                  loading: () => CircularProgressIndicator(),
                );
              },
              error: (error, stackTrace) => Text(error.toString()),
              loading: () => CircularProgressIndicator(),
            );
          },
          error: (error, stackTrace) => Text(error.toString()),
          loading: () => CircularProgressIndicator(),
        );
      },
      error: (error, stackTrace) => Text(error.toString()),
      loading: () => CircularProgressIndicator(),
    );
  }

  Future<void> _presentCalsRecord() async {
    final globalData = await ref.read(globalDataControllerProvider.future);
    final inputs = await ref.read(
      steelCantileverTrussSchemeInputControllerProvider.future,
    );
    final inputGlobal = await ref.read(
      steelCantileverTrussSchemeInputGlobalControllerProvider.future,
    );
    final scheme = await ref.read(
      steelCantileverTrussSchemeDataControllerProvider.future,
    );
    await showDialog(
      context: context,
      builder: (context) {
        ColorScheme colorScheme = Theme.of(context).colorScheme;
        TextTheme textTheme = Theme.of(context).textTheme;
        Color bgColor = colorScheme.surfaceContainer;
        Color onBgColor = colorScheme.onSurface;
        Color titleBgColor = colorScheme.primary.withAlpha(150);
        Color titleOnBgColor = colorScheme.onPrimary;
        TextStyle titleTextStyle = textTheme.labelSmall!.copyWith(
          color: titleOnBgColor,
        );
        ScrollController scrollController = ScrollController();

        late List<String> unit;
        switch (globalData.unit) {
          case 'metrics':
            unit = PreferredUnit.metrics;
            break;
          case 'imperial':
            unit = PreferredUnit.imperial;
            break;
          default:
            unit = PreferredUnit.metrics;
        }
        const double maxH = 600;
        const double maxW = 600;
        NumberFormat f0 = NumberFormat('0');

        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(15),
          ),
          backgroundColor: bgColor,
          child: ConstrainedBox(
            constraints: const BoxConstraints(maxWidth: maxW, maxHeight: maxH),
            child: FutureBuilder(
              future: _writeTrussResult(unit),
              builder: (context, snapshot) {
                if (snapshot.hasData) {
                  final StringBuffer steelTrussCals = snapshot.data!;
                  return Column(
                    children: [
                      Align(
                        alignment: Alignment.centerRight,
                        child: Padding(
                          padding: const EdgeInsets.fromLTRB(0, 10, 10, 0),
                          child: FunctionButton(
                            bgColor: titleBgColor,
                            labelTextColor: titleOnBgColor,
                            pressedColor: titleBgColor,
                            labelIcon: Icon(
                              Icons.print_outlined,
                              color: titleOnBgColor.withAlpha(175),
                            ),
                            labelText: '',
                            onTap: (value) {
                              exportListToPdf(
                                context,
                                ref,
                                steelTrussCals.toString().split('\n'),
                              );
                            },
                          ),
                        ),
                      ),

                      Scrollbar(
                        controller: scrollController,
                        thumbVisibility: true,
                        trackVisibility: false,
                        child: ConstrainedBox(
                          constraints: const BoxConstraints(
                            maxWidth: maxW,
                            maxHeight: maxH - 70,
                          ),
                          child: SingleChildScrollView(
                            controller: scrollController,
                            child: Padding(
                              padding: const EdgeInsets.fromLTRB(
                                25.0,
                                5,
                                25.0,
                                5,
                              ),
                              child: DefaultTextStyle(
                                style: textTheme.labelMedium!.copyWith(
                                  color: onBgColor,
                                ),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  children: [
                                    Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      children: [
                                        Container(
                                          decoration: BoxDecoration(
                                            color: titleBgColor,
                                            shape: BoxShape.rectangle,
                                            borderRadius: BorderRadius.circular(
                                              10.0,
                                            ),
                                            border: Border.all(
                                              color: titleOnBgColor,
                                            ),
                                          ),
                                          child: Padding(
                                            padding: const EdgeInsets.all(4.0),
                                            child: Text(
                                              'Section :\n${scheme.steelSection}',
                                              style: titleTextStyle,
                                            ),
                                          ),
                                        ),
                                        SizedBox(width: 5),
                                        Container(
                                          decoration: BoxDecoration(
                                            color: titleBgColor,
                                            shape: BoxShape.rectangle,
                                            borderRadius: BorderRadius.circular(
                                              10.0,
                                            ),
                                            border: Border.all(
                                              color: titleOnBgColor,
                                            ),
                                          ),
                                          child: Padding(
                                            padding: const EdgeInsets.all(4.0),
                                            child: Text(
                                              'Fy [${unit[5]}]:\n${f0.format(scheme.fsy)} ',
                                              style: titleTextStyle,
                                            ),
                                          ),
                                        ),

                                        SizedBox(width: 5),
                                        Container(
                                          decoration: BoxDecoration(
                                            color: titleBgColor,
                                            shape: BoxShape.rectangle,
                                            borderRadius: BorderRadius.circular(
                                              10.0,
                                            ),
                                            border: Border.all(
                                              color: titleOnBgColor,
                                            ),
                                          ),
                                          child: Padding(
                                            padding: const EdgeInsets.all(4.0),
                                            child: Text(
                                              'Chord Pc [${unit[0]}]:\n${f0.format(scheme.chordAxialCapacity)} ',
                                              style: titleTextStyle,
                                            ),
                                          ),
                                        ),
                                        SizedBox(width: 5),
                                        Container(
                                          decoration: BoxDecoration(
                                            color: titleBgColor,
                                            shape: BoxShape.rectangle,
                                            borderRadius: BorderRadius.circular(
                                              10.0,
                                            ),
                                            border: Border.all(
                                              color: titleOnBgColor,
                                            ),
                                          ),
                                          child: Padding(
                                            padding: const EdgeInsets.all(4.0),
                                            child: Text(
                                              'Truss Mc [${unit[0]}]:\n${f0.format(scheme.momentCapacity)} ',
                                              style: titleTextStyle,
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                    SizedBox(height: 5),
                                    Container(
                                      decoration: BoxDecoration(
                                        borderRadius: BorderRadius.circular(
                                          10.0,
                                        ),
                                        border: Border.all(color: onBgColor),
                                      ),
                                      child: Padding(
                                        padding: const EdgeInsets.all(8.0),
                                        child: Text(steelTrussCals.toString()),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  );
                } else if (snapshot.hasError) {
                  return Center(child: Text(snapshot.error.toString()));
                } else {
                  return const CircularProgressIndicator();
                }
              },
            ),
          ),
        );
      },
    );
  }

  Future<StringBuffer> _writeTrussResult(List<String> unit) async {
    final SteelCantileverTrussSchemeData scheme = await ref.read(
      steelCantileverTrussSchemeDataControllerProvider.future,
    );
    final List<SteelCantileverTrussSchemeInput> inputs = await ref.read(
      steelCantileverTrussSchemeInputControllerProvider.future,
    );
    final SteelCantileverTrussSchemeInputGlobal inputGlobal = await ref.read(
      steelCantileverTrussSchemeInputGlobalControllerProvider.future,
    );
    //* initialize variables for easy use
    StringBuffer buffer = StringBuffer();
    NumberFormat f0 = NumberFormat('0'),
        f1 = NumberFormat('0.0'),
        f3 = NumberFormat('0.000');
    late final double Es,
        maxThk,
        minR,
        lamda,
        lamda0,
        eta,
        alpha,
        pE,
        phi,
        pc,
        aG,
        axialCap;
    double fsy = scheme.fsy;
    Es = 205000;
    final List<Map<String, dynamic>> steelSections =
        await getAllSteelISection();
    final Map<String, dynamic> section = steelSections.firstWhere(
      (item) => item['name'] == scheme.steelSection,
    );
    final calsLogMap = extractCalsLog(scheme.calsLog);

    //* writing the results
    buffer.write('*******************\n');
    buffer.write('Parameters\n');
    buffer.write('*******************\n');
    buffer.write('Truss Chord / Web Section:  ${scheme.steelSection}\n');
    buffer.write('Span, L = ${f1.format(inputGlobal.span)} [${unit[3]}]\n');
    buffer.write('Truss Depth = ${f0.format(scheme.strZone)} [${unit[4]}]\n');
    buffer.write('Lever Arm, d = ${f0.format(scheme.leverArm)} [${unit[4]}]\n');
    buffer.write('-------------------\n');
    for (int i = 0; i < inputs.length; i++) {
      buffer.write('Point Load\n');
      buffer.write(
        '${i + 1} >> Force: ${f0.format(inputs[i].pointLoad)} [${unit[0]}] | ',
      );
      buffer.write('Distance a: ${f0.format(inputs[i].distA)} [${unit[3]}] \n');
    }
    buffer.write('-------------------\n');
    buffer.write('Uniformly Distributed Loads, UDL\n');
    buffer.write(
      '= ${f1.format(double.tryParse(calsLogMap['UDL']![0]))} [${unit[0]}/${unit[3]}]\n',
    );
    buffer.write('-------------------\n');
    buffer.write('Design Force\n');
    buffer.write(
      'Md : ${f0.format(double.tryParse(calsLogMap['M']![0]))} [${unit[2]}] at ${f1.format(getDoubleValue(calsLogMap, 'xMaxMd'))} [${unit[3]}] (Max from M diagram)\n',
    );
    buffer.write('*******************\n');
    buffer.write('Chord Axial Capacity\n');
    buffer.write('*******************\n');
    //* design strength updated
    fsy = await getDesignStrength(scheme.steelSection, fsy, steelSections);
    //* calculate the pc
    minR =
        min(
          double.tryParse(section['r22'])!,
          double.tryParse(section['r33'])!,
        ) *
        10; // [mm]
    maxThk =
        max(double.tryParse(section['tF'])!, double.tryParse(section['tW'])!) *
        10; // [mm]
    lamda = inputGlobal.unbracedLength * 1000 / minR;
    lamda0 = 0.2 * sqrt(pow(pi, 2) * Es / fsy);
    if (maxThk <= 40) {
      alpha =
          3.5; // buckling curve (b): Rolled I, about weak axis y-y, max thickness <= 40mm
    } else {
      alpha =
          5.5; // buckling curve (c): Rolled I, about weak axis y-y, max thickness > 40mm
    }
    eta = max(alpha * (lamda - lamda0) / 1000, 0);
    pE = pow(pi, 2) * Es / pow(lamda, 2);
    phi = (fsy + (eta + 1) * pE) / 2;
    pc = (pE * fsy) / (phi + sqrt(pow(phi, 2) - pE * fsy));
    aG = double.parse(section['A']) * 100; // [mm2]
    axialCap = pc * aG / 1000; // [kN]

    //* present the calculated parameters
    buffer.write(
      'Fy = ${f0.format(scheme.fsy)} [${unit[5]}] (design strength considering section)\n',
    );
    buffer.write('E = ${f0.format(Es)} [${unit[5]}]\n');
    buffer.write(
      'L = ${f0.format(inputGlobal.unbracedLength * 1000)} [${unit[4]}] (max unbraced length)\n',
    );
    buffer.write(
      'r = ${f0.format(minR)} [${unit[4]}] (min radius of gyration)\n',
    );
    buffer.write('\u03BB = L/r = ${f1.format(lamda)}\n');
    buffer.write(
      '\u03BB\u2080 = 0.2\u221A(\u03C0^2*E/Fy) = ${f1.format(lamda0)}\n',
    );
    buffer.write(
      '\u03B1 = ${f1.format(alpha)} (buckling curve: ${alpha == 3.5 ? 'b' : 'c'})\n',
    );
    buffer.write(
      '\u03B7 = max(\u03B1*(\u03BB-\u03BB\u2080)/1000,0) = ${f3.format(eta)}\n',
    );
    buffer.write(
      'pE = (\u03C0^2*E)/\u03BB^2 = ${f0.format(pE)} [${unit[5]}]\n ',
    );
    buffer.write('\u03A6 = (Fy + (\u03B7+1)*pE)/2 = ${f0.format(phi)}\n');
    buffer.write(
      'pc = (pE*Fy)/(\u03A6 + \u221A(\u03A6^2-pE*Fy)) =${f1.format(pc)} [${unit[5]}]\n',
    );
    buffer.write('Ag = ${f0.format(aG)} [${unit[6]}]\n');
    buffer.write('Pc = pc*Ag = ${f0.format(axialCap)} [${unit[0]}]\n');
    buffer.write('*******************\n');
    buffer.write('Truss Moment Capacity\n');
    buffer.write('*******************\n');
    buffer.write(
      'Mc = 2*(Pc*d/2) = ${f0.format(2 * (axialCap * scheme.leverArm * pow(10, -3) / 2))} [${unit[2]}]\n',
    );
    if (double.tryParse(calsLogMap['Mc']![0])! >
        double.tryParse(calsLogMap['M']![0])!) {
      buffer.write('Mc > Md (pass)\n');
    } else {
      buffer.write('Mc < Md (fail)\n');
    }

    //* log any error / warnings
    List<String> errors = [], warnings = [];
    RegExp failReg = RegExp(r'fail', caseSensitive: false);
    if (failReg.hasMatch(buffer.toString())) {
      errors.add('Result not reliable. Something fails.');
      warnings.add(
        '1. Consider to adjust grid (hence the point load / distributed load)',
      );
      warnings.add('2. Consider to use bundle truss with coupling beam');
      warnings.add('3. Consider to increase truss depth (if possible)');
    }

    if (warnings.isNotEmpty) {
      buffer = addWarningHeader(buffer, warnings);
    }
    if (errors.isNotEmpty) {
      buffer = addErrorHeader(buffer, errors);
    }

    return buffer;
  }

  void _updateHeights() {
    final double screenHeight = MediaQuery.of(context).size.height;
    _maxHeight =
        (widget.maxHeight == null)
            ? screenHeight * 0.6
            : (widget.maxHeight! > screenHeight * 0.6)
            ? screenHeight * 0.6
            : widget.maxHeight!;

    _minHeight =
        (widget.minHeight == null)
            ? screenHeight * 0.1
            : (widget.minHeight! > screenHeight * 0.1)
            ? screenHeight * 0.1
            : widget.minHeight!;

    // Adjust _maxHeight if needed

    if (_maxHeight < _minHeight) {
      _maxHeight = _minHeight;
    }
  }
}
