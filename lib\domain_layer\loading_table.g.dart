// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'loading_table.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_LoadingTable _$LoadingTableFromJson(Map<String, dynamic> json) =>
    _LoadingTable(
      usage: json['usage'] as String? ?? 'Usage',
      finish: (json['finish'] as num?)?.toDouble() ?? 0.0,
      service: (json['service'] as num?)?.toDouble() ?? 0.0,
      liveLoad: (json['liveLoad'] as num?)?.toDouble() ?? 0.0,
      loadingTableId: json['loadingTableId'] as String? ?? '',
    );

Map<String, dynamic> _$LoadingTableToJson(_LoadingTable instance) =>
    <String, dynamic>{
      'usage': instance.usage,
      'finish': instance.finish,
      'service': instance.service,
      'liveLoad': instance.liveLoad,
      'loadingTableId': instance.loadingTableId,
    };
