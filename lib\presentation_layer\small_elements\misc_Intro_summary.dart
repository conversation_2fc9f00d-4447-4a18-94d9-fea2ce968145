// import 'dart:nativewrappers/_internal/vm/lib/math_patch.dart';

import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:structify/domain_layer/pile_frictional_bored_input.dart';

//below for printing
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:printing/printing.dart';
// import 'dart:typed_data';
// import 'dart:ui';

//domain layer
import '../../domain_layer/global_data.dart';
import '../../domain_layer/pile_frictional_bored_data.dart';

// presentation layer
import '../screen/homescreen.dart';
// import '../small_elements/custom_stateful_double_input.dart';
// import '../small_elements/custom_stateful_text_input.dart';
// import '../small_elements/global_data_ui.dart';

//misc

import 'button/selection_button.dart';

class IntroSummary extends ConsumerStatefulWidget {
  const IntroSummary({
    this.isExpanded,
    this.maxHeight,
    this.minHeight,
    super.key,
  });

  final bool? isExpanded;
  final double? maxHeight;
  final double? minHeight;

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _IntroSummaryState();
}

class _IntroSummaryState extends ConsumerState<IntroSummary>
    with WidgetsBindingObserver {
  late bool _isExpanded;
  late double _maxHeight;
  late double _minHeight;
  late ScrollController _scrollController;

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void initState() {
    WidgetsBinding.instance.addObserver(this);
    _isExpanded = widget.isExpanded ?? false;
    _maxHeight = widget.maxHeight ?? 300;
    _minHeight = widget.minHeight ?? 50;
    _scrollController = ScrollController();
    // _updateHeights();

    super.initState();
  }

  @override
  void didChangeDependencies() {
    _updateHeights();
    super.didChangeDependencies();
    // Update heights when dependencies change, including after initState
  }

  @override
  void didChangeMetrics() {
    // This method is called when the metrics change (like resizing the window)
    setState(() {
      _updateHeights();
    });
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;
    final globalData = ref.watch(globalDataControllerProvider);
    final TextStyle titleTextStyle = textTheme.titleMedium!.copyWith(
      color: colorScheme.onSurface,
    );
    final TextStyle bodyTextStyle = textTheme.bodyMedium!.copyWith(
      color: colorScheme.onSurface,
    );
    final TextStyle highlightText = textTheme.labelLarge!.copyWith(
      color: colorScheme.primary,
    );

    return globalData.when(
      data: (data) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.fromLTRB(10, 0, 10, 0),
              child: Divider(),
            ),
            Padding(
              padding: const EdgeInsets.fromLTRB(10, 0, 0, 0),
              child: Align(
                alignment: Alignment.centerLeft,
                child: Wrap(
                  crossAxisAlignment: WrapCrossAlignment.center,
                  children: [
                    IconButton(
                      icon: Icon(
                        _isExpanded ? Icons.expand_less : Icons.expand_more,
                      ),
                      color: colorScheme.onSurface,
                      onPressed: () {
                        setState(() {
                          _isExpanded = !_isExpanded;
                        });
                      },
                    ),
                    Text(
                      'Introduction',
                      style: textTheme.titleLarge!.copyWith(
                        color: colorScheme.onSurface,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            Padding(
              padding: const EdgeInsets.fromLTRB(10, 0, 10, 0),
              child: Divider(),
            ),

            ClipRect(
              child: ConstrainedBox(
                constraints: BoxConstraints(
                  maxHeight: _isExpanded ? _maxHeight : 0,
                ),
                child: Scrollbar(
                  controller: _scrollController,
                  thumbVisibility: true,
                  trackVisibility: false,
                  child: SingleChildScrollView(
                    scrollDirection: Axis.vertical,
                    controller: _scrollController,
                    child: Padding(
                      padding: const EdgeInsets.fromLTRB(10, 0, 0, 0),
                      child: DefaultTextStyle(
                        style: textTheme.bodyMedium!.copyWith(
                          color: colorScheme.onSurface,
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            _itemBlock(
                              'Introduction',
                              [
                                '______ will be constructed in ______. ',
                                'Two distinct and viable schemes in view of its function, safety, and cost will be proposed',
                              ],
                              backgroundColor: colorScheme.tertiaryContainer
                                  .withAlpha(100),
                              borderColor: colorScheme.tertiaryContainer
                                  .withAlpha(150),
                              textColor: colorScheme.onTertiaryContainer,
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ],
        );
      },
      error: (error, stackTrace) => Text(error.toString()),
      loading: () => CircularProgressIndicator(),
    );
  }

  Widget _itemBlock(
    String requirementTitle,
    List<String> requirement, {
    Color? backgroundColor,
    Color? borderColor,
    Color? textColor,
  }) {
    //*initialize
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;
    backgroundColor ??= colorScheme.primaryContainer.withAlpha(50);
    borderColor ??= colorScheme.primary.withAlpha(50);
    textColor ??= colorScheme.onPrimaryContainer;

    return Padding(
      padding: const EdgeInsets.fromLTRB(0, 8, 0, 8),
      child: Container(
        decoration: BoxDecoration(
          color: backgroundColor,
          borderRadius: BorderRadius.circular(8.0),
          border: Border.all(width: 1.5, color: borderColor),
        ),
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: DefaultTextStyle(
            style: textTheme.bodyMedium!.copyWith(
              color: textColor,
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(width: 10.0),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '$requirementTitle',
                        style: textTheme.labelLarge!.copyWith(
                          color: textColor,
                        ),
                      ),
                      ...List.generate(requirement.length, (index) {
                        return Row(children: [Text(requirement[index])]);
                      }),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // Method to update heights based on the current media query
  void _updateHeights() {
    final double screenHeight = MediaQuery.of(context).size.height;
    _maxHeight =
        (widget.maxHeight == null)
            ? screenHeight * 0.15
            : (widget.maxHeight! > screenHeight * 0.15)
            ? screenHeight * 0.15
            : widget.maxHeight!;

    _minHeight =
        (widget.minHeight == null)
            ? screenHeight * 0.1
            : (widget.minHeight! > screenHeight * 0.1)
            ? screenHeight * 0.1
            : widget.minHeight!;

    // Adjust _maxHeight if needed

    if (_maxHeight < _minHeight) {
      _maxHeight = _minHeight;
    }
  }
}
