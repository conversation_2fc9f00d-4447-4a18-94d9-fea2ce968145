import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

//presentation layer
// import '../screen/homescreen.dart';

class FunctionButton extends ConsumerStatefulWidget {
  const FunctionButton({
    this.labelText,
    this.labelIcon,
    this.onTap,
    this.isTapped = false,
    this.bgColor,
    this.pressedColor,
    this.labelTextColor,
    this.labelTextStyle,
    this.listener,
    this.scale,
    super.key,
  });

  final String? labelText;
  final Icon? labelIcon;
  final Color? bgColor;
  final Color? pressedColor;
  final Color? labelTextColor;
  final TextStyle? labelTextStyle;
  final dynamic Function(bool)? onTap;
  final dynamic Function(bool)? listener;
  final bool isTapped;
  final double? scale;

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => FunctionButtonState();
}

class FunctionButtonState extends ConsumerState<FunctionButton> {
  late final String _labelText;
  late final Icon? _labelIcon;
  late bool _isTapped;
  late bool _isHoevered;

  late final dynamic Function(bool)? _onTap;
  late final dynamic Function(bool)? _listener;

  late final Color _bgColor;
  late final Color _labelTextColor;
  late final Color _pressedColor;

  late final TextStyle? _labelTextStyle;

  late double _scale;

  @override
  void initState() {
    _labelText = widget.labelText ?? 'Function Buttom';
    _labelIcon = widget.labelIcon;
    _isTapped = widget.isTapped;
    _isHoevered = false;
    _onTap = widget.onTap;
    _listener = widget.listener;
    _scale = widget.scale ?? 1.0;
    super.initState();
  }

  @override
  void didChangeDependencies() {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;
    //Update state if  necessary when new widget property input
    _bgColor = widget.bgColor ?? colorScheme.surfaceContainer;
    _pressedColor = widget.pressedColor ?? colorScheme.tertiaryContainer;
    _labelTextColor = widget.labelTextColor ?? colorScheme.onSurface;
    _labelTextStyle =
        widget.labelTextStyle ??
        textTheme.titleSmall!.copyWith(color: _labelTextColor);
    _scale = widget.scale ?? _scale;

    super.didChangeDependencies();
  }

  @override
  Widget build(BuildContext context) {
    if (_listener != null) {
      final x = _listener(_isTapped);
      if (x is bool) {
        _isTapped = x;
      }
    }
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;
    return MouseRegion(
      onEnter: (event) {
        setState(() {
          _isHoevered = true;
        });
      },
      onExit: (event) {
        setState(() {
          _isHoevered = false;
        });
      },
      child: GestureDetector(
        onTap: () {
          if (_onTap != null) {
            setState(() {
              final result = _onTap(true);
            });
          }
        },
        onTapDown: (details) {
          setState(() {
            _isTapped = true;
          });
        },
        onTapUp: (details) {
          setState(() {
            _isTapped = false;
          });
        },

        child: AnimatedContainer(
          transform:
              Matrix4.identity()
                ..scale(_scale)
                ..setEntry(3, 2, 0.005)
                ..setTranslationRaw(
                  0,
                  (_isTapped
                      ? 5
                      : _isHoevered
                      ? -3
                      : 0),
                  0,
                ),
          duration: const Duration(milliseconds: 150),
          decoration: BoxDecoration(
            color: _isTapped ? _pressedColor : _bgColor,
            borderRadius: BorderRadius.circular(10),
            border: Border.all(color: colorScheme.outline),
          ),
          child: Padding(
            padding: const EdgeInsets.all(4.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              mainAxisSize: MainAxisSize.min,
              children: [
                _labelIcon ?? const SizedBox(width: 0, height: 0),
                Text(
                  _labelText,
                  style: _labelTextStyle,
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
