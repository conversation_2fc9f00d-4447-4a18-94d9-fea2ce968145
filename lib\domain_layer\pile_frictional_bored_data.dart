import 'package:freezed_annotation/freezed_annotation.dart';
// import 'package:flutter/foundation.dart';
part 'pile_frictional_bored_data.freezed.dart';
part 'pile_frictional_bored_data.g.dart';

@freezed
abstract class PileFrictionalBoredData with _$PileFrictionalBoredData {
  const PileFrictionalBoredData._();
  factory PileFrictionalBoredData({
    @Default(50) double sptNValue,
    @Default(19) double soilUnitWeight,
    @Default(0.25) double kTan,
    @Default(3) double fos,
    @Default(45) double fcu,
    @Default(30) double maxPileLength,
    @Default(2000) double maxPileDiameter,
    @Default(0.04) double maxSteelRatio,
    @Default(1000) double slsLoad,
    @Default(2000) double ulsLoad,
    @Default(0) double diameter,
    @Default(1.65) double ratioOfBelloutDia,
    @Default(0) double length,
    @Default(0) double shaftCapacity,
    @Default(0) double baseCapacity,
    @Default(0) double totalGroundResistance,
    @Default(0) double strCapacity,
    @Default('')
    String rebar, //will be overriden  as soon as new instance created
    @Default(0) double steelRatio,
    @Default(false) bool isSelected,
    @Default('')
    String calsLog, //will be overriden  as soon as new instance created
    @Default('')
    String
    pileFrictionalBoredSchemeId, //will be overriden  as soon as new instance created
  }) = _PileFrictionalBoredData;

  factory PileFrictionalBoredData.fromJson(Map<String, Object?> json) =>
      _$PileFrictionalBoredDataFromJson(json);
}
