import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

//* below for printing
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:printing/printing.dart';

mixin MixinToolsForUI {
  //*
  //* Method to extract the calslog (string) into a Map for scheme
  //*
  Map<String, List<dynamic>> extractCalsLog(String calsLog) {
    final record = calsLog;
    List<String> lines = record.split('\n');

    RegExp titleRegExp = RegExp(r'([\w\d_ ()]*[:])');
    RegExp digitsRegExp = RegExp(r'(?<=: )([-\d]+[\.]?[\d]*)');
    RegExp unitRegExp = RegExp(r'(\[(.+?)\])');
    RegExp textRegExp = RegExp(r'(?<=: )[\w \.\-\+\(\){":,}]*');
    // RegExp textRegExp = RegExp(r'(?<=: )[\w ]*');

    Map<String, List<dynamic>> resultMap = {};

    for (String line in lines) {
      final List<String> newLines = line.split(r'|');
      for (String line in newLines) {
        final titleMatch = titleRegExp
            .firstMatch(line)
            ?.group(0)
            ?.trim()
            .replaceAll(':', '');
        final String? digitMatch =
            digitsRegExp.firstMatch(line)?.group(0)?.trim();
        String? unitMatch = unitRegExp.firstMatch(line)?.group(0);
        String? textMatch = textRegExp.firstMatch(line)?.group(0)?.trim();

        //* capture numeric/text result
        if (titleMatch != null) {
          if ((textMatch != null && !RegExp(r'[a-zA-Z]').hasMatch(textMatch)) &&
              (digitMatch != null)) {
            resultMap[titleMatch] = [digitMatch, unitMatch];
          } else {
            resultMap[titleMatch] = [textMatch, unitMatch];
          }
        }
      }
    }
    return resultMap;
  }

  double getDoubleValue(Map<String, List<dynamic>> pair, String key) {
    if (pair[key]?.first != null) {
      if (pair[key]?.first == "") {
        return double.infinity;
      } else {
        return double.parse(pair[key]?.first);
      }
    } else {
      return 0.0;
    }
  }

  int getValueAsInt(Map<String, List<dynamic>> pair, String key) {
    if (pair[key]?.first != null) {
      return int.parse(pair[key]?.first);
    } else {
      return 0;
    }
  }

  String getStringValue(Map<String, List<dynamic>> pair, String key) {
    return pair[key]?.first;
  }

  Future<void> exportListToPdf(
    BuildContext context,
    WidgetRef ref,
    List<String> contents,
  ) async {
    final pw.ThemeData customTheme = pw.ThemeData.withFont(
      base: pw.Font.ttf(await rootBundle.load("assets/OpenSans-Regular.ttf")),
      bold: pw.Font.ttf(await rootBundle.load("assets/OpenSans-Bold.ttf")),
      italic: pw.Font.ttf(await rootBundle.load("assets/OpenSans-Italic.ttf")),
      boldItalic: pw.Font.ttf(
        await rootBundle.load("assets/OpenSans-BoldItalic.ttf"),
      ),
    );

    final pdf = pw.Document(theme: customTheme);
    final maxLines = 45;
    List<pw.Widget> pageBuffer = [];

    for (int i = 0; i < contents.length; i++) {
      pageBuffer.add(pw.Text(contents[i]));

      //! note that the .addPage is deferred.
      //! it means it executes later after pageBuffer.celar runs
      //! so we put in copy of pageBuffer to it everytime we .addPage

      if ((pageBuffer.length == maxLines) || (i == contents.length - 1)) {
        final pageBufferCopy = List<pw.Text>.from(pageBuffer);
        pdf.addPage(
          pw.Page(
            margin: pw.EdgeInsets.fromLTRB(25, 35, 25, 35),
            build: (pw.Context context) {
              return pw.Column(
                mainAxisAlignment: pw.MainAxisAlignment.start,
                crossAxisAlignment: pw.CrossAxisAlignment.start,
                children: pageBufferCopy,
              );
            },
          ),
        );
        pageBuffer.clear();
      }
    }
    await Printing.layoutPdf(
      onLayout: (PdfPageFormat format) async => pdf.save(),
    );
  }

  StringBuffer addErrorHeader(StringBuffer buffer, List<String> errors) {
    final newBuffer = StringBuffer();
    for (var error in errors) {
      newBuffer.write('\u274C Error: ');
      newBuffer.write(error);
      newBuffer.write('\n');      
    }
    newBuffer.write(buffer.toString());
    return newBuffer;
  }StringBuffer addWarningHeader(StringBuffer buffer, List<String> warnings) {
    final newBuffer = StringBuffer();
    for (var warning in warnings) {
      newBuffer.write('\u26A0 Warning: ');
      newBuffer.write(warning);
      newBuffer.write('\n');      
    }
    newBuffer.write(buffer.toString());
    return newBuffer;
  }

  //* Method to show a slide in messgae
  void showSlidingFadingMessage(
    BuildContext context,
    String message, {
    Duration duration = const Duration(seconds: 5),
    double? offsetFromLeft,
    double? width,
    Color? containerColor,
    TextStyle? textStyle,
  }) {
    final overlay = Overlay.of(context);
    late OverlayEntry overlayEntry;

    final ColorScheme colorScheme = Theme.of(context).colorScheme;
    final TextTheme textTheme = Theme.of(context).textTheme;

    //*
    //* start Default values
    //*

    containerColor ??= colorScheme.inverseSurface.withAlpha(175);
    textStyle ??= textTheme.titleSmall!.copyWith(
      color: colorScheme.onInverseSurface.withAlpha(200),
    );
    width ??= MediaQuery.of(context).size.width * 0.35;
    offsetFromLeft ??= (MediaQuery.of(context).size.width - width) * 0.5;

    //*
    //* end Default values
    //*

    overlayEntry = OverlayEntry(
      builder: (context) {
        return Positioned(
          bottom: 50, // Position from bottom
          left: offsetFromLeft,
          width: width,
          child: _SlidingFadingMessageWidget(
            message: message,
            duration: duration,
            onComplete: () => overlayEntry.remove(),
            containerColor: containerColor,
            textStyle: textStyle,
          ),
        );
      },
    );

    overlay?.insert(overlayEntry);
  }
}

class _SlidingFadingMessageWidget extends StatefulWidget {
  final String message;
  final Duration duration;
  final VoidCallback? onComplete;
  final Color? containerColor;
  final TextStyle? textStyle;

  const _SlidingFadingMessageWidget({
    required this.message,
    required this.duration,
    this.onComplete,
    this.containerColor,
    this.textStyle,
  });

  @override
  State<_SlidingFadingMessageWidget> createState() =>
      _SlidingFadingMessageWidgetState();
}

//*
//* PRIVATE CLASS
//*
class _SlidingFadingMessageWidgetState
    extends State<_SlidingFadingMessageWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();

    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 500), // slide and fade in duration
    );

    // Slide from bottom to original position (Offset(0, 1) to Offset(0, 0))
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 1),
      end: Offset.zero,
    ).animate(CurvedAnimation(parent: _controller, curve: Curves.easeOut));

    // Fade in from 0 to 1
    _fadeAnimation = Tween<double>(
      begin: 0,
      end: 1,
    ).animate(CurvedAnimation(parent: _controller, curve: Curves.easeIn));

    _controller.forward();

    // Wait for duration - fade time, then reverse animation to fade & slide out
    Future.delayed(widget.duration - const Duration(milliseconds: 500), () {
      _controller.reverse().then((value) {
        if (widget.onComplete != null) {
          widget.onComplete!();
        }
      });
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SlideTransition(
      position: _slideAnimation,
      child: FadeTransition(
        opacity: _fadeAnimation,
        child: Material(
          elevation: 10,
          borderRadius: BorderRadius.circular(8),
          color: widget.containerColor,
          child: Padding(
            padding: const EdgeInsets.all(12.0),
            child: Text(
              widget.message,
              style: widget.textStyle,
              textAlign: TextAlign.center,
            ),
          ),
        ),
      ),
    );
  }
}
