import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:structify/domain_layer/pile_socketed_data.dart';

//presentation layer
import '../screen/homescreen.dart';

// domain layer

part 'pile_socketed_data_controller.g.dart';

@riverpod
class PileSocketedController extends _$PileSocketedController {
  @override
  FutureOr<PileSocketedData> build() async {
    // print('Build: Column Scheme Data');
    final pileFrictionalBoredData =
        await ref
            .read(appDatabaseControllerProvider.notifier)
            . queryPileSocketedData();
    return pileFrictionalBoredData;
  }

  // Future<void> addSchemeData(
  //   List<PileFrictionalBoredData> pileFrictionalBoredData,
  // ) async {
  //   final x = await future;
  //   state = AsyncData([...x, ...pileFrictionalBoredData]);
  // }

  // Future<void> deleteTable(String id) async {
  //   final x = await future;
  //   x.removeWhere((item) => item.pileFrictionalBoredSchemeId == id);
  //   state = AsyncData(x);
  // }

  // Future<void> deleteAllTable() async {
  //   state = AsyncData([]);
  // }

  Future<void> toggleSelectScheme() async {
    final x = await future;
    final newState = x.copyWith(isSelected: !x.isSelected);
    state = AsyncData(newState);
  }

  // Future<String> _generateUniqueId() async {
  //   final x = await future;
  //   String newID = nanoid(alphabet: Alphabet.noDoppelgangerSafe);
  //   final Set<String> pileFrictionalBoredSchemeId =
  //       x.map((item) => item.pileFrictionalBoredSchemeId).toSet();
  //   while (pileFrictionalBoredSchemeId.contains(newID)) {
  //     newID = nanoid(alphabet: Alphabet.noDoppelgangerSafe);
  //   }
  //   return newID;
  // }

  
}
