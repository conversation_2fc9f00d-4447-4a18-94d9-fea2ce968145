import 'package:freezed_annotation/freezed_annotation.dart';
// import 'package:flutter/foundation.dart';
part 'steel_column_scheme_data.freezed.dart';
part 'steel_column_scheme_data.g.dart';

@freezed
abstract class SteelColumnSchemeData with _$SteelColumnSchemeData {
  const SteelColumnSchemeData._();
  factory SteelColumnSchemeData({
    @Default('') String steelColumnSchemeDataId, //will be overriden  as soon as new instance created
    @Default(0.0) double sdl,
    @Default(0.0) double ll,
    @Default(0.0) double slsLoad,
    @Default(0.0) double ulsLoad,
    @Default(355.0) double fsy,
    @Default(5.0) double unbracedLength,
    @Default(0.0) double axialCapacity,
    @Default('') String steelSection,
    @Default('') String calsLog,
  }) = _SteelColumnSchemeData; 

  factory SteelColumnSchemeData.fromJson(Map<String, Object?> json) =>
      _$SteelColumnSchemeDataFromJson(json);
}
