// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'programme_items_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$programmeItemsControllerHash() =>
    r'de96b3c6135578161b5e6f317a994185fc52a209';

/// See also [ProgrammeItemsController].
@ProviderFor(ProgrammeItemsController)
final programmeItemsControllerProvider = AutoDisposeAsyncNotifierProvider<
  ProgrammeItemsController,
  List<ProgrammeItem>
>.internal(
  ProgrammeItemsController.new,
  name: r'programmeItemsControllerProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$programmeItemsControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$ProgrammeItemsController =
    AutoDisposeAsyncNotifier<List<ProgrammeItem>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
