// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'global_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_GlobalData _$GlobalDataFromJson(Map<String, dynamic> json) => _GlobalData(
  unit: json['unit'] as String? ?? 'metrics',
  sdlFactor: (json['sdlFactor'] as num?)?.toDouble() ?? 1.4,
  llFactor: (json['llFactor'] as num?)?.toDouble() ?? 1.6,
  rcUnitWeight: (json['rcUnitWeight'] as num?)?.toDouble() ?? 24.5,
  finishUnitWeight: (json['finishUnitWeight'] as num?)?.toDouble() ?? 24.0,
  steelUnitWeight: (json['steelUnitWeight'] as num?)?.toDouble() ?? 78.5,
  soilUnitWeight: (json['soilUnitWeight'] as num?)?.toDouble() ?? 19.0,
  waterUnitWeight: (json['waterUnitWeight'] as num?)?.toDouble() ?? 10.0,
);

Map<String, dynamic> _$GlobalDataToJson(_GlobalData instance) =>
    <String, dynamic>{
      'unit': instance.unit,
      'sdlFactor': instance.sdlFactor,
      'llFactor': instance.llFactor,
      'rcUnitWeight': instance.rcUnitWeight,
      'finishUnitWeight': instance.finishUnitWeight,
      'steelUnitWeight': instance.steelUnitWeight,
      'soilUnitWeight': instance.soilUnitWeight,
      'waterUnitWeight': instance.waterUnitWeight,
    };
