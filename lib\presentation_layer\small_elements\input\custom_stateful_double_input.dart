import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter/services.dart';

//presentation layer
// import '../screen/homescreen.dart';

class CustomStatefulDoubleInput extends ConsumerStatefulWidget {
  CustomStatefulDoubleInput({
    this.title = 'Double Input',
    this.value = 0.0,
    this.sizeScale = 1.0,
    this.onChanged,
    this.listener,
    this.maxValue = double.infinity,
    this.minValue = -double.infinity,
    this.helperText,
    this.tooltipText,
    this.titleStyle,
    this.readOnly = false,
    this.allowNegative = false,
    super.key,
  });

  final String? title;
  double? value;
  double? sizeScale;
  void Function(double)? onChanged;
  void Function(bool, double)? listener;
  double maxValue;
  double minValue;
  String? helperText;
  String? tooltipText;
  bool? readOnly;
  bool? allowNegative;
  TextStyle? titleStyle;

  @override
  ConsumerState<ConsumerStatefulWidget> createState() =>
      _CustomStatefulDoubleInputState();
}

class _CustomStatefulDoubleInputState
    extends ConsumerState<CustomStatefulDoubleInput> {
  //corresponding to widget part
  late String _title;
  late double _value;
  late double _sizeScale;
  late void Function(double)? _onChanged;
  late void Function(bool, double)? _listener;
  late double _maxValue;
  late double _minValue;
  late FocusNode _focusNode;

  late TextEditingController _controller;
  late String? _errorMsg;
  late String? _helperText;
  late String? _tooltipText;
  late bool _allowNegative;

  late TextStyle? _titleStyle;
  late bool _readOnly;

  @override
  void dispose() {
    _controller.dispose();
    _focusNode.removeListener(() {});
    _focusNode.dispose();
    super.dispose();
  }

  @override
  void initState() {
    _title = widget.title ?? '';
    _value = widget.value ?? 0.0;
    _sizeScale = widget.sizeScale ?? 1.0;
    _onChanged = widget.onChanged;
    _listener = widget.listener;
    _maxValue = widget.maxValue;
    _minValue = widget.minValue;

    _controller = TextEditingController(text: widget.value.toString());
    _errorMsg = null;
    _helperText = widget.helperText;
    _tooltipText = widget.tooltipText;

    _titleStyle = widget.titleStyle;
    _readOnly = widget.readOnly ?? false;
    _allowNegative = widget.allowNegative ?? false;

    _focusNode = FocusNode();
    _focusNode.addListener(() {
      final hasFocuse = _focusNode.hasFocus;
      try {
        _value = double.parse(_controller.text);
        widget.value = _value;
        if (_listener != null) {
          _listener!(hasFocuse, _value);
          // print('Listening: $hasFocuse | $_value');
        }
        _errorMsg = null;
      } catch (e) {
        // widget.value = _value;
        // _controller.text = widget.value.toString();
        _errorMsg = "Invalid Number";
      }
    });
    super.initState();
  }

  @override
  void didUpdateWidget(covariant CustomStatefulDoubleInput oldWidget) {
    super.didUpdateWidget(oldWidget);
    //Refresh the state from widget
    _title = widget.title ?? _title;
    _readOnly = widget.readOnly ?? _readOnly;
    _onChanged = widget.onChanged ?? _onChanged;
    final newText = widget.value?.toString() ?? _controller.text;
    //* update controller text
    if (double.tryParse(_controller.text) != double.tryParse(newText)) {
      _controller.text = newText;
      // Preserve cursor position when possible
      final newOffset = _controller.text.length;
      _controller.selection = TextSelection.collapsed(offset: newOffset);
    }
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final colorScheme = Theme.of(context).colorScheme;

    final fillColor =
        _readOnly ? colorScheme.surfaceDim.withAlpha(150) : colorScheme.surface;
    final textColor =
        _readOnly
            ? colorScheme.onSurfaceVariant.withAlpha(150)
            : textTheme.bodyMedium?.color;

    return Transform.scale(
      scale: widget.sizeScale,
      child: TextField(
        
        readOnly: _readOnly ?? false,
        controller: _controller,
        focusNode: _focusNode,
        style: textTheme.bodyMedium!.copyWith(color: textColor),
        decoration: InputDecoration(
          filled: true,
          fillColor: fillColor,
          helperText: _helperText,
          suffix:
              _tooltipText != null
                  ? Tooltip(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10.0),
                      color: colorScheme.tertiaryContainer.withAlpha(225),
                    ),
                    textStyle: textTheme.labelLarge!.copyWith(
                      color: colorScheme.onTertiaryContainer.withAlpha(225),
                    ),
                    message: _tooltipText,
                    child: Icon(
                      Icons.info_outline,
                      size: textTheme.titleMedium?.fontSize,
                      color: colorScheme.onPrimaryContainer,
                    ),
                  )
                  : null,
          border: OutlineInputBorder(),
          label: Text(_title),
          labelStyle: _titleStyle ?? textTheme.labelLarge,
          errorText: _errorMsg,
          isDense: true,
        ),
        keyboardType: TextInputType.number,
        inputFormatters: <TextInputFormatter>[
          FilteringTextInputFormatter.allow(
            RegExp(_allowNegative ? r'[0-9\.\-]' : r'[0-9\.]'),
          ),
        ],
        onChanged: (value) {
            try {
              _value = double.parse(value);
              if (_value > _maxValue) {
                _value = _maxValue;
                _controller.text = _value.toString();
              }
              if (_value < _minValue) {
                _value = _minValue;
                _controller.text = _value.toString();
              }
              if (_onChanged != null) {
                _onChanged!(_value);
              }
              _errorMsg = null;
            } catch (e) {
              _errorMsg = "Invalid Number";
            }
            
        },
      ),
    );
  }
}
