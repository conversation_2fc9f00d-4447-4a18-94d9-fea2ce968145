// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'pile_frictional_bored_input_global.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_PileFrictionalBoredInputGlobal _$PileFrictionalBoredInputGlobalFromJson(
  Map<String, dynamic> json,
) => _PileFrictionalBoredInputGlobal(
  colLoadFactor: (json['colLoadFactor'] as num?)?.toDouble() ?? 1.0,
  id: json['id'] as String? ?? '1',
);

Map<String, dynamic> _$PileFrictionalBoredInputGlobalToJson(
  _PileFrictionalBoredInputGlobal instance,
) => <String, dynamic>{
  'colLoadFactor': instance.colLoadFactor,
  'id': instance.id,
};
