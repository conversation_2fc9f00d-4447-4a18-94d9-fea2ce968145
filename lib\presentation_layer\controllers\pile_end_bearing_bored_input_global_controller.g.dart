// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'pile_end_bearing_bored_input_global_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$pileEndBearingBoredInputGlobalControllerHash() =>
    r'f5d749afe610180d00e7df046a2c74d995b7ea93';

/// See also [PileEndBearingBoredInputGlobalController].
@ProviderFor(PileEndBearingBoredInputGlobalController)
final pileEndBearingBoredInputGlobalControllerProvider =
    AutoDisposeAsyncNotifierProvider<
      PileEndBearingBoredInputGlobalController,
      PileEndBearingBoredInputGlobal
    >.internal(
      PileEndBearingBoredInputGlobalController.new,
      name: r'pileEndBearingBoredInputGlobalControllerProvider',
      debugGetCreateSourceHash:
          const bool.fromEnvironment('dart.vm.product')
              ? null
              : _$pileEndBearingBoredInputGlobalControllerHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$PileEndBearingBoredInputGlobalController =
    AutoDisposeAsyncNotifier<PileEndBearingBoredInputGlobal>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
