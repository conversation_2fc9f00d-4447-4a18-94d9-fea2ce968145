import 'package:freezed_annotation/freezed_annotation.dart';
// import 'package:flutter/foundation.dart';
part 'global_data.freezed.dart';
part 'global_data.g.dart';

@freezed
abstract class GlobalData with _$GlobalData{
  const GlobalData._();
  factory GlobalData({
    @Default('metrics') String unit,
    @Default(1.4) double sdlFactor,
    @Default(1.6) double llFactor,
    @Default(24.5) double rcUnitWeight,
    @Default(24.0) double finishUnitWeight,
    @Default(78.5) double steelUnitWeight,
    @Default(19.0) double soilUnitWeight,
    @Default(10.0) double waterUnitWeight,

    // @Default(true) bool showGlobalDataUiInSubPage,
  }) = _GlobalData;

  factory GlobalData.fromJson(Map<String, Object?> json) =>
      _$GlobalDataFromJson(json);
}
