import 'package:freezed_annotation/freezed_annotation.dart';
// import 'package:flutter/foundation.dart';
part 'pile_frictional_bored_input_global.freezed.dart';
part 'pile_frictional_bored_input_global.g.dart';

@freezed
abstract class PileFrictionalBoredInputGlobal with _$PileFrictionalBoredInputGlobal{
  const PileFrictionalBoredInputGlobal._();
  factory PileFrictionalBoredInputGlobal({
    @Default(1.0) double colLoadFactor,
    @Default('1') String id, //will be overriden  as soon as new instance created
  }) = _PileFrictionalBoredInputGlobal;

  factory PileFrictionalBoredInputGlobal.fromJson(Map<String, Object?> json) =>
      _$PileFrictionalBoredInputGlobalFromJson(json);
}
