// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'wind_load.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$WindLoad {

 double get h;// (Will be from Global Input) Building Height
 double get bTop;// (Will be from Global Input) building width at top
 double get dTop;// (Will be from Global Input) building depth at top
 double get sS;// (Will be from Global Input) size factor
 String get bldgType;// (Will be from Global Input) building depth at height considered
 double get amplificationFactor;// (Will be from Global Input)
 double get z;// height where pressure is considered
 double get b;// width where pressure is considered
 double get d;// depth where pressure is considered
 String get id;
/// Create a copy of WindLoad
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$WindLoadCopyWith<WindLoad> get copyWith => _$WindLoadCopyWithImpl<WindLoad>(this as WindLoad, _$identity);

  /// Serializes this WindLoad to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is WindLoad&&(identical(other.h, h) || other.h == h)&&(identical(other.bTop, bTop) || other.bTop == bTop)&&(identical(other.dTop, dTop) || other.dTop == dTop)&&(identical(other.sS, sS) || other.sS == sS)&&(identical(other.bldgType, bldgType) || other.bldgType == bldgType)&&(identical(other.amplificationFactor, amplificationFactor) || other.amplificationFactor == amplificationFactor)&&(identical(other.z, z) || other.z == z)&&(identical(other.b, b) || other.b == b)&&(identical(other.d, d) || other.d == d)&&(identical(other.id, id) || other.id == id));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,h,bTop,dTop,sS,bldgType,amplificationFactor,z,b,d,id);

@override
String toString() {
  return 'WindLoad(h: $h, bTop: $bTop, dTop: $dTop, sS: $sS, bldgType: $bldgType, amplificationFactor: $amplificationFactor, z: $z, b: $b, d: $d, id: $id)';
}


}

/// @nodoc
abstract mixin class $WindLoadCopyWith<$Res>  {
  factory $WindLoadCopyWith(WindLoad value, $Res Function(WindLoad) _then) = _$WindLoadCopyWithImpl;
@useResult
$Res call({
 double h, double bTop, double dTop, double sS, String bldgType, double amplificationFactor, double z, double b, double d, String id
});




}
/// @nodoc
class _$WindLoadCopyWithImpl<$Res>
    implements $WindLoadCopyWith<$Res> {
  _$WindLoadCopyWithImpl(this._self, this._then);

  final WindLoad _self;
  final $Res Function(WindLoad) _then;

/// Create a copy of WindLoad
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? h = null,Object? bTop = null,Object? dTop = null,Object? sS = null,Object? bldgType = null,Object? amplificationFactor = null,Object? z = null,Object? b = null,Object? d = null,Object? id = null,}) {
  return _then(_self.copyWith(
h: null == h ? _self.h : h // ignore: cast_nullable_to_non_nullable
as double,bTop: null == bTop ? _self.bTop : bTop // ignore: cast_nullable_to_non_nullable
as double,dTop: null == dTop ? _self.dTop : dTop // ignore: cast_nullable_to_non_nullable
as double,sS: null == sS ? _self.sS : sS // ignore: cast_nullable_to_non_nullable
as double,bldgType: null == bldgType ? _self.bldgType : bldgType // ignore: cast_nullable_to_non_nullable
as String,amplificationFactor: null == amplificationFactor ? _self.amplificationFactor : amplificationFactor // ignore: cast_nullable_to_non_nullable
as double,z: null == z ? _self.z : z // ignore: cast_nullable_to_non_nullable
as double,b: null == b ? _self.b : b // ignore: cast_nullable_to_non_nullable
as double,d: null == d ? _self.d : d // ignore: cast_nullable_to_non_nullable
as double,id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,
  ));
}

}


/// Adds pattern-matching-related methods to [WindLoad].
extension WindLoadPatterns on WindLoad {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _WindLoad value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _WindLoad() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _WindLoad value)  $default,){
final _that = this;
switch (_that) {
case _WindLoad():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _WindLoad value)?  $default,){
final _that = this;
switch (_that) {
case _WindLoad() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( double h,  double bTop,  double dTop,  double sS,  String bldgType,  double amplificationFactor,  double z,  double b,  double d,  String id)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _WindLoad() when $default != null:
return $default(_that.h,_that.bTop,_that.dTop,_that.sS,_that.bldgType,_that.amplificationFactor,_that.z,_that.b,_that.d,_that.id);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( double h,  double bTop,  double dTop,  double sS,  String bldgType,  double amplificationFactor,  double z,  double b,  double d,  String id)  $default,) {final _that = this;
switch (_that) {
case _WindLoad():
return $default(_that.h,_that.bTop,_that.dTop,_that.sS,_that.bldgType,_that.amplificationFactor,_that.z,_that.b,_that.d,_that.id);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( double h,  double bTop,  double dTop,  double sS,  String bldgType,  double amplificationFactor,  double z,  double b,  double d,  String id)?  $default,) {final _that = this;
switch (_that) {
case _WindLoad() when $default != null:
return $default(_that.h,_that.bTop,_that.dTop,_that.sS,_that.bldgType,_that.amplificationFactor,_that.z,_that.b,_that.d,_that.id);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _WindLoad extends WindLoad {
   _WindLoad({this.h = 0.0, this.bTop = 0.0, this.dTop = 0.0, this.sS = 0.0, this.bldgType = 'Concrete', this.amplificationFactor = 0.0, this.z = 0.0, this.b = 0.0, this.d = 0.0, this.id = ''}): super._();
  factory _WindLoad.fromJson(Map<String, dynamic> json) => _$WindLoadFromJson(json);

@override@JsonKey() final  double h;
// (Will be from Global Input) Building Height
@override@JsonKey() final  double bTop;
// (Will be from Global Input) building width at top
@override@JsonKey() final  double dTop;
// (Will be from Global Input) building depth at top
@override@JsonKey() final  double sS;
// (Will be from Global Input) size factor
@override@JsonKey() final  String bldgType;
// (Will be from Global Input) building depth at height considered
@override@JsonKey() final  double amplificationFactor;
// (Will be from Global Input)
@override@JsonKey() final  double z;
// height where pressure is considered
@override@JsonKey() final  double b;
// width where pressure is considered
@override@JsonKey() final  double d;
// depth where pressure is considered
@override@JsonKey() final  String id;

/// Create a copy of WindLoad
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$WindLoadCopyWith<_WindLoad> get copyWith => __$WindLoadCopyWithImpl<_WindLoad>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$WindLoadToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _WindLoad&&(identical(other.h, h) || other.h == h)&&(identical(other.bTop, bTop) || other.bTop == bTop)&&(identical(other.dTop, dTop) || other.dTop == dTop)&&(identical(other.sS, sS) || other.sS == sS)&&(identical(other.bldgType, bldgType) || other.bldgType == bldgType)&&(identical(other.amplificationFactor, amplificationFactor) || other.amplificationFactor == amplificationFactor)&&(identical(other.z, z) || other.z == z)&&(identical(other.b, b) || other.b == b)&&(identical(other.d, d) || other.d == d)&&(identical(other.id, id) || other.id == id));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,h,bTop,dTop,sS,bldgType,amplificationFactor,z,b,d,id);

@override
String toString() {
  return 'WindLoad(h: $h, bTop: $bTop, dTop: $dTop, sS: $sS, bldgType: $bldgType, amplificationFactor: $amplificationFactor, z: $z, b: $b, d: $d, id: $id)';
}


}

/// @nodoc
abstract mixin class _$WindLoadCopyWith<$Res> implements $WindLoadCopyWith<$Res> {
  factory _$WindLoadCopyWith(_WindLoad value, $Res Function(_WindLoad) _then) = __$WindLoadCopyWithImpl;
@override @useResult
$Res call({
 double h, double bTop, double dTop, double sS, String bldgType, double amplificationFactor, double z, double b, double d, String id
});




}
/// @nodoc
class __$WindLoadCopyWithImpl<$Res>
    implements _$WindLoadCopyWith<$Res> {
  __$WindLoadCopyWithImpl(this._self, this._then);

  final _WindLoad _self;
  final $Res Function(_WindLoad) _then;

/// Create a copy of WindLoad
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? h = null,Object? bTop = null,Object? dTop = null,Object? sS = null,Object? bldgType = null,Object? amplificationFactor = null,Object? z = null,Object? b = null,Object? d = null,Object? id = null,}) {
  return _then(_WindLoad(
h: null == h ? _self.h : h // ignore: cast_nullable_to_non_nullable
as double,bTop: null == bTop ? _self.bTop : bTop // ignore: cast_nullable_to_non_nullable
as double,dTop: null == dTop ? _self.dTop : dTop // ignore: cast_nullable_to_non_nullable
as double,sS: null == sS ? _self.sS : sS // ignore: cast_nullable_to_non_nullable
as double,bldgType: null == bldgType ? _self.bldgType : bldgType // ignore: cast_nullable_to_non_nullable
as String,amplificationFactor: null == amplificationFactor ? _self.amplificationFactor : amplificationFactor // ignore: cast_nullable_to_non_nullable
as double,z: null == z ? _self.z : z // ignore: cast_nullable_to_non_nullable
as double,b: null == b ? _self.b : b // ignore: cast_nullable_to_non_nullable
as double,d: null == d ? _self.d : d // ignore: cast_nullable_to_non_nullable
as double,id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

// dart format on
