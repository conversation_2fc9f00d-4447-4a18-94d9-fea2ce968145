import 'package:freezed_annotation/freezed_annotation.dart';
// import 'package:flutter/foundation.dart';
part 'pile_driven_input_global.freezed.dart';
part 'pile_driven_input_global.g.dart';

@freezed
abstract class PileDrivenInputGlobal with _$PileDrivenInputGlobal{
  const PileDrivenInputGlobal._();
  factory PileDrivenInputGlobal({
    @Default(1.0) double colLoadFactor,
    @Default('1') String id, //will be overriden  as soon as new instance created
  }) = _PileDrivenInputGlobal;

  factory PileDrivenInputGlobal.fromJson(Map<String, Object?> json) =>
      _$PileDrivenInputGlobalFromJson(json);
}
