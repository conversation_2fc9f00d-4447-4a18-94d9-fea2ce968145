// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'pile_frictional_bored_input.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_PileFrictionalBoredInput _$PileFrictionalBoredInputFromJson(
  Map<String, dynamic> json,
) => _PileFrictionalBoredInput(
  sptNValue: (json['sptNValue'] as num?)?.toDouble() ?? 50,
  soilUnitWeight: (json['soilUnitWeight'] as num?)?.toDouble() ?? 19,
  kTan: (json['kTan'] as num?)?.toDouble() ?? 0.25,
  fos: (json['fos'] as num?)?.toDouble() ?? 3,
  fcu: (json['fcu'] as num?)?.toDouble() ?? 45,
  maxPileLength: (json['maxPileLength'] as num?)?.toDouble() ?? 30,
  maxPileDiameter: (json['maxPileDiameter'] as num?)?.toDouble() ?? 2000,
  ratioOfBelloutDia: (json['ratioOfBelloutDia'] as num?)?.toDouble() ?? 1.65,
  maxSteelRatio: (json['maxSteelRatio'] as num?)?.toDouble() ?? 0.04,
  slsLoad: (json['slsLoad'] as num?)?.toDouble() ?? 1000,
  ulsLoad: (json['ulsLoad'] as num?)?.toDouble() ?? 2000,
  diaIncrement: (json['diaIncrement'] as num?)?.toDouble() ?? 200,
  useSelectColLoad: json['useSelectColLoad'] as bool? ?? false,
  id: json['id'] as String? ?? '1',
);

Map<String, dynamic> _$PileFrictionalBoredInputToJson(
  _PileFrictionalBoredInput instance,
) => <String, dynamic>{
  'sptNValue': instance.sptNValue,
  'soilUnitWeight': instance.soilUnitWeight,
  'kTan': instance.kTan,
  'fos': instance.fos,
  'fcu': instance.fcu,
  'maxPileLength': instance.maxPileLength,
  'maxPileDiameter': instance.maxPileDiameter,
  'ratioOfBelloutDia': instance.ratioOfBelloutDia,
  'maxSteelRatio': instance.maxSteelRatio,
  'slsLoad': instance.slsLoad,
  'ulsLoad': instance.ulsLoad,
  'diaIncrement': instance.diaIncrement,
  'useSelectColLoad': instance.useSelectColLoad,
  'id': instance.id,
};
