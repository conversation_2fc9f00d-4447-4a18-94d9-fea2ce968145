// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'transfer_beam_scheme_input_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$transferBeamSchemeInputControllerHash() =>
    r'b27677c0a5a4554a7486739cf23d524755299a7d';

/// See also [TransferBeamSchemeInputController].
@ProviderFor(TransferBeamSchemeInputController)
final transferBeamSchemeInputControllerProvider =
    AutoDisposeAsyncNotifierProvider<
      TransferBeamSchemeInputController,
      List<TransferBeamSchemeInput>
    >.internal(
      TransferBeamSchemeInputController.new,
      name: r'transferBeamSchemeInputControllerProvider',
      debugGetCreateSourceHash:
          const bool.fromEnvironment('dart.vm.product')
              ? null
              : _$transferBeamSchemeInputControllerHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$TransferBeamSchemeInputController =
    AutoDisposeAsyncNotifier<List<TransferBeamSchemeInput>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
