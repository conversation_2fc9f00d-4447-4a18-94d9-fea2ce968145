import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:structify/presentation_layer/small_elements/popup/custom_popup.dart';
import 'package:structify/presentation_layer/small_elements/sketch/draw_column_scheme_circle.dart';
import 'package:structify/presentation_layer/small_elements/sketch/draw_steel_truss_global_info.dart';

//presentation layer
// import '../controllers/steel_beam_scheme_input_controller.dart';
import 'input/custom_stateful_double_input.dart';
import '../screen/homescreen.dart';

//domain layer
import '../../domain_layer/preferences.dart';

class SteelCantileverTrussSchemeInputGlobalUi extends ConsumerStatefulWidget {
  const SteelCantileverTrussSchemeInputGlobalUi({
    this.isExpanded,
    this.maxHeight,
    this.minHeight,
    super.key,
  });

  final bool? isExpanded;
  final double? maxHeight;
  final double? minHeight;

  @override
  ConsumerState<ConsumerStatefulWidget> createState() =>
      _SteelCantileverTrussSchemeInputGlobalUiState();
}

class _SteelCantileverTrussSchemeInputGlobalUiState
    extends ConsumerState<SteelCantileverTrussSchemeInputGlobalUi>
    with WidgetsBindingObserver {
  late bool _isExpanded;
  late double _maxHeight;
  late double _minHeight;
  late ScrollController _controller;

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _controller.dispose();
    super.dispose();
  }

  @override
  void initState() {
    WidgetsBinding.instance.addObserver(this);
    _isExpanded = widget.isExpanded ?? false;
    _maxHeight = widget.maxHeight ?? 210;
    _minHeight = widget.minHeight ?? 50;
    _controller = ScrollController();
    // _updateHeights();

    super.initState();
  }

  @override
  void didChangeDependencies() {
    _updateHeights();
    super.didChangeDependencies();
    // Update heights when dependencies change, including after initState
  }

  @override
  void didChangeMetrics() {
    // This method is called when the metrics change (like resizing the window)
    setState(() {
      _updateHeights();
    });
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;
    final steelTransferTrussSchemeInputGlobal = ref.watch(
      steelTransferTrussSchemeInputGlobalControllerProvider,
    );

    // final beamSchemeData = ref.watch(beamSchemeDataControllerProvider);
    final globalData = ref.watch(globalDataControllerProvider);
    final loadingTables = ref.watch(loadingTablesControllerProvider);
    // ref.watch(appWatcherProvider);
    return loadingTables.when(
      data: (tables) {
        return globalData.when(
          data: (data) {
            late final List<String> unit;
            switch (data.unit) {
              case 'metrics':
                unit = PreferredUnit.metrics;
                break;
              case 'imperial':
                unit = PreferredUnit.imperial;
                break;
              default:
                unit = PreferredUnit.metrics;
                break;
            }
            return steelTransferTrussSchemeInputGlobal.when(
              data: (input) {
                final int containerOpacity = 175;
                final int textOpacity = 225;
                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Padding(
                      padding: const EdgeInsets.fromLTRB(10, 0, 10, 0),
                      child: Divider(),
                    ),
                    Padding(
                      padding: const EdgeInsets.fromLTRB(10, 0, 0, 0),
                      child: Align(
                        alignment: Alignment.centerLeft,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                IconButton(
                                  icon: Icon(
                                    _isExpanded
                                        ? Icons.expand_less
                                        : Icons.expand_more,
                                  ),
                                  color: colorScheme.onSurface,
                                  onPressed: () {
                                    setState(() {
                                      _isExpanded = !_isExpanded;
                                    });
                                  },
                                ),
                                Text(
                                  'Steel Cantilever Truss Settings',
                                  style: textTheme.titleLarge!.copyWith(
                                    color: colorScheme.onSurface,
                                  ),
                                ),
                                SizedBox(width: 10.0),
                                 CustomPopup(
                                  popupWidth: 550,
                                  popupHeight: 550,
                                  widgetList: [
                                    DrawSteelTrussGlobalInfo(
                                      sketchWidth: 500,
                                      sketchHeight: 500,
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.fromLTRB(10, 0, 10, 0),
                      child: Divider(),
                    ),
                    ClipRect(
                      child: ConstrainedBox(
                        constraints: BoxConstraints(
                          maxHeight: _isExpanded ? _maxHeight : 0,
                        ),
                        child: Scrollbar(
                          controller: _controller,
                          thumbVisibility: true,
                          trackVisibility: false,
                          child: SingleChildScrollView(
                            controller: _controller,
                            child: Column(
                              mainAxisSize: MainAxisSize.min,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Padding(
                                  padding: const EdgeInsets.fromLTRB(
                                    8.0,
                                    0.0,
                                    8.0,
                                    0.0,
                                  ),
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Container(
                                        decoration: BoxDecoration(
                                          borderRadius: BorderRadius.circular(
                                            10.0,
                                          ),
                                          border: Border.all(
                                            color: Colors.grey.withAlpha(150),
                                          ),
                                          color: colorScheme.secondaryContainer
                                              .withAlpha(containerOpacity),
                                        ),
                                        child: Padding(
                                          padding: const EdgeInsets.all(4.0),
                                          child: Text(
                                            'Basic Info',
                                            style: textTheme.titleSmall!
                                                .copyWith(
                                                  color: colorScheme
                                                      .onSecondaryContainer
                                                      .withAlpha(textOpacity),
                                                ),
                                          ),
                                        ),
                                      ),
                                      SizedBox(height: 10.0),
                                      Row(
                                        mainAxisSize: MainAxisSize.min,
                                        mainAxisAlignment:
                                            MainAxisAlignment.start,
                                        children: [
                                          
                                          Flexible(
                                            child: CustomStatefulDoubleInput(
                                              title: 'Fy [${unit[5]}]',
                                              value: input.fsy,
                                              onChanged: (value) async{
                                                await ref
                                                    .read(
                                                       steelCantileverTrussSchemeInputGlobalControllerProvider
                                                          .notifier,
                                                    )
                                                    .updateTable(fsy: value);
                                              },
                                            ),
                                          ),
                                          Flexible(child: SizedBox(width: 5.0)),
                                          Flexible(child: SizedBox(width: 5.0)),
                                          Flexible(child: SizedBox(width: 5.0)),
                                        ],
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                );
              },
              error: (error, stackTrace) => Text(error.toString()),
              loading: () => const CircularProgressIndicator(),
            );
          },
          error: (error, stackTrace) => Text(error.toString()),
          loading: () => Center(child: CircularProgressIndicator()),
        );
      },
      error: (error, stackTrace) => Text(error.toString()),
      loading: () => Center(child: CircularProgressIndicator()),
    );
  }

  void _updateHeights() {
    final double screenHeight = MediaQuery.of(context).size.height;
    _maxHeight =
        (widget.maxHeight == null)
            ? screenHeight * 0.5
            : (widget.maxHeight! > screenHeight * 0.5)
            ? screenHeight * 0.5
            : widget.maxHeight!;

    _minHeight =
        (widget.minHeight == null)
            ? screenHeight * 0.1
            : (widget.minHeight! > screenHeight * 0.1)
            ? screenHeight * 0.1
            : widget.minHeight!;

    // Adjust _maxHeight if needed

    if (_maxHeight < _minHeight) {
      _maxHeight = _minHeight;
    }
  }
}
