import 'dart:math' as math;

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:structify/domain_layer/column_scheme_data.dart';
import 'package:structify/presentation_layer/screen/homescreen.dart';
import 'package:vector_math/vector_math.dart' as vec;

import '../../../domain_layer/global_data.dart';
import '../../../domain_layer/preferences.dart';

class DrawColumnSchemeSquare extends ConsumerWidget {
  DrawColumnSchemeSquare({
    required this.sketchWidth,
    required this.sketchHeight,
    required this.index,
    this.fontSize,
    super.key,
  });

  final int index;
  final double sketchWidth;
  final double sketchHeight;
  double? fontSize;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final data1 = ref.watch(columnSchemeDataControllerProvider);
    final data2 = ref.watch(globalDataControllerProvider);
    final ColorScheme colorScheme = Theme.of(context).colorScheme;
    final TextTheme textTheme = Theme.of(context).textTheme;

    fontSize = fontSize ?? math.min(sketchWidth, sketchHeight) / 15;
    return data1.when(
      data: (columnSchemedata) {
        return data2.when(
          data: (globalData) {
            final constraints = BoxConstraints(
              maxWidth: sketchWidth,
              maxHeight: sketchHeight,
              minHeight: 100,
              minWidth: 100,
            );
            return Center(
              child: Container(
                decoration: BoxDecoration(
                  color: colorScheme.surfaceContainer.withAlpha(100),
                  borderRadius: BorderRadius.circular(10.0),
                  border: Border.all(color: Colors.black.withAlpha(100)),
                ),
                width:
                    constraints.maxWidth == double.infinity
                        ? 100
                        : constraints.maxWidth,
                height:
                    constraints.maxHeight == double.infinity
                        ? 100
                        : constraints.maxHeight,
                child: CustomPaint(
                  painter: DrawColumnSchemeSquarePainter(
                    columnSchemedata: columnSchemedata[index],
                    globalData: globalData,
                    boxConstraints: constraints,
                    fontSize: fontSize,
                    context: context,
                  ),
                ),
              ),
            );
          },

          error: (error, stackTrace) {
            return Text(error.toString());
          },
          loading: () {
            return const CircularProgressIndicator();
          },
        );
      },
      error: (error, stackTrace) {
        return Text(error.toString());
      },
      loading: () {
        return const CircularProgressIndicator();
      },
    );
  }
}

class DrawColumnSchemeSquarePainter extends CustomPainter {
  final ColumnSchemeData columnSchemedata;
  final GlobalData globalData;
  final BoxConstraints boxConstraints;
  double? fontSize;
  final BuildContext context;

  DrawColumnSchemeSquarePainter({
    required this.columnSchemedata,
    required this.globalData,
    required this.boxConstraints,
    this.fontSize,
    required this.context,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final ColorScheme colorScheme = Theme.of(context).colorScheme;
    final TextTheme textTheme = Theme.of(context).textTheme;
    late final double secBeamSpacing;

    fontSize = fontSize ?? math.min(size.width, size.height) / 40;

    Paint rcPaint =
        Paint()
          ..color = colorScheme.onSurface
          ..strokeCap = StrokeCap.round
          ..style = PaintingStyle.stroke
          ..strokeWidth = 1.0;

    Paint rebarPaint =
        Paint()
          ..color = colorScheme.onSurface
          ..strokeCap = StrokeCap.round
          ..style = PaintingStyle.fill
          ..strokeWidth = 2.0;

    final double minDim = math.min(
      boxConstraints.maxWidth,
      boxConstraints.maxHeight,
    );
    final double ratio = minDim / columnSchemedata.size * 2 / 3;

    late Offset startP1, endP1, startP2, endP2, startP3, endP3;

    Path path = Path();

    // ******************
    // draw slab
    // ******************
    startP1 = Offset(
      0.5 * (boxConstraints.maxWidth),
      0.5 * (boxConstraints.maxHeight),
    );

    canvas.drawRect(
      Rect.fromCenter(
        center: startP1,
        width: columnSchemedata.size * ratio,
        height: columnSchemedata.size * ratio,
      ),
      rcPaint,
    );

    _drawDimLine(
      canvas,
      fontSize!,
      startP1 -
          Offset(
            columnSchemedata.size * ratio / 2,
            columnSchemedata.size * ratio / 2,
          ),
      startP1 +
          Offset(
            columnSchemedata.size * ratio / 2,
            -columnSchemedata.size * ratio / 2,
          ),
      10,
      fontSize! / 2,
      columnSchemedata.size,
      globalData,
      context,
    );

    _drawDimLine(
      canvas,
      fontSize!,
      startP1 +
          Offset(
            -columnSchemedata.size * ratio / 2,
            columnSchemedata.size * ratio / 2,
          ),
      startP1 +
          Offset(
            -columnSchemedata.size * ratio / 2,
            -columnSchemedata.size * ratio / 2,
          ),
      -10,
      fontSize! / 2,
      columnSchemedata.size,
      globalData,
      context,
    );

    // ******************
    // draw rebars
    // ******************

    final rebar = columnSchemedata.mainBarSquare;
    final links = 'T12';

    List<String> rebarCorner = [], rebarOther = [];
    int cornerDia = 0, otherDia = 0;
    int nosOfCorner = 0, nosOfOther = 0;
    double spacing = 0,
        cover = columnSchemedata.cover * ratio,
        linkDia = 12 * ratio;
    final Color fontColor = Colors.red.withAlpha(200);
    final RegExp regex1 = RegExp(r"\b\w+(?=\+)"),
        regex2 = RegExp(r"(?<=\+)\w+"),
        regex3 = RegExp(r"\d+(?=T)"),
        regex4 = RegExp(r"(?<=T)\d+");

    // -------------------
    // Draw Corner bar
    // -------------------
    rebarCorner =
        regex1.allMatches(rebar).map((e) => e.group(0)!.toString()).toList();
    nosOfCorner =
        regex3
            .allMatches(rebarCorner[0])
            .map((e) => int.parse(e.group(0)!))
            .toList()[0];
    cornerDia =
        regex4
            .allMatches(rebarCorner[0])
            .map((e) => int.parse(e.group(0)!))
            .toList()[0];
    rebarOther =
        regex2.allMatches(rebar).map((e) => e.group(0)!.toString()).toList();
    nosOfOther =
        regex3
            .allMatches(rebarOther[0])
            .map((e) => int.parse(e.group(0)!))
            .toList()[0];
    otherDia =
        regex4
            .allMatches(rebarOther[0])
            .map((e) => int.parse(e.group(0)!))
            .toList()[0];

    startP2 =
        startP1 -
        Offset(
          columnSchemedata.size / 2 * ratio,
          columnSchemedata.size / 2 * ratio,
        ) +
        Offset(cover + linkDia, cover + linkDia) +
        Offset(cornerDia / 2 * ratio, cornerDia / 2 * ratio);
    endP2 =
        startP2 +
        Offset(
          columnSchemedata.size * ratio -
              2 * cover -
              2 * linkDia -
              2 * cornerDia / 2 * ratio,
          0,
        );
    startP3 =
        startP2 +
        Offset(0, columnSchemedata.size * ratio) -
        Offset(0, 2 * cover + 2 * linkDia) -
        Offset(0, 2 * cornerDia / 2 * ratio);
    endP3 =
        startP3 +
        Offset(
          columnSchemedata.size * ratio -
              2 * cover -
              2 * linkDia -
              2 * cornerDia / 2 * ratio,
          0,
        );
    canvas.drawCircle(startP2, cornerDia / 2 * ratio, rebarPaint);
    canvas.drawCircle(endP2, cornerDia / 2 * ratio, rebarPaint);
    canvas.drawCircle(startP3, cornerDia / 2 * ratio, rebarPaint);
    canvas.drawCircle(endP3, cornerDia / 2 * ratio, rebarPaint);

    spacing = (endP2.dx - startP2.dx) / ((nosOfOther / 4) + 2 - 1);

    for (int i = 0; i < nosOfOther / 4; i++) {
      canvas.drawCircle(
        startP2 + Offset(spacing * (i + 1), 0),
        cornerDia / 2 * ratio,
        rebarPaint,
      );
    }
    for (int i = 0; i < nosOfOther / 4; i++) {
      canvas.drawCircle(
        startP3 + Offset(spacing * (i + 1), 0),
        cornerDia / 2 * ratio,
        rebarPaint,
      );
    }
    for (int i = 0; i < nosOfOther / 4; i++) {
      canvas.drawCircle(
        startP2 + Offset(0, spacing * (i + 1)),
        cornerDia / 2 * ratio,
        rebarPaint,
      );
    }
    for (int i = 0; i < nosOfOther / 4; i++) {
      canvas.drawCircle(
        endP2 + Offset(0, spacing * (i + 1)),
        cornerDia / 2 * ratio,
        rebarPaint,
      );
    }
    //draw links
    canvas.drawRRect(
      RRect.fromRectXY(
        Rect.fromCenter(
          center: startP1,
          width: (columnSchemedata.size) * ratio - 2 * cover,
          height: (columnSchemedata.size) * ratio - 2 * cover,
        ),
        2,
        2,
      ),
      rcPaint,
    );

    // Text for Corner Bar
    TextSpan textSpan = TextSpan(
      text: 'T$cornerDia',
      style: TextStyle(
        fontSize: fontSize,
        color: fontColor,
        fontWeight: FontWeight.bold,
      ),
    );

    TextPainter textPainter = TextPainter(
      text: textSpan,
      textAlign: TextAlign.center,
      textDirection: TextDirection.ltr,
    )..layout();

    textPainter.paint(
      canvas,
      startP2 +
          Offset(
            -columnSchemedata.size * ratio / 5,
            -columnSchemedata.size * ratio / 9,
          ),
    );

    textPainter.paint(
      canvas,
      startP3 +
          Offset(
            -columnSchemedata.size * ratio / 5,
            columnSchemedata.size * ratio / 15,
          ),
    );
    textPainter.paint(
      canvas,
      endP2 + Offset(0, -columnSchemedata.size * ratio / 9),
    );
    textPainter.paint(
      canvas,
      endP3 + Offset(0, columnSchemedata.size * ratio / 15),
    );

    //Text for middle Bar
    textSpan = TextSpan(
      text: 'T$otherDia',
      style: TextStyle(
        fontSize: fontSize,
        color: fontColor,
        fontWeight: FontWeight.bold,
      ),
    );

    textPainter = TextPainter(
      text: textSpan,
      textAlign: TextAlign.center,
      textDirection: TextDirection.ltr,
    )..layout();

    textPainter.paint(
      canvas,
      (startP2 + startP3) / 2 +
          Offset(
            columnSchemedata.size * ratio / 25,
            -columnSchemedata.size * ratio / 15,
          ),
    );

    textPainter.paint(
      canvas,
      (endP2 + endP3) / 2 +
          Offset(
            -columnSchemedata.size * ratio / 5,
            -columnSchemedata.size * ratio / 15,
          ),
    );

    textPainter.paint(
      canvas,
      (startP2 + endP2) / 2 +
          Offset(
            -columnSchemedata.size * ratio / 15,
            columnSchemedata.size * ratio / 50,
          ),
    );

    textPainter.paint(
      canvas,
      (startP3 + endP3) / 2 +
          Offset(
            -columnSchemedata.size * ratio / 15,
            -columnSchemedata.size * ratio / 7,
          ),
    );
  }

  // void _rebarDesignation(
  //   List<int> rebarDia,
  //   int i,
  //   List<int> rebarSpacing,
  //   ui.Color fontColor,
  //   ui.Canvas canvas,
  //   ui.Offset endP3,
  // ) {
  //   TextSpan textSpan = TextSpan(
  //     text: 'T${rebarDia[i]}-${rebarSpacing[i]}',
  //     style: TextStyle(
  //       fontSize: fontSize,
  //       color: fontColor,
  //       fontWeight: FontWeight.bold,
  //     ),
  //   );

  //   TextPainter textPainter = TextPainter(
  //     text: textSpan,
  //     textAlign: TextAlign.center,
  //     textDirection: TextDirection.ltr,
  //   )..layout();

  //   textPainter.paint(canvas, endP3 + Offset(7, -7));
  // }

  void _drawDimLine(
    Canvas canvas,
    double fontSize,
    Offset start,
    Offset end,
    double offsetDistance,
    double dimExtension,
    double slabDepth,
    GlobalData globalData,
    BuildContext context,
  ) {
    double tempOffset = 10; // forthe dim line end
    late Offset tempEnd;
    late Offset tempStart;
    late Offset textCenter;

    late double angle;

    late List<String> unit;

    Color fontColor = Colors.red.withAlpha(200);

    late final vec.Vector2 u;
    late final vec.Vector2 uUnit;
    late final vec.Vector2 vUnit;
    late final vec.Vector2 vUnit45;
    late final vec.Matrix2 m;

    final ColorScheme colorScheme = Theme.of(context).colorScheme;
    final TextTheme textTheme = Theme.of(context).textTheme;

    // get the unit
    switch (globalData.unit) {
      case 'metrics':
        unit = PreferredUnit.metrics;
        break;
      case 'imperial':
        unit = PreferredUnit.imperial;
        break;
      default:
        unit = PreferredUnit.metrics;
    }

    // double dimExtension = offsetDistance.abs() / 2;

    final Paint paint =
        Paint()
          ..color = fontColor
          ..strokeWidth = 1.0
          ..style = PaintingStyle.stroke
          ..strokeJoin = StrokeJoin.round;

    // line vector
    u = vec.Vector2(end.dx - start.dx, end.dy - start.dy);
    uUnit = u / u.length;

    // orthogonal vector
    vUnit = vec.Vector2(uUnit.y, uUnit.x); //rotate aniti-clockwise

    // Vector of parallel line with Offset
    Offset newStart = Offset(
      start.dx - vUnit.x * offsetDistance - uUnit.x * dimExtension,
      start.dy - vUnit.y * offsetDistance - uUnit.y * dimExtension,
    );
    Offset newEnd = Offset(
      end.dx - vUnit.x * offsetDistance + uUnit.x * dimExtension,
      end.dy - vUnit.y * offsetDistance + uUnit.y * dimExtension,
    );
    canvas.drawLine(newStart, newEnd, paint);

    // draw start crossing
    tempStart = Offset(
      (start.dx - vUnit.x * offsetDistance) - vUnit.x * dimExtension,
      (start.dy - vUnit.y * offsetDistance) - vUnit.y * dimExtension,
    );
    tempEnd = Offset(
      (start.dx - vUnit.x * offsetDistance) + vUnit.x * dimExtension,
      (start.dy - vUnit.y * offsetDistance) + vUnit.y * dimExtension,
    );
    canvas.drawLine(tempStart, tempEnd, paint);

    m = vec.Matrix2.rotation(-45 * math.pi / 180);
    vUnit45 = m * vUnit;
    tempStart = Offset(
      (start.dx - vUnit.x * offsetDistance) - vUnit45.x * dimExtension,
      (start.dy - vUnit.y * offsetDistance) - vUnit45.y * dimExtension,
    );
    tempEnd = Offset(
      (start.dx - vUnit.x * offsetDistance) + vUnit45.x * dimExtension,
      (start.dy - vUnit.y * offsetDistance) + vUnit45.y * dimExtension,
    );
    canvas.drawLine(tempStart, tempEnd, paint);

    // draw end crossing
    tempStart = Offset(
      (end.dx - vUnit.x * offsetDistance) - vUnit.x * dimExtension,
      (end.dy - vUnit.y * offsetDistance) - vUnit.y * dimExtension,
    );
    tempEnd = Offset(
      (end.dx - vUnit.x * offsetDistance) + vUnit.x * dimExtension,
      (end.dy - vUnit.y * offsetDistance) + vUnit.y * dimExtension,
    );
    canvas.drawLine(tempStart, tempEnd, paint);

    tempStart = Offset(
      (end.dx - vUnit.x * offsetDistance) - vUnit45.x * dimExtension,
      (end.dy - vUnit.y * offsetDistance) - vUnit45.y * dimExtension,
    );
    tempEnd = Offset(
      (end.dx - vUnit.x * offsetDistance) + vUnit45.x * dimExtension,
      (end.dy - vUnit.y * offsetDistance) + vUnit45.y * dimExtension,
    );
    canvas.drawLine(tempStart, tempEnd, paint);

    // indicate span length and beam size

    TextSpan textSpan = TextSpan(
      text: slabDepth.toStringAsFixed(0),
      style: TextStyle(
        fontSize: fontSize,
        color: fontColor,
        fontWeight: FontWeight.bold,
      ),
    );

    TextPainter textPainter = TextPainter(
      text: textSpan,
      textAlign: TextAlign.center,
      textDirection: TextDirection.ltr,
    )..layout();

    textCenter = Offset(
      (start.dx + end.dx) / 2 -
          vUnit.x * offsetDistance * 2 -
          uUnit.x * textPainter.width / 2,
      (start.dy + end.dy) / 2 -
          vUnit.y * offsetDistance * 2 -
          uUnit.y * textPainter.width / 2,
    );

    angle = math.atan2(end.dy - start.dy, end.dx - start.dx) * 180 / math.pi;

    canvas.save();
    canvas.translate(textCenter.dx, textCenter.dy);
    canvas.rotate(angle * math.pi / 180);
    textPainter.paint(canvas, Offset.zero);
    canvas.restore();
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    // TODO: implement shouldRepaint
    return false;
  }
}
