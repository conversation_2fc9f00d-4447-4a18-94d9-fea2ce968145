import 'dart:math';

import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:nanoid2/nanoid2.dart';
import '../../domain_layer/loading_data.dart';

//presentation layer
import '../screen/homescreen.dart';

// domain layer
import '../../domain_layer/loading_table.dart';
part 'loading_tables_controller.g.dart';

@riverpod
class LoadingTablesController extends _$LoadingTablesController {
  
  @override
  FutureOr<List<LoadingTable>> build() async {
    // print('Build: Loading Tables');
    final loadingTables =
        await ref
            .read(appDatabaseControllerProvider.notifier)
            .queryAllLoadingTables();
    if (loadingTables.isEmpty) {
      String newID = nanoid(alphabet: Alphabet.noDoppelgangerSafe);
      return [LoadingTable(loadingTableId: newID)];
    } else {
      return loadingTables;
    }
  }

  Future<void> addEmptytable() async {
    final x = await future;
    final id = await _generateTaskID();
    final usages = x.map((x1) => x1.usage).toSet();
    if (usages.isEmpty) {
      state = AsyncData([...x, LoadingTable(loadingTableId: id)]);
    } else {
      String newUsage = '${usages.last}_1';
      while (usages.contains(newUsage)) {
        newUsage = '${newUsage}_1';
      }
      state = AsyncData([
        ...x,
        LoadingTable(usage: newUsage, loadingTableId: id),
      ]);
    }
  }

  Future<void> addTable(LoadingTable loadingTable) async {
    final x = await future;
    final id = await _generateTaskID();
    final usages = x.map((x1) => x1.usage).toSet();
    if (usages.isEmpty) {
      state = AsyncData([...x, LoadingTable(loadingTableId: id)]);
    } else {
      String newUsage = '${loadingTable.usage}';
      while (usages.contains(newUsage)) {
        newUsage = '${newUsage}_1';
      }
      state = AsyncData([
        ...x,
        loadingTable.copyWith(usage: newUsage, loadingTableId: id),
      ]);
    }
  }

  Future<void> deleteTable(String id) async {
    // print("Before deletion: ${state.map((item) => item.usage).toList()}");
    final x = await future;
    x.removeWhere((item) => item.loadingTableId == id);
    // print("After deletion: ${x.map((item) => item.usage).toList()}");
    state = AsyncData(x);
  }

  // Future<void> insertEmptyTable(int index) async {
  //   final x = await future;
  //   final id = await _generateTaskID();
  //   x.insert(index + 1, LoadingTable(loadingTableId: id));
  //   state = AsyncData(x);
  // }

  Future<void> copyAndInsertTable(int index) async {
    final x = await future;
    final id = await _generateTaskID();
    final usages = x.map((x1) => x1.usage).toList();
    String newUsage = '${usages[index]}_1';
    while (usages.contains(newUsage)) {
      newUsage = '${newUsage}_1';
    }
    x.insert(index + 1, x[index].copyWith(usage: newUsage, loadingTableId: id));
    state = AsyncData(x);
  }

  Future<String> _generateTaskID() async {
    String newID = nanoid(alphabet: Alphabet.noDoppelgangerSafe);
    final x = await future;
    final Set<String> loadingTableIds =
        x.map((item) => item.loadingTableId).toSet();
    // final Set<String> taskIDs = ref.read(_taskIDsProvider);
    while (loadingTableIds.contains(newID)) {
      newID = nanoid(alphabet: Alphabet.noDoppelgangerSafe);
    }
    return newID;
  }

  Future<void> updateTable(
    String id, {
    String? usage,
    double? finish,
    double? service,
    double? liveload,
  }) async {
    final x = await future;

    // Create a list of updates
    final newState =
        x.map((item) {
          if (item.loadingTableId == id) {
            return item.copyWith(
              usage: usage ?? item.usage,
              finish: finish ?? item.finish,
              service: service ?? item.service,
              liveLoad: liveload ?? item.liveLoad,
              loadingTableId: item.loadingTableId,
            );
          }
          return item;
        }).toList();

    state = AsyncLoading();
    state = AsyncData(newState);
  }

  Future<double> getSDL(String usage) async {
    final x = await future;
    final globalData = await ref.read(globalDataControllerProvider.future);
    final table = x.firstWhere((element) => element.usage == usage);
    return table.finish * globalData.finishUnitWeight * pow(10, -3) +
        table.service;
  }

  Future<double> getLL(String usage) async {
    final x = await future;
    final globalData = await ref.read(globalDataControllerProvider.future);
    final table = x.firstWhere((element) => element.usage == usage);
    return table.liveLoad;
  }

  Future<double> getULS(String usage) async {
    final x = await future;
    final globalData = await ref.read(globalDataControllerProvider.future);
    final table = x.firstWhere((element) => element.usage == usage);
    return globalData.sdlFactor *
            (table.finish * globalData.finishUnitWeight * pow(10, -3) +
                table.service) +
        globalData.llFactor * table.liveLoad;
  }

  Future<double> getSLS(String usage) async {
    final x = await future;
    final globalData = await ref.read(globalDataControllerProvider.future);
    final table = x.firstWhere((element) => element.usage == usage);
    return table.finish * globalData.finishUnitWeight * pow(10, -3) +
        table.service +
        table.liveLoad;
  }

  Future<LoadingData> getLoadingData(String usage) async {
    final x = await future;
    final globalData = await ref.read(globalDataControllerProvider.future);
    final table = x.firstWhere((element) => element.usage == usage);
    double sdl =
        table.finish * globalData.finishUnitWeight * pow(10, -3) +
        table.service;
    double ll = table.liveLoad;
    double slsLoad = sdl + ll;
    double ulsLoad = globalData.sdlFactor * sdl + globalData.llFactor * ll;

    return LoadingData(sdl: sdl, ll: ll, slsLoad: slsLoad, ulsLoad: ulsLoad);
  }
}
