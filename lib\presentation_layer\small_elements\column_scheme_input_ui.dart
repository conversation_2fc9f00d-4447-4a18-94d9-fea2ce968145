import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:structify/presentation_layer/small_elements/input/custom_stateful_int_input.dart';

//domain layer
import '../../domain_layer/mixin/mixin_tools_for_ui.dart';
import '../../domain_layer/preferences.dart';

// presentation layer
import '../../domain_layer/slab_scheme_data.dart';
import '../../misc/show_overlay_msg.dart';
import '../screen/homescreen.dart';
import 'input/custom_stateful_double_input.dart';
import 'input/custom_stateful_text_input.dart';
import 'input/custom_stateful_dropList.dart';
import 'button/function_button.dart';
import 'popup/multiseleciton_dialog.dart';
import 'button/selection_button.dart';
// import '../small_elements/global_data_ui.dart';
// import '../small_elements/loadcals_summary_ui.dart';

class ColumnSchemeInputUi extends ConsumerStatefulWidget {
  const ColumnSchemeInputUi({
    this.isExpanded,
    this.maxHeight,
    this.minHeight,
    super.key,
  });

  final bool? isExpanded;
  final double? maxHeight;
  final double? minHeight;

  @override
  ConsumerState<ConsumerStatefulWidget> createState() =>
      _ColumnSchemeInputUiState();
}

class _ColumnSchemeInputUiState extends ConsumerState<ColumnSchemeInputUi>
    with WidgetsBindingObserver {
  late bool _isExpanded;
  late double _maxHeight;
  late double _minHeight;
  late CustomStatefulTextInput _usageInput;
  late GlobalKey _buttonKey;

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void initState() {
    WidgetsBinding.instance.addObserver(this);
    _isExpanded = widget.isExpanded ?? false;
    _maxHeight = widget.maxHeight ?? 300;
    _minHeight = widget.minHeight ?? 50;
    _usageInput = CustomStatefulTextInput();
    _buttonKey = GlobalKey();
    // _updateHeights();

    super.initState();
  }

  @override
  void didChangeDependencies() {
    _updateHeights();
    super.didChangeDependencies();
    // Update heights when dependencies change, including after initState
  }

  @override
  void didChangeMetrics() {
    // This method is called when the metrics change (like resizing the window)
    setState(() {
      _updateHeights();
    });
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;
    final globalData = ref.watch(globalDataControllerProvider);
    final loadingTables = ref.watch(loadingTablesControllerProvider);
    final columnInput = ref.watch(columnSchemeInputControllerProvider);
    final columnInputGlobal = ref.watch(
      columnSchemeInputGlobalControllerProvider,
    );

    final slabSchemeData = ref.watch(slabSchemeDataControllerProvider);
    final scrollController = ScrollController();

    return globalData.when(
      data: (data) {
        late final List<String> units;
        switch (data.unit) {
          case 'metrics':
            units = PreferredUnit.metrics;
            break;
          case 'imperial':
            units = PreferredUnit.imperial;
            break;
          default:
            units = PreferredUnit.metrics;
            break;
        }

        return loadingTables.when(
          data: (tables) {
            return columnInput.when(
              data: (input) {
                return columnInputGlobal.when(
                  data: (inputGlobal) {
                    return slabSchemeData.when(
                      data: (slabData) {
                        final int containerOpacity = 175;
                        final int textOpacity = 225;
                        return Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Padding(
                              padding: const EdgeInsets.fromLTRB(10, 0, 10, 0),
                              child: Divider(),
                            ),
                            Padding(
                              padding: const EdgeInsets.fromLTRB(10, 0, 0, 0),
                              child: Align(
                                alignment: Alignment.centerLeft,
                                child: Wrap(
                                  crossAxisAlignment: WrapCrossAlignment.center,
                                  children: [
                                    Wrap(
                                      crossAxisAlignment:
                                          WrapCrossAlignment.center,
                                      children: [
                                        IconButton(
                                          icon: Icon(
                                            _isExpanded
                                                ? Icons.keyboard_arrow_up
                                                : Icons.keyboard_arrow_down,
                                            color: colorScheme.onSurface,
                                          ),
                                          onPressed: () {
                                            setState(() {
                                              _isExpanded = !_isExpanded;
                                            });
                                          },
                                        ),
                                        Text(
                                          'RC Column Scheme Inputs ',
                                          style: textTheme.titleLarge!.copyWith(
                                            color: colorScheme.onSurface,
                                          ),
                                        ),
                                      ],
                                    ),
                                    Container(
                                      decoration: BoxDecoration(
                                        borderRadius: BorderRadius.circular(
                                          5.0,
                                        ),
                                        color: colorScheme.surfaceContainer
                                            .withAlpha(225),
                                        border: Border.all(
                                          color: Colors.black.withAlpha(125),
                                        ),
                                      ),
                                      child: Padding(
                                        padding: const EdgeInsets.all(2.0),
                                        child: Text(
                                          '(Total: ${input.length})',
                                          style: textTheme.labelMedium!
                                              .copyWith(
                                                color: colorScheme
                                                    .onSurfaceVariant
                                                    .withAlpha(225),
                                              ),
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                            Padding(
                              padding: const EdgeInsets.fromLTRB(10, 0, 10, 0),
                              child: Divider(),
                            ),
                            Padding(
                              padding: const EdgeInsets.fromLTRB(
                                8.0,
                                0.0,
                                8.0,
                                0.0,
                              ),
                              child: ConstrainedBox(
                                constraints: BoxConstraints(
                                  maxHeight: _isExpanded ? _maxHeight : 0,
                                ),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Container(
                                      decoration: BoxDecoration(
                                        borderRadius: BorderRadius.circular(
                                          10.0,
                                        ),
                                        border: Border.all(
                                          color: Colors.grey.withAlpha(150),
                                        ),
                                        color: colorScheme.secondaryContainer
                                            .withAlpha(containerOpacity),
                                      ),
                                      child: Padding(
                                        padding: const EdgeInsets.all(4.0),
                                        child: Text(
                                          'Iterations Parameters',
                                          style: textTheme.titleSmall!.copyWith(
                                            color: colorScheme
                                                .onSecondaryContainer
                                                .withAlpha(textOpacity),
                                          ),
                                        ),
                                      ),
                                    ),
                                    SizedBox(height: 10.0),
                                    Row(
                                      children: [
                                        Flexible(
                                          child: CustomStatefulDoubleInput(
                                            title: 'Design Safety Factor',
                                            value: inputGlobal.safetyFactor,

                                            tooltipText:
                                                'Demand axial load will be\n'
                                                'multiplied by this factor',
                                            titleStyle: textTheme.labelLarge!
                                                .copyWith(
                                                  color: colorScheme.error
                                                      .withAlpha(200),
                                                ),
                                            onChanged: (value) {
                                              ref
                                                  .read(
                                                    columnSchemeInputGlobalControllerProvider
                                                        .notifier,
                                                  )
                                                  .updateTable(
                                                    safetyFactor: value,
                                                  );
                                            },
                                          ),
                                        ),
                                        SizedBox(width: 5.0),
                                        Flexible(
                                          child: CustomStatefulIntInput(
                                            title: 'Iteration Steps',
                                            value: inputGlobal.iterationSteps,
                                            onChanged: (value) {
                                              ref
                                                  .read(
                                                    columnSchemeInputGlobalControllerProvider
                                                        .notifier,
                                                  )
                                                  .updateTable(
                                                    iterationSteps: value,
                                                  );
                                            },
                                          ),
                                        ),
                                        SizedBox(width: 5.0),
                                        Flexible(
                                          child: CustomStatefulDoubleInput(
                                            title:
                                                'Size Increment [${units[4]}]',
                                            value: inputGlobal.sizeIncrement,
                                            onChanged: (value) {
                                              ref
                                                  .read(
                                                    columnSchemeInputGlobalControllerProvider
                                                        .notifier,
                                                  )
                                                  .updateTable(
                                                    sizeIncrement: value,
                                                  );
                                            },
                                          ),
                                        ),
                                        Flexible(child: SizedBox(width: 5.0)),
                                      ],
                                    ),
                                    SizedBox(height: 10.0),
                                    Row(
                                      children: [
                                        Flexible(
                                          child: MultiSelectionDialog(
                                            dialogWidth: 50.0,
                                            dialogHeight: 400.0,
                                            label: 'Grade',
                                            selectedOptions: inputGlobal
                                                .concreteGrade
                                                .split(','),
                                            options:
                                                MaterialProps.concreteGrade,
                                            onPressed: (selectedOptions) async {
                                              final String concreteGrade =
                                                  selectedOptions.join(',');
                                              if (concreteGrade.isNotEmpty) {
                                                await ref
                                                    .read(
                                                      columnSchemeInputGlobalControllerProvider
                                                          .notifier,
                                                    )
                                                    .updateTable(
                                                      concreteGrade:
                                                          concreteGrade,
                                                    );
                                              } else {
                                                //default to be C45 only
                                                await ref
                                                    .read(
                                                      columnSchemeInputGlobalControllerProvider
                                                          .notifier,
                                                    )
                                                    .updateTable(
                                                      concreteGrade: "C45",
                                                    );
                                              }
                                            },
                                          ),
                                        ),
                                        SizedBox(width: 5.0),
                                        SelectionButton(
                                          labelTextStyle:
                                              inputGlobal.useSlabSelected
                                                  ? textTheme.labelLarge!.copyWith(
                                                    color:
                                                        colorScheme
                                                            .onTertiaryContainer,
                                                  )
                                                  : textTheme.labelLarge!
                                                      .copyWith(
                                                        color:
                                                            colorScheme
                                                                .onSurface,
                                                      ),
                                          labelText: 'Use Slab Selected',
                                          pressedColor:
                                              colorScheme.tertiaryContainer,
                                          bgColor:
                                              inputGlobal.useSlabSelected
                                                  ? colorScheme
                                                      .tertiaryContainer
                                                  : colorScheme
                                                      .surfaceContainer,

                                          onTap: (value) async {
                                            final inputGlobal = await ref.read(
                                              columnSchemeInputGlobalControllerProvider
                                                  .future,
                                            );

                                            await ref
                                                .read(
                                                  columnSchemeInputGlobalControllerProvider
                                                      .notifier,
                                                )
                                                .updateTable(
                                                  useSlabSelected:
                                                      !inputGlobal
                                                          .useSlabSelected,
                                                );
                                            //* we avoid get the input again here for better performance
                                            if (!inputGlobal.useSlabSelected) {
                                              final selectedSlab = slabData
                                                  .firstWhere(
                                                    (scheme) =>
                                                        scheme.isSelected,
                                                    orElse:
                                                        () => SlabSchemeData(),
                                                  );
                                              final columnInputs = await ref.read(
                                                columnSchemeInputControllerProvider
                                                    .future,
                                              );
                                              for (
                                                int i = 0;
                                                i < columnInputs.length;
                                                i++
                                              ) {
                                                await ref
                                                    .read(
                                                      columnSchemeInputControllerProvider
                                                          .notifier,
                                                    )
                                                    .updateTable(
                                                      columnSchemeInputId:
                                                          columnInputs[i]
                                                              .columnSchemeInputId,
                                                      slabThickness:
                                                          selectedSlab.strZone,
                                                    );
                                              }
                                            }
                                          },
                                        ),
                                      ],
                                    ),
                                    SizedBox(height: 10.0),
                                    Flexible(
                                      child: Column(
                                        children: [
                                          Row(
                                            mainAxisAlignment:
                                                MainAxisAlignment.start,
                                            children: [
                                              FunctionButton(
                                                labelIcon: Icon(
                                                  Icons.add_box_outlined,
                                                ),
                                                labelText: 'Add',
                                                onTap: (isPressed) async {
                                                  await ref
                                                      .read(
                                                        columnSchemeInputControllerProvider
                                                            .notifier,
                                                      )
                                                      .addEmptytable();
                                                },
                                              ),
                                              SizedBox(width: 5.0),
                                              FunctionButton(
                                                key: _buttonKey,
                                                labelIcon: Icon(
                                                  Icons.calculate_outlined,
                                                ),
                                                labelText: 'Run',
                                                onTap: (isPressed) async {
                                                  final columnSchemeDataList =
                                                      await ref
                                                          .read(
                                                            columnSchemeDataControllerProvider
                                                                .notifier,
                                                          )
                                                          .bacthColumnScheming();
                                                  if (columnSchemeDataList
                                                      .isEmpty) {
                                                    showGentleMessageBox(
                                                      offsetX: 200,
                                                      offsetY: -5,
                                                      context,
                                                      _buttonKey,
                                                      'No RC Column Scheme Pass!',
                                                      containerColor:
                                                          colorScheme
                                                              .errorContainer
                                                              .withAlpha(175),
                                                      textStyle: textTheme
                                                          .labelMedium!
                                                          .copyWith(
                                                            color: colorScheme
                                                                .onErrorContainer
                                                                .withAlpha(175),
                                                          ),
                                                    );
                                                  }
                                                },
                                              ),
                                              SizedBox(width: 5.0),
                                              FunctionButton(
                                                labelIcon: Icon(
                                                  Icons.delete_outlined,
                                                ),
                                                labelText: 'Clear Schemes',
                                                onTap: (isPressed) async {
                                                  await ref
                                                      .read(
                                                        columnSchemeDataControllerProvider
                                                            .notifier,
                                                      )
                                                      .deleteAllTable();
                                                },
                                              ),
                                            ],
                                          ),

                                          SizedBox(height: 10),
                                          Expanded(
                                            child: Scrollbar(
                                              controller: scrollController,
                                              thumbVisibility: true,
                                              child: ListView.builder(
                                                controller: scrollController,
                                                itemCount: input.length,
                                                itemBuilder: (context, index) {
                                                  return Padding(
                                                    padding:
                                                        const EdgeInsets.fromLTRB(
                                                          8.0,
                                                          4.0,
                                                          8.0,
                                                          4.0,
                                                        ),
                                                    child: Row(
                                                      mainAxisSize:
                                                          MainAxisSize.min,
                                                      mainAxisAlignment:
                                                          MainAxisAlignment
                                                              .start,
                                                      children: [
                                                        Container(
                                                          decoration: BoxDecoration(
                                                            shape:
                                                                BoxShape.circle,
                                                            color: colorScheme
                                                                .surfaceContainer
                                                                .withAlpha(200),
                                                            // borderRadius: BorderRadius.circular(5),
                                                            border: Border.all(
                                                              color:
                                                                  Colors.black,
                                                            ),
                                                          ),
                                                          child: Padding(
                                                            padding:
                                                                const EdgeInsets.all(
                                                                  4.0,
                                                                ),
                                                            child: Text(
                                                              '${index + 1}',
                                                              style: textTheme
                                                                  .labelLarge!
                                                                  .copyWith(
                                                                    color: colorScheme
                                                                        .onSurfaceVariant
                                                                        .withAlpha(
                                                                          250,
                                                                        ),
                                                                  ),
                                                            ),
                                                          ),
                                                        ),
                                                        IconButton(
                                                          icon: Icon(
                                                            Icons
                                                                .copy_all_outlined,
                                                            color:
                                                                colorScheme
                                                                    .onSurface,
                                                          ),
                                                          onPressed: () {
                                                            ref
                                                                .read(
                                                                  columnSchemeInputControllerProvider
                                                                      .notifier,
                                                                )
                                                                .copyAndInsertTable(
                                                                  index,
                                                                );
                                                          },
                                                        ),
                                                        IconButton(
                                                          icon: Icon(
                                                            Icons
                                                                .delete_outline,
                                                            color:
                                                                colorScheme
                                                                    .onSurface,
                                                          ),
                                                          onPressed: () {
                                                            ref
                                                                .read(
                                                                  columnSchemeInputControllerProvider
                                                                      .notifier,
                                                                )
                                                                .deleteTable(
                                                                  input[index]
                                                                      .columnSchemeInputId,
                                                                );
                                                          },
                                                        ),
                                                        ClipRect(
                                                          child: Container(
                                                            width: 75,
                                                            child: CustomStatefulDropDown(
                                                              items:
                                                                  tables
                                                                      .map(
                                                                        (e) =>
                                                                            e.usage,
                                                                      )
                                                                      .toList(),
                                                              selectedValue:
                                                                  input[index].usage ==
                                                                          ''
                                                                      ? null
                                                                      : input[index]
                                                                          .usage,
                                                              onTap: (
                                                                selectedValue,
                                                              ) async {
                                                                final input =
                                                                    await ref.read(
                                                                      columnSchemeInputControllerProvider
                                                                          .future,
                                                                    );
                                                                ref
                                                                    .read(
                                                                      columnSchemeInputControllerProvider
                                                                          .notifier,
                                                                    )
                                                                    .updateTable(
                                                                      columnSchemeInputId:
                                                                          input[index]
                                                                              .columnSchemeInputId,
                                                                      usage:
                                                                          selectedValue,
                                                                    );
                                                              },
                                                            ),
                                                          ),
                                                        ),
                                                        SizedBox(width: 5.0),
                                                        Flexible(
                                                          child: CustomStatefulDoubleInput(
                                                            readOnly:
                                                                inputGlobal
                                                                    .useSlabSelected,
                                                            title:
                                                                'Slab Depth [${units[4]}]',
                                                            key: ValueKey(
                                                              '${input[index].columnSchemeInputId}_slabThickness',
                                                            ),
                                                            value:
                                                                input[index]
                                                                    .slabThickness,
                                                            listener: (
                                                              hasFocus,
                                                              value,
                                                            ) {
                                                              if (!hasFocus) {
                                                                ref
                                                                    .read(
                                                                      columnSchemeInputControllerProvider
                                                                          .notifier,
                                                                    )
                                                                    .updateTable(
                                                                      columnSchemeInputId:
                                                                          input[index]
                                                                              .columnSchemeInputId,
                                                                      slabThickness:
                                                                          value,
                                                                    );
                                                              }
                                                            },
                                                          ),
                                                        ),
                                                        SizedBox(width: 5.0),
                                                        Flexible(
                                                          child: CustomStatefulIntInput(
                                                            title:
                                                                'Nos of Floor',
                                                            key: ValueKey(
                                                              '${input[index].columnSchemeInputId}_nosOfFloor',
                                                            ),
                                                            value:
                                                                input[index]
                                                                    .nosOfFloor,
                                                            listener: (
                                                              hasFocus,
                                                              value,
                                                            ) {
                                                              if (!hasFocus) {
                                                                ref
                                                                    .read(
                                                                      columnSchemeInputControllerProvider
                                                                          .notifier,
                                                                    )
                                                                    .updateTable(
                                                                      columnSchemeInputId:
                                                                          input[index]
                                                                              .columnSchemeInputId,
                                                                      nosOfFloor:
                                                                          value,
                                                                    );
                                                              }
                                                            },
                                                          ),
                                                        ),
                                                        SizedBox(width: 5.0),
                                                        Flexible(
                                                          child: CustomStatefulDoubleInput(
                                                            title:
                                                                'Load Width [${units[3]}]',
                                                            key: ValueKey(
                                                              '${input[index].columnSchemeInputId}_LoadWidth',
                                                            ),
                                                            value:
                                                                input[index]
                                                                    .loadWidth,
                                                            listener: (
                                                              hasFocus,
                                                              value,
                                                            ) {
                                                              if (!hasFocus) {
                                                                ref
                                                                    .read(
                                                                      columnSchemeInputControllerProvider
                                                                          .notifier,
                                                                    )
                                                                    .updateTable(
                                                                      columnSchemeInputId:
                                                                          input[index]
                                                                              .columnSchemeInputId,
                                                                      loadWidth:
                                                                          value,
                                                                    );
                                                              }
                                                            },
                                                          ),
                                                        ),
                                                        SizedBox(width: 5.0),
                                                        Flexible(
                                                          child: CustomStatefulDoubleInput(
                                                            title:
                                                                'Load Length [${units[3]}]',
                                                            key: ValueKey(
                                                              '${input[index].columnSchemeInputId}_LoadLength',
                                                            ),
                                                            value:
                                                                input[index]
                                                                    .loadLength,
                                                            listener: (
                                                              hasFocus,
                                                              value,
                                                            ) {
                                                              if (!hasFocus) {
                                                                ref
                                                                    .read(
                                                                      columnSchemeInputControllerProvider
                                                                          .notifier,
                                                                    )
                                                                    .updateTable(
                                                                      columnSchemeInputId:
                                                                          input[index]
                                                                              .columnSchemeInputId,
                                                                      loadLength:
                                                                          value,
                                                                    );
                                                              }
                                                            },
                                                          ),
                                                        ),
                                                      ],
                                                    ),
                                                  );
                                                },
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ],
                        );
                      },
                      error: (error, stackTrace) {
                        return Text('Error: $error');
                      },
                      loading: () {
                        return const CircularProgressIndicator();
                      },
                    );
                  },
                  error: (error, stackTrace) {
                    return Text('Error: $error');
                  },
                  loading: () {
                    return const CircularProgressIndicator();
                  },
                );
              },
              error: (error, stackTrace) {
                return Text('Error: $error');
              },
              loading: () {
                return const CircularProgressIndicator();
              },
            );
          },
          error: (error, stackTrace) => Text(error.toString()),
          loading: () => CircularProgressIndicator(),
        );
      },
      error: (error, stackTrace) {
        return Text('Error: $error');
      },
      loading: () {
        return const CircularProgressIndicator();
      },
    );
  }

  void _updateHeights() {
    final double screenHeight = MediaQuery.of(context).size.height;
    _maxHeight =
        (widget.maxHeight == null)
            ? screenHeight * 0.6
            : (widget.maxHeight! > screenHeight * 0.6)
            ? screenHeight * 0.6
            : widget.maxHeight!;

    _minHeight =
        (widget.minHeight == null)
            ? screenHeight * 0.1
            : (widget.minHeight! > screenHeight * 0.1)
            ? screenHeight * 0.1
            : widget.minHeight!;

    // Adjust _maxHeight if needed
    if (_maxHeight < _minHeight) {
      _maxHeight = _minHeight;
    }
  }
}
