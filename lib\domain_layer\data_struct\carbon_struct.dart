import 'dart:math';

import 'package:flutter/foundation.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
// import 'package:flutter/foundation.dart';
part 'carbon_struct.freezed.dart';
part 'carbon_struct.g.dart';

enum CarbonType {
  concreteArea, //eg. slab, wall, pile cap
  metalArea, //eg. metal decking
  concreteLine, //eg. beam, column, pile
  rebar,
  steelLine, //eg. steel beam/column
}

@freezed
abstract class Carbon with _$Carbon {
  const Carbon._();

  factory Carbon.create({
    required CarbonType carbonType,
    required String id,
    Map<String, dynamic> data = const {},
  }) {
    final json = {
      // Use the .name property to convert the enum to its string representation
      ...data,
      'carbonType': carbonType.name,
      'id': id,
      'runtimeType': carbonType.name,
    };
    return Carbon.fromJson(json);
  }

  const factory Carbon.concreteArea({
    @Default('') String name,
    @Default('concreteArea') String carbonType,
    @Default(0.0) double thk, //[mm]
    @Default(0.0) double area, //[m2]
    @Default(0.0) double totalSteelRatio,  //[top + bottom bars]
    @Default(2400.0) double density, // [kg/m3]
    @Default(0.198) double ecA13, // production [kgCO2e/kg]
    @Default(0.003) double ecA4, // transport to the site [kgCO2e/kg]
    @Default(0.053) double wF, // waste factor, for ecA53 cals
    @Default(-1.640) double sCO2,
    @Default(0.009) double ecC2, // transport away from site [kgCO2e/m2]
    @Default(0.0012) double ecC34, // recycle and disposal[kgCO2e/m2]
    @Default('') String id,
  }) = ConcreteArea;

  const factory Carbon.concreteLine({
    @Default('') String name,
    @Default('concreteLine') String carbonType,
    @Default(0.0) double length, //[m]
    @Default(0.0) double sectionSize, //[mm2]
    @Default(0.0) double  totalSteelRatio,  //[top + bottom bars]
    @Default(2400.0) double density, // [kg/m3]
    @Default(0.198) double ecA13, // production [kgCO2e/kg]
    @Default(0.003) double ecA4, // transport to the site [kgCO2e/kg]
    @Default(0.053) double wF, // waste factor, for ecA53 cals
    @Default(-1.640) double sCO2,
    @Default(0.009) double ecC2, // transport away from site [kgCO2e/m2]
    @Default(0.0012) double ecC34, // recycle and disposal[kgCO2e/m2]
    @Default('') String id,
  }) = ConcreteLine;

  const factory Carbon.rebar({
    @Default('') String name,
    @Default('rebar') String carbonType,
    @Default(0.0) double length, //[m]
    @Default(0.0) double sectionSize, //[mm2]
    @Default(7850.0) double density, // [kg/m3]
    @Default(0.720) double ecA13, // production [kgCO2e/kg]
    @Default(0.003) double ecA4, // transport to the site [kgCO2e/kg]
    @Default(0.053) double wF, // waste factor, for ecA53 cals
    @Default(-1.640)
    double sCO2, // biogenic carbon sequestered from the air, for ecA53 cals
    @Default(0.009) double ecC2, // transport away from site [kgCO2e/m2]
    @Default(0.0012) double ecC34, // recycle and disposal[kgCO2e/m2]
    @Default('') String id,
  }) = Rebar;

  const factory Carbon.steelLine({
    @Default('') String name,
    @Default('steelLine') String carbonType,
    @Default(0.0) double length, //[m]
    @Default(0.0) double sectionSize, //[mm2]
    @Default(7850.0) double density, // [kg/m3]
    @Default(1.64) double ecA13, // production [kgCO2e/kg]
    @Default(0.003) double ecA4, // transport to the site [kgCO2e/kg]
    @Default(0.01) double wF, // waste factor, for ecA53 cals
    @Default(-1.640)
    double sCO2, // biogenic carbon sequestered from the air, for ecA53 cals
    @Default(0.009) double ecC2, // transport away from site [kgCO2e/m2]
    @Default(0.0012) double ecC34, // recycle and disposal[kgCO2e/m2]
    @Default('') String id,
  }) = SteelLine;

  const factory Carbon.metalArea({
    @Default('') String name,
    @Default('metalArea') String carbonType,
    @Default(0.0) double area, //[m2]
    @Default(13.79) double density, //* [kg/m2]
    @Default(2.83) double ecA13, // production [kgCO2e/kg]
    @Default(0.003) double ecA4, // transport to the site [kgCO2e/kg]
    @Default(0.01) double wF, // waste factor, for ecA53 cals
    @Default(-1.640)
    double sCO2, // biogenic carbon sequestered from the air, for ecA53 cals
    @Default(0.009) double ecC2, // transport away from site [kgCO2e/m2]
    @Default(0.0012) double ecC34, // recycle and disposal[kgCO2e/m2]
    @Default('') String id,
  }) = MetalArea;

  factory Carbon.fromJson(Map<String, Object?> json) => _$CarbonFromJson(json);

  Carbon generalCopyWith({
    String? name,
    double? thk,
    double? area,
    double? length,
    double? sectionSize,
    double? totalSteelRatio,
    String? id,
  }) {
    switch (runtimeType) {
      case ConcreteArea:
        return (this as ConcreteArea).copyWith(
          name: name ?? this.name,
          thk: thk ?? (this as ConcreteArea).thk,
          area: area ?? (this as ConcreteArea).area,
          totalSteelRatio:
              totalSteelRatio ?? (this as ConcreteArea).totalSteelRatio,
          id: id ?? this.id,
        );
      case MetalArea:
        return (this as MetalArea).copyWith(
          name: name ?? this.name,
          area: area ?? (this as MetalArea).area,
          id: id ?? this.id,
        );
      case ConcreteLine:
        return (this as ConcreteLine).copyWith(
          name: name ?? this.name,
          length: length ?? (this as ConcreteLine).length,
          sectionSize: sectionSize ?? (this as ConcreteLine).sectionSize,
          totalSteelRatio:
              totalSteelRatio ?? (this as ConcreteLine).totalSteelRatio,
          id: id ?? this.id,
        );
      case Rebar:
        return (this as Rebar).copyWith(
          name: name ?? this.name,
          length: length ?? (this as Rebar).length,
          sectionSize: sectionSize ?? (this as Rebar).sectionSize,
          id: id ?? this.id,
        );
      case SteelLine:
        return (this as SteelLine).copyWith(
          name: name ?? this.name,
          length: length ?? (this as SteelLine).length,
          sectionSize: sectionSize ?? (this as SteelLine).sectionSize,
          id: id ?? this.id,
        );

      default:
        throw Exception('Invalid runtimeType');
    }
  }

  //* getters
  double get ecA53 => wF * (ecA13 + sCO2 + ecA4 + ecC2 + ecC34);
  double get ecTotal => ecA13 + ecA4 + ecA53;
  double get allowance {
    switch (this) {
      case ConcreteArea() || ConcreteLine() || MetalArea():
        return 0;
      case Rebar():
        return 0.15; // for lap and links; increase the total rebar weight [kg] [% converted to decimal]
      case SteelLine():
        return 0.15; // for joints; increase the total steel weight [kg] [% converted to decimal]
      default:
        throw Exception('Invalid runtimeType');
    }
  }
  double get weight => volume * density;
  double get totalCO2 => volume * ecTotal;
  double get volume => map(
    concreteArea:
        (data) =>
            (data.thk + allowance) *
            pow(10, -3) *
            data.area *
            data.density ,
    metalArea:
        (data) => (data.area + allowance * data.area) * data.density ,
    concreteLine:
        (data) =>
            (data.length + allowance) *
            data.sectionSize *
            pow(10, -6) *
            data.density ,
    rebar:
        (data) =>
            data.length *
            data.sectionSize *
            pow(10, -6) *
            data.density *
            (1 + allowance) ,
    steelLine:
        (data) =>
            data.length *
            data.sectionSize *
            pow(10, -6) *
            data.density *
            (1 + allowance) ,
  );
}
