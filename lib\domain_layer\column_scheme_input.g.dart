// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'column_scheme_input.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_ColumnSchemeInput _$ColumnSchemeInputFromJson(Map<String, dynamic> json) =>
    _ColumnSchemeInput(
      usage: json['usage'] as String? ?? '',
      slabThickness: (json['slabThickness'] as num?)?.toDouble() ?? 150.0,
      loadWidth: (json['loadWidth'] as num?)?.toDouble() ?? 8,
      loadLength: (json['loadLength'] as num?)?.toDouble() ?? 12,
      nosOfFloor: (json['nosOfFloor'] as num?)?.toInt() ?? 1,
      columnSchemeInputId: json['columnSchemeInputId'] as String? ?? '',
    );

Map<String, dynamic> _$ColumnSchemeInputToJson(_ColumnSchemeInput instance) =>
    <String, dynamic>{
      'usage': instance.usage,
      'slabThickness': instance.slabThickness,
      'loadWidth': instance.loadWidth,
      'loadLength': instance.loadLength,
      'nosOfFloor': instance.nosOfFloor,
      'columnSchemeInputId': instance.columnSchemeInputId,
    };
