// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'transfer_beam_scheme_input.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_TransferBeamSchemeInput _$TransferBeamSchemeInputFromJson(
  Map<String, dynamic> json,
) => _TransferBeamSchemeInput(
  pointLoad: (json['pointLoad'] as num?)?.toDouble() ?? 0.0,
  distA: (json['distA'] as num?)?.toDouble() ?? 3.0,
  loadFromSelectedCol: json['loadFromSelectedCol'] as bool? ?? false,
  transferBeamSchemeInputId: json['transferBeamSchemeInputId'] as String? ?? '',
);

Map<String, dynamic> _$TransferBeamSchemeInputToJson(
  _TransferBeamSchemeInput instance,
) => <String, dynamic>{
  'pointLoad': instance.pointLoad,
  'distA': instance.distA,
  'loadFromSelectedCol': instance.loadFromSelectedCol,
  'transferBeamSchemeInputId': instance.transferBeamSchemeInputId,
};
