// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'steel_transfer_truss_scheme_input.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_SteelTransferTrussSchemeInput _$SteelTransferTrussSchemeInputFromJson(
  Map<String, dynamic> json,
) => _SteelTransferTrussSchemeInput(
  pointLoad: (json['pointLoad'] as num?)?.toDouble() ?? 0.0,
  distA: (json['distA'] as num?)?.toDouble() ?? 3.0,
  steelTransferTrussSchemeInputId:
      json['steelTransferTrussSchemeInputId'] as String? ?? '',
);

Map<String, dynamic> _$SteelTransferTrussSchemeInputToJson(
  _SteelTransferTrussSchemeInput instance,
) => <String, dynamic>{
  'pointLoad': instance.pointLoad,
  'distA': instance.distA,
  'steelTransferTrussSchemeInputId': instance.steelTransferTrussSchemeInputId,
};
