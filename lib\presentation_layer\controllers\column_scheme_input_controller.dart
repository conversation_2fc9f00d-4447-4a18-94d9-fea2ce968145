import 'package:nanoid2/nanoid2.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:structify/presentation_layer/screen/column_scheme.dart';

//presentation layer
import '../../domain_layer/slab_scheme_data.dart';
import '../screen/homescreen.dart';
import '../../domain_layer/column_scheme_input.dart';

// domain layer

part 'column_scheme_input_controller.g.dart';

@riverpod
class ColumnSchemeInputController extends _$ColumnSchemeInputController {
  @override
  FutureOr<List<ColumnSchemeInput>> build() async {
    // print('Build: Column Scheme Input');
    final List<ColumnSchemeInput> columnSchemeInputs =
        await ref
            .read(appDatabaseControllerProvider.notifier)
            .queryColumnSchemeInput();
    if (columnSchemeInputs.isEmpty) {
      String newID = nanoid(alphabet: Alphabet.noDoppelgangerSafe);
      columnSchemeInputs.add(ColumnSchemeInput(columnSchemeInputId: newID));
    }
    final data1 = ref.watch(loadingTablesControllerProvider);
    final data2 = ref.watch(slabSchemeDataControllerProvider);
    return data1.when(
      data: (loadingTables) async {
        return data2.when(
          data: (slabData) async {
            //* Validate the loading (usage) used
            final usages = loadingTables.map((x) => x.usage).toList();
            final preList =
                columnSchemeInputs.map((item) {
                  if (usages.contains(item.usage)) {
                    return item;
                  } else {
                    return item.copyWith(usage: usages[0]);
                  }
                }).toList();
            //* Validate the slab thickness used
            final List<ColumnSchemeInput> revisedList = <ColumnSchemeInput>[];
            final selectedSlab = slabData.firstWhere(
              (scheme) => scheme.isSelected,
              orElse: () => SlabSchemeData(),
            );
            final inputGlobal = await ref.read(
              columnSchemeInputGlobalControllerProvider.future,
            );
            if (inputGlobal.useSlabSelected) {
              await Future.forEach(preList, (item) async {
                revisedList.add(
                  item.copyWith(slabThickness: selectedSlab.strZone),
                );
              });
              return revisedList;
            } else {
              return preList;
            }
          },
          error: (error, stackTrace) => <ColumnSchemeInput>[],
          loading: () => <ColumnSchemeInput>[],
        );
      },
      error: (error, stackTrace) => <ColumnSchemeInput>[],
      loading: () => <ColumnSchemeInput>[],
    );
  }

  Future<void> addEmptytable() async {
    final x = await future;
    final x1 = await ref.read(loadingTablesControllerProvider.future);
    final id = await _generateTaskID();
    final inputGlobals = await ref.read(
      columnSchemeInputGlobalControllerProvider.future,
    );

    if (inputGlobals.useSlabSelected) {
      final slabData = await ref.read(slabSchemeDataControllerProvider.future);
      final selectedSlab = slabData.firstWhere(
        (scheme) => scheme.isSelected,
        orElse: () => SlabSchemeData(),
      );
      state = AsyncData([
        ...x,
        ColumnSchemeInput(
          usage: x1.first.usage,
          slabThickness: selectedSlab.strZone,
          columnSchemeInputId: id,
        ),
      ]);
    } else {
      state = AsyncData([
        ...x,
        ColumnSchemeInput(usage: x1.first.usage, columnSchemeInputId: id),
      ]);
    }
  }

  Future<void> deleteTable(String id) async {
    final x = await future;
    x.removeWhere((item) => item.columnSchemeInputId == id);
    state = AsyncData(x);
  }

  // Future<void> deleteAllTable() async {
  //   state = AsyncData([]);
  // }

  // Future<void> insertEmptyTable(int index) async {
  //   final x = await future;
  //   final id = await _generateTaskID();
  //   x.insert(index+1, LoadingTable(loadingTableId: id));
  //   state = AsyncData(x);
  // }

  Future<void> copyAndInsertTable(int index) async {
    final x = await future;
    final id = await _generateTaskID();
    x.insert(index + 1, x[index].copyWith(columnSchemeInputId: id));
    state = AsyncData(x);
  }

  Future<String> _generateTaskID() async {
    String newID = nanoid(alphabet: Alphabet.noDoppelgangerSafe);
    final x = await future;
    final Set<String> columnSchemeInputIds =
        x.map((item) => item.columnSchemeInputId).toSet();
    // final Set<String> taskIDs = ref.read(_taskIDsProvider);
    while (columnSchemeInputIds.contains(newID)) {
      newID = nanoid(alphabet: Alphabet.noDoppelgangerSafe);
    }
    return newID;
  }

  Future<void> updateTable({
    String? usage,
    double? slabThickness,
    double? loadWidth,
    double? loadLength,
    int? nosOfFloor,
    required String columnSchemeInputId,
  }) async {
    final x = await future;
    List<ColumnSchemeInput> finalList = [];
    late ColumnSchemeInput data;
    // Create a list of updates
    await Future.forEach((x), (x1) {
      if (x1.columnSchemeInputId == columnSchemeInputId) {
        data = x1.copyWith(
          usage: usage ?? x1.usage,
          slabThickness: slabThickness ?? x1.slabThickness,
          loadWidth: loadWidth ?? x1.loadWidth,
          loadLength: loadLength ?? x1.loadLength,
          nosOfFloor: nosOfFloor ?? x1.nosOfFloor,
          columnSchemeInputId: columnSchemeInputId,
        );
      } else {
        data = x1;
      }
      finalList.add(data);
    });
    // Update the state only if there are changes
    state = AsyncData(finalList);
  }
}
