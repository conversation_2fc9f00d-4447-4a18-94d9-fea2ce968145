import 'package:freezed_annotation/freezed_annotation.dart';
// import 'package:flutter/foundation.dart';
part 'str_force_struct.freezed.dart';
part 'str_force_struct.g.dart';

@freezed
abstract class StrForce with _$StrForce {
  const StrForce._();
  factory StrForce({
    @Default(0.0) double x,
    @Default(0.0) double Md,
    @Default(0.0) double Vd,
  }) = _StrForce; 

  factory StrForce.fromJson(Map<String, Object?> json) =>
      _$StrForceFromJson(json);
}
