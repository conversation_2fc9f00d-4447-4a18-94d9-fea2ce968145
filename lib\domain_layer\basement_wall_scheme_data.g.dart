// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'basement_wall_scheme_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_BasementWallSchemeData _$BasementWallSchemeDataFromJson(
  Map<String, dynamic> json,
) => _BasementWallSchemeData(
  strZone: (json['strZone'] as num?)?.toDouble() ?? 0.0,
  fcu: (json['fcu'] as num?)?.toDouble() ?? 45.0,
  cover: (json['cover'] as num?)?.toDouble() ?? 75.0,
  mainTopBar: json['mainTopBar'] as String? ?? '',
  mainBottomBar: json['mainBottomBar'] as String? ?? '',
  mainLinks: json['mainLinks'] as String? ?? '',
  wallTopLevel: (json['wallTopLevel'] as num?)?.toDouble() ?? 0.0,
  wallBottomLevel: (json['wallBottomLevel'] as num?)?.toDouble() ?? -3.0,
  soilTopLevel: (json['soilTopLevel'] as num?)?.toDouble() ?? 0.0,
  waterTopLevel: (json['waterTopLevel'] as num?)?.toDouble() ?? 0.0,
  basementWallSchemeId: json['basementWallSchemeId'] as String? ?? '1',
  calsLog: json['calsLog'] as String? ?? '',
  wallForceULS: json['wallForceULS'] as String? ?? '',
  wallForceSLS: json['wallForceSLS'] as String? ?? '',
);

Map<String, dynamic> _$BasementWallSchemeDataToJson(
  _BasementWallSchemeData instance,
) => <String, dynamic>{
  'strZone': instance.strZone,
  'fcu': instance.fcu,
  'cover': instance.cover,
  'mainTopBar': instance.mainTopBar,
  'mainBottomBar': instance.mainBottomBar,
  'mainLinks': instance.mainLinks,
  'wallTopLevel': instance.wallTopLevel,
  'wallBottomLevel': instance.wallBottomLevel,
  'soilTopLevel': instance.soilTopLevel,
  'waterTopLevel': instance.waterTopLevel,
  'basementWallSchemeId': instance.basementWallSchemeId,
  'calsLog': instance.calsLog,
  'wallForceULS': instance.wallForceULS,
  'wallForceSLS': instance.wallForceSLS,
};
