// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'wind_load_global.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_WindLoad _$WindLoadFromJson(Map<String, dynamic> json) => _WindLoad(
  h: (json['h'] as num?)?.toDouble() ?? 50.0,
  bTop: (json['bTop'] as num?)?.toDouble() ?? 30.0,
  dTop: (json['dTop'] as num?)?.toDouble() ?? 40.0,
  sS: (json['sS'] as num?)?.toDouble() ?? 1.11,
  bldgType: json['bldgType'] as String? ?? 'Concrete',
  amplificationFactor: (json['amplificationFactor'] as num?)?.toDouble() ?? 1.4,
  id: json['id'] as String? ?? '1',
);

Map<String, dynamic> _$WindLoadToJson(_WindLoad instance) => <String, dynamic>{
  'h': instance.h,
  'bTop': instance.bTop,
  'dTop': instance.dTop,
  'sS': instance.sS,
  'bldgType': instance.bldgType,
  'amplificationFactor': instance.amplificationFactor,
  'id': instance.id,
};
