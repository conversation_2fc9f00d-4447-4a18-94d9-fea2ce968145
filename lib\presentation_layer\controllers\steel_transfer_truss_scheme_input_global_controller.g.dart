// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'steel_transfer_truss_scheme_input_global_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$steelTransferTrussSchemeInputGlobalControllerHash() =>
    r'f8bbf962d795282dc3bddc4606dc513d1818272c';

/// See also [SteelTransferTrussSchemeInputGlobalController].
@ProviderFor(SteelTransferTrussSchemeInputGlobalController)
final steelTransferTrussSchemeInputGlobalControllerProvider =
    AutoDisposeAsyncNotifierProvider<
      SteelTransferTrussSchemeInputGlobalController,
      SteelTransferTrussSchemeInputGlobal
    >.internal(
      SteelTransferTrussSchemeInputGlobalController.new,
      name: r'steelTransferTrussSchemeInputGlobalControllerProvider',
      debugGetCreateSourceHash:
          const bool.fromEnvironment('dart.vm.product')
              ? null
              : _$steelTransferTrussSchemeInputGlobalControllerHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$SteelTransferTrussSchemeInputGlobalController =
    AutoDisposeAsyncNotifier<SteelTransferTrussSchemeInputGlobal>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
