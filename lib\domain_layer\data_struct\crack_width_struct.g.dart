// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'crack_width_struct.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_CrackWidth _$CrackWidthFromJson(Map<String, dynamic> json) => _CrackWidth(
  M: (json['M'] as num?)?.toDouble() ?? 0.0,
  h: (json['h'] as num?)?.toDouble() ?? 0.0,
  b: (json['b'] as num?)?.toDouble() ?? 0.0,
  d: (json['d'] as num?)?.toDouble() ?? 0.0,
  fcu: (json['fcu'] as num?)?.toDouble() ?? 0.0,
  As: (json['As'] as num?)?.toDouble() ?? 0.0,
  x: (json['x'] as num?)?.toDouble() ?? 0.0,
  c_min: (json['c_min'] as num?)?.toDouble() ?? 0.0,
  dia_l: (json['dia_l'] as num?)?.toInt() ?? 0,
  Es: (json['Es'] as num?)?.toDouble() ?? 200000.0,
  a_cr: (json['a_cr'] as num?)?.toDouble() ?? 0.0,
  fcc: (json['fcc'] as num?)?.toDouble() ?? 0.0,
  fst: (json['fst'] as num?)?.toDouble() ?? 0.0,
  a_pi: (json['a_pi'] as num?)?.toDouble() ?? 0.0,
  e_1: (json['e_1'] as num?)?.toDouble() ?? 0.0,
  e_m: (json['e_m'] as num?)?.toDouble() ?? 0.0,
  crackWidth: (json['crackWidth'] as num?)?.toDouble() ?? 0.0,
);

Map<String, dynamic> _$CrackWidthToJson(_CrackWidth instance) =>
    <String, dynamic>{
      'M': instance.M,
      'h': instance.h,
      'b': instance.b,
      'd': instance.d,
      'fcu': instance.fcu,
      'As': instance.As,
      'x': instance.x,
      'c_min': instance.c_min,
      'dia_l': instance.dia_l,
      'Es': instance.Es,
      'a_cr': instance.a_cr,
      'fcc': instance.fcc,
      'fst': instance.fst,
      'a_pi': instance.a_pi,
      'e_1': instance.e_1,
      'e_m': instance.e_m,
      'crackWidth': instance.crackWidth,
    };
