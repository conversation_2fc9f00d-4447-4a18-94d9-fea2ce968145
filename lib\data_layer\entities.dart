import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:drift/drift.dart' as drift;
import 'package:structify/data_layer/app_database.dart';

part 'entities.freezed.dart';
// part 'entities.g.dart';

@freezed
abstract class LoadingTableEntity with _$LoadingTableEntity {
  const LoadingTableEntity._();

  factory LoadingTableEntity({
    String? usage,
    double? finish,
    double? service,
    double? liveLoad,
    String? loadingTableId,
  }) = _LoadingTableEntity;

  ListLoadingTableCompanion toCompanion() {
    return ListLoadingTableCompanion(
      usage: drift.Value(usage ?? 'Usage'),
      finish: drift.Value(finish ?? 0.0),
      service: drift.Value(service ?? 0.0),
      liveLoad: drift.Value(liveLoad ?? 0.0),
      loadingTableId: drift.Value(loadingTableId ?? ''),
    );
  }
}

@freezed
abstract class GlobalDataEntity with _$GlobalDataEntity {
  const GlobalDataEntity._();

  factory GlobalDataEntity({
    String? id,
    String? unit,
    double? sdlFactor,
    double? llFactor,
    double? rcUnitWeight,
    double? finishUnitWeight,
    double? steelUnitWeight,
    double? soilUnitWeight,
    double? waterUnitWeight,
    // bool? showGlobalDataUiInSubPage,
  }) = _GlobalDataEntity;

  ListGlobalDataCompanion toCompanion() {
    return ListGlobalDataCompanion(
      id: drift.Value(id ?? '1'),
      unit: drift.Value(unit ?? ''),
      sdlFactor: drift.Value(sdlFactor ?? 0.0),
      llFactor: drift.Value(llFactor ?? 0.0),
      rcUnitWeight: drift.Value(rcUnitWeight ?? 0.0),
      finishUnitWeight: drift.Value(finishUnitWeight ?? 0.0),
      steelUnitWeight: drift.Value(steelUnitWeight ?? 0.0),
      soilUnitWeight: drift.Value(soilUnitWeight ?? 0.0),
      waterUnitWeight: drift.Value(waterUnitWeight ?? 0.0),
      // showGlobalDataUiInSubPage: drift.Value(showGlobalDataUiInSubPage ?? true),
    );
  }
}

@freezed
abstract class BeamSchemeDataEntity with _$BeamSchemeDataEntity {
  const BeamSchemeDataEntity._();

  factory BeamSchemeDataEntity({
    String? usage,
    double? finish,
    double? service,
    double? liveLoad,
    String? loadingTableId,
    double? shortSpan,
    double? longSpan,
    int? bays,
    double? mainStrZone,
    double? secStrZone,
    double? fcu,
    double? cover,
    double? mainWidth,
    double? secWidth,
    String? mainTopBar,
    String? mainBottomBar,
    String? mainLinks,
    String? secTopBar,
    String? secBottomBar,
    String? secLinks,
    String? beamSchemeId,
    String? calsLog,
    bool? isSelected,
  }) = _BeamSchemeDataEntity;

  ListBeamSchemeDataCompanion toCompanion() {
    return ListBeamSchemeDataCompanion(
      usage: drift.Value(usage ?? ''),
      finish: drift.Value(finish ?? 0.0),
      service: drift.Value(service ?? 0.0),
      liveLoad: drift.Value(liveLoad ?? 0.0),
      loadingTableId: drift.Value(loadingTableId ?? ''),
      shortSpan: drift.Value(shortSpan ?? 5.0),
      longSpan: drift.Value(longSpan ?? 12.0),
      bays: drift.Value(bays ?? 2),
      mainStrZone: drift.Value(mainStrZone ?? 500.0),
      secStrZone: drift.Value(secStrZone ?? 500.0),
      fcu: drift.Value(fcu ?? 45.0),
      cover: drift.Value(cover ?? 40.0),
      mainWidth: drift.Value(mainWidth ?? 200),
      secWidth: drift.Value(secWidth ?? 200),
      mainTopBar: drift.Value(mainTopBar ?? ''),
      mainBottomBar: drift.Value(mainBottomBar ?? ''),
      mainLinks: drift.Value(mainLinks ?? ''),
      secTopBar: drift.Value(secTopBar ?? ''),
      secBottomBar: drift.Value(secBottomBar ?? ''),
      secLinks: drift.Value(secLinks ?? ''),
      beamSchemeId: drift.Value(beamSchemeId ?? ''),
      calsLog: drift.Value(calsLog ?? ''),
      isSelected: drift.Value(isSelected ?? false),
    );
  }
}

@freezed
abstract class BeamSchemeInputEntity with _$BeamSchemeInputEntity {
  const BeamSchemeInputEntity._();

  factory BeamSchemeInputEntity({
    String? id,
    double? shortSpan,
    double? longSpan,
    int? bays,
    double? mainStrZone,
    double? secStrZone,
    double? fcu,
    double? cover,
    double? mainKValue,
    double? mainSteelRatio,
    double? secKValue,
    double? secSteelRatio,
    int? minS,
    int? maxS,
    double? maxWidth,
    int? maxLayers,
    double? shortSpanIncreament,
    double? longSpanIncreament,
    int? baysIncreament,
    int? iterationSteps,
    String? usage,
    double? slabThickness,
    bool? useSlabSelected,
  }) = _BeamSchemeInputEntity;

  ListBeamSchemeInputCompanion toCompanion() {
    return ListBeamSchemeInputCompanion(
      id: drift.Value(id ?? '1'),
      shortSpan: drift.Value(shortSpan ?? 5.0),
      longSpan: drift.Value(longSpan ?? 12.0),
      bays: drift.Value(bays ?? 2),
      mainStrZone: drift.Value(mainStrZone ?? 500.0),
      secStrZone: drift.Value(secStrZone ?? 500.0),
      fcu: drift.Value(fcu ?? 45.0),
      cover: drift.Value(cover ?? 40.0),
      mainKValue: drift.Value(mainKValue ?? 0.156),
      mainSteelRatio: drift.Value(mainSteelRatio ?? 0.025),
      minS: drift.Value(minS ?? 100),
      maxS: drift.Value(maxS ?? 300),
      maxWidth: drift.Value(maxWidth ?? 2000.0),
      maxLayers: drift.Value(maxLayers ?? 4),
      secKValue: drift.Value(secKValue ?? 0.156),
      secSteelRatio: drift.Value(secSteelRatio ?? 0.040),
      shortSpanIncreament: drift.Value(shortSpanIncreament ?? 0.0),
      longSpanIncreament: drift.Value(longSpanIncreament ?? 0.0),
      baysIncreament: drift.Value(baysIncreament ?? 1),
      iterationSteps: drift.Value(iterationSteps ?? 4),
      usage: drift.Value(usage ?? 'Usage'),
      slabThickness: drift.Value(slabThickness ?? 0.0),
      useSlabSelected: drift.Value(useSlabSelected ?? false),
    );
  }
}

@freezed
abstract class SlabSchemeInputEntity with _$SlabSchemeInputEntity {
  const SlabSchemeInputEntity._();

  factory SlabSchemeInputEntity({
    String? id,
    double? span,
    double? fcu,
    double? cover,
    double? mainKValue,
    double? mainSteelRatio,
    int? minS,
    int? maxS,
    double? maxDepth,
    double? minDepth,
    int? maxLayers,
    double? spanIncreament,
    int? iterationSteps,
    String? usage,
  }) = _SlabSchemeInputEntity;

  ListSlabSchemeInputCompanion toCompanion() {
    return ListSlabSchemeInputCompanion(
      id: drift.Value(id ?? '1'),
      span: drift.Value(span ?? 5.0),
      fcu: drift.Value(fcu ?? 45.0),
      cover: drift.Value(cover ?? 35.0),
      mainKValue: drift.Value(mainKValue ?? 0.156),
      mainSteelRatio: drift.Value(mainSteelRatio ?? 0.04),
      minS: drift.Value(minS ?? 100),
      maxS: drift.Value(maxS ?? 300),
      maxDepth: drift.Value(maxDepth ?? 500.0),
      minDepth: drift.Value(minDepth ?? 100.0),
      maxLayers: drift.Value(maxLayers ?? 2),
      spanIncreament: drift.Value(spanIncreament ?? 0.5),
      iterationSteps: drift.Value(iterationSteps ?? 4),
      usage: drift.Value(usage ?? 'Usage'),
    );
  }
}

@freezed
abstract class SlabSchemeDataEntity with _$SlabSchemeDataEntity {
  const SlabSchemeDataEntity._();

  factory SlabSchemeDataEntity({
    String? usage,
    double? finish,
    double? service,
    double? liveLoad,
    String? loadingTableId,
    double? span,
    double? strZone,
    double? fcu,
    double? cover,
    String? mainTopBar,
    String? mainBottomBar,
    String? mainLinks,
    String? slabSchemeId,
    String? calsLog,
    bool? isSelected,
  }) = _SlabSchemeDataEntity;

  ListSlabSchemeDataCompanion toCompanion() {
    return ListSlabSchemeDataCompanion(
      usage: drift.Value(usage ?? ''),
      finish: drift.Value(finish ?? 0.0),
      service: drift.Value(service ?? 0.0),
      liveLoad: drift.Value(liveLoad ?? 0.0),
      loadingTableId: drift.Value(loadingTableId ?? ''),
      span: drift.Value(span ?? 5.0),
      strZone: drift.Value(strZone ?? 500.0),
      fcu: drift.Value(fcu ?? 45.0),
      cover: drift.Value(cover ?? 40.0),
      mainTopBar: drift.Value(mainTopBar ?? ''),
      mainBottomBar: drift.Value(mainBottomBar ?? ''),
      mainLinks: drift.Value(mainLinks ?? ''),
      slabSchemeId: drift.Value(slabSchemeId ?? ''),
      calsLog: drift.Value(calsLog ?? ''),
      isSelected: drift.Value(isSelected ?? false),
    );
  }
}

@freezed
abstract class ColumnSchemeInputEntity with _$ColumnSchemeInputEntity {
  const ColumnSchemeInputEntity._();

  factory ColumnSchemeInputEntity({
    String? usage,
    double? slabThickness,
    double? loadWidth,
    double? loadLength,
    int? nosOfFloor,
    String? columnSchemeInputId,
  }) = _ColumnSchemeInputEntity;

  ListColumnSchemeInputCompanion toCompanion() {
    return ListColumnSchemeInputCompanion(
      usage: drift.Value(usage ?? 'Usage'),
      slabThickness: drift.Value(slabThickness ?? 0.0),
      loadWidth: drift.Value(loadWidth ?? 0.0),
      loadLength: drift.Value(loadLength ?? 0.0),
      nosOfFloor: drift.Value(nosOfFloor ?? 0),
      columnSchemeInputId: drift.Value(columnSchemeInputId ?? ''),
    );
  }
}

@freezed
abstract class ColumnSchemeInputGlobalEntity
    with _$ColumnSchemeInputGlobalEntity {
  const ColumnSchemeInputGlobalEntity._();

  factory ColumnSchemeInputGlobalEntity({
    int? columnSchemeInputGlobalId,
    double? cover,
    double? minColumnSize,
    double? sizeIncrement,
    int? iterationSteps,
    String? concreteGrade,
    double? minSteelRatio,
    double? maxSteelRatio,
    double? safetyFactor,
    double? minClearS,
    bool? useSlabSelected,
  }) = _ColumnSchemeInputGlobalEntity;

  ListColumnSchemeInputGlobalCompanion toCompanion() {
    return ListColumnSchemeInputGlobalCompanion(
      columnSchemeInputGlobalId: drift.Value(columnSchemeInputGlobalId ?? 1),
      cover: drift.Value(cover ?? 40.0),
      minColumnSize: drift.Value(minColumnSize ?? 0.0),
      sizeIncrement: drift.Value(sizeIncrement ?? 0.0),
      iterationSteps: drift.Value(iterationSteps ?? 0),
      concreteGrade: drift.Value(concreteGrade ?? 'C45'),
      minSteelRatio: drift.Value(minSteelRatio ?? 0.0),
      maxSteelRatio: drift.Value(maxSteelRatio ?? 0.0),
      safetyFactor: drift.Value(safetyFactor ?? 1.0),
      minClearS: drift.Value(minClearS ?? 0.0),
      useSlabSelected: drift.Value(useSlabSelected ?? false),
    );
  }
}

@freezed
abstract class ColumnSchemeDataEntity with _$ColumnSchemeDataEntity {
  const ColumnSchemeDataEntity._();

  factory ColumnSchemeDataEntity({
    String? columnSchemeDataId,
    double? sdl,
    double? ll,
    double? slsLoad,
    double? ulsLoad,
    double? size,
    double? fcu,
    double? cover,
    double? axialCapacitySquare,
    String? mainBarSquare,
    double? steelRatioSqaure,
    double? axialCapacityCircle,
    String? mainBarCircle,
    double? steelRatioCircle,
    String? calsLog,
    bool? isSelected,
  }) = _ColumnSchemeDataEntity;

  ListColumnSchemeDataCompanion toCompanion() {
    return ListColumnSchemeDataCompanion(
      columnSchemeDataId: drift.Value(columnSchemeDataId ?? ''),
      sdl: drift.Value(sdl ?? 0.0),
      ll: drift.Value(ll ?? 0.0),
      slsLoad: drift.Value(slsLoad ?? 0.0),
      ulsLoad: drift.Value(ulsLoad ?? 0.0),
      size: drift.Value(size ?? 0.0),
      fcu: drift.Value(fcu ?? 0.0),
      cover: drift.Value(cover ?? 0.0),
      axialCapacitySquare: drift.Value(axialCapacitySquare ?? 0.0),
      mainBarSquare: drift.Value(mainBarSquare ?? ''),
      steelRatioSqaure: drift.Value(steelRatioSqaure ?? 0.0),
      axialCapacityCircle: drift.Value(axialCapacityCircle ?? 0.0),
      mainBarCircle: drift.Value(mainBarCircle ?? ''),
      steelRatioCircle: drift.Value(steelRatioCircle ?? 0.0),
      calsLog: drift.Value(calsLog ?? ''),
      isSelected: drift.Value(isSelected ?? false),
    );
  }
}

@freezed
abstract class TransferBeamSchemeInputGlobalEntity
    with _$TransferBeamSchemeInputGlobalEntity {
  const TransferBeamSchemeInputGlobalEntity._();

  factory TransferBeamSchemeInputGlobalEntity({
    String? id,
    double? span,
    double? loadWidth,
    double? strZone,
    double? fcu,
    double? cover,
    double? mainKValue,
    double? mainSteelRatio,
    int? minS,
    int? maxS,
    double? maxWidth,
    int? maxLayers,
    String? usage,
    double? slabThickness,
    bool? useSlabSelected,
  }) = _TransferBeamSchemeInputGlobalEntity;

  ListTransferBeamSchemeInputGlobalCompanion toCompanion() {
    return ListTransferBeamSchemeInputGlobalCompanion(
      id: drift.Value(id ?? ''),
      span: drift.Value(span ?? 20.0),
      loadWidth: drift.Value(loadWidth ?? 10.0),
      strZone: drift.Value(strZone ?? 1000.0),
      fcu: drift.Value(fcu ?? 45.0),
      cover: drift.Value(cover ?? 40.0),
      mainKValue: drift.Value(mainKValue ?? 0.156),
      mainSteelRatio: drift.Value(mainSteelRatio ?? 0.025),
      minS: drift.Value(minS ?? 100),
      maxS: drift.Value(maxS ?? 300),
      maxWidth: drift.Value(maxWidth ?? 2000.0),
      maxLayers: drift.Value(maxLayers ?? 4),
      usage: drift.Value(usage ?? ''),
      slabThickness: drift.Value(slabThickness ?? 150.0),
      useSlabSelected: drift.Value(useSlabSelected ?? false),
    );
  }
}

@freezed
abstract class TransferBeamSchemeInputEntity
    with _$TransferBeamSchemeInputEntity {
  const TransferBeamSchemeInputEntity._();

  factory TransferBeamSchemeInputEntity({
    double? pointLoad,
    double? distA,
    bool? loadFromSelectedCol,
    String? transferBeamSchemeInputId,
  }) = _TransferBeamSchemeInputEntity;

  ListTransferBeamSchemeInputCompanion toCompanion() {
    return ListTransferBeamSchemeInputCompanion(
      pointLoad: drift.Value(pointLoad ?? 500.0),
      distA: drift.Value(distA ?? 3.0),
      loadFromSelectedCol: drift.Value(loadFromSelectedCol ?? false),
      transferBeamSchemeInputId: drift.Value(transferBeamSchemeInputId ?? ''),
    );
  }
}

@freezed
abstract class TransferBeamSchemeDataEntity
    with _$TransferBeamSchemeDataEntity {
  const TransferBeamSchemeDataEntity._();

  factory TransferBeamSchemeDataEntity({
    String? usage,
    double? slabThickness,
    double? span,
    double? loadWidth,
    double? strZone,
    double? fcu,
    double? cover,
    double? mainWidth,
    String? mainTopBar,
    String? mainBottomBar,
    String? mainLinks,
    String? id,
    String? calsLog,
    String? beamForce,
  }) = _TransferBeamSchemeDataEntity;

  ListTransferBeamSchemeDataCompanion toCompanion() {
    return ListTransferBeamSchemeDataCompanion(
      usage: drift.Value(usage ?? ''),
      slabThickness: drift.Value(slabThickness ?? 150.0),
      span: drift.Value(span ?? 1.0),
      loadWidth: drift.Value(loadWidth ?? 1.0),
      strZone: drift.Value(strZone ?? 500.0),
      fcu: drift.Value(fcu ?? 45.0),
      cover: drift.Value(cover ?? 40.0),
      mainWidth: drift.Value(mainWidth ?? 200),
      mainTopBar: drift.Value(mainTopBar ?? ''),
      mainBottomBar: drift.Value(mainBottomBar ?? ''),
      mainLinks: drift.Value(mainLinks ?? ''),
      id: drift.Value(id ?? ''),
      calsLog: drift.Value(calsLog ?? ''),
      beamForce: drift.Value(beamForce ?? ''),
    );
  }
}

@freezed
abstract class CantileverSchemeInputGlobalEntity
    with _$CantileverSchemeInputGlobalEntity {
  const CantileverSchemeInputGlobalEntity._();

  factory CantileverSchemeInputGlobalEntity({
    String? id,
    double? span,
    double? loadWidth,
    double? strZone,
    double? fcu,
    double? cover,
    double? mainKValue,
    double? mainSteelRatio,
    int? minS,
    int? maxS,
    double? maxWidth,
    int? maxLayers,
    String? usage,
    double? slabThickness,
    bool? useSlabSelected,
  }) = _CantileverSchemeInputGlobalEntity;

  ListCantileverSchemeInputGlobalCompanion toCompanion() {
    return ListCantileverSchemeInputGlobalCompanion(
      id: drift.Value(id ?? ''),
      span: drift.Value(span ?? 20.0),
      loadWidth: drift.Value(loadWidth ?? 10.0),
      strZone: drift.Value(strZone ?? 1000.0),
      fcu: drift.Value(fcu ?? 45.0),
      cover: drift.Value(cover ?? 40.0),
      mainKValue: drift.Value(mainKValue ?? 0.156),
      mainSteelRatio: drift.Value(mainSteelRatio ?? 0.025),
      minS: drift.Value(minS ?? 100),
      maxS: drift.Value(maxS ?? 300),
      maxWidth: drift.Value(maxWidth ?? 2000.0),
      maxLayers: drift.Value(maxLayers ?? 4),
      usage: drift.Value(usage ?? ''),
      slabThickness: drift.Value(slabThickness ?? 150.0),
      useSlabSelected: drift.Value(useSlabSelected ?? false),
    );
  }
}

@freezed
abstract class CantileverSchemeInputEntity with _$CantileverSchemeInputEntity {
  const CantileverSchemeInputEntity._();

  factory CantileverSchemeInputEntity({
    double? pointLoad,
    double? distA,
    bool? loadFromSelectedCol,
    String? cantileverSchemeInputId,
  }) = _CantileverSchemeInputEntity;

  ListCantileverSchemeInputCompanion toCompanion() {
    return ListCantileverSchemeInputCompanion(
      pointLoad: drift.Value(pointLoad ?? 500.0),
      distA: drift.Value(distA ?? 3.0),
      loadFromSelectedCol: drift.Value(loadFromSelectedCol ?? false),
      cantileverSchemeInputId: drift.Value(cantileverSchemeInputId ?? ''),
    );
  }
}

@freezed
abstract class CantileverSchemeDataEntity with _$CantileverSchemeDataEntity {
  const CantileverSchemeDataEntity._();

  factory CantileverSchemeDataEntity({
    String? usage,
    double? slabThickness,
    double? span,
    double? loadWidth,
    double? strZone,
    double? fcu,
    double? cover,
    double? mainWidth,
    String? mainTopBar,
    String? mainBottomBar,
    String? mainLinks,
    String? id,
    String? calsLog,
    String? beamForce,
  }) = _CantileverSchemeDataEntity;

  ListCantileverSchemeDataCompanion toCompanion() {
    return ListCantileverSchemeDataCompanion(
      usage: drift.Value(usage ?? ''),
      slabThickness: drift.Value(slabThickness ?? 150.0),
      span: drift.Value(span ?? 1.0),
      loadWidth: drift.Value(loadWidth ?? 1.0),
      strZone: drift.Value(strZone ?? 500.0),
      fcu: drift.Value(fcu ?? 45.0),
      cover: drift.Value(cover ?? 40.0),
      mainWidth: drift.Value(mainWidth ?? 200),
      mainTopBar: drift.Value(mainTopBar ?? ''),
      mainBottomBar: drift.Value(mainBottomBar ?? ''),
      mainLinks: drift.Value(mainLinks ?? ''),
      id: drift.Value(id ?? ''),
      calsLog: drift.Value(calsLog ?? ''),
      beamForce: drift.Value(beamForce ?? ''),
    );
  }
}

@freezed
abstract class SteelBeamSchemeInputEntity with _$SteelBeamSchemeInputEntity {
  const SteelBeamSchemeInputEntity._();

  factory SteelBeamSchemeInputEntity({
    String? id,
    double? shortSpan,
    double? longSpan,
    int? bays,
    double? strZone,
    double? fsy,
    double? shortSpanIncreament,
    double? longSpanIncreament,
    int? baysIncreament,
    int? iterationSteps,
    String? usage,
    double? slabThickness,
    double? compositeActionFactor,
  }) = _SteelBeamSchemeInputEntity;

  ListSteelBeamSchemeInputCompanion toCompanion() {
    return ListSteelBeamSchemeInputCompanion(
      id: drift.Value(id ?? '1'),
      shortSpan: drift.Value(shortSpan ?? 6.0),
      longSpan: drift.Value(longSpan ?? 10.0),
      bays: drift.Value(bays ?? 2),
      strZone: drift.Value(strZone ?? 500.0),
      fsy: drift.Value(fsy ?? 355.0),
      shortSpanIncreament: drift.Value(shortSpanIncreament ?? 1.0),
      longSpanIncreament: drift.Value(longSpanIncreament ?? 1.0),
      baysIncreament: drift.Value(baysIncreament ?? 1),
      iterationSteps: drift.Value(iterationSteps ?? 10),
      usage: drift.Value(usage ?? 'Usage'),
      slabThickness: drift.Value(slabThickness ?? 130.0),
      compositeActionFactor: drift.Value(compositeActionFactor ?? 1.0),
    );
  }
}

@freezed
abstract class SteelBeamSchemeDataEntity with _$SteelBeamSchemeDataEntity {
  const SteelBeamSchemeDataEntity._();

  factory SteelBeamSchemeDataEntity({
    String? usage,
    double? finish,
    double? service,
    double? liveLoad,
    String? loadingTableId,
    double? slabThickness,
    double? compositeActionFactor,
    double? shortSpan,
    double? longSpan,
    int? bays,
    double? strZone,
    double? fsy,
    String? mainBeamSection,
    String? secBeamSection,
    String? steelBeamSchemeId,
    String? calsLog,
  }) = _SteelBeamSchemeDataEntity;

  ListSteelBeamSchemeDataCompanion toCompanion() {
    return ListSteelBeamSchemeDataCompanion(
      usage: drift.Value(usage ?? ''),
      finish: drift.Value(finish ?? 0.0),
      service: drift.Value(service ?? 0.0),
      liveLoad: drift.Value(liveLoad ?? 0.0),
      loadingTableId: drift.Value(loadingTableId ?? ''),
      slabThickness: drift.Value(slabThickness ?? 0.0),
      compositeActionFactor: drift.Value(compositeActionFactor ?? 0.0),
      shortSpan: drift.Value(shortSpan ?? 6.0),
      longSpan: drift.Value(longSpan ?? 10.0),
      bays: drift.Value(bays ?? 2),
      strZone: drift.Value(strZone ?? 500.0),
      fsy: drift.Value(fsy ?? 355.0),
      mainBeamSection: drift.Value(mainBeamSection ?? ''),
      secBeamSection: drift.Value(secBeamSection ?? ''),
      steelBeamSchemeId: drift.Value(steelBeamSchemeId ?? ''),
      calsLog: drift.Value(calsLog ?? ''),
    );
  }
}

@freezed
abstract class SteelColumnSchemeInputEntity
    with _$SteelColumnSchemeInputEntity {
  const SteelColumnSchemeInputEntity._();

  factory SteelColumnSchemeInputEntity({
    String? usage,
    double? slabThickness,
    double? loadWidth,
    double? loadLength,
    int? nosOfFloor,
    String? steelColumnSchemeInputId,
  }) = _SteelColumnSchemeInputEntity;

  ListSteelColumnSchemeInputCompanion toCompanion() {
    return ListSteelColumnSchemeInputCompanion(
      usage: drift.Value(usage ?? 'Usage'),
      slabThickness: drift.Value(slabThickness ?? 0.0),
      loadWidth: drift.Value(loadWidth ?? 0.0),
      loadLength: drift.Value(loadLength ?? 0.0),
      nosOfFloor: drift.Value(nosOfFloor ?? 0),
      steelColumnSchemeInputId: drift.Value(steelColumnSchemeInputId ?? ''),
    );
  }
}

@freezed
abstract class SteelColumnSchemeInputGlobalEntity
    with _$SteelColumnSchemeInputGlobalEntity {
  const SteelColumnSchemeInputGlobalEntity._();

  factory SteelColumnSchemeInputGlobalEntity({
    String? id,
    String? steelGrade,
    double? unbracedLength,
  }) = _SteelColumnSchemeInputGlobalEntity;

  ListSteelColumnSchemeInputGlobalCompanion toCompanion() {
    return ListSteelColumnSchemeInputGlobalCompanion(
      id: drift.Value(id ?? '1'),
      steelGrade: drift.Value(steelGrade ?? 'S355'),
      unbracedLength: drift.Value(unbracedLength ?? 5.0),
    );
  }
}

@freezed
abstract class SteelColumnSchemeDataEntity with _$SteelColumnSchemeDataEntity {
  const SteelColumnSchemeDataEntity._();

  factory SteelColumnSchemeDataEntity({
    String? steelColumnSchemeDataId,
    double? sdl,
    double? ll,
    double? slsLoad,
    double? ulsLoad,
    double? fsy,
    double? unbracedLength,
    double? axialCapacity,
    String? steelSection,
    String? calsLog,
  }) = _SteelColumnSchemeDataEntity;

  ListSteelColumnSchemeDataCompanion toCompanion() {
    return ListSteelColumnSchemeDataCompanion(
      steelColumnSchemeDataId: drift.Value(steelColumnSchemeDataId ?? ''),
      sdl: drift.Value(sdl ?? 0.0),
      ll: drift.Value(ll ?? 0.0),
      slsLoad: drift.Value(slsLoad ?? 0.0),
      ulsLoad: drift.Value(ulsLoad ?? 0.0),
      fsy: drift.Value(fsy ?? 355.0),
      unbracedLength: drift.Value(unbracedLength ?? 5.0),
      axialCapacity: drift.Value(axialCapacity ?? 0.0),
      steelSection: drift.Value(steelSection ?? ''),
      calsLog: drift.Value(calsLog ?? ''),
    );
  }
}

@freezed
abstract class SteelTransferTrussSchemeInputEntity
    with _$SteelTransferTrussSchemeInputEntity {
  const SteelTransferTrussSchemeInputEntity._();

  factory SteelTransferTrussSchemeInputEntity({
    double? pointLoad,
    double? distA,
    String? steelTransferTrussSchemeInputId,
  }) = _SteelTransferTrussSchemeInputEntity;

  ListSteelTransferTrussSchemeInputCompanion toCompanion() {
    return ListSteelTransferTrussSchemeInputCompanion(
      pointLoad: drift.Value(pointLoad ?? 500.0),
      distA: drift.Value(distA ?? 3.0),
      steelTransferTrussSchemeInputId: drift.Value(
        steelTransferTrussSchemeInputId ?? '',
      ),
    );
  }
}

@freezed
abstract class SteelTransferTrussSchemeInputGlobalEntity
    with _$SteelTransferTrussSchemeInputGlobalEntity {
  const SteelTransferTrussSchemeInputGlobalEntity._();

  factory SteelTransferTrussSchemeInputGlobalEntity({
    String? id,
    double? span,
    double? loadWidth,
    double? strZone,
    double? fsy,
    double? unbracedLength,
    String? usage,
    double? slabThickness,
  }) = _SteelTransferTrussSchemeInputGlobalEntity;

  ListSteelTransferTrussSchemeInputGlobalCompanion toCompanion() {
    return ListSteelTransferTrussSchemeInputGlobalCompanion(
      id: drift.Value(id ?? '1'),
      span: drift.Value(span ?? 20.0),
      loadWidth: drift.Value(loadWidth ?? 10.0),
      strZone: drift.Value(strZone ?? 1000.0),
      fsy: drift.Value(fsy ?? 355),
      unbracedLength: drift.Value(unbracedLength ?? 5.0),
      usage: drift.Value(usage ?? ''),
      slabThickness: drift.Value(slabThickness ?? 150.0),
    );
  }
}

@freezed
abstract class SteelTransferTrussSchemeDataEntity
    with _$SteelTransferTrussSchemeDataEntity {
  const SteelTransferTrussSchemeDataEntity._();

  factory SteelTransferTrussSchemeDataEntity({
    String? id,
    String? usage,
    double? slabThickness,
    double? span,
    double? loadWidth,
    double? strZone,
    double? fsy,
    double? unbracedLength,
    String? steelSection,
    double? chordAxialCapacity,
    double? leverArm,
    double? momentCapacity,
    String? liveLoadDeflection,
    String? calsLog,
    String? beamForce,
  }) = _SteelTransferTrussSchemeDataEntity;

  ListSteelTransferTrussSchemeDataCompanion toCompanion() {
    return ListSteelTransferTrussSchemeDataCompanion(
      id: drift.Value(id ?? '1'),
      usage: drift.Value(usage ?? ''),
      slabThickness: drift.Value(slabThickness ?? 0.0),
      span: drift.Value(span ?? 0.0),
      loadWidth: drift.Value(loadWidth ?? 0.0),
      strZone: drift.Value(strZone ?? 0.0),
      fsy: drift.Value(fsy ?? 0.0),
      unbracedLength: drift.Value(unbracedLength ?? 0.0),
      steelSection: drift.Value(steelSection ?? ''),
      chordAxialCapacity: drift.Value(chordAxialCapacity ?? 0.0),
      leverArm: drift.Value(leverArm ?? 0.0),
      momentCapacity: drift.Value(momentCapacity ?? 0.0),
      liveLoadDeflection: drift.Value(liveLoadDeflection ?? ''),
      calsLog: drift.Value(calsLog ?? ''),
      beamForce: drift.Value(beamForce ?? ''),
    );
  }
}

@freezed
abstract class SteelCantileverTrussSchemeInputEntity
    with _$SteelCantileverTrussSchemeInputEntity {
  const SteelCantileverTrussSchemeInputEntity._();

  factory SteelCantileverTrussSchemeInputEntity({
    double? pointLoad,
    double? distA,
    String? steelCantileverTrussSchemeInputId,
  }) = _SteelCantileverTrussSchemeInputEntity;

  ListSteelCantileverTrussSchemeInputCompanion toCompanion() {
    return ListSteelCantileverTrussSchemeInputCompanion(
      pointLoad: drift.Value(pointLoad ?? 500.0),
      distA: drift.Value(distA ?? 3.0),
      steelCantileverTrussSchemeInputId: drift.Value(
        steelCantileverTrussSchemeInputId ?? '',
      ),
    );
  }
}

@freezed
abstract class SteelCantileverTrussSchemeInputGlobalEntity
    with _$SteelCantileverTrussSchemeInputGlobalEntity {
  const SteelCantileverTrussSchemeInputGlobalEntity._();

  factory SteelCantileverTrussSchemeInputGlobalEntity({
    String? id,
    double? span,
    double? loadWidth,
    double? strZone,
    double? fsy,
    double? unbracedLength,
    String? usage,
    double? slabThickness,
  }) = _SteelCantileverTrussSchemeInputGlobalEntity;

  ListSteelCantileverTrussSchemeInputGlobalCompanion toCompanion() {
    return ListSteelCantileverTrussSchemeInputGlobalCompanion(
      id: drift.Value(id ?? '1'),
      span: drift.Value(span ?? 20.0),
      loadWidth: drift.Value(loadWidth ?? 10.0),
      strZone: drift.Value(strZone ?? 1000.0),
      fsy: drift.Value(fsy ?? 355),
      unbracedLength: drift.Value(unbracedLength ?? 5.0),
      usage: drift.Value(usage ?? ''),
      slabThickness: drift.Value(slabThickness ?? 150.0),
    );
  }
}

@freezed
abstract class SteelCantileverTrussSchemeDataEntity
    with _$SteelCantileverTrussSchemeDataEntity {
  const SteelCantileverTrussSchemeDataEntity._();

  factory SteelCantileverTrussSchemeDataEntity({
    String? id,
    String? usage,
    double? slabThickness,
    double? span,
    double? loadWidth,
    double? strZone,
    double? fsy,
    double? unbracedLength,
    String? steelSection,
    double? chordAxialCapacity,
    double? leverArm,
    double? momentCapacity,
    String? liveLoadDeflection,
    String? calsLog,
    String? beamForce,
  }) = _SteelCantileverTrussSchemeDataEntity;

  ListSteelCantileverTrussSchemeDataCompanion toCompanion() {
    return ListSteelCantileverTrussSchemeDataCompanion(
      id: drift.Value(id ?? '1'),
      usage: drift.Value(usage ?? ''),
      slabThickness: drift.Value(slabThickness ?? 0.0),
      span: drift.Value(span ?? 0.0),
      loadWidth: drift.Value(loadWidth ?? 0.0),
      strZone: drift.Value(strZone ?? 0.0),
      fsy: drift.Value(fsy ?? 0.0),
      unbracedLength: drift.Value(unbracedLength ?? 0.0),
      steelSection: drift.Value(steelSection ?? ''),
      chordAxialCapacity: drift.Value(chordAxialCapacity ?? 0.0),
      leverArm: drift.Value(leverArm ?? 0.0),
      momentCapacity: drift.Value(momentCapacity ?? 0.0),
      liveLoadDeflection: drift.Value(liveLoadDeflection ?? ''),
      calsLog: drift.Value(calsLog ?? ''),
      beamForce: drift.Value(beamForce ?? ''),
    );
  }
}

@freezed
abstract class PileEndBearingBoredInputEntity
    with _$PileEndBearingBoredInputEntity {
  const PileEndBearingBoredInputEntity._();

  factory PileEndBearingBoredInputEntity({
    double? safeBearing,
    double? fos,
    double? fcu,
    double? maxPileDiameter,
    double? ratioOfBelloutDia,
    double? maxSteelRatio,
    double? slsLoad,
    double? ulsLoad,
    double? diaIncrement,
    bool? useSelectColLoad,
    String? id,
  }) = _PileEndBearingBoredInputEntity;

  ListPileEndBearingBoredInputCompanion toCompanion() {
    return ListPileEndBearingBoredInputCompanion(
      safeBearing: drift.Value(safeBearing ?? 1000.0),
      fos: drift.Value(fos ?? 3.0),
      fcu: drift.Value(fcu ?? 45.0),
      maxPileDiameter: drift.Value(maxPileDiameter ?? 2000.0),
      ratioOfBelloutDia: drift.Value(ratioOfBelloutDia ?? 1.65),
      maxSteelRatio: drift.Value(maxSteelRatio ?? 0.04),
      slsLoad: drift.Value(slsLoad ?? 1000.0),
      ulsLoad: drift.Value(ulsLoad ?? 2000.0),
      diaIncrement: drift.Value(diaIncrement ?? 200.0),
      useSelectColLoad: drift.Value(useSelectColLoad ?? false),
      id: drift.Value(id ?? '1'),
    );
  }
}

@freezed
abstract class PileEndBearingBoredInputGlobalEntity
    with _$PileEndBearingBoredInputGlobalEntity {
  const PileEndBearingBoredInputGlobalEntity._();

  factory PileEndBearingBoredInputGlobalEntity({
    double? colLoadFactor,
    String? id,
  }) = _PileEndBearingBoredInputGlobalEntity;

  ListPileEndBearingBoredInputGlobalCompanion toCompanion() {
    return ListPileEndBearingBoredInputGlobalCompanion(
      colLoadFactor: drift.Value(colLoadFactor ?? 1.0),
      id: drift.Value(id ?? '1'),
    );
  }
}

@freezed
abstract class PileEndBearingBoredDataEntity
    with _$PileEndBearingBoredDataEntity {
  const PileEndBearingBoredDataEntity._();

  factory PileEndBearingBoredDataEntity({
    double? safeBearing,
    double? fos,
    double? fcu,
    double? maxPileDiameter,
    double? maxSteelRatio,
    double? slsLoad,
    double? ulsLoad,
    double? diameter,
    double? ratioOfBelloutDia,
    double? length,
    double? baseCapacity,
    double? totalGroundResistance,
    double? strCapacity,
    String? rebar,
    double? steelRatio,
    bool? isSelected,
    String? calsLog,
    String? pileEndBearingBoredSchemeId,
  }) = _PileEndBearingBoredDataEntity;

  ListPileEndBearingBoredDataCompanion toCompanion() {
    return ListPileEndBearingBoredDataCompanion(
      safeBearing: drift.Value(safeBearing ?? 1000.0),
      fos: drift.Value(fos ?? 3.0),
      fcu: drift.Value(fcu ?? 45.0),
      maxPileDiameter: drift.Value(maxPileDiameter ?? 2000.0),
      maxSteelRatio: drift.Value(maxSteelRatio ?? 0.04),
      slsLoad: drift.Value(slsLoad ?? 1000.0),
      ulsLoad: drift.Value(ulsLoad ?? 2000.0),
      diameter: drift.Value(diameter ?? 0.0),
      ratioOfBelloutDia: drift.Value(ratioOfBelloutDia ?? 1.65),
      length: drift.Value(length ?? 0.0),
      baseCapacity: drift.Value(baseCapacity ?? 0.0),
      totalGroundResistance: drift.Value(totalGroundResistance ?? 0.0),
      strCapacity: drift.Value(strCapacity ?? 0.0),
      rebar: drift.Value(
        rebar ?? '',
      ), //will be overriden  as soon as new instance created
      steelRatio: drift.Value(steelRatio ?? 0.0),
      isSelected: drift.Value(isSelected ?? false),
      calsLog: drift.Value(calsLog ?? ''),
      pileEndBearingBoredSchemeId: drift.Value(
        pileEndBearingBoredSchemeId ?? '',
      ),
    );
  }
}

@freezed
abstract class PileFrictionalBoredInputEntity
    with _$PileFrictionalBoredInputEntity {
  const PileFrictionalBoredInputEntity._();

  factory PileFrictionalBoredInputEntity({
    double? sptNValue,
    double? soilUnitWeight,
    double? kTan,
    double? fos,
    double? fcu,
    double? maxPileLength,
    double? maxPileDiameter,
    double? ratioOfBelloutDia,
    double? maxSteelRatio,
    double? slsLoad,
    double? ulsLoad,
    double? diaIncrement,
    bool? useSelectColLoad,
    String? id,
  }) = _PileFrictionalBoredInputEntity;

  ListPileFrictionalBoredInputCompanion toCompanion() {
    return ListPileFrictionalBoredInputCompanion(
      sptNValue: drift.Value(sptNValue ?? 50.0),
      soilUnitWeight: drift.Value(soilUnitWeight ?? 19.0),
      kTan: drift.Value(kTan ?? 0.25),
      fos: drift.Value(fos ?? 3.0),
      fcu: drift.Value(fcu ?? 45.0),
      maxPileLength: drift.Value(maxPileLength ?? 30.0),
      maxPileDiameter: drift.Value(maxPileDiameter ?? 2000.0),
      ratioOfBelloutDia: drift.Value(ratioOfBelloutDia ?? 1.65),
      maxSteelRatio: drift.Value(maxSteelRatio ?? 0.04),
      slsLoad: drift.Value(slsLoad ?? 1000.0),
      ulsLoad: drift.Value(ulsLoad ?? 2000.0),
      diaIncrement: drift.Value(diaIncrement ?? 200.0),
      useSelectColLoad: drift.Value(useSelectColLoad ?? false),
      id: drift.Value(id ?? '1'),
    );
  }
}

@freezed
abstract class PileFrictionalBoredInputGlobalEntity
    with _$PileFrictionalBoredInputGlobalEntity {
  const PileFrictionalBoredInputGlobalEntity._();

  factory PileFrictionalBoredInputGlobalEntity({
    double? colLoadFactor,
    String? id,
  }) = _PileFrictionalBoredInputGlobalEntity;

  ListPileFrictionalBoredInputGlobalCompanion toCompanion() {
    return ListPileFrictionalBoredInputGlobalCompanion(
      colLoadFactor: drift.Value(colLoadFactor ?? 1.0),
      id: drift.Value(id ?? '1'),
    );
  }
}

@freezed
abstract class PileFrictionalBoredDataEntity
    with _$PileFrictionalBoredDataEntity {
  const PileFrictionalBoredDataEntity._();

  factory PileFrictionalBoredDataEntity({
    double? sptNValue,
    double? soilUnitWeight,
    double? kTan,
    double? fos,
    double? fcu,
    double? maxPileLength,
    double? maxPileDiameter,
    double? ratioOfBelloutDia,
    double? maxSteelRatio,
    double? slsLoad,
    double? ulsLoad,
    double? diameter,
    double? length,
    double? shaftCapacity,
    double? baseCapacity,
    double? totalGroundResistance,
    double? strCapacity,
    String? rebar, //will be overriden  as soon as new instance created
    double? steelRatio,
    bool? isSelected,
    String? calsLog,
    String? pileFrictionalBoredSchemeId,
  }) = _PileFrictionalBoredDataEntity;

  ListPileFrictionalBoredDataCompanion toCompanion() {
    return ListPileFrictionalBoredDataCompanion(
      sptNValue: drift.Value(sptNValue ?? 50.0),
      soilUnitWeight: drift.Value(soilUnitWeight ?? 19.0),
      kTan: drift.Value(kTan ?? 0.25),
      fos: drift.Value(fos ?? 3.0),
      fcu: drift.Value(fcu ?? 45.0),
      maxPileLength: drift.Value(maxPileLength ?? 30.0),
      maxPileDiameter: drift.Value(maxPileDiameter ?? 2000.0),
      ratioOfBelloutDia: drift.Value(ratioOfBelloutDia ?? 1.65),
      maxSteelRatio: drift.Value(maxSteelRatio ?? 0.04),
      slsLoad: drift.Value(slsLoad ?? 1000.0),
      ulsLoad: drift.Value(ulsLoad ?? 2000.0),
      diameter: drift.Value(diameter ?? 0.0),
      length: drift.Value(length ?? 0.0),
      shaftCapacity: drift.Value(shaftCapacity ?? 0.0),
      baseCapacity: drift.Value(baseCapacity ?? 0.0),
      totalGroundResistance: drift.Value(totalGroundResistance ?? 0.0),
      strCapacity: drift.Value(strCapacity ?? 0.0),
      rebar: drift.Value(
        rebar ?? '',
      ), //will be overriden  as soon as new instance created
      steelRatio: drift.Value(steelRatio ?? 0.0),
      isSelected: drift.Value(isSelected ?? false),
      calsLog: drift.Value(calsLog ?? ''),
      pileFrictionalBoredSchemeId: drift.Value(
        pileFrictionalBoredSchemeId ?? '',
      ),
    );
  }
}

@freezed
abstract class PileSocketedDataEntity with _$PileSocketedDataEntity {
  const PileSocketedDataEntity._();

  factory PileSocketedDataEntity({
    bool? isSelected,
    String? pileSocketedSchemeId,
  }) = _PileSocketedDataEntity;

  ListPileSocketedDataCompanion toCompanion() {
    return ListPileSocketedDataCompanion(
      isSelected: drift.Value(isSelected ?? false),
      pileSocketedSchemeId: drift.Value(pileSocketedSchemeId ?? '1'),
    );
  }
}

@freezed
abstract class PileDrivenInputEntity with _$PileDrivenInputEntity {
  const PileDrivenInputEntity._();

  factory PileDrivenInputEntity({
    double? sptNValue,
    double? fos,
    double? maxPileLength,
    double? slsLoad,
    double? ulsLoad,
    bool? useSelectColLoad,
    String? id,
  }) = _PileDrivenInputEntity;

  ListPileDrivenInputCompanion toCompanion() {
    return ListPileDrivenInputCompanion(
      sptNValue: drift.Value(sptNValue ?? 50.0),
      fos: drift.Value(fos ?? 3.0),
      maxPileLength: drift.Value(maxPileLength ?? 30.0),
      slsLoad: drift.Value(slsLoad ?? 1000.0),
      ulsLoad: drift.Value(ulsLoad ?? 2000.0),
      useSelectColLoad: drift.Value(useSelectColLoad ?? false),
      id: drift.Value(id ?? '1'),
    );
  }
}

@freezed
abstract class PileDrivenInputGlobalEntity with _$PileDrivenInputGlobalEntity {
  const PileDrivenInputGlobalEntity._();

  factory PileDrivenInputGlobalEntity({double? colLoadFactor, String? id}) =
      _PileDrivenInputGlobalEntity;

  ListPileDrivenInputGlobalCompanion toCompanion() {
    return ListPileDrivenInputGlobalCompanion(
      colLoadFactor: drift.Value(colLoadFactor ?? 1.0),
      id: drift.Value(id ?? '1'),
    );
  }
}

@freezed
abstract class PileDrivenDataEntity with _$PileDrivenDataEntity {
  const PileDrivenDataEntity._();

  factory PileDrivenDataEntity({
    double? sptNValue,
    double? fos,
    double? maxPileLength,
    double? slsLoad,
    double? ulsLoad,
    String? section,
    double? length,
    double? shaftCapacity,
    double? strSLSCapacuity,
    double? totalGroundResistance,
    double? strULSCapacity,
    bool? isSelected,
    String? calsLog,
    String? pileDrivenSchemeId,
  }) = _PileDrivenDataEntity;

  ListPileDrivenDataCompanion toCompanion() {
    return ListPileDrivenDataCompanion(
      sptNValue: drift.Value(sptNValue ?? 50.0),
      fos: drift.Value(fos ?? 3.0),
      maxPileLength: drift.Value(maxPileLength ?? 30.0),
      slsLoad: drift.Value(slsLoad ?? 1000.0),
      ulsLoad: drift.Value(ulsLoad ?? 2000.0),
      section: drift.Value(section ?? ''),
      length: drift.Value(length ?? 0.0),
      shaftCapacity: drift.Value(shaftCapacity ?? 0.0),
      strSLSCapacuity: drift.Value(strSLSCapacuity ?? 0.0),
      totalGroundResistance: drift.Value(totalGroundResistance ?? 0.0),
      strULSCapacity: drift.Value(strULSCapacity ?? 0.0),
      isSelected: drift.Value(isSelected ?? false),
      calsLog: drift.Value(calsLog ?? ''),
      pileDrivenSchemeId: drift.Value(pileDrivenSchemeId ?? '1'),
    );
  }
}

@freezed
abstract class FootingSchemeInputEntity with _$FootingSchemeInputEntity {
  const FootingSchemeInputEntity._();
  factory FootingSchemeInputEntity({
    String? id,
    double? fcu,
    double? cover,
    double? mainKValue,
    double? mainSteelRatio,
    int? minS,
    int? maxS,
    double? maxDepth,
    double? minDepth,
    int? maxLayers,
    String? groundType,
    double? soilNValue,
    double? footingTopLevel,
    double? waterTableLevel,
    double? rockCapacity,
    String? colShape,
    double? columnSize,
    double? slsLoad,
    double? ulsLoad,
    bool? useSelectColLoad,
  }) = _FootingSchemeInputEntity;

  ListFootingSchemeInputCompanion toCompanion() {
    return ListFootingSchemeInputCompanion(
      id: drift.Value(id ?? '1'),
      fcu: drift.Value(fcu ?? 45.0),
      cover: drift.Value(cover ?? 35.0),
      mainKValue: drift.Value(mainKValue ?? 0.156),
      mainSteelRatio: drift.Value(mainSteelRatio ?? 0.04),
      minS: drift.Value(minS ?? 100),
      maxS: drift.Value(maxS ?? 300),
      maxDepth: drift.Value(maxDepth ?? 500.0),
      minDepth: drift.Value(minDepth ?? 150.0),
      maxLayers: drift.Value(maxLayers ?? 2),
      groundType: drift.Value(groundType ?? 'Soil'),
      soilNValue: drift.Value(soilNValue ?? 50),
      footingTopLevel: drift.Value(footingTopLevel ?? 0),
      waterTableLevel: drift.Value(waterTableLevel ?? -3),
      rockCapacity: drift.Value(rockCapacity ?? 1000),
      colShape: drift.Value(colShape ?? 'Sqaure'),
      columnSize: drift.Value(columnSize ?? 1000.0),
      slsLoad: drift.Value(slsLoad ?? 2000.0),
      ulsLoad: drift.Value(ulsLoad ?? 3000.0),
      useSelectColLoad: drift.Value(useSelectColLoad ?? false),
    );
  }
}

@freezed
abstract class FootingSchemeInputGlobalEntity
    with _$FootingSchemeInputGlobalEntity {
  const FootingSchemeInputGlobalEntity._();
  factory FootingSchemeInputGlobalEntity({String? id, double? colLoadFactor}) =
      _FootingSchemeInputGlobalEntity;

  ListFootingSchemeInputGlobalCompanion toCompanion() {
    return ListFootingSchemeInputGlobalCompanion(
      id: drift.Value(id ?? '1'),
      colLoadFactor: drift.Value(colLoadFactor ?? 1.0),
    );
  }
}

@freezed
abstract class FootingSchemeDataEntity with _$FootingSchemeDataEntity {
  const FootingSchemeDataEntity._();
  factory FootingSchemeDataEntity({
    double? size,
    double? strZone,
    double? fcu,
    double? cover,
    String? groundType,
    double? soilNValue,
    double? footingTopLevel,
    double? waterTableLevel,
    double? rockCapacity,
    String? colShape,
    double? columnSize,
    double? slsLoad,
    double? ulsLoad,
    String? mainTopBar,
    String? mainBottomBar,
    String? mainLinks,
    String? punchingLinks,
    String? calsLog,
    bool? isSelected,
    String? footingSchemeDataId,
  }) = _FootingSchemeDataEntity;

  ListFootingSchemeDataCompanion toCompanion() {
    return ListFootingSchemeDataCompanion(
      size: drift.Value(size ?? 5.0),
      strZone: drift.Value(strZone ?? 500.0),
      fcu: drift.Value(fcu ?? 45.0),
      cover: drift.Value(cover ?? 35.0),
      groundType: drift.Value(groundType ?? 'Soil'),
      soilNValue: drift.Value(soilNValue ?? 50),
      footingTopLevel: drift.Value(footingTopLevel ?? 0),
      waterTableLevel: drift.Value(waterTableLevel ?? -3),
      rockCapacity: drift.Value(rockCapacity ?? 1000),
      colShape: drift.Value(colShape ?? 'Sqaure'),
      columnSize: drift.Value(columnSize ?? 1000.0),
      slsLoad: drift.Value(slsLoad ?? 2000.0),
      ulsLoad: drift.Value(ulsLoad ?? 3000.0),
      mainTopBar: drift.Value(mainTopBar ?? ''),
      mainBottomBar: drift.Value(mainBottomBar ?? ''),
      mainLinks: drift.Value(mainLinks ?? ''),
      punchingLinks: drift.Value(punchingLinks ?? ''),
      calsLog: drift.Value(calsLog ?? ''),
      isSelected: drift.Value(isSelected ?? false),
      footingSchemeDataId: drift.Value(footingSchemeDataId ?? ''),
    );
  }
}

@freezed
abstract class StrZoneTableEntity with _$StrZoneTableEntity {
  const StrZoneTableEntity._();
  factory StrZoneTableEntity({
    String? floor,
    double? height,
    double? finish,
    double? service,
    double? clear,
    int? nosOfFloor,
    String? strZoneId,
  }) = _StrZoneTableEntity;

  ListStrZoneTableCompanion toCompanion() {
    return ListStrZoneTableCompanion(
      floor: drift.Value(floor ?? ''),
      height: drift.Value(height ?? 0.0),
      finish: drift.Value(finish ?? 0.0),
      service: drift.Value(service ?? 0.0),
      clear: drift.Value(clear ?? 0.0),
      nosOfFloor: drift.Value(nosOfFloor ?? 0),
      strZoneId: drift.Value(strZoneId ?? ''),
    );
  }
}

@freezed
abstract class RecommendedLoadEntity with _$RecommendedLoadEntity {
  const RecommendedLoadEntity._();
  factory RecommendedLoadEntity({
    String? usage,
    double? finish,
    double? service,
    double? partitionLoad,
    double? sdl,
    double? ll,
    double? frp,
  }) = _RecommendedLoadEntity;

  ListRecommendedLoadCompanion toCompanion() {
    return ListRecommendedLoadCompanion(
      usage: drift.Value(usage ?? ''),
      finish: drift.Value(finish ?? 0.0),
      service: drift.Value(service ?? 0.0),
      partitionLoad: drift.Value(partitionLoad ?? 0.0),
      sdl: drift.Value(sdl ?? 0.0),
      ll: drift.Value(ll ?? 0.0),
      frp: drift.Value(frp ?? 0.0),
    );
  }
}

@freezed
abstract class BasementWallSchemeInputEntity
    with _$BasementWallSchemeInputEntity {
  const BasementWallSchemeInputEntity._();
  factory BasementWallSchemeInputEntity({
    String? id,
    double? fcu,
    double? cover,
    double? mainKValue,
    double? mainSteelRatio,
    int? minS,
    int? maxS,
    double? maxDepth,
    double? minDepth,
    int? maxLayers,
    double? wallTopLevel,
    double? wallBottomLevel,
    double? soilTopLevel,
    double? waterTopLevel,
  }) = _BasementWallSchemeInputEntity;

  ListBasementWallSchemeInputCompanion toCompanion() {
    return ListBasementWallSchemeInputCompanion(
      id: drift.Value(id ?? '1'),
      fcu: drift.Value(fcu ?? 45.0),
      cover: drift.Value(cover ?? 35.0),
      mainKValue: drift.Value(mainKValue ?? 0.156),
      mainSteelRatio: drift.Value(mainSteelRatio ?? 0.04),
      minS: drift.Value(minS ?? 100),
      maxS: drift.Value(maxS ?? 300),
      maxDepth: drift.Value(maxDepth ?? 500.0),
      minDepth: drift.Value(minDepth ?? 150.0),
      maxLayers: drift.Value(maxLayers ?? 2),
      wallTopLevel: drift.Value(wallTopLevel ?? 0.0),
      wallBottomLevel: drift.Value(wallBottomLevel ?? -3.0),
      soilTopLevel: drift.Value(soilTopLevel ?? 0.0),
      waterTopLevel: drift.Value(waterTopLevel ?? -3.0),
    );
  }
}

@freezed
abstract class BasementWallSchemeDataEntity
    with _$BasementWallSchemeDataEntity {
  const BasementWallSchemeDataEntity._();
  factory BasementWallSchemeDataEntity({
    double? strZone,
    double? fcu,
    double? cover,
    String? mainTopBar,
    String? mainBottomBar,
    String? mainLinks,
    double? wallTopLevel,
    double? wallBottomLevel,
    double? soilTopLevel,
    double? waterTopLevel,
    String? basementWallSchemeId,
    String? calsLog,
    String? wallForceULS,
    String? wallForceSLS,
  }) = _BasementWallSchemeDataEntity;

  ListBasementWallSchemeDataCompanion toCompanion() {
    return ListBasementWallSchemeDataCompanion(
      strZone: drift.Value(strZone ?? 0.0),
      fcu: drift.Value(fcu ?? 45.0),
      cover: drift.Value(cover ?? 35.0),
      mainTopBar: drift.Value(mainTopBar ?? ''),
      mainBottomBar: drift.Value(mainBottomBar ?? ''),
      mainLinks: drift.Value(mainLinks ?? ''),
      wallTopLevel: drift.Value(wallTopLevel ?? 0.0),
      wallBottomLevel: drift.Value(wallBottomLevel ?? -3.0),
      soilTopLevel: drift.Value(soilTopLevel ?? 0.0),
      waterTopLevel: drift.Value(waterTopLevel ?? -3.0),
      basementWallSchemeId: drift.Value(basementWallSchemeId ?? ''),
      calsLog: drift.Value(calsLog ?? ''),
      wallForceULS: drift.Value(wallForceULS ?? ''),
      wallForceSLS: drift.Value(wallForceSLS ?? ''),
    );
  }
}

@freezed
abstract class ProgrammeItemEntity with _$ProgrammeItemEntity {
  const ProgrammeItemEntity._();
  factory ProgrammeItemEntity({
    String? itemName,
    double? start,
    double? duration,
    bool? isTouched,
    String? id,
  }) = _ProgrammeItemEntity;

  ListProgrammeItemCompanion toCompanion() {
    return ListProgrammeItemCompanion(
      itemName: drift.Value(itemName ?? ''),
      start: drift.Value(start ?? 0.0),
      duration: drift.Value(duration ?? 0.0),
      id: drift.Value(id ?? ''),
      isTouched: drift.Value(isTouched ?? false),
    );
  }
  
}
