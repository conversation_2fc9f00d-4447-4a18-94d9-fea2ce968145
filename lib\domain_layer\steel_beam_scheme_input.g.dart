// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'steel_beam_scheme_input.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_SteelBeamSchemeInput _$SteelBeamSchemeInputFromJson(
  Map<String, dynamic> json,
) => _SteelBeamSchemeInput(
  id: json['id'] as String? ?? '1',
  shortSpan: (json['shortSpan'] as num?)?.toDouble() ?? 6.0,
  longSpan: (json['longSpan'] as num?)?.toDouble() ?? 10.0,
  bays: (json['bays'] as num?)?.toInt() ?? 2,
  strZone: (json['strZone'] as num?)?.toDouble() ?? 500.0,
  fsy: (json['fsy'] as num?)?.toDouble() ?? 355.0,
  shortSpanIncreament: (json['shortSpanIncreament'] as num?)?.toDouble() ?? 1.0,
  longSpanIncreament: (json['longSpanIncreament'] as num?)?.toDouble() ?? 1.0,
  baysIncreament: (json['baysIncreament'] as num?)?.toInt() ?? 1,
  iterationSteps: (json['iterationSteps'] as num?)?.toInt() ?? 10,
  usage: json['usage'] as String? ?? '',
  slabThickness: (json['slabThickness'] as num?)?.toDouble() ?? 130.0,
  compositeActionFactor:
      (json['compositeActionFactor'] as num?)?.toDouble() ?? 1.0,
);

Map<String, dynamic> _$SteelBeamSchemeInputToJson(
  _SteelBeamSchemeInput instance,
) => <String, dynamic>{
  'id': instance.id,
  'shortSpan': instance.shortSpan,
  'longSpan': instance.longSpan,
  'bays': instance.bays,
  'strZone': instance.strZone,
  'fsy': instance.fsy,
  'shortSpanIncreament': instance.shortSpanIncreament,
  'longSpanIncreament': instance.longSpanIncreament,
  'baysIncreament': instance.baysIncreament,
  'iterationSteps': instance.iterationSteps,
  'usage': instance.usage,
  'slabThickness': instance.slabThickness,
  'compositeActionFactor': instance.compositeActionFactor,
};
