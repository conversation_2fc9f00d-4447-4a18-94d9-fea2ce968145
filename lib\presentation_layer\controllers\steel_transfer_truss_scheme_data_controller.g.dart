// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'steel_transfer_truss_scheme_data_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$steelTransferTrussSchemeDataControllerHash() =>
    r'44d8cd61d09c99a387c90a556ca13ab5680163e6';

/// See also [SteelTransferTrussSchemeDataController].
@ProviderFor(SteelTransferTrussSchemeDataController)
final steelTransferTrussSchemeDataControllerProvider =
    AutoDisposeAsyncNotifierProvider<
      SteelTransferTrussSchemeDataController,
      SteelTransferTrussSchemeData
    >.internal(
      SteelTransferTrussSchemeDataController.new,
      name: r'steelTransferTrussSchemeDataControllerProvider',
      debugGetCreateSourceHash:
          const bool.fromEnvironment('dart.vm.product')
              ? null
              : _$steelTransferTrussSchemeDataControllerHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$SteelTransferTrussSchemeDataController =
    AutoDisposeAsyncNotifier<SteelTransferTrussSchemeData>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
