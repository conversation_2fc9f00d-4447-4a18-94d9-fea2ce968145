// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'cantilever_scheme_data.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$CantileverSchemeData {

 String get usage; double get slabThickness; double get span; double get loadWidth; double get strZone; double get fcu; double get cover; double get mainWidth; String get mainTopBar; String get mainBottomBar; String get mainLinks; String get id;//will be overriden  as soon as new instance created
 String get calsLog; String get beamForce;
/// Create a copy of CantileverSchemeData
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$CantileverSchemeDataCopyWith<CantileverSchemeData> get copyWith => _$CantileverSchemeDataCopyWithImpl<CantileverSchemeData>(this as CantileverSchemeData, _$identity);

  /// Serializes this CantileverSchemeData to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is CantileverSchemeData&&(identical(other.usage, usage) || other.usage == usage)&&(identical(other.slabThickness, slabThickness) || other.slabThickness == slabThickness)&&(identical(other.span, span) || other.span == span)&&(identical(other.loadWidth, loadWidth) || other.loadWidth == loadWidth)&&(identical(other.strZone, strZone) || other.strZone == strZone)&&(identical(other.fcu, fcu) || other.fcu == fcu)&&(identical(other.cover, cover) || other.cover == cover)&&(identical(other.mainWidth, mainWidth) || other.mainWidth == mainWidth)&&(identical(other.mainTopBar, mainTopBar) || other.mainTopBar == mainTopBar)&&(identical(other.mainBottomBar, mainBottomBar) || other.mainBottomBar == mainBottomBar)&&(identical(other.mainLinks, mainLinks) || other.mainLinks == mainLinks)&&(identical(other.id, id) || other.id == id)&&(identical(other.calsLog, calsLog) || other.calsLog == calsLog)&&(identical(other.beamForce, beamForce) || other.beamForce == beamForce));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,usage,slabThickness,span,loadWidth,strZone,fcu,cover,mainWidth,mainTopBar,mainBottomBar,mainLinks,id,calsLog,beamForce);

@override
String toString() {
  return 'CantileverSchemeData(usage: $usage, slabThickness: $slabThickness, span: $span, loadWidth: $loadWidth, strZone: $strZone, fcu: $fcu, cover: $cover, mainWidth: $mainWidth, mainTopBar: $mainTopBar, mainBottomBar: $mainBottomBar, mainLinks: $mainLinks, id: $id, calsLog: $calsLog, beamForce: $beamForce)';
}


}

/// @nodoc
abstract mixin class $CantileverSchemeDataCopyWith<$Res>  {
  factory $CantileverSchemeDataCopyWith(CantileverSchemeData value, $Res Function(CantileverSchemeData) _then) = _$CantileverSchemeDataCopyWithImpl;
@useResult
$Res call({
 String usage, double slabThickness, double span, double loadWidth, double strZone, double fcu, double cover, double mainWidth, String mainTopBar, String mainBottomBar, String mainLinks, String id, String calsLog, String beamForce
});




}
/// @nodoc
class _$CantileverSchemeDataCopyWithImpl<$Res>
    implements $CantileverSchemeDataCopyWith<$Res> {
  _$CantileverSchemeDataCopyWithImpl(this._self, this._then);

  final CantileverSchemeData _self;
  final $Res Function(CantileverSchemeData) _then;

/// Create a copy of CantileverSchemeData
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? usage = null,Object? slabThickness = null,Object? span = null,Object? loadWidth = null,Object? strZone = null,Object? fcu = null,Object? cover = null,Object? mainWidth = null,Object? mainTopBar = null,Object? mainBottomBar = null,Object? mainLinks = null,Object? id = null,Object? calsLog = null,Object? beamForce = null,}) {
  return _then(_self.copyWith(
usage: null == usage ? _self.usage : usage // ignore: cast_nullable_to_non_nullable
as String,slabThickness: null == slabThickness ? _self.slabThickness : slabThickness // ignore: cast_nullable_to_non_nullable
as double,span: null == span ? _self.span : span // ignore: cast_nullable_to_non_nullable
as double,loadWidth: null == loadWidth ? _self.loadWidth : loadWidth // ignore: cast_nullable_to_non_nullable
as double,strZone: null == strZone ? _self.strZone : strZone // ignore: cast_nullable_to_non_nullable
as double,fcu: null == fcu ? _self.fcu : fcu // ignore: cast_nullable_to_non_nullable
as double,cover: null == cover ? _self.cover : cover // ignore: cast_nullable_to_non_nullable
as double,mainWidth: null == mainWidth ? _self.mainWidth : mainWidth // ignore: cast_nullable_to_non_nullable
as double,mainTopBar: null == mainTopBar ? _self.mainTopBar : mainTopBar // ignore: cast_nullable_to_non_nullable
as String,mainBottomBar: null == mainBottomBar ? _self.mainBottomBar : mainBottomBar // ignore: cast_nullable_to_non_nullable
as String,mainLinks: null == mainLinks ? _self.mainLinks : mainLinks // ignore: cast_nullable_to_non_nullable
as String,id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,calsLog: null == calsLog ? _self.calsLog : calsLog // ignore: cast_nullable_to_non_nullable
as String,beamForce: null == beamForce ? _self.beamForce : beamForce // ignore: cast_nullable_to_non_nullable
as String,
  ));
}

}


/// Adds pattern-matching-related methods to [CantileverSchemeData].
extension CantileverSchemeDataPatterns on CantileverSchemeData {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _CantileverSchemeData value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _CantileverSchemeData() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _CantileverSchemeData value)  $default,){
final _that = this;
switch (_that) {
case _CantileverSchemeData():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _CantileverSchemeData value)?  $default,){
final _that = this;
switch (_that) {
case _CantileverSchemeData() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String usage,  double slabThickness,  double span,  double loadWidth,  double strZone,  double fcu,  double cover,  double mainWidth,  String mainTopBar,  String mainBottomBar,  String mainLinks,  String id,  String calsLog,  String beamForce)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _CantileverSchemeData() when $default != null:
return $default(_that.usage,_that.slabThickness,_that.span,_that.loadWidth,_that.strZone,_that.fcu,_that.cover,_that.mainWidth,_that.mainTopBar,_that.mainBottomBar,_that.mainLinks,_that.id,_that.calsLog,_that.beamForce);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String usage,  double slabThickness,  double span,  double loadWidth,  double strZone,  double fcu,  double cover,  double mainWidth,  String mainTopBar,  String mainBottomBar,  String mainLinks,  String id,  String calsLog,  String beamForce)  $default,) {final _that = this;
switch (_that) {
case _CantileverSchemeData():
return $default(_that.usage,_that.slabThickness,_that.span,_that.loadWidth,_that.strZone,_that.fcu,_that.cover,_that.mainWidth,_that.mainTopBar,_that.mainBottomBar,_that.mainLinks,_that.id,_that.calsLog,_that.beamForce);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String usage,  double slabThickness,  double span,  double loadWidth,  double strZone,  double fcu,  double cover,  double mainWidth,  String mainTopBar,  String mainBottomBar,  String mainLinks,  String id,  String calsLog,  String beamForce)?  $default,) {final _that = this;
switch (_that) {
case _CantileverSchemeData() when $default != null:
return $default(_that.usage,_that.slabThickness,_that.span,_that.loadWidth,_that.strZone,_that.fcu,_that.cover,_that.mainWidth,_that.mainTopBar,_that.mainBottomBar,_that.mainLinks,_that.id,_that.calsLog,_that.beamForce);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _CantileverSchemeData extends CantileverSchemeData {
   _CantileverSchemeData({this.usage = '', this.slabThickness = 150.0, this.span = 1.0, this.loadWidth = 1.0, this.strZone = 500.0, this.fcu = 45.0, this.cover = 40.0, this.mainWidth = 200.0, this.mainTopBar = '', this.mainBottomBar = '', this.mainLinks = '', this.id = '1', this.calsLog = '', this.beamForce = ''}): super._();
  factory _CantileverSchemeData.fromJson(Map<String, dynamic> json) => _$CantileverSchemeDataFromJson(json);

@override@JsonKey() final  String usage;
@override@JsonKey() final  double slabThickness;
@override@JsonKey() final  double span;
@override@JsonKey() final  double loadWidth;
@override@JsonKey() final  double strZone;
@override@JsonKey() final  double fcu;
@override@JsonKey() final  double cover;
@override@JsonKey() final  double mainWidth;
@override@JsonKey() final  String mainTopBar;
@override@JsonKey() final  String mainBottomBar;
@override@JsonKey() final  String mainLinks;
@override@JsonKey() final  String id;
//will be overriden  as soon as new instance created
@override@JsonKey() final  String calsLog;
@override@JsonKey() final  String beamForce;

/// Create a copy of CantileverSchemeData
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$CantileverSchemeDataCopyWith<_CantileverSchemeData> get copyWith => __$CantileverSchemeDataCopyWithImpl<_CantileverSchemeData>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$CantileverSchemeDataToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _CantileverSchemeData&&(identical(other.usage, usage) || other.usage == usage)&&(identical(other.slabThickness, slabThickness) || other.slabThickness == slabThickness)&&(identical(other.span, span) || other.span == span)&&(identical(other.loadWidth, loadWidth) || other.loadWidth == loadWidth)&&(identical(other.strZone, strZone) || other.strZone == strZone)&&(identical(other.fcu, fcu) || other.fcu == fcu)&&(identical(other.cover, cover) || other.cover == cover)&&(identical(other.mainWidth, mainWidth) || other.mainWidth == mainWidth)&&(identical(other.mainTopBar, mainTopBar) || other.mainTopBar == mainTopBar)&&(identical(other.mainBottomBar, mainBottomBar) || other.mainBottomBar == mainBottomBar)&&(identical(other.mainLinks, mainLinks) || other.mainLinks == mainLinks)&&(identical(other.id, id) || other.id == id)&&(identical(other.calsLog, calsLog) || other.calsLog == calsLog)&&(identical(other.beamForce, beamForce) || other.beamForce == beamForce));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,usage,slabThickness,span,loadWidth,strZone,fcu,cover,mainWidth,mainTopBar,mainBottomBar,mainLinks,id,calsLog,beamForce);

@override
String toString() {
  return 'CantileverSchemeData(usage: $usage, slabThickness: $slabThickness, span: $span, loadWidth: $loadWidth, strZone: $strZone, fcu: $fcu, cover: $cover, mainWidth: $mainWidth, mainTopBar: $mainTopBar, mainBottomBar: $mainBottomBar, mainLinks: $mainLinks, id: $id, calsLog: $calsLog, beamForce: $beamForce)';
}


}

/// @nodoc
abstract mixin class _$CantileverSchemeDataCopyWith<$Res> implements $CantileverSchemeDataCopyWith<$Res> {
  factory _$CantileverSchemeDataCopyWith(_CantileverSchemeData value, $Res Function(_CantileverSchemeData) _then) = __$CantileverSchemeDataCopyWithImpl;
@override @useResult
$Res call({
 String usage, double slabThickness, double span, double loadWidth, double strZone, double fcu, double cover, double mainWidth, String mainTopBar, String mainBottomBar, String mainLinks, String id, String calsLog, String beamForce
});




}
/// @nodoc
class __$CantileverSchemeDataCopyWithImpl<$Res>
    implements _$CantileverSchemeDataCopyWith<$Res> {
  __$CantileverSchemeDataCopyWithImpl(this._self, this._then);

  final _CantileverSchemeData _self;
  final $Res Function(_CantileverSchemeData) _then;

/// Create a copy of CantileverSchemeData
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? usage = null,Object? slabThickness = null,Object? span = null,Object? loadWidth = null,Object? strZone = null,Object? fcu = null,Object? cover = null,Object? mainWidth = null,Object? mainTopBar = null,Object? mainBottomBar = null,Object? mainLinks = null,Object? id = null,Object? calsLog = null,Object? beamForce = null,}) {
  return _then(_CantileverSchemeData(
usage: null == usage ? _self.usage : usage // ignore: cast_nullable_to_non_nullable
as String,slabThickness: null == slabThickness ? _self.slabThickness : slabThickness // ignore: cast_nullable_to_non_nullable
as double,span: null == span ? _self.span : span // ignore: cast_nullable_to_non_nullable
as double,loadWidth: null == loadWidth ? _self.loadWidth : loadWidth // ignore: cast_nullable_to_non_nullable
as double,strZone: null == strZone ? _self.strZone : strZone // ignore: cast_nullable_to_non_nullable
as double,fcu: null == fcu ? _self.fcu : fcu // ignore: cast_nullable_to_non_nullable
as double,cover: null == cover ? _self.cover : cover // ignore: cast_nullable_to_non_nullable
as double,mainWidth: null == mainWidth ? _self.mainWidth : mainWidth // ignore: cast_nullable_to_non_nullable
as double,mainTopBar: null == mainTopBar ? _self.mainTopBar : mainTopBar // ignore: cast_nullable_to_non_nullable
as String,mainBottomBar: null == mainBottomBar ? _self.mainBottomBar : mainBottomBar // ignore: cast_nullable_to_non_nullable
as String,mainLinks: null == mainLinks ? _self.mainLinks : mainLinks // ignore: cast_nullable_to_non_nullable
as String,id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,calsLog: null == calsLog ? _self.calsLog : calsLog // ignore: cast_nullable_to_non_nullable
as String,beamForce: null == beamForce ? _self.beamForce : beamForce // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

// dart format on
