import 'package:nanoid2/nanoid2.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

//presentation layer
import '../../domain_layer/column_scheme_data.dart';
import '../../domain_layer/data_struct/carbon_struct.dart';
import '../../domain_layer/transfer_beam_scheme_input.dart';
import '../screen/homescreen.dart';

// domain layer

part 'carbon_input_controller.g.dart';

@riverpod
class CarbonInputController extends _$CarbonInputController {
  @override
  FutureOr<List<Carbon>> build() async {
    // print('Build: Transfer Beam Scheme Input');
    final List<Carbon> carbonInputs =
        await ref
            .read(appDatabaseControllerProvider.notifier)
            .queryCarbonInput();
    return carbonInputs;
  }

  Future<void> addEmptytable() async {
    final x = await future;
    final id = await _generateTaskID();
    state = AsyncData([
      ...x,
      Carbon.create(carbonType: CarbonType.concreteArea, id: id),
    ]);
  }

  Future<void> deleteTable(String id) async {
    final x = await future;
    x.removeWhere((item) => item.id == id);
    state = AsyncData(x);
  }

  Future<void> deleteAllTable() async {
    state = AsyncData(<Carbon>[]);
  }

  Future<void> insertEmptyTable(int index) async {
    final x = await future;
    final id = await _generateTaskID();
    String newName = x[index].name;
    final names = x.map((item) => item.name).toSet();
    int i = 1;
    while (names.contains(newName)) {
      newName = '${newName}_${i}';
    }
    x.insert(
      index + 1,
      Carbon.create(
        carbonType: CarbonType.concreteArea,
        id: id,
        data: {'name': newName},
      ),
    );
    state = AsyncData(x);
  }

  Future<void> copyAndInsertTable(int index) async {
    final x = await future;
    final id = await _generateTaskID();
    String newName = x[index].name;
    final names = x.map((item) => item.name).toSet();
    int i = 1;
    while (names.contains(newName)) {
      newName = '${newName}_${i}';
    }
    x.insert(index + 1, x[index].copyWith(name: newName, id: id));
    state = AsyncData(x);
  }

  Future<String> _generateTaskID() async {
    String newID = nanoid(alphabet: Alphabet.noDoppelgangerSafe);
    final x = await future;
    final Set<String> carbonInputIds = x.map((item) => item.id).toSet();
    // final Set<String> taskIDs = ref.read(_taskIDsProvider);
    while (carbonInputIds.contains(newID)) {
      newID = nanoid(alphabet: Alphabet.noDoppelgangerSafe);
    }
    return newID;
  }

  Future<void> updateType(String id, CarbonType carbonType) async {
    final x = await future;
    List<Carbon> finalList = [];
    late Carbon data;
    // Create a list of updates
    await Future.forEach((x), (x1) {
      if (x1.id == id) {
        if (carbonType == x1.carbonType) {
           data = x1;
        } else {
           data = Carbon.create(carbonType: carbonType, id: id);
        };
      } else {
        data = x1;
      }
      finalList.add(data);
    });
    // Update the state only if there are changes
    state = AsyncData(finalList);
  }

  Future<void> updateTable(
    String id, {
    String? name,
    double? thk,
    double? area,
    double? length,
    double? sectionSize,
    double? totalSteelRatio,
  }) async {
    final x = await future;
    List<Carbon> finalList = [];
    late Carbon data;
    // Create a list of updates
    await Future.forEach((x), (x1) {
      if (x1.id == id) {
        data = x1.generalCopyWith(
          name: name,
          thk: thk,
          area: area,
          length: length,
          sectionSize: sectionSize,
          totalSteelRatio: totalSteelRatio,
          id: id,
        );
      } else {
        data = x1;
      }
      finalList.add(data);
    });
    // Update the state only if there are changes
    state = AsyncData(finalList);
  }
}
