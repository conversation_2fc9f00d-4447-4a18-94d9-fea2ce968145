// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'wind_load_global.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
WindLoadGlobal _$WindLoadGlobalFromJson(
  Map<String, dynamic> json
) {
    return _WindLoad.fromJson(
      json
    );
}

/// @nodoc
mixin _$WindLoadGlobal {

 double get h;// Building Height 
 double get bTop;// building width at top
 double get dTop;// building width at top
 double get sS;// size factor
 String get bldgType;// building depth at height considered
 double get amplificationFactor; String get id;
/// Create a copy of WindLoadGlobal
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$WindLoadGlobalCopyWith<WindLoadGlobal> get copyWith => _$WindLoadGlobalCopyWithImpl<WindLoadGlobal>(this as WindLoadGlobal, _$identity);

  /// Serializes this WindLoadGlobal to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is WindLoadGlobal&&(identical(other.h, h) || other.h == h)&&(identical(other.bTop, bTop) || other.bTop == bTop)&&(identical(other.dTop, dTop) || other.dTop == dTop)&&(identical(other.sS, sS) || other.sS == sS)&&(identical(other.bldgType, bldgType) || other.bldgType == bldgType)&&(identical(other.amplificationFactor, amplificationFactor) || other.amplificationFactor == amplificationFactor)&&(identical(other.id, id) || other.id == id));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,h,bTop,dTop,sS,bldgType,amplificationFactor,id);

@override
String toString() {
  return 'WindLoadGlobal(h: $h, bTop: $bTop, dTop: $dTop, sS: $sS, bldgType: $bldgType, amplificationFactor: $amplificationFactor, id: $id)';
}


}

/// @nodoc
abstract mixin class $WindLoadGlobalCopyWith<$Res>  {
  factory $WindLoadGlobalCopyWith(WindLoadGlobal value, $Res Function(WindLoadGlobal) _then) = _$WindLoadGlobalCopyWithImpl;
@useResult
$Res call({
 double h, double bTop, double dTop, double sS, String bldgType, double amplificationFactor, String id
});




}
/// @nodoc
class _$WindLoadGlobalCopyWithImpl<$Res>
    implements $WindLoadGlobalCopyWith<$Res> {
  _$WindLoadGlobalCopyWithImpl(this._self, this._then);

  final WindLoadGlobal _self;
  final $Res Function(WindLoadGlobal) _then;

/// Create a copy of WindLoadGlobal
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? h = null,Object? bTop = null,Object? dTop = null,Object? sS = null,Object? bldgType = null,Object? amplificationFactor = null,Object? id = null,}) {
  return _then(_self.copyWith(
h: null == h ? _self.h : h // ignore: cast_nullable_to_non_nullable
as double,bTop: null == bTop ? _self.bTop : bTop // ignore: cast_nullable_to_non_nullable
as double,dTop: null == dTop ? _self.dTop : dTop // ignore: cast_nullable_to_non_nullable
as double,sS: null == sS ? _self.sS : sS // ignore: cast_nullable_to_non_nullable
as double,bldgType: null == bldgType ? _self.bldgType : bldgType // ignore: cast_nullable_to_non_nullable
as String,amplificationFactor: null == amplificationFactor ? _self.amplificationFactor : amplificationFactor // ignore: cast_nullable_to_non_nullable
as double,id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,
  ));
}

}


/// Adds pattern-matching-related methods to [WindLoadGlobal].
extension WindLoadGlobalPatterns on WindLoadGlobal {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _WindLoad value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _WindLoad() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _WindLoad value)  $default,){
final _that = this;
switch (_that) {
case _WindLoad():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _WindLoad value)?  $default,){
final _that = this;
switch (_that) {
case _WindLoad() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( double h,  double bTop,  double dTop,  double sS,  String bldgType,  double amplificationFactor,  String id)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _WindLoad() when $default != null:
return $default(_that.h,_that.bTop,_that.dTop,_that.sS,_that.bldgType,_that.amplificationFactor,_that.id);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( double h,  double bTop,  double dTop,  double sS,  String bldgType,  double amplificationFactor,  String id)  $default,) {final _that = this;
switch (_that) {
case _WindLoad():
return $default(_that.h,_that.bTop,_that.dTop,_that.sS,_that.bldgType,_that.amplificationFactor,_that.id);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( double h,  double bTop,  double dTop,  double sS,  String bldgType,  double amplificationFactor,  String id)?  $default,) {final _that = this;
switch (_that) {
case _WindLoad() when $default != null:
return $default(_that.h,_that.bTop,_that.dTop,_that.sS,_that.bldgType,_that.amplificationFactor,_that.id);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _WindLoad extends WindLoadGlobal {
   _WindLoad({this.h = 50.0, this.bTop = 30.0, this.dTop = 40.0, this.sS = 1.11, this.bldgType = 'Concrete', this.amplificationFactor = 1.4, this.id = '1'}): super._();
  factory _WindLoad.fromJson(Map<String, dynamic> json) => _$WindLoadFromJson(json);

@override@JsonKey() final  double h;
// Building Height 
@override@JsonKey() final  double bTop;
// building width at top
@override@JsonKey() final  double dTop;
// building width at top
@override@JsonKey() final  double sS;
// size factor
@override@JsonKey() final  String bldgType;
// building depth at height considered
@override@JsonKey() final  double amplificationFactor;
@override@JsonKey() final  String id;

/// Create a copy of WindLoadGlobal
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$WindLoadCopyWith<_WindLoad> get copyWith => __$WindLoadCopyWithImpl<_WindLoad>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$WindLoadToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _WindLoad&&(identical(other.h, h) || other.h == h)&&(identical(other.bTop, bTop) || other.bTop == bTop)&&(identical(other.dTop, dTop) || other.dTop == dTop)&&(identical(other.sS, sS) || other.sS == sS)&&(identical(other.bldgType, bldgType) || other.bldgType == bldgType)&&(identical(other.amplificationFactor, amplificationFactor) || other.amplificationFactor == amplificationFactor)&&(identical(other.id, id) || other.id == id));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,h,bTop,dTop,sS,bldgType,amplificationFactor,id);

@override
String toString() {
  return 'WindLoadGlobal(h: $h, bTop: $bTop, dTop: $dTop, sS: $sS, bldgType: $bldgType, amplificationFactor: $amplificationFactor, id: $id)';
}


}

/// @nodoc
abstract mixin class _$WindLoadCopyWith<$Res> implements $WindLoadGlobalCopyWith<$Res> {
  factory _$WindLoadCopyWith(_WindLoad value, $Res Function(_WindLoad) _then) = __$WindLoadCopyWithImpl;
@override @useResult
$Res call({
 double h, double bTop, double dTop, double sS, String bldgType, double amplificationFactor, String id
});




}
/// @nodoc
class __$WindLoadCopyWithImpl<$Res>
    implements _$WindLoadCopyWith<$Res> {
  __$WindLoadCopyWithImpl(this._self, this._then);

  final _WindLoad _self;
  final $Res Function(_WindLoad) _then;

/// Create a copy of WindLoadGlobal
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? h = null,Object? bTop = null,Object? dTop = null,Object? sS = null,Object? bldgType = null,Object? amplificationFactor = null,Object? id = null,}) {
  return _then(_WindLoad(
h: null == h ? _self.h : h // ignore: cast_nullable_to_non_nullable
as double,bTop: null == bTop ? _self.bTop : bTop // ignore: cast_nullable_to_non_nullable
as double,dTop: null == dTop ? _self.dTop : dTop // ignore: cast_nullable_to_non_nullable
as double,sS: null == sS ? _self.sS : sS // ignore: cast_nullable_to_non_nullable
as double,bldgType: null == bldgType ? _self.bldgType : bldgType // ignore: cast_nullable_to_non_nullable
as String,amplificationFactor: null == amplificationFactor ? _self.amplificationFactor : amplificationFactor // ignore: cast_nullable_to_non_nullable
as double,id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

// dart format on
