// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'app_watcher.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$appWatcherHash() => r'cf1afabe6944d4f0db2389db5260973b24af0300';

/// See also [AppWatcher].
@ProviderFor(AppWatcher)
final appWatcherProvider =
    AutoDisposeAsyncNotifierProvider<AppWatcher, void>.internal(
      AppWatcher.new,
      name: r'appWatcherProvider',
      debugGetCreateSourceHash:
          const bool.fromEnvironment('dart.vm.product')
              ? null
              : _$appWatcherHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$AppWatcher = AutoDisposeAsyncNotifier<void>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
