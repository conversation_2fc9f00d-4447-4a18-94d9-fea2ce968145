import 'package:freezed_annotation/freezed_annotation.dart';
// import 'package:flutter/foundation.dart';
part 'cantilever_scheme_data.freezed.dart';
part 'cantilever_scheme_data.g.dart';

@freezed
abstract class CantileverSchemeData with _$CantileverSchemeData {
  const CantileverSchemeData._();
  factory CantileverSchemeData({
    @Default('') String usage,
    @Default(150.0) double slabThickness,
    @Default(1.0) double span,
    @Default(1.0) double loadWidth,
    @Default(500.0) double strZone,
    @Default(45.0) double fcu,
    @Default(40.0) double cover,
    @Default(200.0) double mainWidth,
    @Default('') String mainTopBar,
    @Default('') String mainBottomBar,
    @Default('') String mainLinks,
    @Default('1') String id, //will be overriden  as soon as new instance created
    @Default('') String calsLog,
    @Default('') String beamForce,
  }) = _CantileverSchemeData; 


  factory CantileverSchemeData.fromJson(Map<String, Object?> json) =>
      _$CantileverSchemeDataFromJson(json);
}

