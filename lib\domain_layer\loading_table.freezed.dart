// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'loading_table.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$LoadingTable {

 String get usage; double get finish; double get service; double get liveLoad; String get loadingTableId;
/// Create a copy of LoadingTable
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$LoadingTableCopyWith<LoadingTable> get copyWith => _$LoadingTableCopyWithImpl<LoadingTable>(this as LoadingTable, _$identity);

  /// Serializes this LoadingTable to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is LoadingTable&&(identical(other.usage, usage) || other.usage == usage)&&(identical(other.finish, finish) || other.finish == finish)&&(identical(other.service, service) || other.service == service)&&(identical(other.liveLoad, liveLoad) || other.liveLoad == liveLoad)&&(identical(other.loadingTableId, loadingTableId) || other.loadingTableId == loadingTableId));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,usage,finish,service,liveLoad,loadingTableId);

@override
String toString() {
  return 'LoadingTable(usage: $usage, finish: $finish, service: $service, liveLoad: $liveLoad, loadingTableId: $loadingTableId)';
}


}

/// @nodoc
abstract mixin class $LoadingTableCopyWith<$Res>  {
  factory $LoadingTableCopyWith(LoadingTable value, $Res Function(LoadingTable) _then) = _$LoadingTableCopyWithImpl;
@useResult
$Res call({
 String usage, double finish, double service, double liveLoad, String loadingTableId
});




}
/// @nodoc
class _$LoadingTableCopyWithImpl<$Res>
    implements $LoadingTableCopyWith<$Res> {
  _$LoadingTableCopyWithImpl(this._self, this._then);

  final LoadingTable _self;
  final $Res Function(LoadingTable) _then;

/// Create a copy of LoadingTable
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? usage = null,Object? finish = null,Object? service = null,Object? liveLoad = null,Object? loadingTableId = null,}) {
  return _then(_self.copyWith(
usage: null == usage ? _self.usage : usage // ignore: cast_nullable_to_non_nullable
as String,finish: null == finish ? _self.finish : finish // ignore: cast_nullable_to_non_nullable
as double,service: null == service ? _self.service : service // ignore: cast_nullable_to_non_nullable
as double,liveLoad: null == liveLoad ? _self.liveLoad : liveLoad // ignore: cast_nullable_to_non_nullable
as double,loadingTableId: null == loadingTableId ? _self.loadingTableId : loadingTableId // ignore: cast_nullable_to_non_nullable
as String,
  ));
}

}


/// Adds pattern-matching-related methods to [LoadingTable].
extension LoadingTablePatterns on LoadingTable {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _LoadingTable value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _LoadingTable() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _LoadingTable value)  $default,){
final _that = this;
switch (_that) {
case _LoadingTable():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _LoadingTable value)?  $default,){
final _that = this;
switch (_that) {
case _LoadingTable() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String usage,  double finish,  double service,  double liveLoad,  String loadingTableId)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _LoadingTable() when $default != null:
return $default(_that.usage,_that.finish,_that.service,_that.liveLoad,_that.loadingTableId);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String usage,  double finish,  double service,  double liveLoad,  String loadingTableId)  $default,) {final _that = this;
switch (_that) {
case _LoadingTable():
return $default(_that.usage,_that.finish,_that.service,_that.liveLoad,_that.loadingTableId);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String usage,  double finish,  double service,  double liveLoad,  String loadingTableId)?  $default,) {final _that = this;
switch (_that) {
case _LoadingTable() when $default != null:
return $default(_that.usage,_that.finish,_that.service,_that.liveLoad,_that.loadingTableId);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _LoadingTable extends LoadingTable {
   _LoadingTable({this.usage = 'Usage', this.finish = 0.0, this.service = 0.0, this.liveLoad = 0.0, this.loadingTableId = ''}): super._();
  factory _LoadingTable.fromJson(Map<String, dynamic> json) => _$LoadingTableFromJson(json);

@override@JsonKey() final  String usage;
@override@JsonKey() final  double finish;
@override@JsonKey() final  double service;
@override@JsonKey() final  double liveLoad;
@override@JsonKey() final  String loadingTableId;

/// Create a copy of LoadingTable
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$LoadingTableCopyWith<_LoadingTable> get copyWith => __$LoadingTableCopyWithImpl<_LoadingTable>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$LoadingTableToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _LoadingTable&&(identical(other.usage, usage) || other.usage == usage)&&(identical(other.finish, finish) || other.finish == finish)&&(identical(other.service, service) || other.service == service)&&(identical(other.liveLoad, liveLoad) || other.liveLoad == liveLoad)&&(identical(other.loadingTableId, loadingTableId) || other.loadingTableId == loadingTableId));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,usage,finish,service,liveLoad,loadingTableId);

@override
String toString() {
  return 'LoadingTable(usage: $usage, finish: $finish, service: $service, liveLoad: $liveLoad, loadingTableId: $loadingTableId)';
}


}

/// @nodoc
abstract mixin class _$LoadingTableCopyWith<$Res> implements $LoadingTableCopyWith<$Res> {
  factory _$LoadingTableCopyWith(_LoadingTable value, $Res Function(_LoadingTable) _then) = __$LoadingTableCopyWithImpl;
@override @useResult
$Res call({
 String usage, double finish, double service, double liveLoad, String loadingTableId
});




}
/// @nodoc
class __$LoadingTableCopyWithImpl<$Res>
    implements _$LoadingTableCopyWith<$Res> {
  __$LoadingTableCopyWithImpl(this._self, this._then);

  final _LoadingTable _self;
  final $Res Function(_LoadingTable) _then;

/// Create a copy of LoadingTable
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? usage = null,Object? finish = null,Object? service = null,Object? liveLoad = null,Object? loadingTableId = null,}) {
  return _then(_LoadingTable(
usage: null == usage ? _self.usage : usage // ignore: cast_nullable_to_non_nullable
as String,finish: null == finish ? _self.finish : finish // ignore: cast_nullable_to_non_nullable
as double,service: null == service ? _self.service : service // ignore: cast_nullable_to_non_nullable
as double,liveLoad: null == liveLoad ? _self.liveLoad : liveLoad // ignore: cast_nullable_to_non_nullable
as double,loadingTableId: null == loadingTableId ? _self.loadingTableId : loadingTableId // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

// dart format on
