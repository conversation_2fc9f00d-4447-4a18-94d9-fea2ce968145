// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'pile_socketed_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_PileSocketedData _$PileSocketedDataFromJson(Map<String, dynamic> json) =>
    _PileSocketedData(
      isSelected: json['isSelected'] as bool? ?? false,
      pileSocketedSchemeId: json['pileSocketedSchemeId'] as String? ?? '1',
    );

Map<String, dynamic> _$PileSocketedDataToJson(_PileSocketedData instance) =>
    <String, dynamic>{
      'isSelected': instance.isSelected,
      'pileSocketedSchemeId': instance.pileSocketedSchemeId,
    };
