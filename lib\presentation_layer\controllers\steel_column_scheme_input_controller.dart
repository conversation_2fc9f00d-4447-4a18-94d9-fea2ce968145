import 'package:nanoid2/nanoid2.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

//presentation layer
import '../../domain_layer/steel_column_scheme_input.dart';
import '../screen/homescreen.dart';

// domain layer

part 'steel_column_scheme_input_controller.g.dart';

@riverpod
class SteelColumnSchemeInputController
    extends _$SteelColumnSchemeInputController {
  @override
  FutureOr<List<SteelColumnSchemeInput>> build() async {
    // print('Build: Column Scheme Input');
    final List<SteelColumnSchemeInput> steelColumnSchemeInputs =
        await ref
            .read(appDatabaseControllerProvider.notifier)
            .querySteelColumnSchemeInput();
    if (steelColumnSchemeInputs.isEmpty) {
      String newID = nanoid(alphabet: Alphabet.noDoppelgangerSafe);
      steelColumnSchemeInputs.add(
        SteelColumnSchemeInput(steelColumnSchemeInputId: newID),
      );
    }
    final data = ref.watch(loadingTablesControllerProvider);
    return data.when(
      data: (loadingTables) async {
        final usages = loadingTables.map((x) => x.usage).toList();
        final finalList =
            steelColumnSchemeInputs.map((item) {
              if (usages.contains(item.usage)) {
                return item;
              } else {
                return item.copyWith(usage: usages[0]);
              }
            }).toList();
        return finalList;
      },
      error: (error, stackTrace) => [],
      loading: () => [],
    );
  }

  Future<void> addEmptytable() async {
    final x = await future;
    final x1 = await ref.read(loadingTablesControllerProvider.future);
    final id = await _generateTaskID();
    state = AsyncData([
      ...x,
      SteelColumnSchemeInput(usage: x1.first.usage, steelColumnSchemeInputId: id),
    ]);
  }

  Future<void> deleteTable(String id) async {
    final x = await future;
    x.removeWhere((item) => item.steelColumnSchemeInputId == id);
    state = AsyncData(x);
  }

  Future<void> deleteAllTable() async {
    state = AsyncData([]);
  }

  // Future<void> insertEmptyTable(int index) async {
  //   final x = await future;
  //   final id = await _generateTaskID();
  //   x.insert(index+1, LoadingTable(loadingTableId: id));
  //   state = AsyncData(x);
  // }

  Future<void> copyAndInsertTable(int index) async {
    final x = await future;
    final id = await _generateTaskID();
    x.insert(index + 1, x[index].copyWith(steelColumnSchemeInputId: id));
    state = AsyncData(x);
  }

  Future<String> _generateTaskID() async {
    String newID = nanoid(alphabet: Alphabet.noDoppelgangerSafe);
    final x = await future;
    final Set<String> steelColumnSchemeInputIds =
        x.map((item) => item.steelColumnSchemeInputId).toSet();
    // final Set<String> taskIDs = ref.read(_taskIDsProvider);
    while (steelColumnSchemeInputIds.contains(newID)) {
      newID = nanoid(alphabet: Alphabet.noDoppelgangerSafe);
    }
    return newID;
  }

  Future<void> updateTable({
    String? usage,
    double? slabThickness,
    double? loadWidth,
    double? loadLength,
    int? nosOfFloor,
    required String steelColumnSchemeInputId,
  }) async {
    final x = await future;
    List<SteelColumnSchemeInput> finalList = [];
    late SteelColumnSchemeInput data;
    // Create a list of updates
    await Future.forEach((x), (x1) {
      if (x1.steelColumnSchemeInputId == steelColumnSchemeInputId) {
        data = x1.copyWith(
          usage: usage ?? x1.usage,
          slabThickness: slabThickness ?? x1.slabThickness,
          loadWidth: loadWidth ?? x1.loadWidth,
          loadLength: loadLength ?? x1.loadLength,
          nosOfFloor: nosOfFloor ?? x1.nosOfFloor,
          steelColumnSchemeInputId: steelColumnSchemeInputId,
        );
      } else {
        data = x1;
      }
      finalList.add(data);
    });
    // Update the state only if there are changes
    state = AsyncData(finalList);
  }
}
