import 'dart:math';

import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:structify/domain_layer/global_data.dart';

//presentation layer
import '../../domain_layer/data_struct/recommend_load.dart';
import '../../domain_layer/loading_table.dart';
import '../../misc/custom_func.dart';
import '../screen/homescreen.dart';

part 'recommended_load_controller.g.dart';

@riverpod
class RecommendedLoadController extends _$RecommendedLoadController {
  @override
  FutureOr<List<RecommendedLoad>> build() async {
    // print('Build: Beam Scheme Input');
    final data = ref.watch(globalDataControllerProvider);
    return data.when(
      data: (globalData) {
        return (_initializeRecommendedLoading(globalData));
      },
      error: (error, stackTrace) => <RecommendedLoad>[],
      loading: () => <RecommendedLoad>[],
    );
  }

  List<RecommendedLoad> _initializeRecommendedLoading(GlobalData globalData) {
    final List<RecommendedLoad> recommendedLoading = [];
    recommendedLoading.add(
      _createUsgae(globalData, 'Office', 50, 0.7, 1.0, 5, 1),
    );
    recommendedLoading.add(
      _createUsgae(globalData, 'Accessible Roof', 100, 0.3, 0.0, 2, 1),
    );
    recommendedLoading.add(
      _createUsgae(globalData, 'Inaccessible Roof', 100, 0.3, 0.0, 2, 1),
    );
    recommendedLoading.add(
      _createUsgae(globalData, 'Residential', 50, 0.3, 2.5, 2, 1),
    );
    recommendedLoading.add(
      _createUsgae(globalData, 'Hotel/Bedroom', 50, 0.6, 2.0, 2, 1),
    );
    recommendedLoading.add(
      _createUsgae(globalData, 'Reception', 50, 0.3, 1.5, 5, 1),
    );
    recommendedLoading.add(
      _createUsgae(globalData, 'Retail', 50, 0.3, 1.5, 5, 1),
    );
    recommendedLoading.add(
      _createUsgae(globalData, 'Carpark', 50, 0.6, 0.0, 3, 2),
    );
    recommendedLoading.add(
      _createUsgae(globalData, 'School', 50, 0.3, 2.5, 3, 2),
    );
    recommendedLoading.add(
      _createUsgae(globalData, 'Exhibition Hall', 50, 0.3, 1.5, 5, 2),
    );
    recommendedLoading.add(
      _createUsgae(globalData, 'Hospital', 50, 0.6, 1.0, 2.5, 2),
    );
    recommendedLoading.add(
      _createUsgae(globalData, 'Libraries', 50, 0.3, 1.0, 5, 2),
    );
    recommendedLoading.add(
      _createUsgae(globalData, 'Laboratories', 50, 1.0, 2.0, 3, 2),
    );
    recommendedLoading.add(
      _createUsgae(globalData, 'Chapels', 50, 0.3, 1.0, 4, 2),
    );
    recommendedLoading.add(
      _createUsgae(globalData, 'Gymnasia', 50, 0.6, 0.5, 5, 2),
    );
    recommendedLoading.add(
      _createUsgae(globalData, 'Plant Room', 50, 0.7, 0.0, 7.5, 2),
    );
    recommendedLoading.add(
      _createUsgae(globalData, 'Basement', 50, 0.6, 0.0, 3.0, 4),
    );
    recommendedLoading.add(
      _createUsgae(globalData, 'Bank', 50, 1.0, 1.5, 4, 4),
    );
    recommendedLoading.add(
      _createUsgae(globalData, 'Museum', 50, 1.0, 2.5, 5, 2),
    );
    recommendedLoading.add(
      _createUsgae(globalData, 'Refuge Floor', 100, 0.5, 0.0, 5.0, 2),
    );
    recommendedLoading.add(
      _createUsgae(globalData, 'Cinema', 50.0, 0.6, 0.0, 5, 2),
    );

    return recommendedLoading;
  }

  RecommendedLoad _createUsgae(
    GlobalData globalData,
    String usage,
    double finish,
    double service,
    double partitionLoad,
    double liveLoad,
    double frp,
  ) {
    return RecommendedLoad(
      usage: usage,
      finish: finish,
      service: service,
      partitionLoad: partitionLoad,
      sdl: globalData.finishUnitWeight * finish * pow(10, -3) + service,
      ll: liveLoad,
      frp: frp,
    );
  }

  Future<LoadingTable> toLoadingTable(String usage) async {
    final x = await future;
    final recommendedLoad = x.firstWhere((element) => element.usage == usage);
    return LoadingTable(
      usage: recommendedLoad.usage,
      finish: recommendedLoad.finish,
      service: roundTo(recommendedLoad.sdl, 1),
      liveLoad: recommendedLoad.ll,
      loadingTableId: 'TO BE FILLED',
    );
  }
}
