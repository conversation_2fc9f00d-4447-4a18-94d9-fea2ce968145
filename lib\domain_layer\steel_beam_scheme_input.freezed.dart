// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'steel_beam_scheme_input.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$SteelBeamSchemeInput {

 String get id; double get shortSpan; double get longSpan; int get bays; double get strZone; double get fsy; double get shortSpanIncreament; double get longSpanIncreament; int get baysIncreament; int get iterationSteps; String get usage; double get slabThickness; double get compositeActionFactor;
/// Create a copy of SteelBeamSchemeInput
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$SteelBeamSchemeInputCopyWith<SteelBeamSchemeInput> get copyWith => _$SteelBeamSchemeInputCopyWithImpl<SteelBeamSchemeInput>(this as SteelBeamSchemeInput, _$identity);

  /// Serializes this SteelBeamSchemeInput to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is SteelBeamSchemeInput&&(identical(other.id, id) || other.id == id)&&(identical(other.shortSpan, shortSpan) || other.shortSpan == shortSpan)&&(identical(other.longSpan, longSpan) || other.longSpan == longSpan)&&(identical(other.bays, bays) || other.bays == bays)&&(identical(other.strZone, strZone) || other.strZone == strZone)&&(identical(other.fsy, fsy) || other.fsy == fsy)&&(identical(other.shortSpanIncreament, shortSpanIncreament) || other.shortSpanIncreament == shortSpanIncreament)&&(identical(other.longSpanIncreament, longSpanIncreament) || other.longSpanIncreament == longSpanIncreament)&&(identical(other.baysIncreament, baysIncreament) || other.baysIncreament == baysIncreament)&&(identical(other.iterationSteps, iterationSteps) || other.iterationSteps == iterationSteps)&&(identical(other.usage, usage) || other.usage == usage)&&(identical(other.slabThickness, slabThickness) || other.slabThickness == slabThickness)&&(identical(other.compositeActionFactor, compositeActionFactor) || other.compositeActionFactor == compositeActionFactor));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,shortSpan,longSpan,bays,strZone,fsy,shortSpanIncreament,longSpanIncreament,baysIncreament,iterationSteps,usage,slabThickness,compositeActionFactor);

@override
String toString() {
  return 'SteelBeamSchemeInput(id: $id, shortSpan: $shortSpan, longSpan: $longSpan, bays: $bays, strZone: $strZone, fsy: $fsy, shortSpanIncreament: $shortSpanIncreament, longSpanIncreament: $longSpanIncreament, baysIncreament: $baysIncreament, iterationSteps: $iterationSteps, usage: $usage, slabThickness: $slabThickness, compositeActionFactor: $compositeActionFactor)';
}


}

/// @nodoc
abstract mixin class $SteelBeamSchemeInputCopyWith<$Res>  {
  factory $SteelBeamSchemeInputCopyWith(SteelBeamSchemeInput value, $Res Function(SteelBeamSchemeInput) _then) = _$SteelBeamSchemeInputCopyWithImpl;
@useResult
$Res call({
 String id, double shortSpan, double longSpan, int bays, double strZone, double fsy, double shortSpanIncreament, double longSpanIncreament, int baysIncreament, int iterationSteps, String usage, double slabThickness, double compositeActionFactor
});




}
/// @nodoc
class _$SteelBeamSchemeInputCopyWithImpl<$Res>
    implements $SteelBeamSchemeInputCopyWith<$Res> {
  _$SteelBeamSchemeInputCopyWithImpl(this._self, this._then);

  final SteelBeamSchemeInput _self;
  final $Res Function(SteelBeamSchemeInput) _then;

/// Create a copy of SteelBeamSchemeInput
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? shortSpan = null,Object? longSpan = null,Object? bays = null,Object? strZone = null,Object? fsy = null,Object? shortSpanIncreament = null,Object? longSpanIncreament = null,Object? baysIncreament = null,Object? iterationSteps = null,Object? usage = null,Object? slabThickness = null,Object? compositeActionFactor = null,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,shortSpan: null == shortSpan ? _self.shortSpan : shortSpan // ignore: cast_nullable_to_non_nullable
as double,longSpan: null == longSpan ? _self.longSpan : longSpan // ignore: cast_nullable_to_non_nullable
as double,bays: null == bays ? _self.bays : bays // ignore: cast_nullable_to_non_nullable
as int,strZone: null == strZone ? _self.strZone : strZone // ignore: cast_nullable_to_non_nullable
as double,fsy: null == fsy ? _self.fsy : fsy // ignore: cast_nullable_to_non_nullable
as double,shortSpanIncreament: null == shortSpanIncreament ? _self.shortSpanIncreament : shortSpanIncreament // ignore: cast_nullable_to_non_nullable
as double,longSpanIncreament: null == longSpanIncreament ? _self.longSpanIncreament : longSpanIncreament // ignore: cast_nullable_to_non_nullable
as double,baysIncreament: null == baysIncreament ? _self.baysIncreament : baysIncreament // ignore: cast_nullable_to_non_nullable
as int,iterationSteps: null == iterationSteps ? _self.iterationSteps : iterationSteps // ignore: cast_nullable_to_non_nullable
as int,usage: null == usage ? _self.usage : usage // ignore: cast_nullable_to_non_nullable
as String,slabThickness: null == slabThickness ? _self.slabThickness : slabThickness // ignore: cast_nullable_to_non_nullable
as double,compositeActionFactor: null == compositeActionFactor ? _self.compositeActionFactor : compositeActionFactor // ignore: cast_nullable_to_non_nullable
as double,
  ));
}

}


/// Adds pattern-matching-related methods to [SteelBeamSchemeInput].
extension SteelBeamSchemeInputPatterns on SteelBeamSchemeInput {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _SteelBeamSchemeInput value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _SteelBeamSchemeInput() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _SteelBeamSchemeInput value)  $default,){
final _that = this;
switch (_that) {
case _SteelBeamSchemeInput():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _SteelBeamSchemeInput value)?  $default,){
final _that = this;
switch (_that) {
case _SteelBeamSchemeInput() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  double shortSpan,  double longSpan,  int bays,  double strZone,  double fsy,  double shortSpanIncreament,  double longSpanIncreament,  int baysIncreament,  int iterationSteps,  String usage,  double slabThickness,  double compositeActionFactor)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _SteelBeamSchemeInput() when $default != null:
return $default(_that.id,_that.shortSpan,_that.longSpan,_that.bays,_that.strZone,_that.fsy,_that.shortSpanIncreament,_that.longSpanIncreament,_that.baysIncreament,_that.iterationSteps,_that.usage,_that.slabThickness,_that.compositeActionFactor);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  double shortSpan,  double longSpan,  int bays,  double strZone,  double fsy,  double shortSpanIncreament,  double longSpanIncreament,  int baysIncreament,  int iterationSteps,  String usage,  double slabThickness,  double compositeActionFactor)  $default,) {final _that = this;
switch (_that) {
case _SteelBeamSchemeInput():
return $default(_that.id,_that.shortSpan,_that.longSpan,_that.bays,_that.strZone,_that.fsy,_that.shortSpanIncreament,_that.longSpanIncreament,_that.baysIncreament,_that.iterationSteps,_that.usage,_that.slabThickness,_that.compositeActionFactor);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  double shortSpan,  double longSpan,  int bays,  double strZone,  double fsy,  double shortSpanIncreament,  double longSpanIncreament,  int baysIncreament,  int iterationSteps,  String usage,  double slabThickness,  double compositeActionFactor)?  $default,) {final _that = this;
switch (_that) {
case _SteelBeamSchemeInput() when $default != null:
return $default(_that.id,_that.shortSpan,_that.longSpan,_that.bays,_that.strZone,_that.fsy,_that.shortSpanIncreament,_that.longSpanIncreament,_that.baysIncreament,_that.iterationSteps,_that.usage,_that.slabThickness,_that.compositeActionFactor);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _SteelBeamSchemeInput extends SteelBeamSchemeInput {
   _SteelBeamSchemeInput({this.id = '1', this.shortSpan = 6.0, this.longSpan = 10.0, this.bays = 2, this.strZone = 500.0, this.fsy = 355.0, this.shortSpanIncreament = 1.0, this.longSpanIncreament = 1.0, this.baysIncreament = 1, this.iterationSteps = 10, this.usage = '', this.slabThickness = 130.0, this.compositeActionFactor = 1.0}): super._();
  factory _SteelBeamSchemeInput.fromJson(Map<String, dynamic> json) => _$SteelBeamSchemeInputFromJson(json);

@override@JsonKey() final  String id;
@override@JsonKey() final  double shortSpan;
@override@JsonKey() final  double longSpan;
@override@JsonKey() final  int bays;
@override@JsonKey() final  double strZone;
@override@JsonKey() final  double fsy;
@override@JsonKey() final  double shortSpanIncreament;
@override@JsonKey() final  double longSpanIncreament;
@override@JsonKey() final  int baysIncreament;
@override@JsonKey() final  int iterationSteps;
@override@JsonKey() final  String usage;
@override@JsonKey() final  double slabThickness;
@override@JsonKey() final  double compositeActionFactor;

/// Create a copy of SteelBeamSchemeInput
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$SteelBeamSchemeInputCopyWith<_SteelBeamSchemeInput> get copyWith => __$SteelBeamSchemeInputCopyWithImpl<_SteelBeamSchemeInput>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$SteelBeamSchemeInputToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _SteelBeamSchemeInput&&(identical(other.id, id) || other.id == id)&&(identical(other.shortSpan, shortSpan) || other.shortSpan == shortSpan)&&(identical(other.longSpan, longSpan) || other.longSpan == longSpan)&&(identical(other.bays, bays) || other.bays == bays)&&(identical(other.strZone, strZone) || other.strZone == strZone)&&(identical(other.fsy, fsy) || other.fsy == fsy)&&(identical(other.shortSpanIncreament, shortSpanIncreament) || other.shortSpanIncreament == shortSpanIncreament)&&(identical(other.longSpanIncreament, longSpanIncreament) || other.longSpanIncreament == longSpanIncreament)&&(identical(other.baysIncreament, baysIncreament) || other.baysIncreament == baysIncreament)&&(identical(other.iterationSteps, iterationSteps) || other.iterationSteps == iterationSteps)&&(identical(other.usage, usage) || other.usage == usage)&&(identical(other.slabThickness, slabThickness) || other.slabThickness == slabThickness)&&(identical(other.compositeActionFactor, compositeActionFactor) || other.compositeActionFactor == compositeActionFactor));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,shortSpan,longSpan,bays,strZone,fsy,shortSpanIncreament,longSpanIncreament,baysIncreament,iterationSteps,usage,slabThickness,compositeActionFactor);

@override
String toString() {
  return 'SteelBeamSchemeInput(id: $id, shortSpan: $shortSpan, longSpan: $longSpan, bays: $bays, strZone: $strZone, fsy: $fsy, shortSpanIncreament: $shortSpanIncreament, longSpanIncreament: $longSpanIncreament, baysIncreament: $baysIncreament, iterationSteps: $iterationSteps, usage: $usage, slabThickness: $slabThickness, compositeActionFactor: $compositeActionFactor)';
}


}

/// @nodoc
abstract mixin class _$SteelBeamSchemeInputCopyWith<$Res> implements $SteelBeamSchemeInputCopyWith<$Res> {
  factory _$SteelBeamSchemeInputCopyWith(_SteelBeamSchemeInput value, $Res Function(_SteelBeamSchemeInput) _then) = __$SteelBeamSchemeInputCopyWithImpl;
@override @useResult
$Res call({
 String id, double shortSpan, double longSpan, int bays, double strZone, double fsy, double shortSpanIncreament, double longSpanIncreament, int baysIncreament, int iterationSteps, String usage, double slabThickness, double compositeActionFactor
});




}
/// @nodoc
class __$SteelBeamSchemeInputCopyWithImpl<$Res>
    implements _$SteelBeamSchemeInputCopyWith<$Res> {
  __$SteelBeamSchemeInputCopyWithImpl(this._self, this._then);

  final _SteelBeamSchemeInput _self;
  final $Res Function(_SteelBeamSchemeInput) _then;

/// Create a copy of SteelBeamSchemeInput
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? shortSpan = null,Object? longSpan = null,Object? bays = null,Object? strZone = null,Object? fsy = null,Object? shortSpanIncreament = null,Object? longSpanIncreament = null,Object? baysIncreament = null,Object? iterationSteps = null,Object? usage = null,Object? slabThickness = null,Object? compositeActionFactor = null,}) {
  return _then(_SteelBeamSchemeInput(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,shortSpan: null == shortSpan ? _self.shortSpan : shortSpan // ignore: cast_nullable_to_non_nullable
as double,longSpan: null == longSpan ? _self.longSpan : longSpan // ignore: cast_nullable_to_non_nullable
as double,bays: null == bays ? _self.bays : bays // ignore: cast_nullable_to_non_nullable
as int,strZone: null == strZone ? _self.strZone : strZone // ignore: cast_nullable_to_non_nullable
as double,fsy: null == fsy ? _self.fsy : fsy // ignore: cast_nullable_to_non_nullable
as double,shortSpanIncreament: null == shortSpanIncreament ? _self.shortSpanIncreament : shortSpanIncreament // ignore: cast_nullable_to_non_nullable
as double,longSpanIncreament: null == longSpanIncreament ? _self.longSpanIncreament : longSpanIncreament // ignore: cast_nullable_to_non_nullable
as double,baysIncreament: null == baysIncreament ? _self.baysIncreament : baysIncreament // ignore: cast_nullable_to_non_nullable
as int,iterationSteps: null == iterationSteps ? _self.iterationSteps : iterationSteps // ignore: cast_nullable_to_non_nullable
as int,usage: null == usage ? _self.usage : usage // ignore: cast_nullable_to_non_nullable
as String,slabThickness: null == slabThickness ? _self.slabThickness : slabThickness // ignore: cast_nullable_to_non_nullable
as double,compositeActionFactor: null == compositeActionFactor ? _self.compositeActionFactor : compositeActionFactor // ignore: cast_nullable_to_non_nullable
as double,
  ));
}


}

// dart format on
