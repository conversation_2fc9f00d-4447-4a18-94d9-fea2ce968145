import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:structify/domain_layer/mixin/mixin_tools_for_ui.dart';
import 'package:structify/domain_layer/preferences.dart';

//presentation layer
import '../../domain_layer/column_scheme_data.dart';
import '../../misc/show_overlay_msg.dart';
import 'button/function_button.dart';
import 'input/custom_stateful_double_input.dart';
import '../screen/homescreen.dart';
import 'button/selection_button.dart';

class PileFrictionalBoredInputUi extends ConsumerStatefulWidget {
  const PileFrictionalBoredInputUi({
    this.isExpanded,
    this.maxHeight,
    this.minHeight,
    super.key,
  });

  final bool? isExpanded;
  final double? maxHeight;
  final double? minHeight;

  @override
  ConsumerState<ConsumerStatefulWidget> createState() =>
      _PileFrictionalBoredInputUiState();
}

class _PileFrictionalBoredInputUiState
    extends ConsumerState<PileFrictionalBoredInputUi>
    with WidgetsBindingObserver, MixinToolsForUI {
  late bool _isExpanded;
  late double _maxHeight;
  late double _minHeight;

  late GlobalKey _buttonKey;

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void initState() {
    WidgetsBinding.instance.addObserver(this);
    _isExpanded = widget.isExpanded ?? false;
    _maxHeight = widget.maxHeight ?? 210;
    _minHeight = widget.minHeight ?? 50;
    _buttonKey = GlobalKey();
    // _updateHeights();

    super.initState();
  }

  @override
  void didChangeDependencies() {
    _updateHeights();
    super.didChangeDependencies();
    // Update heights when dependencies change, including after initState
  }

  @override
  void didChangeMetrics() {
    // This method is called when the metrics change (like resizing the window)
    setState(() {
      _updateHeights();
    });
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    ref.watch(appWatcherProvider);
    final globalData = ref.watch(globalDataControllerProvider);
    final pileFrictionalBoredInput = ref.watch(
      pileFrictionalBoredInputControllerProvider,
    );
    final pileFrictionalBoredInputGlobal = ref.watch(
      pileFrictionalBoredInputGlobalControllerProvider,
    );
    final int containerOpacity = 175;
    final int textOpacity = 225;
    return globalData.when(
      data: (data) {
        return pileFrictionalBoredInput.when(
          data: (input) {
            return pileFrictionalBoredInputGlobal.when(
              data: (inputGlobal) {
                late final List<String> unit;
                switch (data.unit) {
                  case 'metrics':
                    unit = PreferredUnit.metrics;
                    break;
                  case 'imperial':
                    unit = PreferredUnit.imperial;
                    break;
                  default:
                    unit = PreferredUnit.metrics;
                    break;
                }

                return Column(
                  children: [
                    Padding(
                      padding: const EdgeInsets.fromLTRB(10, 0, 10, 0),
                      child: Divider(),
                    ),
                    Padding(
                      padding: const EdgeInsets.fromLTRB(10, 0, 0, 0),
                      child: Align(
                        alignment: Alignment.centerLeft,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Wrap(
                              crossAxisAlignment: WrapCrossAlignment.center,
                              children: [
                                IconButton(
                                  icon: Icon(
                                    _isExpanded
                                        ? Icons.expand_less
                                        : Icons.expand_more,
                                  ),
                                  color: colorScheme.onSurface,
                                  onPressed: () {
                                    setState(() {
                                      _isExpanded = !_isExpanded;
                                    });
                                  },
                                ),
                                Text(
                                  'Frictional Bored Pile Scheme Input',
                                  style: textTheme.titleLarge!.copyWith(
                                    color: colorScheme.onSurface,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.fromLTRB(10, 0, 10, 0),
                      child: Divider(),
                    ),
                    ClipRect(
                      child: ConstrainedBox(
                        constraints: BoxConstraints(
                          maxHeight: _isExpanded ? _maxHeight : 0,
                        ),
                        child: SingleChildScrollView(
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Padding(
                                padding: const EdgeInsets.fromLTRB(
                                  8.0,
                                  0.0,
                                  8.0,
                                  0.0,
                                ),
                                child: Container(
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(10.0),
                                    border: Border.all(
                                      color: Colors.grey.withAlpha(150),
                                    ),
                                    color: colorScheme.secondaryContainer
                                        .withAlpha(containerOpacity),
                                  ),
                                  child: Padding(
                                    padding: const EdgeInsets.all(4.0),
                                    child: Text(
                                      'Pile Data',
                                      style: textTheme.titleSmall!.copyWith(
                                        color: colorScheme.onSecondaryContainer
                                            .withAlpha(textOpacity),
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                              Padding(
                                padding: const EdgeInsets.fromLTRB(
                                  8.0,
                                  8.0,
                                  8.0,
                                  4.0,
                                ),
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  mainAxisAlignment: MainAxisAlignment.start,
                                  children: [
                                    Flexible(
                                      child: CustomStatefulDoubleInput(
                                        title: 'SPT N-Value',
                                        value: input.sptNValue,
                                        maxValue: 200,
                                        tooltipText:
                                            'Good Practice: Limited to 200',
                                        onChanged: (value) async {
                                          await ref
                                              .read(
                                                pileFrictionalBoredInputControllerProvider
                                                    .notifier,
                                              )
                                              .updateTable(sptNValue: value);
                                        },
                                      ),
                                    ),
                                    SizedBox(width: 5.0),
                                    Flexible(
                                      child: CustomStatefulDoubleInput(
                                        readOnly: true,
                                        title:
                                            'Soil Unit Weight [${unit[0]}/${unit[3]}3]',
                                            tooltipText: 'Defined in Global Data',
                                        value: input.soilUnitWeight,
                                        onChanged: (value) async {
                                          await ref
                                              .read(
                                                pileFrictionalBoredInputControllerProvider
                                                    .notifier,
                                              )
                                              .updateTable(
                                                soilUnitWeight: value,
                                              );
                                        },
                                      ),
                                    ),
                                    SizedBox(width: 5.0),
                                    Flexible(
                                      child: CustomStatefulDoubleInput(
                                        title: 'kTan',
                                        value: input.kTan,
                                        onChanged: (value) async {
                                          await ref
                                              .read(
                                                pileFrictionalBoredInputControllerProvider
                                                    .notifier,
                                              )
                                              .updateTable(kTan: value);
                                        },
                                      ),
                                    ),
                                    SizedBox(width: 5.0),
                                    Flexible(
                                      child: CustomStatefulDoubleInput(
                                        title: 'FOS',
                                        value: input.fos,
                                        onChanged: (value) async {
                                          await ref
                                              .read(
                                                pileFrictionalBoredInputControllerProvider
                                                    .notifier,
                                              )
                                              .updateTable(fos: value);
                                        },
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              SizedBox(height: 5.0),
                              Padding(
                                padding: const EdgeInsets.fromLTRB(
                                  8.0,
                                  4.0,
                                  8.0,
                                  4.0,
                                ),
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  mainAxisAlignment: MainAxisAlignment.start,
                                  children: [
                                    Flexible(
                                      child: CustomStatefulDoubleInput(
                                        title: 'fcu [${unit[5]}]',
                                        value: input.fcu,
                                        onChanged: (value) async {
                                          await ref
                                              .read(
                                                pileFrictionalBoredInputControllerProvider
                                                    .notifier,
                                              )
                                              .updateTable(fcu: value);
                                        },
                                      ),
                                    ),
                                    SizedBox(width: 5.0),
                                    Flexible(
                                      child: CustomStatefulDoubleInput(
                                        title: 'Max Pile Length [${unit[3]}]',
                                        value: input.maxPileLength,
                                        onChanged: (value) async {
                                          await ref
                                              .read(
                                                pileFrictionalBoredInputControllerProvider
                                                    .notifier,
                                              )
                                              .updateTable(
                                                maxPileLength: value,
                                              );
                                        },
                                      ),
                                    ),
                                    SizedBox(width: 5.0),
                                    Flexible(
                                      child: CustomStatefulDoubleInput(
                                        title: 'Max Pile Diameter [${unit[4]}]',
                                        value: input.maxPileDiameter,
                                        onChanged: (value) async {
                                          await ref
                                              .read(
                                                pileFrictionalBoredInputControllerProvider
                                                    .notifier,
                                              )
                                              .updateTable(
                                                maxPileDiameter: value,
                                              );
                                        },
                                      ),
                                    ),
                                    SizedBox(width: 5.0),
                                    Flexible(
                                      child: CustomStatefulDoubleInput(
                                        title: 'Max Steel Ratio',
                                        value: input.maxSteelRatio,
                                        onChanged: (value) async {
                                          await ref
                                              .read(
                                                pileFrictionalBoredInputControllerProvider
                                                    .notifier,
                                              )
                                              .updateTable(
                                                maxSteelRatio: value,
                                              );
                                        },
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              Padding(
                                padding: const EdgeInsets.fromLTRB(
                                  8.0,
                                  4.0,
                                  8.0,
                                  4.0,
                                ),
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  mainAxisAlignment: MainAxisAlignment.start,
                                  children: [
                                    Flexible(
                                      child: CustomStatefulDoubleInput(
                                        title: 'Ratio of Bellout to Shaft',
                                        tooltipText:
                                            'Ratio of Bellout Diameter to Shaft Diameter.\nHK Code Limit: 1.65',
                                        value: input.ratioOfBelloutDia,
                                        onChanged: (value) async {
                                          await ref
                                              .read(
                                                pileFrictionalBoredInputControllerProvider
                                                    .notifier,
                                              )
                                              .updateTable(
                                                ratioOfBelloutDia: value,
                                              );
                                        },
                                      ),
                                    ),
                                    SizedBox(width: 5.0),
                                    Flexible(
                                      child: CustomStatefulDoubleInput(
                                        readOnly: input.useSelectColLoad,
                                        title: 'SLS Load [${unit[0]}]',
                                        value: input.slsLoad,
                                        tooltipText:
                                            'load per pile, not total Load',
                                        onChanged: (value) async {
                                          await ref
                                              .read(
                                                pileFrictionalBoredInputControllerProvider
                                                    .notifier,
                                              )
                                              .updateTable(slsLoad: value);
                                        },
                                      ),
                                    ),
                                    SizedBox(width: 5.0),
                                    Flexible(
                                      child: CustomStatefulDoubleInput(
                                        readOnly: input.useSelectColLoad,
                                        title: 'ULS Load [${unit[0]}]',
                                        tooltipText:
                                            'load per pile, not total Load',
                                        value: input.ulsLoad,
                                        onChanged: (value) async {
                                          await ref
                                              .read(
                                                pileFrictionalBoredInputControllerProvider
                                                    .notifier,
                                              )
                                              .updateTable(ulsLoad: value);
                                        },
                                      ),
                                    ),
                                    Flexible(child: SizedBox(width: 5.0)),
                                  ],
                                ),
                              ),
                              Padding(
                                padding: const EdgeInsets.fromLTRB(
                                  8.0,
                                  4.0,
                                  8.0,
                                  4.0,
                                ),
                                child: Container(
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(10.0),
                                    border: Border.all(
                                      color: Colors.grey.withAlpha(150),
                                    ),
                                    color: colorScheme.secondaryContainer
                                        .withAlpha(containerOpacity),
                                  ),
                                  child: Padding(
                                    padding: const EdgeInsets.all(4.0),
                                    child: Text(
                                      'Iteration Data',
                                      style: textTheme.titleSmall!.copyWith(
                                        color: colorScheme.onSecondaryContainer
                                            .withAlpha(textOpacity),
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                              Padding(
                                padding: const EdgeInsets.fromLTRB(
                                  8.0,
                                  4.0,
                                  8.0,
                                  4.0,
                                ),
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  mainAxisAlignment: MainAxisAlignment.start,
                                  children: [
                                    Flexible(
                                      child: CustomStatefulDoubleInput(
                                        title: 'Diameter Delta [${unit[4]}]',
                                        tooltipText:
                                            'Increament of Pile Diameter for each iteration',
                                        value: input.diaIncrement,
                                        onChanged: (value) async {
                                          await ref
                                              .read(
                                                pileFrictionalBoredInputControllerProvider
                                                    .notifier,
                                              )
                                              .updateTable(diaIncrement: value);
                                        },
                                      ),
                                    ),
                                    Flexible(child: SizedBox(width: 5.0)),
                                    Flexible(child: SizedBox(width: 5.0)),
                                    Flexible(child: SizedBox(width: 5.0)),
                                  ],
                                ),
                              ),

                              Padding(
                                padding: const EdgeInsets.fromLTRB(
                                  8.0,
                                  4.0,
                                  8.0,
                                  4.0,
                                ),
                                child: Row(
                                  children: [
                                    SelectionButton(
                                      labelTextStyle:
                                          input.useSelectColLoad
                                              ? textTheme.labelLarge!.copyWith(
                                                color:
                                                    colorScheme
                                                        .onTertiaryContainer,
                                              )
                                              : textTheme.labelLarge!.copyWith(
                                                color: colorScheme.onSurface,
                                              ),
                                      labelText:
                                          'Use Load from\nSelected Column',
                                      pressedColor:
                                          colorScheme.tertiaryContainer,
                                      bgColor:
                                          input.useSelectColLoad
                                              ? colorScheme.tertiaryContainer
                                              : colorScheme.surfaceContainer,

                                      onTap: (value) async {
                                        final input = await ref.read(
                                          pileFrictionalBoredInputControllerProvider
                                              .future,
                                        );
                                        final colData = await ref.read(
                                          columnSchemeDataControllerProvider
                                              .future,
                                        );

                                        await ref
                                            .read(
                                              pileFrictionalBoredInputControllerProvider
                                                  .notifier,
                                            )
                                            .updateTable(
                                              useSelectColLoad:
                                                  !input.useSelectColLoad,
                                            );
                                        //* Tricky here: if the new toggled value (!input.useSlabSelected) is true,
                                        //* we run below logic
                                        if (!input.useSelectColLoad) {
                                          final selectedColumn = colData
                                              .firstWhere(
                                                (scheme) => scheme.isSelected,
                                                orElse:
                                                    () => ColumnSchemeData(),
                                              );
                                          await ref
                                              .read(
                                                pileFrictionalBoredInputControllerProvider
                                                    .notifier,
                                              )
                                              .updateTable(
                                                slsLoad:
                                                    selectedColumn.slsLoad /
                                                    inputGlobal.colLoadFactor,
                                                ulsLoad:
                                                    selectedColumn.ulsLoad /
                                                    inputGlobal.colLoadFactor,
                                              );
                                        }
                                      },
                                    ),
                                    SizedBox(width: 5.0),
                                    Flexible(
                                      child: CustomStatefulDoubleInput(
                                        readOnly: !input.useSelectColLoad,
                                        title: 'Column Load Dividor ',
                                        tooltipText:
                                            'Factor to adjust the col load\n(and hence the load per pile)',
                                        value: inputGlobal.colLoadFactor,
                                        listener: (hasFocus, value) async {
                                          if (!hasFocus) {
                                            await ref
                                                .read(
                                                  pileFrictionalBoredInputGlobalControllerProvider
                                                      .notifier,
                                                )
                                                .updateTable(
                                                  colLoadFactor: value,
                                                );
                                          }
                                        },
                                      ),
                                    ),
                                    SizedBox(width: 5.0),
                                    Flexible(
                                      child: FunctionButton(
                                        key: _buttonKey,
                                        labelIcon: Icon(
                                          Icons.calculate_outlined,
                                        ),
                                        labelText: 'Run',
                                        onTap: (isPressed) async {
                                          final pileFrictionalBoredSchemeDataList =
                                              await ref
                                                  .read(
                                                    pileFrictionalBoredDataControllerProvider
                                                        .notifier,
                                                  )
                                                  .pileFrictionalBoredScheming();
                                          if (pileFrictionalBoredSchemeDataList
                                              .isEmpty) {
                                            showGentleMessageBox(
                                              context,
                                              _buttonKey,
                                              'No scheme Available!',
                                              containerColor: colorScheme
                                                  .errorContainer
                                                  .withAlpha(175),
                                              textStyle: textTheme.labelMedium!
                                                  .copyWith(
                                                    color: colorScheme
                                                        .onErrorContainer
                                                        .withAlpha(175),
                                                  ),
                                            );
                                          }
                                        },
                                      ),
                                    ),
                                    SizedBox(width: 5.0),
                                    Flexible(
                                      child: FunctionButton(
                                        labelIcon: Icon(Icons.delete_outlined),
                                        labelText: 'Clear Schemes',
                                        onTap: (isPressed) async {
                                          await ref
                                              .read(
                                                pileFrictionalBoredDataControllerProvider
                                                    .notifier,
                                              )
                                              .deleteAllTable();
                                        },
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ],
                );
              },
              error: (error, loading) {
                return Text('Error: $error');
              },
              loading: () {
                return Text('Loading...');
              },
            );
          },
          error: (error, loading) {
            return Text('Error: $error');
          },
          loading: () {
            return Text('Loading...');
          },
        );
      },
      error: (error, loading) {
        return Text('Error: $error');
      },
      loading: () {
        return Text('Loading...');
      },
    );
  }

  void _updateHeights() {
    final double screenHeight = MediaQuery.of(context).size.height;
    _maxHeight =
        (widget.maxHeight == null)
            ? screenHeight * 0.6
            : (widget.maxHeight! > screenHeight * 0.6)
            ? screenHeight * 0.6
            : widget.maxHeight!;

    _minHeight =
        (widget.minHeight == null)
            ? screenHeight * 0.1
            : (widget.minHeight! > screenHeight * 0.1)
            ? screenHeight * 0.1
            : widget.minHeight!;

    // Adjust _maxHeight if needed

    if (_maxHeight < _minHeight) {
      _maxHeight = _minHeight;
    }
  }
}
