import 'package:nanoid2/nanoid2.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

//presentation layer
import '../../domain_layer/steel_cantilever_truss_scheme_input.dart';
import '../../domain_layer/steel_transfer_truss_scheme_input.dart';
import '../screen/homescreen.dart';

// domain layer

part 'steel_cantilever_truss_scheme_input_controller.g.dart';

@riverpod
class SteelCantileverTrussSchemeInputController
    extends _$SteelCantileverTrussSchemeInputController {
  @override
  FutureOr<List<SteelCantileverTrussSchemeInput>> build() async {
    // print('Build: Transfer Beam Scheme Input');
    final List<SteelCantileverTrussSchemeInput>
    steelCantileverTrussSchemeInputs =
        await ref
            .read(appDatabaseControllerProvider.notifier)
            .querySteelCantileverTrussSchemeInput();
    return steelCantileverTrussSchemeInputs;
  }

  Future<void> addEmptytable() async {
    final x = await future;
    final id = await _generateTaskID();
    state = AsyncData([
      ...x,
      SteelCantileverTrussSchemeInput(steelCantileverTrussSchemeInputId: id),
    ]);
  }

  Future<void> deleteTable(String id) async {
    final x = await future;
    x.removeWhere((item) => item.steelCantileverTrussSchemeInputId == id);
    state = AsyncData(x);
  }

  Future<void> deleteAllTable() async {
    state = AsyncData(<SteelCantileverTrussSchemeInput>[]);
  }

  Future<void> insertEmptyTable(int index) async {
    final x = await future;
    final id = await _generateTaskID();
    x.insert(
      index + 1,
      SteelCantileverTrussSchemeInput(steelCantileverTrussSchemeInputId: id),
    );
    state = AsyncData(x);
  }

  Future<void> copyAndInsertTable(int index) async {
    final x = await future;
    final id = await _generateTaskID();
    x.insert(
      index + 1,
      x[index].copyWith(steelCantileverTrussSchemeInputId: id),
    );
    state = AsyncData(x);
  }

  Future<String> _generateTaskID() async {
    String newID = nanoid(alphabet: Alphabet.noDoppelgangerSafe);
    final x = await future;
    final Set<String> steelCantileverTrussSchemeInputIds =
        x.map((item) => item.steelCantileverTrussSchemeInputId).toSet();
    // final Set<String> taskIDs = ref.read(_taskIDsProvider);
    while (steelCantileverTrussSchemeInputIds.contains(newID)) {
      newID = nanoid(alphabet: Alphabet.noDoppelgangerSafe);
    }
    return newID;
  }

  Future<void> updateTable({
    double? pointLoad,
    double? distA,
    required String steelCantileverTrussSchemeInputId,
  }) async {
    final x = await future;
    List<SteelCantileverTrussSchemeInput> finalList = [];
    late SteelCantileverTrussSchemeInput data;
    // Create a list of updates
    await Future.forEach((x), (x1) {
      if (x1.steelCantileverTrussSchemeInputId ==
          steelCantileverTrussSchemeInputId) {
        data = x1.copyWith(
          pointLoad: pointLoad ?? x1.pointLoad,
          distA: distA ?? x1.distA,
          steelCantileverTrussSchemeInputId: steelCantileverTrussSchemeInputId,
        );
      } else {
        data = x1;
      }
      finalList.add(data);
    });
    // Update the state only if there are changes
    state = AsyncData(finalList);
  }
}
