// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'pile_end_bearing_bored_data_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$pileEndBearingBoredDataControllerHash() =>
    r'863894609b4c87374bc4ada4327fc7f0f5c3c545';

/// See also [PileEndBearingBoredDataController].
@ProviderFor(PileEndBearingBoredDataController)
final pileEndBearingBoredDataControllerProvider =
    AutoDisposeAsyncNotifierProvider<
      PileEndBearingBoredDataController,
      List<PileEndBearingBoredData>
    >.internal(
      PileEndBearingBoredDataController.new,
      name: r'pileEndBearingBoredDataControllerProvider',
      debugGetCreateSourceHash:
          const bool.fromEnvironment('dart.vm.product')
              ? null
              : _$pileEndBearingBoredDataControllerHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$PileEndBearingBoredDataController =
    AutoDisposeAsyncNotifier<List<PileEndBearingBoredData>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
