import 'dart:math' as math;
import 'dart:math';
import 'dart:typed_data';
import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:structify/domain_layer/pile_socketed_data.dart';
import 'package:structify/presentation_layer/screen/homescreen.dart';
import 'package:vector_math/vector_math.dart' as vec;

import '../../../domain_layer/global_data.dart';
import '../../../domain_layer/mixin/mixin_steel_str.dart';
import '../../../domain_layer/preferences.dart';

class DrawPileSocketedScheme extends ConsumerStatefulWidget {
  DrawPileSocketedScheme({
    required this.sketchWidth,
    required this.sketchHeight,
    this.fontSize,
    super.key,
  });

  final double sketchWidth;
  final double sketchHeight;
  double? fontSize;

  @override
  ConsumerState<ConsumerStatefulWidget> createState() =>
      _DrawPileSocketedSchemeState();
}

class _DrawPileSocketedSchemeState
    extends ConsumerState<DrawPileSocketedScheme>  with SteelStrHK{
  late final double sketchWidth;
  late final double _sketchHeight;
  double? _fontSize;
  // late final List<Map<String, dynamic>> _steelSections;

  @override
  void initState() {
    super.initState();
    sketchWidth = widget.sketchWidth;
    _sketchHeight = widget.sketchHeight;
    _fontSize = widget.fontSize;
  }

  @override
  Widget build(BuildContext context) {
    final data1 = ref.watch(pileSocketedDataControllerProvider);
    final data2 = ref.watch(globalDataControllerProvider);
    final ColorScheme colorScheme = Theme.of(context).colorScheme;
    final TextTheme textTheme = Theme.of(context).textTheme;

    _fontSize = _fontSize ?? math.min(sketchWidth, _sketchHeight) / 15;
    return data1.when(
      data: (pileSocketedSchemedata) {
        return data2.when(
          data: (globalData) {
            final constraints = BoxConstraints(
              maxWidth: sketchWidth,
              maxHeight: _sketchHeight,
              minHeight: 100,
              minWidth: 100,
            );
            return Center(
              child: Container(
                decoration: BoxDecoration(
                  color: colorScheme.surfaceContainer.withAlpha(100),
                  borderRadius: BorderRadius.circular(10.0),
                  border: Border.all(color: Colors.black.withAlpha(100)),
                ),
                width:
                    constraints.maxWidth == double.infinity
                        ? 100
                        : constraints.maxWidth,
                height:
                    constraints.maxHeight == double.infinity
                        ? 100
                        : constraints.maxHeight,
                child: FutureBuilder(
                  future: getAllSteelISection(sectionShape: 'UBP'),
                  builder: (context, snapshot) {
                    if (snapshot.hasData) {
                      return CustomPaint(
                        painter: DrawPileSocketedSchemePainter(
                          pileSocketedSchemedata: pileSocketedSchemedata,
                          globalData: globalData,
                          boxConstraints: constraints,
                          steelSections: snapshot.data!,
                          fontSize: _fontSize,
                          context: context,
                        ),
                      );
                    } else {
                      return Transform.scale(
                        scale: 0.25,
                        child: const CircularProgressIndicator(),
                      );
                    }
                  },
                ),
              ),
            );
          },

          error: (error, stackTrace) {
            return Text(error.toString());
          },
          loading: () {
            return const CircularProgressIndicator();
          },
        );
      },
      error: (error, stackTrace) {
        return Text(error.toString());
      },
      loading: () {
        return const CircularProgressIndicator();
      },
    );
  }
}

class DrawPileSocketedSchemePainter extends CustomPainter {
  final PileSocketedData pileSocketedSchemedata;
  final GlobalData globalData;
  final BoxConstraints boxConstraints;
  final List<Map<String, dynamic>> steelSections;
  double? fontSize;
  final BuildContext context;

  DrawPileSocketedSchemePainter({
    required this.pileSocketedSchemedata,
    required this.globalData,
    required this.boxConstraints,
    required this.steelSections,
    this.fontSize,
    required this.context,
  });

  @override
  void paint(Canvas canvas, Size size) async {
    final ColorScheme colorScheme = Theme.of(context).colorScheme;
    final TextTheme textTheme = Theme.of(context).textTheme;
    late final double secBeamSpacing;

    fontSize = fontSize ?? math.min(size.width, size.height) / 40;

    Paint sectionPaint =
        Paint()
          ..color = colorScheme.onSurface
          ..strokeCap = StrokeCap.round
          ..style = PaintingStyle.stroke
          ..strokeWidth = 1.0;

    final double minDim = math.min(
      boxConstraints.maxWidth,
      boxConstraints.maxHeight,
    );

    //* get the section dimensions
    final Map<String, dynamic> section = steelSections.firstWhere(
      (item) => item['name'] == 'UBP305X305X223',
    );

    final double ratio =
        minDim /
        (max(610, max(double.tryParse(section['d'])!, double.tryParse(section['b'])!) *
            10)) *
        4 /
        5;

    late Offset startP1, endP1, startP2, endP2, startP3, endP3;
    // Path path = Path();
    // ******************
    //* Concrete Section
    // ******************
    startP1 = Offset(
      0.5 * (boxConstraints.maxWidth),
      0.5 * (boxConstraints.maxHeight),
    );

    canvas.drawCircle(startP1, 610/2 * ratio , sectionPaint);
_drawDimLine(
      canvas,
      fontSize!,
      startP1 -
          Offset(
            610 * ratio / 2,
            610 * ratio / 2,
          ),
      startP1 +
          Offset(
            610 * ratio / 2,
            -610 * ratio / 2,
          ),
      6,
      fontSize! / 2,
      610,
      globalData,
      context,
    );
    
    // ******************
    //* Steel Section
    // ******************
    // start from upper left corner
    startP1 =
        Offset(
          0.5 * (boxConstraints.maxWidth),
          0.5 * (boxConstraints.maxHeight),
        ) +
        Offset(
          -double.tryParse(section['b'])! * 10 * ratio / 2,
          -double.tryParse(section['d'])! * 10 * ratio / 2 +
              boxConstraints.maxHeight / 20,
        );

    final points = Float32List.fromList(
      _getPointsFromOffset(startP1, [
        Offset(double.tryParse(section['b'])! * 10 * ratio, 0),
        Offset(0, double.tryParse(section['tF'])! * 10 * ratio),
        Offset(
          -(double.tryParse(section['b'])! - double.tryParse(section['tW'])!) /
              2 *
              10 *
              ratio,
          0,
        ),
        Offset(
          0,
          (double.tryParse(section['d'])! -
                  double.tryParse(section['tF'])! * 2) *
              10 *
              ratio,
        ),
        Offset(
          (double.tryParse(section['b'])! - double.tryParse(section['tW'])!) /
              2 *
              10 *
              ratio,
          0,
        ),
        Offset(0, double.tryParse(section['tF'])! * 10 * ratio),
        Offset(-double.tryParse(section['b'])! * 10 * ratio, 0),
        Offset(0, -double.tryParse(section['tF'])! * 10 * ratio),
        Offset(
          (double.tryParse(section['b'])! - double.tryParse(section['tW'])!) /
              2 *
              10 *
              ratio,
          0,
        ),
        Offset(
          0,
          -(double.tryParse(section['d'])! -
                  double.tryParse(section['tF'])! * 2) *
              10 *
              ratio,
        ),
        Offset(
          -(double.tryParse(section['b'])! - double.tryParse(section['tW'])!) /
              2 *
              10 *
              ratio,
          0,
        ),
        Offset(0, -double.tryParse(section['tF'])! * 10 * ratio),
      ]),
    );
    canvas.drawRawPoints(PointMode.polygon, points, sectionPaint);

    //* Indicate the Section Width and Depth
    _drawDimLine(
      canvas,
      fontSize!,
      startP1,
      startP1 + Offset(double.tryParse(section['b'])! * 10 * ratio, 0),
      boxConstraints.maxHeight / 15,
      fontSize! / 2,
      double.tryParse(section['b'])! * 10,
      globalData,
      context,
    );
    _drawDimLine(
      canvas,
      fontSize!,
      startP1 + Offset(0, double.tryParse(section['d'])! * 10 * ratio),
      startP1,
      boxConstraints.maxWidth / 30,
      fontSize! / 2,
      double.tryParse(section['d'])! * 10,
      globalData,
      context,
    );
    //* Indicate the Flange and Web Thickness
    _drawDimLine(
      canvas,
      fontSize!,
      startP1 + Offset(0, double.tryParse(section['tF'])! * 10 * ratio),
      startP1,
      boxConstraints.maxHeight / 14,
      fontSize! / 2,
      double.tryParse(section['tF'])! * 10,
      globalData,
      context,
    );

    _drawDimLine(
      canvas,
      fontSize!,
      startP1 +
          Offset(
            (double.tryParse(section['b'])! - double.tryParse(section['tW'])!) /
                2 *
                10 *
                ratio,
            boxConstraints.maxHeight / 15,
          ),
      startP1 +
          Offset(
            ((double.tryParse(section['b'])! -
                            double.tryParse(section['tW'])!) /
                        2 +
                    double.tryParse(section['tW'])!) *
                10 *
                ratio,
            boxConstraints.maxHeight / 15,
          ),

      -boxConstraints.maxWidth / 30,
      fontSize! / 2,
      double.tryParse(section['tW'])! * 10,
      globalData,
      context,
    );
    // _drawDimLine(
    //   canvas,
    //   fontSize!,
    //   startP1 +
    //       Offset(
    //         -steelColumnSchemedata.size * ratio / 2,
    //         steelColumnSchemedata.size * ratio / 2,
    //       ),
    //   startP1 +
    //       Offset(
    //         -steelColumnSchemedata.size * ratio / 2,
    //         -steelColumnSchemedata.size * ratio / 2,
    //       ),
    //   -10,
    //   fontSize! / 2,
    //   steelColumnSchemedata.size,
    //   globalData,
    //   context,
    // );

    // //Text for middle Bar
    // textSpan = TextSpan(
    //   text: 'T${otherDia}',
    //   style: TextStyle(
    //     fontSize: fontSize,
    //     color: fontColor,
    //     fontWeight: FontWeight.bold,
    //   ),
    // );

    // textPainter = TextPainter(
    //   text: textSpan,
    //   textAlign: TextAlign.center,
    //   textDirection: TextDirection.ltr,
    // )..layout();

    // textPainter.paint(
    //   canvas,
    //   (startP2 + startP3) / 2 +
    //       Offset(
    //         steelColumnSchemedata.size * ratio / 25,
    //         -steelColumnSchemedata.size * ratio / 15,
    //       ),
    // );
  }

  // void _rebarDesignation(
  //   List<int> rebarDia,
  //   int i,
  //   List<int> rebarSpacing,
  //   ui.Color fontColor,
  //   ui.Canvas canvas,
  //   ui.Offset endP3,
  // ) {
  //   TextSpan textSpan = TextSpan(
  //     text: 'T${rebarDia[i]}-${rebarSpacing[i]}',
  //     style: TextStyle(
  //       fontSize: fontSize,
  //       color: fontColor,
  //       fontWeight: FontWeight.bold,
  //     ),
  //   );

  //   TextPainter textPainter = TextPainter(
  //     text: textSpan,
  //     textAlign: TextAlign.center,
  //     textDirection: TextDirection.ltr,
  //   )..layout();

  //   textPainter.paint(canvas, endP3 + Offset(7, -7));
  // }

  void _drawDimLine(
    Canvas canvas,
    double fontSize,
    Offset start,
    Offset end,
    double offsetDistance,
    double dimExtension,
    double slabDepth,
    GlobalData globalData,
    BuildContext context,
  ) {
    double tempOffset = 10; // forthe dim line end
    late Offset tempEnd;
    late Offset tempStart;
    late Offset textCenter;

    late double angle;

    late List<String> unit;

    Color fontColor = Colors.red.withAlpha(200);

    late final vec.Vector2 u;
    late final vec.Vector2 uUnit;
    late final vec.Vector2 vUnit;
    late final vec.Vector2 vUnit45;
    late final vec.Matrix2 m;

    final ColorScheme colorScheme = Theme.of(context).colorScheme;
    final TextTheme textTheme = Theme.of(context).textTheme;

    // get the unit
    switch (globalData.unit) {
      case 'metrics':
        unit = PreferredUnit.metrics;
        break;
      case 'imperial':
        unit = PreferredUnit.imperial;
        break;
      default:
        unit = PreferredUnit.metrics;
    }

    // double dimExtension = offsetDistance.abs() / 2;

    final Paint paint =
        Paint()
          ..color = fontColor
          ..strokeWidth = 1.0
          ..style = PaintingStyle.stroke
          ..strokeJoin = StrokeJoin.round;

    // line vector
    u = vec.Vector2(end.dx - start.dx, end.dy - start.dy);
    uUnit = u / u.length;

    // orthogonal vector
    vUnit = vec.Vector2(uUnit.y, uUnit.x); //rotate aniti-clockwise

    // Vector of parallel line with Offset
    Offset newStart = Offset(
      start.dx - vUnit.x * offsetDistance - uUnit.x * dimExtension,
      start.dy - vUnit.y * offsetDistance - uUnit.y * dimExtension,
    );
    Offset newEnd = Offset(
      end.dx - vUnit.x * offsetDistance + uUnit.x * dimExtension,
      end.dy - vUnit.y * offsetDistance + uUnit.y * dimExtension,
    );
    canvas.drawLine(newStart, newEnd, paint);

    // draw start crossing
    tempStart = Offset(
      (start.dx - vUnit.x * offsetDistance) - vUnit.x * dimExtension,
      (start.dy - vUnit.y * offsetDistance) - vUnit.y * dimExtension,
    );
    tempEnd = Offset(
      (start.dx - vUnit.x * offsetDistance) + vUnit.x * dimExtension,
      (start.dy - vUnit.y * offsetDistance) + vUnit.y * dimExtension,
    );
    canvas.drawLine(tempStart, tempEnd, paint);

    m = vec.Matrix2.rotation(-45 * math.pi / 180);
    vUnit45 = m * vUnit;
    tempStart = Offset(
      (start.dx - vUnit.x * offsetDistance) - vUnit45.x * dimExtension,
      (start.dy - vUnit.y * offsetDistance) - vUnit45.y * dimExtension,
    );
    tempEnd = Offset(
      (start.dx - vUnit.x * offsetDistance) + vUnit45.x * dimExtension,
      (start.dy - vUnit.y * offsetDistance) + vUnit45.y * dimExtension,
    );
    canvas.drawLine(tempStart, tempEnd, paint);

    // draw end crossing
    tempStart = Offset(
      (end.dx - vUnit.x * offsetDistance) - vUnit.x * dimExtension,
      (end.dy - vUnit.y * offsetDistance) - vUnit.y * dimExtension,
    );
    tempEnd = Offset(
      (end.dx - vUnit.x * offsetDistance) + vUnit.x * dimExtension,
      (end.dy - vUnit.y * offsetDistance) + vUnit.y * dimExtension,
    );
    canvas.drawLine(tempStart, tempEnd, paint);

    tempStart = Offset(
      (end.dx - vUnit.x * offsetDistance) - vUnit45.x * dimExtension,
      (end.dy - vUnit.y * offsetDistance) - vUnit45.y * dimExtension,
    );
    tempEnd = Offset(
      (end.dx - vUnit.x * offsetDistance) + vUnit45.x * dimExtension,
      (end.dy - vUnit.y * offsetDistance) + vUnit45.y * dimExtension,
    );
    canvas.drawLine(tempStart, tempEnd, paint);

    // indicate span length and beam size

    TextSpan textSpan = TextSpan(
      text: slabDepth.toStringAsFixed(0),
      style: TextStyle(
        fontSize: fontSize,
        color: fontColor,
        fontWeight: FontWeight.bold,
      ),
    );

    TextPainter textPainter = TextPainter(
      text: textSpan,
      textAlign: TextAlign.center,
      textDirection: TextDirection.ltr,
    )..layout();

    textCenter = Offset(
      (start.dx + end.dx) / 2 -
          vUnit.x * offsetDistance * 2 -
          uUnit.x * textPainter.width / 2,
      (start.dy + end.dy) / 2 -
          vUnit.y * offsetDistance * 2 -
          uUnit.y * textPainter.width / 2,
    );

    angle = math.atan2(end.dy - start.dy, end.dx - start.dx) * 180 / math.pi;

    canvas.save();
    canvas.translate(textCenter.dx, textCenter.dy);
    canvas.rotate(angle * math.pi / 180);
    textPainter.paint(canvas, Offset.zero);
    canvas.restore();
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    // TODO: implement shouldRepaint
    return false;
  }

  Float32List _getPointsFromOffset(Offset start, List<Offset> offsets) {
    List<Offset> points = [start];
    for (Offset offset in offsets) {
      start = start + offset;
      points.add(start);
    }
    return Float32List.fromList(points.expand((i) => [i.dx, i.dy]).toList());
  }
}
