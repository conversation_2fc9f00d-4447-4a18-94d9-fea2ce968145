// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'cantilever_scheme_input_global_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$cantileverSchemeInputGlobalControllerHash() =>
    r'a4810ffb0713f7b829f7f4c1b170f5ca3fd1b158';

/// See also [CantileverSchemeInputGlobalController].
@ProviderFor(CantileverSchemeInputGlobalController)
final cantileverSchemeInputGlobalControllerProvider =
    AutoDisposeAsyncNotifierProvider<
      CantileverSchemeInputGlobalController,
      CantileverSchemeInputGlobal
    >.internal(
      CantileverSchemeInputGlobalController.new,
      name: r'cantileverSchemeInputGlobalControllerProvider',
      debugGetCreateSourceHash:
          const bool.fromEnvironment('dart.vm.product')
              ? null
              : _$cantileverSchemeInputGlobalControllerHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$CantileverSchemeInputGlobalController =
    AutoDisposeAsyncNotifier<CantileverSchemeInputGlobal>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
