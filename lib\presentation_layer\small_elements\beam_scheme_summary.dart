import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:structify/domain_layer/beam_scheme_data.dart';
import 'package:structify/domain_layer/beam_scheme_input.dart';
import 'package:structify/domain_layer/global_data.dart';
import 'package:structify/presentation_layer/small_elements/button/function_button.dart';

//below for printing
// import 'dart:typed_data';
// import 'dart:ui';
import '../../domain_layer/mixin/mixin_str_general_cals.dart';
import '../../domain_layer/mixin/mixin_tools_for_ui.dart';
import 'button/selection_button.dart';

//domain layer
import '../../domain_layer/preferences.dart';

// presentation layer
import '../screen/homescreen.dart';

//misc
import 'sketch/draw_beam_scheme_sketch.dart';

class BeamSchemeSummary extends ConsumerStatefulWidget {
  const BeamSchemeSummary({
    this.isExpanded,
    this.maxHeight,
    this.minHeight,
    super.key,
  });

  final bool? isExpanded;
  final double? maxHeight;
  final double? minHeight;

  @override
  ConsumerState<ConsumerStatefulWidget> createState() =>
      _BeamSchemeSummaryState();
}

class _BeamSchemeSummaryState extends ConsumerState<BeamSchemeSummary>
    with WidgetsBindingObserver, MixinToolsForUI, StrGeneralCals {
  late bool _isExpanded;
  late double _maxHeight;
  late double _minHeight;

  late ScrollController _scrollController;

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void initState() {
    WidgetsBinding.instance.addObserver(this);
    _isExpanded = widget.isExpanded ?? false;
    _maxHeight = widget.maxHeight ?? 300;
    _minHeight = widget.minHeight ?? 50;
    _scrollController = ScrollController();
    // _updateHeights();

    super.initState();
  }

  @override
  void didChangeDependencies() {
    _updateHeights();
    super.didChangeDependencies();
    // Update heights when dependencies change, including after initState
  }

  @override
  void didChangeMetrics() {
    // This method is called when the metrics change (like resizing the window)
    setState(() {
      _updateHeights();
    });
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    final beamSchemeTables = ref.watch(beamSchemeDataControllerProvider);
    final globalData = ref.watch(globalDataControllerProvider);

    final TextStyle subtitleStyle = textTheme.labelMedium!;
    final TextStyle bodyStyle = textTheme.bodySmall!;

    return beamSchemeTables.when(
      data: (tables) {
        //start reutnr real stuffs when loading tables available
        return globalData.when(
          //also, start reutnr real stuffs when global data tables available
          data: (data) {
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding: const EdgeInsets.fromLTRB(10, 0, 10, 0),
                  child: Divider(),
                ),

                Padding(
                  padding: const EdgeInsets.fromLTRB(10, 0, 0, 0),
                  child: Align(
                    alignment: Alignment.centerLeft,
                    child: Wrap(
                      crossAxisAlignment: WrapCrossAlignment.center,
                      children: [
                        IconButton(
                          icon: Icon(
                            _isExpanded ? Icons.expand_less : Icons.expand_more,
                          ),
                          color: colorScheme.onSurface,
                          onPressed: () {
                            setState(() {
                              _isExpanded = !_isExpanded;
                            });
                          },
                        ),
                        Text(
                          'RC Beam Scheme Summary ',
                          style: textTheme.titleLarge!.copyWith(
                            color: colorScheme.onSurface,
                          ),
                        ),
                        Container(
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(5.0),
                            color: colorScheme.surfaceContainer.withAlpha(225),
                            border: Border.all(
                              color: Colors.black.withAlpha(125),
                            ),
                          ),
                          child: Padding(
                            padding: const EdgeInsets.all(2.0),
                            child: Text(
                              '(Total: ${tables.length})',
                              style: textTheme.labelMedium!.copyWith(
                                color: colorScheme.onSurfaceVariant.withAlpha(
                                  225,
                                ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),

                Padding(
                  padding: const EdgeInsets.fromLTRB(10, 0, 10, 0),
                  child: Divider(),
                ),

                ClipRect(
                  child: ConstrainedBox(
                    constraints: BoxConstraints(
                      maxHeight: _isExpanded ? _maxHeight : 0,
                    ),
                    child: Column(
                      children: [
                        Builder(
                          builder: (context) {
                            if (tables.isEmpty) {
                              return Padding(
                                padding: const EdgeInsets.fromLTRB(
                                  10,
                                  0,
                                  10,
                                  0,
                                ),
                                child: Container(
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(5.0),
                                    color: colorScheme.errorContainer.withAlpha(
                                      100,
                                    ),
                                    border: Border.all(
                                      width: 0.5,
                                      color: colorScheme.error.withAlpha(100),
                                    ),
                                  ),
                                  child: Padding(
                                    padding: const EdgeInsets.all(4.0),
                                    child: Text(
                                      'No Beam Scheme Data',
                                      style: textTheme.labelMedium!.copyWith(
                                        color: colorScheme.error,
                                      ),
                                    ),
                                  ),
                                ),
                              );
                            } else {
                              return SizedBox(height: 0, width: 0);
                            }
                          },
                        ),

                        Flexible(
                          child: Scrollbar(
                            controller: _scrollController,
                            thumbVisibility: true,
                            child: DefaultTextStyle(
                              style: textTheme.bodySmall!.copyWith(
                                color: colorScheme.onSurface,
                              ),
                              child: ListView.builder(
                                controller: _scrollController,
                                itemCount: tables.length,
                                itemBuilder: (context, index) {
                                  late Widget mainBeamStatus;
                                  late Widget secBeamStatus;

                                  final RegExp checker1 = RegExp(
                                    r'fail',
                                    caseSensitive: false,
                                  );

                                  final bool mainBeamStatusBool =
                                      !checker1.hasMatch(
                                        tables[index].mainTopBar,
                                      ) &&
                                      !checker1.hasMatch(
                                        tables[index].mainBottomBar,
                                      ) &&
                                      !checker1.hasMatch(
                                        tables[index].mainLinks,
                                      );
                                  final bool secBeamStatusBool =
                                      !checker1.hasMatch(
                                        tables[index].secTopBar,
                                      ) &&
                                      !checker1.hasMatch(
                                        tables[index].secBottomBar,
                                      ) &&
                                      !checker1.hasMatch(
                                        tables[index].secLinks,
                                      );

                                  final int containerOpacity = 175;
                                  final int textOpacity = 255;

                                  if (mainBeamStatusBool) {
                                    mainBeamStatus = Container(
                                      decoration: BoxDecoration(
                                        borderRadius: BorderRadius.circular(
                                          4.0,
                                        ),
                                        color: colorScheme.primary.withAlpha(
                                          containerOpacity,
                                        ),
                                        border: Border.all(
                                          width: 0.5,
                                          color: colorScheme.primary.withAlpha(
                                            containerOpacity,
                                          ),
                                        ),
                                      ),
                                      child: Padding(
                                        padding: const EdgeInsets.all(4.0),
                                        child: Text(
                                          'Main Beam: pass',
                                          style: textTheme.bodySmall!.copyWith(
                                            color: colorScheme.onPrimary
                                                .withAlpha(textOpacity),
                                          ),
                                        ),
                                      ),
                                    );
                                  } else {
                                    mainBeamStatus = Container(
                                      decoration: BoxDecoration(
                                        borderRadius: BorderRadius.circular(
                                          4.0,
                                        ),
                                        color: colorScheme.error.withAlpha(
                                          containerOpacity,
                                        ),
                                        border: Border.all(
                                          width: 0.5,
                                          color: colorScheme.onError.withAlpha(
                                            containerOpacity,
                                          ),
                                        ),
                                      ),
                                      child: Padding(
                                        padding: const EdgeInsets.all(4.0),
                                        child: Text(
                                          'Main Beam: fail',
                                          style: textTheme.bodySmall!.copyWith(
                                            color: colorScheme.onError
                                                .withAlpha(textOpacity),
                                          ),
                                        ),
                                      ),
                                    );
                                  }
                                  if (secBeamStatusBool) {
                                    secBeamStatus = Container(
                                      decoration: BoxDecoration(
                                        borderRadius: BorderRadius.circular(
                                          4.0,
                                        ),
                                        color: colorScheme.primary.withAlpha(
                                          containerOpacity,
                                        ),
                                        border: Border.all(
                                          width: 0.5,
                                          color: colorScheme.primary.withAlpha(
                                            containerOpacity,
                                          ),
                                        ),
                                      ),
                                      child: Padding(
                                        padding: const EdgeInsets.all(4.0),
                                        child: Text(
                                          '2nd Beam: pass',
                                          style: textTheme.bodySmall!.copyWith(
                                            color: colorScheme.onPrimary
                                                .withAlpha(textOpacity),
                                          ),
                                        ),
                                      ),
                                    );
                                  } else {
                                    secBeamStatus = Container(
                                      decoration: BoxDecoration(
                                        borderRadius: BorderRadius.circular(
                                          4.0,
                                        ),
                                        color: colorScheme.error.withAlpha(
                                          containerOpacity,
                                        ),
                                        border: Border.all(
                                          width: 0.5,
                                          color: colorScheme.onError.withAlpha(
                                            containerOpacity,
                                          ),
                                        ),
                                      ),
                                      child: Padding(
                                        padding: const EdgeInsets.all(4.0),
                                        child: Text(
                                          '2nd Beam: fail',
                                          style: textTheme.bodySmall!.copyWith(
                                            color: colorScheme.onError
                                                .withAlpha(textOpacity),
                                          ),
                                        ),
                                      ),
                                    );
                                  }

                                  late List<String> unit;
                                  switch (data.unit) {
                                    case 'metrics':
                                      unit = PreferredUnit.metrics;
                                      break;
                                    case 'imperial':
                                      unit = PreferredUnit.imperial;
                                      break;
                                    default:
                                      unit = PreferredUnit.metrics;
                                  }

                                  final sdl =
                                      tables[index].service +
                                      tables[index].finish /
                                          1000 *
                                          data.finishUnitWeight;

                                  return Padding(
                                    padding: const EdgeInsets.fromLTRB(
                                      8.0,
                                      4.0,
                                      8.0,
                                      4.0,
                                    ),
                                    child: Column(
                                      children: [
                                        Row(
                                          // mainAxisSize: MainAxisSize.min,
                                          children: [
                                            Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.center,
                                              children: [
                                                FunctionButton(
                                                  labelText: ' ${index + 1} ',
                                                  pressedColor: colorScheme
                                                      .tertiary
                                                      .withAlpha(150),
                                                  onTap: (value) async {
                                                    final tables = await ref.read(
                                                      beamSchemeDataControllerProvider
                                                          .future,
                                                    );
                                                    await ref
                                                        .read(
                                                          beamSchemeDataControllerProvider
                                                              .notifier,
                                                        )
                                                        .toggleSelectScheme(
                                                          tables[index]
                                                              .beamSchemeId,
                                                        );
                                                  },
                                                ),
                                                SizedBox(height: 5.0),

                                                SelectionButton(
                                                  labelIcon: Icon(
                                                    Icons.check,
                                                    color:
                                                        tables[index].isSelected
                                                            ? colorScheme
                                                                .onTertiary
                                                            : colorScheme
                                                                .onSurface
                                                                .withAlpha(100),
                                                  ),
                                                  labelText: '',
                                                  bgColor:
                                                      tables[index].isSelected
                                                          ? colorScheme.tertiary
                                                          : colorScheme
                                                              .surfaceContainer
                                                              .withAlpha(100),

                                                  onTap: (value) async {
                                                    final beamSchemes =
                                                        await ref.read(
                                                          beamSchemeDataControllerProvider
                                                              .future,
                                                        );
                                                    await ref
                                                        .read(
                                                          beamSchemeDataControllerProvider
                                                              .notifier,
                                                        )
                                                        .toggleSelectScheme(
                                                          beamSchemes[index]
                                                              .beamSchemeId,
                                                        );
                                                    final newBeamSchemes =
                                                        await ref.read(
                                                          beamSchemeDataControllerProvider
                                                              .future,
                                                        );
                                                    if (newBeamSchemes[index]
                                                        .isSelected) {
                                                      final removedTable =
                                                          newBeamSchemes
                                                              .removeAt(index);
                                                      newBeamSchemes.insert(
                                                        0,
                                                        removedTable,
                                                      );
                                                      await ref
                                                          .read(
                                                            beamSchemeDataControllerProvider
                                                                .notifier,
                                                          )
                                                          .replaceEntireTable(
                                                            newBeamSchemes,
                                                          );
                                                      if (mounted) {
                                                        showSlidingFadingMessage(
                                                          context,
                                                          'Selected beam scheme  (original index: ${index + 1}) pushed to top',
                                                        );
                                                      }
                                                    }
                                                  },
                                                ),
                                                SizedBox(height: 5.0),
                                                FunctionButton(
                                                  key: ValueKey(
                                                    'beamSchemeId_${tables[index].beamSchemeId}',
                                                  ),
                                                  labelText: 'Show Cals',
                                                  pressedColor: colorScheme
                                                      .tertiary
                                                      .withAlpha(150),
                                                  onTap: (value) async {
                                                    await _presentCalsRecord(
                                                      unit,
                                                      tables,
                                                      index,
                                                    );
                                                  },
                                                ),
                                              ],
                                            ),
                                            SizedBox(width: 5.0),
                                            IconButton(
                                              icon: Icon(
                                                Icons.delete,
                                                color: colorScheme.onSurface,
                                              ),
                                              onPressed: () {
                                                ref
                                                    .read(
                                                      beamSchemeDataControllerProvider
                                                          .notifier,
                                                    )
                                                    .deleteTable(
                                                      tables[index]
                                                          .beamSchemeId,
                                                    );
                                              },
                                            ),
                                            Flexible(
                                              child: DefaultTextStyle(
                                                style: bodyStyle,
                                                child: Row(
                                                  children: [
                                                    Column(
                                                      children: [
                                                        SizedBox(height: 3.0),
                                                        mainBeamStatus,
                                                        SizedBox(height: 3.0),
                                                        secBeamStatus,
                                                      ],
                                                    ),
                                                    SizedBox(width: 10.0),
                                                    Column(
                                                      mainAxisSize:
                                                          MainAxisSize.min,
                                                      crossAxisAlignment:
                                                          CrossAxisAlignment
                                                              .start,
                                                      children: [
                                                        Text(
                                                          'Usage :',
                                                          style: subtitleStyle,
                                                        ),
                                                        Text(
                                                          'Finish [${unit[4]}]:',
                                                          style: subtitleStyle,
                                                        ),

                                                        Text(
                                                          'SDL [${unit[1]}]:',
                                                          style: subtitleStyle,
                                                        ),

                                                        Text(
                                                          'LL [${unit[1]}]:',
                                                          style: subtitleStyle,
                                                        ),
                                                        SizedBox(height: 5.0),
                                                        Text(
                                                          'Span [${unit[3]}]:',
                                                          style: subtitleStyle,
                                                        ),
                                                        Text(
                                                          'Bays:',
                                                          style: subtitleStyle,
                                                        ),
                                                        SizedBox(height: 5.0),
                                                        Text(
                                                          'Main Beam [${unit[4]}]:',
                                                          style: subtitleStyle,
                                                        ),
                                                        Text(
                                                          'Sec Beam [${unit[4]}]:',
                                                          style: subtitleStyle,
                                                        ),
                                                      ],
                                                    ),
                                                    SizedBox(width: 5.0),
                                                    Column(
                                                      mainAxisSize:
                                                          MainAxisSize.min,
                                                      crossAxisAlignment:
                                                          CrossAxisAlignment
                                                              .start,
                                                      children: [
                                                        Text(
                                                          tables[index].usage,
                                                        ),
                                                        Text(
                                                          NumberFormat(
                                                            '0',
                                                          ).format(
                                                            tables[index]
                                                                .finish,
                                                          ),
                                                        ),

                                                        Text(
                                                          NumberFormat(
                                                            '0.0',
                                                          ).format(sdl),
                                                        ),

                                                        Text(
                                                          NumberFormat(
                                                            '0.0',
                                                          ).format(
                                                            tables[index]
                                                                .liveLoad,
                                                          ),
                                                        ),
                                                        SizedBox(height: 5.0),
                                                        Text(
                                                          '${NumberFormat('0.0').format(tables[index].shortSpan)}(S)x${NumberFormat('0.0').format(tables[index].longSpan)}(L)',
                                                        ),
                                                        Text(
                                                          NumberFormat(
                                                            '0',
                                                          ).format(
                                                            tables[index].bays,
                                                          ),
                                                        ),
                                                        SizedBox(height: 5.0),
                                                        Text(
                                                          '${NumberFormat('0').format(tables[index].mainWidth)}(W)x${NumberFormat('0').format(tables[index].mainStrZone)}(D)',
                                                        ),
                                                        Text(
                                                          '${NumberFormat('0').format(tables[index].secWidth)}(W)x${NumberFormat('0').format(tables[index].secStrZone)}(D)',
                                                        ),
                                                      ],
                                                    ),
                                                    SizedBox(width: 5.0),

                                                    SizedBox(width: 5.0),
                                                    GestureDetector(
                                                      onTap: () async {
                                                        await showDialog(
                                                          context: context,
                                                          builder: (context) {
                                                            return Dialog(
                                                              backgroundColor:
                                                                  colorScheme
                                                                      .surfaceContainer,
                                                              child: SizedBox(
                                                                width: 550,
                                                                height: 550,
                                                                child: DrawBeamSchemeSketch(
                                                                  sketchWidth:
                                                                      500,
                                                                  sketchHeight:
                                                                      500,
                                                                  index: index,
                                                                  // fontSize: 9.0,
                                                                ),
                                                              ),
                                                            );
                                                          },
                                                        );
                                                      },
                                                      child:
                                                          DrawBeamSchemeSketch(
                                                            sketchWidth: 150,
                                                            sketchHeight: 150,
                                                            index: index,
                                                            fontSize: 9.0,
                                                          ),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                            ),
                                            SizedBox(width: 5.0),
                                          ],
                                        ),
                                        Padding(
                                          padding: const EdgeInsets.fromLTRB(
                                            10,
                                            0,
                                            10,
                                            0,
                                          ),
                                          child: Divider(
                                            thickness: 0.2,
                                            color: colorScheme.primary,
                                          ),
                                        ),
                                      ],
                                    ),
                                  );
                                },
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            );
          },
          error: (error, stackTrace) => Text(error.toString()),
          loading: () => CircularProgressIndicator(),
        );
      },
      error: (error, stackTrace) => Text(error.toString()),
      loading: () => CircularProgressIndicator(),
    );
  }

  Future<void> _presentCalsRecord(
    List<String> unit,
    List<BeamSchemeData> tables,
    int index,
  ) async {
    Map<String, List<dynamic>> resultSec = {}, resultMain = {};
    _extractCalsLogModified(tables, index, resultSec, resultMain);
    GlobalData globalData = await ref.read(globalDataControllerProvider.future);

    //* main beam result
    final mainBeamCals = await _writeBeamResult(
      resultMain, // input the main beam result
      tables,
      index,
      unit,
      presentOption: CalsPresentOptions.main,
    );

    //* secondary beam result
    final secBeamCals = await _writeBeamResult(
      resultSec, // input the secondary beam result
      tables,
      index,
      unit,
      presentOption: CalsPresentOptions.secondary,
    );
    if (mounted) {
      await showDialog(
        context: context,
        builder: (context) {
          ColorScheme colorScheme = Theme.of(context).colorScheme;
          TextTheme textTheme = Theme.of(context).textTheme;
          Color bgColor = colorScheme.surfaceContainer;
          Color onBgColor = colorScheme.onSurface;
          Color titleBgColor = colorScheme.primary.withAlpha(150);
          Color titleOnBgColor = colorScheme.onPrimary;
          TextStyle titleTextStyle = textTheme.labelSmall!.copyWith(
            color: titleOnBgColor,
          );
          ScrollController scrollController = ScrollController();

          late List<String> unit;
          switch (globalData.unit) {
            case 'metrics':
              unit = PreferredUnit.metrics;
              break;
            case 'imperial':
              unit = PreferredUnit.imperial;
              break;
            default:
              unit = PreferredUnit.metrics;
          }
          const double maxH = 550;
          const double maxW = 400;

          return Dialog(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(15),
            ),
            backgroundColor: bgColor,
            child: ConstrainedBox(
              constraints: const BoxConstraints(
                maxWidth: maxW,
                maxHeight: maxH,
              ),
              child: Column(
                children: [
                  Align(
                    alignment: Alignment.centerRight,
                    child: Padding(
                      padding: const EdgeInsets.fromLTRB(0, 10, 10, 0),
                      child: FunctionButton(
                        bgColor: titleBgColor,
                        labelTextColor: titleOnBgColor,
                        labelIcon: Icon(
                          Icons.print_outlined,
                          color: titleOnBgColor.withAlpha(175),
                        ),
                        labelText: '',
                        onTap: (value) {
                          exportListToPdf(context, ref, [
                            ...mainBeamCals.toString().split('\n'),
                            ...secBeamCals.toString().split('\n'),
                          ]);
                        },
                      ),
                    ),
                  ),

                  Scrollbar(
                    controller: scrollController,
                    thumbVisibility: true,
                    trackVisibility: false,
                    child: ConstrainedBox(
                      constraints: const BoxConstraints(
                        maxWidth: maxW,
                        maxHeight: maxH - 70,
                      ),
                      child: SingleChildScrollView(
                        controller: scrollController,
                        child: Padding(
                          padding: const EdgeInsets.fromLTRB(25.0, 5, 25.0, 5),
                          child: DefaultTextStyle(
                            style: textTheme.labelMedium!.copyWith(
                              color: onBgColor,
                            ),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                Container(
                                  decoration: BoxDecoration(
                                    color: colorScheme.tertiary,
                                    shape: BoxShape.circle,
                                    border: Border.all(color: titleOnBgColor),
                                  ),
                                  child: Padding(
                                    padding: const EdgeInsets.all(12.0),
                                    child: Text(
                                      '${index + 1}',
                                      style: titleTextStyle.copyWith(
                                        fontSize: 24,
                                        color: colorScheme.onTertiary,
                                      ),
                                    ),
                                  ),
                                ),
                                SizedBox(width: 10),
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Container(
                                      decoration: BoxDecoration(
                                        color: titleBgColor,
                                        shape: BoxShape.rectangle,
                                        borderRadius: BorderRadius.circular(
                                          10.0,
                                        ),
                                        border: Border.all(
                                          color: titleOnBgColor,
                                        ),
                                      ),
                                      child: Padding(
                                        padding: const EdgeInsets.all(4.0),
                                        child: Text(
                                          'Usage:\n${tables[index].usage}',
                                          style: titleTextStyle,
                                        ),
                                      ),
                                    ),
                                    SizedBox(width: 5),
                                    Container(
                                      decoration: BoxDecoration(
                                        color: titleBgColor,
                                        shape: BoxShape.rectangle,
                                        borderRadius: BorderRadius.circular(
                                          10.0,
                                        ),
                                        border: Border.all(
                                          color: titleOnBgColor,
                                        ),
                                      ),
                                      child: Padding(
                                        padding: const EdgeInsets.all(4.0),
                                        child: Text(
                                          'Finish [${unit[4]}]:\n${tables[index].finish} ',
                                          style: titleTextStyle,
                                        ),
                                      ),
                                    ),
                                    SizedBox(width: 5),
                                    Container(
                                      decoration: BoxDecoration(
                                        color: titleBgColor,
                                        shape: BoxShape.rectangle,
                                        borderRadius: BorderRadius.circular(
                                          10.0,
                                        ),
                                        border: Border.all(
                                          color: titleOnBgColor,
                                        ),
                                      ),
                                      child: Padding(
                                        padding: const EdgeInsets.all(4.0),
                                        child: Text(
                                          'Service [${unit[1]}]:\n${tables[index].service} ',
                                          style: titleTextStyle,
                                        ),
                                      ),
                                    ),
                                    SizedBox(width: 5),
                                    Container(
                                      decoration: BoxDecoration(
                                        color: titleBgColor,
                                        shape: BoxShape.rectangle,
                                        borderRadius: BorderRadius.circular(
                                          10.0,
                                        ),
                                        border: Border.all(
                                          color: titleOnBgColor,
                                        ),
                                      ),
                                      child: Padding(
                                        padding: const EdgeInsets.all(4.0),
                                        child: Text(
                                          'Live Load [${unit[1]}]:\n${tables[index].liveLoad}',
                                          style: titleTextStyle,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                                SizedBox(height: 15),
                                Container(
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(10.0),
                                    border: Border.all(color: onBgColor),
                                  ),
                                  child: Padding(
                                    padding: const EdgeInsets.all(8.0),
                                    child: Text(secBeamCals.toString()),
                                  ),
                                ),
                                SizedBox(height: 10),
                                Container(
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(10.0),
                                    border: Border.all(color: onBgColor),
                                  ),
                                  child: Padding(
                                    padding: const EdgeInsets.all(8.0),
                                    child: Text(mainBeamCals.toString()),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      );
    }
    ;
  }

  void _extractCalsLogModified(
    List<BeamSchemeData> tables,
    int index,
    Map<String, List<dynamic>> resultSec,
    Map<String, List<dynamic>> resultMain,
  ) {
    List<String> recordForSec = <String>[], recordForMain = <String>[];
    bool stopExtractSec = false;

    final record = tables[index].calsLog;
    List<String> lines = record.split('\n');

    RegExp checker = RegExp(r'Design For: Main Beam');
    for (String line in lines) {
      if (checker.hasMatch(line)) {
        stopExtractSec = true;
      }

      if (!stopExtractSec) {
        recordForSec.add(line);
      } else {
        recordForMain.add(line);
      }
    }
    resultSec.addAll(extractCalsLog(recordForSec.join('\n')));
    resultMain.addAll(extractCalsLog(recordForMain.join('\n')));
  }

  Future<StringBuffer> _writeBeamResult(
    Map<String, List<dynamic>> result,
    List<BeamSchemeData> tables,
    int index,
    List<String> unit, {
    String presentOption = CalsPresentOptions.secondary,
  }) async {
    GlobalData globalData = await ref.read(globalDataControllerProvider.future);
    BeamSchemeInput input = await ref.read(
      beamSchemeInputControllerProvider.future,
    );

    StringBuffer buffer = StringBuffer();
    double tempDouble = 0.0, tempDouble2 = 0.0, tempDouble3 = 0.0;
    final NumberFormat f0 = NumberFormat('0'),
        f1 = NumberFormat('0.0'),
        f3 = NumberFormat('0.000'),
        f5 = NumberFormat('0.00000');

    int tempInt = 0;
    String tempString = '';
    buffer.write('------------------------------\n');
    tempInt = getValueAsInt(result, 'width');
    if (presentOption == CalsPresentOptions.secondary) {
      buffer.write('Secondary Beam\n');
      buffer.write(
        '${f0.format(tempInt)}x${f0.format(tables[index].secStrZone)}dp\n',
      );
    } else {
      buffer.write('Main Beam\n');
      buffer.write(
        '${f0.format(tempInt)}x${f0.format(tables[index].mainStrZone)}dp\n',
      );
    }
    buffer.write('------------------------------\n');
    buffer.write('*******************\n');
    buffer.write('Loading\n');
    buffer.write('*******************\n');
    if (presentOption == CalsPresentOptions.secondary) {
      buffer.write('Span, L = ');
      tempDouble = getDoubleValue(result, 'Long Span');
      buffer.write('${f1.format(tempDouble)} [${unit[3]}]\n');

      buffer.write('SW\n= slab + beam\n= ');
      buffer.write(
        '${globalData.rcUnitWeight}x${getDoubleValue(result, 'Slab Thickness') / 1000}x${getDoubleValue(result, 'Secondary Beam Load Width')}',
      );
      buffer.write(
        '+${globalData.rcUnitWeight}x${getDoubleValue(result, 'width') / 1000}x${tables[index].secStrZone / 1000}',
      );
      tempDouble =
          getPressureFromThick(
                getDoubleValue(result, 'Slab Thickness'),
                globalData.rcUnitWeight,
              ) *
              getDoubleValue(result, 'Secondary Beam Load Width') +
          getSelfWeight(
            globalData.rcUnitWeight,
            getDoubleValue(result, 'width') * tables[index].secStrZone,
          );
      buffer.write('\n= ${f1.format(tempDouble)} [${unit[0]}/${unit[3]}]\n');

      buffer.write('SDL\n=');
      tempDouble =
          getDoubleValue(result, 'SDL') *
          getDoubleValue(result, 'Secondary Beam Load Width');
      buffer.write(
        '${getDoubleValue(result, 'SDL')}x${getDoubleValue(result, 'Secondary Beam Load Width')}\n',
      );
      buffer.write('= ${f1.format(tempDouble)} [${unit[0]}/${unit[3]}]\n');

      buffer.write('LL\n=');
      tempDouble =
          getDoubleValue(result, 'LL') *
          getDoubleValue(result, 'Secondary Beam Load Width');
      buffer.write(
        '${getDoubleValue(result, 'LL')}x${getDoubleValue(result, 'Secondary Beam Load Width')}\n',
      );
      buffer.write('= ${f1.format(tempDouble)} [${unit[0]}/${unit[3]}]\n');

      buffer.write('ULS = ');
      tempDouble =
          getDoubleValue(result, 'ULS') *
              getDoubleValue(result, 'Secondary Beam Load Width') +
          globalData.sdlFactor *
              (getPressureFromThick(
                        getDoubleValue(result, 'Slab Thickness'),
                        globalData.rcUnitWeight,
                      ) *
                      getDoubleValue(
                        result,
                        'Secondary Beam Load Width',
                      ) // slab SW
                      +
                  getSelfWeight(
                    globalData.rcUnitWeight,
                    getDoubleValue(result, 'width') * tables[index].secStrZone,
                  )); // beam SW
      buffer.write('${f1.format(tempDouble)} [${unit[0]}/${unit[3]}]\n');

      buffer.write('M\n= wL^2/8\n= ');
      tempDouble = getDoubleValue(result, 'M');
      buffer.write('${f0.format(tempDouble)} [${unit[2]}]\n');

      buffer.write('V\n= wL/2\n= ');
      tempDouble = getDoubleValue(result, 'V');
      buffer.write('${f0.format(tempDouble)} [${unit[0]}]\n');
    } else {
      //* Loading for main beam case
      buffer.write('Span, L = ');
      buffer.write(
        '${f1.format(getDoubleValue(result, 'Short Span'))} [${unit[3]}]\n',
      );

      buffer.write('Bays, n = ${getValueAsInt(result, 'Bays')}\n');

      buffer.write('SW\n= beam SW\n= ');
      buffer.write(
        '${globalData.rcUnitWeight}x${getDoubleValue(result, 'width') / 1000}x${tables[index].mainStrZone / 1000}\n',
      );
      tempDouble = getSelfWeight(
        globalData.rcUnitWeight,
        getDoubleValue(result, 'width') * tables[index].mainStrZone,
      );
      buffer.write('= ${f1.format(tempDouble)} [${unit[0]}/${unit[3]}]\n');

      buffer.write('ULS = ');
      tempDouble =
          globalData.sdlFactor *
          getSelfWeight(
            globalData.rcUnitWeight,
            getDoubleValue(result, 'width') * tables[index].mainStrZone,
          );
      buffer.write('${f1.format(tempDouble)} [${unit[0]}/${unit[3]}]\n');
      tempDouble3 = tempDouble; //* main beam factored SW UDL [kN/m]

      buffer.write('P = ');
      tempDouble =
          (globalData.sdlFactor *
                  (getPressureFromThick(
                            getDoubleValue(result, 'Slab Thickness'),
                            globalData.rcUnitWeight,
                          ) *
                          getDoubleValue(
                            result,
                            'Secondary Beam Load Width',
                          ) + //slab SW on 2nd beam
                      getSelfWeight(
                        globalData.rcUnitWeight,
                        getDoubleValue(result, 'Secondary Width') *
                            tables[index].secStrZone,
                      )) + //2nd beam SW
              getDoubleValue(result, 'ULS') *
                  getDoubleValue(result, 'Secondary Beam Load Width')) *
          getDoubleValue(result, 'Long Span'); // ULS on 2nd beam

      tempDouble2 =
          tempDouble; //* ULS point load on the main beam (from 2nd Beam) [kN/m]

      buffer.write('${f0.format(tempDouble)} [${unit[0]}]');
      buffer.write(' (From 2nd Beam)\n');

      if (getValueAsInt(result, 'Bays') % 2 != 0) {
        buffer.write('M\n= wL^2/8 + (n^2-1)PL/8n\n= ');
        tempDouble =
            tempDouble3 * pow(getDoubleValue(result, 'Short Span'), 2) / 8 +
            (pow(getValueAsInt(result, 'Bays'), 2) - 1) *
                tempDouble2 *
                getDoubleValue(result, 'Short Span') /
                (8 * getValueAsInt(result, 'Bays'));
      } else {
        buffer.write('M\n= wL^2/8 + nPL/8\n= ');
        tempDouble =
            tempDouble3 * pow(getDoubleValue(result, 'Short Span'), 2) / 8 +
            getValueAsInt(result, 'Bays') *
                tempDouble2 *
                getDoubleValue(result, 'Short Span') /
                8;
      }
      buffer.write('${f0.format(tempDouble)} [${unit[2]}]\n');

      buffer.write('V\n= wL/2 + (n-1)P/2\n= ');
      tempDouble =
          tempDouble3 * getDoubleValue(result, 'Short Span') / 2 +
          (getValueAsInt(result, 'Bays') - 1) * tempDouble2 / 2;
      buffer.write('${f0.format(tempDouble)} [${unit[0]}]\n');
    }
    buffer.write('*******************\n');
    buffer.write('Check Moment\n');
    buffer.write('*******************\n');

    buffer.write('d = ');
    tempDouble = getDoubleValue(result, 'd');
    buffer.write('${f0.format(tempDouble)} [${unit[4]}]\n');

    buffer.write('d_c = ');
    tempDouble = getDoubleValue(result, 'd_c');
    buffer.write('${f0.format(tempDouble)} [${unit[4]}]\n');

    buffer.write('k = ');
    tempDouble = getDoubleValue(result, 'k');
    buffer.write('${f5.format(tempDouble)} \n');

    buffer.write('z = ');
    tempDouble = getDoubleValue(result, 'z');
    buffer.write('${f0.format(tempDouble)} [${unit[4]}]\n');

    buffer.write('As_t,req = ');
    tempDouble = getDoubleValue(result, 'As_t');
    buffer.write('${f0.format(tempDouble)} [${unit[6]}]\n');

    buffer.write('As_t,pro');
    tempString = getStringValue(result, 'As_t_pro Designation');

    if (tempString != 'null') {
      buffer.write(' = $tempString');
      tempDouble = getDoubleValue(result, 'As_t_pro');
      buffer.write(' = ${f0.format(tempDouble)} [${unit[6]}]\n');
    } else {
      buffer.write(' = $tempString\n');
    }

    buffer.write('As_c,req = ');
    tempDouble = getDoubleValue(result, 'As_c');
    buffer.write('${f0.format(tempDouble)} [${unit[6]}]\n');

    buffer.write('As_c,pro');
    tempString = getStringValue(result, 'As_c_pro Designation');

    if (!(RegExp(r'N.A.').hasMatch(tempString))) {
      buffer.write(' = $tempString');
      tempDouble = getDoubleValue(result, 'As_c_pro');
      buffer.write(' = ${f0.format(tempDouble)} [${unit[6]}]\n');
    } else {
      buffer.write(' = $tempString\n');
    }

    buffer.write('*******************\n');
    buffer.write('Check Shear\n');
    buffer.write('*******************\n');

    buffer.write('v = ');
    tempDouble = getDoubleValue(result, 'v_d');
    buffer.write('${f3.format(tempDouble)} [${unit[5]}]\n');

    buffer.write('v_r = ');
    tempDouble = getDoubleValue(result, 'v_r');
    buffer.write('${f3.format(tempDouble)} [${unit[5]}]\n');

    buffer.write('v_c = ');
    tempDouble = getDoubleValue(result, 'v_c');
    buffer.write('${f3.format(tempDouble)} [${unit[5]}]\n');

    buffer.write('Asv_req = ');
    tempDouble = getDoubleValue(result, 'l_req');
    buffer.write('${f3.format(tempDouble)} [${unit[6]}/${unit[4]}]\n');

    //* for beam, we must have links even links are not required
    buffer.write('Asv_pro = ');
    tempString = getStringValue(result, 'l_pro Designation');
    buffer.write('$tempString');
    buffer.write(
      ' = ${getDoubleValue(result, 'l_pro')} [${unit[6]}/${unit[4]}]\n',
    );
    buffer.write('*******************\n');
    buffer.write('Check Deflection\n');
    buffer.write('*******************\n');

    buffer.write('L/d = ');
    if (presentOption == CalsPresentOptions.secondary) {
      tempDouble2 = getDoubleValue(result, 'Long Span') * 1000;
      tempDouble3 = getDoubleValue(result, 'd');
      buffer.write('${f0.format(tempDouble2)}/${f0.format(tempDouble3)}=');
      tempDouble =
          getDoubleValue(result, 'Long Span') *
          1000 /
          getDoubleValue(result, 'd');
    } else {
      tempDouble2 = getDoubleValue(result, 'Short Span') * 1000;
      tempDouble3 = getDoubleValue(result, 'd');
      buffer.write('${f0.format(tempDouble2)}/${f0.format(tempDouble3)}=');
      tempDouble =
          getDoubleValue(result, 'Short Span') *
          1000 /
          getDoubleValue(result, 'd');
    }
    buffer.write(f3.format(tempDouble));

    if (tempDouble > 20.0) {
      buffer.write(' > 20\n');
      buffer.write(
        '(check actual deflection and might\nuse pre-camber in detailed design stage)\n',
      );
    } else {
      buffer.write(' < 20\n');
    }

    //* log any warning
    List<String> errors = [], warnings = [];
    RegExp failReg = RegExp(r'fail', caseSensitive: false);

    if (failReg.hasMatch(buffer.toString())) {
      errors.add('Result not reliable. Something fails.');
    }
    if (getValueAsInt(result, 'width') > input.maxWidth) {
      errors.add('Beam Max Width Exceeded. All results are invalid.');
      warnings.add('1. Consider to adjust the beam width limit');
      warnings.add('2. Consider to adjust the beam str zone (if possible)');
      warnings.add('3. Consider to adjust preferred k-value');
    }
    if (failReg.hasMatch(getStringValue(result, 'As_t_pro Designation'))) {
      errors.add('Tension bar fails.');
    }
    if (failReg.hasMatch(getStringValue(result, 'As_c_pro Designation'))) {
      errors.add('Compression bar fails.');
    }
    if (failReg.hasMatch(getStringValue(result, 'l_pro Designation'))) {
      errors.add('Links fails.');
    }
    if (presentOption == CalsPresentOptions.main &&
        getDoubleValue(result, 'Secondary Width') > input.maxWidth) {
      warnings.add(
        'Secondary beam fails, so main beam design is not reliable.',
      );
    }

    // add all erros and warnings in one go
    if (warnings.isNotEmpty) {
      buffer = addWarningHeader(buffer, warnings);
    }
    if (errors.isNotEmpty) {
      buffer = addErrorHeader(buffer, errors);
    }

    return buffer;
  }

  // Method to update heights based on the current media query
  void _updateHeights() {
    final double screenHeight = MediaQuery.of(context).size.height;
    _maxHeight =
        (widget.maxHeight == null)
            ? screenHeight * 0.6
            : (widget.maxHeight! > screenHeight * 0.6)
            ? screenHeight * 0.6
            : widget.maxHeight!;

    _minHeight =
        (widget.minHeight == null)
            ? screenHeight * 0.1
            : (widget.minHeight! > screenHeight * 0.1)
            ? screenHeight * 0.1
            : widget.minHeight!;

    // Adjust _maxHeight if needed

    if (_maxHeight < _minHeight) {
      _maxHeight = _minHeight;
    }
  }
}
