// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'wind_load.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_WindLoad _$WindLoadFromJson(Map<String, dynamic> json) => _WindLoad(
  h: (json['h'] as num?)?.toDouble() ?? 0.0,
  bTop: (json['bTop'] as num?)?.toDouble() ?? 0.0,
  dTop: (json['dTop'] as num?)?.toDouble() ?? 0.0,
  sS: (json['sS'] as num?)?.toDouble() ?? 0.0,
  bldgType: json['bldgType'] as String? ?? 'Concrete',
  amplificationFactor: (json['amplificationFactor'] as num?)?.toDouble() ?? 0.0,
  z: (json['z'] as num?)?.toDouble() ?? 0.0,
  b: (json['b'] as num?)?.toDouble() ?? 0.0,
  d: (json['d'] as num?)?.toDouble() ?? 0.0,
  id: json['id'] as String? ?? '',
);

Map<String, dynamic> _$WindLoadToJson(_WindLoad instance) => <String, dynamic>{
  'h': instance.h,
  'bTop': instance.bTop,
  'dTop': instance.dTop,
  'sS': instance.sS,
  'bldgType': instance.bldgType,
  'amplificationFactor': instance.amplificationFactor,
  'z': instance.z,
  'b': instance.b,
  'd': instance.d,
  'id': instance.id,
};
