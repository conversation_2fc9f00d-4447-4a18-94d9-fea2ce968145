import 'dart:math';
import 'package:flutter/services.dart';
import 'package:xml/xml.dart';


mixin SteelStrHK {
  static double elasticModulus = 205000; // [MPa]
  static double poissonRatio = 0.3;
  static double shearModulus = elasticModulus / (2 * (1 + poissonRatio));
  static double coeffLinearThermalExp = 14.0 * pow(10, -6);
  static double density = 7850; // [kg/m3]

  //* ---------------------------
  //*
  //* BEAM CALS
  //*
  //* ---------------------------

  //* ---------------------------
  //*
  //* COLUMN CALS
  //*
  //* ---------------------------
  Future<double> getAxialCapacitySteelUC({
    double fsyGrade = 355, //original fsy
    double unbracedLength = 5,
    String sectionName = 'UC356X406X634',
    List<Map<String, dynamic>>? steelSections,
  }) async {
    late final double Es,
        maxThk,
        minR,
        lamda,
        lamda0,
        eta,
        alpha,
        pE,
        phi,
        pc,
        aG,
        axialCap;

    Es = SteelStrHK.elasticModulus;
    steelSections ??= await getAllSteelISection();

    try {
      final Map<String, dynamic> section = steelSections.firstWhere(
        (item) => item['name'] == sectionName,
      );
      fsyGrade = await getDesignStrength(sectionName, fsyGrade, steelSections);
      minR =
          min(
            double.tryParse(section['r22'])!,
            double.tryParse(section['r33'])!,
          ) *
          10; // [mm]
      maxThk =
          max(
            double.tryParse(section['tF'])!,
            double.tryParse(section['tW'])!,
          ) *
          10; // [mm]
      lamda = unbracedLength * 1000 / minR;
      lamda0 = 0.2 * sqrt(pow(pi, 2) * Es / fsyGrade);
      if (maxThk <= 40) {
        alpha =
            3.5; // buckling curve (b): Rolled I, about weak axis y-y, max thickness <= 40mm
      } else {
        alpha =
            5.5; // buckling curve (c): Rolled I, about weak axis y-y, max thickness > 40mm
      }
      eta = max(alpha * (lamda - lamda0) / 1000, 0);
      pE = pow(pi, 2) * Es / pow(lamda, 2);
      phi = (fsyGrade + (eta + 1) * pE) / 2;
      pc = (pE * fsyGrade) / (phi + sqrt(pow(phi, 2) - pE * fsyGrade));
      aG = double.parse(section['A']) * 100; // [mm2]
      axialCap = pc * aG / 1000; // [kN]
    } catch (e) {
      axialCap = 0;
    }
    return axialCap;
  }

  //* ---------------------------
  //*
  //* PILES CALS
  //*
  //* ---------------------------

  //input:
  //section = section properties from steel section library
  //pileLength = length of pile [m]
  //sptNValue = SPT N-value [-]
  //fos = factor of safety [-]
  //output:
  //shaft capacity [kN]
  double getDrivenPileShaftCap(
    Map<String, dynamic> section,
    double pileLength,
    double sptNValue, {
    double fos = 3,
  }) {
    final double f, N, Ap;
    f = 1.5;
    N = sptNValue;
    Ap =
        2 *
        (double.parse(section['d']) * 10 +
            double.parse(section['b']) * 10); // [mm2/m]

    double shaftCapacity = (f * N * Ap * pileLength) / 1000 / fos;
    return shaftCapacity;
  }

  //* ---------------------------
  //*
  //* MISC
  //*
  //* ---------------------------
  Future<List<Map<String, String>>> getAllSteelISection({
    String sectionShape = 'UC',
  }) async {
    final xmlString = await rootBundle.loadString('assets/UKSteelSection.xml');
    final xmlDoc = XmlDocument.parse(xmlString);

    final List<Map<String, String>> ucSections = [];

    for (final section in xmlDoc.findAllElements('STEEL_I_SECTION')) {
      final label = section.findElements('LABEL').single.innerText;
      if (label.contains(sectionShape) &&
          sectionShape.contains(RegExp(r'UC||UBP'))) {
        final a = section.findElements('A').single.innerText; //[cm2]
        final d = section.findElements('D').single.innerText; //[cm]
        final b = section.findElements('BF').single.innerText; //[cm]
        final i33 = section.findElements('I33').single.innerText; // [cm4]
        final s33 =
            section
                .findElements('Z33')
                .single
                .innerText; // [cm3] plastic modulus (z is used in the .xml but s should be the convention)
        final z33 =
            section
                .findElements('S33POS')
                .single
                .innerText; // [cm3] section modulus (s is used in the .xml but z should be the convention)

        final r33 = section.findElements('R33').single.innerText; //[cm]
        final r22 = section.findElements('R22').single.innerText; //[cm]
        final tF = section.findElements('TF').single.innerText; //[cm]
        final tW = section.findElements('TW').single.innerText; //[cm]

        ucSections.add({
          'name': label,
          'A': a,
          'd': d,
          'b': b,
          'Ix': i33,
          'Sx': s33,
          'Zx': z33,
          'r33': r33,
          'r22': r22,
          'tF': tF,
          'tW': tW,
        });
      }
    }
    //* Sort the list by 'd' and then by 'a'
    ucSections.sort((a, b) {
      // Compare by 'd' first
      final double? d1 = double.tryParse(a['d']!);
      final double? d2 = double.tryParse(b['d']!);
      final double? a1 = double.tryParse(a['A']!);
      final double? a2 = double.tryParse(b['A']!);

      // no sorting if any of these value is null
      if (d1 == null || d2 == null || a1 == null || a2 == null) {
        return 0;
      }

      // otherwise, sort by 'd' first, then by 'a'
      if (d1 < d2) {
        return -1;
      } else if (d1 == d2) {
        if (a1 < a2) {
          return -1;
        } else if (a1 > a2) {
          return 1;
        } else {
          return 0;
        }
      } else {
        return 1;
      }
    });
    return ucSections;
  }

  Future<double> getDesignStrength(
    String steelSection,
    double fsy,
    List<Map<String, dynamic>>? steelSections,
  ) async {
    steelSections ??= await getAllSteelISection();
    final section = steelSections.firstWhere(
      (item) => item['name'] == steelSection,
    );
    final maxThk =
        max(double.parse(section['tF']!), double.parse(section['tW']!)) *
        10; // [mm]
    switch (fsy.toInt()) {
      case 275:
        if (maxThk <= 16) {
          fsy = 275;
        } else if (maxThk <= 40) {
          fsy = 265;
        } else if (maxThk <= 63) {
          fsy = 255;
        } else if (maxThk <= 80) {
          fsy = 245;
        } else if (maxThk <= 100) {
          fsy = 235;
        } else if (maxThk <= 150) {
          fsy = 225;
        } else {
          fsy = 225; // or handle as needed
        }
        break;

      case 355:
        if (maxThk <= 16) {
          fsy = 355;
        } else if (maxThk <= 40) {
          fsy = 345;
        } else if (maxThk <= 63) {
          fsy = 335;
        } else if (maxThk <= 80) {
          fsy = 325;
        } else if (maxThk <= 100) {
          fsy = 315;
        } else if (maxThk <= 150) {
          fsy = 295;
        } else {
          fsy = 295; // or handle as needed
        }
        break;

      case 460:
        if (maxThk <= 16) {
          fsy = 450;
        } else if (maxThk <= 40) {
          fsy = 430;
        } else if (maxThk <= 63) {
          fsy = 410;
        } else if (maxThk <= 80) {
          fsy = 390;
        } else if (maxThk <= 100) {
          fsy = 380;
        } else {
          fsy = 380; // or handle as needed
        }
        break;

      case 550:
        if (maxThk <= 50) {
          fsy = 550;
        } else if (maxThk <= 100) {
          fsy = 530;
        } else if (maxThk <= 150) {
          fsy = 490;
        } else {
          fsy = 490; // or handle as needed
        }
        break;
      case 690:
        if (maxThk <= 50) {
          fsy = 690;
        } else if (maxThk <= 100) {
          fsy = 650;
        } else if (maxThk <= 150) {
          fsy = 630;
        } else {
          fsy = 630; // or handle as needed
        }
        break;
      default:
        throw Exception('Invalid steel grade');
    }

    return fsy;
  }
}
