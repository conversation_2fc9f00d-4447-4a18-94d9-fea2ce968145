// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'basement_wall_scheme_input.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$BasementWallSchemeInput {

 String get id; double get fcu; double get cover; double get mainKValue; double get mainSteelRatio; int get minS; int get maxS; double get maxDepth; double get minDepth; int get maxLayers; double get wallTopLevel; double get wallBottomLevel; double get soilTopLevel; double get waterTopLevel;
/// Create a copy of BasementWallSchemeInput
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$BasementWallSchemeInputCopyWith<BasementWallSchemeInput> get copyWith => _$BasementWallSchemeInputCopyWithImpl<BasementWallSchemeInput>(this as BasementWallSchemeInput, _$identity);

  /// Serializes this BasementWallSchemeInput to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is BasementWallSchemeInput&&(identical(other.id, id) || other.id == id)&&(identical(other.fcu, fcu) || other.fcu == fcu)&&(identical(other.cover, cover) || other.cover == cover)&&(identical(other.mainKValue, mainKValue) || other.mainKValue == mainKValue)&&(identical(other.mainSteelRatio, mainSteelRatio) || other.mainSteelRatio == mainSteelRatio)&&(identical(other.minS, minS) || other.minS == minS)&&(identical(other.maxS, maxS) || other.maxS == maxS)&&(identical(other.maxDepth, maxDepth) || other.maxDepth == maxDepth)&&(identical(other.minDepth, minDepth) || other.minDepth == minDepth)&&(identical(other.maxLayers, maxLayers) || other.maxLayers == maxLayers)&&(identical(other.wallTopLevel, wallTopLevel) || other.wallTopLevel == wallTopLevel)&&(identical(other.wallBottomLevel, wallBottomLevel) || other.wallBottomLevel == wallBottomLevel)&&(identical(other.soilTopLevel, soilTopLevel) || other.soilTopLevel == soilTopLevel)&&(identical(other.waterTopLevel, waterTopLevel) || other.waterTopLevel == waterTopLevel));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,fcu,cover,mainKValue,mainSteelRatio,minS,maxS,maxDepth,minDepth,maxLayers,wallTopLevel,wallBottomLevel,soilTopLevel,waterTopLevel);

@override
String toString() {
  return 'BasementWallSchemeInput(id: $id, fcu: $fcu, cover: $cover, mainKValue: $mainKValue, mainSteelRatio: $mainSteelRatio, minS: $minS, maxS: $maxS, maxDepth: $maxDepth, minDepth: $minDepth, maxLayers: $maxLayers, wallTopLevel: $wallTopLevel, wallBottomLevel: $wallBottomLevel, soilTopLevel: $soilTopLevel, waterTopLevel: $waterTopLevel)';
}


}

/// @nodoc
abstract mixin class $BasementWallSchemeInputCopyWith<$Res>  {
  factory $BasementWallSchemeInputCopyWith(BasementWallSchemeInput value, $Res Function(BasementWallSchemeInput) _then) = _$BasementWallSchemeInputCopyWithImpl;
@useResult
$Res call({
 String id, double fcu, double cover, double mainKValue, double mainSteelRatio, int minS, int maxS, double maxDepth, double minDepth, int maxLayers, double wallTopLevel, double wallBottomLevel, double soilTopLevel, double waterTopLevel
});




}
/// @nodoc
class _$BasementWallSchemeInputCopyWithImpl<$Res>
    implements $BasementWallSchemeInputCopyWith<$Res> {
  _$BasementWallSchemeInputCopyWithImpl(this._self, this._then);

  final BasementWallSchemeInput _self;
  final $Res Function(BasementWallSchemeInput) _then;

/// Create a copy of BasementWallSchemeInput
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? fcu = null,Object? cover = null,Object? mainKValue = null,Object? mainSteelRatio = null,Object? minS = null,Object? maxS = null,Object? maxDepth = null,Object? minDepth = null,Object? maxLayers = null,Object? wallTopLevel = null,Object? wallBottomLevel = null,Object? soilTopLevel = null,Object? waterTopLevel = null,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,fcu: null == fcu ? _self.fcu : fcu // ignore: cast_nullable_to_non_nullable
as double,cover: null == cover ? _self.cover : cover // ignore: cast_nullable_to_non_nullable
as double,mainKValue: null == mainKValue ? _self.mainKValue : mainKValue // ignore: cast_nullable_to_non_nullable
as double,mainSteelRatio: null == mainSteelRatio ? _self.mainSteelRatio : mainSteelRatio // ignore: cast_nullable_to_non_nullable
as double,minS: null == minS ? _self.minS : minS // ignore: cast_nullable_to_non_nullable
as int,maxS: null == maxS ? _self.maxS : maxS // ignore: cast_nullable_to_non_nullable
as int,maxDepth: null == maxDepth ? _self.maxDepth : maxDepth // ignore: cast_nullable_to_non_nullable
as double,minDepth: null == minDepth ? _self.minDepth : minDepth // ignore: cast_nullable_to_non_nullable
as double,maxLayers: null == maxLayers ? _self.maxLayers : maxLayers // ignore: cast_nullable_to_non_nullable
as int,wallTopLevel: null == wallTopLevel ? _self.wallTopLevel : wallTopLevel // ignore: cast_nullable_to_non_nullable
as double,wallBottomLevel: null == wallBottomLevel ? _self.wallBottomLevel : wallBottomLevel // ignore: cast_nullable_to_non_nullable
as double,soilTopLevel: null == soilTopLevel ? _self.soilTopLevel : soilTopLevel // ignore: cast_nullable_to_non_nullable
as double,waterTopLevel: null == waterTopLevel ? _self.waterTopLevel : waterTopLevel // ignore: cast_nullable_to_non_nullable
as double,
  ));
}

}


/// Adds pattern-matching-related methods to [BasementWallSchemeInput].
extension BasementWallSchemeInputPatterns on BasementWallSchemeInput {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _BasementWallSchemeInput value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _BasementWallSchemeInput() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _BasementWallSchemeInput value)  $default,){
final _that = this;
switch (_that) {
case _BasementWallSchemeInput():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _BasementWallSchemeInput value)?  $default,){
final _that = this;
switch (_that) {
case _BasementWallSchemeInput() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  double fcu,  double cover,  double mainKValue,  double mainSteelRatio,  int minS,  int maxS,  double maxDepth,  double minDepth,  int maxLayers,  double wallTopLevel,  double wallBottomLevel,  double soilTopLevel,  double waterTopLevel)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _BasementWallSchemeInput() when $default != null:
return $default(_that.id,_that.fcu,_that.cover,_that.mainKValue,_that.mainSteelRatio,_that.minS,_that.maxS,_that.maxDepth,_that.minDepth,_that.maxLayers,_that.wallTopLevel,_that.wallBottomLevel,_that.soilTopLevel,_that.waterTopLevel);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  double fcu,  double cover,  double mainKValue,  double mainSteelRatio,  int minS,  int maxS,  double maxDepth,  double minDepth,  int maxLayers,  double wallTopLevel,  double wallBottomLevel,  double soilTopLevel,  double waterTopLevel)  $default,) {final _that = this;
switch (_that) {
case _BasementWallSchemeInput():
return $default(_that.id,_that.fcu,_that.cover,_that.mainKValue,_that.mainSteelRatio,_that.minS,_that.maxS,_that.maxDepth,_that.minDepth,_that.maxLayers,_that.wallTopLevel,_that.wallBottomLevel,_that.soilTopLevel,_that.waterTopLevel);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  double fcu,  double cover,  double mainKValue,  double mainSteelRatio,  int minS,  int maxS,  double maxDepth,  double minDepth,  int maxLayers,  double wallTopLevel,  double wallBottomLevel,  double soilTopLevel,  double waterTopLevel)?  $default,) {final _that = this;
switch (_that) {
case _BasementWallSchemeInput() when $default != null:
return $default(_that.id,_that.fcu,_that.cover,_that.mainKValue,_that.mainSteelRatio,_that.minS,_that.maxS,_that.maxDepth,_that.minDepth,_that.maxLayers,_that.wallTopLevel,_that.wallBottomLevel,_that.soilTopLevel,_that.waterTopLevel);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _BasementWallSchemeInput extends BasementWallSchemeInput {
   _BasementWallSchemeInput({this.id = '1', this.fcu = 45.0, this.cover = 75.0, this.mainKValue = 0.156, this.mainSteelRatio = 0.04, this.minS = 100, this.maxS = 300, this.maxDepth = 500, this.minDepth = 150.0, this.maxLayers = 2, this.wallTopLevel = 0.0, this.wallBottomLevel = -3.0, this.soilTopLevel = 0.0, this.waterTopLevel = 0.0}): super._();
  factory _BasementWallSchemeInput.fromJson(Map<String, dynamic> json) => _$BasementWallSchemeInputFromJson(json);

@override@JsonKey() final  String id;
@override@JsonKey() final  double fcu;
@override@JsonKey() final  double cover;
@override@JsonKey() final  double mainKValue;
@override@JsonKey() final  double mainSteelRatio;
@override@JsonKey() final  int minS;
@override@JsonKey() final  int maxS;
@override@JsonKey() final  double maxDepth;
@override@JsonKey() final  double minDepth;
@override@JsonKey() final  int maxLayers;
@override@JsonKey() final  double wallTopLevel;
@override@JsonKey() final  double wallBottomLevel;
@override@JsonKey() final  double soilTopLevel;
@override@JsonKey() final  double waterTopLevel;

/// Create a copy of BasementWallSchemeInput
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$BasementWallSchemeInputCopyWith<_BasementWallSchemeInput> get copyWith => __$BasementWallSchemeInputCopyWithImpl<_BasementWallSchemeInput>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$BasementWallSchemeInputToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _BasementWallSchemeInput&&(identical(other.id, id) || other.id == id)&&(identical(other.fcu, fcu) || other.fcu == fcu)&&(identical(other.cover, cover) || other.cover == cover)&&(identical(other.mainKValue, mainKValue) || other.mainKValue == mainKValue)&&(identical(other.mainSteelRatio, mainSteelRatio) || other.mainSteelRatio == mainSteelRatio)&&(identical(other.minS, minS) || other.minS == minS)&&(identical(other.maxS, maxS) || other.maxS == maxS)&&(identical(other.maxDepth, maxDepth) || other.maxDepth == maxDepth)&&(identical(other.minDepth, minDepth) || other.minDepth == minDepth)&&(identical(other.maxLayers, maxLayers) || other.maxLayers == maxLayers)&&(identical(other.wallTopLevel, wallTopLevel) || other.wallTopLevel == wallTopLevel)&&(identical(other.wallBottomLevel, wallBottomLevel) || other.wallBottomLevel == wallBottomLevel)&&(identical(other.soilTopLevel, soilTopLevel) || other.soilTopLevel == soilTopLevel)&&(identical(other.waterTopLevel, waterTopLevel) || other.waterTopLevel == waterTopLevel));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,fcu,cover,mainKValue,mainSteelRatio,minS,maxS,maxDepth,minDepth,maxLayers,wallTopLevel,wallBottomLevel,soilTopLevel,waterTopLevel);

@override
String toString() {
  return 'BasementWallSchemeInput(id: $id, fcu: $fcu, cover: $cover, mainKValue: $mainKValue, mainSteelRatio: $mainSteelRatio, minS: $minS, maxS: $maxS, maxDepth: $maxDepth, minDepth: $minDepth, maxLayers: $maxLayers, wallTopLevel: $wallTopLevel, wallBottomLevel: $wallBottomLevel, soilTopLevel: $soilTopLevel, waterTopLevel: $waterTopLevel)';
}


}

/// @nodoc
abstract mixin class _$BasementWallSchemeInputCopyWith<$Res> implements $BasementWallSchemeInputCopyWith<$Res> {
  factory _$BasementWallSchemeInputCopyWith(_BasementWallSchemeInput value, $Res Function(_BasementWallSchemeInput) _then) = __$BasementWallSchemeInputCopyWithImpl;
@override @useResult
$Res call({
 String id, double fcu, double cover, double mainKValue, double mainSteelRatio, int minS, int maxS, double maxDepth, double minDepth, int maxLayers, double wallTopLevel, double wallBottomLevel, double soilTopLevel, double waterTopLevel
});




}
/// @nodoc
class __$BasementWallSchemeInputCopyWithImpl<$Res>
    implements _$BasementWallSchemeInputCopyWith<$Res> {
  __$BasementWallSchemeInputCopyWithImpl(this._self, this._then);

  final _BasementWallSchemeInput _self;
  final $Res Function(_BasementWallSchemeInput) _then;

/// Create a copy of BasementWallSchemeInput
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? fcu = null,Object? cover = null,Object? mainKValue = null,Object? mainSteelRatio = null,Object? minS = null,Object? maxS = null,Object? maxDepth = null,Object? minDepth = null,Object? maxLayers = null,Object? wallTopLevel = null,Object? wallBottomLevel = null,Object? soilTopLevel = null,Object? waterTopLevel = null,}) {
  return _then(_BasementWallSchemeInput(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,fcu: null == fcu ? _self.fcu : fcu // ignore: cast_nullable_to_non_nullable
as double,cover: null == cover ? _self.cover : cover // ignore: cast_nullable_to_non_nullable
as double,mainKValue: null == mainKValue ? _self.mainKValue : mainKValue // ignore: cast_nullable_to_non_nullable
as double,mainSteelRatio: null == mainSteelRatio ? _self.mainSteelRatio : mainSteelRatio // ignore: cast_nullable_to_non_nullable
as double,minS: null == minS ? _self.minS : minS // ignore: cast_nullable_to_non_nullable
as int,maxS: null == maxS ? _self.maxS : maxS // ignore: cast_nullable_to_non_nullable
as int,maxDepth: null == maxDepth ? _self.maxDepth : maxDepth // ignore: cast_nullable_to_non_nullable
as double,minDepth: null == minDepth ? _self.minDepth : minDepth // ignore: cast_nullable_to_non_nullable
as double,maxLayers: null == maxLayers ? _self.maxLayers : maxLayers // ignore: cast_nullable_to_non_nullable
as int,wallTopLevel: null == wallTopLevel ? _self.wallTopLevel : wallTopLevel // ignore: cast_nullable_to_non_nullable
as double,wallBottomLevel: null == wallBottomLevel ? _self.wallBottomLevel : wallBottomLevel // ignore: cast_nullable_to_non_nullable
as double,soilTopLevel: null == soilTopLevel ? _self.soilTopLevel : soilTopLevel // ignore: cast_nullable_to_non_nullable
as double,waterTopLevel: null == waterTopLevel ? _self.waterTopLevel : waterTopLevel // ignore: cast_nullable_to_non_nullable
as double,
  ));
}


}

// dart format on
