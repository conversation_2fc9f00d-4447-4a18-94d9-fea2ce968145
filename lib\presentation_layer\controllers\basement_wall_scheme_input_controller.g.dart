// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'basement_wall_scheme_input_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$basementWallSchemeInputControllerHash() =>
    r'ceded12907106576578e619fc90aa45f29c846a3';

/// See also [BasementWallSchemeInputController].
@ProviderFor(BasementWallSchemeInputController)
final basementWallSchemeInputControllerProvider =
    AutoDisposeAsyncNotifierProvider<
      BasementWallSchemeInputController,
      BasementWallSchemeInput
    >.internal(
      BasementWallSchemeInputController.new,
      name: r'basementWallSchemeInputControllerProvider',
      debugGetCreateSourceHash:
          const bool.fromEnvironment('dart.vm.product')
              ? null
              : _$basementWallSchemeInputControllerHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$BasementWallSchemeInputController =
    AutoDisposeAsyncNotifier<BasementWallSchemeInput>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
