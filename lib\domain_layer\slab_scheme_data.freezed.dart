// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'slab_scheme_data.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$SlabSchemeData {

 String get usage; double get finish; double get service; double get liveLoad; String get loadingTableId; double get span; double get strZone; double get fcu; double get cover; String get mainTopBar; String get mainBottomBar; String get mainLinks; String get slabSchemeId; String get calsLog; bool get isSelected;
/// Create a copy of SlabSchemeData
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$SlabSchemeDataCopyWith<SlabSchemeData> get copyWith => _$SlabSchemeDataCopyWithImpl<SlabSchemeData>(this as SlabSchemeData, _$identity);

  /// Serializes this SlabSchemeData to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is SlabSchemeData&&(identical(other.usage, usage) || other.usage == usage)&&(identical(other.finish, finish) || other.finish == finish)&&(identical(other.service, service) || other.service == service)&&(identical(other.liveLoad, liveLoad) || other.liveLoad == liveLoad)&&(identical(other.loadingTableId, loadingTableId) || other.loadingTableId == loadingTableId)&&(identical(other.span, span) || other.span == span)&&(identical(other.strZone, strZone) || other.strZone == strZone)&&(identical(other.fcu, fcu) || other.fcu == fcu)&&(identical(other.cover, cover) || other.cover == cover)&&(identical(other.mainTopBar, mainTopBar) || other.mainTopBar == mainTopBar)&&(identical(other.mainBottomBar, mainBottomBar) || other.mainBottomBar == mainBottomBar)&&(identical(other.mainLinks, mainLinks) || other.mainLinks == mainLinks)&&(identical(other.slabSchemeId, slabSchemeId) || other.slabSchemeId == slabSchemeId)&&(identical(other.calsLog, calsLog) || other.calsLog == calsLog)&&(identical(other.isSelected, isSelected) || other.isSelected == isSelected));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,usage,finish,service,liveLoad,loadingTableId,span,strZone,fcu,cover,mainTopBar,mainBottomBar,mainLinks,slabSchemeId,calsLog,isSelected);

@override
String toString() {
  return 'SlabSchemeData(usage: $usage, finish: $finish, service: $service, liveLoad: $liveLoad, loadingTableId: $loadingTableId, span: $span, strZone: $strZone, fcu: $fcu, cover: $cover, mainTopBar: $mainTopBar, mainBottomBar: $mainBottomBar, mainLinks: $mainLinks, slabSchemeId: $slabSchemeId, calsLog: $calsLog, isSelected: $isSelected)';
}


}

/// @nodoc
abstract mixin class $SlabSchemeDataCopyWith<$Res>  {
  factory $SlabSchemeDataCopyWith(SlabSchemeData value, $Res Function(SlabSchemeData) _then) = _$SlabSchemeDataCopyWithImpl;
@useResult
$Res call({
 String usage, double finish, double service, double liveLoad, String loadingTableId, double span, double strZone, double fcu, double cover, String mainTopBar, String mainBottomBar, String mainLinks, String slabSchemeId, String calsLog, bool isSelected
});




}
/// @nodoc
class _$SlabSchemeDataCopyWithImpl<$Res>
    implements $SlabSchemeDataCopyWith<$Res> {
  _$SlabSchemeDataCopyWithImpl(this._self, this._then);

  final SlabSchemeData _self;
  final $Res Function(SlabSchemeData) _then;

/// Create a copy of SlabSchemeData
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? usage = null,Object? finish = null,Object? service = null,Object? liveLoad = null,Object? loadingTableId = null,Object? span = null,Object? strZone = null,Object? fcu = null,Object? cover = null,Object? mainTopBar = null,Object? mainBottomBar = null,Object? mainLinks = null,Object? slabSchemeId = null,Object? calsLog = null,Object? isSelected = null,}) {
  return _then(_self.copyWith(
usage: null == usage ? _self.usage : usage // ignore: cast_nullable_to_non_nullable
as String,finish: null == finish ? _self.finish : finish // ignore: cast_nullable_to_non_nullable
as double,service: null == service ? _self.service : service // ignore: cast_nullable_to_non_nullable
as double,liveLoad: null == liveLoad ? _self.liveLoad : liveLoad // ignore: cast_nullable_to_non_nullable
as double,loadingTableId: null == loadingTableId ? _self.loadingTableId : loadingTableId // ignore: cast_nullable_to_non_nullable
as String,span: null == span ? _self.span : span // ignore: cast_nullable_to_non_nullable
as double,strZone: null == strZone ? _self.strZone : strZone // ignore: cast_nullable_to_non_nullable
as double,fcu: null == fcu ? _self.fcu : fcu // ignore: cast_nullable_to_non_nullable
as double,cover: null == cover ? _self.cover : cover // ignore: cast_nullable_to_non_nullable
as double,mainTopBar: null == mainTopBar ? _self.mainTopBar : mainTopBar // ignore: cast_nullable_to_non_nullable
as String,mainBottomBar: null == mainBottomBar ? _self.mainBottomBar : mainBottomBar // ignore: cast_nullable_to_non_nullable
as String,mainLinks: null == mainLinks ? _self.mainLinks : mainLinks // ignore: cast_nullable_to_non_nullable
as String,slabSchemeId: null == slabSchemeId ? _self.slabSchemeId : slabSchemeId // ignore: cast_nullable_to_non_nullable
as String,calsLog: null == calsLog ? _self.calsLog : calsLog // ignore: cast_nullable_to_non_nullable
as String,isSelected: null == isSelected ? _self.isSelected : isSelected // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}

}


/// Adds pattern-matching-related methods to [SlabSchemeData].
extension SlabSchemeDataPatterns on SlabSchemeData {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _SlabSchemeData value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _SlabSchemeData() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _SlabSchemeData value)  $default,){
final _that = this;
switch (_that) {
case _SlabSchemeData():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _SlabSchemeData value)?  $default,){
final _that = this;
switch (_that) {
case _SlabSchemeData() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String usage,  double finish,  double service,  double liveLoad,  String loadingTableId,  double span,  double strZone,  double fcu,  double cover,  String mainTopBar,  String mainBottomBar,  String mainLinks,  String slabSchemeId,  String calsLog,  bool isSelected)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _SlabSchemeData() when $default != null:
return $default(_that.usage,_that.finish,_that.service,_that.liveLoad,_that.loadingTableId,_that.span,_that.strZone,_that.fcu,_that.cover,_that.mainTopBar,_that.mainBottomBar,_that.mainLinks,_that.slabSchemeId,_that.calsLog,_that.isSelected);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String usage,  double finish,  double service,  double liveLoad,  String loadingTableId,  double span,  double strZone,  double fcu,  double cover,  String mainTopBar,  String mainBottomBar,  String mainLinks,  String slabSchemeId,  String calsLog,  bool isSelected)  $default,) {final _that = this;
switch (_that) {
case _SlabSchemeData():
return $default(_that.usage,_that.finish,_that.service,_that.liveLoad,_that.loadingTableId,_that.span,_that.strZone,_that.fcu,_that.cover,_that.mainTopBar,_that.mainBottomBar,_that.mainLinks,_that.slabSchemeId,_that.calsLog,_that.isSelected);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String usage,  double finish,  double service,  double liveLoad,  String loadingTableId,  double span,  double strZone,  double fcu,  double cover,  String mainTopBar,  String mainBottomBar,  String mainLinks,  String slabSchemeId,  String calsLog,  bool isSelected)?  $default,) {final _that = this;
switch (_that) {
case _SlabSchemeData() when $default != null:
return $default(_that.usage,_that.finish,_that.service,_that.liveLoad,_that.loadingTableId,_that.span,_that.strZone,_that.fcu,_that.cover,_that.mainTopBar,_that.mainBottomBar,_that.mainLinks,_that.slabSchemeId,_that.calsLog,_that.isSelected);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _SlabSchemeData extends SlabSchemeData {
   _SlabSchemeData({this.usage = '', this.finish = 0.0, this.service = 0.0, this.liveLoad = 0.0, this.loadingTableId = '', this.span = 5.0, this.strZone = 0.0, this.fcu = 45.0, this.cover = 35.0, this.mainTopBar = '', this.mainBottomBar = '', this.mainLinks = '', this.slabSchemeId = '', this.calsLog = '', this.isSelected = false}): super._();
  factory _SlabSchemeData.fromJson(Map<String, dynamic> json) => _$SlabSchemeDataFromJson(json);

@override@JsonKey() final  String usage;
@override@JsonKey() final  double finish;
@override@JsonKey() final  double service;
@override@JsonKey() final  double liveLoad;
@override@JsonKey() final  String loadingTableId;
@override@JsonKey() final  double span;
@override@JsonKey() final  double strZone;
@override@JsonKey() final  double fcu;
@override@JsonKey() final  double cover;
@override@JsonKey() final  String mainTopBar;
@override@JsonKey() final  String mainBottomBar;
@override@JsonKey() final  String mainLinks;
@override@JsonKey() final  String slabSchemeId;
@override@JsonKey() final  String calsLog;
@override@JsonKey() final  bool isSelected;

/// Create a copy of SlabSchemeData
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$SlabSchemeDataCopyWith<_SlabSchemeData> get copyWith => __$SlabSchemeDataCopyWithImpl<_SlabSchemeData>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$SlabSchemeDataToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _SlabSchemeData&&(identical(other.usage, usage) || other.usage == usage)&&(identical(other.finish, finish) || other.finish == finish)&&(identical(other.service, service) || other.service == service)&&(identical(other.liveLoad, liveLoad) || other.liveLoad == liveLoad)&&(identical(other.loadingTableId, loadingTableId) || other.loadingTableId == loadingTableId)&&(identical(other.span, span) || other.span == span)&&(identical(other.strZone, strZone) || other.strZone == strZone)&&(identical(other.fcu, fcu) || other.fcu == fcu)&&(identical(other.cover, cover) || other.cover == cover)&&(identical(other.mainTopBar, mainTopBar) || other.mainTopBar == mainTopBar)&&(identical(other.mainBottomBar, mainBottomBar) || other.mainBottomBar == mainBottomBar)&&(identical(other.mainLinks, mainLinks) || other.mainLinks == mainLinks)&&(identical(other.slabSchemeId, slabSchemeId) || other.slabSchemeId == slabSchemeId)&&(identical(other.calsLog, calsLog) || other.calsLog == calsLog)&&(identical(other.isSelected, isSelected) || other.isSelected == isSelected));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,usage,finish,service,liveLoad,loadingTableId,span,strZone,fcu,cover,mainTopBar,mainBottomBar,mainLinks,slabSchemeId,calsLog,isSelected);

@override
String toString() {
  return 'SlabSchemeData(usage: $usage, finish: $finish, service: $service, liveLoad: $liveLoad, loadingTableId: $loadingTableId, span: $span, strZone: $strZone, fcu: $fcu, cover: $cover, mainTopBar: $mainTopBar, mainBottomBar: $mainBottomBar, mainLinks: $mainLinks, slabSchemeId: $slabSchemeId, calsLog: $calsLog, isSelected: $isSelected)';
}


}

/// @nodoc
abstract mixin class _$SlabSchemeDataCopyWith<$Res> implements $SlabSchemeDataCopyWith<$Res> {
  factory _$SlabSchemeDataCopyWith(_SlabSchemeData value, $Res Function(_SlabSchemeData) _then) = __$SlabSchemeDataCopyWithImpl;
@override @useResult
$Res call({
 String usage, double finish, double service, double liveLoad, String loadingTableId, double span, double strZone, double fcu, double cover, String mainTopBar, String mainBottomBar, String mainLinks, String slabSchemeId, String calsLog, bool isSelected
});




}
/// @nodoc
class __$SlabSchemeDataCopyWithImpl<$Res>
    implements _$SlabSchemeDataCopyWith<$Res> {
  __$SlabSchemeDataCopyWithImpl(this._self, this._then);

  final _SlabSchemeData _self;
  final $Res Function(_SlabSchemeData) _then;

/// Create a copy of SlabSchemeData
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? usage = null,Object? finish = null,Object? service = null,Object? liveLoad = null,Object? loadingTableId = null,Object? span = null,Object? strZone = null,Object? fcu = null,Object? cover = null,Object? mainTopBar = null,Object? mainBottomBar = null,Object? mainLinks = null,Object? slabSchemeId = null,Object? calsLog = null,Object? isSelected = null,}) {
  return _then(_SlabSchemeData(
usage: null == usage ? _self.usage : usage // ignore: cast_nullable_to_non_nullable
as String,finish: null == finish ? _self.finish : finish // ignore: cast_nullable_to_non_nullable
as double,service: null == service ? _self.service : service // ignore: cast_nullable_to_non_nullable
as double,liveLoad: null == liveLoad ? _self.liveLoad : liveLoad // ignore: cast_nullable_to_non_nullable
as double,loadingTableId: null == loadingTableId ? _self.loadingTableId : loadingTableId // ignore: cast_nullable_to_non_nullable
as String,span: null == span ? _self.span : span // ignore: cast_nullable_to_non_nullable
as double,strZone: null == strZone ? _self.strZone : strZone // ignore: cast_nullable_to_non_nullable
as double,fcu: null == fcu ? _self.fcu : fcu // ignore: cast_nullable_to_non_nullable
as double,cover: null == cover ? _self.cover : cover // ignore: cast_nullable_to_non_nullable
as double,mainTopBar: null == mainTopBar ? _self.mainTopBar : mainTopBar // ignore: cast_nullable_to_non_nullable
as String,mainBottomBar: null == mainBottomBar ? _self.mainBottomBar : mainBottomBar // ignore: cast_nullable_to_non_nullable
as String,mainLinks: null == mainLinks ? _self.mainLinks : mainLinks // ignore: cast_nullable_to_non_nullable
as String,slabSchemeId: null == slabSchemeId ? _self.slabSchemeId : slabSchemeId // ignore: cast_nullable_to_non_nullable
as String,calsLog: null == calsLog ? _self.calsLog : calsLog // ignore: cast_nullable_to_non_nullable
as String,isSelected: null == isSelected ? _self.isSelected : isSelected // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}


}

// dart format on
