import 'package:riverpod_annotation/riverpod_annotation.dart';

//presentation layer
import '../../domain_layer/pile_driven_input.dart';
import '../../domain_layer/pile_driven_input_global.dart';
import '../screen/homescreen.dart';

// domain layer

part 'pile_driven_input_global_controller.g.dart';

@riverpod
class PileDrivenInputGlobalController extends _$PileDrivenInputGlobalController {
  @override
  FutureOr<PileDrivenInputGlobal> build() async {
    // print('Build: Column Scheme Input Global');
    PileDrivenInputGlobal pileDrivenInput =
        await ref
            .read(appDatabaseControllerProvider.notifier)
            . queryPileDrivenInputGlobal();
    return pileDrivenInput;
  }

  Future<void> updateTable({
     double? colLoadFactor,
  }) async {
    final x = await future;
    PileDrivenInputGlobal newState = x.copyWith(
      colLoadFactor: colLoadFactor ?? x.colLoadFactor,
      id: '1',
    );
    // Update the state only if there are changes
    state = AsyncData(newState);
  }
}
