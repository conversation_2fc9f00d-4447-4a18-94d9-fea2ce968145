// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'steel_cantilever_truss_scheme_input_global.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_SteelCantileverTrussSchemeInputGlobal
_$SteelCantileverTrussSchemeInputGlobalFromJson(Map<String, dynamic> json) =>
    _SteelCantileverTrussSchemeInputGlobal(
      id: json['id'] as String? ?? '1',
      span: (json['span'] as num?)?.toDouble() ?? 20.0,
      loadWidth: (json['loadWidth'] as num?)?.toDouble() ?? 10.0,
      strZone: (json['strZone'] as num?)?.toDouble() ?? 1200.0,
      fsy: (json['fsy'] as num?)?.toDouble() ?? 355.0,
      unbracedLength: (json['unbracedLength'] as num?)?.toDouble() ?? 5.0,
      usage: json['usage'] as String? ?? '',
      slabThickness: (json['slabThickness'] as num?)?.toDouble() ?? 130.0,
    );

Map<String, dynamic> _$SteelCantileverTrussSchemeInputGlobalToJson(
  _SteelCantileverTrussSchemeInputGlobal instance,
) => <String, dynamic>{
  'id': instance.id,
  'span': instance.span,
  'loadWidth': instance.loadWidth,
  'strZone': instance.strZone,
  'fsy': instance.fsy,
  'unbracedLength': instance.unbracedLength,
  'usage': instance.usage,
  'slabThickness': instance.slabThickness,
};
