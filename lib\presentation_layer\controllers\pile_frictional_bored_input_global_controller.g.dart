// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'pile_frictional_bored_input_global_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$pileFrictionalBoredInputGlobalControllerHash() =>
    r'29f23bd312e869045ee3d3912f2e0f4e036287a2';

/// See also [PileFrictionalBoredInputGlobalController].
@ProviderFor(PileFrictionalBoredInputGlobalController)
final pileFrictionalBoredInputGlobalControllerProvider =
    AutoDisposeAsyncNotifierProvider<
      PileFrictionalBoredInputGlobalController,
      PileFrictionalBoredInputGlobal
    >.internal(
      PileFrictionalBoredInputGlobalController.new,
      name: r'pileFrictionalBoredInputGlobalControllerProvider',
      debugGetCreateSourceHash:
          const bool.fromEnvironment('dart.vm.product')
              ? null
              : _$pileFrictionalBoredInputGlobalControllerHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$PileFrictionalBoredInputGlobalController =
    AutoDisposeAsyncNotifier<PileFrictionalBoredInputGlobal>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
