// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'pile_socketed_data_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$pileSocketedControllerHash() =>
    r'c219beac86268671d02bbf7378517ce5b6a845c0';

/// See also [PileSocketedController].
@ProviderFor(PileSocketedController)
final pileSocketedControllerProvider = AutoDisposeAsyncNotifierProvider<
  PileSocketedController,
  PileSocketedData
>.internal(
  PileSocketedController.new,
  name: r'pileSocketedControllerProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$pileSocketedControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$PileSocketedController = AutoDisposeAsyncNotifier<PileSocketedData>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
