// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'str_force_struct.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$StrForce {

 double get x; double get Md; double get Vd;
/// Create a copy of StrForce
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$StrForceCopyWith<StrForce> get copyWith => _$StrForceCopyWithImpl<StrForce>(this as StrForce, _$identity);

  /// Serializes this StrForce to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is StrForce&&(identical(other.x, x) || other.x == x)&&(identical(other.Md, Md) || other.Md == Md)&&(identical(other.Vd, Vd) || other.Vd == Vd));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,x,Md,Vd);

@override
String toString() {
  return 'StrForce(x: $x, Md: $Md, Vd: $Vd)';
}


}

/// @nodoc
abstract mixin class $StrForceCopyWith<$Res>  {
  factory $StrForceCopyWith(StrForce value, $Res Function(StrForce) _then) = _$StrForceCopyWithImpl;
@useResult
$Res call({
 double x, double Md, double Vd
});




}
/// @nodoc
class _$StrForceCopyWithImpl<$Res>
    implements $StrForceCopyWith<$Res> {
  _$StrForceCopyWithImpl(this._self, this._then);

  final StrForce _self;
  final $Res Function(StrForce) _then;

/// Create a copy of StrForce
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? x = null,Object? Md = null,Object? Vd = null,}) {
  return _then(_self.copyWith(
x: null == x ? _self.x : x // ignore: cast_nullable_to_non_nullable
as double,Md: null == Md ? _self.Md : Md // ignore: cast_nullable_to_non_nullable
as double,Vd: null == Vd ? _self.Vd : Vd // ignore: cast_nullable_to_non_nullable
as double,
  ));
}

}


/// Adds pattern-matching-related methods to [StrForce].
extension StrForcePatterns on StrForce {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _StrForce value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _StrForce() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _StrForce value)  $default,){
final _that = this;
switch (_that) {
case _StrForce():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _StrForce value)?  $default,){
final _that = this;
switch (_that) {
case _StrForce() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( double x,  double Md,  double Vd)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _StrForce() when $default != null:
return $default(_that.x,_that.Md,_that.Vd);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( double x,  double Md,  double Vd)  $default,) {final _that = this;
switch (_that) {
case _StrForce():
return $default(_that.x,_that.Md,_that.Vd);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( double x,  double Md,  double Vd)?  $default,) {final _that = this;
switch (_that) {
case _StrForce() when $default != null:
return $default(_that.x,_that.Md,_that.Vd);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _StrForce extends StrForce {
   _StrForce({this.x = 0.0, this.Md = 0.0, this.Vd = 0.0}): super._();
  factory _StrForce.fromJson(Map<String, dynamic> json) => _$StrForceFromJson(json);

@override@JsonKey() final  double x;
@override@JsonKey() final  double Md;
@override@JsonKey() final  double Vd;

/// Create a copy of StrForce
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$StrForceCopyWith<_StrForce> get copyWith => __$StrForceCopyWithImpl<_StrForce>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$StrForceToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _StrForce&&(identical(other.x, x) || other.x == x)&&(identical(other.Md, Md) || other.Md == Md)&&(identical(other.Vd, Vd) || other.Vd == Vd));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,x,Md,Vd);

@override
String toString() {
  return 'StrForce(x: $x, Md: $Md, Vd: $Vd)';
}


}

/// @nodoc
abstract mixin class _$StrForceCopyWith<$Res> implements $StrForceCopyWith<$Res> {
  factory _$StrForceCopyWith(_StrForce value, $Res Function(_StrForce) _then) = __$StrForceCopyWithImpl;
@override @useResult
$Res call({
 double x, double Md, double Vd
});




}
/// @nodoc
class __$StrForceCopyWithImpl<$Res>
    implements _$StrForceCopyWith<$Res> {
  __$StrForceCopyWithImpl(this._self, this._then);

  final _StrForce _self;
  final $Res Function(_StrForce) _then;

/// Create a copy of StrForce
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? x = null,Object? Md = null,Object? Vd = null,}) {
  return _then(_StrForce(
x: null == x ? _self.x : x // ignore: cast_nullable_to_non_nullable
as double,Md: null == Md ? _self.Md : Md // ignore: cast_nullable_to_non_nullable
as double,Vd: null == Vd ? _self.Vd : Vd // ignore: cast_nullable_to_non_nullable
as double,
  ));
}


}

// dart format on
