import 'package:riverpod_annotation/riverpod_annotation.dart';

//presentation layer
import '../../domain_layer/steel_cantilever_truss_scheme_input_global.dart';
import '../../domain_layer/steel_transfer_truss_scheme_input_global.dart';
import '../screen/homescreen.dart';

// domain layer

part 'steel_cantilever_truss_scheme_input_global_controller.g.dart';

@riverpod
class SteelCantileverTrussSchemeInputGlobalController
    extends _$SteelCantileverTrussSchemeInputGlobalController {
  @override
  FutureOr<SteelCantileverTrussSchemeInputGlobal> build() async {
    // print('Build: Column Scheme Input Global');
    SteelCantileverTrussSchemeInputGlobal steelCantileverTrussSchemeGlobalInput =
        await ref
            .read(appDatabaseControllerProvider.notifier)
            . querySteelCantileverTrussSchemeInputGlobal();
    final data = ref.watch(loadingTablesControllerProvider);
    return data.when(
      data: (loadingTables) async {
        //* Validate the loading usage
        final usages = loadingTables.map((x) => x.usage).toList();
        if (steelCantileverTrussSchemeGlobalInput.usage == '' ||
            !usages.contains(steelCantileverTrussSchemeGlobalInput.usage)) {
          steelCantileverTrussSchemeGlobalInput =
              steelCantileverTrussSchemeGlobalInput.copyWith(usage: usages.first);
        }
        return steelCantileverTrussSchemeGlobalInput;
      },
      error: (error, stackTrace) => SteelCantileverTrussSchemeInputGlobal(),
      loading: () => SteelCantileverTrussSchemeInputGlobal(),
    );
  }

  Future<void> updateTable({
    String? id,
    double? span,
    double? loadWidth,
    double? strZone,
    double? fsy,
    double? unbracedLength,
    String? usage,
    double? slabThickness,
  }) async {
    final x = await future;
     SteelCantileverTrussSchemeInputGlobal newState = x.copyWith(
      id: id ?? x.id,
      span: span ?? x.span,
      loadWidth: loadWidth ?? x.loadWidth,
      strZone: strZone ?? x.strZone,
      fsy: fsy ?? x.fsy,
      unbracedLength: unbracedLength ?? x.unbracedLength,
      usage: usage ?? x.usage,
      slabThickness: slabThickness ?? x.slabThickness,
    );
    // Update the state only if there are changes
    state = AsyncData(newState);
  }
}
