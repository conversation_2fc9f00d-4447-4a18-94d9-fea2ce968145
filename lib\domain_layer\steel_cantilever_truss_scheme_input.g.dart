// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'steel_cantilever_truss_scheme_input.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_SteelCantileverTrussSchemeInput _$SteelCantileverTrussSchemeInputFromJson(
  Map<String, dynamic> json,
) => _SteelCantileverTrussSchemeInput(
  pointLoad: (json['pointLoad'] as num?)?.toDouble() ?? 0.0,
  distA: (json['distA'] as num?)?.toDouble() ?? 3.0,
  steelCantileverTrussSchemeInputId:
      json['steelCantileverTrussSchemeInputId'] as String? ?? '',
);

Map<String, dynamic> _$SteelCantileverTrussSchemeInputToJson(
  _SteelCantileverTrussSchemeInput instance,
) => <String, dynamic>{
  'pointLoad': instance.pointLoad,
  'distA': instance.distA,
  'steelCantileverTrussSchemeInputId':
      instance.steelCantileverTrussSchemeInputId,
};
