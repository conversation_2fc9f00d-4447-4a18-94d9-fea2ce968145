import 'package:freezed_annotation/freezed_annotation.dart';
// import 'package:flutter/foundation.dart';
part 'steel_transfer_truss_scheme_input.freezed.dart';
part 'steel_transfer_truss_scheme_input.g.dart';

@freezed
abstract class SteelTransferTrussSchemeInput with _$SteelTransferTrussSchemeInput {
  const SteelTransferTrussSchemeInput._();
  factory SteelTransferTrussSchemeInput({
    @Default(0.0) double pointLoad,
    @Default(3.0) double distA,
    @Default('')
    String
    steelTransferTrussSchemeInputId, //will be overriden  as soon as new instance created
  }) = _SteelTransferTrussSchemeInput;

  factory SteelTransferTrussSchemeInput.fromJson(Map<String, Object?> json) =>
      _$SteelTransferTrussSchemeInputFromJson(json);
}
