import 'package:riverpod_annotation/riverpod_annotation.dart';

//presentation layer
import '../../domain_layer/basement_wall_scheme_input.dart';
import '../screen/homescreen.dart';

part 'basement_wall_scheme_input_controller.g.dart';

@riverpod
class BasementWallSchemeInputController extends _$BasementWallSchemeInputController {
  @override
  FutureOr< BasementWallSchemeInput> build() async {
    // print('Build: Slab Scheme Input');
    BasementWallSchemeInput basementWallSchemeInput =
        await ref
            .read(appDatabaseControllerProvider.notifier)
            . queryBasementWallSchemeInput();
    final data = ref.watch(loadingTablesControllerProvider);
    return data.when(
      data: (loadingTables) async {
        return basementWallSchemeInput;
      },
      error: (error, stackTrace) => BasementWallSchemeInput(),
      loading: () => BasementWallSchemeInput(),
    );
  }

  Future<void> updateTable({
    String? id,
    double? fcu,
    double? cover,
    double? mainKValue,
    double? mainSteelRatio,
    int? minS,
    int? maxS,
    double? maxDepth,
    double? minDepth,
    int? maxLayers,
    double? wallTopLevel,
    double? wallBottomLevel,
    double? soilTopLevel,
    double? waterTopLevel,
    double? spanIncreament,
    int? iterationSteps,
  }) async {
    final x = await future;
    // Create a list of updates
    final newState = x.copyWith(
      id: id ?? x.id,
      fcu: fcu ?? x.fcu,
      cover: cover ?? x.cover,
      mainKValue: mainKValue ?? x.mainKValue,
      mainSteelRatio: mainSteelRatio ?? x.mainSteelRatio,
      minS: minS ?? x.minS,
      maxS: maxS ?? x.maxS,
      maxDepth: maxDepth ?? x.maxDepth,
      minDepth: minDepth ?? x.minDepth,
      maxLayers: maxLayers ?? x.maxLayers,
      wallTopLevel: wallTopLevel ?? x.wallTopLevel,
      wallBottomLevel: wallBottomLevel ?? x.wallBottomLevel,
      soilTopLevel: soilTopLevel ?? x.soilTopLevel,
      waterTopLevel: waterTopLevel ?? x.waterTopLevel,
    );
    // Update the state only if there are changes
    state = AsyncData(newState);
  }
}
