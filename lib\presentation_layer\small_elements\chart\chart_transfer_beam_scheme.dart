import 'dart:math';
import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:structify/misc/custom_func.dart';


class ChartTransferBeamScheme extends ConsumerStatefulWidget {
  const ChartTransferBeamScheme({
    this.Md = const [],
    this.Vd = const [],
    this.showMd = true,
    this.showVd = true,
    super.key,
  });

  final List<FlSpot> Md;
  final List<FlSpot> Vd;
  final bool showMd;
  final bool showVd;

  @override
  ConsumerState<ConsumerStatefulWidget> createState() =>
      _ChartTransferBeamSchemeState();
}

class _ChartTransferBeamSchemeState
    extends ConsumerState<ChartTransferBeamScheme> {
  late List<FlSpot> _Md;
  late List<FlSpot> _Vd;
  late bool _showMd;
  late bool _showVd;
  late ScrollController _scrollController;

  @override
  void initState() {
    _Md = widget.Md;
    _Vd = widget.Vd;
    _showMd = widget.showMd;
    _showVd = widget.showVd;
    _scrollController = ScrollController();
    super.initState();
  }

  @override
  void didUpdateWidget(covariant ChartTransferBeamScheme oldWidget) {
    // TODO: implement didUpdateWidget
    super.didUpdateWidget(oldWidget);
    _Md = widget.Md;
    _Vd = widget.Vd;
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  Widget bottomTitleWidgets(
    double value,
    TitleMeta meta,
    double chartWidth,
    TextStyle textStyle,
  ) {
    if (value % 1 != 0) {
      return Container();
    }
    return SideTitleWidget(
      meta: meta,
      space: 16,
      child: Text(meta.formattedValue, style: textStyle),
    );
  }

  Widget leftTitleWidgets(
    double value,
    TitleMeta meta,
    double chartWidth,
    TextStyle textStyle,
  ) {
    return SideTitleWidget(
      meta: meta,
      space: 16,
      child: Text(meta.formattedValue, style: textStyle),
    );
  }

  @override
  Widget build(BuildContext context) {
    final ColorScheme colorScheme = Theme.of(context).colorScheme;
    final TextTheme textTheme = Theme.of(context).textTheme;

    final TextStyle sideTitleStyle = textTheme.labelLarge!.copyWith(
      color: colorScheme.secondary,
    );
    final TextStyle bottomTitleStyle = textTheme.labelLarge!.copyWith(
      color: colorScheme.secondary,
    );

    Color toolTipbgColor = colorScheme.primaryContainer.withAlpha(100);
    final TextStyle tooltipStyle = textTheme.labelSmall!.copyWith(
      color: colorScheme.onPrimaryContainer.withAlpha(100),
    );

    Color horizontalGridColor = colorScheme.secondary.withAlpha(50);
    Color horizontalGridZeroColor = colorScheme.error.withAlpha(100);
    Color verticalGridColor = colorScheme.secondary.withAlpha(50);
    Color chartBgColor = colorScheme.surface;
    Color borderColor = colorScheme.secondary.withAlpha(100);

    return Padding(
      padding: const EdgeInsets.only(
        left: 12,
        bottom: 20,
        right: 20,
        top: 20,
      ),
      child: AspectRatio(
        aspectRatio: 2,
        child: LayoutBuilder(
          builder: (context, constraints) {

            final double minMd = roundTo(
              minFromNum(_Md.map((e) => e.y).toList()),
              1,
            );

            final double maxMd = roundTo(
              maxFromNum(_Md.map((e) => e.y).toList()),
              1,
            );
            final double maxVd = roundTo(
              maxFromNum(_Vd.map((e) => e.y).toList()),
              1,
            );
            final double minVd = roundTo(
              minFromNum(_Vd.map((e) => e.y).toList()),
              1,
            );
    
            return LineChart(
              LineChartData(
                minX: null,
                maxX: null,
                minY: min( minMd + minMd / 10, minVd + minVd / 10),
                maxY: max(maxMd + maxMd / 10, maxVd + maxVd / 10),
                backgroundColor: chartBgColor,
    
                lineTouchData: LineTouchData(
                  touchTooltipData: LineTouchTooltipData(
                    fitInsideHorizontally: true,
                    fitInsideVertically: true,
                    maxContentWidth: 200,
                    getTooltipColor: (touchedSpot) => toolTipbgColor,
                    getTooltipItems: (touchedSpots) {
                      final sortedSpots = List<LineBarSpot>.from(
                        touchedSpots,
                      )..sort((a, b) => a.barIndex.compareTo(b.barIndex));
    
                      return sortedSpots.map((LineBarSpot touchedSpot) {
                        final String touchedSpotTitle =
                            touchedSpot.barIndex == 0 ? 'Md' : 'Vd';
                        final String touchedSpotTitleUnit =
                            touchedSpot.barIndex == 0 ? '[kN-m]' : '[kN]';
                        return LineTooltipItem(
                          'x: ${touchedSpot.x}[m] , $touchedSpotTitle: ${touchedSpot.y.toStringAsFixed(0)} $touchedSpotTitleUnit',
                          tooltipStyle,
                        );
                      }).toList();
                    },
                  ),
                  getTouchedSpotIndicator: (barData, spotIndexes) {
                    // return [
                    //   TouchedSpotIndicatorData(
                    //     FlLine(
                    //       color: colorScheme.primary,
                    //       strokeWidth: 0.5,
                    //     ),
                    //     FlDotData(
                    //       show: true,
                    //       getDotPainter: (spot, percent, barData, index) {
                    //         return FlDotCirclePainter(
                    //           radius: 5,
                    //           color: Colors.transparent,
                    //           strokeColor: colorScheme.primary,
                    //           strokeWidth: 1,
                    //         );
                    //       },
                    //     ),
                    //   ),
                    // ];
    
                    return List.generate(spotIndexes.length, (i) {
                      return TouchedSpotIndicatorData(
                        FlLine(
                          color: colorScheme.primary,
                          strokeWidth: 0.5,
                        ),
                        FlDotData(
                          show: true,
                          getDotPainter: (spot, percent, barData, index) {
                            return FlDotCirclePainter(
                              radius: 5,
                              color: Colors.transparent,
                              strokeColor: colorScheme.primary,
                              strokeWidth: 1,
                            );
                          },
                        ),
                      );
                    });
                  },
                  handleBuiltInTouches: true,
                  getTouchLineStart: (data, index) => 0,
                ),
                lineBarsData: [
                  LineChartBarData(
                    show: _showMd,
                    color: colorScheme.primary,
                    spots: _Md ?? [],
                    isCurved: false,
                    isStrokeCapRound: true,
                    barWidth: 2,
                    belowBarData: BarAreaData(show: false),
                    dotData: const FlDotData(show: false),
                  ),
                  LineChartBarData(
                    show: _showVd,
                    color: colorScheme.tertiary,
                    spots: _Vd ?? [],
                    isCurved: false,
                    isStrokeCapRound: true,
                    barWidth: 2,
                    belowBarData: BarAreaData(show: false),
                    dotData: const FlDotData(show: false),
                  ),
                ],
                titlesData: FlTitlesData(
                  topTitles: AxisTitles(
                    axisNameSize: 25,
                    axisNameWidget: Text(
                      'Moment and Shear Along Span',
                      style: textTheme.titleMedium!.copyWith(
                        color: colorScheme.primary,
                      ),
                    ),
                    drawBelowEverything: true,
                  ),
                  leftTitles: AxisTitles(
                    axisNameWidget: Text(
                      'M [kN-m]',
                      style: textTheme.labelMedium!.copyWith(
                        color: colorScheme.primary,
                      ),
                    ),
                    sideTitles: SideTitles(
                      showTitles: true,
                      getTitlesWidget:
                          (value, meta) => leftTitleWidgets(
                            value,
                            meta,
                            constraints.maxWidth,
                            sideTitleStyle,
                          ),
                      reservedSize: 56,
                    ),
                    drawBelowEverything: true,
                  ),
                  rightTitles: const AxisTitles(
                    sideTitles: SideTitles(showTitles: false),
                  ),
    
                  bottomTitles: AxisTitles(
                    axisNameWidget: Text(
                      'Span [m]',
                      style: textTheme.labelMedium!.copyWith(
                        color: colorScheme.primary,
                      ),
                    ),
                    sideTitles: SideTitles(
                      showTitles: true,
                      getTitlesWidget:
                          (value, meta) => bottomTitleWidgets(
                            value,
                            meta,
                            constraints.maxWidth,
                            bottomTitleStyle,
                          ),
                      reservedSize: 36,
                      interval: 1,
                    ),
                    drawBelowEverything: true,
                  ),
                ),
                gridData: FlGridData(
                  show: true,
                  drawHorizontalLine: true,
                  drawVerticalLine: true,
                  horizontalInterval: 500.0,
                  verticalInterval: 1.0,
                  // checkToShowHorizontalLine: (value) {
                  //   return value.toInt() != 0;
                  // },
                  getDrawingHorizontalLine: (yvalue) {
                    if (yvalue == 0) {
                      return FlLine(
                        color: horizontalGridZeroColor,
                        // dashArray: [8, 2],
                        strokeWidth: 1,
                      );
                    } else {
                      return FlLine(
                        color: horizontalGridColor,
                        // dashArray: [8, 2],
                        strokeWidth: 1,
                      );
                    }
                  },
                  getDrawingVerticalLine:
                      (_) => FlLine(
                        color: verticalGridColor,
                        // color: Colors.indigo,
                        // dashArray: [8, 2],
                        strokeWidth: 1,
                      ),
                  checkToShowVerticalLine: (value) {
                    return value.toInt() != 0;
                  },
                ),
                borderData: FlBorderData(
                  show: true,
                  border: Border.all(color: borderColor, width: 2),
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}
