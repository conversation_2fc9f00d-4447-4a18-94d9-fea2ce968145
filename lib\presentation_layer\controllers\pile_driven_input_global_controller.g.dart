// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'pile_driven_input_global_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$pileDrivenInputGlobalControllerHash() =>
    r'c3ca4ca01251a574245244b509f82ed754cee13b';

/// See also [PileDrivenInputGlobalController].
@ProviderFor(PileDrivenInputGlobalController)
final pileDrivenInputGlobalControllerProvider =
    AutoDisposeAsyncNotifierProvider<
      PileDrivenInputGlobalController,
      PileDrivenInputGlobal
    >.internal(
      PileDrivenInputGlobalController.new,
      name: r'pileDrivenInputGlobalControllerProvider',
      debugGetCreateSourceHash:
          const bool.fromEnvironment('dart.vm.product')
              ? null
              : _$pileDrivenInputGlobalControllerHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$PileDrivenInputGlobalController =
    AutoDisposeAsyncNotifier<PileDrivenInputGlobal>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
