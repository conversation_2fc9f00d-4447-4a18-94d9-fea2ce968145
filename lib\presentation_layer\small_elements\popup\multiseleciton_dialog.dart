import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:structify/presentation_layer/small_elements/button/function_button.dart';

class MultiSelectionDialog extends ConsumerStatefulWidget {
  MultiSelectionDialog({
    this.options = const [],
    this.selectedOptions = const [],
    this.label = '',
    this.onPressed,
    this.dialogWidth = 500,
    this.dialogHeight = 500,
    this.trailing = const <Widget>[],
    super.key,
  });
  List<String> options;
  List<String> selectedOptions;

  final String label;
  final void Function(List<String>)? onPressed;

  final double dialogWidth;
  final double dialogHeight;
  final List<Widget> trailing;

  @override
  ConsumerState<ConsumerStatefulWidget> createState() =>
      _MultiSelectionDialogState();
}

class _MultiSelectionDialogState extends ConsumerState<MultiSelectionDialog> {
  late List<String> _options;
  late List<String> _selectedOptions;
  late void Function(List<String>)? _onPressed;
  late ScrollController _scrollController;
  late String _label;

  late double _dialogWidth;
  late double _dialogHeight;

  late List<Widget> _trailing;

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  void initState() {
    _label = widget.label;
    _options = widget.options;
    _selectedOptions = widget.selectedOptions;
    _onPressed = widget.onPressed;
    _trailing = widget.trailing;

    _scrollController = ScrollController();
    _dialogWidth = widget.dialogWidth;
    _dialogHeight = widget.dialogHeight;
    super.initState();
  }

  @override
  void didUpdateWidget(covariant MultiSelectionDialog oldWidget) {
    _selectedOptions = widget.selectedOptions ?? _selectedOptions;
    _options = widget.options ?? _options;
    super.didUpdateWidget(oldWidget);
  }

  @override
  Widget build(BuildContext context) {
    return FunctionButton(
      labelIcon: Icon(Icons.construction_outlined),
      labelText: _label,
      onTap: (value) async {
        await _getSelectedGrade(context);
        if (_onPressed != null) {
          _onPressed!(_selectedOptions);
        }
      },
    );
  }

  Future<void> _getSelectedGrade(BuildContext context) async {
    await showDialog(
      context: context,
      builder: (context) {
        ColorScheme colorScheme = Theme.of(context).colorScheme;
        TextTheme textTheme = Theme.of(context).textTheme;
        return AlertDialog(
          backgroundColor: colorScheme.secondaryContainer,
          title: Align(
            child: Text(
              widget.label,
              style: textTheme.titleLarge!.copyWith(
                color: colorScheme.onSecondaryContainer,
              ),
            ),
          ),
          content: SingleChildScrollView(
            child: Scrollbar(
              controller: _scrollController,
              thumbVisibility: true,
              child: SizedBox(
                width: _dialogWidth,
                height: _dialogHeight,
                child: ListView.builder(
                  controller: _scrollController,
                  itemCount: _options.length,
                  itemBuilder: (context, index) {
                    return Row(
                      children: [
                        Flexible(
                          flex: 3,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              CustomCheckBoxListTile(
                                value: _selectedOptions.contains(
                                  _options[index],
                                ),
                                option: _options[index],
                                onPressed: (value) {
                                  setState(() {
                                    if (value) {
                                      _selectedOptions.add(_options[index]);
                                    } else {
                                      _selectedOptions.remove(_options[index]);
                                    }
                                  });
                                },
                              ),
                            ],
                          ),
                        ),
                        //* show trailing widget, if available.
                        _trailing.isNotEmpty
                                  ? Flexible(
                          flex: 1,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                               _trailing[index]
                            ],
                          ),
                        ): SizedBox(width: 1, height: 1),
                      ],
                    );
                  },
                ),
              ),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(_selectedOptions),
              child: Text('Done'),
            ),
          ],
        );
      },
    );
  }
}

class CustomCheckBoxListTile extends StatefulWidget {
  final bool value;
  final String option;
  final void Function(bool)? onPressed;

  const CustomCheckBoxListTile({
    this.value = false,
    this.option = '',
    this.onPressed,
    super.key,
  });

  @override
  State<CustomCheckBoxListTile> createState() => _CustomCheckBoxListTileState();
}

class _CustomCheckBoxListTileState extends State<CustomCheckBoxListTile> {
  late final String _option;
  late bool _value = false;
  late void Function(bool)? _onPressed;
  @override
  void initState() {
    _option = widget.option;
    _value = widget.value;
    _onPressed = widget.onPressed;
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    ColorScheme colorScheme = Theme.of(context).colorScheme;
    TextTheme textTheme = Theme.of(context).textTheme;
    return CheckboxListTile(
      controlAffinity: ListTileControlAffinity.platform,
      title: Text(
        _option,
        style: textTheme.labelLarge!.copyWith(
          color: colorScheme.onSecondaryContainer,
        ),
      ),
      value: _value,
      onChanged: (bool? value) {
        if (_onPressed != null && value != null) {
          _onPressed!(value);
        }
        setState(() {
          _value = value ?? _value;
        });
      },
    );
  }
}
