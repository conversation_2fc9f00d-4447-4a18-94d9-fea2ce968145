// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'loading_data.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$LoadingData {

 double get sdl; double get ll; double get slsLoad; double get ulsLoad;
/// Create a copy of LoadingData
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$LoadingDataCopyWith<LoadingData> get copyWith => _$LoadingDataCopyWithImpl<LoadingData>(this as LoadingData, _$identity);

  /// Serializes this LoadingData to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is LoadingData&&(identical(other.sdl, sdl) || other.sdl == sdl)&&(identical(other.ll, ll) || other.ll == ll)&&(identical(other.slsLoad, slsLoad) || other.slsLoad == slsLoad)&&(identical(other.ulsLoad, ulsLoad) || other.ulsLoad == ulsLoad));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,sdl,ll,slsLoad,ulsLoad);

@override
String toString() {
  return 'LoadingData(sdl: $sdl, ll: $ll, slsLoad: $slsLoad, ulsLoad: $ulsLoad)';
}


}

/// @nodoc
abstract mixin class $LoadingDataCopyWith<$Res>  {
  factory $LoadingDataCopyWith(LoadingData value, $Res Function(LoadingData) _then) = _$LoadingDataCopyWithImpl;
@useResult
$Res call({
 double sdl, double ll, double slsLoad, double ulsLoad
});




}
/// @nodoc
class _$LoadingDataCopyWithImpl<$Res>
    implements $LoadingDataCopyWith<$Res> {
  _$LoadingDataCopyWithImpl(this._self, this._then);

  final LoadingData _self;
  final $Res Function(LoadingData) _then;

/// Create a copy of LoadingData
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? sdl = null,Object? ll = null,Object? slsLoad = null,Object? ulsLoad = null,}) {
  return _then(_self.copyWith(
sdl: null == sdl ? _self.sdl : sdl // ignore: cast_nullable_to_non_nullable
as double,ll: null == ll ? _self.ll : ll // ignore: cast_nullable_to_non_nullable
as double,slsLoad: null == slsLoad ? _self.slsLoad : slsLoad // ignore: cast_nullable_to_non_nullable
as double,ulsLoad: null == ulsLoad ? _self.ulsLoad : ulsLoad // ignore: cast_nullable_to_non_nullable
as double,
  ));
}

}


/// Adds pattern-matching-related methods to [LoadingData].
extension LoadingDataPatterns on LoadingData {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _LoadingData value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _LoadingData() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _LoadingData value)  $default,){
final _that = this;
switch (_that) {
case _LoadingData():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _LoadingData value)?  $default,){
final _that = this;
switch (_that) {
case _LoadingData() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( double sdl,  double ll,  double slsLoad,  double ulsLoad)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _LoadingData() when $default != null:
return $default(_that.sdl,_that.ll,_that.slsLoad,_that.ulsLoad);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( double sdl,  double ll,  double slsLoad,  double ulsLoad)  $default,) {final _that = this;
switch (_that) {
case _LoadingData():
return $default(_that.sdl,_that.ll,_that.slsLoad,_that.ulsLoad);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( double sdl,  double ll,  double slsLoad,  double ulsLoad)?  $default,) {final _that = this;
switch (_that) {
case _LoadingData() when $default != null:
return $default(_that.sdl,_that.ll,_that.slsLoad,_that.ulsLoad);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _LoadingData extends LoadingData {
   _LoadingData({this.sdl = 0.0, this.ll = 0.0, this.slsLoad = 0.0, this.ulsLoad = 0.0}): super._();
  factory _LoadingData.fromJson(Map<String, dynamic> json) => _$LoadingDataFromJson(json);

@override@JsonKey() final  double sdl;
@override@JsonKey() final  double ll;
@override@JsonKey() final  double slsLoad;
@override@JsonKey() final  double ulsLoad;

/// Create a copy of LoadingData
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$LoadingDataCopyWith<_LoadingData> get copyWith => __$LoadingDataCopyWithImpl<_LoadingData>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$LoadingDataToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _LoadingData&&(identical(other.sdl, sdl) || other.sdl == sdl)&&(identical(other.ll, ll) || other.ll == ll)&&(identical(other.slsLoad, slsLoad) || other.slsLoad == slsLoad)&&(identical(other.ulsLoad, ulsLoad) || other.ulsLoad == ulsLoad));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,sdl,ll,slsLoad,ulsLoad);

@override
String toString() {
  return 'LoadingData(sdl: $sdl, ll: $ll, slsLoad: $slsLoad, ulsLoad: $ulsLoad)';
}


}

/// @nodoc
abstract mixin class _$LoadingDataCopyWith<$Res> implements $LoadingDataCopyWith<$Res> {
  factory _$LoadingDataCopyWith(_LoadingData value, $Res Function(_LoadingData) _then) = __$LoadingDataCopyWithImpl;
@override @useResult
$Res call({
 double sdl, double ll, double slsLoad, double ulsLoad
});




}
/// @nodoc
class __$LoadingDataCopyWithImpl<$Res>
    implements _$LoadingDataCopyWith<$Res> {
  __$LoadingDataCopyWithImpl(this._self, this._then);

  final _LoadingData _self;
  final $Res Function(_LoadingData) _then;

/// Create a copy of LoadingData
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? sdl = null,Object? ll = null,Object? slsLoad = null,Object? ulsLoad = null,}) {
  return _then(_LoadingData(
sdl: null == sdl ? _self.sdl : sdl // ignore: cast_nullable_to_non_nullable
as double,ll: null == ll ? _self.ll : ll // ignore: cast_nullable_to_non_nullable
as double,slsLoad: null == slsLoad ? _self.slsLoad : slsLoad // ignore: cast_nullable_to_non_nullable
as double,ulsLoad: null == ulsLoad ? _self.ulsLoad : ulsLoad // ignore: cast_nullable_to_non_nullable
as double,
  ));
}


}

// dart format on
