// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'steel_beam_scheme_data.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$SteelBeamSchemeData {

 String get usage; double get finish; double get service; double get liveLoad; String get loadingTableId; double get slabThickness; double get compositeActionFactor; double get shortSpan; double get longSpan; int get bays; double get strZone; double get fsy; String get mainBeamSection; String get secBeamSection; String get steelBeamSchemeId;//will be overriden  as soon as new instance created
 String get calsLog;
/// Create a copy of SteelBeamSchemeData
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$SteelBeamSchemeDataCopyWith<SteelBeamSchemeData> get copyWith => _$SteelBeamSchemeDataCopyWithImpl<SteelBeamSchemeData>(this as SteelBeamSchemeData, _$identity);

  /// Serializes this SteelBeamSchemeData to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is SteelBeamSchemeData&&(identical(other.usage, usage) || other.usage == usage)&&(identical(other.finish, finish) || other.finish == finish)&&(identical(other.service, service) || other.service == service)&&(identical(other.liveLoad, liveLoad) || other.liveLoad == liveLoad)&&(identical(other.loadingTableId, loadingTableId) || other.loadingTableId == loadingTableId)&&(identical(other.slabThickness, slabThickness) || other.slabThickness == slabThickness)&&(identical(other.compositeActionFactor, compositeActionFactor) || other.compositeActionFactor == compositeActionFactor)&&(identical(other.shortSpan, shortSpan) || other.shortSpan == shortSpan)&&(identical(other.longSpan, longSpan) || other.longSpan == longSpan)&&(identical(other.bays, bays) || other.bays == bays)&&(identical(other.strZone, strZone) || other.strZone == strZone)&&(identical(other.fsy, fsy) || other.fsy == fsy)&&(identical(other.mainBeamSection, mainBeamSection) || other.mainBeamSection == mainBeamSection)&&(identical(other.secBeamSection, secBeamSection) || other.secBeamSection == secBeamSection)&&(identical(other.steelBeamSchemeId, steelBeamSchemeId) || other.steelBeamSchemeId == steelBeamSchemeId)&&(identical(other.calsLog, calsLog) || other.calsLog == calsLog));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,usage,finish,service,liveLoad,loadingTableId,slabThickness,compositeActionFactor,shortSpan,longSpan,bays,strZone,fsy,mainBeamSection,secBeamSection,steelBeamSchemeId,calsLog);

@override
String toString() {
  return 'SteelBeamSchemeData(usage: $usage, finish: $finish, service: $service, liveLoad: $liveLoad, loadingTableId: $loadingTableId, slabThickness: $slabThickness, compositeActionFactor: $compositeActionFactor, shortSpan: $shortSpan, longSpan: $longSpan, bays: $bays, strZone: $strZone, fsy: $fsy, mainBeamSection: $mainBeamSection, secBeamSection: $secBeamSection, steelBeamSchemeId: $steelBeamSchemeId, calsLog: $calsLog)';
}


}

/// @nodoc
abstract mixin class $SteelBeamSchemeDataCopyWith<$Res>  {
  factory $SteelBeamSchemeDataCopyWith(SteelBeamSchemeData value, $Res Function(SteelBeamSchemeData) _then) = _$SteelBeamSchemeDataCopyWithImpl;
@useResult
$Res call({
 String usage, double finish, double service, double liveLoad, String loadingTableId, double slabThickness, double compositeActionFactor, double shortSpan, double longSpan, int bays, double strZone, double fsy, String mainBeamSection, String secBeamSection, String steelBeamSchemeId, String calsLog
});




}
/// @nodoc
class _$SteelBeamSchemeDataCopyWithImpl<$Res>
    implements $SteelBeamSchemeDataCopyWith<$Res> {
  _$SteelBeamSchemeDataCopyWithImpl(this._self, this._then);

  final SteelBeamSchemeData _self;
  final $Res Function(SteelBeamSchemeData) _then;

/// Create a copy of SteelBeamSchemeData
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? usage = null,Object? finish = null,Object? service = null,Object? liveLoad = null,Object? loadingTableId = null,Object? slabThickness = null,Object? compositeActionFactor = null,Object? shortSpan = null,Object? longSpan = null,Object? bays = null,Object? strZone = null,Object? fsy = null,Object? mainBeamSection = null,Object? secBeamSection = null,Object? steelBeamSchemeId = null,Object? calsLog = null,}) {
  return _then(_self.copyWith(
usage: null == usage ? _self.usage : usage // ignore: cast_nullable_to_non_nullable
as String,finish: null == finish ? _self.finish : finish // ignore: cast_nullable_to_non_nullable
as double,service: null == service ? _self.service : service // ignore: cast_nullable_to_non_nullable
as double,liveLoad: null == liveLoad ? _self.liveLoad : liveLoad // ignore: cast_nullable_to_non_nullable
as double,loadingTableId: null == loadingTableId ? _self.loadingTableId : loadingTableId // ignore: cast_nullable_to_non_nullable
as String,slabThickness: null == slabThickness ? _self.slabThickness : slabThickness // ignore: cast_nullable_to_non_nullable
as double,compositeActionFactor: null == compositeActionFactor ? _self.compositeActionFactor : compositeActionFactor // ignore: cast_nullable_to_non_nullable
as double,shortSpan: null == shortSpan ? _self.shortSpan : shortSpan // ignore: cast_nullable_to_non_nullable
as double,longSpan: null == longSpan ? _self.longSpan : longSpan // ignore: cast_nullable_to_non_nullable
as double,bays: null == bays ? _self.bays : bays // ignore: cast_nullable_to_non_nullable
as int,strZone: null == strZone ? _self.strZone : strZone // ignore: cast_nullable_to_non_nullable
as double,fsy: null == fsy ? _self.fsy : fsy // ignore: cast_nullable_to_non_nullable
as double,mainBeamSection: null == mainBeamSection ? _self.mainBeamSection : mainBeamSection // ignore: cast_nullable_to_non_nullable
as String,secBeamSection: null == secBeamSection ? _self.secBeamSection : secBeamSection // ignore: cast_nullable_to_non_nullable
as String,steelBeamSchemeId: null == steelBeamSchemeId ? _self.steelBeamSchemeId : steelBeamSchemeId // ignore: cast_nullable_to_non_nullable
as String,calsLog: null == calsLog ? _self.calsLog : calsLog // ignore: cast_nullable_to_non_nullable
as String,
  ));
}

}


/// Adds pattern-matching-related methods to [SteelBeamSchemeData].
extension SteelBeamSchemeDataPatterns on SteelBeamSchemeData {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _SteelBeamSchemeData value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _SteelBeamSchemeData() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _SteelBeamSchemeData value)  $default,){
final _that = this;
switch (_that) {
case _SteelBeamSchemeData():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _SteelBeamSchemeData value)?  $default,){
final _that = this;
switch (_that) {
case _SteelBeamSchemeData() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String usage,  double finish,  double service,  double liveLoad,  String loadingTableId,  double slabThickness,  double compositeActionFactor,  double shortSpan,  double longSpan,  int bays,  double strZone,  double fsy,  String mainBeamSection,  String secBeamSection,  String steelBeamSchemeId,  String calsLog)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _SteelBeamSchemeData() when $default != null:
return $default(_that.usage,_that.finish,_that.service,_that.liveLoad,_that.loadingTableId,_that.slabThickness,_that.compositeActionFactor,_that.shortSpan,_that.longSpan,_that.bays,_that.strZone,_that.fsy,_that.mainBeamSection,_that.secBeamSection,_that.steelBeamSchemeId,_that.calsLog);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String usage,  double finish,  double service,  double liveLoad,  String loadingTableId,  double slabThickness,  double compositeActionFactor,  double shortSpan,  double longSpan,  int bays,  double strZone,  double fsy,  String mainBeamSection,  String secBeamSection,  String steelBeamSchemeId,  String calsLog)  $default,) {final _that = this;
switch (_that) {
case _SteelBeamSchemeData():
return $default(_that.usage,_that.finish,_that.service,_that.liveLoad,_that.loadingTableId,_that.slabThickness,_that.compositeActionFactor,_that.shortSpan,_that.longSpan,_that.bays,_that.strZone,_that.fsy,_that.mainBeamSection,_that.secBeamSection,_that.steelBeamSchemeId,_that.calsLog);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String usage,  double finish,  double service,  double liveLoad,  String loadingTableId,  double slabThickness,  double compositeActionFactor,  double shortSpan,  double longSpan,  int bays,  double strZone,  double fsy,  String mainBeamSection,  String secBeamSection,  String steelBeamSchemeId,  String calsLog)?  $default,) {final _that = this;
switch (_that) {
case _SteelBeamSchemeData() when $default != null:
return $default(_that.usage,_that.finish,_that.service,_that.liveLoad,_that.loadingTableId,_that.slabThickness,_that.compositeActionFactor,_that.shortSpan,_that.longSpan,_that.bays,_that.strZone,_that.fsy,_that.mainBeamSection,_that.secBeamSection,_that.steelBeamSchemeId,_that.calsLog);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _SteelBeamSchemeData extends SteelBeamSchemeData {
   _SteelBeamSchemeData({this.usage = '', this.finish = 0.0, this.service = 0.0, this.liveLoad = 0.0, this.loadingTableId = '', this.slabThickness = 0.0, this.compositeActionFactor = 0.0, this.shortSpan = 5.0, this.longSpan = 12.0, this.bays = 2, this.strZone = 500.0, this.fsy = 355.0, this.mainBeamSection = '', this.secBeamSection = '', this.steelBeamSchemeId = '', this.calsLog = ''}): super._();
  factory _SteelBeamSchemeData.fromJson(Map<String, dynamic> json) => _$SteelBeamSchemeDataFromJson(json);

@override@JsonKey() final  String usage;
@override@JsonKey() final  double finish;
@override@JsonKey() final  double service;
@override@JsonKey() final  double liveLoad;
@override@JsonKey() final  String loadingTableId;
@override@JsonKey() final  double slabThickness;
@override@JsonKey() final  double compositeActionFactor;
@override@JsonKey() final  double shortSpan;
@override@JsonKey() final  double longSpan;
@override@JsonKey() final  int bays;
@override@JsonKey() final  double strZone;
@override@JsonKey() final  double fsy;
@override@JsonKey() final  String mainBeamSection;
@override@JsonKey() final  String secBeamSection;
@override@JsonKey() final  String steelBeamSchemeId;
//will be overriden  as soon as new instance created
@override@JsonKey() final  String calsLog;

/// Create a copy of SteelBeamSchemeData
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$SteelBeamSchemeDataCopyWith<_SteelBeamSchemeData> get copyWith => __$SteelBeamSchemeDataCopyWithImpl<_SteelBeamSchemeData>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$SteelBeamSchemeDataToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _SteelBeamSchemeData&&(identical(other.usage, usage) || other.usage == usage)&&(identical(other.finish, finish) || other.finish == finish)&&(identical(other.service, service) || other.service == service)&&(identical(other.liveLoad, liveLoad) || other.liveLoad == liveLoad)&&(identical(other.loadingTableId, loadingTableId) || other.loadingTableId == loadingTableId)&&(identical(other.slabThickness, slabThickness) || other.slabThickness == slabThickness)&&(identical(other.compositeActionFactor, compositeActionFactor) || other.compositeActionFactor == compositeActionFactor)&&(identical(other.shortSpan, shortSpan) || other.shortSpan == shortSpan)&&(identical(other.longSpan, longSpan) || other.longSpan == longSpan)&&(identical(other.bays, bays) || other.bays == bays)&&(identical(other.strZone, strZone) || other.strZone == strZone)&&(identical(other.fsy, fsy) || other.fsy == fsy)&&(identical(other.mainBeamSection, mainBeamSection) || other.mainBeamSection == mainBeamSection)&&(identical(other.secBeamSection, secBeamSection) || other.secBeamSection == secBeamSection)&&(identical(other.steelBeamSchemeId, steelBeamSchemeId) || other.steelBeamSchemeId == steelBeamSchemeId)&&(identical(other.calsLog, calsLog) || other.calsLog == calsLog));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,usage,finish,service,liveLoad,loadingTableId,slabThickness,compositeActionFactor,shortSpan,longSpan,bays,strZone,fsy,mainBeamSection,secBeamSection,steelBeamSchemeId,calsLog);

@override
String toString() {
  return 'SteelBeamSchemeData(usage: $usage, finish: $finish, service: $service, liveLoad: $liveLoad, loadingTableId: $loadingTableId, slabThickness: $slabThickness, compositeActionFactor: $compositeActionFactor, shortSpan: $shortSpan, longSpan: $longSpan, bays: $bays, strZone: $strZone, fsy: $fsy, mainBeamSection: $mainBeamSection, secBeamSection: $secBeamSection, steelBeamSchemeId: $steelBeamSchemeId, calsLog: $calsLog)';
}


}

/// @nodoc
abstract mixin class _$SteelBeamSchemeDataCopyWith<$Res> implements $SteelBeamSchemeDataCopyWith<$Res> {
  factory _$SteelBeamSchemeDataCopyWith(_SteelBeamSchemeData value, $Res Function(_SteelBeamSchemeData) _then) = __$SteelBeamSchemeDataCopyWithImpl;
@override @useResult
$Res call({
 String usage, double finish, double service, double liveLoad, String loadingTableId, double slabThickness, double compositeActionFactor, double shortSpan, double longSpan, int bays, double strZone, double fsy, String mainBeamSection, String secBeamSection, String steelBeamSchemeId, String calsLog
});




}
/// @nodoc
class __$SteelBeamSchemeDataCopyWithImpl<$Res>
    implements _$SteelBeamSchemeDataCopyWith<$Res> {
  __$SteelBeamSchemeDataCopyWithImpl(this._self, this._then);

  final _SteelBeamSchemeData _self;
  final $Res Function(_SteelBeamSchemeData) _then;

/// Create a copy of SteelBeamSchemeData
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? usage = null,Object? finish = null,Object? service = null,Object? liveLoad = null,Object? loadingTableId = null,Object? slabThickness = null,Object? compositeActionFactor = null,Object? shortSpan = null,Object? longSpan = null,Object? bays = null,Object? strZone = null,Object? fsy = null,Object? mainBeamSection = null,Object? secBeamSection = null,Object? steelBeamSchemeId = null,Object? calsLog = null,}) {
  return _then(_SteelBeamSchemeData(
usage: null == usage ? _self.usage : usage // ignore: cast_nullable_to_non_nullable
as String,finish: null == finish ? _self.finish : finish // ignore: cast_nullable_to_non_nullable
as double,service: null == service ? _self.service : service // ignore: cast_nullable_to_non_nullable
as double,liveLoad: null == liveLoad ? _self.liveLoad : liveLoad // ignore: cast_nullable_to_non_nullable
as double,loadingTableId: null == loadingTableId ? _self.loadingTableId : loadingTableId // ignore: cast_nullable_to_non_nullable
as String,slabThickness: null == slabThickness ? _self.slabThickness : slabThickness // ignore: cast_nullable_to_non_nullable
as double,compositeActionFactor: null == compositeActionFactor ? _self.compositeActionFactor : compositeActionFactor // ignore: cast_nullable_to_non_nullable
as double,shortSpan: null == shortSpan ? _self.shortSpan : shortSpan // ignore: cast_nullable_to_non_nullable
as double,longSpan: null == longSpan ? _self.longSpan : longSpan // ignore: cast_nullable_to_non_nullable
as double,bays: null == bays ? _self.bays : bays // ignore: cast_nullable_to_non_nullable
as int,strZone: null == strZone ? _self.strZone : strZone // ignore: cast_nullable_to_non_nullable
as double,fsy: null == fsy ? _self.fsy : fsy // ignore: cast_nullable_to_non_nullable
as double,mainBeamSection: null == mainBeamSection ? _self.mainBeamSection : mainBeamSection // ignore: cast_nullable_to_non_nullable
as String,secBeamSection: null == secBeamSection ? _self.secBeamSection : secBeamSection // ignore: cast_nullable_to_non_nullable
as String,steelBeamSchemeId: null == steelBeamSchemeId ? _self.steelBeamSchemeId : steelBeamSchemeId // ignore: cast_nullable_to_non_nullable
as String,calsLog: null == calsLog ? _self.calsLog : calsLog // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

// dart format on
