import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:structify/data_layer/app_database.dart';
import 'package:drift_db_viewer/drift_db_viewer.dart';

//below for printing
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:printing/printing.dart';
import 'package:structify/misc/custom_func.dart';
import 'package:structify/presentation_layer/small_elements/transfer_beam_scheme_input_ui.dart';
import 'package:structify/presentation_layer/small_elements/transfer_beam_scheme_summary.dart';

// import 'package:flutter/services.dart';

//domain layer
import '../../domain_layer/preferences.dart';

// presentation layer
// import '../presentation_layer/homescreen.dart';
import '../small_elements/dessign_assumption.dart';
import '../small_elements/button/function_button.dart';
import 'homescreen.dart';
// import '../small_elements/custom_stateful_double_input.dart';
// import '../small_elements/custom_stateful_text_input.dart';

class TransferScheme extends ConsumerWidget {
  TransferScheme({super.key});

  final GlobalKey _globalKey = GlobalKey();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final ColorScheme colorScheme = Theme.of(context).colorScheme;
    final TextTheme textTheme = Theme.of(context).textTheme;
    final globalData = ref.watch(globalDataControllerProvider);
    final transferBeamSchemeInput = ref.watch(
      transferBeamSchemeInputControllerProvider,
    );
    ref.watch(appWatcherProvider);
    return globalData.when(
      data: (globalData) {
        return transferBeamSchemeInput.when(
          data: (input) {
            return Scaffold(
              backgroundColor: colorScheme.surface,
              body: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        // FunctionButton(
                        //   labelIcon: Icon(Icons.print_outlined),
                        //   labelText: 'Print',
                        //   onTap: (isPressed) async {
                        //     // await _exportListToPdf(
                        //     //   context,
                        //     //   ref,
                        //     //   rowsPerPage: 13,
                        //     // );
                        //   },
                        // ),
                        // SizedBox(width: 5.0),
                        FunctionButton(
                          labelIcon: Icon(Icons.data_array_outlined),
                          labelText: 'Assumption',
                          onTap: (isPressed) async {
                            showAssumption(
                              context,
                              DesignAssumption(
                                isExpanded: true,
                                textStyle: textTheme.bodyMedium!.copyWith(
                                  color: colorScheme.onInverseSurface,
                                ),
                                titleStyle: textTheme.titleLarge!.copyWith(
                                  color: colorScheme.onInverseSurface,
                                ),
                                title: 'Assumption',
                                assumptions: [
                                  'HKCoP for Structural Use of Concrete 2013 (HKCoPSUC2013)',
                                  'fy = 500 MPa',
                                  'Flexural design: cl.6.1.2.4(c) | k-limit: <10% moment redistribution',
                                  'Shear design: cl.******* | vc: table 6.3, notes 1 to 3',
                                  'Deflection Limit: L/d <= 20 (simply-supoorted beam)',
                                  'effective d = str zone - cover - links dia. - (highest layer bar dia. + (layersNos - 1 )*(T40 dia + spacer dia.))/2',
                                  'spacer dia. = T16 dia.',
                                ],
                                tooltipText:
                                    '------if flexural fails------\n-adjust preferred k-value OR\n-adjust max beam width OR\n-adjust max rebar layer OR\n-adjust concrete grade\n-if possible, adjust str zone (hence reduce str zone a bit from each of other floors)\n'
                                    '------if shear fails------\n-adjust the min links spacing OR\n-if possible, adjust str zone (hence reduce str zone a bit from each of other floors)\n'
                                    '------If deflection fails------\n-use pre-chamber OR\n-add compression bar in detailed design stage OR\n-check actual deflection in detailed design stage',
                              ),
                            );
                          },
                        ),
                        SizedBox(width: 5.0),
                        FunctionButton(
                          labelIcon: Icon(Icons.data_array_outlined),
                          labelText: 'Summary',
                          onTap: (isPressed) async {
                            showSummary(context);
                          },
                        ),
                        SizedBox(width: 5.0),
                        FunctionButton(
                          labelIcon: Icon(Icons.data_array_outlined),
                          labelText: 'Data',
                          onTap: (isPressed) async {
                            final db =
                                AppDatabase(); //This should be a singleton
                            Navigator.of(context).push(
                              MaterialPageRoute(
                                builder: (context) => DriftDbViewer(db),
                              ),
                            );
                          },
                        ),
                      ],
                    ),
                  ),
                  Flexible(
                    child: Row(
                      children: [
                        Flexible(
                          flex: 3,
                          child: TransferBeamSchemeInputUi(isExpanded: true),
                        ),
                        Flexible(
                          flex: 4,
                          child: TransferBeamSchemeSummary(isExpanded: true),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              // floatingActionButton: FloatingActionButton(
              //   backgroundColor: colorScheme.secondaryContainer.withAlpha(150),
              //   child: Icon(
              //     Icons.add_circle_outline_outlined,
              //     color: colorScheme.onSecondaryContainer.withAlpha(150),
              //   ),
              //   onPressed: () async{
              //     await ref
              //         .read(transferBeamSchemeInputControllerProvider.notifier)
              //         .addEmptytable();
              //   },
              // ),
            );
          },
          error: (error, stackTrace) => Text('Error: $error'),
          loading: () => CircularProgressIndicator(),
        );
      },
      error: (error, stackTrace) {
        return Text('Error: $error');
      },
      loading: () {
        return CircularProgressIndicator();
      },
    );
  }

  Future<void> _exportListToPdf(
    BuildContext context,
    WidgetRef ref, {
    int rowsPerPage = 10,
  }) async {
    final pdf = pw.Document();
    final columnSchemes = ref.read(columnSchemeDataControllerProvider);
    columnSchemes.when(
      data: (data) {
        int counter = 0;
        do {
          if (counter + rowsPerPage <= data.length) {
            pdf.addPage(
              _customPage(
                context,
                ref,
                startFrom: counter,
                rowsPerPage: rowsPerPage,
              ),
            );
          } else {
            pdf.addPage(
              _customPage(
                context,
                ref,
                startFrom: counter,
                rowsPerPage: data.length - counter,
              ),
            );
          }
          counter += rowsPerPage;
        } while (counter <= data.length);
      },
      error: (error, stackTrace) => {},
      loading: () {},
    );

    await Printing.layoutPdf(
      onLayout: (PdfPageFormat format) async => pdf.save(),
    );
  }
}

pw.Page _customPage(
  BuildContext context,
  WidgetRef ref, {
  int startFrom = 0,
  int? rowsPerPage,
}) {
  // final textStyle = Theme.of(context).textTheme.bodySmall;
  return pw.Page(
    build: (pw.Context context) {
      final columnSchemes = ref.read(columnSchemeDataControllerProvider);
      final globaldata = ref.read(globalDataControllerProvider);

      return columnSchemes.when(
        data: (tables) {
          return globaldata.when(
            data: (data) {
              final double titleFontSize = 8;
              final double normalFontSize = 8;

              return pw.Center(
                child: pw.Column(
                  mainAxisAlignment: pw.MainAxisAlignment.start,
                  children: [
                    ...List.generate(rowsPerPage ?? tables.length - startFrom, (
                      index,
                    ) {
                      NumberFormat f0 = NumberFormat('0');
                      NumberFormat f3 = NumberFormat('0.000');

                      late List<String> unit;
                      switch (data.unit) {
                        case 'metrics':
                          unit = PreferredUnit.metrics;
                          break;
                        case 'imperial':
                          unit = PreferredUnit.imperial;
                          break;
                        default:
                          unit = PreferredUnit.metrics;
                      }
                      return pw.DefaultTextStyle(
                        style: pw.TextStyle(fontSize: normalFontSize),
                        child: pw.Column(
                          children: [
                            pw.Row(
                              children: [
                                pw.Text(
                                  '${index + startFrom + 1}',
                                  style: pw.TextStyle(
                                    fontSize: titleFontSize,
                                    fontWeight: pw.FontWeight.bold,
                                  ),
                                ),

                                _customVerticalDivider(),

                                pw.Column(
                                  children: [
                                    pw.Text(
                                      'Size [${unit[4]}]:',
                                      style: pw.TextStyle(
                                        fontSize: titleFontSize,
                                        fontWeight: pw.FontWeight.bold,
                                      ),
                                    ),
                                    pw.Text(
                                      'fcu [${unit[5]}]:',
                                      style: pw.TextStyle(
                                        fontSize: titleFontSize,
                                        fontWeight: pw.FontWeight.bold,
                                      ),
                                    ),
                                  ],
                                ),
                                pw.SizedBox(width: 5.0),
                                pw.Column(
                                  children: [
                                    pw.Text(
                                      f0.format(tables[index + startFrom].size),
                                      style: pw.TextStyle(
                                        fontSize: titleFontSize,
                                        fontWeight: pw.FontWeight.bold,
                                      ),
                                    ),
                                    pw.Text(
                                      f0.format(tables[index + startFrom].fcu),
                                      style: pw.TextStyle(
                                        fontSize: titleFontSize,
                                        fontWeight: pw.FontWeight.bold,
                                      ),
                                    ),
                                  ],
                                ),

                                _customVerticalDivider(),

                                pw.Column(
                                  crossAxisAlignment:
                                      pw.CrossAxisAlignment.start,

                                  children: [
                                    pw.Text('SLS Load [${unit[0]}]: '),
                                    pw.Text('ULS Load [${unit[0]}]: '),
                                  ],
                                ),
                                pw.SizedBox(width: 5.0),

                                pw.Column(
                                  crossAxisAlignment:
                                      pw.CrossAxisAlignment.start,

                                  children: [
                                    pw.Text(
                                      f0.format(
                                        tables[index + startFrom].slsLoad,
                                      ),
                                    ),
                                    pw.Text(
                                      f0.format(
                                        tables[index + startFrom].ulsLoad,
                                      ),
                                    ),
                                  ],
                                ),

                                _customVerticalDivider(),

                                pw.Column(
                                  children: [
                                    pw.Text(
                                      'Square Column',
                                      style: pw.TextStyle(
                                        fontSize: titleFontSize,
                                        fontWeight: pw.FontWeight.bold,
                                      ),
                                    ),
                                    pw.Row(
                                      children: [
                                        pw.Column(
                                          crossAxisAlignment:
                                              pw.CrossAxisAlignment.start,

                                          children: [
                                            pw.Text(
                                              'Capacity Load [${unit[0]}]: ',
                                            ),
                                            pw.Text('Rebar : '),
                                            pw.Text('Steel Ratio : '),
                                          ],
                                        ),
                                        pw.SizedBox(width: 5.0),

                                        pw.Column(
                                          crossAxisAlignment:
                                              pw.CrossAxisAlignment.start,

                                          children: [
                                            pw.Text(
                                              f0.format(
                                                tables[index + startFrom]
                                                    .axialCapacitySquare,
                                              ),
                                            ),
                                            pw.Text(
                                              tables[index + startFrom]
                                                  .mainBarSquare,
                                            ),
                                            pw.Text(
                                              '${tables[index + startFrom].steelRatioSqaure}',
                                            ),
                                          ],
                                        ),
                                      ],
                                    ),
                                  ],
                                ),
                                _customVerticalDivider(),
                                pw.Column(
                                  children: [
                                    pw.Text(
                                      'Circular Column',
                                      style: pw.TextStyle(
                                        fontSize: titleFontSize,
                                        fontWeight: pw.FontWeight.bold,
                                      ),
                                    ),
                                    pw.Row(
                                      children: [
                                        pw.Column(
                                          crossAxisAlignment:
                                              pw.CrossAxisAlignment.start,

                                          children: [
                                            pw.Text(
                                              'Capacity Load [${unit[0]}]: ',
                                            ),
                                            pw.Text('Rebar : '),
                                            pw.Text('Steel Ratio : '),
                                          ],
                                        ),
                                        pw.SizedBox(width: 5.0),

                                        pw.Column(
                                          crossAxisAlignment:
                                              pw.CrossAxisAlignment.start,

                                          children: [
                                            pw.Text(
                                              f0.format(
                                                tables[index + startFrom]
                                                    .axialCapacityCircle,
                                              ),
                                            ),
                                            pw.Text(
                                              tables[index + startFrom]
                                                  .mainBarCircle,
                                            ),
                                            pw.Text(
                                              '${tables[index + startFrom].steelRatioCircle}',
                                            ),
                                          ],
                                        ),
                                      ],
                                    ),
                                  ],
                                ),
                              ],
                            ),
                            _customDivider(),
                          ],
                        ),
                      );
                    }),
                  ],
                ),
              );
            },
            error: (error, stacktrace) => pw.Text('Error: $error'),
            loading: () => pw.Text('Loading'),
          );
        },
        error: (error, stackTrace) => pw.Text('Error: $error'),
        loading: () => pw.Text('loading'),
      );
    },
  );
}

pw.Widget _customVerticalDivider() {
  return pw.Row(
    children: [
      pw.SizedBox(width: 15.0),
      pw.Container(width: 1.0, height: 30, color: PdfColors.grey400),
      pw.SizedBox(width: 15.0),
    ],
  );
}

pw.Widget _customDivider() {
  return pw.Divider(color: PdfColors.grey400);
}
