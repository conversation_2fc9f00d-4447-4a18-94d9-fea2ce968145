import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:structify/domain_layer/column_scheme_input_global.dart';

//presentation layer
import '../screen/homescreen.dart';

// domain layer

part 'column_scheme_input_global_controller.g.dart';

@riverpod
class ColumnSchemeInputGlobalController
    extends _$ColumnSchemeInputGlobalController {
  @override
  FutureOr<ColumnSchemeInputGlobal> build() async {
    // print('Build: Column Scheme Input Global');
    ColumnSchemeInputGlobal columnSchemeGlobalInput =
        await ref
            .read(appDatabaseControllerProvider.notifier)
            .queryColumnSchemeInputGlobal();
    return columnSchemeGlobalInput;
  }

  // final globalData =
  //     ref.read(appDatabaseControllerProvider.notifier).queryGlobalData();
  // return globalData;

  // Future<void> addEmptytable() async {
  //   final x = await future;
  //   final id = await _generateTaskID();
  //   state = AsyncData([...x, ColumnSchemeInput(columnSchemeInputId: id)]);
  // }

  // Future<void> deleteTable(String id) async {
  //   final x = await future;
  //   x.removeWhere((item) => item.columnSchemeInputId == id);
  //   state = AsyncData(x);
  // }

  // Future<void> insertEmptyTable(int index) async {
  //   final x = await future;
  //   final id = await _generateTaskID();
  //   x.insert(index+1, LoadingTable(loadingTableId: id));
  //   state = AsyncData(x);
  // }

  // Future<void> copyAndInsertTable(int index) async {
  //   final x = await future;
  //   final id = await _generateTaskID();
  //   x.insert(index + 1, x[index].copyWith(columnSchemeInputId: id));
  //   state = AsyncData(x);
  // }

  // Future<String> _generateTaskID() async {
  //   String newID = nanoid(alphabet: Alphabet.noDoppelgangerSafe);
  //   final x = await future;
  //   final Set<String> columnSchemeInputIds =
  //       x.map((item) => item.columnSchemeInputId).toSet();
  //   // final Set<String> taskIDs = ref.read(_taskIDsProvider);
  //   while (columnSchemeInputIds.contains(newID)) {
  //     newID = nanoid(alphabet: Alphabet.noDoppelgangerSafe);
  //   }
  //   return newID;
  // }

  Future<void> updateTable({
    int? columnSchemeInputGlobalId,
    double? cover,
    double? minColumnSize,
    double? sizeIncrement,
    int? iterationSteps,
    String? concreteGrade,
    double? minSteelRatio,
    double? maxSteelRatio,
    double? safetyFactor,
    double? minClearS, 
    bool? useSlabSelected,
  }) async {
    final x = await future;
    ColumnSchemeInputGlobal newState = x.copyWith(
      columnSchemeInputGlobalId:
          columnSchemeInputGlobalId ?? x.columnSchemeInputGlobalId,
      cover: cover ?? x.cover,
      minColumnSize: minColumnSize ?? x.minColumnSize,
      sizeIncrement: sizeIncrement ?? x.sizeIncrement,
      iterationSteps: iterationSteps ?? x.iterationSteps,
      concreteGrade: concreteGrade ?? x.concreteGrade,
      minSteelRatio: minSteelRatio ?? x.minSteelRatio,
      maxSteelRatio: maxSteelRatio ?? x.maxSteelRatio,
      safetyFactor: safetyFactor ?? x.safetyFactor,
      minClearS: minClearS ?? x.minClearS, 
      useSlabSelected: useSlabSelected ?? x.useSlabSelected,
    );
    // Update the state only if there are changes
    state = AsyncData(newState);
  }
}
