// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'transfer_beam_scheme_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_TransferBeamSchemeData _$TransferBeamSchemeDataFromJson(
  Map<String, dynamic> json,
) => _TransferBeamSchemeData(
  usage: json['usage'] as String? ?? '',
  slabThickness: (json['slabThickness'] as num?)?.toDouble() ?? 150.0,
  span: (json['span'] as num?)?.toDouble() ?? 1.0,
  loadWidth: (json['loadWidth'] as num?)?.toDouble() ?? 1.0,
  strZone: (json['strZone'] as num?)?.toDouble() ?? 500.0,
  fcu: (json['fcu'] as num?)?.toDouble() ?? 45.0,
  cover: (json['cover'] as num?)?.toDouble() ?? 40.0,
  mainWidth: (json['mainWidth'] as num?)?.toDouble() ?? 200.0,
  mainTopBar: json['mainTopBar'] as String? ?? '',
  mainBottomBar: json['mainBottomBar'] as String? ?? '',
  mainLinks: json['mainLinks'] as String? ?? '',
  id: json['id'] as String? ?? '1',
  calsLog: json['calsLog'] as String? ?? '',
  beamForce: json['beamForce'] as String? ?? '',
);

Map<String, dynamic> _$TransferBeamSchemeDataToJson(
  _TransferBeamSchemeData instance,
) => <String, dynamic>{
  'usage': instance.usage,
  'slabThickness': instance.slabThickness,
  'span': instance.span,
  'loadWidth': instance.loadWidth,
  'strZone': instance.strZone,
  'fcu': instance.fcu,
  'cover': instance.cover,
  'mainWidth': instance.mainWidth,
  'mainTopBar': instance.mainTopBar,
  'mainBottomBar': instance.mainBottomBar,
  'mainLinks': instance.mainLinks,
  'id': instance.id,
  'calsLog': instance.calsLog,
  'beamForce': instance.beamForce,
};
