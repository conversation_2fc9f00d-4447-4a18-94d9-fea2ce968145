// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'column_scheme_data.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$ColumnSchemeData {

 String get columnSchemeDataId;//will be overriden  as soon as new instance created
 double get sdl; double get ll; double get slsLoad; double get ulsLoad; double get size; double get fcu; double get cover; double get axialCapacitySquare; String get mainBarSquare; double get steelRatioSqaure; double get axialCapacityCircle; String get mainBarCircle; double get steelRatioCircle; String get calsLog; bool get isSelected;
/// Create a copy of ColumnSchemeData
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ColumnSchemeDataCopyWith<ColumnSchemeData> get copyWith => _$ColumnSchemeDataCopyWithImpl<ColumnSchemeData>(this as ColumnSchemeData, _$identity);

  /// Serializes this ColumnSchemeData to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ColumnSchemeData&&(identical(other.columnSchemeDataId, columnSchemeDataId) || other.columnSchemeDataId == columnSchemeDataId)&&(identical(other.sdl, sdl) || other.sdl == sdl)&&(identical(other.ll, ll) || other.ll == ll)&&(identical(other.slsLoad, slsLoad) || other.slsLoad == slsLoad)&&(identical(other.ulsLoad, ulsLoad) || other.ulsLoad == ulsLoad)&&(identical(other.size, size) || other.size == size)&&(identical(other.fcu, fcu) || other.fcu == fcu)&&(identical(other.cover, cover) || other.cover == cover)&&(identical(other.axialCapacitySquare, axialCapacitySquare) || other.axialCapacitySquare == axialCapacitySquare)&&(identical(other.mainBarSquare, mainBarSquare) || other.mainBarSquare == mainBarSquare)&&(identical(other.steelRatioSqaure, steelRatioSqaure) || other.steelRatioSqaure == steelRatioSqaure)&&(identical(other.axialCapacityCircle, axialCapacityCircle) || other.axialCapacityCircle == axialCapacityCircle)&&(identical(other.mainBarCircle, mainBarCircle) || other.mainBarCircle == mainBarCircle)&&(identical(other.steelRatioCircle, steelRatioCircle) || other.steelRatioCircle == steelRatioCircle)&&(identical(other.calsLog, calsLog) || other.calsLog == calsLog)&&(identical(other.isSelected, isSelected) || other.isSelected == isSelected));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,columnSchemeDataId,sdl,ll,slsLoad,ulsLoad,size,fcu,cover,axialCapacitySquare,mainBarSquare,steelRatioSqaure,axialCapacityCircle,mainBarCircle,steelRatioCircle,calsLog,isSelected);

@override
String toString() {
  return 'ColumnSchemeData(columnSchemeDataId: $columnSchemeDataId, sdl: $sdl, ll: $ll, slsLoad: $slsLoad, ulsLoad: $ulsLoad, size: $size, fcu: $fcu, cover: $cover, axialCapacitySquare: $axialCapacitySquare, mainBarSquare: $mainBarSquare, steelRatioSqaure: $steelRatioSqaure, axialCapacityCircle: $axialCapacityCircle, mainBarCircle: $mainBarCircle, steelRatioCircle: $steelRatioCircle, calsLog: $calsLog, isSelected: $isSelected)';
}


}

/// @nodoc
abstract mixin class $ColumnSchemeDataCopyWith<$Res>  {
  factory $ColumnSchemeDataCopyWith(ColumnSchemeData value, $Res Function(ColumnSchemeData) _then) = _$ColumnSchemeDataCopyWithImpl;
@useResult
$Res call({
 String columnSchemeDataId, double sdl, double ll, double slsLoad, double ulsLoad, double size, double fcu, double cover, double axialCapacitySquare, String mainBarSquare, double steelRatioSqaure, double axialCapacityCircle, String mainBarCircle, double steelRatioCircle, String calsLog, bool isSelected
});




}
/// @nodoc
class _$ColumnSchemeDataCopyWithImpl<$Res>
    implements $ColumnSchemeDataCopyWith<$Res> {
  _$ColumnSchemeDataCopyWithImpl(this._self, this._then);

  final ColumnSchemeData _self;
  final $Res Function(ColumnSchemeData) _then;

/// Create a copy of ColumnSchemeData
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? columnSchemeDataId = null,Object? sdl = null,Object? ll = null,Object? slsLoad = null,Object? ulsLoad = null,Object? size = null,Object? fcu = null,Object? cover = null,Object? axialCapacitySquare = null,Object? mainBarSquare = null,Object? steelRatioSqaure = null,Object? axialCapacityCircle = null,Object? mainBarCircle = null,Object? steelRatioCircle = null,Object? calsLog = null,Object? isSelected = null,}) {
  return _then(_self.copyWith(
columnSchemeDataId: null == columnSchemeDataId ? _self.columnSchemeDataId : columnSchemeDataId // ignore: cast_nullable_to_non_nullable
as String,sdl: null == sdl ? _self.sdl : sdl // ignore: cast_nullable_to_non_nullable
as double,ll: null == ll ? _self.ll : ll // ignore: cast_nullable_to_non_nullable
as double,slsLoad: null == slsLoad ? _self.slsLoad : slsLoad // ignore: cast_nullable_to_non_nullable
as double,ulsLoad: null == ulsLoad ? _self.ulsLoad : ulsLoad // ignore: cast_nullable_to_non_nullable
as double,size: null == size ? _self.size : size // ignore: cast_nullable_to_non_nullable
as double,fcu: null == fcu ? _self.fcu : fcu // ignore: cast_nullable_to_non_nullable
as double,cover: null == cover ? _self.cover : cover // ignore: cast_nullable_to_non_nullable
as double,axialCapacitySquare: null == axialCapacitySquare ? _self.axialCapacitySquare : axialCapacitySquare // ignore: cast_nullable_to_non_nullable
as double,mainBarSquare: null == mainBarSquare ? _self.mainBarSquare : mainBarSquare // ignore: cast_nullable_to_non_nullable
as String,steelRatioSqaure: null == steelRatioSqaure ? _self.steelRatioSqaure : steelRatioSqaure // ignore: cast_nullable_to_non_nullable
as double,axialCapacityCircle: null == axialCapacityCircle ? _self.axialCapacityCircle : axialCapacityCircle // ignore: cast_nullable_to_non_nullable
as double,mainBarCircle: null == mainBarCircle ? _self.mainBarCircle : mainBarCircle // ignore: cast_nullable_to_non_nullable
as String,steelRatioCircle: null == steelRatioCircle ? _self.steelRatioCircle : steelRatioCircle // ignore: cast_nullable_to_non_nullable
as double,calsLog: null == calsLog ? _self.calsLog : calsLog // ignore: cast_nullable_to_non_nullable
as String,isSelected: null == isSelected ? _self.isSelected : isSelected // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}

}


/// Adds pattern-matching-related methods to [ColumnSchemeData].
extension ColumnSchemeDataPatterns on ColumnSchemeData {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _ColumnSchemeData value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _ColumnSchemeData() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _ColumnSchemeData value)  $default,){
final _that = this;
switch (_that) {
case _ColumnSchemeData():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _ColumnSchemeData value)?  $default,){
final _that = this;
switch (_that) {
case _ColumnSchemeData() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String columnSchemeDataId,  double sdl,  double ll,  double slsLoad,  double ulsLoad,  double size,  double fcu,  double cover,  double axialCapacitySquare,  String mainBarSquare,  double steelRatioSqaure,  double axialCapacityCircle,  String mainBarCircle,  double steelRatioCircle,  String calsLog,  bool isSelected)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _ColumnSchemeData() when $default != null:
return $default(_that.columnSchemeDataId,_that.sdl,_that.ll,_that.slsLoad,_that.ulsLoad,_that.size,_that.fcu,_that.cover,_that.axialCapacitySquare,_that.mainBarSquare,_that.steelRatioSqaure,_that.axialCapacityCircle,_that.mainBarCircle,_that.steelRatioCircle,_that.calsLog,_that.isSelected);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String columnSchemeDataId,  double sdl,  double ll,  double slsLoad,  double ulsLoad,  double size,  double fcu,  double cover,  double axialCapacitySquare,  String mainBarSquare,  double steelRatioSqaure,  double axialCapacityCircle,  String mainBarCircle,  double steelRatioCircle,  String calsLog,  bool isSelected)  $default,) {final _that = this;
switch (_that) {
case _ColumnSchemeData():
return $default(_that.columnSchemeDataId,_that.sdl,_that.ll,_that.slsLoad,_that.ulsLoad,_that.size,_that.fcu,_that.cover,_that.axialCapacitySquare,_that.mainBarSquare,_that.steelRatioSqaure,_that.axialCapacityCircle,_that.mainBarCircle,_that.steelRatioCircle,_that.calsLog,_that.isSelected);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String columnSchemeDataId,  double sdl,  double ll,  double slsLoad,  double ulsLoad,  double size,  double fcu,  double cover,  double axialCapacitySquare,  String mainBarSquare,  double steelRatioSqaure,  double axialCapacityCircle,  String mainBarCircle,  double steelRatioCircle,  String calsLog,  bool isSelected)?  $default,) {final _that = this;
switch (_that) {
case _ColumnSchemeData() when $default != null:
return $default(_that.columnSchemeDataId,_that.sdl,_that.ll,_that.slsLoad,_that.ulsLoad,_that.size,_that.fcu,_that.cover,_that.axialCapacitySquare,_that.mainBarSquare,_that.steelRatioSqaure,_that.axialCapacityCircle,_that.mainBarCircle,_that.steelRatioCircle,_that.calsLog,_that.isSelected);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _ColumnSchemeData extends ColumnSchemeData {
   _ColumnSchemeData({this.columnSchemeDataId = '', this.sdl = 0.0, this.ll = 0.0, this.slsLoad = 0.0, this.ulsLoad = 0.0, this.size = 500.0, this.fcu = 45.0, this.cover = 40.0, this.axialCapacitySquare = 0.0, this.mainBarSquare = '', this.steelRatioSqaure = 0.0, this.axialCapacityCircle = 0.0, this.mainBarCircle = '', this.steelRatioCircle = 0.0, this.calsLog = '', this.isSelected = false}): super._();
  factory _ColumnSchemeData.fromJson(Map<String, dynamic> json) => _$ColumnSchemeDataFromJson(json);

@override@JsonKey() final  String columnSchemeDataId;
//will be overriden  as soon as new instance created
@override@JsonKey() final  double sdl;
@override@JsonKey() final  double ll;
@override@JsonKey() final  double slsLoad;
@override@JsonKey() final  double ulsLoad;
@override@JsonKey() final  double size;
@override@JsonKey() final  double fcu;
@override@JsonKey() final  double cover;
@override@JsonKey() final  double axialCapacitySquare;
@override@JsonKey() final  String mainBarSquare;
@override@JsonKey() final  double steelRatioSqaure;
@override@JsonKey() final  double axialCapacityCircle;
@override@JsonKey() final  String mainBarCircle;
@override@JsonKey() final  double steelRatioCircle;
@override@JsonKey() final  String calsLog;
@override@JsonKey() final  bool isSelected;

/// Create a copy of ColumnSchemeData
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ColumnSchemeDataCopyWith<_ColumnSchemeData> get copyWith => __$ColumnSchemeDataCopyWithImpl<_ColumnSchemeData>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ColumnSchemeDataToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ColumnSchemeData&&(identical(other.columnSchemeDataId, columnSchemeDataId) || other.columnSchemeDataId == columnSchemeDataId)&&(identical(other.sdl, sdl) || other.sdl == sdl)&&(identical(other.ll, ll) || other.ll == ll)&&(identical(other.slsLoad, slsLoad) || other.slsLoad == slsLoad)&&(identical(other.ulsLoad, ulsLoad) || other.ulsLoad == ulsLoad)&&(identical(other.size, size) || other.size == size)&&(identical(other.fcu, fcu) || other.fcu == fcu)&&(identical(other.cover, cover) || other.cover == cover)&&(identical(other.axialCapacitySquare, axialCapacitySquare) || other.axialCapacitySquare == axialCapacitySquare)&&(identical(other.mainBarSquare, mainBarSquare) || other.mainBarSquare == mainBarSquare)&&(identical(other.steelRatioSqaure, steelRatioSqaure) || other.steelRatioSqaure == steelRatioSqaure)&&(identical(other.axialCapacityCircle, axialCapacityCircle) || other.axialCapacityCircle == axialCapacityCircle)&&(identical(other.mainBarCircle, mainBarCircle) || other.mainBarCircle == mainBarCircle)&&(identical(other.steelRatioCircle, steelRatioCircle) || other.steelRatioCircle == steelRatioCircle)&&(identical(other.calsLog, calsLog) || other.calsLog == calsLog)&&(identical(other.isSelected, isSelected) || other.isSelected == isSelected));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,columnSchemeDataId,sdl,ll,slsLoad,ulsLoad,size,fcu,cover,axialCapacitySquare,mainBarSquare,steelRatioSqaure,axialCapacityCircle,mainBarCircle,steelRatioCircle,calsLog,isSelected);

@override
String toString() {
  return 'ColumnSchemeData(columnSchemeDataId: $columnSchemeDataId, sdl: $sdl, ll: $ll, slsLoad: $slsLoad, ulsLoad: $ulsLoad, size: $size, fcu: $fcu, cover: $cover, axialCapacitySquare: $axialCapacitySquare, mainBarSquare: $mainBarSquare, steelRatioSqaure: $steelRatioSqaure, axialCapacityCircle: $axialCapacityCircle, mainBarCircle: $mainBarCircle, steelRatioCircle: $steelRatioCircle, calsLog: $calsLog, isSelected: $isSelected)';
}


}

/// @nodoc
abstract mixin class _$ColumnSchemeDataCopyWith<$Res> implements $ColumnSchemeDataCopyWith<$Res> {
  factory _$ColumnSchemeDataCopyWith(_ColumnSchemeData value, $Res Function(_ColumnSchemeData) _then) = __$ColumnSchemeDataCopyWithImpl;
@override @useResult
$Res call({
 String columnSchemeDataId, double sdl, double ll, double slsLoad, double ulsLoad, double size, double fcu, double cover, double axialCapacitySquare, String mainBarSquare, double steelRatioSqaure, double axialCapacityCircle, String mainBarCircle, double steelRatioCircle, String calsLog, bool isSelected
});




}
/// @nodoc
class __$ColumnSchemeDataCopyWithImpl<$Res>
    implements _$ColumnSchemeDataCopyWith<$Res> {
  __$ColumnSchemeDataCopyWithImpl(this._self, this._then);

  final _ColumnSchemeData _self;
  final $Res Function(_ColumnSchemeData) _then;

/// Create a copy of ColumnSchemeData
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? columnSchemeDataId = null,Object? sdl = null,Object? ll = null,Object? slsLoad = null,Object? ulsLoad = null,Object? size = null,Object? fcu = null,Object? cover = null,Object? axialCapacitySquare = null,Object? mainBarSquare = null,Object? steelRatioSqaure = null,Object? axialCapacityCircle = null,Object? mainBarCircle = null,Object? steelRatioCircle = null,Object? calsLog = null,Object? isSelected = null,}) {
  return _then(_ColumnSchemeData(
columnSchemeDataId: null == columnSchemeDataId ? _self.columnSchemeDataId : columnSchemeDataId // ignore: cast_nullable_to_non_nullable
as String,sdl: null == sdl ? _self.sdl : sdl // ignore: cast_nullable_to_non_nullable
as double,ll: null == ll ? _self.ll : ll // ignore: cast_nullable_to_non_nullable
as double,slsLoad: null == slsLoad ? _self.slsLoad : slsLoad // ignore: cast_nullable_to_non_nullable
as double,ulsLoad: null == ulsLoad ? _self.ulsLoad : ulsLoad // ignore: cast_nullable_to_non_nullable
as double,size: null == size ? _self.size : size // ignore: cast_nullable_to_non_nullable
as double,fcu: null == fcu ? _self.fcu : fcu // ignore: cast_nullable_to_non_nullable
as double,cover: null == cover ? _self.cover : cover // ignore: cast_nullable_to_non_nullable
as double,axialCapacitySquare: null == axialCapacitySquare ? _self.axialCapacitySquare : axialCapacitySquare // ignore: cast_nullable_to_non_nullable
as double,mainBarSquare: null == mainBarSquare ? _self.mainBarSquare : mainBarSquare // ignore: cast_nullable_to_non_nullable
as String,steelRatioSqaure: null == steelRatioSqaure ? _self.steelRatioSqaure : steelRatioSqaure // ignore: cast_nullable_to_non_nullable
as double,axialCapacityCircle: null == axialCapacityCircle ? _self.axialCapacityCircle : axialCapacityCircle // ignore: cast_nullable_to_non_nullable
as double,mainBarCircle: null == mainBarCircle ? _self.mainBarCircle : mainBarCircle // ignore: cast_nullable_to_non_nullable
as String,steelRatioCircle: null == steelRatioCircle ? _self.steelRatioCircle : steelRatioCircle // ignore: cast_nullable_to_non_nullable
as double,calsLog: null == calsLog ? _self.calsLog : calsLog // ignore: cast_nullable_to_non_nullable
as String,isSelected: null == isSelected ? _self.isSelected : isSelected // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}


}

// dart format on
