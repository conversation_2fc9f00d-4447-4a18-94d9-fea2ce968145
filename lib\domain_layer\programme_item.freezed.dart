// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'programme_item.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$ProgrammeItem {

 String get itemName; double get start; double get duration; bool get isTouched; String get id;
/// Create a copy of ProgrammeItem
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ProgrammeItemCopyWith<ProgrammeItem> get copyWith => _$ProgrammeItemCopyWithImpl<ProgrammeItem>(this as ProgrammeItem, _$identity);

  /// Serializes this ProgrammeItem to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ProgrammeItem&&(identical(other.itemName, itemName) || other.itemName == itemName)&&(identical(other.start, start) || other.start == start)&&(identical(other.duration, duration) || other.duration == duration)&&(identical(other.isTouched, isTouched) || other.isTouched == isTouched)&&(identical(other.id, id) || other.id == id));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,itemName,start,duration,isTouched,id);

@override
String toString() {
  return 'ProgrammeItem(itemName: $itemName, start: $start, duration: $duration, isTouched: $isTouched, id: $id)';
}


}

/// @nodoc
abstract mixin class $ProgrammeItemCopyWith<$Res>  {
  factory $ProgrammeItemCopyWith(ProgrammeItem value, $Res Function(ProgrammeItem) _then) = _$ProgrammeItemCopyWithImpl;
@useResult
$Res call({
 String itemName, double start, double duration, bool isTouched, String id
});




}
/// @nodoc
class _$ProgrammeItemCopyWithImpl<$Res>
    implements $ProgrammeItemCopyWith<$Res> {
  _$ProgrammeItemCopyWithImpl(this._self, this._then);

  final ProgrammeItem _self;
  final $Res Function(ProgrammeItem) _then;

/// Create a copy of ProgrammeItem
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? itemName = null,Object? start = null,Object? duration = null,Object? isTouched = null,Object? id = null,}) {
  return _then(_self.copyWith(
itemName: null == itemName ? _self.itemName : itemName // ignore: cast_nullable_to_non_nullable
as String,start: null == start ? _self.start : start // ignore: cast_nullable_to_non_nullable
as double,duration: null == duration ? _self.duration : duration // ignore: cast_nullable_to_non_nullable
as double,isTouched: null == isTouched ? _self.isTouched : isTouched // ignore: cast_nullable_to_non_nullable
as bool,id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,
  ));
}

}


/// Adds pattern-matching-related methods to [ProgrammeItem].
extension ProgrammeItemPatterns on ProgrammeItem {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _ProgrammeItem value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _ProgrammeItem() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _ProgrammeItem value)  $default,){
final _that = this;
switch (_that) {
case _ProgrammeItem():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _ProgrammeItem value)?  $default,){
final _that = this;
switch (_that) {
case _ProgrammeItem() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String itemName,  double start,  double duration,  bool isTouched,  String id)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _ProgrammeItem() when $default != null:
return $default(_that.itemName,_that.start,_that.duration,_that.isTouched,_that.id);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String itemName,  double start,  double duration,  bool isTouched,  String id)  $default,) {final _that = this;
switch (_that) {
case _ProgrammeItem():
return $default(_that.itemName,_that.start,_that.duration,_that.isTouched,_that.id);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String itemName,  double start,  double duration,  bool isTouched,  String id)?  $default,) {final _that = this;
switch (_that) {
case _ProgrammeItem() when $default != null:
return $default(_that.itemName,_that.start,_that.duration,_that.isTouched,_that.id);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _ProgrammeItem extends ProgrammeItem {
   _ProgrammeItem({this.itemName = 'item', this.start = 0.0, this.duration = 4.0, this.isTouched = false, this.id = ''}): super._();
  factory _ProgrammeItem.fromJson(Map<String, dynamic> json) => _$ProgrammeItemFromJson(json);

@override@JsonKey() final  String itemName;
@override@JsonKey() final  double start;
@override@JsonKey() final  double duration;
@override@JsonKey() final  bool isTouched;
@override@JsonKey() final  String id;

/// Create a copy of ProgrammeItem
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ProgrammeItemCopyWith<_ProgrammeItem> get copyWith => __$ProgrammeItemCopyWithImpl<_ProgrammeItem>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ProgrammeItemToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ProgrammeItem&&(identical(other.itemName, itemName) || other.itemName == itemName)&&(identical(other.start, start) || other.start == start)&&(identical(other.duration, duration) || other.duration == duration)&&(identical(other.isTouched, isTouched) || other.isTouched == isTouched)&&(identical(other.id, id) || other.id == id));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,itemName,start,duration,isTouched,id);

@override
String toString() {
  return 'ProgrammeItem(itemName: $itemName, start: $start, duration: $duration, isTouched: $isTouched, id: $id)';
}


}

/// @nodoc
abstract mixin class _$ProgrammeItemCopyWith<$Res> implements $ProgrammeItemCopyWith<$Res> {
  factory _$ProgrammeItemCopyWith(_ProgrammeItem value, $Res Function(_ProgrammeItem) _then) = __$ProgrammeItemCopyWithImpl;
@override @useResult
$Res call({
 String itemName, double start, double duration, bool isTouched, String id
});




}
/// @nodoc
class __$ProgrammeItemCopyWithImpl<$Res>
    implements _$ProgrammeItemCopyWith<$Res> {
  __$ProgrammeItemCopyWithImpl(this._self, this._then);

  final _ProgrammeItem _self;
  final $Res Function(_ProgrammeItem) _then;

/// Create a copy of ProgrammeItem
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? itemName = null,Object? start = null,Object? duration = null,Object? isTouched = null,Object? id = null,}) {
  return _then(_ProgrammeItem(
itemName: null == itemName ? _self.itemName : itemName // ignore: cast_nullable_to_non_nullable
as String,start: null == start ? _self.start : start // ignore: cast_nullable_to_non_nullable
as double,duration: null == duration ? _self.duration : duration // ignore: cast_nullable_to_non_nullable
as double,isTouched: null == isTouched ? _self.isTouched : isTouched // ignore: cast_nullable_to_non_nullable
as bool,id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

// dart format on
