import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:nanoid2/nanoid2.dart';

//domain layer
import '../../domain_layer/column_scheme_data.dart';
import '../../domain_layer/preferences.dart';

// presentation layer
import '../../domain_layer/slab_scheme_data.dart';
import '../screen/homescreen.dart';
import 'input/custom_stateful_double_input.dart';
import 'input/custom_stateful_text_input.dart';
import 'popup/custom_popup.dart';
import 'input/custom_stateful_dropList.dart';
import 'input/custom_stateful_int_input.dart';
import 'sketch/draw_transfer_beam_loading_info.dart';
import 'button/function_button.dart';
import 'button/selection_button.dart';

class TransferBeamSchemeInputUi extends ConsumerStatefulWidget {
  const TransferBeamSchemeInputUi({
    this.isExpanded,
    this.maxHeight,
    this.minHeight,
    super.key,
  });

  final bool? isExpanded;
  final double? maxHeight;
  final double? minHeight;

  @override
  ConsumerState<ConsumerStatefulWidget> createState() =>
      _TransferBeamSchemeInputUiState();
}

class _TransferBeamSchemeInputUiState
    extends ConsumerState<TransferBeamSchemeInputUi>
    with WidgetsBindingObserver {
  late bool _isExpanded;
  late double _maxHeight;
  late double _minHeight;
  late CustomStatefulTextInput _usageInput;
  late String _id;
  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void initState() {
    WidgetsBinding.instance.addObserver(this);
    _isExpanded = widget.isExpanded ?? false;
    _maxHeight = widget.maxHeight ?? 300;
    _minHeight = widget.minHeight ?? 50;
    _usageInput = CustomStatefulTextInput();
    _id = _generateTaskID();

    // _updateHeights();

    super.initState();
  }

  @override
  void didChangeDependencies() {
    _updateHeights();
    super.didChangeDependencies();
    // Update heights when dependencies change, including after initState
  }

  @override
  void didChangeMetrics() {
    // This method is called when the metrics change (like resizing the window)
    setState(() {
      _updateHeights();
    });
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;
    final globalData = ref.watch(globalDataControllerProvider);
    final slabSchemeData = ref.watch(slabSchemeDataControllerProvider);
    final loadingTables = ref.watch(loadingTablesControllerProvider);
    final transferBeamSchemeInput = ref.watch(
      transferBeamSchemeInputControllerProvider,
    );
    final transferBeamSchemeInputGlobal = ref.watch(
      transferBeamSchemeInputGlobalControllerProvider,
    );
    final scrollController = ScrollController();
    final int containerOpacity = 175;
    final int textOpacity = 225;

    return globalData.when(
      data: (data) {
        late final List<String> units;
        switch (data.unit) {
          case 'metrics':
            units = PreferredUnit.metrics;
            break;
          case 'imperial':
            units = PreferredUnit.imperial;
            break;
          default:
            units = PreferredUnit.metrics;
            break;
        }

        return loadingTables.when(
          data: (tables) {
            return transferBeamSchemeInputGlobal.when(
              data: (inputGlobal) {
                return transferBeamSchemeInput.when(
                  data: (input) {
                    return slabSchemeData.when(
                      data: (slabData) {
                        return Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Padding(
                              padding: const EdgeInsets.fromLTRB(10, 0, 10, 0),
                              child: Divider(),
                            ),
                            Padding(
                              padding: const EdgeInsets.fromLTRB(10, 0, 0, 0),
                              child: Align(
                                alignment: Alignment.centerLeft,
                                child: Wrap(
                                  crossAxisAlignment: WrapCrossAlignment.center,
                                  children: [
                                    Wrap(
                                      crossAxisAlignment:
                                          WrapCrossAlignment.center,
                                      children: [
                                        IconButton(
                                          icon: Icon(
                                            _isExpanded
                                                ? Icons.keyboard_arrow_up
                                                : Icons.keyboard_arrow_down,
                                            color: colorScheme.onSurface,
                                          ),
                                          onPressed: () {
                                            setState(() {
                                              _isExpanded = !_isExpanded;
                                            });
                                          },
                                        ),
                                        Text(
                                          'RC Transfer Beam Scheme Inputs ',
                                          style: textTheme.titleLarge!.copyWith(
                                            color: colorScheme.onSurface,
                                          ),
                                        ),
                                      ],
                                    ),
                                    Container(
                                      decoration: BoxDecoration(
                                        borderRadius: BorderRadius.circular(
                                          5.0,
                                        ),
                                        color: colorScheme.surfaceContainer
                                            .withAlpha(225),
                                        border: Border.all(
                                          color: Colors.black.withAlpha(125),
                                        ),
                                      ),
                                      child: Padding(
                                        padding: const EdgeInsets.all(2.0),
                                        child: Text(
                                          '(Total: ${input.length})',
                                          style: textTheme.labelMedium!
                                              .copyWith(
                                                color: colorScheme
                                                    .onSurfaceVariant
                                                    .withAlpha(225),
                                              ),
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                            Padding(
                              padding: const EdgeInsets.fromLTRB(10, 0, 10, 0),
                              child: Divider(),
                            ),
                            Flexible(
                              child: ConstrainedBox(
                                constraints: BoxConstraints(
                                  maxHeight: _isExpanded ? _maxHeight : 0,
                                ),
                                child: Column(
                                  children: [
                                    Padding(
                                      padding: const EdgeInsets.fromLTRB(
                                        8.0,
                                        0.0,
                                        8.0,
                                        0.0,
                                      ),
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          // Container(
                                          //   decoration: BoxDecoration(
                                          //     borderRadius: BorderRadius.circular(10.0),
                                          //     border: Border.all(
                                          //       color: Colors.grey.withAlpha(150),
                                          //     ),
                                          //     color: colorScheme.secondaryContainer
                                          //         .withAlpha(containerOpacity),
                                          //   ),
                                          //   child: Padding(
                                          //     padding: const EdgeInsets.all(4.0),
                                          //     child: Text(
                                          //       'Transfer Beam Data',
                                          //       style: textTheme.titleSmall!.copyWith(
                                          //         color: colorScheme
                                          //             .onSecondaryContainer
                                          //             .withAlpha(textOpacity),
                                          //       ),
                                          //     ),
                                          //   ),
                                          // ),
                                          // SizedBox(height: 10.0),
                                          Row(
                                            children: [
                                              Flexible(
                                                child: CustomStatefulDoubleInput(
                                                  title: 'Span [${units[3]}]',
                                                  value: inputGlobal.span,
                                                  onChanged: (value) async {
                                                    await ref
                                                        .read(
                                                          transferBeamSchemeInputGlobalControllerProvider
                                                              .notifier,
                                                        )
                                                        .updateTable(
                                                          span: value,
                                                        );
                                                  },
                                                ),
                                              ),
                                
                                              SizedBox(width: 5.0),
                                              Flexible(
                                                child: CustomStatefulDoubleInput(
                                                  title:
                                                      'Str Zone [${units[4]}]',
                                                  value: inputGlobal.strZone,
                                                  onChanged: (value) async {
                                                    await ref
                                                        .read(
                                                          transferBeamSchemeInputGlobalControllerProvider
                                                              .notifier,
                                                        )
                                                        .updateTable(
                                                          strZone: value,
                                                        );
                                                  },
                                                ),
                                              ),
                                              Flexible(
                                                child: SizedBox(width: 5.0),
                                              ),
                                              Flexible(
                                                child: SizedBox(width: 5.0),
                                              ),
                                            ],
                                          ),
                                          SizedBox(height: 10.0),
                                          Row(
                                            mainAxisSize: MainAxisSize.min,
                                            mainAxisAlignment:
                                                MainAxisAlignment.start,
                                            children: [
                                              Flexible(
                                                child: CustomStatefulDoubleInput(
                                                  readOnly:
                                                      inputGlobal
                                                          .useSlabSelected,
                                                  title:
                                                      'Slab Thickness [${units[4]}]',
                                                  key: ValueKey(
                                                    '${_id}_slabThickness',
                                                  ),
                                                  value:
                                                      inputGlobal.slabThickness,
                                                  listener: (
                                                    hasFocus,
                                                    value,
                                                  ) async {
                                                    if (!hasFocus) {
                                                      await ref
                                                          .read(
                                                            transferBeamSchemeInputGlobalControllerProvider
                                                                .notifier,
                                                          )
                                                          .updateTable(
                                                            id: '1',
                                                            slabThickness:
                                                                value,
                                                          );
                                                    }
                                                  },
                                                ),
                                              ),
                                              SizedBox(width: 5.0),
                                              Flexible(
                                                child: CustomStatefulDoubleInput(
                                                  title:
                                                      'Load Width [${units[3]}]',
                                                  titleStyle: textTheme
                                                      .labelLarge!
                                                      .copyWith(
                                                        color: colorScheme.error
                                                            .withAlpha(200),
                                                      ),
                                                  tooltipText:
                                                      'Affect UDL on beam. Adjust Column\ngrid and hence transfer beam spacing,\nif necessary',
                                                  value: inputGlobal.loadWidth,
                                                  onChanged: (value) async {
                                                    await ref
                                                        .read(
                                                          transferBeamSchemeInputGlobalControllerProvider
                                                              .notifier,
                                                        )
                                                        .updateTable(
                                                          loadWidth: value,
                                                        );
                                                  },
                                                ),
                                              ),
                                              SizedBox(width: 5.0),
                                
                                              Flexible(
                                                child: CustomStatefulDoubleInput(
                                                  title: 'Preferred k-value',
                                                  titleStyle: textTheme
                                                      .labelLarge!
                                                      .copyWith(
                                                        color: colorScheme.error
                                                            .withAlpha(200),
                                                      ),
                                                  tooltipText:
                                                      'Affect flexural strength. Design will be as close to this k-value\n'
                                                      'as possible while considering the code limit as well',
                                
                                                  value: inputGlobal.mainKValue,
                                                  onChanged: (value) async {
                                                    await ref
                                                        .read(
                                                          transferBeamSchemeInputGlobalControllerProvider
                                                              .notifier,
                                                        )
                                                        .updateTable(
                                                          mainKValue: value,
                                                        );
                                                  },
                                                ),
                                              ),
                                              Flexible(
                                                child: SizedBox(width: 5.0),
                                              ),
                                            ],
                                          ),
                                          SizedBox(height: 10.0),
                                          Row(
                                            children: [
                                              Flexible(
                                                child: CustomStatefulDoubleInput(
                                                  title:
                                                      'Max Beam Width [${units[4]}]',
                                                  titleStyle: textTheme
                                                      .labelLarge!
                                                      .copyWith(
                                                        color: colorScheme.error
                                                            .withAlpha(200),
                                                      ),
                                                  tooltipText:
                                                      'Affect flexural Strength.\nRecommended same ascolumn width for typical bay.',
                                
                                                  value: inputGlobal.maxWidth,
                                                  onChanged: (value) async {
                                                    await ref
                                                        .read(
                                                          transferBeamSchemeInputGlobalControllerProvider
                                                              .notifier,
                                                        )
                                                        .updateTable(
                                                          maxWidth: value,
                                                        );
                                                  },
                                                ),
                                              ),
                                
                                              SizedBox(width: 5.0),
                                              Flexible(
                                                child: CustomStatefulIntInput(
                                                  title: 'Max Rebar Layer',
                                                  tooltipText:
                                                      'Affect flexural Strength.\nNot recommended >2 for typical bay',
                                                  titleStyle: textTheme
                                                      .labelLarge!
                                                      .copyWith(
                                                        color: colorScheme.error
                                                            .withAlpha(200),
                                                      ),
                                                  value: inputGlobal.maxLayers,
                                                  onChanged: (value) async {
                                                    await ref
                                                        .read(
                                                          transferBeamSchemeInputGlobalControllerProvider
                                                              .notifier,
                                                        )
                                                        .updateTable(
                                                          maxLayers: value,
                                                        );
                                                  },
                                                ),
                                              ),
                                              SizedBox(width: 5.0),
                                              Flexible(
                                                child: CustomStatefulIntInput(
                                                  title:
                                                      'Min Links Spacing [${units[4]}]',
                                                  tooltipText:
                                                      'Affect shear capacity.\nNot recommended <100mm',
                                                  titleStyle: textTheme
                                                      .labelLarge!
                                                      .copyWith(
                                                        color: colorScheme.error
                                                            .withAlpha(200),
                                                      ),
                                                  value: inputGlobal.minS,
                                                  onChanged: (value) async {
                                                    await ref
                                                        .read(
                                                          transferBeamSchemeInputGlobalControllerProvider
                                                              .notifier,
                                                        )
                                                        .updateTable(
                                                          minS: value,
                                                        );
                                                  },
                                                ),
                                              ),
                                              SizedBox(width: 5.0),
                                              Flexible(
                                                child: CustomStatefulIntInput(
                                                  title:
                                                      'Max Links Spacing [${units[4]}]',
                                                  tooltipText:
                                                      'Affect shear capacity.\nNot recommended >300mm',
                                                  titleStyle: textTheme
                                                      .labelLarge!
                                                      .copyWith(
                                                        color: colorScheme.error
                                                            .withAlpha(200),
                                                      ),
                                                  value: inputGlobal.maxS,
                                                  onChanged: (value) async {
                                                    await ref
                                                        .read(
                                                          transferBeamSchemeInputGlobalControllerProvider
                                                              .notifier,
                                                        )
                                                        .updateTable(
                                                          maxS: value,
                                                        );
                                                  },
                                                ),
                                              ),
                                            ],
                                          ),
                                        ],
                                      ),
                                    ),
                                    SizedBox(height: 10.0),
                                    Padding(
                                      padding: const EdgeInsets.fromLTRB(
                                        8.0,
                                        0.0,
                                        8.0,
                                        0.0,
                                      ),
                                      child: Row(
                                        children: [
                                          Flexible(
                                            child: ClipRect(
                                              child: Container(
                                                width: 150,
                                                child: CustomStatefulDropDown(
                                                  items:
                                                      tables
                                                          .map((e) => e.usage)
                                                          .toList(),
                                                  selectedValue:
                                                      inputGlobal.usage == ''
                                                          ? null
                                                          : inputGlobal.usage,
                                                  onTap: (selectedValue) async {
                                                    // final inputGlobal = await ref.read(
                                                    //   transferBeamSchemeInputGlobalControllerProvider
                                                    //       .future,
                                                    // );
                                                    await ref
                                                        .read(
                                                          transferBeamSchemeInputGlobalControllerProvider
                                                              .notifier,
                                                        )
                                                        .updateTable(
                                                          id: '1',
                                                          usage: selectedValue,
                                                        );
                                                  },
                                                ),
                                              ),
                                            ),
                                          ),
                                          SizedBox(width: 5.0),
                                          FunctionButton(
                                            labelIcon: Icon(
                                              Icons.add_box_outlined,
                                            ),
                                            labelText: 'Add',
                                            onTap: (isPressed) async {
                                              await ref
                                                  .read(
                                                    transferBeamSchemeInputControllerProvider
                                                        .notifier,
                                                  )
                                                  .addEmptytable();
                                            },
                                          ),
                                          SizedBox(width: 10.0),
                                          SelectionButton(
                                            labelTextStyle:
                                                inputGlobal.useSlabSelected
                                                    ? textTheme.labelLarge!
                                                        .copyWith(
                                                          color:
                                                              colorScheme
                                                                  .onTertiaryContainer,
                                                        )
                                                    : textTheme.labelLarge!
                                                        .copyWith(
                                                          color:
                                                              colorScheme
                                                                  .onSurface,
                                                        ),
                                            labelText: 'Use Slab Selected',
                                            pressedColor:
                                                colorScheme.tertiaryContainer,
                                            bgColor:
                                                inputGlobal.useSlabSelected
                                                    ? colorScheme
                                                        .tertiaryContainer
                                                    : colorScheme
                                                        .surfaceContainer,
                                
                                            onTap: (value) async {
                                              final inputGlobal = await ref.read(
                                                transferBeamSchemeInputGlobalControllerProvider
                                                    .future,
                                              );
                                
                                              await ref
                                                  .read(
                                                    transferBeamSchemeInputGlobalControllerProvider
                                                        .notifier,
                                                  )
                                                  .updateTable(
                                                    useSlabSelected:
                                                        !inputGlobal
                                                            .useSlabSelected,
                                                  );
                                              //* we avoid get the input again here for better performance
                                              if (!inputGlobal
                                                  .useSlabSelected) {
                                                final selectedSlab = slabData
                                                    .firstWhere(
                                                      (scheme) =>
                                                          scheme.isSelected,
                                                      orElse:
                                                          () =>
                                                              SlabSchemeData(),
                                                    );
                                                await ref
                                                    .read(
                                                      transferBeamSchemeInputGlobalControllerProvider
                                                          .notifier,
                                                    )
                                                    .updateTable(
                                                      slabThickness:
                                                          selectedSlab.strZone,
                                                    );
                                              }
                                            },
                                          ),
                                          CustomPopup(
                                            popupWidth: 550,
                                            popupHeight: 550,
                                            widgetList: [
                                              DrawTransferBeamLoadingInfo(
                                                sketchWidth: 500,
                                                sketchHeight: 500,
                                              ),
                                            ],
                                          ),
                                        ],
                                      ),
                                    ),
                                    SizedBox(height: 10.0),
                                    Flexible(
                                      child: ClipRect(
                                        child: Scrollbar(
                                          controller: scrollController,
                                          thumbVisibility: true,
                                          child: ListView.builder(
                                            controller: scrollController,
                                            itemCount: input.length,
                                            itemBuilder: (context, index) {
                                              return Padding(
                                                padding:
                                                    const EdgeInsets.fromLTRB(
                                                      8.0,
                                                      4.0,
                                                      8.0,
                                                      4.0,
                                                    ),
                                                child: Row(
                                                  mainAxisSize:
                                                      MainAxisSize.min,
                                                  mainAxisAlignment:
                                                      MainAxisAlignment.start,
                                                  children: [
                                                    Container(
                                                      decoration: BoxDecoration(
                                                        shape: BoxShape.circle,
                                                        color: colorScheme
                                                            .surfaceContainer
                                                            .withAlpha(200),
                                                        // borderRadius: BorderRadius.circular(5),
                                                        border: Border.all(
                                                          color: Colors.black,
                                                        ),
                                                      ),
                                                      child: Padding(
                                                        padding:
                                                            const EdgeInsets.all(
                                                              4.0,
                                                            ),
                                                        child: Text(
                                                          '${index + 1}',
                                                          style: textTheme
                                                              .labelLarge!
                                                              .copyWith(
                                                                color: colorScheme
                                                                    .onSurfaceVariant
                                                                    .withAlpha(
                                                                      250,
                                                                    ),
                                                              ),
                                                        ),
                                                      ),
                                                    ),
                                                    IconButton(
                                                      icon: Icon(
                                                        Icons.copy_all_outlined,
                                                        color:
                                                            colorScheme
                                                                .onSurface,
                                                      ),
                                                      onPressed: () async {
                                                        await ref
                                                            .read(
                                                              transferBeamSchemeInputControllerProvider
                                                                  .notifier,
                                                            )
                                                            .copyAndInsertTable(
                                                              index,
                                                            );
                                                      },
                                                    ),
                                                    IconButton(
                                                      icon: Icon(
                                                        Icons.delete_outline,
                                                        color:
                                                            colorScheme
                                                                .onSurface,
                                                      ),
                                                      onPressed: () async {
                                                        await ref
                                                            .read(
                                                              transferBeamSchemeInputControllerProvider
                                                                  .notifier,
                                                            )
                                                            .deleteTable(
                                                              input[index]
                                                                  .transferBeamSchemeInputId,
                                                            );
                                                      },
                                                    ),
                                                    SizedBox(width: 5.0),
                                                    SelectionButton(
                                                      key: ValueKey(
                                                        '${input[index].transferBeamSchemeInputId}_loadFromSelectedCol',
                                                      ),
                                                      labelTextStyle:
                                                          input[index]
                                                                  .loadFromSelectedCol
                                                              ? textTheme
                                                                  .labelLarge!
                                                                  .copyWith(
                                                                    color:
                                                                        colorScheme
                                                                            .onTertiaryContainer,
                                                                  )
                                                              : textTheme
                                                                  .labelLarge!
                                                                  .copyWith(
                                                                    color:
                                                                        colorScheme
                                                                            .onSurface,
                                                                  ),
                                                      labelText:
                                                          'Load from Col Selected',
                                                      pressedColor:
                                                          colorScheme
                                                              .tertiaryContainer,
                                                      bgColor:
                                                          input[index]
                                                                  .loadFromSelectedCol
                                                              ? colorScheme
                                                                  .tertiaryContainer
                                                              : colorScheme
                                                                  .surfaceContainer,
                                                      onTap: (value) async {
                                                        final input = await ref
                                                            .read(
                                                              transferBeamSchemeInputControllerProvider
                                                                  .future,
                                                            );
                                                        await ref
                                                            .read(
                                                              transferBeamSchemeInputControllerProvider
                                                                  .notifier,
                                                            )
                                                            .updateTable(
                                                              transferBeamSchemeInputId:
                                                                  input[index]
                                                                      .transferBeamSchemeInputId,
                                                              loadFromSelectedCol:
                                                                  !input[index]
                                                                      .loadFromSelectedCol,
                                                            );
                                                        //* if true (use col load)
                                                        if (!input[index]
                                                            .loadFromSelectedCol) {
                                                          final colData =
                                                              await ref.read(
                                                                columnSchemeDataControllerProvider
                                                                    .future,
                                                              );
                                                          final selectedCol =
                                                              colData.firstWhere(
                                                                (scheme) =>
                                                                    scheme
                                                                        .isSelected,
                                                                orElse:
                                                                    () =>
                                                                        ColumnSchemeData(),
                                                              );
                                                          await ref
                                                              .read(
                                                                transferBeamSchemeInputControllerProvider
                                                                    .notifier,
                                                              )
                                                              .updateTable(
                                                                transferBeamSchemeInputId:
                                                                    input[index]
                                                                        .transferBeamSchemeInputId,
                                                                pointLoad:
                                                                    selectedCol
                                                                        .ulsLoad,
                                                              );
                                                        }
                                                      },
                                                    ),
                                                    SizedBox(width: 5.0),
                                                    Flexible(
                                                      child: CustomStatefulDoubleInput(
                                                        readOnly:
                                                            input[index]
                                                                .loadFromSelectedCol,
                                                        title:
                                                            'Point Load [${units[0]}]',
                                                        key: ValueKey(
                                                          '${input[index].transferBeamSchemeInputId}_pointLoad',
                                                        ),
                                                        value:
                                                            input[index]
                                                                .pointLoad,
                                                        listener: (
                                                          hasFocus,
                                                          value,
                                                        ) async {
                                                          if (!hasFocus) {
                                                            await ref
                                                                .read(
                                                                  transferBeamSchemeInputControllerProvider
                                                                      .notifier,
                                                                )
                                                                .updateTable(
                                                                  transferBeamSchemeInputId:
                                                                      input[index]
                                                                          .transferBeamSchemeInputId,
                                                                  pointLoad:
                                                                      value,
                                                                );
                                                          }
                                                        },
                                                      ),
                                                    ),
                                                    SizedBox(width: 5.0),
                                                    Flexible(
                                                      child: CustomStatefulDoubleInput(
                                                        title:
                                                            'Distance a [${units[3]}]',
                                                        key: ValueKey(
                                                          '${input[index].transferBeamSchemeInputId}_distA',
                                                        ),
                                                        value:
                                                            input[index].distA,
                                                        maxValue:
                                                            inputGlobal.span,
                                                        listener: (
                                                          hasFocus,
                                                          value,
                                                        ) async {
                                                          if (!hasFocus) {
                                                            await ref
                                                                .read(
                                                                  transferBeamSchemeInputControllerProvider
                                                                      .notifier,
                                                                )
                                                                .updateTable(
                                                                  transferBeamSchemeInputId:
                                                                      input[index]
                                                                          .transferBeamSchemeInputId,
                                                                  distA: value,
                                                                );
                                                          }
                                                        },
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              );
                                            },
                                          ),
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ],
                        );
                      },
                      error: (error, stackTrace) {
                        return Text('Error: $error');
                      },
                      loading: () {
                        return const CircularProgressIndicator();
                      },
                    );
                  },
                  error: (error, stackTrace) {
                    return Text('Error: $error');
                  },
                  loading: () {
                    return const CircularProgressIndicator();
                  },
                );
              },
              error: (error, stackTrace) => Text(error.toString()),
              loading: () => CircularProgressIndicator(),
            );
          },
          error: (error, stackTrace) => Text(error.toString()),
          loading: () => CircularProgressIndicator(),
        );
      },
      error: (error, stackTrace) => Text(error.toString()),
      loading: () => CircularProgressIndicator(),
    );
  }

  void _updateHeights() {
    final double screenHeight = MediaQuery.of(context).size.height;
    _maxHeight =
        (widget.maxHeight == null)
            ? screenHeight * 0.4
            : (widget.maxHeight! > screenHeight * 0.4)
            ? screenHeight * 0.4
            : widget.maxHeight!;

    _minHeight =
        (widget.minHeight == null)
            ? screenHeight * 0.05
            : (widget.minHeight! > screenHeight * 0.05)
            ? screenHeight * 0.05
            : widget.minHeight!;

    // Adjust _maxHeight if needed
    if (_maxHeight < _minHeight) {
      _maxHeight = _minHeight;
    }
  }
}

String _generateTaskID() {
  String newID = nanoid(alphabet: Alphabet.noDoppelgangerSafe);
  newID = nanoid(alphabet: Alphabet.noDoppelgangerSafe);
  return newID;
}
