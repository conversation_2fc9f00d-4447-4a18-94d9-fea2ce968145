import 'carbon_struct.dart';

class PreferredUnit {
  static const List<String> metrics = [
    'kN', // 0
    'kPa',
    'kNm', // 2
    'm',
    'mm', // 4
    'MPa',
    'mm2', // 6
    'm2',
    'cm4', // 8
    'kN/m3',
    'mPD', // 10
    'm3',
    'kg', // 12
  ];
  static const List<String> imperial = [
    'kip',
    'psf',
    'kip-ft',
    'ft',
    'in',
    'psi',
    'in2',
    'ft2',
    'in4',
    'kip/ft3',
    'ftPD',
    'ft3',
  ];
}

class CalsPresentOptions {
  static const String main = 'Main Beam';
  static const String secondary = 'Secondary Beam';
}

class MaterialProps {
  static const List<String> concreteGrade = [
    'C45',
    'C50',
    'C55',
    'C60',
    'C65',
    'C70',
    'C75',
    'C80',
  ];
  static const List<String> steelGrade = ['S355', 'S460', 'S550', 'S690'];
}
