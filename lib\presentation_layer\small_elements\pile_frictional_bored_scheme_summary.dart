// import 'dart:nativewrappers/_internal/vm/lib/math_patch.dart';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:structify/domain_layer/pile_frictional_bored_input.dart';

//below for printing
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:printing/printing.dart';
// import 'dart:typed_data';
// import 'dart:ui';

//domain layer
import '../../domain_layer/global_data.dart';
import '../../domain_layer/mixin/mixin_tools_for_ui.dart';
import '../../domain_layer/pile_frictional_bored_data.dart';
import '../../domain_layer/preferences.dart';

// presentation layer
import '../screen/homescreen.dart';
// import '../small_elements/custom_stateful_double_input.dart';
// import '../small_elements/custom_stateful_text_input.dart';
// import '../small_elements/global_data_ui.dart';

//misc

import 'sketch/draw_pile_fricitonal_bored_scheme.dart';
import 'button/function_button.dart';
import 'button/selection_button.dart';

class PileFrictionalBoredSchemeSummary extends ConsumerStatefulWidget {
  const PileFrictionalBoredSchemeSummary({
    this.isExpanded,
    this.maxHeight,
    this.minHeight,
    super.key,
  });

  final bool? isExpanded;
  final double? maxHeight;
  final double? minHeight;

  @override
  ConsumerState<ConsumerStatefulWidget> createState() =>
      _PileFrictionalBoredSchemeSummaryState();
}

class _PileFrictionalBoredSchemeSummaryState
    extends ConsumerState<PileFrictionalBoredSchemeSummary>
    with WidgetsBindingObserver, MixinToolsForUI {
  late bool _isExpanded;
  late double _maxHeight;
  late double _minHeight;
  late ScrollController _scrollController;

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void initState() {
    WidgetsBinding.instance.addObserver(this);
    _isExpanded = widget.isExpanded ?? false;
    _maxHeight = widget.maxHeight ?? 300;
    _minHeight = widget.minHeight ?? 50;
    _scrollController = ScrollController();
    // _updateHeights();

    super.initState();
  }

  @override
  void didChangeDependencies() {
    _updateHeights();
    super.didChangeDependencies();
    // Update heights when dependencies change, including after initState
  }

  @override
  void didChangeMetrics() {
    // This method is called when the metrics change (like resizing the window)
    setState(() {
      _updateHeights();
    });
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    final loadingTables = ref.watch(loadingTablesControllerProvider);
    final globalData = ref.watch(globalDataControllerProvider);
    final pileFrictionalBoredInput = ref.watch(
      pileFrictionalBoredInputControllerProvider,
    );

    final pileFrictionalBoredSchemes = ref.watch(
      pileFrictionalBoredDataControllerProvider,
    );

    return loadingTables.when(
      data: (tables) {
        //start reutnr real stuffs when loading tables available
        return globalData.when(
          //also, start reutnr real stuffs when global data tables available
          data: (data) {
            return pileFrictionalBoredInput.when(
              data: (inputs) {
                return pileFrictionalBoredSchemes.when(
                  data: (schemes) {
                    late int indexMinScheme;
                    return Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Padding(
                          padding: const EdgeInsets.fromLTRB(10, 0, 10, 0),
                          child: Divider(),
                        ),
                        Padding(
                          padding: const EdgeInsets.fromLTRB(10, 0, 0, 0),
                          child: Align(
                            alignment: Alignment.centerLeft,
                            child: Wrap(
                              crossAxisAlignment: WrapCrossAlignment.center,
                              children: [
                                IconButton(
                                  icon: Icon(
                                    _isExpanded
                                        ? Icons.expand_less
                                        : Icons.expand_more,
                                  ),
                                  color: colorScheme.onSurface,
                                  onPressed: () {
                                    setState(() {
                                      _isExpanded = !_isExpanded;
                                    });
                                  },
                                ),
                                Text(
                                  'Frictional Bored Pile Scheme Summary ',
                                  style: textTheme.titleLarge!.copyWith(
                                    color: colorScheme.onSurface,
                                  ),
                                ),
                                Container(
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(5.0),
                                    color: colorScheme.surfaceContainer
                                        .withAlpha(225),
                                    border: Border.all(
                                      color: Colors.black.withAlpha(125),
                                    ),
                                  ),
                                  child: Padding(
                                    padding: const EdgeInsets.all(2.0),
                                    child: Text(
                                      '(Total: ${schemes.length})',
                                      style: textTheme.labelMedium!.copyWith(
                                        color: colorScheme.onSurfaceVariant
                                            .withAlpha(225),
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                        Padding(
                          padding: const EdgeInsets.fromLTRB(10, 0, 10, 0),
                          child: Divider(),
                        ),

                        ClipRect(
                          child: ConstrainedBox(
                            constraints: BoxConstraints(
                              maxHeight: _isExpanded ? _maxHeight : 0,
                            ),
                            child: Column(
                              children: [
                                Builder(
                                  builder: (context) {
                                    if (schemes.isNotEmpty) {
                                      final maxTable = schemes.reduce((
                                        current,
                                        next,
                                      ) {
                                        int sizeComparison = current.diameter
                                            .compareTo(next.diameter);

                                        if (sizeComparison < 0) {
                                          return current; // Sort by size first
                                        } else if (sizeComparison > 0) {
                                          return next;
                                        } else {
                                          if (current.steelRatio <
                                              next.steelRatio) {
                                            return current;
                                          } else {
                                            return next;
                                          }
                                        }
                                      });
                                      indexMinScheme = schemes.indexOf(
                                        maxTable,
                                      );

                                      late List<String> unit;
                                      switch (data.unit) {
                                        case 'metrics':
                                          unit = PreferredUnit.metrics;
                                          break;
                                        case 'imperial':
                                          unit = PreferredUnit.imperial;
                                          break;
                                        default:
                                          unit = PreferredUnit.metrics;
                                      }
                                      return DefaultTextStyle(
                                        style: textTheme.labelSmall!.copyWith(
                                          color: colorScheme.onErrorContainer,
                                        ),
                                        child: Padding(
                                          padding: const EdgeInsets.fromLTRB(
                                            10,
                                            0,
                                            10,
                                            0,
                                          ),
                                          child: Container(
                                            decoration: BoxDecoration(
                                              borderRadius:
                                                  BorderRadius.circular(5.0),
                                              color: colorScheme.errorContainer
                                                  .withAlpha(100),
                                              border: Border.all(
                                                width: 0.5,
                                                color: colorScheme.error
                                                    .withAlpha(100),
                                              ),
                                            ),
                                            child: Padding(
                                              padding: const EdgeInsets.all(
                                                4.0,
                                              ),
                                              child: Row(
                                                mainAxisAlignment:
                                                    MainAxisAlignment.start,
                                                mainAxisSize: MainAxisSize.min,
                                                children: [
                                                  Container(
                                                    decoration: BoxDecoration(
                                                      shape: BoxShape.circle,
                                                      border: Border.all(
                                                        color:
                                                            colorScheme
                                                                .onErrorContainer,
                                                        width: 1.0,
                                                      ),
                                                    ),
                                                    child: Padding(
                                                      padding:
                                                          const EdgeInsets.all(
                                                            8.0,
                                                          ),
                                                      child: Text(
                                                        '${indexMinScheme + 1}',
                                                        style: textTheme
                                                            .titleSmall!
                                                            .copyWith(
                                                              color:
                                                                  colorScheme
                                                                      .onErrorContainer,
                                                            ),
                                                      ),
                                                    ),
                                                  ),
                                                  SizedBox(width: 15.0),
                                                  Column(
                                                    mainAxisSize:
                                                        MainAxisSize.min,
                                                    crossAxisAlignment:
                                                        CrossAxisAlignment
                                                            .start,
                                                    children: [
                                                      Text(
                                                        'Minimal Scheme',
                                                        style: textTheme
                                                            .labelMedium!
                                                            .copyWith(
                                                              color:
                                                                  colorScheme
                                                                      .error,
                                                            ),
                                                      ),

                                                      Text(
                                                        'Shaft Dia: ${NumberFormat('0').format(maxTable.diameter)} [${unit[4]}]',
                                                      ),
                                                      Text(
                                                        'Base Dia: ${NumberFormat('0').format(maxTable.diameter * maxTable.ratioOfBelloutDia)} [${unit[4]}]',
                                                      ),
                                                      Text(
                                                        'Length: ${NumberFormat('0').format(maxTable.length)} [${unit[3]}]',
                                                      ),
                                                      Text(
                                                        'fcu: ${NumberFormat('0').format(maxTable.fcu)} [${unit[5]}]',
                                                      ),
                                                      Text(
                                                        'SPT N-Value: ${NumberFormat('0').format(maxTable.sptNValue)}',
                                                      ),
                                                      Text(' '), // placeholder
                                                      Text(' '), // placeholder
                                                    ],
                                                  ),
                                                  SizedBox(width: 15.0),
                                                  Column(
                                                    mainAxisSize:
                                                        MainAxisSize.min,
                                                    crossAxisAlignment:
                                                        CrossAxisAlignment
                                                            .start,
                                                    children: [
                                                      Text(
                                                        'SLS Load: ${NumberFormat('0').format(maxTable.slsLoad)} [${unit[0]}]',
                                                      ),
                                                      Text(
                                                        'ULS Load: ${NumberFormat('0').format(maxTable.ulsLoad)} [${unit[0]}]',
                                                      ),
                                                      Text(
                                                        'Ground Capacity: ${NumberFormat('0').format(maxTable.totalGroundResistance)} [${unit[0]}]',
                                                      ),
                                                      Text(
                                                        'Str Capacity: ${NumberFormat('0').format(maxTable.strCapacity)} [${unit[0]}]',
                                                      ),
                                                      Text(
                                                        'Rebar: ${maxTable.rebar} ',
                                                      ),
                                                      Text(
                                                        'Steel Ratio: ${NumberFormat('0.000').format(maxTable.steelRatio * 100)}\u0025 ',
                                                      ),
                                                    ],
                                                  ),
                                                ],
                                              ),
                                            ),
                                          ),
                                        ),
                                      );
                                    } else {
                                      return Align(
                                        child: Padding(
                                          padding: const EdgeInsets.fromLTRB(
                                            10,
                                            0,
                                            10,
                                            0,
                                          ),
                                          child: Container(
                                            decoration: BoxDecoration(
                                              borderRadius:
                                                  BorderRadius.circular(5.0),
                                              color: colorScheme.errorContainer
                                                  .withAlpha(100),
                                              border: Border.all(
                                                width: 0.5,
                                                color: colorScheme.error
                                                    .withAlpha(100),
                                              ),
                                            ),
                                            child: Padding(
                                              padding: const EdgeInsets.all(
                                                4.0,
                                              ),
                                              child: Text(
                                                'No Pile Scheme Data',
                                                style: textTheme.labelMedium!
                                                    .copyWith(
                                                      color: colorScheme.error,
                                                    ),
                                              ),
                                            ),
                                          ),
                                        ),
                                      );
                                    }
                                  },
                                ),
                                SizedBox(height: 10),
                                Flexible(
                                  child: Scrollbar(
                                    controller: _scrollController,
                                    thumbVisibility: true,
                                    child: DefaultTextStyle(
                                      style: textTheme.labelSmall!.copyWith(
                                        color: colorScheme.onSurface,
                                      ),
                                      child: ListView.builder(
                                        controller: _scrollController,
                                        itemCount: schemes.length,
                                        itemBuilder: (context, index) {
                                          late List<String> unit;
                                          switch (data.unit) {
                                            case 'metrics':
                                              unit = PreferredUnit.metrics;
                                              break;
                                            case 'imperial':
                                              unit = PreferredUnit.imperial;
                                              break;
                                            default:
                                              unit = PreferredUnit.metrics;
                                          }

                                          return Padding(
                                            padding: const EdgeInsets.fromLTRB(
                                              8.0,
                                              4.0,
                                              8.0,
                                              4.0,
                                            ),
                                            child: Row(
                                              children: [
                                                Column(
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.center,
                                                  children: [
                                                    FunctionButton(
                                                      labelText:
                                                          ' ${index + 1} ',
                                                      pressedColor: colorScheme
                                                          .tertiary
                                                          .withAlpha(150),
                                                      onTap: (value) async {
                                                        final schemes =
                                                            await ref.read(
                                                              pileFrictionalBoredDataControllerProvider
                                                                  .future,
                                                            );
                                                        await ref
                                                            .read(
                                                              pileFrictionalBoredDataControllerProvider
                                                                  .notifier,
                                                            )
                                                            .toggleSelectScheme(
                                                              schemes[index]
                                                                  .pileFrictionalBoredSchemeId,
                                                            );
                                                      },
                                                    ),
                                                    SizedBox(height: 5.0),
                                                    SelectionButton(
                                                      labelIcon: Icon(
                                                        Icons.check,
                                                        color:
                                                            schemes[index]
                                                                    .isSelected
                                                                ? colorScheme
                                                                    .onTertiary
                                                                : colorScheme
                                                                    .onSurface
                                                                    .withAlpha(
                                                                      100,
                                                                    ),
                                                      ),
                                                      labelText: '',
                                                      // pressedColor:
                                                      //     colorScheme.tertiary,
                                                      bgColor:
                                                          schemes[index]
                                                                  .isSelected
                                                              ? colorScheme
                                                                  .tertiary
                                                              : colorScheme
                                                                  .surfaceContainer
                                                                  .withAlpha(
                                                                    100,
                                                                  ),

                                                      onTap: (value) async {
                                                        final schemes =
                                                            await ref.read(
                                                              pileFrictionalBoredDataControllerProvider
                                                                  .future,
                                                            );
                                                        await ref
                                                            .read(
                                                              pileFrictionalBoredDataControllerProvider
                                                                  .notifier,
                                                            )
                                                            .toggleSelectScheme(
                                                              schemes[index]
                                                                  .pileFrictionalBoredSchemeId,
                                                            );

                                                        final newPileSchemes =
                                                            await ref.read(
                                                              pileFrictionalBoredDataControllerProvider
                                                                  .future,
                                                            );
                                                        if (newPileSchemes[index]
                                                            .isSelected) {
                                                          final removedTable =
                                                              newPileSchemes
                                                                  .removeAt(
                                                                    index,
                                                                  );
                                                          newPileSchemes.insert(
                                                            0,
                                                            removedTable,
                                                          );
                                                          await ref
                                                              .read(
                                                                pileFrictionalBoredDataControllerProvider
                                                                    .notifier,
                                                              )
                                                              .replaceEntireTable(
                                                                newPileSchemes,
                                                              );
                                                          if (mounted) {
                                                            showSlidingFadingMessage(
                                                              context,
                                                              'Selected frictional bored pile scheme  (original index: ${index + 1}) pushed to top',
                                                            );
                                                          }
                                                        }
                                                      },
                                                    ),
                                                    SizedBox(height: 5.0),
                                                    FunctionButton(
                                                      key: ValueKey(
                                                        'pileFrictionalBoredSchemeId_${schemes[index].pileFrictionalBoredSchemeId}',
                                                      ),
                                                      labelText: 'Show Cals',
                                                      pressedColor: colorScheme
                                                          .tertiary
                                                          .withAlpha(150),
                                                      onTap: (value) async {
                                                        final globalData =
                                                            await ref.read(
                                                              globalDataControllerProvider
                                                                  .future,
                                                            );
                                                        _presentCalsRecord(
                                                          globalData,
                                                          unit,
                                                          inputs,
                                                          schemes,
                                                          index,
                                                        );
                                                      },
                                                    ),
                                                  ],
                                                ),
                                                IconButton(
                                                  icon: Icon(
                                                    Icons.delete,
                                                    color:
                                                        colorScheme.onSurface,
                                                  ),
                                                  onPressed: () {
                                                    ref
                                                        .read(
                                                          pileFrictionalBoredDataControllerProvider
                                                              .notifier,
                                                        )
                                                        .deleteTable(
                                                          schemes[index]
                                                              .pileFrictionalBoredSchemeId,
                                                        );
                                                  },
                                                ),
                                                Flexible(
                                                  child: Row(
                                                    mainAxisAlignment:
                                                        MainAxisAlignment.start,
                                                    mainAxisSize:
                                                        MainAxisSize.min,
                                                    children: [
                                                      SizedBox(width: 15.0),
                                                      Column(
                                                        mainAxisSize:
                                                            MainAxisSize.min,
                                                        crossAxisAlignment:
                                                            CrossAxisAlignment
                                                                .start,
                                                        children: [
                                                          // Container(
                                                          //   decoration: BoxDecoration(
                                                          //     borderRadius:
                                                          //         BorderRadius.circular(
                                                          //           5.0,
                                                          //         ),
                                                          //     color: colorScheme
                                                          //         .secondaryContainer
                                                          //         .withAlpha(
                                                          //           200,
                                                          //         ),
                                                          //   ),
                                                          //   child: Padding(
                                                          //     padding:
                                                          //         const EdgeInsets.all(
                                                          //           3.0,
                                                          //         ),
                                                          //     child: Text(
                                                          //       'SPT N-Value: ${NumberFormat('0').format(schemes[index].sptNValue)}',
                                                          //       style: textTheme
                                                          //           .labelMedium!
                                                          //           .copyWith(
                                                          //             color:
                                                          //                 colorScheme
                                                          //                     .onSecondaryContainer,
                                                          //           ),
                                                          //     ),
                                                          //   ),
                                                          // ),
                                                          // SizedBox(height: 5.0),
                                                          Container(
                                                            decoration: BoxDecoration(
                                                              borderRadius:
                                                                  BorderRadius.circular(
                                                                    5.0,
                                                                  ),
                                                              color: colorScheme
                                                                  .secondaryContainer
                                                                  .withAlpha(
                                                                    200,
                                                                  ),
                                                            ),
                                                            child: Padding(
                                                              padding:
                                                                  const EdgeInsets.all(
                                                                    3.0,
                                                                  ),
                                                              child: Text(
                                                                'Shaft Dia: ${NumberFormat('0').format(schemes[index].diameter)} [${unit[4]}]',
                                                                style: textTheme
                                                                    .labelMedium!
                                                                    .copyWith(
                                                                      color:
                                                                          colorScheme
                                                                              .onSecondaryContainer,
                                                                    ),
                                                              ),
                                                            ),
                                                          ),
                                                          SizedBox(height: 5.0),
                                                          Container(
                                                            decoration: BoxDecoration(
                                                              borderRadius:
                                                                  BorderRadius.circular(
                                                                    5.0,
                                                                  ),
                                                              color: colorScheme
                                                                  .secondaryContainer
                                                                  .withAlpha(
                                                                    200,
                                                                  ),
                                                            ),
                                                            child: Padding(
                                                              padding:
                                                                  const EdgeInsets.all(
                                                                    3.0,
                                                                  ),
                                                              child: Text(
                                                                'Base Dia: ${NumberFormat('0').format(schemes[index].diameter * schemes[index].ratioOfBelloutDia)} [${unit[4]}]',
                                                                style: textTheme
                                                                    .labelMedium!
                                                                    .copyWith(
                                                                      color:
                                                                          colorScheme
                                                                              .onSecondaryContainer,
                                                                    ),
                                                              ),
                                                            ),
                                                          ),
                                                          SizedBox(height: 5.0),
                                                          Container(
                                                            decoration: BoxDecoration(
                                                              borderRadius:
                                                                  BorderRadius.circular(
                                                                    5.0,
                                                                  ),
                                                              color: colorScheme
                                                                  .secondaryContainer
                                                                  .withAlpha(
                                                                    200,
                                                                  ),
                                                            ),
                                                            child: Padding(
                                                              padding:
                                                                  const EdgeInsets.all(
                                                                    3.0,
                                                                  ),
                                                              child: Text(
                                                                'Length: ${NumberFormat('0').format(schemes[index].length)} [${unit[3]}]',
                                                                style: textTheme
                                                                    .labelMedium!
                                                                    .copyWith(
                                                                      color:
                                                                          colorScheme
                                                                              .onSecondaryContainer,
                                                                    ),
                                                              ),
                                                            ),
                                                          ),
                                                          SizedBox(height: 5.0),
                                                          Container(
                                                            decoration: BoxDecoration(
                                                              borderRadius:
                                                                  BorderRadius.circular(
                                                                    5.0,
                                                                  ),
                                                              color: colorScheme
                                                                  .secondaryContainer
                                                                  .withAlpha(
                                                                    200,
                                                                  ),
                                                            ),
                                                            child: Padding(
                                                              padding:
                                                                  const EdgeInsets.all(
                                                                    3.0,
                                                                  ),
                                                              child: Text(
                                                                'fcu: ${NumberFormat('0').format(schemes[index].fcu)} [${unit[5]}]',
                                                                style: textTheme
                                                                    .labelMedium!
                                                                    .copyWith(
                                                                      color:
                                                                          colorScheme
                                                                              .onSecondaryContainer,
                                                                    ),
                                                              ),
                                                            ),
                                                          ),
                                                        ],
                                                      ),
                                                      SizedBox(width: 15.0),
                                                      Column(
                                                        mainAxisSize:
                                                            MainAxisSize.min,
                                                        crossAxisAlignment:
                                                            CrossAxisAlignment
                                                                .start,
                                                        children: [
                                                          Text(
                                                            'SPT N-Value: ${NumberFormat('0').format(schemes[index].sptNValue)}',
                                                          ),
                                                          Text(
                                                            'SLS Load: ${NumberFormat('0').format(schemes[index].slsLoad)} [${unit[0]}]',
                                                          ),
                                                          Text(
                                                            'ULS Load: ${NumberFormat('0').format(schemes[index].ulsLoad)} [${unit[0]}]',
                                                          ),
                                                          Text(
                                                            'Ground Capacity: ${NumberFormat('0').format(schemes[index].totalGroundResistance)} [${unit[0]}]',
                                                          ),
                                                          Text(
                                                            'Str Capacity: ${NumberFormat('0').format(schemes[index].strCapacity)} [${unit[0]}]',
                                                          ),
                                                          Text(
                                                            'Rebar: ${schemes[index].rebar} ',
                                                          ),
                                                          Text(
                                                            'Steel Ratio: ${NumberFormat('0.000').format(schemes[index].steelRatio * 100)}\u0025',
                                                          ),
                                                        ],
                                                      ),
                                                      SizedBox(width: 5.0),
                                                      GestureDetector(
                                                        onTap: () async {
                                                          await showDialog(
                                                            context: context,
                                                            builder: (context) {
                                                              return Dialog(
                                                                backgroundColor:
                                                                    colorScheme
                                                                        .surfaceContainer,
                                                                child: SizedBox(
                                                                  width: 550,
                                                                  height: 550,
                                                                  child: DrawPileFrictionalBoredScheme(
                                                                    sketchWidth:
                                                                        500,
                                                                    sketchHeight:
                                                                        500,
                                                                    index:
                                                                        index,
                                                                  ),
                                                                ),
                                                              );
                                                            },
                                                          );
                                                        },
                                                        child:
                                                            DrawPileFrictionalBoredScheme(
                                                              sketchWidth: 125,
                                                              sketchHeight: 125,
                                                              index: index,
                                                            ),
                                                      ),
                                                      SizedBox(width: 5.0),
                                                    ],
                                                  ),
                                                ),
                                                SizedBox(width: 5.0),
                                              ],
                                            ),
                                          );
                                        },
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    );
                  },
                  error: (error, stackTrace) => Text(error.toString()),
                  loading: () => CircularProgressIndicator(),
                );
              },
              error: (error, stackTrace) => Text(error.toString()),
              loading: () => CircularProgressIndicator(),
            );
          },

          error: (error, stackTrace) => Text(error.toString()),
          loading: () => CircularProgressIndicator(),
        );
      },
      error: (error, stackTrace) => Text(error.toString()),
      loading: () => CircularProgressIndicator(),
    );
  }

  // Method to update heights based on the current media query
  void _updateHeights() {
    final double screenHeight = MediaQuery.of(context).size.height;
    _maxHeight =
        (widget.maxHeight == null)
            ? screenHeight * 0.6
            : (widget.maxHeight! > screenHeight * 0.6)
            ? screenHeight * 0.6
            : widget.maxHeight!;

    _minHeight =
        (widget.minHeight == null)
            ? screenHeight * 0.1
            : (widget.minHeight! > screenHeight * 0.1)
            ? screenHeight * 0.1
            : widget.minHeight!;

    // Adjust _maxHeight if needed

    if (_maxHeight < _minHeight) {
      _maxHeight = _minHeight;
    }
  }

  Future<void> _presentCalsRecord(
    GlobalData globalData,
    List<String> unit,
    PileFrictionalBoredInput input,
    List<PileFrictionalBoredData> schemes,
    int index,
  ) async {
    //* get the record first
    Map<String, List<dynamic>> result = _extractCalsLog(schemes[index].calsLog);

    await showDialog(
      context: context,
      builder: (context) {
        ColorScheme colorScheme = Theme.of(context).colorScheme;
        TextTheme textTheme = Theme.of(context).textTheme;
        Color bgColor = colorScheme.surfaceContainer;
        Color onBgColor = colorScheme.onSurface;
        Color titleBgColor = colorScheme.primary.withAlpha(150);
        Color titleOnBgColor = colorScheme.onPrimary;
        TextStyle titleTextStyle = textTheme.labelSmall!.copyWith(
          color: titleOnBgColor,
        );
        ScrollController scrollController = ScrollController();

        late List<String> unit;
        switch (globalData.unit) {
          case 'metrics':
            unit = PreferredUnit.metrics;
            break;
          case 'imperial':
            unit = PreferredUnit.imperial;
            break;
          default:
            unit = PreferredUnit.metrics;
        }
        const double maxH = 600;
        const double maxW = 600;
        final StringBuffer pileCals = _writePileResult(
          globalData,
          result, // input the main beam result
          schemes,
          input,
          index,
          unit,
        );

        NumberFormat f0 = NumberFormat('0');
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(15),
          ),
          backgroundColor: bgColor,
          child: ConstrainedBox(
            constraints: const BoxConstraints(maxWidth: maxW, maxHeight: maxH),
            child: Column(
              children: [
                Align(
                  alignment: Alignment.centerRight,
                  child: Padding(
                    padding: const EdgeInsets.fromLTRB(0, 10, 10, 0),
                    child: FunctionButton(
                      bgColor: titleBgColor,
                      labelTextColor: titleOnBgColor,
                      labelIcon: Icon(
                        Icons.print_outlined,
                        color: titleOnBgColor.withAlpha(175),
                      ),
                      labelText: '',
                      onTap: (value) {
                        _exportListToPdf(
                          context,
                          ref,
                          pileCals.toString().split('\n'),
                        );
                      },
                    ),
                  ),
                ),

                Scrollbar(
                  controller: scrollController,
                  thumbVisibility: true,
                  trackVisibility: false,
                  child: ConstrainedBox(
                    constraints: const BoxConstraints(
                      maxWidth: maxW,
                      maxHeight: maxH - 70,
                    ),
                    child: SingleChildScrollView(
                      controller: scrollController,
                      child: Padding(
                        padding: const EdgeInsets.fromLTRB(25.0, 5, 25.0, 5),
                        child: DefaultTextStyle(
                          style: textTheme.labelMedium!.copyWith(
                            color: onBgColor,
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              Container(
                                decoration: BoxDecoration(
                                  color: colorScheme.tertiary,
                                  shape: BoxShape.circle,
                                  border: Border.all(color: titleOnBgColor),
                                ),
                                child: Padding(
                                  padding: const EdgeInsets.all(12.0),
                                  child: Text(
                                    '${index + 1}',
                                    style: titleTextStyle.copyWith(
                                      fontSize: 24,
                                      color: colorScheme.onTertiary,
                                    ),
                                  ),
                                ),
                              ),
                              Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Container(
                                    decoration: BoxDecoration(
                                      color: titleBgColor,
                                      shape: BoxShape.rectangle,
                                      borderRadius: BorderRadius.circular(10.0),
                                      border: Border.all(color: titleOnBgColor),
                                    ),
                                    child: Padding(
                                      padding: const EdgeInsets.all(4.0),
                                      child: Text(
                                        'Diamter [${unit[4]}] :\n${f0.format(schemes[index].diameter)}',
                                        style: titleTextStyle,
                                      ),
                                    ),
                                  ),
                                  SizedBox(width: 5),
                                  Container(
                                    decoration: BoxDecoration(
                                      color: titleBgColor,
                                      shape: BoxShape.rectangle,
                                      borderRadius: BorderRadius.circular(10.0),
                                      border: Border.all(color: titleOnBgColor),
                                    ),
                                    child: Padding(
                                      padding: const EdgeInsets.all(4.0),
                                      child: Text(
                                        'fcu [${unit[5]}]:\n${f0.format(schemes[index].fcu)} ',
                                        style: titleTextStyle,
                                      ),
                                    ),
                                  ),

                                  SizedBox(width: 5),
                                  Container(
                                    decoration: BoxDecoration(
                                      color: titleBgColor,
                                      shape: BoxShape.rectangle,
                                      borderRadius: BorderRadius.circular(10.0),
                                      border: Border.all(color: titleOnBgColor),
                                    ),
                                    child: Padding(
                                      padding: const EdgeInsets.all(4.0),
                                      child: Text(
                                        'SLS Load [${unit[0]}]:\n${f0.format(schemes[index].slsLoad)}',
                                        style: titleTextStyle,
                                      ),
                                    ),
                                  ),
                                  SizedBox(width: 5),
                                  Container(
                                    decoration: BoxDecoration(
                                      color: titleBgColor,
                                      shape: BoxShape.rectangle,
                                      borderRadius: BorderRadius.circular(10.0),
                                      border: Border.all(color: titleOnBgColor),
                                    ),
                                    child: Padding(
                                      padding: const EdgeInsets.all(4.0),
                                      child: Text(
                                        'ULS Load [${unit[0]}]:\n${f0.format(schemes[index].ulsLoad)}',
                                        style: titleTextStyle,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                              SizedBox(height: 5),
                              Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Container(
                                    decoration: BoxDecoration(
                                      color: titleBgColor,
                                      shape: BoxShape.rectangle,
                                      borderRadius: BorderRadius.circular(10.0),
                                      border: Border.all(color: titleOnBgColor),
                                    ),
                                    child: Padding(
                                      padding: const EdgeInsets.all(4.0),
                                      child: Text(
                                        ' Ground Capacity [${unit[0]}]:\n${f0.format(schemes[index].totalGroundResistance)} ',
                                        style: titleTextStyle,
                                      ),
                                    ),
                                  ),
                                  SizedBox(width: 5),
                                  Container(
                                    decoration: BoxDecoration(
                                      color: titleBgColor,
                                      shape: BoxShape.rectangle,
                                      borderRadius: BorderRadius.circular(10.0),
                                      border: Border.all(color: titleOnBgColor),
                                    ),
                                    child: Padding(
                                      padding: const EdgeInsets.all(4.0),
                                      child: Text(
                                        'Str Capacity [${unit[0]}]:\n${f0.format(schemes[index].strCapacity)}',
                                        style: titleTextStyle,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                              SizedBox(height: 5),
                              Container(
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(10.0),
                                  border: Border.all(color: onBgColor),
                                ),
                                child: Padding(
                                  padding: const EdgeInsets.all(8.0),
                                  child: Text(pileCals.toString()),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  StringBuffer _writePileResult(
    GlobalData globalData,
    Map<String, List> result,
    List<PileFrictionalBoredData> schemes,
    PileFrictionalBoredInput input,
    int index,
    List<String> unit,
  ) {
    StringBuffer buffer = StringBuffer();
    PileFrictionalBoredData scheme = schemes[index];
    NumberFormat f0 = NumberFormat('0');
    NumberFormat f3 = NumberFormat('0.000');

    buffer.write('------------------------------\n');
    buffer.write(
      'Shaft Diameter, Ds: ${NumberFormat('0').format(scheme.diameter)} [${unit[4]}]\n',
    );
    buffer.write(
      'Base Diameter, Db: ${NumberFormat('0').format(scheme.diameter * scheme.ratioOfBelloutDia)} [${unit[4]}]\n',
    );
    buffer.write(
      'Length: ${NumberFormat('0').format(scheme.length)} [${unit[3]}]\n',
    );
    buffer.write('------------------------------\n');
    buffer.write('*******************\n');
    buffer.write('Loading\n');
    buffer.write('*******************\n');

    buffer.write('SLS Total: ${f0.format(scheme.slsLoad)} [${unit[0]}]\n');
    buffer.write('ULS Total: ${f0.format(scheme.ulsLoad)} [${unit[0]}]\n');
    buffer.write('*******************\n');
    buffer.write('Parameters\n');
    buffer.write('*******************\n');
    buffer.write('SPT N Value, N: ${f0.format(scheme.sptNValue)}\n');
    buffer.write(
      'Soil Unit Weight, \u03B3: ${f0.format(scheme.soilUnitWeight)} [${unit[0]}/${unit[3]}]\n',
    );
    buffer.write('ktan\u03B4: ${f3.format(scheme.kTan)}\n');
    buffer.write('FOS: ${f0.format(scheme.fos)}\n');

    buffer.write('***************************\n');
    buffer.write('Ground Capacity (SLS)\n');
    buffer.write('***************************\n');
    buffer.write(
      'Shaft Capacity, Qs\n= \u03C0*Ds*L*(0.5*\u03B3*L)(ktan\u03B4)/FOS\n= ${f0.format(scheme.shaftCapacity)} [${unit[0]}]\n',
    );
    buffer.write(
      'Base Capacity, Qb\n= (\u03B3*L*N)*(\u03C0*Db^2/4)/FOS\n= ${f0.format(scheme.baseCapacity)} [${unit[0]}]\n',
    );
    buffer.write(
      'Total Ground Resistance, Qs+Qb\n= ${f0.format(scheme.totalGroundResistance)} [${unit[0]}]\n',
    );
    if (scheme.totalGroundResistance > scheme.slsLoad) {
      buffer.write(
        '> SLS Load = ${f0.format(scheme.slsLoad)} [${unit[0]}] (OK)\n',
      );
    } else {
      buffer.write(
        '< SLS Load = ${f0.format(scheme.slsLoad)} [${unit[0]}] (Fail)\n',
      );
    }

    buffer.write('***************************\n');
    buffer.write('Structural Capacity (ULS)\n');
    buffer.write('***************************\n');
    buffer.write(
      'Rebar: ${scheme.rebar} (steel ratio: ${f3.format(scheme.steelRatio * 100)}%)\n',
    );
    buffer.write('Pc = 0.35fcuAc + 0.67fyAs\n');

    buffer.write(
      '= 0.35 x ${f0.format(scheme.fcu)} x ${f0.format(scheme.diameter)}^2 x ${'\u03C0'}/4',
    );
    buffer.write(
      ' + 0.67 x 500 x ${f0.format(scheme.diameter)}^2 x ${'\u03C0'}/4 x${f3.format(scheme.steelRatio * 100)}/100\n',
    );
    buffer.write('= ${f0.format(scheme.strCapacity)} [${unit[0]}]\n');
    if (scheme.strCapacity > scheme.ulsLoad) {
      buffer.write(
        '> ULS Load = ${f0.format(scheme.ulsLoad)} [${unit[0]}] (OK)\n',
      );
    } else {
      buffer.write(
        '< ULS Load = ${f0.format(scheme.ulsLoad)} [${unit[0]}] (Fail)\n',
      );
    }
    return buffer;
  }

  Future<void> _exportListToPdf(
    BuildContext context,
    WidgetRef ref,
    List<String> contents,
  ) async {
    final pw.ThemeData customTheme = pw.ThemeData.withFont(
      base: pw.Font.ttf(await rootBundle.load("assets/OpenSans-Regular.ttf")),
      bold: pw.Font.ttf(await rootBundle.load("assets/OpenSans-Bold.ttf")),
      italic: pw.Font.ttf(await rootBundle.load("assets/OpenSans-Italic.ttf")),
      boldItalic: pw.Font.ttf(
        await rootBundle.load("assets/OpenSans-BoldItalic.ttf"),
      ),
    );

    final pdf = pw.Document(theme: customTheme);
    final maxLines = 45;
    List<pw.Widget> pageBuffer = [];
    // TextStyle textStyle = Theme.of(context).textTheme.bodySmall!;

    for (int i = 0; i < contents.length; i++) {
      pageBuffer.add(pw.Text(contents[i]));

      //! note that the builder of .addPage is deferred callback
      //! it means it executes later when the pdf is actually printed out
      //! so we put in copy of pageBuffer to it everytime we .addPage

      if ((pageBuffer.length == maxLines) || (i == contents.length - 1)) {
        final pageBufferCopy = List<pw.Text>.from(pageBuffer);
        pdf.addPage(
          pw.Page(
            margin: pw.EdgeInsets.fromLTRB(25, 35, 25, 35),
            build: (pw.Context context) {
              // return pw.Center(
              //    child: pw.Text(
              //     'testing',
              //     style: pw.TextStyle(fontSize: textStyle.fontSize!),
              //    )
              // );
              return pw.Column(
                mainAxisAlignment: pw.MainAxisAlignment.start,
                crossAxisAlignment: pw.CrossAxisAlignment.start,
                children: pageBufferCopy,
              );
            },
          ),
        );
        pageBuffer.clear();
      }
    }

    await Printing.layoutPdf(
      onLayout: (PdfPageFormat format) async => pdf.save(),
    );
  }

  Map<String, List<dynamic>> _extractCalsLog(String calsLog) {
    // 1. get the record first
    final record = calsLog;
    List<String> lines = record.split('\n');
    // Regex to match items
    RegExp titleRegExp = RegExp(r'([a-zA-Z_ ]*[:])');
    RegExp digitsRegExp = RegExp(r'([\d]+[\.]?[\d]?)');
    RegExp unitRegExp = RegExp(r'(\[(.+?)\])');
    RegExp textRegExp = RegExp(r'(?<=: )[\w ]*');

    Map<String, List<dynamic>> resultMap = {};

    for (String line in lines) {
      final List<String> newLines = line.split(r'|');
      for (String line in newLines) {
        final titleMatch = titleRegExp
            .firstMatch(line)
            ?.group(0)
            ?.trim()
            .replaceAll(':', '');
        final String? digitMatch =
            digitsRegExp.firstMatch(line)?.group(0)?.trim();
        String? unitMatch = unitRegExp.firstMatch(line)?.group(0);
        String? textMatch;

        //* capture numeric/text result
        if (titleMatch != null) {
          textMatch = textRegExp.firstMatch(line)?.group(0)?.trim();
          if (textMatch != null && textMatch.contains(RegExp(r'[a-zA-Z]'))) {
            resultMap[titleMatch] = [textMatch, unitMatch];
          } else {
            resultMap[titleMatch] = [digitMatch, unitMatch];
          }
          print(
            '$titleMatch: ${resultMap[titleMatch]![0]} ${resultMap[titleMatch]![1]}',
          );
        }
      }
    }
    return resultMap;
  }
}
