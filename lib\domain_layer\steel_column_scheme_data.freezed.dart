// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'steel_column_scheme_data.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$SteelColumnSchemeData {

 String get steelColumnSchemeDataId;//will be overriden  as soon as new instance created
 double get sdl; double get ll; double get slsLoad; double get ulsLoad; double get fsy; double get unbracedLength; double get axialCapacity; String get steelSection; String get calsLog;
/// Create a copy of SteelColumnSchemeData
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$SteelColumnSchemeDataCopyWith<SteelColumnSchemeData> get copyWith => _$SteelColumnSchemeDataCopyWithImpl<SteelColumnSchemeData>(this as SteelColumnSchemeData, _$identity);

  /// Serializes this SteelColumnSchemeData to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is SteelColumnSchemeData&&(identical(other.steelColumnSchemeDataId, steelColumnSchemeDataId) || other.steelColumnSchemeDataId == steelColumnSchemeDataId)&&(identical(other.sdl, sdl) || other.sdl == sdl)&&(identical(other.ll, ll) || other.ll == ll)&&(identical(other.slsLoad, slsLoad) || other.slsLoad == slsLoad)&&(identical(other.ulsLoad, ulsLoad) || other.ulsLoad == ulsLoad)&&(identical(other.fsy, fsy) || other.fsy == fsy)&&(identical(other.unbracedLength, unbracedLength) || other.unbracedLength == unbracedLength)&&(identical(other.axialCapacity, axialCapacity) || other.axialCapacity == axialCapacity)&&(identical(other.steelSection, steelSection) || other.steelSection == steelSection)&&(identical(other.calsLog, calsLog) || other.calsLog == calsLog));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,steelColumnSchemeDataId,sdl,ll,slsLoad,ulsLoad,fsy,unbracedLength,axialCapacity,steelSection,calsLog);

@override
String toString() {
  return 'SteelColumnSchemeData(steelColumnSchemeDataId: $steelColumnSchemeDataId, sdl: $sdl, ll: $ll, slsLoad: $slsLoad, ulsLoad: $ulsLoad, fsy: $fsy, unbracedLength: $unbracedLength, axialCapacity: $axialCapacity, steelSection: $steelSection, calsLog: $calsLog)';
}


}

/// @nodoc
abstract mixin class $SteelColumnSchemeDataCopyWith<$Res>  {
  factory $SteelColumnSchemeDataCopyWith(SteelColumnSchemeData value, $Res Function(SteelColumnSchemeData) _then) = _$SteelColumnSchemeDataCopyWithImpl;
@useResult
$Res call({
 String steelColumnSchemeDataId, double sdl, double ll, double slsLoad, double ulsLoad, double fsy, double unbracedLength, double axialCapacity, String steelSection, String calsLog
});




}
/// @nodoc
class _$SteelColumnSchemeDataCopyWithImpl<$Res>
    implements $SteelColumnSchemeDataCopyWith<$Res> {
  _$SteelColumnSchemeDataCopyWithImpl(this._self, this._then);

  final SteelColumnSchemeData _self;
  final $Res Function(SteelColumnSchemeData) _then;

/// Create a copy of SteelColumnSchemeData
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? steelColumnSchemeDataId = null,Object? sdl = null,Object? ll = null,Object? slsLoad = null,Object? ulsLoad = null,Object? fsy = null,Object? unbracedLength = null,Object? axialCapacity = null,Object? steelSection = null,Object? calsLog = null,}) {
  return _then(_self.copyWith(
steelColumnSchemeDataId: null == steelColumnSchemeDataId ? _self.steelColumnSchemeDataId : steelColumnSchemeDataId // ignore: cast_nullable_to_non_nullable
as String,sdl: null == sdl ? _self.sdl : sdl // ignore: cast_nullable_to_non_nullable
as double,ll: null == ll ? _self.ll : ll // ignore: cast_nullable_to_non_nullable
as double,slsLoad: null == slsLoad ? _self.slsLoad : slsLoad // ignore: cast_nullable_to_non_nullable
as double,ulsLoad: null == ulsLoad ? _self.ulsLoad : ulsLoad // ignore: cast_nullable_to_non_nullable
as double,fsy: null == fsy ? _self.fsy : fsy // ignore: cast_nullable_to_non_nullable
as double,unbracedLength: null == unbracedLength ? _self.unbracedLength : unbracedLength // ignore: cast_nullable_to_non_nullable
as double,axialCapacity: null == axialCapacity ? _self.axialCapacity : axialCapacity // ignore: cast_nullable_to_non_nullable
as double,steelSection: null == steelSection ? _self.steelSection : steelSection // ignore: cast_nullable_to_non_nullable
as String,calsLog: null == calsLog ? _self.calsLog : calsLog // ignore: cast_nullable_to_non_nullable
as String,
  ));
}

}


/// Adds pattern-matching-related methods to [SteelColumnSchemeData].
extension SteelColumnSchemeDataPatterns on SteelColumnSchemeData {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _SteelColumnSchemeData value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _SteelColumnSchemeData() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _SteelColumnSchemeData value)  $default,){
final _that = this;
switch (_that) {
case _SteelColumnSchemeData():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _SteelColumnSchemeData value)?  $default,){
final _that = this;
switch (_that) {
case _SteelColumnSchemeData() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String steelColumnSchemeDataId,  double sdl,  double ll,  double slsLoad,  double ulsLoad,  double fsy,  double unbracedLength,  double axialCapacity,  String steelSection,  String calsLog)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _SteelColumnSchemeData() when $default != null:
return $default(_that.steelColumnSchemeDataId,_that.sdl,_that.ll,_that.slsLoad,_that.ulsLoad,_that.fsy,_that.unbracedLength,_that.axialCapacity,_that.steelSection,_that.calsLog);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String steelColumnSchemeDataId,  double sdl,  double ll,  double slsLoad,  double ulsLoad,  double fsy,  double unbracedLength,  double axialCapacity,  String steelSection,  String calsLog)  $default,) {final _that = this;
switch (_that) {
case _SteelColumnSchemeData():
return $default(_that.steelColumnSchemeDataId,_that.sdl,_that.ll,_that.slsLoad,_that.ulsLoad,_that.fsy,_that.unbracedLength,_that.axialCapacity,_that.steelSection,_that.calsLog);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String steelColumnSchemeDataId,  double sdl,  double ll,  double slsLoad,  double ulsLoad,  double fsy,  double unbracedLength,  double axialCapacity,  String steelSection,  String calsLog)?  $default,) {final _that = this;
switch (_that) {
case _SteelColumnSchemeData() when $default != null:
return $default(_that.steelColumnSchemeDataId,_that.sdl,_that.ll,_that.slsLoad,_that.ulsLoad,_that.fsy,_that.unbracedLength,_that.axialCapacity,_that.steelSection,_that.calsLog);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _SteelColumnSchemeData extends SteelColumnSchemeData {
   _SteelColumnSchemeData({this.steelColumnSchemeDataId = '', this.sdl = 0.0, this.ll = 0.0, this.slsLoad = 0.0, this.ulsLoad = 0.0, this.fsy = 355.0, this.unbracedLength = 5.0, this.axialCapacity = 0.0, this.steelSection = '', this.calsLog = ''}): super._();
  factory _SteelColumnSchemeData.fromJson(Map<String, dynamic> json) => _$SteelColumnSchemeDataFromJson(json);

@override@JsonKey() final  String steelColumnSchemeDataId;
//will be overriden  as soon as new instance created
@override@JsonKey() final  double sdl;
@override@JsonKey() final  double ll;
@override@JsonKey() final  double slsLoad;
@override@JsonKey() final  double ulsLoad;
@override@JsonKey() final  double fsy;
@override@JsonKey() final  double unbracedLength;
@override@JsonKey() final  double axialCapacity;
@override@JsonKey() final  String steelSection;
@override@JsonKey() final  String calsLog;

/// Create a copy of SteelColumnSchemeData
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$SteelColumnSchemeDataCopyWith<_SteelColumnSchemeData> get copyWith => __$SteelColumnSchemeDataCopyWithImpl<_SteelColumnSchemeData>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$SteelColumnSchemeDataToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _SteelColumnSchemeData&&(identical(other.steelColumnSchemeDataId, steelColumnSchemeDataId) || other.steelColumnSchemeDataId == steelColumnSchemeDataId)&&(identical(other.sdl, sdl) || other.sdl == sdl)&&(identical(other.ll, ll) || other.ll == ll)&&(identical(other.slsLoad, slsLoad) || other.slsLoad == slsLoad)&&(identical(other.ulsLoad, ulsLoad) || other.ulsLoad == ulsLoad)&&(identical(other.fsy, fsy) || other.fsy == fsy)&&(identical(other.unbracedLength, unbracedLength) || other.unbracedLength == unbracedLength)&&(identical(other.axialCapacity, axialCapacity) || other.axialCapacity == axialCapacity)&&(identical(other.steelSection, steelSection) || other.steelSection == steelSection)&&(identical(other.calsLog, calsLog) || other.calsLog == calsLog));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,steelColumnSchemeDataId,sdl,ll,slsLoad,ulsLoad,fsy,unbracedLength,axialCapacity,steelSection,calsLog);

@override
String toString() {
  return 'SteelColumnSchemeData(steelColumnSchemeDataId: $steelColumnSchemeDataId, sdl: $sdl, ll: $ll, slsLoad: $slsLoad, ulsLoad: $ulsLoad, fsy: $fsy, unbracedLength: $unbracedLength, axialCapacity: $axialCapacity, steelSection: $steelSection, calsLog: $calsLog)';
}


}

/// @nodoc
abstract mixin class _$SteelColumnSchemeDataCopyWith<$Res> implements $SteelColumnSchemeDataCopyWith<$Res> {
  factory _$SteelColumnSchemeDataCopyWith(_SteelColumnSchemeData value, $Res Function(_SteelColumnSchemeData) _then) = __$SteelColumnSchemeDataCopyWithImpl;
@override @useResult
$Res call({
 String steelColumnSchemeDataId, double sdl, double ll, double slsLoad, double ulsLoad, double fsy, double unbracedLength, double axialCapacity, String steelSection, String calsLog
});




}
/// @nodoc
class __$SteelColumnSchemeDataCopyWithImpl<$Res>
    implements _$SteelColumnSchemeDataCopyWith<$Res> {
  __$SteelColumnSchemeDataCopyWithImpl(this._self, this._then);

  final _SteelColumnSchemeData _self;
  final $Res Function(_SteelColumnSchemeData) _then;

/// Create a copy of SteelColumnSchemeData
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? steelColumnSchemeDataId = null,Object? sdl = null,Object? ll = null,Object? slsLoad = null,Object? ulsLoad = null,Object? fsy = null,Object? unbracedLength = null,Object? axialCapacity = null,Object? steelSection = null,Object? calsLog = null,}) {
  return _then(_SteelColumnSchemeData(
steelColumnSchemeDataId: null == steelColumnSchemeDataId ? _self.steelColumnSchemeDataId : steelColumnSchemeDataId // ignore: cast_nullable_to_non_nullable
as String,sdl: null == sdl ? _self.sdl : sdl // ignore: cast_nullable_to_non_nullable
as double,ll: null == ll ? _self.ll : ll // ignore: cast_nullable_to_non_nullable
as double,slsLoad: null == slsLoad ? _self.slsLoad : slsLoad // ignore: cast_nullable_to_non_nullable
as double,ulsLoad: null == ulsLoad ? _self.ulsLoad : ulsLoad // ignore: cast_nullable_to_non_nullable
as double,fsy: null == fsy ? _self.fsy : fsy // ignore: cast_nullable_to_non_nullable
as double,unbracedLength: null == unbracedLength ? _self.unbracedLength : unbracedLength // ignore: cast_nullable_to_non_nullable
as double,axialCapacity: null == axialCapacity ? _self.axialCapacity : axialCapacity // ignore: cast_nullable_to_non_nullable
as double,steelSection: null == steelSection ? _self.steelSection : steelSection // ignore: cast_nullable_to_non_nullable
as String,calsLog: null == calsLog ? _self.calsLog : calsLog // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

// dart format on
