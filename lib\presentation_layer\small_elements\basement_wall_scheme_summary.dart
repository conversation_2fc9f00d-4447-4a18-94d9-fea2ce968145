import 'dart:convert';
// import 'dart:nativewrappers/_internal/vm/lib/math_patch.dart';

import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:structify/domain_layer/basement_wall_scheme_input.dart';
import 'package:structify/domain_layer/data_struct/str_force_struct.dart';

//below for printing
import 'package:structify/presentation_layer/small_elements/sketch/draw_basement_Wall_loading.dart';

//domain layer
import '../../domain_layer/basement_wall_scheme_data.dart';
import '../../domain_layer/data_struct/crack_width_struct.dart';
import '../../domain_layer/mixin/mixin_tools_for_ui.dart';
import '../../domain_layer/preferences.dart';

// presentation layer
import '../screen/homescreen.dart';
// import '../small_elements/custom_stateful_double_input.dart';
// import '../small_elements/custom_stateful_text_input.dart';
// import '../small_elements/global_data_ui.dart';

//misc
import '../../misc/custom_func.dart';

import 'chart/chart_transfer_beam_scheme.dart';
import 'button/function_button.dart';

class BasementWallSchemeSummary extends ConsumerStatefulWidget {
  const BasementWallSchemeSummary({
    this.isExpanded,
    this.maxHeight,
    this.minHeight,
    super.key,
  });

  final bool? isExpanded;
  final double? maxHeight;
  final double? minHeight;

  @override
  ConsumerState<ConsumerStatefulWidget> createState() =>
      _BasementWallSchemeSummaryState();
}

class _BasementWallSchemeSummaryState
    extends ConsumerState<BasementWallSchemeSummary>
    with WidgetsBindingObserver, MixinToolsForUI {
  late bool _isExpanded;
  late double _maxHeight;
  late double _minHeight;
  late ScrollController _scrollController;

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void initState() {
    WidgetsBinding.instance.addObserver(this);
    _isExpanded = widget.isExpanded ?? false;
    _maxHeight = widget.maxHeight ?? 300;
    _minHeight = widget.minHeight ?? 50;
    _scrollController = ScrollController();
    // _updateHeights();

    super.initState();
  }

  @override
  void didChangeDependencies() {
    _updateHeights();
    super.didChangeDependencies();
    // Update heights when dependencies change, including after initState
  }

  @override
  void didChangeMetrics() {
    // This method is called when the metrics change (like resizing the window)
    setState(() {
      _updateHeights();
    });
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    final loadingTables = ref.watch(loadingTablesControllerProvider);
    final globalData = ref.watch(globalDataControllerProvider);
    final basementWallInputs = ref.watch(
      basementWallSchemeInputControllerProvider,
    );

    final basementWallSchemes = ref.watch(
      basementWallSchemeDataControllerProvider,
    );

    late final List<String> units;
    final failRegExp = RegExp(r'Fail', caseSensitive: false);
    final f0 = NumberFormat('0'), f3 = NumberFormat('0.000');

    return loadingTables.when(
      data: (tables) {
        //start reutnr real stuffs when loading tables available
        return globalData.when(
          //also, start reutnr real stuffs when global data tables available
          data: (data) {
            return basementWallInputs.when(
              data: (inputs) {
                return basementWallSchemes.when(
                  data: (scheme) {
                    switch (data.unit) {
                      case 'metrics':
                        units = PreferredUnit.metrics;
                        break;
                      case 'imperial':
                        units = PreferredUnit.imperial;
                        break;
                      default:
                        units = PreferredUnit.metrics;
                        break;
                    }
                    final calsLogMap = extractCalsLog(scheme.calsLog);
                    // convert the beam force to FlSpot
                    final List<FlSpot> wallMd = [];
                    final List<FlSpot> wallVd = [];
                    final Map<String, dynamic> y = jsonDecode(
                      scheme.wallForceULS,
                    );
                    y.map((key, value) {
                      y[key] = StrForce.fromJson(value);
                      return MapEntry(key, value);
                    });

                    for (StrForce wallForce in y.values) {
                      wallMd.add(
                        FlSpot(
                          roundTo(wallForce.x, 1),
                          roundTo(wallForce.Md, 0),
                        ),
                      );
                      wallVd.add(
                        FlSpot(
                          roundTo(wallForce.x, 1),
                          roundTo(wallForce.Vd, 0),
                        ),
                      );
                    }
                    return Column(
                      // mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Padding(
                          padding: const EdgeInsets.fromLTRB(10, 0, 10, 0),
                          child: Divider(),
                        ),
                        Padding(
                          padding: const EdgeInsets.fromLTRB(10, 0, 0, 0),
                          child: Align(
                            alignment: Alignment.centerLeft,
                            child: Wrap(
                              crossAxisAlignment: WrapCrossAlignment.center,
                              children: [
                                IconButton(
                                  icon: Icon(
                                    _isExpanded
                                        ? Icons.expand_less
                                        : Icons.expand_more,
                                  ),
                                  color: colorScheme.onSurface,
                                  onPressed: () {
                                    setState(() {
                                      _isExpanded = !_isExpanded;
                                    });
                                  },
                                ),
                                Text(
                                  'RC Basement Wall Scheme Summary ',
                                  style: textTheme.titleLarge!.copyWith(
                                    color: colorScheme.onSurface,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                        Padding(
                          padding: const EdgeInsets.fromLTRB(10, 0, 10, 0),
                          child: Divider(),
                        ),
                        ConstrainedBox(
                          constraints: BoxConstraints(
                            maxHeight: _isExpanded ? _maxHeight : 0,
                          ),
                          child: Scrollbar(
                            controller: _scrollController,
                            thumbVisibility: true,
                            child: SingleChildScrollView(
                              controller: _scrollController,
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  DefaultTextStyle(
                                    style: textTheme.labelMedium!.copyWith(
                                      color: colorScheme.onSurface,
                                    ),
                                    child: Row(
                                      mainAxisSize: MainAxisSize.max,
                                      mainAxisAlignment:
                                          MainAxisAlignment.start,
                                      children: [
                                        SizedBox(width: 10.0),
                                        Column(
                                          mainAxisSize: MainAxisSize.min,
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Builder(
                                              builder: (context) {
                                                if (!scheme.mainTopBar.contains(
                                                      failRegExp,
                                                    ) ||
                                                    !scheme.mainBottomBar
                                                        .contains(failRegExp) ||
                                                    !scheme.mainLinks.contains(
                                                      failRegExp,
                                                    )) {
                                                  return Text(
                                                    'STATUS:',
                                                    style: textTheme.titleSmall!
                                                        .copyWith(
                                                          color:
                                                              Colors.green[700],
                                                        ),
                                                  );
                                                } else {
                                                  return Text(
                                                    'STATUS:',
                                                    style: textTheme.titleSmall!
                                                        .copyWith(
                                                          color:
                                                              colorScheme.error,
                                                        ),
                                                  );
                                                }
                                              },
                                            ),
                                            Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,

                                              children: [
                                                Text(
                                                  'Wall Thk. [${units[4]}]: ',
                                                ),
                                                Text(
                                                  'Wall Thk. Limit [${units[4]}]:',
                                                ),
                                                Text('Preferred k-value:'),
                                                Text('Current k-value:'),
                                                Builder(
                                                  builder: (context) {
                                                    if (!scheme.mainTopBar
                                                        .contains(failRegExp)) {
                                                      return Text('External Bar:');
                                                    } else {
                                                      return Text(
                                                        'External Bar:',
                                                        style: textTheme
                                                            .titleSmall!
                                                            .copyWith(
                                                              color:
                                                                  colorScheme
                                                                      .error,
                                                            ),
                                                      );
                                                    }
                                                  },
                                                ),
                                                Builder(
                                                  builder: (context) {
                                                    if (!scheme.mainBottomBar
                                                        .contains(failRegExp)) {
                                                      return Text(
                                                        'Internal Bar:',
                                                      );
                                                    } else {
                                                      return Text(
                                                        'Internal Bar:',
                                                        style: textTheme
                                                            .titleSmall!
                                                            .copyWith(
                                                              color:
                                                                  colorScheme
                                                                      .error,
                                                            ),
                                                      );
                                                    }
                                                  },
                                                ),
                                                Builder(
                                                  builder: (context) {
                                                    if (!scheme.mainLinks
                                                        .contains(failRegExp)) {
                                                      return Text('Links:');
                                                    } else {
                                                      return Text(
                                                        'Links:',
                                                        style: textTheme
                                                            .titleSmall!
                                                            .copyWith(
                                                              color:
                                                                  colorScheme
                                                                      .error,
                                                            ),
                                                      );
                                                    }
                                                  },
                                                ),
                                              ],
                                            ),
                                          ],
                                        ),
                                        SizedBox(width: 10.0),
                                        Column(
                                          mainAxisSize: MainAxisSize.min,
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Builder(
                                              builder: (context) {
                                                if (!failRegExp.hasMatch(
                                                  scheme.calsLog,
                                                )) {
                                                  return Text(
                                                    'PASS',
                                                    style: textTheme.titleSmall!
                                                        .copyWith(
                                                          color:
                                                              Colors.green[700],
                                                        ),
                                                  );
                                                } else {
                                                  return Text(
                                                    'FAIL',
                                                    style: textTheme.titleSmall!
                                                        .copyWith(
                                                          color:
                                                              colorScheme.error,
                                                        ),
                                                  );
                                                }
                                              },
                                            ),
                                            Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,

                                              children: [
                                                Text(
                                                  '${f0.format(scheme.strZone)} [${units[4]}]',
                                                ),
                                                Text('${inputs.maxDepth}'),
                                                Text('${inputs.mainKValue}'),
                                                Text(
                                                  f3.format(
                                                    getDoubleValue(
                                                      calsLogMap,
                                                      'k',
                                                    ),
                                                  ),
                                                ),
                                                Builder(
                                                  builder: (context) {
                                                    if (!scheme.mainTopBar
                                                        .contains(failRegExp)) {
                                                      return Text(
                                                        scheme.mainTopBar,
                                                      );
                                                    } else {
                                                      return Text(
                                                        scheme.mainTopBar,
                                                        style: textTheme
                                                            .titleSmall!
                                                            .copyWith(
                                                              color:
                                                                  colorScheme
                                                                      .error,
                                                            ),
                                                      );
                                                    }
                                                  },
                                                ),
                                                Builder(
                                                  builder: (context) {
                                                    if (!scheme.mainBottomBar
                                                        .contains(failRegExp)) {
                                                      return Text(
                                                        scheme.mainBottomBar,
                                                      );
                                                    } else {
                                                      return Text(
                                                        scheme.mainBottomBar,
                                                        style: textTheme
                                                            .titleSmall!
                                                            .copyWith(
                                                              color:
                                                                  colorScheme
                                                                      .error,
                                                            ),
                                                      );
                                                    }
                                                  },
                                                ),
                                                Builder(
                                                  builder: (context) {
                                                    if (!scheme.mainLinks
                                                        .contains(failRegExp)) {
                                                      return Text(
                                                        scheme.mainLinks,
                                                      );
                                                    } else {
                                                      return Text(
                                                        scheme.mainLinks,
                                                        style: textTheme
                                                            .titleSmall!
                                                            .copyWith(
                                                              color:
                                                                  colorScheme
                                                                      .error,
                                                            ),
                                                      );
                                                    }
                                                  },
                                                ),
                                              ],
                                            ),
                                          ],
                                        ),
                                        SizedBox(width: 10.0),
                                        Padding(
                                          padding: const EdgeInsets.fromLTRB(
                                            8.0,
                                            4.0,
                                            8.0,
                                            4.0,
                                          ),
                                          child: GestureDetector(
                                            onTap: () async {
                                              await showDialog(
                                                context: context,
                                                builder: (context) {
                                                  return Dialog(
                                                    backgroundColor:
                                                        colorScheme
                                                            .surfaceContainer,
                                                    child: SizedBox(
                                                      width: 550,
                                                      height: 300,
                                                      child:
                                                          DrawBasementWallLoading(
                                                            sketchWidth: 500,
                                                            sketchHeight: 250,
                                                          ),
                                                    ),
                                                  );
                                                },
                                              );
                                            },
                                            child: DrawBasementWallLoading(
                                              sketchWidth: 300,
                                              sketchHeight: 125,
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                  SizedBox(height: 10.0),
                                  Row(
                                    mainAxisAlignment: MainAxisAlignment.start,
                                    children: [
                                      SizedBox(width: 10.0),
                                      FunctionButton(
                                        labelIcon: Icon(
                                          Icons.calculate_outlined,
                                        ),
                                        labelText: 'Show Cals',
                                        onTap: (isPressed) async {
                                          await _presentCalsRecord();
                                        },
                                      ),
                                    ],
                                  ),
                                  Padding(
                                    padding: const EdgeInsets.fromLTRB(
                                      10,
                                      0,
                                      10,
                                      0,
                                    ),
                                    child: ChartTransferBeamScheme(
                                      Md: wallMd,
                                      Vd: wallVd,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ],
                    );
                  },
                  error: (error, stackTrace) => Text(error.toString()),
                  loading: () => CircularProgressIndicator(),
                );
              },
              error: (error, stackTrace) => Text(error.toString()),
              loading: () => CircularProgressIndicator(),
            );
          },
          error: (error, stackTrace) => Text(error.toString()),
          loading: () => CircularProgressIndicator(),
        );
      },
      error: (error, stackTrace) => Text(error.toString()),
      loading: () => CircularProgressIndicator(),
    );
  }

  // Method to update heights based on the current media query
  void _updateHeights() {
    final double screenHeight = MediaQuery.of(context).size.height;
    _maxHeight =
        (widget.maxHeight == null)
            ? screenHeight * 0.7
            : (widget.maxHeight! > screenHeight * 0.7)
            ? screenHeight * 0.7
            : widget.maxHeight!;

    _minHeight =
        (widget.minHeight == null)
            ? screenHeight * 0.1
            : (widget.minHeight! > screenHeight * 0.1)
            ? screenHeight * 0.1
            : widget.minHeight!;

    // Adjust _maxHeight if needed

    if (_maxHeight < _minHeight) {
      _maxHeight = _minHeight;
    }
  }

  Future<void> _presentCalsRecord() async {
    // 1. get the record first
    final globalData = await ref.read(globalDataControllerProvider.future);
    final inputs = await ref.read(
      basementWallSchemeInputControllerProvider.future,
    );
    final scheme = await ref.read(
      basementWallSchemeDataControllerProvider.future,
    );

    if (mounted) {
      await showDialog(
        context: context,
        builder: (context) {
          ColorScheme colorScheme = Theme.of(context).colorScheme;
          TextTheme textTheme = Theme.of(context).textTheme;
          Color bgColor = colorScheme.surfaceContainer;
          Color onBgColor = colorScheme.onSurface;
          Color titleBgColor = colorScheme.primary.withAlpha(150);
          Color titleOnBgColor = colorScheme.onPrimary;
          TextStyle titleTextStyle = textTheme.labelSmall!.copyWith(
            color: titleOnBgColor,
          );
          ScrollController scrollController = ScrollController();

          late List<String> unit;
          switch (globalData.unit) {
            case 'metrics':
              unit = PreferredUnit.metrics;
              break;
            case 'imperial':
              unit = PreferredUnit.imperial;
              break;
            default:
              unit = PreferredUnit.metrics;
          }
          const double maxH = 600;
          const double maxW = 600;
          NumberFormat f0 = NumberFormat('0');

          return Dialog(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(15),
            ),
            backgroundColor: bgColor,
            child: ConstrainedBox(
              constraints: const BoxConstraints(
                maxWidth: maxW,
                maxHeight: maxH,
              ),
              child: FutureBuilder(
                future: _writeBeamResult(scheme, unit),
                builder: (context, snapshot) {
                  if (snapshot.hasData) {
                    final StringBuffer basementWallCals = snapshot.data!;
                    return Column(
                      children: [
                        Align(
                          alignment: Alignment.centerRight,
                          child: Padding(
                            padding: const EdgeInsets.fromLTRB(0, 10, 10, 0),
                            child: FunctionButton(
                              bgColor: titleBgColor,
                              labelTextColor: titleOnBgColor,
                              pressedColor: titleBgColor,
                              labelIcon: Icon(
                                Icons.print_outlined,
                                color: titleOnBgColor.withAlpha(175),
                              ),
                              labelText: '',
                              onTap: (value) {
                                exportListToPdf(
                                  context,
                                  ref,
                                  basementWallCals.toString().split('\n'),
                                );
                              },
                            ),
                          ),
                        ),

                        Scrollbar(
                          controller: scrollController,
                          thumbVisibility: true,
                          trackVisibility: true,
                          child: ConstrainedBox(
                            constraints: const BoxConstraints(
                              maxWidth: maxW,
                              maxHeight: maxH - 70,
                            ),
                            child: SingleChildScrollView(
                              controller: scrollController,
                              child: Padding(
                                padding: const EdgeInsets.fromLTRB(
                                  25.0,
                                  5,
                                  25.0,
                                  5,
                                ),
                                child: DefaultTextStyle(
                                  style: textTheme.labelMedium!.copyWith(
                                    color: onBgColor,
                                  ),
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.center,
                                    children: [
                                      Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.center,
                                        children: [
                                          Container(
                                            decoration: BoxDecoration(
                                              color: titleBgColor,
                                              shape: BoxShape.rectangle,
                                              borderRadius:
                                                  BorderRadius.circular(10.0),
                                              border: Border.all(
                                                color: titleOnBgColor,
                                              ),
                                            ),
                                            child: Padding(
                                              padding: const EdgeInsets.all(
                                                4.0,
                                              ),
                                              child: Text(
                                                'Wall Thickness :\n${scheme.strZone} [${unit[4]}]',
                                                style: titleTextStyle,
                                              ),
                                            ),
                                          ),
                                          SizedBox(width: 5),
                                          Container(
                                            decoration: BoxDecoration(
                                              color: titleBgColor,
                                              shape: BoxShape.rectangle,
                                              borderRadius:
                                                  BorderRadius.circular(10.0),
                                              border: Border.all(
                                                color: titleOnBgColor,
                                              ),
                                            ),
                                            child: Padding(
                                              padding: const EdgeInsets.all(
                                                4.0,
                                              ),
                                              child: Text(
                                                'fcu [${unit[5]}]:\n${f0.format(scheme.fcu)} ',
                                                style: titleTextStyle,
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                      SizedBox(height: 5),
                                      Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.center,
                                        children: [
                                          Container(
                                            decoration: BoxDecoration(
                                              color: titleBgColor,
                                              shape: BoxShape.rectangle,
                                              borderRadius:
                                                  BorderRadius.circular(10.0),
                                              border: Border.all(
                                                color: titleOnBgColor,
                                              ),
                                            ),
                                            child: Padding(
                                              padding: const EdgeInsets.all(
                                                4.0,
                                              ),
                                              child: Text(
                                                'Top Bar [${unit[0]}]:\n${scheme.mainTopBar} ',
                                                style: titleTextStyle,
                                              ),
                                            ),
                                          ),
                                          SizedBox(width: 5),
                                          Container(
                                            decoration: BoxDecoration(
                                              color: titleBgColor,
                                              shape: BoxShape.rectangle,
                                              borderRadius:
                                                  BorderRadius.circular(10.0),
                                              border: Border.all(
                                                color: titleOnBgColor,
                                              ),
                                            ),
                                            child: Padding(
                                              padding: const EdgeInsets.all(
                                                4.0,
                                              ),
                                              child: Text(
                                                'Bottom Bar [${unit[0]}]:\n${scheme.mainBottomBar} ',
                                                style: titleTextStyle,
                                              ),
                                            ),
                                          ),
                                          SizedBox(width: 5),
                                          Container(
                                            decoration: BoxDecoration(
                                              color: titleBgColor,
                                              shape: BoxShape.rectangle,
                                              borderRadius:
                                                  BorderRadius.circular(10.0),
                                              border: Border.all(
                                                color: titleOnBgColor,
                                              ),
                                            ),
                                            child: Padding(
                                              padding: const EdgeInsets.all(
                                                4.0,
                                              ),
                                              child: Text(
                                                'Links[${unit[0]}]:\n${scheme.mainLinks} ',
                                                style: titleTextStyle,
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                      SizedBox(height: 5),
                                      Container(
                                        decoration: BoxDecoration(
                                          borderRadius: BorderRadius.circular(
                                            10.0,
                                          ),
                                          border: Border.all(color: onBgColor),
                                        ),
                                        child: Padding(
                                          padding: const EdgeInsets.all(8.0),
                                          child: Text(
                                            basementWallCals.toString(),
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    );
                  } else if (snapshot.hasError) {
                    return Center(child: Text(snapshot.error.toString()));
                  } else {
                    return const CircularProgressIndicator();
                  }
                },
              ),
            ),
          );
        },
      );
    }
  }

  Future<StringBuffer> _writeBeamResult(
    BasementWallSchemeData scheme,
    List<String> unit,
  ) async {
    BasementWallSchemeInput input = await ref.read(
      basementWallSchemeInputControllerProvider.future,
    );

    Map<String, List<dynamic>> result = extractCalsLog(scheme.calsLog);
    for (String  key in  result.keys) {
       print(' $key : ${result[key]}');
    }
    StringBuffer buffer = StringBuffer();
    double tempDouble = 0.0, tempDouble2 = 0.0, tempDouble3 = 0.0;
    final f0 = NumberFormat('0'),
        f1 = NumberFormat('0.0'),
        f3 = NumberFormat('0.000'),
        f5 = NumberFormat('0.00000');
    String tempString = '';

    buffer.write('*******************\n');
    buffer.write('Parameters\n');
    buffer.write('*******************\n');
    buffer.write(
      'Span, L = ${f1.format(getDoubleValue(result, 'Span'))} [${unit[3]}]\n',
    );
    buffer.write('-------------------\n');
    buffer.write('Uniformly Distributed Loads, UDL\n');
    buffer.write(
      '= ${f1.format(getDoubleValue(result, 'ULS UDL'))} [${unit[0]}/${unit[3]}]\n',
    );
    buffer.write('-------------------\n');
    buffer.write('Design Force\n');
    buffer.write(
      'ULS Md = ${f0.format(getDoubleValue(result, 'M'))} [${unit[2]}] at ${f1.format(getDoubleValue(result, 'xMaxMd'))} [${unit[3]}] (Max from moment diagram)\n',
    );
    buffer.write(
      'ULS Vd = ${f0.format(getDoubleValue(result, 'V'))} [${unit[0]}] at ${f1.format(getDoubleValue(result, 'xMaxVd'))} [${unit[3]}] (Max from shear diagram)\n',
    );
    buffer.write(
      'SLS Md = ${f0.format(getDoubleValue(result, 'M_sls'))} [${unit[2]}] at ${f1.format(getDoubleValue(result, 'xMaxSLSMd'))} [${unit[3]}] (Max from moment diagram)\n',
    );
    buffer.write('-------------------\n');

    buffer.write('*******************\n');
    buffer.write('Check moement\n');
    buffer.write('*******************\n');

    buffer.write('d = ');
    tempDouble = getDoubleValue(result, 'd');
    buffer.write('${f0.format(tempDouble)} [${unit[4]}]\n');

    buffer.write('d_c = ');
    tempDouble = getDoubleValue(result, 'd_c');
    buffer.write('${f0.format(tempDouble)} [${unit[4]}]\n');

    buffer.write('k = ');
    tempDouble = getDoubleValue(result, 'k');
    buffer.write('${f3.format(tempDouble)} \n');

    buffer.write('z = ');
    tempDouble = getDoubleValue(result, 'z');
    buffer.write('${f0.format(tempDouble)} [${unit[4]}]\n');

    buffer.write('As_t,req = ');
    tempDouble = getDoubleValue(result, 'As_t');
    buffer.write('${f0.format(tempDouble)} [${unit[6]}]\n');

    buffer.write('As_t,pro');
    tempString = getStringValue(result, 'As_t_pro Designation');

    if (tempString != 'null') {
      buffer.write(' = $tempString');
      tempDouble = getDoubleValue(result, 'As_t_pro');
      buffer.write(' = ${f0.format(tempDouble)} [${unit[6]}]\n');
    } else {
      buffer.write(' = $tempString\n');
    }

    buffer.write('As_c,req = ');
    tempDouble = getDoubleValue(result, 'As_c');
    buffer.write('${f0.format(tempDouble)} [${unit[6]}]\n');

    buffer.write('As_c,pro');
    tempString = getStringValue(result, 'As_c_pro Designation');

    if (tempString != 'null') {
      buffer.write(' = $tempString');
      tempDouble = getDoubleValue(result, 'As_c_pro');
      buffer.write(' = ${f0.format(tempDouble)} [${unit[6]}]\n');
    } else {
      buffer.write(' = $tempString\n');
    }

    buffer.write('\n*******************\n');
    buffer.write('Check shear\n');
    buffer.write('*******************\n');

    buffer.write('v = ');
    tempDouble = getDoubleValue(result, 'v_d');
    buffer.write('${f3.format(tempDouble)} [${unit[5]}]\n');

    buffer.write('v_r = ');
    tempDouble = getDoubleValue(result, 'v_r');
    buffer.write('${f3.format(tempDouble)} [${unit[5]}]\n');

    buffer.write('v_c = ');
    tempDouble = getDoubleValue(result, 'v_c');
    buffer.write('${f3.format(tempDouble)} [${unit[5]}]\n');

    buffer.write('Asv_req = ');
    tempDouble = getDoubleValue(result, 'l_req');
    buffer.write(' = ${f3.format(tempDouble)} [${unit[6]}/${unit[4]}]\n');

    buffer.write('Asv_pro = ');
    tempString = getStringValue(result, 'l_pro Designation');
    if (tempString != 'null') {
      buffer.write(' = $tempString');
      buffer.write(
        ' = ${getDoubleValue(result, 'l_pro')} [${unit[6]}/${unit[4]}]\n',
      );
    } else {
      buffer.write(' = $tempString\n');
    }
    buffer.write('\n*******************\n');
    buffer.write('Check deflection\n');
    buffer.write('*******************\n');
    buffer.write(
      'Limit = L/250 = ${f3.format(getDoubleValue(result, 'Deflection Limit'))} [${unit[4]}]\n',
    );
    buffer.write('Max Deflection, f: \n');
    // buffer.write('if x < a, f = (wb^2L)/(72EI)*(2-(3/5)(b/L)^2)-(2(x/L)^2)\n');
    // buffer.write(
    //   'if x > a, f = (wb^2L^2)/(72EI)*(2-(3/5)(b/L)^2-2(x/L)^2)(x/L)+(3(x-a)^5)/(5b^3L^2)\n',
    // );
    buffer.write('y(x) = d^2M(x)/d^2x + C1x + C2\n(where C1 and C2 found by boudnary conditions)\n');
    buffer.write(
      '= ${f3.format(getDoubleValue(result, 'Max Deflection'))} [${unit[4]}] ',
    );
    if (getDoubleValue(result, 'Max Deflection') <
        getDoubleValue(result, 'Deflection Limit')) {
      buffer.write('(OK)\n');
    } else {
      buffer.write('(Fail)\n');
    }
    buffer.write('\n*******************\n');
    buffer.write('Check crack width\n');
    buffer.write('*******************\n');
    CrackWidth crack = CrackWidth.fromJson(
      jsonDecode(getStringValue(result, 'Crack Width JSON')),
    );
    buffer.write('M = ${f0.format(crack.M)} [${unit[2]}]\n');
    buffer.write('h = ${f0.format(crack.h)} [${unit[4]}]\n');
    buffer.write('b = ${f0.format(crack.b)} [${unit[4]}]\n');
    buffer.write('x = ${f0.format(crack.x)} [${unit[4]}]\n');
    buffer.write('c_min = ${f0.format(crack.c_min)} [${unit[4]}]\n');
    buffer.write('a_pi = ${f0.format(crack.a_pi)} [${unit[4]}]\n');
    buffer.write('a_cr = ${f0.format(crack.a_cr)} [${unit[4]}]\n');
    buffer.write('As_t,pro = ${f0.format(crack.As)} [${unit[6]}]\n');
    buffer.write('fcc = ${f0.format(crack.fcc)} [${unit[5]}]\n');
    buffer.write('fst = ${f0.format(crack.fst)} [${unit[5]}]\n');
    buffer.write('e_1 = y * (fst / Es) / (d - x) = ${f5.format(crack.e_1)} [-]\n');
    buffer.write('e_m = (e_1 - (b*(h-x)*(a_pi-x))/(3*Es*As*(d-x))) = ');
    buffer.write('${f5.format(crack.e_m)} [-]\n');
    buffer.write('Crack Width = (3*a_cr*e_m)/(1 + 2(a_cr-c_min)/(h-x)) = ');
    buffer.write('${f3.format(crack.crackWidth)} [${unit[4]}]\n');
    buffer.write(
      'Limit = ${f3.format(getDoubleValue(result, 'Crack Width Limit'))} [${unit[4]}] ',
    );
    if (crack.crackWidth < getDoubleValue(result, 'Crack Width Limit')) {
      buffer.write('(OK)\n');
    } else {
      buffer.write('(Fail)\n');
    }
    //* log any error / warnings
    List<String> errors = [], warnings = [];
    RegExp failReg = RegExp(r'fail', caseSensitive: false);
    if (failReg.hasMatch(buffer.toString())) {
      errors.add('Result not reliable. Something fails.');
    }
    if (getDoubleValue(result, 'depth') > input.maxDepth) {
      errors.add('Wall Thickness Exceeds Limit.');
    }
    if (failReg.hasMatch(getStringValue(result, 'As_t_pro Designation'))) {
      errors.add('Tension bar fails.');
    }
    if (failReg.hasMatch(getStringValue(result, 'As_c_pro Designation'))) {
      errors.add('Compression bar fails.');
    }

    if (failReg.hasMatch(getStringValue(result, 'l_pro Designation'))) {
      errors.add('Links fails.');
    }
    if (getDoubleValue(result, 'Max Deflection') >
        getDoubleValue(result, 'Deflection Limit')) {
      errors.add('Deflection fails.');
    }
    if (crack.crackWidth > getDoubleValue(result, 'Crack Width Limit')) {
      errors.add('Crack Width fails.');
    }
    if (errors.isNotEmpty) {
      warnings.add('1. Consider to adjust the beam width limit');
      warnings.add('2. Consider to adjust the beam str zone (if possible)');
      warnings.add('3. Consider to adjust preferred k-value');
      warnings.add('4. Consider to adjust max rebar layer');
    }
    if (warnings.isNotEmpty) {
      buffer = addWarningHeader(buffer, warnings);
    }
    if (errors.isNotEmpty) {
      buffer = addErrorHeader(buffer, errors);
    }

    return buffer;
  }
}
