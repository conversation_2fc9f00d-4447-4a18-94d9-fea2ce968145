import 'package:flutter/material.dart';

void showGentleMessageBox(
  BuildContext context,
  GlobalKey? widgetGlobalKey,
  String message, {
  Duration duration = const Duration(seconds: 2),
  double offsetX = 30,
  double offsetY = -35,
  Color? containerColor,
  TextStyle? textStyle,
}) {
  final overlay = Overlay.of(context);
  late OverlayEntry overlayEntry;
  final RenderBox renderBox;
  final Offset buttonPosition;

  // Get RenderBox and position of the button
  if (widgetGlobalKey != null) {
    renderBox = widgetGlobalKey.currentContext?.findRenderObject() as RenderBox;
    buttonPosition = renderBox.localToGlobal(Offset.zero);
  } else {
    renderBox = context.findRenderObject() as RenderBox;
    buttonPosition = renderBox.localToGlobal(Offset.zero);
  }

  overlayEntry = OverlayEntry(
    builder:
        (context) => Stack(
          children: [
            // Blocks interactions with underlying widgets
            // ModalBarrier(
            //   dismissible: true,
            //   color: Colors.black.withAlpha(100), // mild dimming
            // ),
            widgetGlobalKey == null
                ? Center(
                  child: _FadingMessageBox(
                    message: message,
                    onFinish: () {
                      overlayEntry.remove();
                    },
                    duration: duration,
                  ),
                )
                : Positioned(
                  left: buttonPosition.dx + offsetX,
                  top:
                      buttonPosition.dy +
                      offsetY, // 50 pixels above the button, adjust as needed
                  child: _FadingMessageBox(
                    message: message,
                    onFinish: () {
                      overlayEntry.remove();
                    },
                    duration: duration,
                    containerColor: containerColor,
                    textStyle: textStyle,
                  ),
                ),
          ],
        ),
  );

  overlay.insert(overlayEntry);
}

class _FadingMessageBox extends StatefulWidget {
  final String message;
  final VoidCallback onFinish;
  final Duration duration;
  final Color? containerColor;
  final TextStyle? textStyle;

  const _FadingMessageBox({
    required this.message,
    required this.onFinish,
    required this.duration,
    this.containerColor,
    this.textStyle,
  });

  @override
  _FadingMessageBoxState createState() => _FadingMessageBoxState();
}

class _FadingMessageBoxState extends State<_FadingMessageBox>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _opacityAnimation;
  late final Color _containerColor;
  late final TextStyle _textStyle;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );
    _opacityAnimation = Tween<double>(begin: 0, end: 1).animate(_controller);
    _controller.forward();

    // After duration, fade out and then remove overlay
    Future.delayed(widget.duration, () async {
      await _controller.reverse();
      widget.onFinish();
    });
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _containerColor =
        widget.containerColor ??
        Theme.of(context).colorScheme.secondaryContainer.withAlpha(150);
    _textStyle =
        widget.textStyle ??
        Theme.of(context).textTheme.labelMedium!.copyWith(
          color: Theme.of(
            context,
          ).colorScheme.onSecondaryContainer.withAlpha(150),
        );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return FadeTransition(
      opacity: _opacityAnimation,
      child: Material(
        color: _containerColor,
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          child: Text(widget.message, style: _textStyle),
        ),
      ),
    );
  }
}
