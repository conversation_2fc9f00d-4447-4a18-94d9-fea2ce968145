// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'cantilever_scheme_input_global.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$CantileverSchemeInputGlobal {

 String get id; double get span; double get loadWidth; double get strZone; double get fcu; double get cover; double get mainKValue; double get mainSteelRatio; int get minS; int get maxS; double get maxWidth; int get maxLayers; String get usage; double get slabThickness; bool get useSlabSelected;
/// Create a copy of CantileverSchemeInputGlobal
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$CantileverSchemeInputGlobalCopyWith<CantileverSchemeInputGlobal> get copyWith => _$CantileverSchemeInputGlobalCopyWithImpl<CantileverSchemeInputGlobal>(this as CantileverSchemeInputGlobal, _$identity);

  /// Serializes this CantileverSchemeInputGlobal to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is CantileverSchemeInputGlobal&&(identical(other.id, id) || other.id == id)&&(identical(other.span, span) || other.span == span)&&(identical(other.loadWidth, loadWidth) || other.loadWidth == loadWidth)&&(identical(other.strZone, strZone) || other.strZone == strZone)&&(identical(other.fcu, fcu) || other.fcu == fcu)&&(identical(other.cover, cover) || other.cover == cover)&&(identical(other.mainKValue, mainKValue) || other.mainKValue == mainKValue)&&(identical(other.mainSteelRatio, mainSteelRatio) || other.mainSteelRatio == mainSteelRatio)&&(identical(other.minS, minS) || other.minS == minS)&&(identical(other.maxS, maxS) || other.maxS == maxS)&&(identical(other.maxWidth, maxWidth) || other.maxWidth == maxWidth)&&(identical(other.maxLayers, maxLayers) || other.maxLayers == maxLayers)&&(identical(other.usage, usage) || other.usage == usage)&&(identical(other.slabThickness, slabThickness) || other.slabThickness == slabThickness)&&(identical(other.useSlabSelected, useSlabSelected) || other.useSlabSelected == useSlabSelected));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,span,loadWidth,strZone,fcu,cover,mainKValue,mainSteelRatio,minS,maxS,maxWidth,maxLayers,usage,slabThickness,useSlabSelected);

@override
String toString() {
  return 'CantileverSchemeInputGlobal(id: $id, span: $span, loadWidth: $loadWidth, strZone: $strZone, fcu: $fcu, cover: $cover, mainKValue: $mainKValue, mainSteelRatio: $mainSteelRatio, minS: $minS, maxS: $maxS, maxWidth: $maxWidth, maxLayers: $maxLayers, usage: $usage, slabThickness: $slabThickness, useSlabSelected: $useSlabSelected)';
}


}

/// @nodoc
abstract mixin class $CantileverSchemeInputGlobalCopyWith<$Res>  {
  factory $CantileverSchemeInputGlobalCopyWith(CantileverSchemeInputGlobal value, $Res Function(CantileverSchemeInputGlobal) _then) = _$CantileverSchemeInputGlobalCopyWithImpl;
@useResult
$Res call({
 String id, double span, double loadWidth, double strZone, double fcu, double cover, double mainKValue, double mainSteelRatio, int minS, int maxS, double maxWidth, int maxLayers, String usage, double slabThickness, bool useSlabSelected
});




}
/// @nodoc
class _$CantileverSchemeInputGlobalCopyWithImpl<$Res>
    implements $CantileverSchemeInputGlobalCopyWith<$Res> {
  _$CantileverSchemeInputGlobalCopyWithImpl(this._self, this._then);

  final CantileverSchemeInputGlobal _self;
  final $Res Function(CantileverSchemeInputGlobal) _then;

/// Create a copy of CantileverSchemeInputGlobal
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? span = null,Object? loadWidth = null,Object? strZone = null,Object? fcu = null,Object? cover = null,Object? mainKValue = null,Object? mainSteelRatio = null,Object? minS = null,Object? maxS = null,Object? maxWidth = null,Object? maxLayers = null,Object? usage = null,Object? slabThickness = null,Object? useSlabSelected = null,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,span: null == span ? _self.span : span // ignore: cast_nullable_to_non_nullable
as double,loadWidth: null == loadWidth ? _self.loadWidth : loadWidth // ignore: cast_nullable_to_non_nullable
as double,strZone: null == strZone ? _self.strZone : strZone // ignore: cast_nullable_to_non_nullable
as double,fcu: null == fcu ? _self.fcu : fcu // ignore: cast_nullable_to_non_nullable
as double,cover: null == cover ? _self.cover : cover // ignore: cast_nullable_to_non_nullable
as double,mainKValue: null == mainKValue ? _self.mainKValue : mainKValue // ignore: cast_nullable_to_non_nullable
as double,mainSteelRatio: null == mainSteelRatio ? _self.mainSteelRatio : mainSteelRatio // ignore: cast_nullable_to_non_nullable
as double,minS: null == minS ? _self.minS : minS // ignore: cast_nullable_to_non_nullable
as int,maxS: null == maxS ? _self.maxS : maxS // ignore: cast_nullable_to_non_nullable
as int,maxWidth: null == maxWidth ? _self.maxWidth : maxWidth // ignore: cast_nullable_to_non_nullable
as double,maxLayers: null == maxLayers ? _self.maxLayers : maxLayers // ignore: cast_nullable_to_non_nullable
as int,usage: null == usage ? _self.usage : usage // ignore: cast_nullable_to_non_nullable
as String,slabThickness: null == slabThickness ? _self.slabThickness : slabThickness // ignore: cast_nullable_to_non_nullable
as double,useSlabSelected: null == useSlabSelected ? _self.useSlabSelected : useSlabSelected // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}

}


/// Adds pattern-matching-related methods to [CantileverSchemeInputGlobal].
extension CantileverSchemeInputGlobalPatterns on CantileverSchemeInputGlobal {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _CantileverSchemeInputGlobal value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _CantileverSchemeInputGlobal() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _CantileverSchemeInputGlobal value)  $default,){
final _that = this;
switch (_that) {
case _CantileverSchemeInputGlobal():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _CantileverSchemeInputGlobal value)?  $default,){
final _that = this;
switch (_that) {
case _CantileverSchemeInputGlobal() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  double span,  double loadWidth,  double strZone,  double fcu,  double cover,  double mainKValue,  double mainSteelRatio,  int minS,  int maxS,  double maxWidth,  int maxLayers,  String usage,  double slabThickness,  bool useSlabSelected)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _CantileverSchemeInputGlobal() when $default != null:
return $default(_that.id,_that.span,_that.loadWidth,_that.strZone,_that.fcu,_that.cover,_that.mainKValue,_that.mainSteelRatio,_that.minS,_that.maxS,_that.maxWidth,_that.maxLayers,_that.usage,_that.slabThickness,_that.useSlabSelected);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  double span,  double loadWidth,  double strZone,  double fcu,  double cover,  double mainKValue,  double mainSteelRatio,  int minS,  int maxS,  double maxWidth,  int maxLayers,  String usage,  double slabThickness,  bool useSlabSelected)  $default,) {final _that = this;
switch (_that) {
case _CantileverSchemeInputGlobal():
return $default(_that.id,_that.span,_that.loadWidth,_that.strZone,_that.fcu,_that.cover,_that.mainKValue,_that.mainSteelRatio,_that.minS,_that.maxS,_that.maxWidth,_that.maxLayers,_that.usage,_that.slabThickness,_that.useSlabSelected);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  double span,  double loadWidth,  double strZone,  double fcu,  double cover,  double mainKValue,  double mainSteelRatio,  int minS,  int maxS,  double maxWidth,  int maxLayers,  String usage,  double slabThickness,  bool useSlabSelected)?  $default,) {final _that = this;
switch (_that) {
case _CantileverSchemeInputGlobal() when $default != null:
return $default(_that.id,_that.span,_that.loadWidth,_that.strZone,_that.fcu,_that.cover,_that.mainKValue,_that.mainSteelRatio,_that.minS,_that.maxS,_that.maxWidth,_that.maxLayers,_that.usage,_that.slabThickness,_that.useSlabSelected);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _CantileverSchemeInputGlobal extends CantileverSchemeInputGlobal {
   _CantileverSchemeInputGlobal({this.id = '1', this.span = 20.0, this.loadWidth = 10.0, this.strZone = 1000.0, this.fcu = 45.0, this.cover = 40.0, this.mainKValue = 0.25, this.mainSteelRatio = 0.025, this.minS = 100, this.maxS = 300, this.maxWidth = 2000.0, this.maxLayers = 4, this.usage = '', this.slabThickness = 150.0, this.useSlabSelected = false}): super._();
  factory _CantileverSchemeInputGlobal.fromJson(Map<String, dynamic> json) => _$CantileverSchemeInputGlobalFromJson(json);

@override@JsonKey() final  String id;
@override@JsonKey() final  double span;
@override@JsonKey() final  double loadWidth;
@override@JsonKey() final  double strZone;
@override@JsonKey() final  double fcu;
@override@JsonKey() final  double cover;
@override@JsonKey() final  double mainKValue;
@override@JsonKey() final  double mainSteelRatio;
@override@JsonKey() final  int minS;
@override@JsonKey() final  int maxS;
@override@JsonKey() final  double maxWidth;
@override@JsonKey() final  int maxLayers;
@override@JsonKey() final  String usage;
@override@JsonKey() final  double slabThickness;
@override@JsonKey() final  bool useSlabSelected;

/// Create a copy of CantileverSchemeInputGlobal
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$CantileverSchemeInputGlobalCopyWith<_CantileverSchemeInputGlobal> get copyWith => __$CantileverSchemeInputGlobalCopyWithImpl<_CantileverSchemeInputGlobal>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$CantileverSchemeInputGlobalToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _CantileverSchemeInputGlobal&&(identical(other.id, id) || other.id == id)&&(identical(other.span, span) || other.span == span)&&(identical(other.loadWidth, loadWidth) || other.loadWidth == loadWidth)&&(identical(other.strZone, strZone) || other.strZone == strZone)&&(identical(other.fcu, fcu) || other.fcu == fcu)&&(identical(other.cover, cover) || other.cover == cover)&&(identical(other.mainKValue, mainKValue) || other.mainKValue == mainKValue)&&(identical(other.mainSteelRatio, mainSteelRatio) || other.mainSteelRatio == mainSteelRatio)&&(identical(other.minS, minS) || other.minS == minS)&&(identical(other.maxS, maxS) || other.maxS == maxS)&&(identical(other.maxWidth, maxWidth) || other.maxWidth == maxWidth)&&(identical(other.maxLayers, maxLayers) || other.maxLayers == maxLayers)&&(identical(other.usage, usage) || other.usage == usage)&&(identical(other.slabThickness, slabThickness) || other.slabThickness == slabThickness)&&(identical(other.useSlabSelected, useSlabSelected) || other.useSlabSelected == useSlabSelected));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,span,loadWidth,strZone,fcu,cover,mainKValue,mainSteelRatio,minS,maxS,maxWidth,maxLayers,usage,slabThickness,useSlabSelected);

@override
String toString() {
  return 'CantileverSchemeInputGlobal(id: $id, span: $span, loadWidth: $loadWidth, strZone: $strZone, fcu: $fcu, cover: $cover, mainKValue: $mainKValue, mainSteelRatio: $mainSteelRatio, minS: $minS, maxS: $maxS, maxWidth: $maxWidth, maxLayers: $maxLayers, usage: $usage, slabThickness: $slabThickness, useSlabSelected: $useSlabSelected)';
}


}

/// @nodoc
abstract mixin class _$CantileverSchemeInputGlobalCopyWith<$Res> implements $CantileverSchemeInputGlobalCopyWith<$Res> {
  factory _$CantileverSchemeInputGlobalCopyWith(_CantileverSchemeInputGlobal value, $Res Function(_CantileverSchemeInputGlobal) _then) = __$CantileverSchemeInputGlobalCopyWithImpl;
@override @useResult
$Res call({
 String id, double span, double loadWidth, double strZone, double fcu, double cover, double mainKValue, double mainSteelRatio, int minS, int maxS, double maxWidth, int maxLayers, String usage, double slabThickness, bool useSlabSelected
});




}
/// @nodoc
class __$CantileverSchemeInputGlobalCopyWithImpl<$Res>
    implements _$CantileverSchemeInputGlobalCopyWith<$Res> {
  __$CantileverSchemeInputGlobalCopyWithImpl(this._self, this._then);

  final _CantileverSchemeInputGlobal _self;
  final $Res Function(_CantileverSchemeInputGlobal) _then;

/// Create a copy of CantileverSchemeInputGlobal
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? span = null,Object? loadWidth = null,Object? strZone = null,Object? fcu = null,Object? cover = null,Object? mainKValue = null,Object? mainSteelRatio = null,Object? minS = null,Object? maxS = null,Object? maxWidth = null,Object? maxLayers = null,Object? usage = null,Object? slabThickness = null,Object? useSlabSelected = null,}) {
  return _then(_CantileverSchemeInputGlobal(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,span: null == span ? _self.span : span // ignore: cast_nullable_to_non_nullable
as double,loadWidth: null == loadWidth ? _self.loadWidth : loadWidth // ignore: cast_nullable_to_non_nullable
as double,strZone: null == strZone ? _self.strZone : strZone // ignore: cast_nullable_to_non_nullable
as double,fcu: null == fcu ? _self.fcu : fcu // ignore: cast_nullable_to_non_nullable
as double,cover: null == cover ? _self.cover : cover // ignore: cast_nullable_to_non_nullable
as double,mainKValue: null == mainKValue ? _self.mainKValue : mainKValue // ignore: cast_nullable_to_non_nullable
as double,mainSteelRatio: null == mainSteelRatio ? _self.mainSteelRatio : mainSteelRatio // ignore: cast_nullable_to_non_nullable
as double,minS: null == minS ? _self.minS : minS // ignore: cast_nullable_to_non_nullable
as int,maxS: null == maxS ? _self.maxS : maxS // ignore: cast_nullable_to_non_nullable
as int,maxWidth: null == maxWidth ? _self.maxWidth : maxWidth // ignore: cast_nullable_to_non_nullable
as double,maxLayers: null == maxLayers ? _self.maxLayers : maxLayers // ignore: cast_nullable_to_non_nullable
as int,usage: null == usage ? _self.usage : usage // ignore: cast_nullable_to_non_nullable
as String,slabThickness: null == slabThickness ? _self.slabThickness : slabThickness // ignore: cast_nullable_to_non_nullable
as double,useSlabSelected: null == useSlabSelected ? _self.useSlabSelected : useSlabSelected // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}


}

// dart format on
