//* package from pub
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:structify/domain_layer/steel_transfer_truss_scheme_input.dart';
import 'package:structify/presentation_layer/controllers/steel_transfer_truss_scheme_input_controller.dart';
import 'package:structify/presentation_layer/screen/appraisal.dart';
import 'package:structify/presentation_layer/screen/recommended_loading.dart';
import 'package:structify/presentation_layer/small_elements/basement_wall_scheme_input_global_ui.dart';
import 'package:structify/presentation_layer/small_elements/steel_transfer_truss_scheme_input_global_ui.dart';

//* app data
import '../../../application_layer/app_watcher.dart';

//* data layer
import '../../../data_layer/app_database.dart';
import '../../../data_layer/controller/app_database_controller.dart';

//* domain layer
import '../../domain_layer/basement_wall_scheme_data.dart';
import '../../domain_layer/basement_wall_scheme_input.dart';
import '../../domain_layer/cantilever_scheme_data.dart';
import '../../domain_layer/cantilever_scheme_input.dart';
import '../../domain_layer/cantilever_scheme_input_global.dart';
import '../../domain_layer/column_scheme_data.dart';
import '../../domain_layer/column_scheme_input.dart';
import '../../domain_layer/data_struct/carbon_struct.dart';
import '../../domain_layer/data_struct/recommend_load.dart';
import '../../domain_layer/footing_scheme_data.dart';
import '../../domain_layer/footing_scheme_input.dart';
import '../../domain_layer/footing_scheme_input_global.dart';
import '../../domain_layer/pile_driven_data.dart';
import '../../domain_layer/pile_driven_input.dart';
import '../../domain_layer/pile_driven_input_global.dart';
import '../../domain_layer/pile_end_bearing_bored_data.dart';
import '../../domain_layer/pile_end_bearing_bored_input.dart';
import '../../domain_layer/pile_end_bearing_bored_input_global.dart';
import '../../domain_layer/pile_frictional_bored_data.dart';
import '../../domain_layer/pile_frictional_bored_input.dart';
import '../../domain_layer/pile_frictional_bored_input_global.dart';
import '../../domain_layer/pile_socketed_data.dart';
import '../../domain_layer/programme_item.dart';
import '../../domain_layer/slab_scheme_data.dart';
import '../../domain_layer/steel_beam_scheme_input.dart';
import '../../domain_layer/steel_cantilever_truss_scheme_data.dart';
import '../../domain_layer/steel_cantilever_truss_scheme_input.dart';
import '../../domain_layer/steel_cantilever_truss_scheme_input_global.dart';
import '../../domain_layer/steel_column_scheme_input_global.dart';
import '../../domain_layer/steel_transfer_truss_scheme_data.dart';
import '../../domain_layer/steel_transfer_truss_scheme_input_global.dart';
import '../../domain_layer/strzone_table.dart';
import '../../domain_layer/transfer_beam_scheme_data.dart';
import '../../domain_layer/transfer_beam_scheme_input.dart';
import '../../domain_layer/transfer_beam_scheme_input_global.dart';
import '../../domain_layer/column_scheme_input_global.dart';
import '../../domain_layer/beam_scheme_input.dart';
import '../../domain_layer/loading_table.dart';
import '../../domain_layer/global_data.dart';
import '../../domain_layer/beam_scheme_data.dart';

//* presentation layer
import '../../domain_layer/wind_load.dart';
import '../../domain_layer/wind_load_global.dart';
import '../controllers/basement_wall_scheme_data_controller .dart';
import '../controllers/basement_wall_scheme_input_controller.dart';
import '../controllers/cantilever_scheme_data_controller.dart';
import '../controllers/cantilever_scheme_input_controller.dart';
import '../controllers/cantilever_scheme_input_global_controller.dart';
import '../controllers/carbon_input_controller.dart';
import '../controllers/column_scheme_data_controller.dart';
import '../controllers/column_scheme_input_controller.dart';
import '../controllers/column_scheme_input_global_controller.dart';
import '../controllers/footing_scheme_data_controller.dart';
import '../controllers/footing_scheme_input_controller.dart';
import '../controllers/footing_scheme_input_global_controller.dart';
import '../controllers/pile_driven_data_controller.dart';
import '../controllers/pile_driven_input_controller.dart';
import '../controllers/pile_driven_input_global_controller.dart';
import '../controllers/pile_end_bearing_bored_data_controller.dart';
import '../controllers/pile_end_bearing_bored_input_controller.dart';
import '../controllers/pile_end_bearing_bored_input_global_controller.dart';
import '../controllers/pile_frictional_bored_data_controller.dart';
import '../controllers/pile_frictional_bored_input_controller.dart';
import '../controllers/pile_frictional_bored_input_global_controller.dart';
import '../controllers/pile_socketed_data_controller.dart';
import '../controllers/programme_items_controller.dart';
import '../controllers/recommended_load_controller.dart';
import '../controllers/slab_scheme_data_controller.dart';
import '../controllers/steel_beam_scheme_data_controller.dart';
import '../controllers/steel_beam_scheme_input_controller.dart';
import '../controllers/steel_cantilever_truss_scheme_data_controller.dart';
import '../controllers/steel_cantilever_truss_scheme_input_controller.dart';
import '../controllers/steel_cantilever_truss_scheme_input_global_controller.dart';
import '../controllers/steel_transfer_truss_scheme_data_controller.dart';
import '../controllers/steel_transfer_truss_scheme_input_global_controller.dart';
import '../controllers/strzone_tables_controller.dart';
import '../controllers/transfer_beam_scheme_data_controller.dart';
import '../controllers/transfer_beam_scheme_input_controller.dart';
import '../controllers/windload_input_controller.dart';
import '../controllers/windload_input_global_controller.dart';
import '../screen/column_scheme.dart';
import '../screen/slab_scheme.dart';
import '../screen/steel_beam_scheme.dart';
import '../screen/steel_column_scheme.dart';
import '../screen/transfer_scheme.dart';
import '../small_elements/cantilever_scheme_input_global_ui.dart';
import '../small_elements/column_scheme_input_global_ui.dart';
import '../small_elements/slab_scheme_input_global_ui.dart';
import '../small_elements/steel_cantilever_truss_scheme_input_global_ui.dart';
import '../small_elements/transfer_beam_scheme_input_global_ui.dart';
import '../../domain_layer/slab_scheme_input.dart';
import '../../domain_layer/steel_beam_scheme_data.dart';
import '../../domain_layer/steel_column_scheme_data.dart';
import '../../domain_layer/steel_column_scheme_input.dart';
import '../controllers/loading_tables_controller.dart';
import '../controllers/global_data_controller.dart';
import '../controllers/beam_scheme_data_controller.dart';
import '../controllers/beam_scheme_input_controller.dart';
import '../controllers/slab_scheme_input_controller.dart';
import '../controllers/steel_column_scheme_data_controller.dart';
import '../controllers/steel_column_scheme_input_controller.dart';
import '../controllers/steel_column_scheme_input_global_controller.dart';
import '../controllers/transfer_beam_scheme_input_global_controller.dart';
import '../small_elements/global_data_ui.dart';
import '../small_elements/beam_scheme_input_global_ui.dart';
import '../screen/beam_scheme.dart';
import '../screen/loadcals_screen.dart';
import '../small_elements/steel_beam_scheme_input_global_ui.dart';
import './footing_scheme.dart';
import './pile_driven_scheme.dart';
import './pile_frictional_bored_scheme.dart';
import './pile_socketed_scheme.dart';
import './scheme_comparison.dart';
import './steel_transfer_scheme.dart';
import './strzonecals_screen.dart';
import 'basement_wall_scheme.dart';
import 'cantilever_scheme.dart';
import 'carbon_cals.dart';
import 'pile_end_bearing_bored_scheme.dart';
import 'programme.dart';
import 'steel_cantilever_scheme.dart';
import 'windload_cals.dart';

//* ====================================================
//* Global Providers
//* ====================================================
final loadingTablesControllerProvider = AsyncNotifierProvider.autoDispose<
  LoadingTablesController,
  List<LoadingTable>
>(() {
  return LoadingTablesController();
});

final globalDataControllerProvider =
    AsyncNotifierProvider.autoDispose<GlobalDataController, GlobalData>(() {
      return GlobalDataController();
    });

final beamSchemeInputControllerProvider = AsyncNotifierProvider.autoDispose<
  BeamSchemeInputController,
  BeamSchemeInput
>(() {
  return BeamSchemeInputController();
});

final beamSchemeDataControllerProvider = AsyncNotifierProvider.autoDispose<
  BeamSchemeDataController,
  List<BeamSchemeData>
>(() {
  return BeamSchemeDataController();
});

final slabSchemeInputControllerProvider = AsyncNotifierProvider.autoDispose<
  SlabSchemeInputController,
  SlabSchemeInput
>(() {
  return SlabSchemeInputController();
});

final slabSchemeDataControllerProvider = AsyncNotifierProvider.autoDispose<
  SlabSchemeDataController,
  List<SlabSchemeData>
>(() {
  return SlabSchemeDataController();
});

final columnSchemeInputControllerProvider = AsyncNotifierProvider.autoDispose<
  ColumnSchemeInputController,
  List<ColumnSchemeInput>
>(() {
  return ColumnSchemeInputController();
});

final columnSchemeInputGlobalControllerProvider =
    AsyncNotifierProvider.autoDispose<
      ColumnSchemeInputGlobalController,
      ColumnSchemeInputGlobal
    >(() {
      return ColumnSchemeInputGlobalController();
    });

final columnSchemeDataControllerProvider = AsyncNotifierProvider.autoDispose<
  ColumnSchemeDataController,
  List<ColumnSchemeData>
>(() {
  return ColumnSchemeDataController();
});

final transferBeamSchemeInputGlobalControllerProvider =
    AsyncNotifierProvider.autoDispose<
      TransferBeamSchemeInputGlobalController,
      TransferBeamSchemeInputGlobal
    >(() {
      return TransferBeamSchemeInputGlobalController();
    });

final transferBeamSchemeInputControllerProvider =
    AsyncNotifierProvider.autoDispose<
      TransferBeamSchemeInputController,
      List<TransferBeamSchemeInput>
    >(() {
      return TransferBeamSchemeInputController();
    });

final transferBeamSchemeDataControllerProvider =
    AsyncNotifierProvider.autoDispose<
      TransferBeamSchemeDataController,
      TransferBeamSchemeData
    >(() {
      return TransferBeamSchemeDataController();
    });

final cantileverSchemeInputGlobalControllerProvider =
    AsyncNotifierProvider.autoDispose<
      CantileverSchemeInputGlobalController,
      CantileverSchemeInputGlobal
    >(() {
      return CantileverSchemeInputGlobalController();
    });

final cantileverSchemeInputControllerProvider =
    AsyncNotifierProvider.autoDispose<
      CantileverSchemeInputController,
      List<CantileverSchemeInput>
    >(() {
      return CantileverSchemeInputController();
    });

final cantileverSchemeDataControllerProvider =
    AsyncNotifierProvider.autoDispose<
      CantileverSchemeDataController,
      CantileverSchemeData
    >(() {
      return CantileverSchemeDataController();
    });

final steelBeamSchemeInputControllerProvider =
    AsyncNotifierProvider.autoDispose<
      SteelBeamSchemeInputController,
      SteelBeamSchemeInput
    >(() {
      return SteelBeamSchemeInputController();
    });

final steelBeamSchemeDataControllerProvider = AsyncNotifierProvider.autoDispose<
  SteelBeamSchemeDataController,
  List<SteelBeamSchemeData>
>(() {
  return SteelBeamSchemeDataController();
});

final steelColumnSchemeInputControllerProvider =
    AsyncNotifierProvider.autoDispose<
      SteelColumnSchemeInputController,
      List<SteelColumnSchemeInput>
    >(() {
      return SteelColumnSchemeInputController();
    });

final steelColumnSchemeInputGlobalControllerProvider =
    AsyncNotifierProvider.autoDispose<
      SteelColumnSchemeInputGlobalController,
      SteelColumnSchemeInputGlobal
    >(() {
      return SteelColumnSchemeInputGlobalController();
    });

final steelColumnSchemeDataControllerProvider =
    AsyncNotifierProvider.autoDispose<
      SteelColumnSchemeDataController,
      List<SteelColumnSchemeData>
    >(() {
      return SteelColumnSchemeDataController();
    });

final steelTransferTrussSchemeInputControllerProvider =
    AsyncNotifierProvider.autoDispose<
      SteelTransferTrussSchemeInputController,
      List<SteelTransferTrussSchemeInput>
    >(() {
      return SteelTransferTrussSchemeInputController();
    });

final steelTransferTrussSchemeInputGlobalControllerProvider =
    AsyncNotifierProvider.autoDispose<
      SteelTransferTrussSchemeInputGlobalController,
      SteelTransferTrussSchemeInputGlobal
    >(() {
      return SteelTransferTrussSchemeInputGlobalController();
    });

final steelTransferTrussSchemeDataControllerProvider =
    AsyncNotifierProvider.autoDispose<
      SteelTransferTrussSchemeDataController,
      SteelTransferTrussSchemeData
    >(() {
      return SteelTransferTrussSchemeDataController();
    });

final steelCantileverTrussSchemeInputControllerProvider =
    AsyncNotifierProvider.autoDispose<
      SteelCantileverTrussSchemeInputController,
      List<SteelCantileverTrussSchemeInput>
    >(() {
      return SteelCantileverTrussSchemeInputController();
    });

final steelCantileverTrussSchemeInputGlobalControllerProvider =
    AsyncNotifierProvider.autoDispose<
      SteelCantileverTrussSchemeInputGlobalController,
      SteelCantileverTrussSchemeInputGlobal
    >(() {
      return SteelCantileverTrussSchemeInputGlobalController();
    });

final steelCantileverTrussSchemeDataControllerProvider =
    AsyncNotifierProvider.autoDispose<
      SteelCantileverTrussSchemeDataController,
      SteelCantileverTrussSchemeData
    >(() {
      return SteelCantileverTrussSchemeDataController();
    });

final pileFrictionalBoredInputControllerProvider =
    AsyncNotifierProvider.autoDispose<
      PileFrictionalBoredInputController,
      PileFrictionalBoredInput
    >(() {
      return PileFrictionalBoredInputController();
    });

final pileFrictionalBoredInputGlobalControllerProvider =
    AsyncNotifierProvider.autoDispose<
      PileFrictionalBoredInputGlobalController,
      PileFrictionalBoredInputGlobal
    >(() {
      return PileFrictionalBoredInputGlobalController();
    });

final pileFrictionalBoredDataControllerProvider =
    AsyncNotifierProvider.autoDispose<
      PileFrictionalBoredDataController,
      List<PileFrictionalBoredData>
    >(() {
      return PileFrictionalBoredDataController();
    });
final pileEndBearingBoredInputControllerProvider =
    AsyncNotifierProvider.autoDispose<
      PileEndBearingBoredInputController,
      PileEndBearingBoredInput
    >(() {
      return PileEndBearingBoredInputController();
    });

final pileEndBearingBoredInputGlobalControllerProvider =
    AsyncNotifierProvider.autoDispose<
      PileEndBearingBoredInputGlobalController,
      PileEndBearingBoredInputGlobal
    >(() {
      return PileEndBearingBoredInputGlobalController();
    });

final pileEndBearingBoredDataControllerProvider =
    AsyncNotifierProvider.autoDispose<
      PileEndBearingBoredDataController,
      List<PileEndBearingBoredData>
    >(() {
      return PileEndBearingBoredDataController();
    });

final pileSocketedDataControllerProvider =
    AsyncNotifierProvider.autoDispose<PileSocketedController, PileSocketedData>(
      () {
        return PileSocketedController();
      },
    );

final pileDrivenInputControllerProvider = AsyncNotifierProvider.autoDispose<
  PileDrivenInputController,
  PileDrivenInput
>(() {
  return PileDrivenInputController();
});

final pileDrivenInputGlobalControllerProvider =
    AsyncNotifierProvider.autoDispose<
      PileDrivenInputGlobalController,
      PileDrivenInputGlobal
    >(() {
      return PileDrivenInputGlobalController();
    });

final pileDrivenDataControllerProvider = AsyncNotifierProvider.autoDispose<
  PileDrivenDataController,
  List<PileDrivenData>
>(() {
  return PileDrivenDataController();
});

final footingSchemeInputControllerProvider = AsyncNotifierProvider.autoDispose<
  FootingSchemeInputController,
  FootingSchemeInput
>(() {
  return FootingSchemeInputController();
});

final footingSchemeInputGlobalControllerProvider =
    AsyncNotifierProvider.autoDispose<
      FootingSchemeInputGlobalController,
      FootingSchemeInputGlobal
    >(() {
      return FootingSchemeInputGlobalController();
    });

final footingSchemeDataControllerProvider = AsyncNotifierProvider.autoDispose<
  FootingSchemeDataController,
  List<FootingSchemeData>
>(() {
  return FootingSchemeDataController();
});

final strZoneTablesControllerProvider = AsyncNotifierProvider.autoDispose<
  StrZoneTablesController,
  List<StrZoneTable>
>(() {
  return StrZoneTablesController();
});

final appDatabaseControllerProvider =
    AsyncNotifierProvider.autoDispose<AppDatabaseController, AppDatabase>(() {
      return AppDatabaseController();
    });

final appWatcherProvider = AsyncNotifierProvider.autoDispose<AppWatcher, void>(
  () {
    return AppWatcher();
  },
);

final recommendedLoadControllerProvider = AsyncNotifierProvider.autoDispose<
  RecommendedLoadController,
  List<RecommendedLoad>
>(() {
  return RecommendedLoadController();
});

final basementWallSchemeInputControllerProvider =
    AsyncNotifierProvider.autoDispose<
      BasementWallSchemeInputController,
      BasementWallSchemeInput
    >(() {
      return BasementWallSchemeInputController();
    });

final basementWallSchemeDataControllerProvider =
    AsyncNotifierProvider.autoDispose<
      BasementWallSchemeDataController,
      BasementWallSchemeData
    >(() {
      return BasementWallSchemeDataController();
    });

final programmeItemsControllerProvider = AsyncNotifierProvider.autoDispose<
  ProgrammeItemsController,
  List<ProgrammeItem>
>(() {
  return ProgrammeItemsController();
});

final windLoadInputControllerProvider =
    AsyncNotifierProvider.autoDispose<WindLoadInputController, List<WindLoad>>(
      () {
        return WindLoadInputController();
      },
    );

final windLoadInputGlobalControllerProvider = AsyncNotifierProvider.autoDispose<
  WindLoadInputGlobalController,
  WindLoadGlobal
>(() {
  return WindLoadInputGlobalController();
});

final carbonInputControllerProvider =
    AsyncNotifierProvider.autoDispose<CarbonInputController, List<Carbon>>(() {
      return CarbonInputController();
    });

//* ====================================================
//* homepage for the whole app
//* ====================================================
class HomeScreen extends ConsumerStatefulWidget {
  const HomeScreen({super.key});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _HomeScreenState();
}

class _HomeScreenState extends ConsumerState<HomeScreen>
    with SingleTickerProviderStateMixin {
  late final GlobalDataUi globalDataUI;
  late final ScrollController _scrollController;
  late final ScrollController _scrollController2;

  late final TabController _tabController;

  late final ColorScheme _colorScheme;
  late final TextTheme _textTheme;

  late List<Tab> _tabs;
  late TabBarView _tabBarView;
  late final List<GlobalKey> _tabKeys;
  late final int _tabLength;
  late final List<String> _tabNames;

  @override
  void dispose() {
    _scrollController.dispose();
    _scrollController2.dispose();
    _tabController.dispose();
    super.dispose();
  }

  @override
  void initState() {
    globalDataUI = GlobalDataUi();
    _scrollController = ScrollController();
    _scrollController2 = ScrollController();
    //! Remember to change this to add tabs
    _tabNames = <String>[
      'Settings',
      'Loadings',
      'Slab',
      'Beam',
      'Column',
      'Transfer',
      'Cantilever',
      'Foundation',
      'Miscellaneous',
    ];
    _tabLength = _tabNames.length;
    _tabController = TabController(length: _tabLength, vsync: this);
    _tabController.addListener(() {
      if (_tabController.indexIsChanging) {
        // Scroll to the selected tab
        scrollToSelectedTab(_tabController.index, _scrollController, _tabKeys);
      }
    });
    super.initState();
  }

  @override
  //since we need to access context before build, we use use override
  // to initialize the tabs
  void didChangeDependencies() {
    _colorScheme = Theme.of(context).colorScheme;
    _textTheme = Theme.of(context).textTheme;
    _tabKeys = List.generate(_tabLength, (_) => GlobalKey());
    _tabs = List.generate(_tabLength, (i) {
      return tabBuilder(
        index: i + 1,
        tabKey: _tabKeys[i],
        tabLabel: _tabNames[i],
        bgColor: _colorScheme.primary,
        indexLabelStyle: _textTheme.labelLarge!.copyWith(
          color: _colorScheme.onPrimary,
        ),
        labelStyle: _textTheme.labelLarge!.copyWith(
          color: _colorScheme.onPrimary,
        ),
      );
    });
    _tabBarView = TabBarView(
      controller: _tabController,
      children: <Widget>[
        NestedTabBarGlobalData(),
        Row(
          children: [
            Flexible(flex: 3, child: LoadingCalculator()),
            // Flexible(flex: 2, child: SummaryPane()),
          ],
        ),
        Row(children: [Flexible(flex: 3, child: SlabSchemeCalculator())]),
        NestedTabBarBeamScheme(),
        NestedTabBarColumnScheme(),
        NestedTabBarTransferScheme(),
        NestedTabBarCantileverScheme(),
        NestedTabBarFdnScheme(),
        NestedTabBarMisc(),
      ],
    );
    super.didChangeDependencies();
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    // !watches all providers such that their build will be called once only
    // !otherwise, unwatched provider might at some point has no watcher
    // !and it will be disposed, and recreated once someone start watching it again
    // !as a result, its build runs again and its previous state is lost

    //* general data, loading, and scheme watcher
    final appWatcher = ref.watch(appWatcherProvider);
    final globalData = ref.watch(globalDataControllerProvider);
    final loadingTables = ref.watch(loadingTablesControllerProvider);
    final slabSchemeData = ref.watch(slabSchemeDataControllerProvider);
    final beamSchemeData = ref.watch(beamSchemeDataControllerProvider);
    final columnSchemeData = ref.watch(columnSchemeDataControllerProvider);
    final steelBeamSchemeData = ref.watch(
      steelBeamSchemeDataControllerProvider,
    );
    final steelColumnSchemeData = ref.watch(
      steelColumnSchemeDataControllerProvider,
    );
    final transferBeamSchemeData = ref.watch(
      transferBeamSchemeDataControllerProvider,
    );
    final cantileverSchemeData = ref.watch(
      cantileverSchemeDataControllerProvider,
    );
    final steelTransferTrussSchemeData = ref.watch(
      steelTransferTrussSchemeDataControllerProvider,
    );
    final steelCantileverTrussSchemeData = ref.watch(
      steelCantileverTrussSchemeDataControllerProvider,
    );
    final pileFrictionalBoredData = ref.watch(
      pileFrictionalBoredDataControllerProvider,
    );
    final pileEndBearingBoredData = ref.watch(
      pileEndBearingBoredDataControllerProvider,
    );
    final pileSocketedData = ref.watch(pileSocketedDataControllerProvider);
    final pileDrivenData = ref.watch(pileDrivenDataControllerProvider);
    final footingSchemeData = ref.watch(footingSchemeDataControllerProvider);

    //* input watcher
    final beamSchemeInputController = ref.watch(
      beamSchemeInputControllerProvider,
    );
    final slabSchemeInputController = ref.watch(
      slabSchemeInputControllerProvider,
    );
    final columnSchemeInputController = ref.watch(
      columnSchemeInputControllerProvider,
    );
    final columnSchemeInputGlobalController = ref.watch(
      columnSchemeInputGlobalControllerProvider,
    );
    final transferBeamSchemeInputController = ref.watch(
      transferBeamSchemeInputControllerProvider,
    );
    final transferBeamSchemeInputGlobalController = ref.watch(
      transferBeamSchemeInputGlobalControllerProvider,
    );
    final cantileverSchemeInputController = ref.watch(
      cantileverSchemeInputControllerProvider,
    );
    final cantileverSchemeInputGlobalController = ref.watch(
      cantileverSchemeInputGlobalControllerProvider,
    );
    final steelBeamSchemeInput = ref.watch(
      steelBeamSchemeInputControllerProvider,
    );
    final steelColumnSchemeInput = ref.watch(
      steelColumnSchemeInputControllerProvider,
    );
    final steelColumnSchemeInputGlobal = ref.watch(
      steelColumnSchemeInputGlobalControllerProvider,
    );
    final steelTransferTrussSchemeInput = ref.watch(
      steelTransferTrussSchemeInputControllerProvider,
    );
    final steelTransferTrussSchemeInputGlobal = ref.watch(
      steelTransferTrussSchemeInputGlobalControllerProvider,
    );
    final steelCantileverTrussSchemeInput = ref.watch(
      steelCantileverTrussSchemeInputControllerProvider,
    );
    final steelCantileverTrussSchemeInputGlobal = ref.watch(
      steelCantileverTrussSchemeInputGlobalControllerProvider,
    );
    final pileEndBearingBoredInput = ref.watch(
      pileEndBearingBoredInputControllerProvider,
    );
    final pileEndBearingBoredInputGlobal = ref.watch(
      pileEndBearingBoredInputGlobalControllerProvider,
    );
    final pileFrictionalBoredInput = ref.watch(
      pileFrictionalBoredInputControllerProvider,
    );
    final pileFrictionalBoredInputGlobal = ref.watch(
      pileFrictionalBoredInputGlobalControllerProvider,
    );
    final pileDrivenInput = ref.watch(pileDrivenInputControllerProvider);

    final footingSchemeInput = ref.watch(footingSchemeInputControllerProvider);

    final footingSchemeInputGlobal = ref.watch(
      footingSchemeInputGlobalControllerProvider,
    );

    //* Misc watcher
    final strZoneTables = ref.watch(strZoneTablesControllerProvider);
    final recommendedLoad = ref.watch(recommendedLoadControllerProvider);
    final basementWallSchemeInput = ref.watch(
      basementWallSchemeInputControllerProvider,
    );
    final basementWallSchemeData = ref.watch(
      basementWallSchemeDataControllerProvider,
    );
    final programmeItems = ref.watch(programmeItemsControllerProvider);
    final windLoadInput = ref.watch(windLoadInputControllerProvider);
    final windLoadInputGlobal = ref.watch(
      windLoadInputGlobalControllerProvider,
    );
    final carbonInput = ref.watch(carbonInputControllerProvider);
    return globalData.when(
      data: (data) {
        // final colorScheme = Theme.of(context).colorScheme;
        Color labelColor = colorScheme.onPrimary;
        Color unselectedLabelColor = colorScheme.onPrimary.withAlpha(150);

        return DefaultTabController(
          length: _tabLength, //! remeber to change this when adding tabs!
          child: Scaffold(
            appBar: AppBar(
              title: Text(
                "Simple Structural Calculator",
                style: textTheme.titleLarge!.copyWith(
                  color: colorScheme.onPrimary,
                ),
              ),
              bottom: PreferredSize(
                preferredSize: const Size.fromHeight(50.0),
                child: Scrollbar(
                  controller: _scrollController,
                  thickness: 6,
                  thumbVisibility: true,
                  trackVisibility: false,
                  child: Padding(
                    padding: const EdgeInsets.fromLTRB(0, 0, 0, 10),
                    child: SingleChildScrollView(
                      scrollDirection: Axis.horizontal,
                      controller: _scrollController,
                      child: TabBar(
                        controller: _tabController,
                        isScrollable: true,
                        labelColor: labelColor,
                        unselectedLabelColor: unselectedLabelColor,
                        dividerColor: Colors.transparent,
                        indicator: UnderlineTabIndicator(
                          borderSide: BorderSide(
                            color: _colorScheme.onPrimary.withAlpha(150),
                            width: 1.25,
                          ),
                          insets: EdgeInsets.fromLTRB(0, 0, 0, 8),
                        ),
                        tabs: _tabs,
                      ),
                    ),
                  ),
                ),
              ),
              backgroundColor: colorScheme.primary,
            ),
            body: _tabBarView,
          ),
        );
      },
      error: (error, _) => Text('Error'),
      loading: () => Center(child: CircularProgressIndicator()),
    );
  }
}

//* ====================================================
//* Nested tabs
//* ====================================================
class NestedTabBarGlobalData extends StatefulWidget {
  const NestedTabBarGlobalData({super.key});

  @override
  State<NestedTabBarGlobalData> createState() => _NestedTabBarGlobalDataState();
}

class _NestedTabBarGlobalDataState extends State<NestedTabBarGlobalData>
    with TickerProviderStateMixin {
  late final TabController _tabController;
  late final List<Widget> _tabs;
  late final TabBarView _tabBarView;
  late final ColorScheme _colorScheme;
  late final TextTheme _textTheme;
  late final List<GlobalKey> _tabKeys;
  late final int _tabLength; // RMEMBER TO CHANGE THIS TO ADD TAB
  late final List<String> _tabNames = [
    'General',
    'Slab',
    'Beam',
    'Column',
    'Transfer',
    'Cantilever',
    'Miscellaneous',
  ];
  late final ScrollController _scrollController;

  @override
  void initState() {
    super.initState();
    _tabLength = _tabNames.length;
    _tabController = TabController(length: _tabLength, vsync: this);
    _scrollController = ScrollController();
    _tabController.addListener(() {
      if (_tabController.indexIsChanging) {
        // Scroll to the selected tab
        scrollToSelectedTab(_tabController.index, _scrollController, _tabKeys);
      }
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  void didChangeDependencies() {
    // TODO: implement didChangeDependencies
    super.didChangeDependencies();
    _colorScheme = Theme.of(context).colorScheme;
    _textTheme = Theme.of(context).textTheme;
    _tabKeys = List.generate(_tabLength, (_) => GlobalKey());
    _tabs = List.generate(_tabLength, (i) {
      return Container(
        decoration: BoxDecoration(
          color: _colorScheme.primaryContainer.withAlpha(50),
          borderRadius: BorderRadius.circular(10),
        ),
        child: Padding(
          padding: const EdgeInsets.fromLTRB(4, 0, 10, 0),
          child: tabBuilder(
            tabKey: _tabKeys[i],
            tabLabel: _tabNames[i],
            indexLabelStyle: _textTheme.labelLarge!.copyWith(
              color: _colorScheme.onPrimaryContainer,
            ),
            labelStyle: _textTheme.labelLarge!.copyWith(
              color: _colorScheme.onPrimaryContainer,
            ),
          ),
        ),
      );
    });
    _tabBarView = TabBarView(
      controller: _tabController,
      children: <Widget>[
        Padding(
          padding: const EdgeInsets.fromLTRB(15, 0, 15, 0),
          child: Row(
            children: [
              Flexible(flex: 3, child: GlobalDataUi(isExpanded: true)),
            ],
          ),
        ),
        Padding(
          padding: const EdgeInsets.fromLTRB(15, 0, 15, 0),
          child: Row(
            children: [
              Flexible(
                flex: 3,
                child: SlabSchemeInputGlobalUi(isExpanded: true),
              ),
            ],
          ),
        ),
        Padding(
          padding: const EdgeInsets.fromLTRB(15, 0, 15, 0),
          child: Row(
            children: [
              Flexible(
                flex: 3,
                child: Column(
                  children: [
                    BeamSchemeInputGlobalUi(isExpanded: true),
                    SteelBeamSchemeInputGlobalUi(isExpanded: true),
                  ],
                ),
              ),
            ],
          ),
        ),
        Padding(
          padding: const EdgeInsets.fromLTRB(15, 0, 15, 0),
          child: Row(
            children: [
              Flexible(
                flex: 3,
                child: Column(
                  children: [
                    ColumnSchemeInputGlobalUi(isExpanded: true),
                    // SteelColumnSchemeInputGlobalUi(isExpanded: true),
                  ],
                ),
              ),
            ],
          ),
        ),
        Padding(
          padding: const EdgeInsets.fromLTRB(15, 0, 15, 0),
          child: Row(
            children: [
              Flexible(
                flex: 3,
                child: Column(
                  children: [
                    TransferBeamSchemeInputGlobalUi(isExpanded: true),
                    SteelTransferTrussSchemeInputGlobalUi(isExpanded: true),
                  ],
                ),
              ),
            ],
          ),
        ),
        Padding(
          padding: const EdgeInsets.fromLTRB(15, 0, 15, 0),
          child: Row(
            children: [
              Flexible(
                flex: 3,
                child: Column(
                  children: [
                    CantileverSchemeInputGlobalUi(isExpanded: true),
                    SteelCantileverTrussSchemeInputGlobalUi(isExpanded: true),
                  ],
                ),
              ),
            ],
          ),
        ),
        Padding(
          padding: const EdgeInsets.fromLTRB(15, 0, 15, 0),
          child: Row(
            children: [
              Flexible(
                child: BasementWallSchemeInputGlobalUi(isExpanded: true),
              ),
            ],
          ),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: <Widget>[
        Scrollbar(
          controller: _scrollController,
          thumbVisibility: true,
          thickness: 6.0,
          child: SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            controller: _scrollController,
            child: Padding(
              padding: const EdgeInsets.fromLTRB(0, 5, 0, 10),
              child: TabBar.secondary(
                controller: _tabController,
                isScrollable: true,
                dividerColor: Colors.transparent,
                indicatorColor: _colorScheme.primaryContainer.withAlpha(225),
                indicator: UnderlineTabIndicator(
                  borderSide: BorderSide(
                    color: _colorScheme.primary.withAlpha(150),
                    width: 1.25,
                  ),
                  insets: EdgeInsets.fromLTRB(20, 0, 20, 8),
                ),
                tabs: _tabs,
              ),
            ),
          ),
        ),
        Flexible(child: _tabBarView),
      ],
    );
  }
}

class NestedTabBarBeamScheme extends StatefulWidget {
  const NestedTabBarBeamScheme({super.key});

  @override
  State<NestedTabBarBeamScheme> createState() => _NestedTabBarBeamSchemeState();
}

class _NestedTabBarBeamSchemeState extends State<NestedTabBarBeamScheme>
    with TickerProviderStateMixin {
  late final TabController _tabController;
  late final List<Widget> _tabs;
  late final TabBarView _tabBarView;
  late final ColorScheme _colorScheme;
  late final TextTheme _textTheme;
  late final List<GlobalKey> _tabKeys;
  late final int _tabLength = 2; // RMEMBER TO CHANGE THIS TO ADD TAB

  late final ScrollController _scrollController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: _tabLength, vsync: this);
    _scrollController = ScrollController();
    _tabController.addListener(() {
      if (_tabController.indexIsChanging) {
        // Scroll to the selected tab
        scrollToSelectedTab(_tabController.index, _scrollController, _tabKeys);
      }
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  void didChangeDependencies() {
    // TODO: implement didChangeDependencies
    super.didChangeDependencies();
    _colorScheme = Theme.of(context).colorScheme;
    _textTheme = Theme.of(context).textTheme;
    _tabKeys = List.generate(_tabLength, (_) => GlobalKey());
    _tabs = <Widget>[
      Container(
        decoration: BoxDecoration(
          color: _colorScheme.primaryContainer.withAlpha(50),
          borderRadius: BorderRadius.circular(10),
        ),
        child: Padding(
          padding: const EdgeInsets.fromLTRB(4, 0, 10, 0),
          child: tabBuilder(
            tabKey: _tabKeys[0],
            tabLabel: 'RC',
            indexLabelStyle: _textTheme.labelLarge!.copyWith(
              color: _colorScheme.onPrimaryContainer,
            ),
            labelStyle: _textTheme.labelLarge!.copyWith(
              color: _colorScheme.onPrimaryContainer,
            ),
          ),
        ),
      ),
      Padding(
        padding: const EdgeInsets.fromLTRB(4, 0, 10, 0),
        child: Container(
          decoration: BoxDecoration(
            color: _colorScheme.primaryContainer.withAlpha(50),
            borderRadius: BorderRadius.circular(10),
          ),
          child: Padding(
            padding: const EdgeInsets.fromLTRB(4, 0, 10, 0),
            child: tabBuilder(
              tabKey: _tabKeys[1],
              tabLabel: 'Steel',
              indexLabelStyle: _textTheme.labelLarge!.copyWith(
                color: _colorScheme.onPrimaryContainer,
              ),
              labelStyle: _textTheme.labelLarge!.copyWith(
                color: _colorScheme.onPrimaryContainer,
              ),
            ),
          ),
        ),
      ),
    ];

    _tabBarView = TabBarView(
      controller: _tabController,
      children: <Widget>[
        Padding(
          padding: const EdgeInsets.fromLTRB(15, 0, 15, 0),
          child: Row(
            children: [
              Flexible(flex: 3, child: BeamSchemeCalculator()),
              // Flexible(flex: 2, child: SummaryPane()),
            ],
          ),
        ),

        Padding(
          padding: const EdgeInsets.fromLTRB(15, 0, 15, 0),
          child: Row(
            children: [
              Flexible(flex: 3, child: SteelBeamSchemeCalculator()),
              // Flexible(flex: 2, child: SummaryPane()),
            ],
          ),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: <Widget>[
        Scrollbar(
          controller: _scrollController,
          thumbVisibility: true,
          thickness: 6.0,
          child: SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            controller: _scrollController,
            child: Padding(
              padding: const EdgeInsets.fromLTRB(0, 5, 0, 10),
              child: TabBar.secondary(
                controller: _tabController,
                isScrollable: true,
                dividerColor: Colors.transparent,
                indicatorColor: _colorScheme.primaryContainer.withAlpha(225),
                indicator: UnderlineTabIndicator(
                  borderSide: BorderSide(
                    color: _colorScheme.primary.withAlpha(150),
                    width: 1.25,
                  ),
                  insets: EdgeInsets.fromLTRB(20, 0, 20, 8),
                ),
                tabs: _tabs,
              ),
            ),
          ),
        ),
        Flexible(child: _tabBarView),
      ],
    );
  }
}

class NestedTabBarColumnScheme extends StatefulWidget {
  const NestedTabBarColumnScheme({super.key});

  @override
  State<NestedTabBarColumnScheme> createState() =>
      _NestedTabBarColumnSchemeState();
}

class _NestedTabBarColumnSchemeState extends State<NestedTabBarColumnScheme>
    with TickerProviderStateMixin {
  late final TabController _tabController;
  late final List<Widget> _tabs;
  late final TabBarView _tabBarView;
  late final ColorScheme _colorScheme;
  late final TextTheme _textTheme;
  late final List<GlobalKey> _tabKeys;
  late final int _tabLength = 2; // RMEMBER TO CHANGE THIS TO ADD TAB

  late final ScrollController _scrollController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: _tabLength, vsync: this);
    _scrollController = ScrollController();
    _tabController.addListener(() {
      if (_tabController.indexIsChanging) {
        // Scroll to the selected tab
        scrollToSelectedTab(_tabController.index, _scrollController, _tabKeys);
      }
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  void didChangeDependencies() {
    // TODO: implement didChangeDependencies
    super.didChangeDependencies();
    _colorScheme = Theme.of(context).colorScheme;
    _textTheme = Theme.of(context).textTheme;
    _tabKeys = List.generate(_tabLength, (_) => GlobalKey());
    _tabs = <Widget>[
      Container(
        decoration: BoxDecoration(
          color: _colorScheme.primaryContainer.withAlpha(50),
          borderRadius: BorderRadius.circular(10),
        ),
        child: Padding(
          padding: const EdgeInsets.fromLTRB(4, 0, 10, 0),
          child: tabBuilder(
            tabKey: _tabKeys[0],
            tabLabel: 'RC',
            indexLabelStyle: _textTheme.labelLarge!.copyWith(
              color: _colorScheme.onPrimaryContainer,
            ),
            labelStyle: _textTheme.labelLarge!.copyWith(
              color: _colorScheme.onPrimaryContainer,
            ),
          ),
        ),
      ),
      Padding(
        padding: const EdgeInsets.fromLTRB(4, 0, 10, 0),
        child: Container(
          decoration: BoxDecoration(
            color: _colorScheme.primaryContainer.withAlpha(50),
            borderRadius: BorderRadius.circular(10),
          ),
          child: Padding(
            padding: const EdgeInsets.fromLTRB(4, 0, 10, 0),
            child: tabBuilder(
              tabKey: _tabKeys[1],
              tabLabel: 'Steel',
              indexLabelStyle: _textTheme.labelLarge!.copyWith(
                color: _colorScheme.onPrimaryContainer,
              ),
              labelStyle: _textTheme.labelLarge!.copyWith(
                color: _colorScheme.onPrimaryContainer,
              ),
            ),
          ),
        ),
      ),
    ];

    _tabBarView = TabBarView(
      controller: _tabController,
      children: <Widget>[
        Padding(
          padding: const EdgeInsets.fromLTRB(15, 0, 15, 0),
          child: Row(
            children: [
              Flexible(flex: 3, child: ColumnScheme()),
              // Flexible(flex: 2, child: SummaryPane()),
            ],
          ),
        ),

        Padding(
          padding: const EdgeInsets.fromLTRB(15, 0, 15, 0),
          child: Row(
            children: [
              Flexible(flex: 3, child: SteelColumnScheme()),
              // Flexible(flex: 2, child: SummaryPane()),
            ],
          ),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: <Widget>[
        Scrollbar(
          controller: _scrollController,
          thumbVisibility: true,
          thickness: 6.0,
          child: SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            controller: _scrollController,
            child: Padding(
              padding: const EdgeInsets.fromLTRB(0, 5, 0, 10),
              child: TabBar.secondary(
                controller: _tabController,
                isScrollable: true,
                dividerColor: Colors.transparent,
                indicatorColor: _colorScheme.primaryContainer.withAlpha(225),
                indicator: UnderlineTabIndicator(
                  borderSide: BorderSide(
                    color: _colorScheme.primary.withAlpha(150),
                    width: 1.25,
                  ),
                  insets: EdgeInsets.fromLTRB(20, 0, 20, 8),
                ),
                tabs: _tabs,
              ),
            ),
          ),
        ),
        Flexible(child: _tabBarView),
      ],
    );
  }
}

class NestedTabBarTransferScheme extends StatefulWidget {
  const NestedTabBarTransferScheme({super.key});

  @override
  State<NestedTabBarTransferScheme> createState() =>
      _NestedTabBarTransferSchemeState();
}

class _NestedTabBarTransferSchemeState extends State<NestedTabBarTransferScheme>
    with TickerProviderStateMixin {
  late final TabController _tabController;
  late final List<Widget> _tabs;
  late final TabBarView _tabBarView;
  late final ColorScheme _colorScheme;
  late final TextTheme _textTheme;
  late final List<GlobalKey> _tabKeys;
  late final int _tabLength = 2; // RMEMBER TO CHANGE THIS TO ADD TAB

  late final ScrollController _scrollController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: _tabLength, vsync: this);
    _scrollController = ScrollController();
    _tabController.addListener(() {
      if (_tabController.indexIsChanging) {
        // Scroll to the selected tab
        scrollToSelectedTab(_tabController.index, _scrollController, _tabKeys);
      }
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  void didChangeDependencies() {
    // TODO: implement didChangeDependencies
    super.didChangeDependencies();
    _colorScheme = Theme.of(context).colorScheme;
    _textTheme = Theme.of(context).textTheme;
    _tabKeys = List.generate(_tabLength, (_) => GlobalKey());
    _tabs = <Widget>[
      Container(
        decoration: BoxDecoration(
          color: _colorScheme.primaryContainer.withAlpha(50),
          borderRadius: BorderRadius.circular(10),
        ),
        child: Padding(
          padding: const EdgeInsets.fromLTRB(4, 0, 10, 0),
          child: tabBuilder(
            tabKey: _tabKeys[0],
            tabLabel: 'RC',
            indexLabelStyle: _textTheme.labelLarge!.copyWith(
              color: _colorScheme.onPrimaryContainer,
            ),
            labelStyle: _textTheme.labelLarge!.copyWith(
              color: _colorScheme.onPrimaryContainer,
            ),
          ),
        ),
      ),
      Padding(
        padding: const EdgeInsets.fromLTRB(4, 0, 10, 0),
        child: Container(
          decoration: BoxDecoration(
            color: _colorScheme.primaryContainer.withAlpha(50),
            borderRadius: BorderRadius.circular(10),
          ),
          child: Padding(
            padding: const EdgeInsets.fromLTRB(4, 0, 10, 0),
            child: tabBuilder(
              tabKey: _tabKeys[1],
              tabLabel: 'Steel',
              indexLabelStyle: _textTheme.labelLarge!.copyWith(
                color: _colorScheme.onPrimaryContainer,
              ),
              labelStyle: _textTheme.labelLarge!.copyWith(
                color: _colorScheme.onPrimaryContainer,
              ),
            ),
          ),
        ),
      ),
    ];

    _tabBarView = TabBarView(
      controller: _tabController,
      children: <Widget>[
        Padding(
          padding: const EdgeInsets.fromLTRB(15, 0, 15, 0),
          child: Row(
            children: [
              Flexible(flex: 3, child: TransferScheme()),
              // Flexible(flex: 2, child: SummaryPane()),
            ],
          ),
        ),

        Padding(
          padding: const EdgeInsets.fromLTRB(15, 0, 15, 0),
          child: Row(
            children: [
              Flexible(flex: 3, child: SteelTransferScheme()),
              // Flexible(flex: 2, child: SummaryPane()),
            ],
          ),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: <Widget>[
        Scrollbar(
          controller: _scrollController,
          thumbVisibility: true,
          thickness: 6.0,
          child: SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            controller: _scrollController,
            child: Padding(
              padding: const EdgeInsets.fromLTRB(0, 5, 0, 10),
              child: TabBar.secondary(
                controller: _tabController,
                isScrollable: true,
                dividerColor: Colors.transparent,
                indicatorColor: _colorScheme.primaryContainer.withAlpha(225),
                indicator: UnderlineTabIndicator(
                  borderSide: BorderSide(
                    color: _colorScheme.primary.withAlpha(150),
                    width: 1.25,
                  ),
                  insets: EdgeInsets.fromLTRB(20, 0, 20, 8),
                ),
                tabs: _tabs,
              ),
            ),
          ),
        ),
        Flexible(child: _tabBarView),
      ],
    );
  }
}

class NestedTabBarCantileverScheme extends StatefulWidget {
  const NestedTabBarCantileverScheme({super.key});

  @override
  State<NestedTabBarCantileverScheme> createState() =>
      _NestedTabBarCantileverSchemeState();
}

class _NestedTabBarCantileverSchemeState
    extends State<NestedTabBarCantileverScheme>
    with TickerProviderStateMixin {
  late final TabController _tabController;
  late final List<Widget> _tabs;
  late final TabBarView _tabBarView;
  late final ColorScheme _colorScheme;
  late final TextTheme _textTheme;
  late final List<GlobalKey> _tabKeys;
  late final int _tabLength = 2; // RMEMBER TO CHANGE THIS TO ADD TAB

  late final ScrollController _scrollController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: _tabLength, vsync: this);
    _scrollController = ScrollController();
    _tabController.addListener(() {
      if (_tabController.indexIsChanging) {
        // Scroll to the selected tab
        scrollToSelectedTab(_tabController.index, _scrollController, _tabKeys);
      }
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  void didChangeDependencies() {
    // TODO: implement didChangeDependencies
    super.didChangeDependencies();
    _colorScheme = Theme.of(context).colorScheme;
    _textTheme = Theme.of(context).textTheme;
    _tabKeys = List.generate(_tabLength, (_) => GlobalKey());
    _tabs = <Widget>[
      Container(
        decoration: BoxDecoration(
          color: _colorScheme.primaryContainer.withAlpha(50),
          borderRadius: BorderRadius.circular(10),
        ),
        child: Padding(
          padding: const EdgeInsets.fromLTRB(4, 0, 10, 0),
          child: tabBuilder(
            tabKey: _tabKeys[0],
            tabLabel: 'RC',
            indexLabelStyle: _textTheme.labelLarge!.copyWith(
              color: _colorScheme.onPrimaryContainer,
            ),
            labelStyle: _textTheme.labelLarge!.copyWith(
              color: _colorScheme.onPrimaryContainer,
            ),
          ),
        ),
      ),
      Padding(
        padding: const EdgeInsets.fromLTRB(4, 0, 10, 0),
        child: Container(
          decoration: BoxDecoration(
            color: _colorScheme.primaryContainer.withAlpha(50),
            borderRadius: BorderRadius.circular(10),
          ),
          child: Padding(
            padding: const EdgeInsets.fromLTRB(4, 0, 10, 0),
            child: tabBuilder(
              tabKey: _tabKeys[1],
              tabLabel: 'Steel',
              indexLabelStyle: _textTheme.labelLarge!.copyWith(
                color: _colorScheme.onPrimaryContainer,
              ),
              labelStyle: _textTheme.labelLarge!.copyWith(
                color: _colorScheme.onPrimaryContainer,
              ),
            ),
          ),
        ),
      ),
    ];

    _tabBarView = TabBarView(
      controller: _tabController,
      children: <Widget>[
        Padding(
          padding: const EdgeInsets.fromLTRB(15, 0, 15, 0),
          child: Row(
            children: [
              Flexible(flex: 3, child: CantileverScheme()),
              // Flexible(flex: 2, child: SummaryPane()),
            ],
          ),
        ),

        Padding(
          padding: const EdgeInsets.fromLTRB(15, 0, 15, 0),
          child: Row(
            children: [
              Flexible(flex: 3, child: SteelCantileverScheme()),
              // Flexible(flex: 2, child: SummaryPane()),
            ],
          ),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: <Widget>[
        Scrollbar(
          controller: _scrollController,
          thumbVisibility: true,
          thickness: 6.0,
          child: SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            controller: _scrollController,
            child: Padding(
              padding: const EdgeInsets.fromLTRB(0, 5, 0, 10),
              child: TabBar.secondary(
                controller: _tabController,
                isScrollable: true,
                dividerColor: Colors.transparent,
                indicatorColor: _colorScheme.primaryContainer.withAlpha(225),
                indicator: UnderlineTabIndicator(
                  borderSide: BorderSide(
                    color: _colorScheme.primary.withAlpha(150),
                    width: 1.25,
                  ),
                  insets: EdgeInsets.fromLTRB(20, 0, 20, 8),
                ),
                tabs: _tabs,
              ),
            ),
          ),
        ),
        Flexible(child: _tabBarView),
      ],
    );
  }
}

class NestedTabBarFdnScheme extends StatefulWidget {
  const NestedTabBarFdnScheme({super.key});

  @override
  State<NestedTabBarFdnScheme> createState() => _NestedTabBarFdnSchemeState();
}

class _NestedTabBarFdnSchemeState extends State<NestedTabBarFdnScheme>
    with TickerProviderStateMixin {
  late final TabController _tabController;
  late final List<Widget> _tabs;
  late final TabBarView _tabBarView;
  late final ColorScheme _colorScheme;
  late final TextTheme _textTheme;
  late final List<GlobalKey> _tabKeys;
  late final int _tabLength; // RMEMBER TO CHANGE THIS TO ADD TAB
  late final List<String> _tabNames = [
    'End Bearing Bored Pile',
    'Frictional Bored Pile',
    'Socketed Steel H Pile',
    'Driven Steel H Pile',
    'Pad Footing',
  ];
  late final ScrollController _scrollController;

  @override
  void initState() {
    super.initState();
    _tabLength = _tabNames.length;
    _tabController = TabController(length: _tabLength, vsync: this);
    _scrollController = ScrollController();
    _tabController.addListener(() {
      if (_tabController.indexIsChanging) {
        // Scroll to the selected tab
        scrollToSelectedTab(_tabController.index, _scrollController, _tabKeys);
      }
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  void didChangeDependencies() {
    // TODO: implement didChangeDependencies
    super.didChangeDependencies();
    _colorScheme = Theme.of(context).colorScheme;
    _textTheme = Theme.of(context).textTheme;
    _tabKeys = List.generate(_tabLength, (_) => GlobalKey());
    _tabs = List.generate(_tabLength, (i) {
      return Container(
        decoration: BoxDecoration(
          color: _colorScheme.primaryContainer.withAlpha(50),
          borderRadius: BorderRadius.circular(10),
        ),
        child: Padding(
          padding: const EdgeInsets.fromLTRB(4, 0, 10, 0),
          child: tabBuilder(
            tabKey: _tabKeys[i],
            tabLabel: _tabNames[i],
            indexLabelStyle: _textTheme.labelLarge!.copyWith(
              color: _colorScheme.onPrimaryContainer,
            ),
            labelStyle: _textTheme.labelLarge!.copyWith(
              color: _colorScheme.onPrimaryContainer,
            ),
          ),
        ),
      );
    });

    _tabBarView = TabBarView(
      controller: _tabController,
      children: <Widget>[
        Padding(
          padding: const EdgeInsets.fromLTRB(15, 0, 15, 0),
          child: Row(
            children: [
              Flexible(flex: 3, child: PileEndBearingBoredScheme()),
              // Flexible(flex: 2, child: SummaryPane()),
            ],
          ),
        ),
        Padding(
          padding: const EdgeInsets.fromLTRB(15, 0, 15, 0),
          child: Row(
            children: [
              Flexible(flex: 3, child: PileFrictionalBoredScheme()),
              // Flexible(flex: 2, child: SummaryPane()),
            ],
          ),
        ),
        Padding(
          padding: const EdgeInsets.fromLTRB(15, 0, 15, 0),
          child: Row(
            children: [
              Flexible(flex: 3, child: PileSocketedScheme()),
              // Flexible(flex: 2, child: SummaryPane()),
            ],
          ),
        ),
        Padding(
          padding: const EdgeInsets.fromLTRB(15, 0, 15, 0),
          child: Row(
            children: [
              Flexible(flex: 3, child: PileDrivenScheme()),
              // Flexible(flex: 2, child: SummaryPane()),
            ],
          ),
        ),
        Padding(
          padding: const EdgeInsets.fromLTRB(15, 0, 15, 0),
          child: Row(
            children: [
              Flexible(flex: 3, child: FootingScheme()),
              // Flexible(flex: 2, child: SummaryPane()),
            ],
          ),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: <Widget>[
        Scrollbar(
          controller: _scrollController,
          thumbVisibility: true,
          thickness: 6.0,
          child: SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            controller: _scrollController,
            child: Padding(
              padding: const EdgeInsets.fromLTRB(0, 5, 0, 10),
              child: TabBar.secondary(
                controller: _tabController,
                isScrollable: true,
                dividerColor: Colors.transparent,
                indicatorColor: _colorScheme.primaryContainer.withAlpha(225),
                indicator: UnderlineTabIndicator(
                  borderSide: BorderSide(
                    color: _colorScheme.primary.withAlpha(150),
                    width: 1.25,
                  ),
                  insets: EdgeInsets.fromLTRB(20, 0, 20, 8),
                ),
                tabs: _tabs,
              ),
            ),
          ),
        ),
        Flexible(child: _tabBarView),
      ],
    );
  }
}

class NestedTabBarMisc extends StatefulWidget {
  const NestedTabBarMisc({super.key});

  @override
  State<NestedTabBarMisc> createState() => _NestedTabBarMiscState();
}

class _NestedTabBarMiscState extends State<NestedTabBarMisc>
    with TickerProviderStateMixin {
  late final TabController _tabController;
  late final List<Widget> _tabs;
  late final TabBarView _tabBarView;
  late final ColorScheme _colorScheme;
  late final TextTheme _textTheme;
  late final List<GlobalKey> _tabKeys;
  late final int _tabLength; // RMEMBER TO CHANGE THIS TO ADD TAB
  late final List<String> _tabNames = [
    'Basement Wall',
    'Str Zone',
    'Wind Load',
    'Embodied Carbon',
    'Programme',
    'Recommended Loading',
    // 'Intro and Appraisal',
    // 'Scheme Comparison',
    // 'Pile Cap',
    // 'Method Statement',
  ];
  late final ScrollController _scrollController;

  @override
  void initState() {
    super.initState();
    _tabLength = _tabNames.length;
    _tabController = TabController(length: _tabLength, vsync: this);
    _scrollController = ScrollController();
    _tabController.addListener(() {
      if (_tabController.indexIsChanging) {
        // Scroll to the selected tab
        scrollToSelectedTab(_tabController.index, _scrollController, _tabKeys);
      }
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  void didChangeDependencies() {
    // TODO: implement didChangeDependencies
    super.didChangeDependencies();
    _colorScheme = Theme.of(context).colorScheme;
    _textTheme = Theme.of(context).textTheme;
    _tabKeys = List.generate(_tabLength, (_) => GlobalKey());
    _tabs = List.generate(_tabLength, (i) {
      return Container(
        decoration: BoxDecoration(
          color: _colorScheme.primaryContainer.withAlpha(50),
          borderRadius: BorderRadius.circular(10),
        ),
        child: Padding(
          padding: const EdgeInsets.fromLTRB(4, 0, 10, 0),
          child: tabBuilder(
            tabKey: _tabKeys[i],
            tabLabel: _tabNames[i],
            indexLabelStyle: _textTheme.labelLarge!.copyWith(
              color: _colorScheme.onPrimaryContainer,
            ),
            labelStyle: _textTheme.labelLarge!.copyWith(
              color: _colorScheme.onPrimaryContainer,
            ),
          ),
        ),
      );
    });

    _tabBarView = TabBarView(
      controller: _tabController,
      children: <Widget>[
        Padding(
          padding: const EdgeInsets.fromLTRB(15, 0, 15, 0),
          child: Row(
            children: [
              Flexible(flex: 3, child: BasementWallScheme()),
              // Flexible(flex: 2, child: SummaryPane()),
            ],
          ),
        ),
        Padding(
          padding: const EdgeInsets.fromLTRB(15, 0, 15, 0),
          child: Row(
            children: [
              Flexible(flex: 3, child: StrZoneCalculator()),
              // Flexible(flex: 2, child: SummaryPane()),
            ],
          ),
        ),
        Padding(
          padding: const EdgeInsets.fromLTRB(15, 0, 15, 0),
          child: Row(
            children: [
              Flexible(flex: 3, child: WindLoadCals()),
              // Flexible(flex: 2, child: SummaryPane()),
            ],
          ),
        ),
        Padding(
          padding: const EdgeInsets.fromLTRB(15, 0, 15, 0),
          child: Row(
            children: [
              Flexible(flex: 3, child: CarbonCals()),
              // Flexible(flex: 2, child: SummaryPane()),
            ],
          ),
        ),
        Padding(
          padding: const EdgeInsets.fromLTRB(15, 0, 15, 0),
          child: Row(
            children: [
              Flexible(flex: 3, child: Programme()),
              // Flexible(flex: 2, child: Placeholder()),
            ],
          ),
        ),
        Padding(
          padding: const EdgeInsets.fromLTRB(15, 0, 15, 0),
          child: Row(
            children: [
              Flexible(flex: 3, child: RecommendedLoading()),
              // Flexible(flex: 2, child: SummaryPane()),
            ],
          ),
        ),

        // Padding(
        //   padding: const EdgeInsets.fromLTRB(15, 0, 15, 0),
        //   child: Row(
        //     children: [
        //       Flexible(flex: 3, child: IntroWithAppraisal()),
        //       // Flexible(flex: 2, child: SummaryPane()),
        //     ],
        //   ),
        // ),

        // Padding(
        //   padding: const EdgeInsets.fromLTRB(15, 0, 15, 0),
        //   child: Row(
        //     children: [
        //       Flexible(flex: 3, child: SchemeComparison()),
        //       // Flexible(flex: 2, child: SummaryPane()),
        //     ],
        //   ),
        // ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: <Widget>[
        Scrollbar(
          controller: _scrollController,
          thumbVisibility: true,
          thickness: 6.0,
          child: SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            controller: _scrollController,
            child: Padding(
              padding: const EdgeInsets.fromLTRB(0, 5, 0, 10),
              child: TabBar.secondary(
                controller: _tabController,
                isScrollable: true,
                dividerColor: Colors.transparent,
                indicatorColor: _colorScheme.primaryContainer.withAlpha(225),
                indicator: UnderlineTabIndicator(
                  borderSide: BorderSide(
                    color: _colorScheme.primary.withAlpha(150),
                    width: 1.25,
                  ),
                  insets: EdgeInsets.fromLTRB(20, 0, 20, 8),
                ),
                tabs: _tabs,
              ),
            ),
          ),
        ),
        Flexible(child: _tabBarView),
      ],
    );
  }
}

//* ====================================================
//* Public Function used by Class in this page
//* ====================================================
Tab tabBuilder({
  GlobalKey? tabKey,
  int? index,
  String? tabLabel,
  Color? bgColor,
  TextStyle? indexLabelStyle,
  TextStyle? labelStyle,
}) {
  return Tab(
    key: tabKey ?? null,
    child: Row(
      children: [
        Builder(
          builder: (context) {
            if (index != null) {
              return CircleAvatar(
                backgroundColor: bgColor ?? Colors.transparent,
                radius: 12,
                child: Padding(
                  padding: const EdgeInsets.all(2.0),
                  child: Text('$index', style: indexLabelStyle ?? null),
                ),
              );
            } else {
              return SizedBox(width: 0, height: 0);
            }
          },
        ),
        SizedBox(width: 5),
        Text(tabLabel ?? 'tabLabel', style: labelStyle ?? null),
      ],
    ),
  );
}

void scrollToSelectedTab(
  int index,
  ScrollController scrollController,
  List<GlobalKey> tabKeys,
) {
  // Get the width of the selected tab
  double offset = 0;
  for (var i = 0; i <= index; i++) {
    offset += i * getTabWidth(i, tabKeys);
  }
  scrollController.animateTo(
    offset,
    duration: Duration(milliseconds: 300),
    curve: Curves.easeInOut,
  );
}

double getTabWidth(int index, List<GlobalKey> tabKeys) {
  final RenderBox renderBox =
      tabKeys[index].currentContext?.findRenderObject() as RenderBox;
  return renderBox.size.width;
}
