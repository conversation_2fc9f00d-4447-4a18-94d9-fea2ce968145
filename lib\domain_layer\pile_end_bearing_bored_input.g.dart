// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'pile_end_bearing_bored_input.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_PileEndBearingBoredInput _$PileEndBearingBoredInputFromJson(
  Map<String, dynamic> json,
) => _PileEndBearingBoredInput(
  safeBearing: (json['safeBearing'] as num?)?.toDouble() ?? 1000,
  fos: (json['fos'] as num?)?.toDouble() ?? 1,
  fcu: (json['fcu'] as num?)?.toDouble() ?? 45,
  maxPileLength: (json['maxPileLength'] as num?)?.toDouble() ?? 30,
  maxPileDiameter: (json['maxPileDiameter'] as num?)?.toDouble() ?? 2000,
  ratioOfBelloutDia: (json['ratioOfBelloutDia'] as num?)?.toDouble() ?? 1.65,
  maxSteelRatio: (json['maxSteelRatio'] as num?)?.toDouble() ?? 0.04,
  slsLoad: (json['slsLoad'] as num?)?.toDouble() ?? 1000,
  ulsLoad: (json['ulsLoad'] as num?)?.toDouble() ?? 2000,
  diaIncrement: (json['diaIncrement'] as num?)?.toDouble() ?? 200,
  useSelectColLoad: json['useSelectColLoad'] as bool? ?? false,
  colLoadFactor: (json['colLoadFactor'] as num?)?.toDouble() ?? 1.0,
  id: json['id'] as String? ?? '1',
);

Map<String, dynamic> _$PileEndBearingBoredInputToJson(
  _PileEndBearingBoredInput instance,
) => <String, dynamic>{
  'safeBearing': instance.safeBearing,
  'fos': instance.fos,
  'fcu': instance.fcu,
  'maxPileLength': instance.maxPileLength,
  'maxPileDiameter': instance.maxPileDiameter,
  'ratioOfBelloutDia': instance.ratioOfBelloutDia,
  'maxSteelRatio': instance.maxSteelRatio,
  'slsLoad': instance.slsLoad,
  'ulsLoad': instance.ulsLoad,
  'diaIncrement': instance.diaIncrement,
  'useSelectColLoad': instance.useSelectColLoad,
  'colLoadFactor': instance.colLoadFactor,
  'id': instance.id,
};
