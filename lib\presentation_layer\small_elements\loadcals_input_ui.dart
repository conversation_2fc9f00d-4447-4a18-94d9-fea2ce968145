import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:structify/domain_layer/data_struct/recommend_load.dart';
import 'package:structify/domain_layer/loading_table.dart';

//domain layer
import '../../domain_layer/preferences.dart';

// presentation layer
import '../screen/homescreen.dart';
import 'input/custom_stateful_double_input.dart';
import 'input/custom_stateful_text_input.dart';
import 'button/function_button.dart';
import 'popup/multiseleciton_dialog.dart';
// import '../small_elements/global_data_ui.dart';
// import '../small_elements/loadcals_summary_ui.dart';

class LoadCalsInput extends ConsumerStatefulWidget {
  const LoadCalsInput({
    this.isExpanded,
    this.maxHeight,
    this.minHeight,
    super.key,
  });

  final bool? isExpanded;
  final double? maxHeight;
  final double? minHeight;

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _LoadCalsInputState();
}

class _LoadCalsInputState extends ConsumerState<LoadCalsInput>
    with WidgetsBindingObserver {
  late bool _isExpanded;
  late double _maxHeight;
  late double _minHeight;
  late CustomStatefulTextInput _usageInput;
  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void initState() {
    WidgetsBinding.instance.addObserver(this);
    _isExpanded = widget.isExpanded ?? false;
    _maxHeight = widget.maxHeight ?? 300;
    _minHeight = widget.minHeight ?? 50;
    _usageInput = CustomStatefulTextInput();

    // _updateHeights();

    super.initState();
  }

  @override
  void didChangeDependencies() {
    _updateHeights();
    super.didChangeDependencies();
    // Update heights when dependencies change, including after initState
  }

  @override
  void didChangeMetrics() {
    // This method is called when the metrics change (like resizing the window)
    setState(() {
      _updateHeights();
    });
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;
    final NumberFormat f1 = NumberFormat('0.0');

    final data1 = ref.watch(globalDataControllerProvider);
    final data2 = ref.watch(loadingTablesControllerProvider);
    final data3 = ref.watch(recommendedLoadControllerProvider);

    final scrollController = ScrollController();

    return data1.when(
      data: (data) {
        late final List<String> unit;
        switch (data.unit) {
          case 'metrics':
            unit = PreferredUnit.metrics;
            break;
          case 'imperial':
            unit = PreferredUnit.imperial;
            break;
          default:
            unit = PreferredUnit.metrics;
            break;
        }

        return data2.when(
          data: (tables) {
            return data3.when(
              data: (recommendedLoading) {
                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Padding(
                      padding: const EdgeInsets.fromLTRB(10, 0, 10, 0),
                      child: Divider(),
                    ),
                    Padding(
                      padding: const EdgeInsets.fromLTRB(10, 0, 0, 0),
                      child: Align(
                        alignment: Alignment.centerLeft,
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                IconButton(
                                  icon: Icon(
                                    _isExpanded
                                        ? Icons.keyboard_arrow_up
                                        : Icons.keyboard_arrow_down,
                                    color: colorScheme.onSurface,
                                  ),
                                  onPressed: () {
                                    setState(() {
                                      _isExpanded = !_isExpanded;
                                    });
                                  },
                                ),
                                Text(
                                  'Loading Table ',
                                  style: textTheme.titleLarge!.copyWith(
                                    color: colorScheme.onSurface,
                                  ),
                                ),
                              ],
                            ),
                            Container(
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(5.0),
                                color: colorScheme.surfaceContainer.withAlpha(
                                  225,
                                ),
                                border: Border.all(
                                  color: Colors.black.withAlpha(125),
                                ),
                              ),
                              child: Padding(
                                padding: const EdgeInsets.all(2.0),
                                child: Text(
                                  '(Total: ${tables.length})',
                                  style: textTheme.labelMedium!.copyWith(
                                    color: colorScheme.onSurfaceVariant
                                        .withAlpha(225),
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.fromLTRB(10, 0, 10, 0),
                      child: Divider(),
                    ),
                    Row(
                      children: [
                        SizedBox(width: 5.0),
                        FunctionButton(
                          labelIcon: Icon(Icons.add_box_outlined),
                          labelText: 'Add New',
                          onTap: (isPressed) async {
                            await ref
                                .read(loadingTablesControllerProvider.notifier)
                                .addEmptytable();
                          },
                        ),
                        SizedBox(width: 5.0),
                        MultiSelectionDialog(
                          trailing:
                              recommendedLoading.map((loading) {
                                return DefaultTextStyle(
                                  style: textTheme.labelSmall!.copyWith(
                                    color: colorScheme.onSecondaryContainer,
                                  ),
                                  child: Column(
                                    children: [
                                      SizedBox(height: 5),
                                      // Text(
                                      //   'Finish: ${f1.format(loading.finish)} [${unit[4]}]',
                                      // ),
                                      Text(
                                        'SDL: ${f1.format(loading.sdl)} [${unit[1]}]',
                                      ),
                                      Text(
                                        'LL: ${f1.format(loading.ll)} [${unit[1]}]',
                                      ),
                                      SizedBox(height: 5),
                                    ],
                                  ),
                                );
                              }).toList(),
                          dialogWidth: 400.0,
                          dialogHeight: 400.0,
                          label: 'Add Recommended',
                          selectedOptions: <String>[],
                          options:
                              recommendedLoading
                                  .map((loading) => loading.usage)
                                  .toList(),
                          onPressed: (selectedOptions) async {
                            if (selectedOptions.isNotEmpty) {
                              for (String usage in selectedOptions) {
                                final loadingTable = await ref
                                    .read(
                                      recommendedLoadControllerProvider
                                          .notifier,
                                    )
                                    .toLoadingTable(usage);
                                await ref
                                    .read(
                                      loadingTablesControllerProvider.notifier,
                                    )
                                    .addTable(loadingTable);
                              }
                            }
                          },
                        ),
                      ],
                    ),

                    ClipRect(
                      child: ConstrainedBox(
                        constraints: BoxConstraints(
                          maxHeight: _isExpanded ? _maxHeight : 0,
                        ),
                        child: Scrollbar(
                          controller: scrollController,
                          thumbVisibility: true,
                          child: ListView.builder(
                            controller: scrollController,
                            itemCount: tables.length,
                            itemBuilder: (context, index) {
                              _usageInput = CustomStatefulTextInput(
                                title: 'Usage',
                                key: ValueKey(
                                  '${tables[index].loadingTableId}_Usage}',
                                ),
                                value:
                                    _usageInput.errorMsg == null
                                        ? tables[index].usage
                                        : _usageInput.value,
                                onChanged: (value) {
                                  final x = ref.read(
                                    loadingTablesControllerProvider,
                                  );
                                  return x.when(
                                    data: (tables) {
                                      //tables excluding the table being edited (i.e. table[index])
                                      final re = RegExp(
                                        r'^' + RegExp.escape(value) + r'$',
                                      );
                                      final tempTable =
                                          tables
                                              .where(
                                                (tbl) =>
                                                    tbl.loadingTableId !=
                                                    tables[index]
                                                        .loadingTableId,
                                              )
                                              .toList();
                                      final repeated = tempTable.any(
                                        (tbl) => re.hasMatch(tbl.usage),
                                      );
                                      if (!repeated) {
                                        ref
                                            .read(
                                              loadingTablesControllerProvider
                                                  .notifier,
                                            )
                                            .updateTable(
                                              tables[index].loadingTableId,
                                              usage: value,
                                            );
                                      } else {
                                        return "Repeated";
                                      }
                                    },
                                    error: (error, stackTrace) {
                                      return 'some error';
                                    },
                                    loading: () {},
                                  );
                                },
                              );

                              return Padding(
                                padding: const EdgeInsets.fromLTRB(
                                  8.0,
                                  4.0,
                                  8.0,
                                  4.0,
                                ),
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  mainAxisAlignment: MainAxisAlignment.start,
                                  children: [
                                    // IconButton(
                                    //   icon: Icon(
                                    //     Icons.add_circle_outline_outlined,
                                    //     color: colorScheme.onSurface,
                                    //   ),
                                    //   onPressed: () {
                                    //     ref
                                    //         .read(loadingTablesControllerProvider.notifier)
                                    //         .insertEmptyTable(index);
                                    //   },
                                    // ),
                                    Container(
                                      decoration: BoxDecoration(
                                        shape: BoxShape.circle,
                                        color: colorScheme.surfaceContainer
                                            .withAlpha(200),
                                        // borderRadius: BorderRadius.circular(5),
                                        border: Border.all(color: Colors.black),
                                      ),
                                      child: Padding(
                                        padding: const EdgeInsets.all(4.0),
                                        child: Text(
                                          '${index + 1}',
                                          style: textTheme.labelLarge!.copyWith(
                                            color: colorScheme.onSurfaceVariant
                                                .withAlpha(250),
                                          ),
                                        ),
                                      ),
                                    ),
                                    IconButton(
                                      icon: Icon(
                                        Icons.copy_all_outlined,
                                        color: colorScheme.onSurface,
                                      ),
                                      onPressed: () {
                                        ref
                                            .read(
                                              loadingTablesControllerProvider
                                                  .notifier,
                                            )
                                            .copyAndInsertTable(index);
                                      },
                                    ),
                                    IconButton(
                                      icon: Icon(
                                        Icons.delete,
                                        color: colorScheme.onSurface,
                                      ),
                                      onPressed: () {
                                        ref
                                            .read(
                                              loadingTablesControllerProvider
                                                  .notifier,
                                            )
                                            .deleteTable(
                                              tables[index].loadingTableId,
                                            );
                                      },
                                    ),
                                    Flexible(child: _usageInput),
                                    SizedBox(width: 5.0),
                                    Flexible(
                                      child: CustomStatefulDoubleInput(
                                        title: 'Finish  [${unit[4]}]',
                                        key: ValueKey(
                                          '${tables[index].loadingTableId}_Finish',
                                        ),
                                        value: tables[index].finish,
                                        listener: (hasFocus, value) {
                                          if (!hasFocus) {
                                            ref
                                                .read(
                                                  loadingTablesControllerProvider
                                                      .notifier,
                                                )
                                                .updateTable(
                                                  tables[index].loadingTableId,
                                                  finish: value,
                                                );
                                          }
                                          // print('Listening: $hasFocus | $value');
                                        },
                                      ),
                                    ),
                                    SizedBox(width: 5.0),
                                    Flexible(
                                      child: CustomStatefulDoubleInput(
                                        title:
                                            'Service & Partition [${unit[1]}]',
                                        tooltipText:
                                            'Include partition load [${unit[1]}], if any',
                                        key: ValueKey(
                                          '${tables[index].loadingTableId}_Service',
                                        ),
                                        value: tables[index].service,
                                        listener: (hasFocus, value) {
                                          if (!hasFocus) {
                                            ref
                                                .read(
                                                  loadingTablesControllerProvider
                                                      .notifier,
                                                )
                                                .updateTable(
                                                  tables[index].loadingTableId,
                                                  service: value,
                                                );
                                          }
                                          // print('Listening: $hasFocus | $value');
                                        },
                                      ),
                                    ),
                                    SizedBox(width: 5.0),
                                    Flexible(
                                      child: CustomStatefulDoubleInput(
                                        title: 'Live Load  [${unit[1]}]',
                                        key: ValueKey(
                                          '${tables[index].loadingTableId}_liveLoad',
                                        ),
                                        value: tables[index].liveLoad,
                                        listener: (hasFocus, value) {
                                          if (!hasFocus) {
                                            ref
                                                .read(
                                                  loadingTablesControllerProvider
                                                      .notifier,
                                                )
                                                .updateTable(
                                                  tables[index].loadingTableId,
                                                  liveload: value,
                                                );
                                          }
                                          // print('Listening: $hasFocus | $value');
                                        },
                                      ),
                                    ),
                                  ],
                                ),
                              );
                            },
                          ),
                        ),
                      ),
                    ),
                  ],
                );
              },
              error: (error, stackTrace) => Text(error.toString()),
              loading: () => CircularProgressIndicator(),
            );
            ;
          },
          error: (error, stackTrace) => Text(error.toString()),
          loading: () => CircularProgressIndicator(),
        );
      },
      error: (error, stackTrace) {
        return Text('Error: $error');
      },
      loading: () {
        return const CircularProgressIndicator();
      },
    );
  }

  void _updateHeights() {
    final double screenHeight = MediaQuery.of(context).size.height;
    _maxHeight =
        (widget.maxHeight == null)
            ? screenHeight * 0.6
            : (widget.maxHeight! > screenHeight * 0.6)
            ? screenHeight * 0.6
            : widget.maxHeight!;

    _minHeight =
        (widget.minHeight == null)
            ? screenHeight * 0.1
            : (widget.minHeight! > screenHeight * 0.1)
            ? screenHeight * 0.1
            : widget.minHeight!;

    // Adjust _maxHeight if needed
    if (_maxHeight < _minHeight) {
      _maxHeight = _minHeight;
    }
  }
}
