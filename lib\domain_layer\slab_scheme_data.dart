import 'package:freezed_annotation/freezed_annotation.dart';
// import 'package:flutter/foundation.dart';
part 'slab_scheme_data.freezed.dart';
part 'slab_scheme_data.g.dart';

@freezed
abstract class SlabSchemeData with _$SlabSchemeData {
  const SlabSchemeData._();
  factory SlabSchemeData({
    @Default('') String usage,
    @Default(0.0) double finish,
    @Default(0.0) double service,
    @Default(0.0) double liveLoad,
    @Default('') String loadingTableId, 
    @Default(5.0) double span,
    @Default(0.0) double strZone,
    @Default(45.0) double fcu,
    @Default(35.0) double cover,
    @Default('') String mainTopBar,
    @Default('') String mainBottomBar,
    @Default('') String mainLinks,
    @Default('') String slabSchemeId,
    @Default('') String calsLog,
    @Default(false) bool isSelected,

  }) = _SlabSchemeData; 

  factory SlabSchemeData.fromJson(Map<String, Object?> json) =>
      _$SlabSchemeDataFromJson(json);
}
