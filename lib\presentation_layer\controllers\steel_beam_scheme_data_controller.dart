import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:nanoid2/nanoid2.dart';
import 'dart:math';
import 'package:intl/intl.dart';
//presentation layer
import '../../domain_layer/mixin/mixin_steel_str.dart';
import '../../domain_layer/mixin/mixin_str_general_cals.dart';
import '../../domain_layer/mixin/mixin_tools_for_ui.dart';
import '../../domain_layer/steel_beam_scheme_data.dart';
import '../../domain_layer/steel_beam_scheme_input.dart';
import '../screen/homescreen.dart';

// domain layer
import '../../domain_layer/design_options.dart';
import '../../domain_layer/global_data.dart';
import '../../domain_layer/loading_table.dart';
import '../../domain_layer/preferences.dart';

part 'steel_beam_scheme_data_controller.g.dart';

@riverpod
class SteelBeamSchemeDataController extends _$SteelBeamSchemeDataController
    with StrGeneralCals, MixinToolsForUI, SteelStrHK {
  @override
  FutureOr<List<SteelBeamSchemeData>> build() async {
    // print('Build: Beam Scheme Data');
    final steelBeamSchemeDataList =
        await ref
            .read(appDatabaseControllerProvider.notifier)
            .querySteelBeamSchemeData();
    final data1 = ref.watch(loadingTablesControllerProvider);
    final data2 = ref.watch(globalDataControllerProvider);
    final data3 = ref.watch(steelBeamSchemeInputControllerProvider);
    return data1.when(
      data: (loadingTables) async {
        return data2.when(
          data: (globalData) async {
            return data3.when(
              data: (inputs) async {
                //* Validate the loading
                return await bacthTypBayScheming(
                  existingID:
                      steelBeamSchemeDataList
                          .map((x) => x.steelBeamSchemeId)
                          .toSet(),
                );
              },
              error: (error, stackTrace) => [],
              loading: () => [],
            );
          },
          error: (error, stackTrace) => [],
          loading: () => [],
        );
      },
      error: (error, stackTrace) => [],
      loading: () => [],
    );
  }

  Future<void> addBeamSchemeData(
    List<SteelBeamSchemeData> steelBeamSchemeData,
  ) async {
    final x = await future;
    state = AsyncData([...x, ...steelBeamSchemeData]);

    // state.when(
    //   data: (data) {
    //     state = AsyncData([...data, ...beamSchemeData]);
    //   },
    //   error: (error, stackTrace) => state = AsyncError(error, stackTrace),
    //   loading: () {},
    // );
  }

  Future<void> deleteTable(String id) async {
    final x = await future;
    x.removeWhere((item) => item.steelBeamSchemeId == id);
    state = AsyncData(x);
  }

  Future<void> deleteAllTable() async {
    state = AsyncData([]);
  }

  Future<String> _generateUniqueId({Set<String>? existingID}) async {
    late final Set<String> beamSchemeDataId;
    if (existingID == null) {
      final x = await future;
      beamSchemeDataId = x.map((item) => item.steelBeamSchemeId).toSet();
    } else {
      beamSchemeDataId = existingID;
    }
    String newID = nanoid(alphabet: Alphabet.noDoppelgangerSafe);
    while (beamSchemeDataId.contains(newID)) {
      newID = nanoid(alphabet: Alphabet.noDoppelgangerSafe);
    }
    return newID;
  }

  Future<List<SteelBeamSchemeData>> bacthTypBayScheming({
    Set<String>? existingID,
  }) async {
    await deleteAllTable();
    final input = await ref.read(steelBeamSchemeInputControllerProvider.future);
    final globalData = await ref.read(globalDataControllerProvider.future);
    final loadingTables = await ref.read(
      loadingTablesControllerProvider.future,
    );

    //* Loading
    final String usage = input.usage;
    final LoadingTable load = loadingTables.firstWhere(
      (item) => item.usage == usage,
      orElse: () => LoadingTable(usage: 'No imposed load'),
    );
    final double sdl =
        load.finish * globalData.finishUnitWeight * pow(10, -3) +
        load.service; // in [kPa]
    final double ll = load.liveLoad; // in [kPa]
    final double ulsLoad =
        globalData.sdlFactor * sdl + globalData.llFactor * ll; // in [kPa]

    List<SteelBeamSchemeData> finalList = [];
    late SteelBeamSchemeData secScheme, mainScheme;

    for (var i = 0; i < input.iterationSteps; i++) {
      final SteelBeamSchemeInput newInput = input.copyWith(
        bays: input.bays + input.baysIncreament * i,
        shortSpan: input.shortSpan + input.shortSpanIncreament * i,
        longSpan: input.longSpan + input.longSpanIncreament * i,
      );
      final double w =
          newInput.shortSpan / newInput.bays; //  revised load width
      final double ulsUDL = ulsLoad * w; // in [kN/m]
      final double llUDL = ll * w; // in [kN/m]

      secScheme = await _typBayScheming(
        newInput,
        ulsUDL,
        llUDL,
        BeamDesignOptions.secBeam,
      );

      mainScheme = await _typBayScheming(
        newInput,
        ulsUDL,
        llUDL,
        BeamDesignOptions.mainBeam,
        secScheme: secScheme,
      );

      final String id = await _generateUniqueId(existingID: existingID);

      final finalLog = StringBuffer();
      finalLog.write(secScheme.calsLog);
      finalLog.write('\n\n');
      finalLog.write(mainScheme.calsLog);

      final tables = await ref.read(loadingTablesControllerProvider.future);
      final loadingtable = tables.firstWhere(
        (tbl) => tbl.usage == newInput.usage,
      );

      final result = SteelBeamSchemeData(
        usage: newInput.usage,
        finish: loadingtable.finish,
        service: loadingtable.service,
        liveLoad: loadingtable.liveLoad,
        loadingTableId: loadingtable.loadingTableId,
        slabThickness: newInput.slabThickness,
        compositeActionFactor: newInput.compositeActionFactor,
        shortSpan: newInput.shortSpan,
        longSpan: newInput.longSpan,
        bays: newInput.bays,
        strZone: newInput.strZone,
        fsy: newInput.fsy,
        mainBeamSection: mainScheme.mainBeamSection,
        secBeamSection: secScheme.secBeamSection,
        steelBeamSchemeId: id,
        calsLog: finalLog.toString(),
      );
      finalList.add(result);
    }
    return finalList;
  }

  Future<SteelBeamSchemeData> _typBayScheming(
    SteelBeamSchemeInput input,
    double ulsUDL,
    double llUDL,
    String beamDesignOption, {
    SteelBeamSchemeData? secScheme,
  }) async {
    // ********************************
    // * initialization
    // ********************************
    GlobalData globalData = await ref.read(globalDataControllerProvider.future);
    List<LoadingTable> loadingTables = await ref.read(
      loadingTablesControllerProvider.future,
    );

    // iteration status
    late bool shouldRedesign;
    bool reDesignMc = true, reDesignI = true;

    //iteration parameter to track
    late double depth;

    //provided tension, compression steel and links
    late double iReq, iPro;
    Map<String, dynamic> section = {}, mainSection = {}, secSection = {};
    final List<Map<String, dynamic>> steelSections = await getAllSteelISection();

    //revised loading (incorproate Self Weight)
    late double newUlsudl, swMainFactored;

    // design moment and shear
    late double Md, vD;

    // design capacity
    late double fsy; // [MPa]
    late double Mc;

    // record
    StringBuffer buffer = StringBuffer();
    List<bool> status = [false, false];
    LoadingTable loadingTable;

    // Some global data
    final double f1 = globalData.sdlFactor;
    final double Es = 205000; // [MPa] Elastic modulus of steel

    // ********************************
    // get the section table
    // ********************************
    section = steelSections.first;

    // ********************************
    // Design Looping
    // ********************************
    shouldRedesign = true; // at least run the first

    while (shouldRedesign) {
      // ********************************
      // * get loading
      // ********************************
      if (beamDesignOption == BeamDesignOptions.secBeam) {
        final double loadWidth = input.shortSpan / input.bays; // load width
        newUlsudl =
            ulsUDL +
            f1 *
                getSelfWeight(
                  globalData.steelUnitWeight,
                  double.parse(section['A']) * pow(10, 2),
                ) + //beam Self weight
            f1 *
                getPressureFromThick(
                  input.slabThickness,
                  globalData.rcUnitWeight,
                ) *
                loadWidth; // slab self weight
        Md = getMaxMomentBeamUDL(newUlsudl, input.longSpan);
        vD = getMaxShearUDLSimpleBeam(newUlsudl, input.longSpan);
        iReq =
            getMaxDeflectionSimpleBeamUDL(llUDL, input.longSpan, Es, 1) *
            (250 / (input.longSpan * pow(10, 3))) *
            (1 / input.compositeActionFactor); //[mm4]
      } else {
        if (secScheme != null && secScheme.secBeamSection != 'fail') {
          secSection = steelSections.firstWhere(
            (item) => item['name'] == secScheme.secBeamSection,
          );
        } else {
          secSection = steelSections.last;
        }
        final double loadWidth =
            input.shortSpan / input.bays; // load width of secondary beam

        newUlsudl =
            ulsUDL +
            f1 *
                getSelfWeight(
                  globalData.steelUnitWeight,
                  double.parse(secSection['A']) * pow(10, 2),
                ) + //beam Self weight
            f1 *
                getPressureFromThick(
                  input.slabThickness,
                  globalData.rcUnitWeight,
                ) *
                loadWidth; // slab self weight
        final pD = newUlsudl * input.longSpan;
        final pLl = llUDL * input.longSpan;
        swMainFactored =
            f1 *
            getSelfWeight(
              globalData.steelUnitWeight,
              double.parse(secSection['A']) * pow(10, 2),
            ); // main beam SW
        Md =
            getMaxMomentBeamUDL(swMainFactored, input.shortSpan) +
            getMaxMomentPointLoadSimpleBeam(
              pD,
              input.shortSpan,
              n: input.bays - 1,
            );
        iReq =
            getMaxDeflectionSimpleBeamPointLoad(
              pLl,
              input.shortSpan,
              Es,
              1,
              n: input.bays - 1,
            ) *
            (250 / (input.shortSpan * pow(10, 3))) *
            (1 / input.compositeActionFactor);
        vD =
            getMaxShearUDLSimpleBeam(swMainFactored, input.shortSpan) +
            getMaxShearPointLoadSimpleBeam(
              pD,
              input.shortSpan,
              n: input.bays - 1,
            );
      }
      // -----------------------------
      // * update Fy based on section
      // -----------------------------
      // get original Fy first, then modify it based on the section.
      fsy = input.fsy;
      fsy = await getDesignStrength(section['name'], fsy, steelSections);

      // -----------------------------
      // * design for LIVE LOAD deflection
      // -----------------------------
      // Ix is in [cm4]
      iPro =
          double.parse(section['Ix']) *
          input.compositeActionFactor *
          pow(10, 4);

      // -----------------------------
      // * design for ULS moment (assume moment control)
      // -----------------------------
      // Sx and Zx are in [cm3]
      Mc =
          min(
            double.parse(section['Sx']) * fsy * pow(10, -3),
            1.2 * double.parse(section['Zx']) * fsy * pow(10, -3),
          ) *
          input.compositeActionFactor;

      // ********************************
      // * Design validaiton in each loop
      // ********************************

      // ! if the depth exceeds the str zone available, directly escape and declare the design failed
      depth = double.parse(section['d']) * 10; // [mm]
      if (depth > input.strZone) {
        status[0] = false;
        status[1] = false;
        break;
      }

      //* validate the Mc
      if (Mc < Md) {
        reDesignMc = true;
        final index = steelSections.indexOf(section) + 1;
        if (index <= steelSections.length - 1) {
          section = steelSections[index];
          continue; // no need to run rest of design process
        } else {
          section = steelSections[index - 1];
          reDesignMc = false;
          status[0] = false; // ! will break later as reDesignMc = false
        }
      } else {
        reDesignMc = false;
        status[0] = true;
      }

      //* validate the I_pro (deflection)
      if (iPro < iReq) {
        reDesignI = true;
        final index = steelSections.indexOf(section) + 1;
        if (index <= steelSections.length - 1) {
          section = steelSections[index];
          continue; // no need to run rest of design process
        } else {
          section = steelSections[index - 1];
          reDesignI = false;
          status[1] = false; // ! will break later as reDesignI = false
        }
      } else {
        reDesignI = false;
        status[1] = true;
      }

      shouldRedesign = reDesignMc || reDesignI;
    }

    // ********************************
    // * Return Design
    // ********************************
    if (beamDesignOption == BeamDesignOptions.secBeam) {
      secSection = section;
    } else {
      mainSection = section;
    }

    // * record the cals result
    _recordCalsResult(
      buffer,
      input,
      globalData,
      beamDesignOption: beamDesignOption,
      depth: depth,
      I_req: iReq,
      I_pro: iPro,
      Md: Md,
      Mc: Mc,
      Vd: vD,
      fsy: fsy,
      Es: Es,
      mainSection: mainSection,
      secSection: secSection,
      status: status,
    );

    loadingTable = loadingTables.firstWhere((tbl) => tbl.usage == input.usage);

    // * Finally assign the result
    if (beamDesignOption == BeamDesignOptions.secBeam) {
      final regExp = RegExp(r'fail');
      final schemeFails = regExp.hasMatch(buffer.toString());
      if (schemeFails) {
        buffer = _addWarningHeader(buffer);
      }
      return SteelBeamSchemeData(
        usage: input.usage,
        finish: loadingTable.finish,
        service: loadingTable.service,
        liveLoad: loadingTable.liveLoad,
        loadingTableId: loadingTable.loadingTableId,
        shortSpan: input.shortSpan,
        longSpan: input.longSpan,
        bays: input.bays,
        strZone: input.strZone,
        fsy: input.fsy,
        mainBeamSection: 'N.A.',
        secBeamSection:
            (status[0] && status[1])
                ? section['name'].toString().trim()
                : 'fail',
        steelBeamSchemeId: 'temp',
        calsLog: buffer.toString(),
      );
    } else {
      if (secScheme == null || secScheme.secBeamSection == 'fail') {
        buffer = _addWarningHeader(buffer);
      } else {
        final regExp = RegExp(r'fail');
        final secSchemeFail = regExp.hasMatch(secScheme.calsLog.toString());
        if (secSchemeFail) {
          buffer = _addWarningHeader(buffer);
        }
      }

      return SteelBeamSchemeData(
        usage: input.usage,
        finish: loadingTable.finish,
        service: loadingTable.service,
        liveLoad: loadingTable.liveLoad,
        loadingTableId: loadingTable.loadingTableId,
        shortSpan: input.shortSpan,
        longSpan: input.longSpan,
        bays: input.bays,
        strZone: input.strZone,
        fsy: input.fsy,
        mainBeamSection:
            (status[0] && status[1])
                ? section['name'].toString().trim()
                : 'fail',
        secBeamSection:
            (secScheme != null && secScheme.secBeamSection != 'fail')
                ? secScheme.secBeamSection
                : 'fail',
        steelBeamSchemeId: 'temp',
        calsLog: buffer.toString(),
      );
    }
  }

  StringBuffer _addWarningHeader(StringBuffer buffer) {
    final newBuffer = StringBuffer();
    newBuffer.write('\n-----------[!Start of Warning!]-------------\n');
    newBuffer.write(' Result not reliable. Either :\n');
    newBuffer.write('1. Secondary beam Fails.\n');
    newBuffer.write(
      '2. Secondary beam scheme not found during main beam design .\n',
    );
    newBuffer.write('3. Main beam fails .\n');
    newBuffer.write('-----------[!End of Warning!]-------------\n');
    newBuffer.write(buffer.toString());
    buffer = newBuffer;
    return buffer;
  }

  // void _updateRecordStatus(List<bool> status, StringBuffer buffer) {
  //   if (status[0]) {
  //     buffer.write('Required Mc: pass | ');
  //   } else {
  //     buffer.write('Required Mc: fail | ');
  //   }
  //   if (status[1]) {
  //     buffer.write('Required I: pass');
  //   } else {
  //     buffer.write('Required I: fail');
  //   }
  // }

  void _recordCalsResult(
    StringBuffer buffer,
    SteelBeamSchemeInput input,
    GlobalData globalData, {
    required String beamDesignOption,
    required double depth,
    required double I_req,
    required double I_pro,
    required double Md,
    required double Mc,
    required double Vd,
    required double fsy,
    required double Es,
    required Map<String, dynamic> mainSection,
    required Map<String, dynamic> secSection,
    required List<bool> status,
  }) {
    final x = globalData.unit;
    late final List<String> unit;
    late final double sdl;
    late final double ll;
    late final double slsLoad;
    late final double ulsLoad;
    switch (x) {
      case 'metrics':
        unit = PreferredUnit.metrics;
        break;
      case 'imperial':
        unit = PreferredUnit.imperial;
        break;
      default:
        unit = PreferredUnit.metrics;
        break;
    }
    // final unit = globalData.unit;

    buffer.clear();

    // * record target beam and status
    buffer.write('Design For: $beamDesignOption\n');
    if (status[0] && status[1]) {
      buffer.write('Beam Status: pass\n');
    } else {
      buffer.write('Beam Status: fail\n');
    }

    if (status[0]) {
      buffer.write('Required Mc: pass | ');
    } else {
      buffer.write('Required Mc: fail | ');
    }
    if (status[1]) {
      buffer.write('Required I: pass\n');
    } else {
      buffer.write('Required I: fail\n');
    }

    // * record input
    buffer.write(
      'Short Span: ${NumberFormat('0.0').format(input.shortSpan)} [${unit[3]}] | ',
    );
    buffer.write(
      'Long Span: ${NumberFormat('0.0').format(input.longSpan)} [${unit[3]}] | ',
    );
    buffer.write('Bays: ${NumberFormat('0').format(input.bays)}\n');
    buffer.write(
      'Secondary Beam Load Width: ${NumberFormat('0.0').format(input.shortSpan / input.bays)} [${unit[3]}] | ',
    );
    buffer.write(
      'Slab Thickness: ${NumberFormat('0.0').format(input.slabThickness)} [${unit[4]}]\n',
    );
    buffer.write('Fy: ${NumberFormat('0.0').format(input.fsy)} [${unit[5]}]\n');

    // * record loading
    final data = ref.read(loadingTablesControllerProvider);
    data.when(
      data: (tables) async {
        final result = tables.firstWhere((tbl) => tbl.usage == input.usage);
        sdl =
            result.finish * globalData.finishUnitWeight * pow(10, -3) +
            result.service;
        ll = result.liveLoad;
        slsLoad = sdl + ll;
        ulsLoad = globalData.sdlFactor * sdl + globalData.llFactor * ll;
        buffer.write('SDL: ${NumberFormat('0.0').format(sdl)} [${unit[1]}] | ');
        buffer.write('LL: ${NumberFormat('0.0').format(ll)} [${unit[1]}] | ');
        buffer.write(
          'SLS: ${NumberFormat('0.0').format(slsLoad)} [${unit[1]}] | ',
        );
        buffer.write(
          'ULS: ${NumberFormat('0.0').format(ulsLoad)} [${unit[1]}]\n',
        );
      },
      error: (error, stackTrace) {},
      loading: () {},
    );

    // * record section depth and available str zone
    if (beamDesignOption == BeamDesignOptions.secBeam) {
      if (status[0] && status[1]) {
        buffer.write(
          'Secondary Beam Depth: ${NumberFormat('0.0').format(double.parse(secSection['d']))} [${unit[4]}]\n',
        );
      } else {
        buffer.write('Secondary Depth: fail\n');
      }
    } else {
      if (status[0] && status[1]) {
        buffer.write(
          'Main Beam Depth: ${NumberFormat('0.0').format(double.parse(mainSection['d']))} [${unit[4]}]\n',
        );
      } else {
        buffer.write('Main Depth: fail\n');
      }
    }
    buffer.write(
      'Str Zone: ${NumberFormat('0').format(input.strZone)} [${unit[4]}]\n',
    );

    // * record M
    buffer.write('M: ${NumberFormat('0').format(Md)} [${unit[2]}] | ');
    buffer.write('Mc: ${NumberFormat('0').format(Mc)} [${unit[2]}]\n');

    // * record V
    buffer.write('V: ${NumberFormat('0').format(Vd)} [${unit[0]}] | ');

    // * record I
    buffer.write(
      'I_req: ${NumberFormat('0').format(I_req * pow(10, -4))} [${unit[8]}] | ',
    );
    buffer.write(
      'I_pro: ${NumberFormat('0').format(I_pro * pow(10, -4))} [${unit[8]}]\n',
    );

    // * Section Designation
    // if (beamDesignOption == BeamDesignOptions.secBeam) {
    //   if (status[0] && status[1]) {
    //     buffer.write('Secondary Beam Designation: ${secSection['name']}\n');
    //   } else {
    //     buffer.write('Secondary Beam Designation: fail\n');
    //   }
    // } else {
    //   if (status[0] && status[1]) {
    //     buffer.write('Main Beam Designation: ${mainSection['name']}\n');
    //   } else {
    //     buffer.write('Main Beam Designation: fail\n');
    //   }
    // }
    if (beamDesignOption == BeamDesignOptions.secBeam) {
      buffer.write('Secondary Beam Designation: ${secSection['name']}\n');
    } else {
      buffer.write('Main Beam Designation: ${mainSection['name']}\n');
    }
  }
}
