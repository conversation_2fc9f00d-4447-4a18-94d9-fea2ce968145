import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:nanoid2/nanoid2.dart';

//presentation layer
// import '../controllers/slab_scheme_data_controller.dart';
import '../../domain_layer/column_scheme_data.dart';
import 'input/custom_stateful_double_input.dart';
import 'input/custom_stateful_int_input.dart';
import '../screen/homescreen.dart';
import 'input/custom_stateful_dropList.dart';

//domain layer
import '../../domain_layer/preferences.dart';
import 'button/selection_button.dart';

class FootingSchemeInputUi extends ConsumerStatefulWidget {
  const FootingSchemeInputUi({
    this.isExpanded,
    this.maxHeight,
    this.minHeight,
    super.key,
  });

  final bool? isExpanded;
  final double? maxHeight;
  final double? minHeight;

  @override
  ConsumerState<ConsumerStatefulWidget> createState() =>
      _FootingSchemeInputUiState();
}

class _FootingSchemeInputUiState extends ConsumerState<FootingSchemeInputUi>
    with WidgetsBindingObserver {
  late bool _isExpanded;
  late double _maxHeight;
  late double _minHeight;
  late int _nosOfValueKeys;
  late ScrollController _controller;
  late final List<ValueKey> _valueKeys;

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _controller.dispose();
    super.dispose();
  }

  @override
  void initState() {
    WidgetsBinding.instance.addObserver(this);
    _isExpanded = widget.isExpanded ?? false;
    _maxHeight = widget.maxHeight ?? 210;
    _minHeight = widget.minHeight ?? 50;
    _nosOfValueKeys = 4;
    _controller = ScrollController();
    _valueKeys = [];
    // _updateHeights();
    for (var i = 0; i < _nosOfValueKeys; i++) {
      _generateUniqueId(_valueKeys);
    }
    super.initState();
  }

  @override
  void didChangeDependencies() {
    _updateHeights();
    super.didChangeDependencies();
    // Update heights when dependencies change, including after initState
  }

  @override
  void didChangeMetrics() {
    // This method is called when the metrics change (like resizing the window)
    setState(() {
      _updateHeights();
    });
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;
    final globalData = ref.watch(globalDataControllerProvider);
    final loadingTables = ref.watch(loadingTablesControllerProvider);
    final footingSchemeInput = ref.watch(footingSchemeInputControllerProvider);
    final footingSchemeInputGlobal = ref.watch(
      footingSchemeInputGlobalControllerProvider,
    );

    final int containerOpacity = 175;
    final int textOpacity = 225;
    final List<String> soilTypes = ['Soil', 'Other'];
    final List<String> soilIsSubmerged = ['true', 'false'];
    final List<String> colShape = ['Square', 'Circle'];
    return loadingTables.when(
      data: (tables) {
        return globalData.when(
          data: (data) {
            late final List<String> unit;
            switch (data.unit) {
              case 'metrics':
                unit = PreferredUnit.metrics;
                break;
              case 'imperial':
                unit = PreferredUnit.imperial;
                break;
              default:
                unit = PreferredUnit.metrics;
                break;
            }
            return footingSchemeInput.when(
              data: (input) {
                return footingSchemeInputGlobal.when(
                  data: (inputGlobal) {
                    return Column(
                      children: [
                        Padding(
                          padding: const EdgeInsets.fromLTRB(10, 0, 10, 0),
                          child: Divider(),
                        ),
                        Padding(
                          padding: const EdgeInsets.fromLTRB(10, 0, 0, 0),
                          child: Align(
                            alignment: Alignment.centerLeft,
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Wrap(
                                  crossAxisAlignment: WrapCrossAlignment.center,
                                  children: [
                                    IconButton(
                                      icon: Icon(
                                        _isExpanded
                                            ? Icons.expand_less
                                            : Icons.expand_more,
                                      ),
                                      color: colorScheme.onSurface,
                                      onPressed: () {
                                        setState(() {
                                          _isExpanded = !_isExpanded;
                                        });
                                      },
                                    ),
                                    Text(
                                      'Pad Footing Scheme Inputs',
                                      style: textTheme.titleLarge!.copyWith(
                                        color: colorScheme.onSurface,
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                        ),
                        Padding(
                          padding: const EdgeInsets.fromLTRB(10, 0, 10, 0),
                          child: Divider(),
                        ),
                        ClipRect(
                          child: ConstrainedBox(
                            constraints: BoxConstraints(
                              maxHeight: _isExpanded ? _maxHeight : 0,
                            ),
                            child: Scrollbar(
                              controller: _controller,
                              thumbVisibility: true,
                              trackVisibility: false,
                              child: SingleChildScrollView(
                                controller: _controller,
                                child: Column(
                                  mainAxisSize: MainAxisSize.min,
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Padding(
                                      padding: const EdgeInsets.fromLTRB(
                                        8.0,
                                        0.0,
                                        8.0,
                                        0.0,
                                      ),
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,

                                        children: [
                                          SizedBox(height: 5.0),
                                          Container(
                                            decoration: BoxDecoration(
                                              borderRadius:
                                                  BorderRadius.circular(10.0),
                                              border: Border.all(
                                                color: Colors.grey.withAlpha(
                                                  150,
                                                ),
                                              ),
                                              color: colorScheme
                                                  .secondaryContainer
                                                  .withAlpha(containerOpacity),
                                            ),
                                            child: Padding(
                                              padding: const EdgeInsets.all(
                                                4.0,
                                              ),
                                              child: Text(
                                                'Footing Data',
                                                style: textTheme.titleSmall!
                                                    .copyWith(
                                                      color: colorScheme
                                                          .onSecondaryContainer
                                                          .withAlpha(
                                                            textOpacity,
                                                          ),
                                                    ),
                                              ),
                                            ),
                                          ),
                                          SizedBox(height: 10.0),
                                          Row(
                                            children: [
                                              Flexible(
                                                child: CustomStatefulDoubleInput(
                                                  title: 'fcu [${unit[5]}]',
                                                  value: input.fcu,
                                                  onChanged: (value) async {
                                                    await ref
                                                        .read(
                                                          footingSchemeInputControllerProvider
                                                              .notifier,
                                                        )
                                                        .updateTable(
                                                          fcu: value,
                                                        );
                                                    await ref
                                                        .read(
                                                          footingSchemeDataControllerProvider
                                                              .notifier,
                                                        )
                                                        .footingScheming();
                                                  },
                                                ),
                                              ),
                                              SizedBox(width: 5.0),
                                              Flexible(
                                                child: CustomStatefulDoubleInput(
                                                  title: 'cover [${unit[4]}]',
                                                  value: input.cover,
                                                  onChanged: (value) async {
                                                    await ref
                                                        .read(
                                                          footingSchemeInputControllerProvider
                                                              .notifier,
                                                        )
                                                        .updateTable(
                                                          cover: value,
                                                        );
                                                    await ref
                                                        .read(
                                                          footingSchemeDataControllerProvider
                                                              .notifier,
                                                        )
                                                        .footingScheming();
                                                  },
                                                ),
                                              ),
                                              SizedBox(width: 5.0),
                                              Flexible(
                                                child: CustomStatefulDoubleInput(
                                                  title: 'Preferred k-value',
                                                  value: input.mainKValue,
                                                  tooltipText:
                                                      'design will be as close to this k-value\n'
                                                      'as possible while considering\n'
                                                      'the code limit as well',
                                                  onChanged: (value) async {
                                                    await ref
                                                        .read(
                                                          footingSchemeInputControllerProvider
                                                              .notifier,
                                                        )
                                                        .updateTable(
                                                          mainKValue: value,
                                                        );
                                                  },
                                                ),
                                              ),
                                              SizedBox(width: 5.0),
                                              Flexible(
                                                child: CustomStatefulDoubleInput(
                                                  title: 'Max steel ratio',
                                                  tooltipText:
                                                      'in decimals, not percentage',
                                                  value: input.mainSteelRatio,
                                                  onChanged: (value) async {
                                                    await ref
                                                        .read(
                                                          footingSchemeInputControllerProvider
                                                              .notifier,
                                                        )
                                                        .updateTable(
                                                          mainSteelRatio: value,
                                                        );
                                                    await ref
                                                        .read(
                                                          footingSchemeDataControllerProvider
                                                              .notifier,
                                                        )
                                                        .footingScheming();
                                                  },
                                                ),
                                              ),

                                              SizedBox(width: 5.0),
                                            ],
                                          ),
                                        ],
                                      ),
                                    ),
                                    Padding(
                                      padding: const EdgeInsets.fromLTRB(
                                        8.0,
                                        0.0,
                                        8.0,
                                        0.0,
                                      ),
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,

                                        children: [
                                          SizedBox(height: 5.0),
                                          Container(
                                            decoration: BoxDecoration(
                                              borderRadius:
                                                  BorderRadius.circular(10.0),
                                              border: Border.all(
                                                color: Colors.grey.withAlpha(
                                                  150,
                                                ),
                                              ),
                                              color: colorScheme
                                                  .secondaryContainer
                                                  .withAlpha(containerOpacity),
                                            ),
                                            child: Padding(
                                              padding: const EdgeInsets.all(
                                                4.0,
                                              ),
                                              child: Text(
                                                'Limit',
                                                style: textTheme.titleSmall!
                                                    .copyWith(
                                                      color: colorScheme
                                                          .onSecondaryContainer
                                                          .withAlpha(
                                                            textOpacity,
                                                          ),
                                                    ),
                                              ),
                                            ),
                                          ),
                                          SizedBox(height: 10.0),
                                          Row(
                                            children: [
                                              Flexible(
                                                child: CustomStatefulIntInput(
                                                  title:
                                                      'Min Links Spacing [${unit[4]}]',
                                                  value: input.minS,
                                                  onChanged: (value) async {
                                                    await ref
                                                        .read(
                                                          footingSchemeInputControllerProvider
                                                              .notifier,
                                                        )
                                                        .updateTable(
                                                          minS: value,
                                                        );
                                                    await ref
                                                        .read(
                                                          footingSchemeDataControllerProvider
                                                              .notifier,
                                                        )
                                                        .footingScheming();
                                                  },
                                                ),
                                              ),
                                              SizedBox(width: 5.0),
                                              Flexible(
                                                child: CustomStatefulIntInput(
                                                  title:
                                                      'Max Links Spacing [${unit[4]}]',
                                                  value: input.maxS,
                                                  onChanged: (value) async {
                                                    await ref
                                                        .read(
                                                          footingSchemeInputControllerProvider
                                                              .notifier,
                                                        )
                                                        .updateTable(
                                                          maxS: value,
                                                        );
                                                    await ref
                                                        .read(
                                                          footingSchemeDataControllerProvider
                                                              .notifier,
                                                        )
                                                        .footingScheming();
                                                  },
                                                ),
                                              ),

                                              SizedBox(width: 5.0),
                                              Flexible(
                                                child: CustomStatefulDoubleInput(
                                                  title:
                                                      'Min Footing Depth [${unit[4]}]',
                                                  value: input.minDepth,
                                                  onChanged: (value) async {
                                                    await ref
                                                        .read(
                                                          footingSchemeInputControllerProvider
                                                              .notifier,
                                                        )
                                                        .updateTable(
                                                          minDepth: value,
                                                        );
                                                    await ref
                                                        .read(
                                                          footingSchemeDataControllerProvider
                                                              .notifier,
                                                        )
                                                        .footingScheming();
                                                  },
                                                ),
                                              ),
                                              SizedBox(width: 5.0),
                                              Flexible(
                                                child: CustomStatefulDoubleInput(
                                                  title:
                                                      'Max Footing Depth [${unit[4]}]',
                                                  value: input.maxDepth,
                                                  onChanged: (value) async {
                                                    await ref
                                                        .read(
                                                          footingSchemeInputControllerProvider
                                                              .notifier,
                                                        )
                                                        .updateTable(
                                                          maxDepth: value,
                                                        );
                                                    await ref
                                                        .read(
                                                          footingSchemeDataControllerProvider
                                                              .notifier,
                                                        )
                                                        .footingScheming();
                                                  },
                                                ),
                                              ),
                                              SizedBox(width: 5.0),
                                            ],
                                          ),
                                          SizedBox(height: 10.0),
                                          Row(
                                            children: [
                                              Flexible(
                                                child: CustomStatefulIntInput(
                                                  title: 'Max Rebar Layer',
                                                  value: input.maxLayers,
                                                  onChanged: (value) async {
                                                    await ref
                                                        .read(
                                                          footingSchemeInputControllerProvider
                                                              .notifier,
                                                        )
                                                        .updateTable(
                                                          maxLayers: value,
                                                        );
                                                    await ref
                                                        .read(
                                                          footingSchemeDataControllerProvider
                                                              .notifier,
                                                        )
                                                        .footingScheming();
                                                  },
                                                ),
                                              ),
                                              Flexible(
                                                child: SizedBox(width: 5.0),
                                              ),
                                              Flexible(
                                                child: SizedBox(width: 5.0),
                                              ),
                                              Flexible(
                                                child: SizedBox(width: 5.0),
                                              ),
                                            ],
                                          ),
                                        ],
                                      ),
                                    ),
                                    Padding(
                                      padding: const EdgeInsets.fromLTRB(
                                        8.0,
                                        0.0,
                                        8.0,
                                        0.0,
                                      ),
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,

                                        children: [
                                          SizedBox(height: 5.0),
                                          Container(
                                            decoration: BoxDecoration(
                                              borderRadius:
                                                  BorderRadius.circular(10.0),
                                              border: Border.all(
                                                color: Colors.grey.withAlpha(
                                                  150,
                                                ),
                                              ),
                                              color: colorScheme
                                                  .secondaryContainer
                                                  .withAlpha(containerOpacity),
                                            ),
                                            child: Padding(
                                              padding: const EdgeInsets.all(
                                                4.0,
                                              ),
                                              child: Text(
                                                'Soil Data',
                                                style: textTheme.titleSmall!
                                                    .copyWith(
                                                      color: colorScheme
                                                          .onSecondaryContainer
                                                          .withAlpha(
                                                            textOpacity,
                                                          ),
                                                    ),
                                              ),
                                            ),
                                          ),
                                          SizedBox(height: 10.0),
                                          Row(
                                            children: [
                                              CustomStatefulDropDown(
                                                items: soilTypes,
                                                selectedValue:
                                                    input.groundType == ''
                                                        ? null
                                                        : input.groundType,
                                                onTap: (selectedValue) async {
                                                  await ref
                                                      .read(
                                                        footingSchemeInputControllerProvider
                                                            .notifier,
                                                      )
                                                      .updateTable(
                                                        groundType:
                                                            selectedValue,
                                                      );
                                                  await ref
                                                      .read(
                                                        footingSchemeDataControllerProvider
                                                            .notifier,
                                                      )
                                                      .footingScheming();
                                                },
                                              ),
                                              SizedBox(width: 5.0),
                                              Builder(
                                                builder: (context) {
                                                  if (input.groundType ==
                                                      'Soil') {
                                                    return Flexible(
                                                      child: Row(
                                                        children: [
                                                          SizedBox(width: 5.0),
                                                          Flexible(
                                                            child: CustomStatefulDoubleInput(
                                                              key:
                                                                  _valueKeys[0],
                                                              title:
                                                                  'Soil SPT-N-Value',
                                                              value:
                                                                  input
                                                                      .soilNValue,
                                                              onChanged: (
                                                                value,
                                                              ) async {
                                                                await ref
                                                                    .read(
                                                                      footingSchemeInputControllerProvider
                                                                          .notifier,
                                                                    )
                                                                    .updateTable(
                                                                      soilNValue:
                                                                          value,
                                                                    );
                                                                await ref
                                                                    .read(
                                                                      footingSchemeDataControllerProvider
                                                                          .notifier,
                                                                    )
                                                                    .footingScheming();
                                                              },
                                                            ),
                                                          ),
                                                          SizedBox(width: 5.0),
                                                          Flexible(
                                                            child: CustomStatefulDoubleInput(
                                                              key:
                                                                  _valueKeys[1],
                                                              title:
                                                                  'Footing Top Level [${unit[10]}]',
                                                              tooltipText:
                                                                  'check if soil submerged and\nhence strength reduced',
                                                              value:
                                                                  input
                                                                      .footingTopLevel,
                                                              onChanged: (
                                                                value,
                                                              ) async {
                                                                await ref
                                                                    .read(
                                                                      footingSchemeInputControllerProvider
                                                                          .notifier,
                                                                    )
                                                                    .updateTable(
                                                                      footingTopLevel:
                                                                          value,
                                                                    );
                                                                await ref
                                                                    .read(
                                                                      footingSchemeDataControllerProvider
                                                                          .notifier,
                                                                    )
                                                                    .footingScheming();
                                                              },
                                                            ),
                                                          ),
                                                          SizedBox(width: 5.0),
                                                          Flexible(
                                                            child: CustomStatefulDoubleInput(
                                                              allowNegative:
                                                                  true,
                                                              key:
                                                                  _valueKeys[2],
                                                              title:
                                                                  'Water Table Level [${unit[10]}]',
                                                              tooltipText:
                                                                  'check if soil submerged and\nhence strength reduced',

                                                              value:
                                                                  input
                                                                      .waterTableLevel,
                                                              onChanged: (
                                                                value,
                                                              ) async {
                                                                await ref
                                                                    .read(
                                                                      footingSchemeInputControllerProvider
                                                                          .notifier,
                                                                    )
                                                                    .updateTable(
                                                                      waterTableLevel:
                                                                          value,
                                                                    );
                                                                await ref
                                                                    .read(
                                                                      footingSchemeDataControllerProvider
                                                                          .notifier,
                                                                    )
                                                                    .footingScheming();
                                                              },
                                                            ),
                                                          ),
                                                        ],
                                                      ),
                                                    );
                                                  } else {
                                                    return Flexible(
                                                      child: Row(
                                                        children: [
                                                          SizedBox(width: 5.0),
                                                          Flexible(
                                                            child: CustomStatefulDoubleInput(
                                                              key:
                                                                  _valueKeys[3],
                                                              title:
                                                                  'Given Capacity [${unit[1]}]',
                                                              value:
                                                                  input
                                                                      .rockCapacity,
                                                              onChanged: (
                                                                value,
                                                              ) async {
                                                                await ref
                                                                    .read(
                                                                      footingSchemeInputControllerProvider
                                                                          .notifier,
                                                                    )
                                                                    .updateTable(
                                                                      rockCapacity:
                                                                          value,
                                                                    );
                                                                await ref
                                                                    .read(
                                                                      footingSchemeDataControllerProvider
                                                                          .notifier,
                                                                    )
                                                                    .footingScheming();
                                                              },
                                                            ),
                                                          ),
                                                          // Flexible(
                                                          //   child: SizedBox(
                                                          //     width: 5.0,
                                                          //   ),
                                                          // ),
                                                        ],
                                                      ),
                                                    );
                                                  }
                                                },
                                              ),
                                            ],
                                          ),
                                        ],
                                      ),
                                    ),
                                    Padding(
                                      padding: const EdgeInsets.fromLTRB(
                                        8.0,
                                        0.0,
                                        8.0,
                                        0.0,
                                      ),
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,

                                        children: [
                                          SizedBox(height: 5.0),
                                          Container(
                                            decoration: BoxDecoration(
                                              borderRadius:
                                                  BorderRadius.circular(10.0),
                                              border: Border.all(
                                                color: Colors.grey.withAlpha(
                                                  150,
                                                ),
                                              ),
                                              color: colorScheme
                                                  .secondaryContainer
                                                  .withAlpha(containerOpacity),
                                            ),
                                            child: Padding(
                                              padding: const EdgeInsets.all(
                                                4.0,
                                              ),
                                              child: Text(
                                                'Other',
                                                style: textTheme.titleSmall!
                                                    .copyWith(
                                                      color: colorScheme
                                                          .onSecondaryContainer
                                                          .withAlpha(
                                                            textOpacity,
                                                          ),
                                                    ),
                                              ),
                                            ),
                                          ),
                                          Row(
                                            children: [
                                              CustomStatefulDropDown(
                                                items: colShape,
                                                selectedValue:
                                                    input.colShape == ''
                                                        ? null
                                                        : input.colShape,
                                                onTap: (selectedValue) async {
                                                  await ref
                                                      .read(
                                                        footingSchemeInputControllerProvider
                                                            .notifier,
                                                      )
                                                      .updateTable(
                                                        colShape: selectedValue,
                                                      );
                                                  await ref
                                                      .read(
                                                        footingSchemeDataControllerProvider
                                                            .notifier,
                                                      )
                                                      .footingScheming();
                                                },
                                              ),
                                              SizedBox(width: 5.0),
                                              Flexible(
                                                child: CustomStatefulDoubleInput(
                                                  title:
                                                      'Column Size [${unit[4]}]',
                                                  value: input.columnSize,
                                                  onChanged: (value) async {
                                                    await ref
                                                        .read(
                                                          footingSchemeInputControllerProvider
                                                              .notifier,
                                                        )
                                                        .updateTable(
                                                          columnSize: value,
                                                        );
                                                    await ref
                                                        .read(
                                                          footingSchemeDataControllerProvider
                                                              .notifier,
                                                        )
                                                        .footingScheming();
                                                  },
                                                ),
                                              ),
                                              SizedBox(width: 5.0),
                                              Flexible(
                                                child: CustomStatefulDoubleInput(
                                                  readOnly:
                                                      input.useSelectColLoad,
                                                  title:
                                                      'SLS Load [${unit[0]}]',
                                                  value: input.slsLoad,
                                                  onChanged: (value) async {
                                                    await ref
                                                        .read(
                                                          footingSchemeInputControllerProvider
                                                              .notifier,
                                                        )
                                                        .updateTable(
                                                          slsLoad: value,
                                                        );
                                                    await ref
                                                        .read(
                                                          footingSchemeDataControllerProvider
                                                              .notifier,
                                                        )
                                                        .footingScheming();
                                                  },
                                                ),
                                              ),

                                              SizedBox(width: 5.0),
                                              Flexible(
                                                child: CustomStatefulDoubleInput(
                                                  readOnly:
                                                      input.useSelectColLoad,
                                                  title:
                                                      'ULS Load [${unit[0]}]',
                                                  value: input.ulsLoad,
                                                  onChanged: (value) async {
                                                    await ref
                                                        .read(
                                                          footingSchemeInputControllerProvider
                                                              .notifier,
                                                        )
                                                        .updateTable(
                                                          ulsLoad: value,
                                                        );
                                                    await ref
                                                        .read(
                                                          footingSchemeDataControllerProvider
                                                              .notifier,
                                                        )
                                                        .footingScheming();
                                                  },
                                                ),
                                              ),
                                              SizedBox(width: 5.0),
                                            ],
                                          ),
                                          SizedBox(height: 10.0),
                                          Row(
                                            children: [
                                              SelectionButton(
                                                labelTextStyle:
                                                    input.useSelectColLoad
                                                        ? textTheme.labelLarge!
                                                            .copyWith(
                                                              color:
                                                                  colorScheme
                                                                      .onTertiaryContainer,
                                                            )
                                                        : textTheme.labelLarge!
                                                            .copyWith(
                                                              color:
                                                                  colorScheme
                                                                      .onSurface,
                                                            ),
                                                labelText:
                                                    'Use Load from\nSelected Column',
                                                pressedColor:
                                                    colorScheme
                                                        .tertiaryContainer,
                                                bgColor:
                                                    input.useSelectColLoad
                                                        ? colorScheme
                                                            .tertiaryContainer
                                                        : colorScheme
                                                            .surfaceContainer,

                                                onTap: (value) async {
                                                  final input = await ref.read(
                                                    footingSchemeInputControllerProvider
                                                        .future,
                                                  );
                                                  final colData = await ref.read(
                                                    columnSchemeDataControllerProvider
                                                        .future,
                                                  );

                                                  await ref
                                                      .read(
                                                        footingSchemeInputControllerProvider
                                                            .notifier,
                                                      )
                                                      .updateTable(
                                                        useSelectColLoad:
                                                            !input
                                                                .useSelectColLoad,
                                                      );
                                                  //* Tricky here: if the new toggled value (!input.useSlabSelected) is true,
                                                  //* we run below logic
                                                  if (!input.useSelectColLoad) {
                                                    final selectedColumn =
                                                        colData.firstWhere(
                                                          (scheme) =>
                                                              scheme.isSelected,
                                                          orElse:
                                                              () =>
                                                                  ColumnSchemeData(),
                                                        );
                                                    await ref
                                                        .read(
                                                          footingSchemeInputControllerProvider
                                                              .notifier,
                                                        )
                                                        .updateTable(
                                                          slsLoad:
                                                              selectedColumn
                                                                  .slsLoad /
                                                              inputGlobal
                                                                  .colLoadFactor,
                                                          ulsLoad:
                                                              selectedColumn
                                                                  .ulsLoad /
                                                              inputGlobal
                                                                  .colLoadFactor,
                                                        );
                                                  }
                                                },
                                              ),
                                              SizedBox(width: 5.0),
                                              Flexible(
                                                child: CustomStatefulDoubleInput(
                                                  readOnly:
                                                      !input.useSelectColLoad,
                                                  title: 'Column Load Dividor',
                                                  value:
                                                      inputGlobal.colLoadFactor,
                                                  listener: (
                                                    hasFocus,
                                                    value,
                                                  ) async {
                                                    if (!hasFocus) {
                                                      await ref
                                                          .read(
                                                            footingSchemeInputGlobalControllerProvider
                                                                .notifier,
                                                          )
                                                          .updateTable(
                                                            colLoadFactor:
                                                                value,
                                                          );
                                                    }
                                                  },
                                                ),
                                              ),

                                              Flexible(
                                                child: SizedBox(width: 5.0),
                                              ),
                                              Flexible(
                                                child: SizedBox(width: 5.0),
                                              ),
                                            ],
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    );
                  },
                  error: (error, stackTrace) => Text(error.toString()),
                  loading: () => const CircularProgressIndicator(),
                );
              },
              error: (error, stackTrace) => Text(error.toString()),
              loading: () => const CircularProgressIndicator(),
            );
          },
          error: (error, stackTrace) => Text(error.toString()),
          loading: () => Center(child: CircularProgressIndicator()),
        );
      },
      error: (error, stackTrace) => Text(error.toString()),
      loading: () => Center(child: CircularProgressIndicator()),
    );
  }

  void _updateHeights() {
    final double screenHeight = MediaQuery.of(context).size.height;
    _maxHeight =
        (widget.maxHeight == null)
            ? screenHeight * 0.6
            : (widget.maxHeight! > screenHeight * 0.6)
            ? screenHeight * 0.6
            : widget.maxHeight!;

    _minHeight =
        (widget.minHeight == null)
            ? screenHeight * 0.1
            : (widget.minHeight! > screenHeight * 0.1)
            ? screenHeight * 0.1
            : widget.minHeight!;

    // Adjust _maxHeight if needed

    if (_maxHeight < _minHeight) {
      _maxHeight = _minHeight;
    }
  }

  Future<void> _generateUniqueId(List<ValueKey> allValueKeys) async {
    String newKey = nanoid(alphabet: Alphabet.base64);
    while (allValueKeys.contains(newKey)) {
      newKey = nanoid(alphabet: Alphabet.base64);
    }
    allValueKeys.add(ValueKey(newKey));
  }
}
