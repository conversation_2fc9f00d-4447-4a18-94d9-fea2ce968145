// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'beam_scheme_data_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$beamSchemeDataControllerHash() =>
    r'0030099f1001b10e23bb56febe753fb8b5f4a10a';

/// See also [BeamSchemeDataController].
@ProviderFor(BeamSchemeDataController)
final beamSchemeDataControllerProvider = AutoDisposeAsyncNotifierProvider<
  BeamSchemeDataController,
  List<BeamSchemeData>
>.internal(
  BeamSchemeDataController.new,
  name: r'beamSchemeDataControllerProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$beamSchemeDataControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$BeamSchemeDataController =
    AutoDisposeAsyncNotifier<List<BeamSchemeData>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
