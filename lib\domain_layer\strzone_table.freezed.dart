// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'strzone_table.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$StrZoneTable {

 String get floor; double get height; double get finish; double get service; double get clear; int get nosOfFloor; String get strZoneId;
/// Create a copy of StrZoneTable
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$StrZoneTableCopyWith<StrZoneTable> get copyWith => _$StrZoneTableCopyWithImpl<StrZoneTable>(this as StrZoneTable, _$identity);

  /// Serializes this StrZoneTable to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is StrZoneTable&&(identical(other.floor, floor) || other.floor == floor)&&(identical(other.height, height) || other.height == height)&&(identical(other.finish, finish) || other.finish == finish)&&(identical(other.service, service) || other.service == service)&&(identical(other.clear, clear) || other.clear == clear)&&(identical(other.nosOfFloor, nosOfFloor) || other.nosOfFloor == nosOfFloor)&&(identical(other.strZoneId, strZoneId) || other.strZoneId == strZoneId));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,floor,height,finish,service,clear,nosOfFloor,strZoneId);

@override
String toString() {
  return 'StrZoneTable(floor: $floor, height: $height, finish: $finish, service: $service, clear: $clear, nosOfFloor: $nosOfFloor, strZoneId: $strZoneId)';
}


}

/// @nodoc
abstract mixin class $StrZoneTableCopyWith<$Res>  {
  factory $StrZoneTableCopyWith(StrZoneTable value, $Res Function(StrZoneTable) _then) = _$StrZoneTableCopyWithImpl;
@useResult
$Res call({
 String floor, double height, double finish, double service, double clear, int nosOfFloor, String strZoneId
});




}
/// @nodoc
class _$StrZoneTableCopyWithImpl<$Res>
    implements $StrZoneTableCopyWith<$Res> {
  _$StrZoneTableCopyWithImpl(this._self, this._then);

  final StrZoneTable _self;
  final $Res Function(StrZoneTable) _then;

/// Create a copy of StrZoneTable
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? floor = null,Object? height = null,Object? finish = null,Object? service = null,Object? clear = null,Object? nosOfFloor = null,Object? strZoneId = null,}) {
  return _then(_self.copyWith(
floor: null == floor ? _self.floor : floor // ignore: cast_nullable_to_non_nullable
as String,height: null == height ? _self.height : height // ignore: cast_nullable_to_non_nullable
as double,finish: null == finish ? _self.finish : finish // ignore: cast_nullable_to_non_nullable
as double,service: null == service ? _self.service : service // ignore: cast_nullable_to_non_nullable
as double,clear: null == clear ? _self.clear : clear // ignore: cast_nullable_to_non_nullable
as double,nosOfFloor: null == nosOfFloor ? _self.nosOfFloor : nosOfFloor // ignore: cast_nullable_to_non_nullable
as int,strZoneId: null == strZoneId ? _self.strZoneId : strZoneId // ignore: cast_nullable_to_non_nullable
as String,
  ));
}

}


/// Adds pattern-matching-related methods to [StrZoneTable].
extension StrZoneTablePatterns on StrZoneTable {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _StrZoneTable value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _StrZoneTable() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _StrZoneTable value)  $default,){
final _that = this;
switch (_that) {
case _StrZoneTable():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _StrZoneTable value)?  $default,){
final _that = this;
switch (_that) {
case _StrZoneTable() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String floor,  double height,  double finish,  double service,  double clear,  int nosOfFloor,  String strZoneId)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _StrZoneTable() when $default != null:
return $default(_that.floor,_that.height,_that.finish,_that.service,_that.clear,_that.nosOfFloor,_that.strZoneId);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String floor,  double height,  double finish,  double service,  double clear,  int nosOfFloor,  String strZoneId)  $default,) {final _that = this;
switch (_that) {
case _StrZoneTable():
return $default(_that.floor,_that.height,_that.finish,_that.service,_that.clear,_that.nosOfFloor,_that.strZoneId);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String floor,  double height,  double finish,  double service,  double clear,  int nosOfFloor,  String strZoneId)?  $default,) {final _that = this;
switch (_that) {
case _StrZoneTable() when $default != null:
return $default(_that.floor,_that.height,_that.finish,_that.service,_that.clear,_that.nosOfFloor,_that.strZoneId);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _StrZoneTable extends StrZoneTable {
   _StrZoneTable({this.floor = 'Floor', this.height = 3500.0, this.finish = 50, this.service = 300, this.clear = 2300, this.nosOfFloor = 1, this.strZoneId = ''}): super._();
  factory _StrZoneTable.fromJson(Map<String, dynamic> json) => _$StrZoneTableFromJson(json);

@override@JsonKey() final  String floor;
@override@JsonKey() final  double height;
@override@JsonKey() final  double finish;
@override@JsonKey() final  double service;
@override@JsonKey() final  double clear;
@override@JsonKey() final  int nosOfFloor;
@override@JsonKey() final  String strZoneId;

/// Create a copy of StrZoneTable
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$StrZoneTableCopyWith<_StrZoneTable> get copyWith => __$StrZoneTableCopyWithImpl<_StrZoneTable>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$StrZoneTableToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _StrZoneTable&&(identical(other.floor, floor) || other.floor == floor)&&(identical(other.height, height) || other.height == height)&&(identical(other.finish, finish) || other.finish == finish)&&(identical(other.service, service) || other.service == service)&&(identical(other.clear, clear) || other.clear == clear)&&(identical(other.nosOfFloor, nosOfFloor) || other.nosOfFloor == nosOfFloor)&&(identical(other.strZoneId, strZoneId) || other.strZoneId == strZoneId));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,floor,height,finish,service,clear,nosOfFloor,strZoneId);

@override
String toString() {
  return 'StrZoneTable(floor: $floor, height: $height, finish: $finish, service: $service, clear: $clear, nosOfFloor: $nosOfFloor, strZoneId: $strZoneId)';
}


}

/// @nodoc
abstract mixin class _$StrZoneTableCopyWith<$Res> implements $StrZoneTableCopyWith<$Res> {
  factory _$StrZoneTableCopyWith(_StrZoneTable value, $Res Function(_StrZoneTable) _then) = __$StrZoneTableCopyWithImpl;
@override @useResult
$Res call({
 String floor, double height, double finish, double service, double clear, int nosOfFloor, String strZoneId
});




}
/// @nodoc
class __$StrZoneTableCopyWithImpl<$Res>
    implements _$StrZoneTableCopyWith<$Res> {
  __$StrZoneTableCopyWithImpl(this._self, this._then);

  final _StrZoneTable _self;
  final $Res Function(_StrZoneTable) _then;

/// Create a copy of StrZoneTable
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? floor = null,Object? height = null,Object? finish = null,Object? service = null,Object? clear = null,Object? nosOfFloor = null,Object? strZoneId = null,}) {
  return _then(_StrZoneTable(
floor: null == floor ? _self.floor : floor // ignore: cast_nullable_to_non_nullable
as String,height: null == height ? _self.height : height // ignore: cast_nullable_to_non_nullable
as double,finish: null == finish ? _self.finish : finish // ignore: cast_nullable_to_non_nullable
as double,service: null == service ? _self.service : service // ignore: cast_nullable_to_non_nullable
as double,clear: null == clear ? _self.clear : clear // ignore: cast_nullable_to_non_nullable
as double,nosOfFloor: null == nosOfFloor ? _self.nosOfFloor : nosOfFloor // ignore: cast_nullable_to_non_nullable
as int,strZoneId: null == strZoneId ? _self.strZoneId : strZoneId // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

// dart format on
