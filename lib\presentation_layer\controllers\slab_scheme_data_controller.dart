import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:nanoid2/nanoid2.dart';
import 'dart:math';
import 'package:intl/intl.dart';
import 'package:structify/domain_layer/slab_scheme_data.dart';

//presentation layer
import '../../domain_layer/mixin/mixin_rc_str.dart';
import '../../domain_layer/mixin/mixin_str_general_cals.dart';
import '../../domain_layer/slab_scheme_input.dart';
import '../screen/homescreen.dart';

// domain layer
import '../../domain_layer/global_data.dart';
import '../../domain_layer/loading_table.dart';
import '../../domain_layer/preferences.dart';

part 'slab_scheme_data_controller.g.dart';

@riverpod
class SlabSchemeDataController extends _$SlabSchemeDataController
    with StrGeneralCals, RCStrHK {
  @override
  FutureOr<List<SlabSchemeData>> build() async {
    final slabSchemeDataList =
        await ref
            .read(appDatabaseControllerProvider.notifier)
            .querySlabSchemeData();
    final data1 = ref.watch(loadingTablesControllerProvider);
    final data2 = ref.watch(globalDataControllerProvider);
    final data3 = ref.watch(slabSchemeInputControllerProvider);

    return data1.when(
      data: (loadingTables) async {
        return data2.when(
          data: (globalData) async {
            return data3.when(
              data: (inputs) async {
                return await batchSlabScheming(
                  existingSchemes: slabSchemeDataList,
                  calledInBuild: true,
                );
              },
              error: (error, stackTrace) => [],
              loading: () => [],
            );
          },
          error: (error, stackTrace) => [],
          loading: () => [],
        );
      },
      error: (error, stackTrace) => [],
      loading: () => [],
    );
  }

  Future<void> addSlabSchemeData(List<SlabSchemeData> slabSchemeData) async {
    final x = await future;
    state = AsyncData([...x, ...slabSchemeData]);
  }

  Future<void> deleteTable(String id) async {
    final x = await future;
    x.removeWhere((item) => item.slabSchemeId == id);
    state = AsyncData(x);
  }

  Future<void> deleteAllTable() async {
    state = AsyncData([]);
  }

  Future<List<SlabSchemeData>> deleteTablesNotSelected({
    List<SlabSchemeData>? existingScheme,
    bool? calledInBuild,
  }) async {
    late final List<SlabSchemeData> schemes;
    if (existingScheme != null && existingScheme.isNotEmpty) {
      schemes = existingScheme;
    } else {
      if (calledInBuild != null && calledInBuild) {
        schemes = [];
      } else {
        schemes = await future;
      }
    }
    schemes.removeWhere((item) => !item.isSelected);
    state = AsyncData(schemes);
    return schemes;
  }

  Future<void> replaceEntireTable(List<SlabSchemeData> slabSchemeData) async {
    state = AsyncData(slabSchemeData);
  }

  Future<String> _generateUniqueId({Set<String>? existingID}) async {
    late final Set<String> slabSchemeDataId;
    if (existingID == null) {
      final x = await future;
      slabSchemeDataId = x.map((item) => item.slabSchemeId).toSet();
    } else {
      slabSchemeDataId = existingID;
    }
    String newID = nanoid(alphabet: Alphabet.noDoppelgangerSafe);
    while (slabSchemeDataId.contains(newID)) {
      newID = nanoid(alphabet: Alphabet.noDoppelgangerSafe);
    }
    return newID;
  }

  Future<void> toggleSelectScheme(String slabSchemeId) async {
    final x = await future;
    final newState =
        x.map((item) {
          if (item.slabSchemeId == slabSchemeId) {
            return item.copyWith(isSelected: !item.isSelected);
          } else {
            return item.copyWith(isSelected: false);
          }
        }).toList();
    state = AsyncData(newState);
  }

  Future<List<SlabSchemeData>> batchSlabScheming({
    List<SlabSchemeData>? existingSchemes,
    bool? calledInBuild,
  }) async {
    //* keep selected schemes
    final List<SlabSchemeData> finalList = [];
    late final List<SlabSchemeData> schemesLeft;
    schemesLeft = await deleteTablesNotSelected(
      existingScheme: existingSchemes,
      calledInBuild: calledInBuild,
    );
    finalList.addAll(schemesLeft);
    final existingID = existingSchemes?.map((e) => e.slabSchemeId).toSet();
    //* cals starts
    final input = await ref.read(slabSchemeInputControllerProvider.future);
    final globalData = await ref.read(globalDataControllerProvider.future);
    final loadingTables = await ref.read(
      loadingTablesControllerProvider.future,
    );

    //* Loading
    final String usage = input.usage;
    final LoadingTable load = loadingTables.firstWhere(
      (item) => item.usage == usage,
      orElse: () => LoadingTable(usage: 'No imposed load'),
    );
    final double sdl =
        load.finish * globalData.finishUnitWeight * pow(10, -3) +
        load.service; // in [kPa]
    final double ll = load.liveLoad; // in [kPa]
    final double slsLoad = sdl + ll; // in [kPa]
    final double ulsLoad =
        globalData.sdlFactor * sdl + globalData.llFactor * ll; // in [kPa]

    late SlabSchemeData slabScheme;
    for (var i = 0; i < input.iterationSteps; i++) {
      // new input refers updated spans and bays after each iteration
      //
      final newInput = input.copyWith(
        span: input.span + input.spanIncreament * i,
      );
      final double w = 1; // load width is 1m for one-way slab
      final double ulsUDL = ulsLoad * w; // ULS UDL

      //we separate two schemes for main and secondary beam
      // then later combine info from them into variable 'result' (see below)

      slabScheme = _slabScheming(
        newInput,
        ulsUDL,
        slsLoad,
        globalData,
        loadingTables,
      );

      final String id = await _generateUniqueId(existingID: existingID);
      slabScheme = slabScheme.copyWith(slabSchemeId: id); // replace the id

      finalList.add(slabScheme);
    }
    state = AsyncData(finalList);
    return finalList;
  }

  SlabSchemeData _slabScheming(
    SlabSchemeInput input,
    double ulsUDL,
    double slsUDL,
    GlobalData globalData,
    List<LoadingTable> loadingTables,
  ) {
    // ********************************
    // initialization
    // ********************************
    // iteration status
    late bool shouldRedesign;
    bool skipIteration = false;
    bool redesignacT = true,
        redesignacC = true,
        reDesignLinks = true,
        reDesignDeflection = true;
    int linksOptimizationSteps = 0;
    int linksOptimizationStepsLimit = 10;

    int compSteelOptimizationSteps = 0;
    int compSteelOptimizationStepsLimit = 10;

    // rebar diameters
    late List<int> diaT; // tension bar diameter
    late List<int> diaC; // tension bar diameter
    late List<int> diaL; // links diameter
    final int spacerBar = 10; // spacer bar diameter for >1 rebar layer
    //inddex for iteration for rebar diameters and links diameter
    int i1 = 0, i2 = 0, j = 0;

    //Initialize the width
    final double width = 1000;
    double depth = input.minDepth;

    //k-value
    late double k;
    late double kLimit;

    //effective depth and lever-arm
    late double d, dC;
    late double z;

    // rebar provided
    int sTPro = input.maxS;
    int sCPro = input.maxS;
    late int sPro;

    // links provided: legs nos and spacing
    int sProX = input.maxS; // links, if any, spacing across section widht
    int sProY = input.maxS; // links, if any, spacing along span

    //nos of layer of tension and compression steel
    int layerT = 1, layerC = 1;

    // Some Limit for reasonable design
    int sMin = input.minS, sMax = input.maxS; // links spacing

    //req'd tension, compression steel, and links
    late double asT, asC;
    double lReq = 0;

    //provided tension, compression steel and links
    late double asTPro, asCPro, lPro;
    String asTProText = '', asCProText = '', lProText = '';

    //revised loading (incorproate Self Weight)
    late double newUlsUDL, newSlsUDL;

    // design moment and shear
    late double mD, vD;

    // design capacity
    double fy = 500; // [MPa]

    late double vC_, vD_, vR_;

    //deflection
    double maxDel = 0, maxDelLimit = 0;

    // record
    StringBuffer buffer = StringBuffer();
    List<bool> status = [false, false, false, false];
    LoadingTable loadingTable;

    // Some global data
    final double f1 = globalData.sdlFactor;

    // ********************************
    // Calculation
    // ********************************
    shouldRedesign = true; // at least run the first

    while (shouldRedesign) {
      if (depth <= 175) {
        diaT = [10, 12, 16, 20];
        diaC = diaT;
        diaL = [10, 12, 16, 20];
      } else if (depth > 175 && depth <= 200) {
        diaT = [10, 12, 16, 20, 25];
        diaC = diaT;
        diaL = [10, 12, 16, 20];
      } else if (depth > 200 && depth <= 250) {
        diaT = [10, 12, 16, 20, 25, 32];
        diaC = diaT;
        diaL = [10, 12, 16, 20];
      } else {
        diaT = [10, 12, 16, 20, 25, 32, 40];
        diaC = diaT;
        diaL = [10, 12, 16, 20, 25];
      }
      // ********************************
      // design moment and shear adjusted with beam self-weight
      // ********************************
      final double loadWidth = width; // load width
      newUlsUDL =
          ulsUDL +
          f1 *
              getSelfWeight(
                globalData.rcUnitWeight,
                width * depth,
              ); // slab self weight
      newSlsUDL =
          slsUDL + getSelfWeight(globalData.rcUnitWeight, width * depth);
      mD = getMaxMomentBeamUDL(newUlsUDL, input.span);
      vD = getMaxShearUDLSimpleBeam(newUlsUDL, input.span);
      // ********************************
      // Calculations
      // ********************************

      // -----------------------------
      // effective depth
      // -----------------------------
      if (lReq <= 0) {
        d =
            depth -
            input.cover -
            (diaT[i1] + (diaT.last + spacerBar) * (layerT - 1)) / 2;

        dC =
            input.cover +
            (diaC[i2] + (diaC.last + spacerBar) * (layerC - 1)) / 2;
      } else {
        d =
            depth -
            input.cover -
            diaL[j] -
            (diaT[i1] + (diaT.last + spacerBar) * (layerT - 1)) / 2;

        dC =
            input.cover +
            diaL[j] +
            (diaC[i2] + (diaC.last + spacerBar) * (layerC - 1)) / 2;
      }

      k = getkValueRectangle(mD, width, d, input.fcu);
      kLimit = getkValueLimitRectangle(input.fcu);
      z = getLeverArmRectangle(k, kLimit, d);

      //* required steel
      asC = getRecBeamReqCompressionBar(mD, width, d, dC, input.fcu, fy: fy);
      asT = getRecBeamReqTensionBar(mD, width, d, dC, input.fcu, fy: fy);

      //* provided steel
      asTPro = getOneWaySlabSteelProvided(
        diaT,
        layerT,
        diaT[i1],
        sTPro,
        input.minS,
        width,
      );
      asCPro = getOneWaySlabSteelProvided(
        diaC,
        layerC,
        diaC[i2],
        sCPro,
        input.minS,
        width,
      );

      //* links
      vD_ = vD * pow(10, 3) / (width * d);
      vC_ = getConcreteShearStrength(asTPro, input.fcu, width, d);
      vR_ = getConcreteShearParams_vR(input.fcu);
      lReq = getRecSlabLinksRequired(vD, asTPro, input.fcu, width, d);

      if (lReq <= 0) {
        lPro = 0;
      } else {
        lPro = getRecSlabLinksProvided(diaL[j], sProX, sProY, width);
      }

      //* Deflection 
      maxDel = getMaxDeflectionSimpleBeamUDL(
        newSlsUDL,
        input.span,
        getElasticModulus(input.fcu),
        getSecondAreaMomentRectangle(width, depth),
        beamType: BeamType.SimplySupported,
      );
      maxDelLimit = input.span * pow(10, 3) / 250;


      // ********************************
      // Design validaiton in each loop
      // ********************************
      // concept of adjustmernt
      // Flexural: decrease spacing, increase bar size, then increase layer
      // Shear: increase legs nos (reduce spacing across), then decrease along spacing, and finally increase bar size.

      //! if the k-value exceeds preferred k-value/steel ratio, skip iteration
      if (k > input.mainKValue ||
          asTPro > input.mainSteelRatio * depth * width ||
          asCPro > input.mainSteelRatio * depth * width) {
        depth += 25;
        skipIteration = true;
      }

      // ! if the depth exceeds the max width, directly escape and declare the design failed
      if (depth > input.maxDepth) {
        status = status.map((element) => false).toList();
        depth -= 25;
        break;
      }

      //! Skip iteration if necessary
      if (skipIteration) {
        //reset diameters, layers, spacing, nos of rebars
        i1 = 0;
        i2 = 0;
        layerT = 1;
        layerC = 1;
        j = 0;
        sTPro = input.maxS;
        sCPro = input.maxS;
        sProX = input.maxS;
        sProY = input.maxS;
        skipIteration = false;
        continue;
      }

      //todo: check deflection
      
      if (maxDel > maxDelLimit) {
        reDesignDeflection = true;
        if (depth + 25 <= input.maxDepth) {
          depth += 25;
          //reset rebars and links arrangement
          i1 = 0;
          i2 = 0;
          layerT = 1;
          layerC = 1;
          sTPro = input.maxS;
          sCPro = input.maxS;

          j = 0;
          sProX = input.maxS;
          sProY = input.maxS;

          continue; // no need to run rest of design process
        } else {
          reDesignDeflection = false;
          status[3] = false;
        }
      } else {
        reDesignDeflection = false;
        status[3] = true;
      }
      // Todo: check tension bars
      if (asTPro < asT) {
        redesignacT = true;
        if (sTPro - 25 >= input.minS) {
          sTPro -= 25;
        } else if ((sTPro == input.minS) && (i1 + 1 <= diaT.length - 1)) {
          sTPro = input.maxS;
          i1 += 1;
        } else if ((sTPro == input.minS) &&
            (i1 == diaT.length - 1) &&
            (layerT + 1 <= input.maxLayers)) {
          sTPro = input.maxS;
          i1 = 0;
          layerT += 1;
        } else if ((sTPro == input.minS) &&
            (i1 == diaT.length - 1) &&
            (layerT == input.maxLayers) &&
            (depth + 25 <= input.maxDepth)) {
          depth += 25;
          //reset rebars and links arrangement
          i1 = 0;
          i2 = 0;
          layerT = 1;
          layerC = 1;
          sTPro = input.maxS;
          sCPro = input.maxS;

          j = 0;
          sProX = input.maxS;
          sProY = input.maxS;

          continue; // no need to run rest of design process
        } else {
          redesignacT = false;
        status[0] = false;
        }
      } else {
        redesignacT = false;
        status[0] = true;
      }

      // todo:  check compression bars
      if (asCPro < asC) {
        redesignacC = true;
        if (sCPro - 25 >= input.minS) {
          sCPro -= 25;
        } else if ((sCPro == input.minS) && (i2 + 1 <= diaC.length - 1)) {
          sCPro = input.maxS;
          i2 += 1;
        } else if ((sCPro == input.minS) &&
            (i2 == diaC.length - 1) &&
            (layerC + 1 <= input.maxLayers)) {
          sCPro = input.maxS;
          i2 = 0;
          layerC += 1;
        } else if (sCPro == input.minS &&
            i2 == diaT.length - 1 &&
            layerC == input.maxLayers &&
            depth + 25 <= input.maxDepth) {
          depth += 25;
          //reset rebars and links arrangement
          i1 = 0;
          i2 = 0;
          layerT = 1;
          layerC = 1;
          sTPro = input.maxS;
          sCPro = input.maxS;

          j = 0;
          sProX = input.maxS;
          sProY = input.maxS;
          continue; // no need to run rest of design process
        } else {
          redesignacC = false;
          status[1] = false;
        }
      } else {
        // there could be situation links pass, rebar not
        // then the width and effective depth keeps change
        // then req'd links keep reducing while provided links kept at high amount
        // Therefore, we redesign in case the provided links are 20% more than required

        if (asCPro > asC * 1.2 &&
            compSteelOptimizationSteps < compSteelOptimizationStepsLimit) {
          compSteelOptimizationSteps += 1;
          reDesignLinks = true;
          sCPro = input.maxS;
          i2 = 0;
        } else {
          redesignacC = false;
          status[1] = true;
        }
      }

      //todo: check shear
      if (lPro < lReq) {
        reDesignLinks = true;
        if (sProX - 25 >= input.minS) {
          sProX -= 25;
        } else if (sProX == input.minS && sProY - 25 >= input.minS) {
          sProX = input.maxS;
          sProY -= 25;
        } else if (sProX == input.minS &&
            sProY == input.minS &&
            j + 1 <= diaL.length - 1) {
          sProX = input.maxS;
          sProY = input.maxS;
          j += 1;
        } else if (sProX == input.minS &&
            sProY == input.minS &&
            j == diaL.length - 1 &&
            depth + 25 <= input.maxDepth) {
          depth += 25;
          //reset rebars and links arrangement
          i1 = 0;
          i2 = 0;
          layerT = 1;
          layerC = 1;
          sTPro = input.maxS;
          sCPro = input.maxS;

          j = 0;
          sProX = input.maxS;
          sProY = input.maxS;

          continue; // no need to run rest of design process
        } else {
          reDesignLinks = false;
          status[2] = false;
        }
      } else {
        // there could be situation links pass, rebar not
        // then the width and effective depth keeps change
        // then req'd links keep reducing while provided links kept at high amount
        // Therefore, we redesign in case the provided links are 20% more than required

        if (lPro > lReq * 1.2 &&
            linksOptimizationSteps < linksOptimizationStepsLimit) {
          linksOptimizationSteps += 1;
          reDesignLinks = true;
          sProX = input.maxS;
          sProY = input.maxS;
          j = 0;
        } else {
          reDesignLinks = false;
          status[2] = true;
        }
      }
      shouldRedesign =
          redesignacT || redesignacC || reDesignLinks || reDesignDeflection;
    }

    // ********************************
    // Unify the spacing and steel provided
    // ********************************
    // across section, the link, tension bar, and compression bar
    // spacing should be the same for constructability
    sPro = min(sProX, min(sTPro, sCPro));
    if (lPro > 0) {
      if (sPro == sProX) {
        sTPro = sPro;
        sCPro = sPro;
      } else {
        if (sProX % sTPro != 0 || sProX % sCPro != 0) {
          sProX = sPro;
          sCPro = sPro;
          sTPro = sPro;
        }
      }
    }

    // re-calcualte the provided steel
    asTPro = getOneWaySlabSteelProvided(
      diaT,
      layerT,
      diaT[i1],
      sTPro,
      input.minS,
      width,
    );
    asCPro = getOneWaySlabSteelProvided(
      diaC,
      layerC,
      diaC[i2],
      sCPro,
      input.minS,
      width,
    );

    if (lReq <= 0) {
      lPro = 0;
    } else {
      lPro = getRecSlabLinksProvided(diaL[j], sProX, sProY, width);
    }

    //* recalculate the concrete shear strength
    vC_ = getConcreteShearStrength(asTPro, input.fcu, width, d);

    // ********************************
    //* Return Design
    // ********************************
    // Tension Bar
    asTProText = getOneWaySlabSteelProvidedText(
      diaT,
      layerT,
      diaT[i1],
      sTPro,
      input.minS,
    );
    if (!status[0]) {
      asTProText = '(Fail) ${asTProText}';
    }

    // Compression Bar
    asCProText = getOneWaySlabSteelProvidedText(
      diaC,
      layerC,
      diaC[i2],
      sCPro,
      input.minS,
    );
    if (!status[1]) {
      asCProText = '(Fail) ${asCProText}';
    }

    // Links
    lProText = getRecSlabLinksProvidedText(diaL[j], sProX, sProY);
    if (status[2]) {
      if (lPro == 0) {
        lProText = 'N.A.';
      }
    } else {
      lProText = '(Fail) $lProText';
    }

    //* record the cals result
    _recordCalsResult(
      buffer,
      input,
      globalData,
      ulsUDL: newUlsUDL,
      slsUDL: newSlsUDL,
      k: k,
      s_t_pro: sTPro,
      s_c_pro: sCPro,
      As_t_pro: asTPro,
      As_t: asT,
      As_c_pro: asCPro,
      As_c: asC,
      M_d: mD,
      V_d: vD,
      depth: depth,
      layer_t: layerT,
      layer_c: layerC,
      dia_t: diaT[i1],
      dia_c: diaC[i2],
      dia_l: diaL[j],
      d: d,
      d_c: dC,
      z: z,
      As_t_pro_text: asTProText,
      As_c_pro_text: asCProText,
      l_pro_text: lProText,
      l_pro: lPro,
      l_req: lReq,
      v_d: vD_,
      v_c: vC_,
      v_r: vR_,
      s_pro_x: sProX,
      s_pro_y: sProY,
      s_min: sMin,
      s_max: sMax,
      maxDeflection: maxDel,
      deflectionLimit: maxDelLimit,
      status: status,
    );

    //* if sth fails, we add warning at the beginning
    if (RegExp(r'fail', caseSensitive: false).hasMatch(buffer.toString())) {
      buffer = _addWarningHeader(buffer);
    }

    // ! Finally assign the result
    loadingTable = loadingTables.firstWhere((tbl) => tbl.usage == input.usage);
    return SlabSchemeData(
      usage: input.usage,
      finish: loadingTable.finish,
      service: loadingTable.service,
      liveLoad: loadingTable.liveLoad,
      loadingTableId: loadingTable.loadingTableId,
      span: input.span,
      strZone: depth,
      fcu: input.fcu,
      cover: input.cover,
      mainTopBar: asCProText,
      mainBottomBar: asTProText,
      mainLinks: lProText,
      slabSchemeId: 'temp',
      calsLog: buffer.toString(),
      isSelected: false,
    );
  }

  StringBuffer _addWarningHeader(StringBuffer buffer) {
    final newBuffer = StringBuffer();
    newBuffer.write('\n-----------[!Start of Warning!]-------------\n');
    newBuffer.write(' Result not reliable. Something fails\n');
    newBuffer.write('-----------[!End of Warning!]-------------\n');
    newBuffer.write(buffer.toString());
    buffer = newBuffer;
    return buffer;
  }

  void _recordCalsResult(
    StringBuffer buffer,
    SlabSchemeInput input,
    GlobalData globalData, {
    double ulsUDL = 0.0,
    double slsUDL = 0.0,
    double k = 0.0,
    int s_t_pro = 0,
    int s_c_pro = 0,
    double As_t_pro = 0.0,
    double As_t = 0.0,
    double As_c_pro = 0.0,
    double As_c = 0.0,
    double M_d = 0.0,
    double V_d = 0.0,
    double depth = 0.0,
    int layer_t = 0,
    int layer_c = 0,
    int dia_t = 0,
    int dia_c = 0,
    int dia_l = 0,
    double d = 0.0,
    double d_c = 0.0,
    double z = 0.0,
    String As_t_pro_text = '',
    String As_c_pro_text = '',
    String l_pro_text = '',
    double l_pro = 0.0,
    double l_req = 0.0,
    double v_d = 0.0,
    double v_c = 0.0,
    double v_r = 0.0,
    int s_pro_x = 0,
    int s_pro_y = 0,
    int s_min = 0,
    int s_max = 0,
    double maxDeflection = 0.0,
    double deflectionLimit = 0.0,
    List<bool> status = const [],
  }) {
    final x = globalData.unit;
    late final List<String> unit;
    late final double sdl;
    late final double ll;
    late final double slsLoad;
    late final double ulsLoad;

    NumberFormat f0 = NumberFormat('0'),
        f1 = NumberFormat('0.0'),
        f3 = NumberFormat('0.000');
    switch (x) {
      case 'metrics':
        unit = PreferredUnit.metrics;
        break;
      case 'imperial':
        unit = PreferredUnit.imperial;
        break;
      default:
        unit = PreferredUnit.metrics;
        break;
    }
    buffer.clear();

    buffer.write('Span: ${f1.format(input.span)} [${unit[3]}] | ');

    //get loading
    final data = ref.read(loadingTablesControllerProvider);
    data.when(
      data: (tables) async {
        final result = tables.firstWhere((tbl) => tbl.usage == input.usage);
        sdl =
            getPressureFromThick(result.finish, globalData.finishUnitWeight) +
            result.service;
        ll = result.liveLoad;
        buffer.write('SDL: ${f1.format(sdl)} [${unit[1]}] | ');
        buffer.write('LL: ${f1.format(ll)} [${unit[1]}] | ');
        buffer.write('SLS: ${f3.format(slsUDL)} [${unit[1]}] | ');
        buffer.write('ULS: ${f3.format(ulsUDL)} [${unit[1]}]\n');
      },
      error: (error, stackTrace) {},
      loading: () {},
    );

    buffer.write('Slab Thickness: ${f1.format(depth)} [${unit[4]}] | ');
    buffer.write('Cover: ${f1.format(input.cover)} [${unit[4]}]\n');

    buffer.write('M: ${f0.format(M_d)} [${unit[2]}/${unit[3]}] | ');
    buffer.write('V: ${f0.format(V_d)} [${unit[0]}/${unit[3]}] | ');
    buffer.write('depth: $depth [${unit[4]}] | ');
    buffer.write('width: 1000 [${unit[4]}]\n');

    buffer.write('k: ${f3.format(k)} | ');
    buffer.write('k_limit: ${f3.format(input.mainKValue)} | ');
    buffer.write('preferred k: ${f3.format(input.mainKValue)}\n');

    buffer.write('v_d: ${f3.format(v_d)} [${unit[5]}] | ');
    buffer.write('v_c: ${f3.format(v_c)} [${unit[5]}] | ');
    buffer.write('v_r: ${f3.format(v_r)} [${unit[5]}]\n');

    buffer.write('dia_t: ${f0.format(dia_t)} [${unit[4]}] | ');
    buffer.write('dia_c: ${f0.format(dia_c)} [${unit[4]}] | ');
    if (l_pro_text != 'N.A.') {
      buffer.write('dia_l: ${f0.format(dia_l)} [${unit[4]}]\n');
    } else {
      buffer.write('dia_l: N.A.\n');
    }

    buffer.write('s_t_pro: ${f0.format(s_t_pro)} [${unit[4]}] | ');
    buffer.write('s_c_pro: ${f0.format(s_c_pro)} [${unit[4]}] | ');

    if (l_pro_text != 'N.A.') {
      buffer.write('s_pro_x: ${f0.format(s_pro_x)} [${unit[4]}] | ');
      buffer.write('s_pro_y: ${f0.format(s_pro_y)} [${unit[4]}]\n');
    } else {
      buffer.write('s_pro_x: N.A. | ');
      buffer.write('s_pro_y: N.A.\n');
    }

    buffer.write('s_min: ${f0.format(s_min)} [${unit[4]}] | ');
    buffer.write('s_max: ${f0.format(s_max)} [${unit[4]}]\n');

    buffer.write('layer_t: ${f0.format(layer_t)} | ');
    buffer.write('layer_c: ${f0.format(layer_c)} | ');
    buffer.write('d: ${f0.format(d)} [${unit[4]}] | ');
    buffer.write('d_c: ${f0.format(d_c)} [${unit[4]}] | ');
    buffer.write('z: ${f0.format(z)} [${unit[4]}]\n');

    buffer.write('As_t_pro: ${f0.format(As_t_pro)} [${unit[6]}/${unit[3]}] | ');
    buffer.write('As_t: ${f0.format(As_t)} [${unit[6]}/${unit[3]}]| ');
    buffer.write(
      'As_t limit: ${f0.format((input.mainSteelRatio) * (1000) * (depth))} [${unit[6]}/${unit[3]}]\n',
    );
    buffer.write('As_c_pro: ${f0.format(As_c_pro)} [${unit[6]}/${unit[3]}] | ');
    buffer.write('As_c: ${f0.format(As_c)} [${unit[6]}/${unit[3]}]| ');
    buffer.write(
      'As_c limit: ${f0.format((input.mainSteelRatio) * (1000) * (depth))} [${unit[6]}/${unit[3]}]\n',
    );

    buffer.write('l_pro: ${f3.format(l_pro)} [${unit[6]}/${unit[4]}] | ');
    buffer.write('l_req: ${f3.format(l_req)} [${unit[6]}/${unit[4]}]\n');

    buffer.write('Max Deflection: ${f3.format(maxDeflection)} [${unit[4]}] | ');
    buffer.write(
      'Deflection Limit: ${f3.format(deflectionLimit)} [${unit[4]}]\n',
    );


    buffer.write('As_t_pro Designation: $As_t_pro_text\n');
    buffer.write('As_c_pro Designation: $As_c_pro_text\n');
    buffer.write('l_pro Designation: $l_pro_text\n');

    if (status[0]) {
      buffer.write('Tension bars: pass | ');
    } else {
      buffer.write('Tension bars: fail | ');
    }
    if (status[1]) {
      buffer.write('Compression bars: pass | ');
    } else {
      buffer.write('Compression bars: fail | ');
    }
    if (status[2]) {
      buffer.write('Links: pass\n');
    } else {
      buffer.write('Links: fail\n');
    }
    if (status[3]) {
      buffer.write('Deflection: pass\n');
    } else {
      buffer.write('Deflection: fail\n');
    }
  }
}
