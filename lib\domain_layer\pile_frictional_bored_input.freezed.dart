// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'pile_frictional_bored_input.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$PileFrictionalBoredInput {

 double get sptNValue; double get soilUnitWeight; double get kTan; double get fos; double get fcu; double get maxPileLength; double get maxPileDiameter; double get ratioOfBelloutDia; double get maxSteelRatio; double get slsLoad; double get ulsLoad; double get diaIncrement; bool get useSelectColLoad; String get id;
/// Create a copy of PileFrictionalBoredInput
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$PileFrictionalBoredInputCopyWith<PileFrictionalBoredInput> get copyWith => _$PileFrictionalBoredInputCopyWithImpl<PileFrictionalBoredInput>(this as PileFrictionalBoredInput, _$identity);

  /// Serializes this PileFrictionalBoredInput to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is PileFrictionalBoredInput&&(identical(other.sptNValue, sptNValue) || other.sptNValue == sptNValue)&&(identical(other.soilUnitWeight, soilUnitWeight) || other.soilUnitWeight == soilUnitWeight)&&(identical(other.kTan, kTan) || other.kTan == kTan)&&(identical(other.fos, fos) || other.fos == fos)&&(identical(other.fcu, fcu) || other.fcu == fcu)&&(identical(other.maxPileLength, maxPileLength) || other.maxPileLength == maxPileLength)&&(identical(other.maxPileDiameter, maxPileDiameter) || other.maxPileDiameter == maxPileDiameter)&&(identical(other.ratioOfBelloutDia, ratioOfBelloutDia) || other.ratioOfBelloutDia == ratioOfBelloutDia)&&(identical(other.maxSteelRatio, maxSteelRatio) || other.maxSteelRatio == maxSteelRatio)&&(identical(other.slsLoad, slsLoad) || other.slsLoad == slsLoad)&&(identical(other.ulsLoad, ulsLoad) || other.ulsLoad == ulsLoad)&&(identical(other.diaIncrement, diaIncrement) || other.diaIncrement == diaIncrement)&&(identical(other.useSelectColLoad, useSelectColLoad) || other.useSelectColLoad == useSelectColLoad)&&(identical(other.id, id) || other.id == id));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,sptNValue,soilUnitWeight,kTan,fos,fcu,maxPileLength,maxPileDiameter,ratioOfBelloutDia,maxSteelRatio,slsLoad,ulsLoad,diaIncrement,useSelectColLoad,id);

@override
String toString() {
  return 'PileFrictionalBoredInput(sptNValue: $sptNValue, soilUnitWeight: $soilUnitWeight, kTan: $kTan, fos: $fos, fcu: $fcu, maxPileLength: $maxPileLength, maxPileDiameter: $maxPileDiameter, ratioOfBelloutDia: $ratioOfBelloutDia, maxSteelRatio: $maxSteelRatio, slsLoad: $slsLoad, ulsLoad: $ulsLoad, diaIncrement: $diaIncrement, useSelectColLoad: $useSelectColLoad, id: $id)';
}


}

/// @nodoc
abstract mixin class $PileFrictionalBoredInputCopyWith<$Res>  {
  factory $PileFrictionalBoredInputCopyWith(PileFrictionalBoredInput value, $Res Function(PileFrictionalBoredInput) _then) = _$PileFrictionalBoredInputCopyWithImpl;
@useResult
$Res call({
 double sptNValue, double soilUnitWeight, double kTan, double fos, double fcu, double maxPileLength, double maxPileDiameter, double ratioOfBelloutDia, double maxSteelRatio, double slsLoad, double ulsLoad, double diaIncrement, bool useSelectColLoad, String id
});




}
/// @nodoc
class _$PileFrictionalBoredInputCopyWithImpl<$Res>
    implements $PileFrictionalBoredInputCopyWith<$Res> {
  _$PileFrictionalBoredInputCopyWithImpl(this._self, this._then);

  final PileFrictionalBoredInput _self;
  final $Res Function(PileFrictionalBoredInput) _then;

/// Create a copy of PileFrictionalBoredInput
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? sptNValue = null,Object? soilUnitWeight = null,Object? kTan = null,Object? fos = null,Object? fcu = null,Object? maxPileLength = null,Object? maxPileDiameter = null,Object? ratioOfBelloutDia = null,Object? maxSteelRatio = null,Object? slsLoad = null,Object? ulsLoad = null,Object? diaIncrement = null,Object? useSelectColLoad = null,Object? id = null,}) {
  return _then(_self.copyWith(
sptNValue: null == sptNValue ? _self.sptNValue : sptNValue // ignore: cast_nullable_to_non_nullable
as double,soilUnitWeight: null == soilUnitWeight ? _self.soilUnitWeight : soilUnitWeight // ignore: cast_nullable_to_non_nullable
as double,kTan: null == kTan ? _self.kTan : kTan // ignore: cast_nullable_to_non_nullable
as double,fos: null == fos ? _self.fos : fos // ignore: cast_nullable_to_non_nullable
as double,fcu: null == fcu ? _self.fcu : fcu // ignore: cast_nullable_to_non_nullable
as double,maxPileLength: null == maxPileLength ? _self.maxPileLength : maxPileLength // ignore: cast_nullable_to_non_nullable
as double,maxPileDiameter: null == maxPileDiameter ? _self.maxPileDiameter : maxPileDiameter // ignore: cast_nullable_to_non_nullable
as double,ratioOfBelloutDia: null == ratioOfBelloutDia ? _self.ratioOfBelloutDia : ratioOfBelloutDia // ignore: cast_nullable_to_non_nullable
as double,maxSteelRatio: null == maxSteelRatio ? _self.maxSteelRatio : maxSteelRatio // ignore: cast_nullable_to_non_nullable
as double,slsLoad: null == slsLoad ? _self.slsLoad : slsLoad // ignore: cast_nullable_to_non_nullable
as double,ulsLoad: null == ulsLoad ? _self.ulsLoad : ulsLoad // ignore: cast_nullable_to_non_nullable
as double,diaIncrement: null == diaIncrement ? _self.diaIncrement : diaIncrement // ignore: cast_nullable_to_non_nullable
as double,useSelectColLoad: null == useSelectColLoad ? _self.useSelectColLoad : useSelectColLoad // ignore: cast_nullable_to_non_nullable
as bool,id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,
  ));
}

}


/// Adds pattern-matching-related methods to [PileFrictionalBoredInput].
extension PileFrictionalBoredInputPatterns on PileFrictionalBoredInput {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _PileFrictionalBoredInput value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _PileFrictionalBoredInput() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _PileFrictionalBoredInput value)  $default,){
final _that = this;
switch (_that) {
case _PileFrictionalBoredInput():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _PileFrictionalBoredInput value)?  $default,){
final _that = this;
switch (_that) {
case _PileFrictionalBoredInput() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( double sptNValue,  double soilUnitWeight,  double kTan,  double fos,  double fcu,  double maxPileLength,  double maxPileDiameter,  double ratioOfBelloutDia,  double maxSteelRatio,  double slsLoad,  double ulsLoad,  double diaIncrement,  bool useSelectColLoad,  String id)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _PileFrictionalBoredInput() when $default != null:
return $default(_that.sptNValue,_that.soilUnitWeight,_that.kTan,_that.fos,_that.fcu,_that.maxPileLength,_that.maxPileDiameter,_that.ratioOfBelloutDia,_that.maxSteelRatio,_that.slsLoad,_that.ulsLoad,_that.diaIncrement,_that.useSelectColLoad,_that.id);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( double sptNValue,  double soilUnitWeight,  double kTan,  double fos,  double fcu,  double maxPileLength,  double maxPileDiameter,  double ratioOfBelloutDia,  double maxSteelRatio,  double slsLoad,  double ulsLoad,  double diaIncrement,  bool useSelectColLoad,  String id)  $default,) {final _that = this;
switch (_that) {
case _PileFrictionalBoredInput():
return $default(_that.sptNValue,_that.soilUnitWeight,_that.kTan,_that.fos,_that.fcu,_that.maxPileLength,_that.maxPileDiameter,_that.ratioOfBelloutDia,_that.maxSteelRatio,_that.slsLoad,_that.ulsLoad,_that.diaIncrement,_that.useSelectColLoad,_that.id);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( double sptNValue,  double soilUnitWeight,  double kTan,  double fos,  double fcu,  double maxPileLength,  double maxPileDiameter,  double ratioOfBelloutDia,  double maxSteelRatio,  double slsLoad,  double ulsLoad,  double diaIncrement,  bool useSelectColLoad,  String id)?  $default,) {final _that = this;
switch (_that) {
case _PileFrictionalBoredInput() when $default != null:
return $default(_that.sptNValue,_that.soilUnitWeight,_that.kTan,_that.fos,_that.fcu,_that.maxPileLength,_that.maxPileDiameter,_that.ratioOfBelloutDia,_that.maxSteelRatio,_that.slsLoad,_that.ulsLoad,_that.diaIncrement,_that.useSelectColLoad,_that.id);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _PileFrictionalBoredInput extends PileFrictionalBoredInput {
   _PileFrictionalBoredInput({this.sptNValue = 50, this.soilUnitWeight = 19, this.kTan = 0.25, this.fos = 3, this.fcu = 45, this.maxPileLength = 30, this.maxPileDiameter = 2000, this.ratioOfBelloutDia = 1.65, this.maxSteelRatio = 0.04, this.slsLoad = 1000, this.ulsLoad = 2000, this.diaIncrement = 200, this.useSelectColLoad = false, this.id = '1'}): super._();
  factory _PileFrictionalBoredInput.fromJson(Map<String, dynamic> json) => _$PileFrictionalBoredInputFromJson(json);

@override@JsonKey() final  double sptNValue;
@override@JsonKey() final  double soilUnitWeight;
@override@JsonKey() final  double kTan;
@override@JsonKey() final  double fos;
@override@JsonKey() final  double fcu;
@override@JsonKey() final  double maxPileLength;
@override@JsonKey() final  double maxPileDiameter;
@override@JsonKey() final  double ratioOfBelloutDia;
@override@JsonKey() final  double maxSteelRatio;
@override@JsonKey() final  double slsLoad;
@override@JsonKey() final  double ulsLoad;
@override@JsonKey() final  double diaIncrement;
@override@JsonKey() final  bool useSelectColLoad;
@override@JsonKey() final  String id;

/// Create a copy of PileFrictionalBoredInput
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$PileFrictionalBoredInputCopyWith<_PileFrictionalBoredInput> get copyWith => __$PileFrictionalBoredInputCopyWithImpl<_PileFrictionalBoredInput>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$PileFrictionalBoredInputToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _PileFrictionalBoredInput&&(identical(other.sptNValue, sptNValue) || other.sptNValue == sptNValue)&&(identical(other.soilUnitWeight, soilUnitWeight) || other.soilUnitWeight == soilUnitWeight)&&(identical(other.kTan, kTan) || other.kTan == kTan)&&(identical(other.fos, fos) || other.fos == fos)&&(identical(other.fcu, fcu) || other.fcu == fcu)&&(identical(other.maxPileLength, maxPileLength) || other.maxPileLength == maxPileLength)&&(identical(other.maxPileDiameter, maxPileDiameter) || other.maxPileDiameter == maxPileDiameter)&&(identical(other.ratioOfBelloutDia, ratioOfBelloutDia) || other.ratioOfBelloutDia == ratioOfBelloutDia)&&(identical(other.maxSteelRatio, maxSteelRatio) || other.maxSteelRatio == maxSteelRatio)&&(identical(other.slsLoad, slsLoad) || other.slsLoad == slsLoad)&&(identical(other.ulsLoad, ulsLoad) || other.ulsLoad == ulsLoad)&&(identical(other.diaIncrement, diaIncrement) || other.diaIncrement == diaIncrement)&&(identical(other.useSelectColLoad, useSelectColLoad) || other.useSelectColLoad == useSelectColLoad)&&(identical(other.id, id) || other.id == id));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,sptNValue,soilUnitWeight,kTan,fos,fcu,maxPileLength,maxPileDiameter,ratioOfBelloutDia,maxSteelRatio,slsLoad,ulsLoad,diaIncrement,useSelectColLoad,id);

@override
String toString() {
  return 'PileFrictionalBoredInput(sptNValue: $sptNValue, soilUnitWeight: $soilUnitWeight, kTan: $kTan, fos: $fos, fcu: $fcu, maxPileLength: $maxPileLength, maxPileDiameter: $maxPileDiameter, ratioOfBelloutDia: $ratioOfBelloutDia, maxSteelRatio: $maxSteelRatio, slsLoad: $slsLoad, ulsLoad: $ulsLoad, diaIncrement: $diaIncrement, useSelectColLoad: $useSelectColLoad, id: $id)';
}


}

/// @nodoc
abstract mixin class _$PileFrictionalBoredInputCopyWith<$Res> implements $PileFrictionalBoredInputCopyWith<$Res> {
  factory _$PileFrictionalBoredInputCopyWith(_PileFrictionalBoredInput value, $Res Function(_PileFrictionalBoredInput) _then) = __$PileFrictionalBoredInputCopyWithImpl;
@override @useResult
$Res call({
 double sptNValue, double soilUnitWeight, double kTan, double fos, double fcu, double maxPileLength, double maxPileDiameter, double ratioOfBelloutDia, double maxSteelRatio, double slsLoad, double ulsLoad, double diaIncrement, bool useSelectColLoad, String id
});




}
/// @nodoc
class __$PileFrictionalBoredInputCopyWithImpl<$Res>
    implements _$PileFrictionalBoredInputCopyWith<$Res> {
  __$PileFrictionalBoredInputCopyWithImpl(this._self, this._then);

  final _PileFrictionalBoredInput _self;
  final $Res Function(_PileFrictionalBoredInput) _then;

/// Create a copy of PileFrictionalBoredInput
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? sptNValue = null,Object? soilUnitWeight = null,Object? kTan = null,Object? fos = null,Object? fcu = null,Object? maxPileLength = null,Object? maxPileDiameter = null,Object? ratioOfBelloutDia = null,Object? maxSteelRatio = null,Object? slsLoad = null,Object? ulsLoad = null,Object? diaIncrement = null,Object? useSelectColLoad = null,Object? id = null,}) {
  return _then(_PileFrictionalBoredInput(
sptNValue: null == sptNValue ? _self.sptNValue : sptNValue // ignore: cast_nullable_to_non_nullable
as double,soilUnitWeight: null == soilUnitWeight ? _self.soilUnitWeight : soilUnitWeight // ignore: cast_nullable_to_non_nullable
as double,kTan: null == kTan ? _self.kTan : kTan // ignore: cast_nullable_to_non_nullable
as double,fos: null == fos ? _self.fos : fos // ignore: cast_nullable_to_non_nullable
as double,fcu: null == fcu ? _self.fcu : fcu // ignore: cast_nullable_to_non_nullable
as double,maxPileLength: null == maxPileLength ? _self.maxPileLength : maxPileLength // ignore: cast_nullable_to_non_nullable
as double,maxPileDiameter: null == maxPileDiameter ? _self.maxPileDiameter : maxPileDiameter // ignore: cast_nullable_to_non_nullable
as double,ratioOfBelloutDia: null == ratioOfBelloutDia ? _self.ratioOfBelloutDia : ratioOfBelloutDia // ignore: cast_nullable_to_non_nullable
as double,maxSteelRatio: null == maxSteelRatio ? _self.maxSteelRatio : maxSteelRatio // ignore: cast_nullable_to_non_nullable
as double,slsLoad: null == slsLoad ? _self.slsLoad : slsLoad // ignore: cast_nullable_to_non_nullable
as double,ulsLoad: null == ulsLoad ? _self.ulsLoad : ulsLoad // ignore: cast_nullable_to_non_nullable
as double,diaIncrement: null == diaIncrement ? _self.diaIncrement : diaIncrement // ignore: cast_nullable_to_non_nullable
as double,useSelectColLoad: null == useSelectColLoad ? _self.useSelectColLoad : useSelectColLoad // ignore: cast_nullable_to_non_nullable
as bool,id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

// dart format on
