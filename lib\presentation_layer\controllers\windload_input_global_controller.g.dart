// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'windload_input_global_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$windLoadInputGlobalControllerHash() =>
    r'61df45b772214cffd174abdadb15bed9b8fadba8';

/// See also [WindLoadInputGlobalController].
@ProviderFor(WindLoadInputGlobalController)
final windLoadInputGlobalControllerProvider = AutoDisposeAsyncNotifierProvider<
  WindLoadInputGlobalController,
  WindLoadGlobal
>.internal(
  WindLoadInputGlobalController.new,
  name: r'windLoadInputGlobalControllerProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$windLoadInputGlobalControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$WindLoadInputGlobalController =
    AutoDisposeAsyncNotifier<WindLoadGlobal>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
