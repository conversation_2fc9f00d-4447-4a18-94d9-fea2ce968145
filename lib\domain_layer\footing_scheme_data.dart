import 'package:freezed_annotation/freezed_annotation.dart';
// import 'package:flutter/foundation.dart';
part 'footing_scheme_data.freezed.dart';
part 'footing_scheme_data.g.dart';

@freezed
abstract class FootingSchemeData with _$FootingSchemeData {
  const FootingSchemeData._();
  factory FootingSchemeData({
    @Default(5.0) double size,
    @Default(500.0) double strZone,
    @Default(45.0) double fcu,
    @Default(35.0) double cover,
    @Default('Soil') String groundType,
    @Default(50) double soilNValue,
    @Default(0) double footingTopLevel,
    @Default(-3) double waterTableLevel,
    @Default(1000) double rockCapacity,
    @Default('Sqaure') String colShape,
    @Default(1000.0) double columnSize,
    @Default(2000.0) double slsLoad,
    @Default(3000.0) double ulsLoad,
    @Default('') String mainTopBar,
    @Default('') String mainBottomBar,
    @Default('') String mainLinks,
    @Default('') String punchingLinks,
    @Default('') String calsLog,
    @Default(false) bool isSelected,
    @Default('1') String footingSchemeDataId,
  }) = _FootingSchemeData; 

  factory FootingSchemeData.fromJson(Map<String, Object?> json) =>
      _$FootingSchemeDataFromJson(json);
}
