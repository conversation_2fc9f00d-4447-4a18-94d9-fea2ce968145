// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'basement_wall_scheme_data_controller .dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$basementWallSchemeDataControllerHash() =>
    r'd94a70a651aae2a4735e9665d588081bf7308701';

/// See also [BasementWallSchemeDataController].
@ProviderFor(BasementWallSchemeDataController)
final basementWallSchemeDataControllerProvider =
    AutoDisposeAsyncNotifierProvider<
      BasementWallSchemeDataController,
      BasementWallSchemeData
    >.internal(
      BasementWallSchemeDataController.new,
      name: r'basementWallSchemeDataControllerProvider',
      debugGetCreateSourceHash:
          const bool.fromEnvironment('dart.vm.product')
              ? null
              : _$basementWallSchemeDataControllerHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$BasementWallSchemeDataController =
    AutoDisposeAsyncNotifier<BasementWallSchemeData>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
