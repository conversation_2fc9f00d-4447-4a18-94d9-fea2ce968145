// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'pile_driven_data_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$pileDrivenDataControllerHash() =>
    r'69e2f4963c004b336650e75b598e11c5c40ec562';

/// See also [PileDrivenDataController].
@ProviderFor(PileDrivenDataController)
final pileDrivenDataControllerProvider = AutoDisposeAsyncNotifierProvider<
  PileDrivenDataController,
  List<PileDrivenData>
>.internal(
  PileDrivenDataController.new,
  name: r'pileDrivenDataControllerProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$pileDrivenDataControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$PileDrivenDataController =
    AutoDisposeAsyncNotifier<List<PileDrivenData>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
