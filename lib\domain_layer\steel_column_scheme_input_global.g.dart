// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'steel_column_scheme_input_global.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_SteelColumnSchemeInputGlobal _$SteelColumnSchemeInputGlobalFromJson(
  Map<String, dynamic> json,
) => _SteelColumnSchemeInputGlobal(
  id: json['id'] as String? ?? '1',
  steelGrade: json['steelGrade'] as String? ?? 'S355',
  unbracedLength: (json['unbracedLength'] as num?)?.toDouble() ?? 5.0,
);

Map<String, dynamic> _$SteelColumnSchemeInputGlobalToJson(
  _SteelColumnSchemeInputGlobal instance,
) => <String, dynamic>{
  'id': instance.id,
  'steelGrade': instance.steelGrade,
  'unbracedLength': instance.unbracedLength,
};
