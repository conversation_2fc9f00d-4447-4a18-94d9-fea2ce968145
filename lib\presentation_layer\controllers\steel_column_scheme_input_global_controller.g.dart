// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'steel_column_scheme_input_global_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$steelColumnSchemeInputGlobalControllerHash() =>
    r'3c8024fe026ea69eacf52521ec1e0bcabfa97fb4';

/// See also [SteelColumnSchemeInputGlobalController].
@ProviderFor(SteelColumnSchemeInputGlobalController)
final steelColumnSchemeInputGlobalControllerProvider =
    AutoDisposeAsyncNotifierProvider<
      SteelColumnSchemeInputGlobalController,
      SteelColumnSchemeInputGlobal
    >.internal(
      SteelColumnSchemeInputGlobalController.new,
      name: r'steelColumnSchemeInputGlobalControllerProvider',
      debugGetCreateSourceHash:
          const bool.fromEnvironment('dart.vm.product')
              ? null
              : _$steelColumnSchemeInputGlobalControllerHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$SteelColumnSchemeInputGlobalController =
    AutoDisposeAsyncNotifier<SteelColumnSchemeInputGlobal>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
