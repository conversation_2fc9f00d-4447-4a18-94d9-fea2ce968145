import 'package:freezed_annotation/freezed_annotation.dart';
// import 'package:flutter/foundation.dart';
part 'transfer_beam_scheme_input.freezed.dart';
part 'transfer_beam_scheme_input.g.dart';

@freezed
abstract class TransferBeamSchemeInput with _$TransferBeamSchemeInput {
  const TransferBeamSchemeInput._();
  factory TransferBeamSchemeInput({
    @Default(0.0) double pointLoad,
    @Default(3.0) double distA,
    @Default(false) bool loadFromSelectedCol,
    @Default('')
    String
    transferBeamSchemeInputId, //will be overriden  as soon as new instance created
  }) = _TransferBeamSchemeInput;

  factory TransferBeamSchemeInput.fromJson(Map<String, Object?> json) =>
      _$TransferBeamSchemeInputFromJson(json);
}
