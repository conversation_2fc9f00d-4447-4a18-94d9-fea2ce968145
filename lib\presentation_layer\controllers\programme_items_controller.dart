import 'dart:math';

import 'package:flutter/material.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:nanoid2/nanoid2.dart';
import 'package:structify/domain_layer/programme_item.dart';
import 'package:structify/presentation_layer/small_elements/popup/custom_popup.dart';

//presentation layer
import '../screen/homescreen.dart';

// domain layer
part 'programme_items_controller.g.dart';

@riverpod
class ProgrammeItemsController extends _$ProgrammeItemsController {
  @override
  FutureOr<List<ProgrammeItem>> build() async {
    // print('Build: Loading Tables');
    final programmeItems =
        await ref
            .read(appDatabaseControllerProvider.notifier)
            .queryAllProgrammeItems();

    if (programmeItems.isEmpty) {
      String newID = nanoid(alphabet: Alphabet.noDoppelgangerSafe);
      return [ProgrammeItem(id: newID)];
    } else {
      return programmeItems;
    }
  }

  Future<void> addEmptytable() async {
    final x = await future;
    final id = await _generateTaskID();
    final names = x.map((item) => item.itemName).toSet();
    if (names.isEmpty) {
      state = AsyncData([...x, ProgrammeItem(id: id)]);
      return;
    } else {
      String newName = ProgrammeItem().itemName;
      while (names.contains(newName)) {
        newName = '${newName}_1';
      }
      state = AsyncData([...x, ProgrammeItem(itemName: newName, id: id)]);
    }
  }

  Future<void> addTable(ProgrammeItem programmeItem) async {
    final x = await future;
    final id = await _generateTaskID();
    final names = x.map((item) => item.itemName).toSet();
    if (names.isEmpty) {
      state = AsyncData([...x, ProgrammeItem(id: id)]);
      return;
    } else {
      String newName = '${programmeItem.itemName}';
      while (names.contains(newName)) {
        newName = '${newName}_1';
      }
      state = AsyncData([
        ...x,
        programmeItem.copyWith(itemName: newName, id: id, isTouched: false),
      ]);
    }
  }

  Future<void> deleteTable(String id) async {
    // print("Before deletion: ${state.map((item) => item.usage).toList()}");
    final x = await future;
    x.removeWhere((item) => item.id == id);
    // print("After deletion: ${x.map((item) => item.usage).toList()}");
    state = AsyncData(x);
  }

  Future<void> copyAndInsertTable(int index) async {
    final x = await future;
    final id = await _generateTaskID();
    final names = x.map((item) => item.itemName).toSet();
    if (names.isEmpty) {
      x.insert(index + 1, ProgrammeItem(id: id));
      state = AsyncData(x);
      return;
    } else {
      String newName = '${x[index].itemName}_1';
      while (names.contains(newName)) {
        newName = '${newName}_1';
      }
      x.insert(
        index + 1,
        x[index].copyWith(itemName: newName, id: id, isTouched: false),
      );
      state = AsyncData(x);
    }
  }

  Future<String> _generateTaskID() async {
    String newID = nanoid(alphabet: Alphabet.noDoppelgangerSafe);
    final x = await future;
    final Set<String> loadingTableIds = x.map((item) => item.id).toSet();
    // final Set<String> taskIDs = ref.read(_taskIDsProvider);
    while (loadingTableIds.contains(newID)) {
      newID = nanoid(alphabet: Alphabet.noDoppelgangerSafe);
    }
    return newID;
  }

  Future<void> updateTable(
    String id, {
    String? itemName,
    double? start,
    double? duration,
    bool? isTouched,
  }) async {
    final x = await future;
    // Create a list of updates
    final newState =
        x.map((item) {
          if (item.id == id) {
            return item.copyWith(
              itemName: itemName ?? item.itemName,
              start: start ?? item.start,
              duration: duration ?? item.duration,
              isTouched: isTouched ?? item.isTouched,
            );
          }
          return item;
        }).toList();

    state = AsyncData(newState);
  }

  Future<void> alignStartToStartOf(String id, int index) async {
    if (await _validateIndex(index)) {
      final x = await future;
      await updateTable(id, start: x[index].start);
    }
  }

  Future<void> alignStartToEndOf(String id, int index) async {
    if (await _validateIndex(index)) {
      final x = await future;
      await updateTable(id, start: x[index].end);
    }
  }

  Future<void> alignEndToStartOf(String id, int index) async {
    if (await _validateIndex(index)) {
      final x = await future;
      final original = x.firstWhere((item) => item.id == id);
      await updateTable(id, start: x[index].start - original.duration);
    }
  }

  Future<void> alignEndToEndOf(String id, int index) async {
    if (await _validateIndex(index)) {
      final x = await future;
      final original = x.firstWhere((item) => item.id == id);
      await updateTable(id, start: x[index].end - original.duration);
    }
  }

  Future<void> offsetItemsAfter(String id, double offset) async {
    final x = await future;
    final index = x.indexWhere((item) => item.id == id);
    final newState =
        x.map((item) {
          if (x.indexOf(item) > index) {
            return item.copyWith(start: item.start + offset);
          } else {
            return item;
          }
        }).toList();
    state = AsyncData(newState);
  }

  Future<void> allInSeriesAfterItem(String id) async {
    final x = await future;
    final index = x.indexWhere((item) => item.id == id);
    for (int i = index; i < x.length - 1; i++) {
      await updateTable(x[i + 1].id, start: x[i].end);
    }
  }

  Future<bool> _validateIndex(int index) async {
    final x = await future;
    return index >= 0 && index < x.length;
  }
}
