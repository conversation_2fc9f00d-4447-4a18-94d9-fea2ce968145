import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:structify/domain_layer/programme_item.dart';
import 'package:structify/presentation_layer/small_elements/button/function_button.dart';
import 'package:structify/presentation_layer/small_elements/popup/custom_popup.dart';
import 'package:structify/presentation_layer/small_elements/popup/custom_tooltip.dart';

//domain layer
import '../../domain_layer/preferences.dart';

// presentation layer
import '../screen/homescreen.dart';
import 'button/programme_config_button.dart';
import 'input/custom_stateful_double_input.dart';
import 'input/custom_stateful_text_input.dart';
import 'popup/multiseleciton_dialog.dart';
// import '../small_elements/global_data_ui.dart';
// import '../small_elements/loadcals_summary_ui.dart';

class ProgrammeItemInputUi extends ConsumerStatefulWidget {
  const ProgrammeItemInputUi({
    this.isExpanded,
    this.maxHeight,
    this.minHeight,
    super.key,
  });

  final bool? isExpanded;
  final double? maxHeight;
  final double? minHeight;

  @override
  ConsumerState<ConsumerStatefulWidget> createState() =>
      _ProgrammeItemInputUiState();
}

class _ProgrammeItemInputUiState extends ConsumerState<ProgrammeItemInputUi>
    with WidgetsBindingObserver {
  late bool _isExpanded;
  late double _maxHeight;
  late double _minHeight;
  late CustomStatefulTextInput _usageInput;
  late GlobalKey _buttonKey;

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void initState() {
    WidgetsBinding.instance.addObserver(this);
    _isExpanded = widget.isExpanded ?? false;
    _maxHeight = widget.maxHeight ?? 300;
    _minHeight = widget.minHeight ?? 50;
    _usageInput = CustomStatefulTextInput();
    _buttonKey = GlobalKey();
    // _updateHeights();

    super.initState();
  }

  @override
  void didChangeDependencies() {
    _updateHeights();
    super.didChangeDependencies();
    // Update heights when dependencies change, including after initState
  }

  @override
  void didChangeMetrics() {
    // This method is called when the metrics change (like resizing the window)
    setState(() {
      _updateHeights();
    });
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;
    final globalData = ref.watch(globalDataControllerProvider);
    final loadingTables = ref.watch(loadingTablesControllerProvider);
    final programmeItemInput = ref.watch(programmeItemsControllerProvider);
    final scrollController = ScrollController();

    return globalData.when(
      data: (data) {
        late final List<String> units;
        switch (data.unit) {
          case 'metrics':
            units = PreferredUnit.metrics;
            break;
          case 'imperial':
            units = PreferredUnit.imperial;
            break;
          default:
            units = PreferredUnit.metrics;
            break;
        }

        return loadingTables.when(
          data: (tables) {
            return programmeItemInput.when(
              data: (input) {
                final int containerOpacity = 175;
                final int textOpacity = 225;
                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Padding(
                      padding: const EdgeInsets.fromLTRB(10, 0, 10, 0),
                      child: Divider(),
                    ),
                    Padding(
                      padding: const EdgeInsets.fromLTRB(10, 0, 0, 0),
                      child: Align(
                        alignment: Alignment.centerLeft,
                        child: Wrap(
                          crossAxisAlignment: WrapCrossAlignment.center,
                          children: [
                            Wrap(
                              crossAxisAlignment: WrapCrossAlignment.center,
                              children: [
                                IconButton(
                                  icon: Icon(
                                    _isExpanded
                                        ? Icons.keyboard_arrow_up
                                        : Icons.keyboard_arrow_down,
                                    color: colorScheme.onSurface,
                                  ),
                                  onPressed: () {
                                    setState(() {
                                      _isExpanded = !_isExpanded;
                                    });
                                  },
                                ),
                                Text(
                                  'Programme Inputs ',
                                  style: textTheme.titleLarge!.copyWith(
                                    color: colorScheme.onSurface,
                                  ),
                                ),
                              ],
                            ),
                            Container(
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(5.0),
                                color: colorScheme.surfaceContainer.withAlpha(
                                  225,
                                ),
                                border: Border.all(
                                  color: Colors.black.withAlpha(125),
                                ),
                              ),
                              child: Padding(
                                padding: const EdgeInsets.all(2.0),
                                child: Text(
                                  '(Total: ${input.length})',
                                  style: textTheme.labelMedium!.copyWith(
                                    color: colorScheme.onSurfaceVariant
                                        .withAlpha(225),
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.fromLTRB(10, 0, 10, 0),
                      child: Divider(),
                    ),
                    Padding(
                      padding: const EdgeInsets.fromLTRB(8.0, 0.0, 8.0, 0.0),
                      child: ConstrainedBox(
                        constraints: BoxConstraints(
                          maxHeight: _isExpanded ? _maxHeight : 0,
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Row(
                              children: [
                                MultiSelectionDialog(
                                  trailing:
                                      optionalProgrammeItem().entries
                                          .map(
                                            (e) => Text(
                                              '${e.value} weeks',
                                              style: textTheme.labelMedium!
                                                  .copyWith(
                                                    color:
                                                        colorScheme
                                                            .onSecondaryContainer,
                                                  ),
                                            ),
                                          )
                                          .toList(),
                                  dialogWidth: 400.0,
                                  dialogHeight: 400.0,
                                  label: 'Add Typical Items',
                                  selectedOptions: <String>[],
                                  options:
                                      optionalProgrammeItem().entries
                                          .map((e) => e.key)
                                          .toList(),
                                  onPressed: (selectedOptions) async {
                                    if (selectedOptions.isNotEmpty) {
                                      for (String itemName in selectedOptions) {
                                        final optionalItem =
                                            optionalProgrammeItem();
                                        final optionalItemDuration =
                                            optionalItem[itemName];
                                        await ref
                                            .read(
                                              programmeItemsControllerProvider
                                                  .notifier,
                                            )
                                            .addTable(
                                              ProgrammeItem(
                                                itemName: itemName,
                                                duration: optionalItemDuration!,
                                              ),
                                            )
                                            .then((value) => setState(() {}));
                                      }
                                    }
                                  },
                                ),
                                SizedBox(width: 5.0),
                                FunctionButton(
                                  key: UniqueKey(),
                                  labelIcon: Icon(Icons.arrow_forward_outlined),
                                  labelText: 'All In Series',
                                  onTap: (isPressed) async {
                                    for (int i = 0; i < input.length - 1; i++) {
                                      final latestInput = await ref.read(
                                        programmeItemsControllerProvider.future,
                                      );
                        
                                      await ref
                                          .read(
                                            programmeItemsControllerProvider
                                                .notifier,
                                          )
                                          .updateTable(
                                            latestInput[i + 1].id,
                                            start: latestInput[i].end,
                                          );
                                    }
                                  },
                                ),
                              ],
                            ),
                            SizedBox(height: 10.0),
                        
                            Flexible(
                              child: Column(
                                children: [
                                  Expanded(
                                    child: Scrollbar(
                                      controller: scrollController,
                                      thumbVisibility: true,
                                      child: ListView.builder(
                                        controller: scrollController,
                                        itemCount: input.length,
                                        itemBuilder: (context, index) {
                                          return Padding(
                                            padding: const EdgeInsets.fromLTRB(
                                              8.0,
                                              4.0,
                                              8.0,
                                              4.0,
                                            ),
                                            child: MouseRegion(
                                              onEnter: (event) async {
                                                await Future.forEach(input, (
                                                  item,
                                                ) async {
                                                  if (item.id ==
                                                      input[index].id) {
                                                    await ref
                                                        .read(
                                                          programmeItemsControllerProvider
                                                              .notifier,
                                                        )
                                                        .updateTable(
                                                          item.id,
                                                          isTouched: true,
                                                        );
                                                  } else {
                                                    await ref
                                                        .read(
                                                          programmeItemsControllerProvider
                                                              .notifier,
                                                        )
                                                        .updateTable(
                                                          item.id,
                                                          isTouched: false,
                                                        );
                                                  }
                                                });
                                              },
                                              onExit: (event) async {
                                                await Future.forEach(input, (
                                                  item,
                                                ) async {
                                                  if (item.id ==
                                                      input[index].id) {
                                                    await ref
                                                        .read(
                                                          programmeItemsControllerProvider
                                                              .notifier,
                                                        )
                                                        .updateTable(
                                                          item.id,
                                                          isTouched: false,
                                                        );
                                                  }
                                                });
                                              },
                                              child: Row(
                                                mainAxisSize: MainAxisSize.min,
                                                mainAxisAlignment:
                                                    MainAxisAlignment.start,
                                                children: [
                                                  Container(
                                                    decoration: BoxDecoration(
                                                      shape: BoxShape.circle,
                                                      color: colorScheme
                                                          .surfaceContainer
                                                          .withAlpha(200),
                                                      // borderRadius: BorderRadius.circular(5),
                                                      border: Border.all(
                                                        color: Colors.black,
                                                      ),
                                                    ),
                                                    child: Padding(
                                                      padding:
                                                          const EdgeInsets.all(
                                                            4.0,
                                                          ),
                                                      child: Text(
                                                        '${index + 1}',
                                                        style: textTheme
                                                            .labelLarge!
                                                            .copyWith(
                                                              color: colorScheme
                                                                  .onSurfaceVariant
                                                                  .withAlpha(
                                                                    250,
                                                                  ),
                                                            ),
                                                      ),
                                                    ),
                                                  ),
                                                  IconButton(
                                                    icon: Icon(
                                                      Icons.copy_all_outlined,
                                                      color:
                                                          colorScheme.onSurface,
                                                    ),
                                                    onPressed: () async {
                                                      await ref
                                                          .read(
                                                            programmeItemsControllerProvider
                                                                .notifier,
                                                          )
                                                          .copyAndInsertTable(
                                                            index,
                                                          );
                                                    },
                                                  ),
                                                  IconButton(
                                                    icon: Icon(
                                                      Icons.delete_outline,
                                                      color:
                                                          colorScheme.onSurface,
                                                    ),
                                                    onPressed: () async {
                                                      await ref
                                                          .read(
                                                            programmeItemsControllerProvider
                                                                .notifier,
                                                          )
                                                          .deleteTable(
                                                            input[index].id,
                                                          );
                                                    },
                                                  ),
                                                  SizedBox(width: 5.0),
                                                  Flexible(
                                                    flex: 2,
                                                    child: CustomStatefulTextInput(
                                                      title: 'Item Name',
                                                      key: ValueKey(
                                                        '${input[index].id}_itemName',
                                                      ),
                                                      value:
                                                          input[index].itemName,
                                                      onChanged: (value) async {
                                                        await ref
                                                            .read(
                                                              programmeItemsControllerProvider
                                                                  .notifier,
                                                            )
                                                            .updateTable(
                                                              input[index].id,
                                                              itemName: value,
                                                            );
                                                      },
                                                    ),
                                                  ),
                                                  SizedBox(width: 5.0),
                                                  Flexible(
                                                    child: CustomStatefulDoubleInput(
                                                      title: 'Start',
                                                      key: ValueKey(
                                                        '${input[index].id}_start',
                                                      ),
                                                      value: input[index].start,
                                                      onChanged: (value) async {
                                                        await ref
                                                            .read(
                                                              programmeItemsControllerProvider
                                                                  .notifier,
                                                            )
                                                            .updateTable(
                                                              input[index].id,
                                                              start: value,
                                                            );
                                                      },
                                                    ),
                                                  ),
                                                  SizedBox(width: 5.0),
                                                  Flexible(
                                                    child: CustomStatefulDoubleInput(
                                                      title: 'Duration',
                                                      key: ValueKey(
                                                        '${input[index].id}_duration',
                                                      ),
                                                      value:
                                                          input[index].duration,
                                                      onChanged: (value) async {
                                                        await ref
                                                            .read(
                                                              programmeItemsControllerProvider
                                                                  .notifier,
                                                            )
                                                            .updateTable(
                                                              input[index].id,
                                                              duration: value,
                                                            );
                                                      },
                                                    ),
                                                  ),
                                                  SizedBox(width: 5.0),
                                                  ProgrammeConfigButton(
                                                    index: index,
                                                    programmeItems:
                                                        input
                                                            .map(
                                                              (item) =>
                                                                  item.itemName,
                                                            )
                                                            .toList(),
                                                    onConfigChanged: (
                                                      result,
                                                    ) async {
                                                      if (result != null) {
                                                        final id =
                                                            input[index].id;
                                                        final originalStart =
                                                            input[index].start;
                        
                                                        if (result['isChecked'][0]) {
                                                          final String choice =
                                                              result['values'][0] +
                                                              result['values'][1];
                        
                                                          final int
                                                          targetIndex = input.indexOf(
                                                            input.firstWhere(
                                                              (item) =>
                                                                  item.itemName ==
                                                                  result['values'][2],
                                                            ),
                                                          );
                                                          switch (choice) {
                                                            case 'StartStart':
                                                              await ref
                                                                  .read(
                                                                    programmeItemsControllerProvider
                                                                        .notifier,
                                                                  )
                                                                  .alignStartToStartOf(
                                                                    id,
                                                                    targetIndex,
                                                                  );
                                                            case 'StartEnd':
                                                              await ref
                                                                  .read(
                                                                    programmeItemsControllerProvider
                                                                        .notifier,
                                                                  )
                                                                  .alignStartToEndOf(
                                                                    id,
                                                                    targetIndex,
                                                                  );
                                                            case 'EndStart':
                                                              await ref
                                                                  .read(
                                                                    programmeItemsControllerProvider
                                                                        .notifier,
                                                                  )
                                                                  .alignEndToStartOf(
                                                                    id,
                                                                    targetIndex,
                                                                  );
                                                            case 'EndEnd':
                                                              await ref
                                                                  .read(
                                                                    programmeItemsControllerProvider
                                                                        .notifier,
                                                                  )
                                                                  .alignEndToEndOf(
                                                                    id,
                                                                    targetIndex,
                                                                  );
                                                              break;
                                                            default:
                                                          }
                                                        }
                                                        if (result['isChecked'][1]) {
                                                          // get latest input first
                                                          final List<
                                                            ProgrammeItem
                                                          >
                                                          latestInput =
                                                              await ref.read(
                                                                programmeItemsControllerProvider
                                                                    .future,
                                                              );
                                                          final double offset =
                                                              latestInput[index]
                                                                  .start -
                                                              originalStart;
                                                          await ref
                                                              .read(
                                                                programmeItemsControllerProvider
                                                                    .notifier,
                                                              )
                                                              .offsetItemsAfter(
                                                                id,
                                                                offset,
                                                              );
                                                        }
                                                      }
                                                    },
                                                  ),
                                                  const SizedBox(width: 5.0),
                                                  Tooltip(
                                                    message:
                                                        'start at previous start',
                                                    child: FunctionButton(
                                                      key: UniqueKey(),
                                                      labelText: 'PS',
                                                      onTap: (isTapped) async {
                                                        if (index - 1 >= 0) {
                                                          await ref
                                                              .read(
                                                                programmeItemsControllerProvider
                                                                    .notifier,
                                                              )
                                                              .alignStartToStartOf(
                                                                input[index].id,
                                                                index - 1,
                                                              );
                                                          // return false;
                                                        }
                                                      },
                                                    ),
                                                  ),
                                                  const SizedBox(width: 5.0),
                                                  Tooltip(
                                                    message:
                                                        'start at previous end',
                                                    child: FunctionButton(
                                                      key: UniqueKey(),
                                                      labelText: 'PE',
                                                      onTap: (isTapped) async {
                                                        if (index - 1 >= 0) {
                                                          await ref
                                                              .read(
                                                                programmeItemsControllerProvider
                                                                    .notifier,
                                                              )
                                                              .alignStartToEndOf(
                                                                input[index].id,
                                                                index - 1,
                                                              );
                                                          // return false;
                                                        }
                                                      },
                                                    ),
                                                  ),
                                                  const SizedBox(width: 5.0),
                                                  Tooltip(
                                                    message:
                                                        'start at next start',
                                                    child: FunctionButton(
                                                      key: UniqueKey(),
                                                      labelText: 'NS',
                                                      onTap: (isTapped) async {
                                                        if (index + 1 <=
                                                            input.length - 1) {
                                                          await ref
                                                              .read(
                                                                programmeItemsControllerProvider
                                                                    .notifier,
                                                              )
                                                              .alignStartToStartOf(
                                                                input[index].id,
                                                                index + 1,
                                                              );
                                                          // return false;
                                                        }
                                                      },
                                                    ),
                                                  ),
                                                  const SizedBox(width: 5.0),
                                                  Tooltip(
                                                    message:
                                                        'start at next end',
                                                    child: FunctionButton(
                                                      key: UniqueKey(),
                                                      labelText: 'NE',
                                                      onTap: (isTapped) async {
                                                        if (index + 1 <=
                                                            input.length - 1) {
                                                          await ref
                                                              .read(
                                                                programmeItemsControllerProvider
                                                                    .notifier,
                                                              )
                                                              .alignStartToEndOf(
                                                                input[index].id,
                                                                index + 1,
                                                              );
                                                          // return false;
                                                        }
                                                      },
                                                    ),
                                                  ),
                                                  
                                                ],
                                              ),
                                            ),
                                          );
                                        },
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                );
              },
              error: (error, stackTrace) {
                return Text('Error: $error');
              },
              loading: () {
                return const CircularProgressIndicator();
              },
            );
          },
          error: (error, stackTrace) => Text(error.toString()),
          loading: () => CircularProgressIndicator(),
        );
      },
      error: (error, stackTrace) {
        return Text('Error: $error');
      },
      loading: () {
        return const CircularProgressIndicator();
      },
    );
  }

  void _updateHeights() {
    final double screenHeight = MediaQuery.of(context).size.height;
    _maxHeight =
        (widget.maxHeight == null)
            ? screenHeight * 0.6
            : (widget.maxHeight! > screenHeight * 0.6)
            ? screenHeight * 0.6
            : widget.maxHeight!;

    _minHeight =
        (widget.minHeight == null)
            ? screenHeight * 0.1
            : (widget.minHeight! > screenHeight * 0.1)
            ? screenHeight * 0.1
            : widget.minHeight!;

    // Adjust _maxHeight if needed
    if (_maxHeight < _minHeight) {
      _maxHeight = _minHeight;
    }
  }
}

Map<String, double> optionalProgrammeItem() {
  return {
    'Site Possession': 4.0,
    'Hoarding': 4.0,
    'Piles': 12.0,
    'Pile Cap and Tie Beams': 4.0,
    'Basement': 1.5,
    'Ground Floor': 1.5,
    'Non-typical Floor': 2.0,
    'Transfer Floor': 2.0,
    'Typical Floor': 1.0,
    'Roof': 1.0,
    'fitting out': 2.0,
    'E&M works': 2.0,
  };
}
