import 'package:freezed_annotation/freezed_annotation.dart';
// import 'package:flutter/foundation.dart';
part 'beam_scheme_input.freezed.dart';
part 'beam_scheme_input.g.dart';

@freezed
abstract class BeamSchemeInput with _$BeamSchemeInput {
  const BeamSchemeInput._();
  factory BeamSchemeInput({
    @Default('1') String id,
    @Default(5.0) double shortSpan,
    @Default(12.0) double longSpan,
    @Default(2) int bays,
    @Default(500.0) double mainStrZone,
    @Default(500.0) double secStrZone,
    @Default(45.0) double fcu,
    @Default(40.0) double cover,
    @Default(0.156) double mainKValue,
    @Default(0.025) double mainSteelRatio,
    @Default(0.156) double secKValue,
    @Default(0.040) double secSteelRatio,
    @Default(100) int minS,
    @Default(300) int maxS,
    @Default(1000.0) double maxWidth,
    @Default(2) int maxLayers,
    @Default(0.0) double shortSpanIncreament,
    @Default(0.0) double longSpanIncreament,
    @Default(1) int baysIncreament,
    @Default(4) int iterationSteps,
    @Default('') String usage,
    @Default(150.0) double slabThickness,
    @Default(false) bool useSlabSelected,
  }) = _BeamSchemeInput;

  factory BeamSchemeInput.fromJson(Map<String, Object?> json) =>
      _$BeamSchemeInputFromJson(json);
}
