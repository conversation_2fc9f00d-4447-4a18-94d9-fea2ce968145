import 'package:freezed_annotation/freezed_annotation.dart';
// import 'package:flutter/foundation.dart';
part 'pile_driven_input.freezed.dart';
part 'pile_driven_input.g.dart';

@freezed
abstract class PileDrivenInput with _$PileDrivenInput{
  const PileDrivenInput._();
  factory PileDrivenInput({
    @Default(50) double sptNValue,
    @Default(2) double fos,
    @Default(30) double maxPileLength,
    @Default(1000) double slsLoad,
    @Default(2000) double ulsLoad,
    @Default(false) bool useSelectColLoad,
    @Default('1') String id, //will be overriden  as soon as new instance created
  }) = _PileDrivenInput;

  factory PileDrivenInput.fromJson(Map<String, Object?> json) =>
      _$PileDrivenInputFromJson(json);
}
