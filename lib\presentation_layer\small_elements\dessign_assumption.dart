import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:structify/domain_layer/global_data.dart';
import 'package:structify/domain_layer/slab_scheme_data.dart';
import 'package:structify/presentation_layer/small_elements/popup/custom_tooltip.dart';
import 'package:structify/presentation_layer/small_elements/sketch/draw_slab_scheme_sketch.dart';
import 'package:structify/presentation_layer/small_elements/button/function_button.dart';

//below for printing
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:printing/printing.dart';
// import 'dart:typed_data';
// import 'dart:ui';
// import 'package:structify/presentation_layer/small_elements/loadcals_summary_ui.dart';

//domain layer
import '../../domain_layer/preferences.dart';

// presentation layer
import '../screen/homescreen.dart';
// import '../small_elements/custom_stateful_double_input.dart';
// import '../small_elements/custom_stateful_text_input.dart';
// import '../small_elements/global_data_ui.dart';

//misc
import 'button/selection_button.dart';

class DesignAssumption extends ConsumerStatefulWidget {
  const DesignAssumption({
    this.isExpanded,
    this.maxHeight,
    this.minHeight,
    this.assumptions,
    this.title,
    this.tooltipText,
    this.opacity,
    this.textStyleTooltips,
    this.backgroundColor,
    this.textStyle,
    this.titleStyle,
    super.key,
  });

  final bool? isExpanded;
  final double? maxHeight;
  final double? minHeight;
  final List<String>? assumptions;
  final String? title;
  final String? tooltipText;
  final int? opacity;
  final TextStyle? textStyleTooltips;
  final Color? backgroundColor;
  final TextStyle? textStyle;
   final TextStyle? titleStyle;

  @override
  ConsumerState<ConsumerStatefulWidget> createState() =>
      _DesignAssumptionState();
}

class _DesignAssumptionState extends ConsumerState<DesignAssumption>
    with WidgetsBindingObserver {
  late bool _isExpanded;
  late double _maxHeight;
  late double _minHeight;
  late List<String> _assumptions;
  late ScrollController _scrollController;
  late String _title;
  late String _tooltipText;

  late ColorScheme _colorScheme;
  late TextTheme _textTheme;
  late TextStyle _textStyle;
  late TextStyle _titleStyle;

  //* below for custom tooltip
  late final int? _opacity;
  late final TextStyle? _textStyleTooltips;
  late final Color? _backgroundColor;

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _isExpanded = widget.isExpanded ?? false;
    _maxHeight = widget.maxHeight ?? 300;
    _minHeight = widget.minHeight ?? 50;
    _assumptions = widget.assumptions ?? <String>[''];
    _scrollController = ScrollController();
    _title = widget.title ?? '';
    _tooltipText = widget.tooltipText ?? '';
    _opacity = widget.opacity;

    //* initialization for custom tooltip
    _textStyleTooltips = widget.textStyleTooltips;
    _backgroundColor = widget.backgroundColor;
  }

  @override
  void didChangeDependencies() {
    _updateHeights();
    super.didChangeDependencies();
    _colorScheme = Theme.of(context).colorScheme;
    _textTheme = Theme.of(context).textTheme;
    _textStyle =
        widget.textStyle ??
        _textTheme.bodyMedium!.copyWith(color: _colorScheme.onSurface);
    _titleStyle =
        widget.titleStyle ??
        _textTheme.titleLarge!.copyWith(color: _colorScheme.onSurface);
  }

  @override
  void didChangeMetrics() {
    // This method is called when the metrics change (like resizing the window)
    setState(() {
      _updateHeights();
    });
  }

  @override
  Widget build(BuildContext context) {
    final int containerOpacity = 255;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.fromLTRB(10, 0, 10, 0),
          child: Divider(),
        ),
        Padding(
          padding: const EdgeInsets.fromLTRB(10, 0, 0, 0),
          child: Align(
            alignment: Alignment.centerLeft,
            child: Row(
              children: [
                IconButton(
                  icon: Icon(
                    _isExpanded ? Icons.expand_less : Icons.expand_more,color: _titleStyle.color,
                  ),
                  color: _colorScheme.onSurface,
                  onPressed: () {
                    setState(() {
                      _isExpanded = !_isExpanded;
                    });
                  },
                ),
                Text(
                  '${_title ?? 'Design Assumptions'}',
                  style: _titleStyle,
                ),
                SizedBox(width: 5.0),
                _tooltipText == ''
                    ? const SizedBox(width: 0, height: 0)
                    : CustomTooltip(
                      icon: Icon( Icons.info_outline, color: _titleStyle.color,
                      ),
                      textstyle: _textStyleTooltips,
                      backgroundColor: _backgroundColor,
                      tooltipText: _tooltipText,
                      opacity: _opacity,
                    ),
              ],
            ),
          ),
        ),
        Padding(
          padding: const EdgeInsets.fromLTRB(10, 0, 10, 0),
          child: Divider(),
        ),
        ClipRect(
          child: ConstrainedBox(
            constraints: BoxConstraints(
              maxHeight: _isExpanded ? _maxHeight : 0,
            ),
            child: Scrollbar(
              controller: _scrollController,
              thumbVisibility: true,
              child: DefaultTextStyle(
                style: _textTheme.bodySmall!.copyWith(
                  color: _colorScheme.onSurface,
                ),
                child: ListView.builder(
                  controller: _scrollController,
                  itemCount: _assumptions.length,
                  itemBuilder: (context, index) {
                    return Padding(
                      padding: const EdgeInsets.fromLTRB(10, 0, 10, 0),
                      child: DefaultTextStyle(
                        style: _textStyle,
                        child: Wrap(
                          children: [
                            Text(
                              '${(_assumptions[0] == '') && (_assumptions.length == 1) ? '' : '${index + 1}. ${_assumptions[index]}'}',
                            ),
                          ],
                        ),
                      ),
                    );
                  },
                ),
              ),
            ),
          ),
        ),
      ],
    );
    // return Container(width: 100, height: 100,color: Colors.amber,);
  }

  // Method to update heights based on the current media query
  void _updateHeights() {
    final double screenHeight = MediaQuery.of(context).size.height;
    _maxHeight =
        (widget.maxHeight == null)
            ? screenHeight * 0.3
            : (widget.maxHeight! > screenHeight * 0.3)
            ? screenHeight * 0.3
            : widget.maxHeight!;

    _minHeight =
        (widget.minHeight == null)
            ? screenHeight * 0.1
            : (widget.minHeight! > screenHeight * 0.1)
            ? screenHeight * 0.1
            : widget.minHeight!;

    // Adjust _maxHeight if needed

    if (_maxHeight < _minHeight) {
      _maxHeight = _minHeight;
    }
  }
}
