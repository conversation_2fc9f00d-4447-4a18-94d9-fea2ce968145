import 'package:freezed_annotation/freezed_annotation.dart';
// import 'package:flutter/foundation.dart';
part 'footing_scheme_input.freezed.dart';
part 'footing_scheme_input.g.dart';

@freezed
abstract class FootingSchemeInput with _$FootingSchemeInput {
  const FootingSchemeInput._();
  factory FootingSchemeInput({
    @Default('1') String id,
    @Default(45.0) double fcu,
    @Default(75.0) double cover,
    @Default(0.156) double mainKValue,
    @Default(0.04) double mainSteelRatio,
    @Default(100) int minS,
    @Default(300) int maxS,
    @Default(2000) double maxDepth,
    @Default(500.0) double minDepth,
    @Default(4) int maxLayers,
    @Default('Soil') String groundType,
    @Default(50) double soilNValue,
    @Default(0) double footingTopLevel,
    @Default(-3) double waterTableLevel,
    @Default(1000) double rockCapacity,
    @Default('Square') String colShape,
    @Default(1000.0) double columnSize,
    @Default(1500.0) double slsLoad,
    @Default(3000.0) double ulsLoad,
    @Default(false) bool useSelectColLoad,
  }) = _FootingSchemeInput; 

  factory FootingSchemeInput.fromJson(Map<String, Object?> json) =>
      _$FootingSchemeInputFromJson(json);
}
