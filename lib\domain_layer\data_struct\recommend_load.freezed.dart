// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'recommend_load.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$RecommendedLoad {

 String get usage; double get finish;//[mm]
 double get service;//[kPa]
 double get partitionLoad;//[kPa]
 double get sdl;//[kPa]
 double get ll;//[kPa]
 double get frp;
/// Create a copy of RecommendedLoad
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$RecommendedLoadCopyWith<RecommendedLoad> get copyWith => _$RecommendedLoadCopyWithImpl<RecommendedLoad>(this as RecommendedLoad, _$identity);

  /// Serializes this RecommendedLoad to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is RecommendedLoad&&(identical(other.usage, usage) || other.usage == usage)&&(identical(other.finish, finish) || other.finish == finish)&&(identical(other.service, service) || other.service == service)&&(identical(other.partitionLoad, partitionLoad) || other.partitionLoad == partitionLoad)&&(identical(other.sdl, sdl) || other.sdl == sdl)&&(identical(other.ll, ll) || other.ll == ll)&&(identical(other.frp, frp) || other.frp == frp));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,usage,finish,service,partitionLoad,sdl,ll,frp);

@override
String toString() {
  return 'RecommendedLoad(usage: $usage, finish: $finish, service: $service, partitionLoad: $partitionLoad, sdl: $sdl, ll: $ll, frp: $frp)';
}


}

/// @nodoc
abstract mixin class $RecommendedLoadCopyWith<$Res>  {
  factory $RecommendedLoadCopyWith(RecommendedLoad value, $Res Function(RecommendedLoad) _then) = _$RecommendedLoadCopyWithImpl;
@useResult
$Res call({
 String usage, double finish, double service, double partitionLoad, double sdl, double ll, double frp
});




}
/// @nodoc
class _$RecommendedLoadCopyWithImpl<$Res>
    implements $RecommendedLoadCopyWith<$Res> {
  _$RecommendedLoadCopyWithImpl(this._self, this._then);

  final RecommendedLoad _self;
  final $Res Function(RecommendedLoad) _then;

/// Create a copy of RecommendedLoad
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? usage = null,Object? finish = null,Object? service = null,Object? partitionLoad = null,Object? sdl = null,Object? ll = null,Object? frp = null,}) {
  return _then(_self.copyWith(
usage: null == usage ? _self.usage : usage // ignore: cast_nullable_to_non_nullable
as String,finish: null == finish ? _self.finish : finish // ignore: cast_nullable_to_non_nullable
as double,service: null == service ? _self.service : service // ignore: cast_nullable_to_non_nullable
as double,partitionLoad: null == partitionLoad ? _self.partitionLoad : partitionLoad // ignore: cast_nullable_to_non_nullable
as double,sdl: null == sdl ? _self.sdl : sdl // ignore: cast_nullable_to_non_nullable
as double,ll: null == ll ? _self.ll : ll // ignore: cast_nullable_to_non_nullable
as double,frp: null == frp ? _self.frp : frp // ignore: cast_nullable_to_non_nullable
as double,
  ));
}

}


/// Adds pattern-matching-related methods to [RecommendedLoad].
extension RecommendedLoadPatterns on RecommendedLoad {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _RecommendedLoad value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _RecommendedLoad() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _RecommendedLoad value)  $default,){
final _that = this;
switch (_that) {
case _RecommendedLoad():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _RecommendedLoad value)?  $default,){
final _that = this;
switch (_that) {
case _RecommendedLoad() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String usage,  double finish,  double service,  double partitionLoad,  double sdl,  double ll,  double frp)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _RecommendedLoad() when $default != null:
return $default(_that.usage,_that.finish,_that.service,_that.partitionLoad,_that.sdl,_that.ll,_that.frp);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String usage,  double finish,  double service,  double partitionLoad,  double sdl,  double ll,  double frp)  $default,) {final _that = this;
switch (_that) {
case _RecommendedLoad():
return $default(_that.usage,_that.finish,_that.service,_that.partitionLoad,_that.sdl,_that.ll,_that.frp);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String usage,  double finish,  double service,  double partitionLoad,  double sdl,  double ll,  double frp)?  $default,) {final _that = this;
switch (_that) {
case _RecommendedLoad() when $default != null:
return $default(_that.usage,_that.finish,_that.service,_that.partitionLoad,_that.sdl,_that.ll,_that.frp);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _RecommendedLoad extends RecommendedLoad {
   _RecommendedLoad({this.usage = '', this.finish = 50.0, this.service = 1.0, this.partitionLoad = 0.0, this.sdl = 0.0, this.ll = 0.0, this.frp = 0.0}): super._();
  factory _RecommendedLoad.fromJson(Map<String, dynamic> json) => _$RecommendedLoadFromJson(json);

@override@JsonKey() final  String usage;
@override@JsonKey() final  double finish;
//[mm]
@override@JsonKey() final  double service;
//[kPa]
@override@JsonKey() final  double partitionLoad;
//[kPa]
@override@JsonKey() final  double sdl;
//[kPa]
@override@JsonKey() final  double ll;
//[kPa]
@override@JsonKey() final  double frp;

/// Create a copy of RecommendedLoad
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$RecommendedLoadCopyWith<_RecommendedLoad> get copyWith => __$RecommendedLoadCopyWithImpl<_RecommendedLoad>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$RecommendedLoadToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _RecommendedLoad&&(identical(other.usage, usage) || other.usage == usage)&&(identical(other.finish, finish) || other.finish == finish)&&(identical(other.service, service) || other.service == service)&&(identical(other.partitionLoad, partitionLoad) || other.partitionLoad == partitionLoad)&&(identical(other.sdl, sdl) || other.sdl == sdl)&&(identical(other.ll, ll) || other.ll == ll)&&(identical(other.frp, frp) || other.frp == frp));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,usage,finish,service,partitionLoad,sdl,ll,frp);

@override
String toString() {
  return 'RecommendedLoad(usage: $usage, finish: $finish, service: $service, partitionLoad: $partitionLoad, sdl: $sdl, ll: $ll, frp: $frp)';
}


}

/// @nodoc
abstract mixin class _$RecommendedLoadCopyWith<$Res> implements $RecommendedLoadCopyWith<$Res> {
  factory _$RecommendedLoadCopyWith(_RecommendedLoad value, $Res Function(_RecommendedLoad) _then) = __$RecommendedLoadCopyWithImpl;
@override @useResult
$Res call({
 String usage, double finish, double service, double partitionLoad, double sdl, double ll, double frp
});




}
/// @nodoc
class __$RecommendedLoadCopyWithImpl<$Res>
    implements _$RecommendedLoadCopyWith<$Res> {
  __$RecommendedLoadCopyWithImpl(this._self, this._then);

  final _RecommendedLoad _self;
  final $Res Function(_RecommendedLoad) _then;

/// Create a copy of RecommendedLoad
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? usage = null,Object? finish = null,Object? service = null,Object? partitionLoad = null,Object? sdl = null,Object? ll = null,Object? frp = null,}) {
  return _then(_RecommendedLoad(
usage: null == usage ? _self.usage : usage // ignore: cast_nullable_to_non_nullable
as String,finish: null == finish ? _self.finish : finish // ignore: cast_nullable_to_non_nullable
as double,service: null == service ? _self.service : service // ignore: cast_nullable_to_non_nullable
as double,partitionLoad: null == partitionLoad ? _self.partitionLoad : partitionLoad // ignore: cast_nullable_to_non_nullable
as double,sdl: null == sdl ? _self.sdl : sdl // ignore: cast_nullable_to_non_nullable
as double,ll: null == ll ? _self.ll : ll // ignore: cast_nullable_to_non_nullable
as double,frp: null == frp ? _self.frp : frp // ignore: cast_nullable_to_non_nullable
as double,
  ));
}


}

// dart format on
