import 'package:freezed_annotation/freezed_annotation.dart';
// import 'package:flutter/foundation.dart';
part 'steel_cantilever_truss_scheme_input_global.freezed.dart';
part 'steel_cantilever_truss_scheme_input_global.g.dart';

@freezed
abstract class SteelCantileverTrussSchemeInputGlobal
    with _$SteelCantileverTrussSchemeInputGlobal {
  const SteelCantileverTrussSchemeInputGlobal._();
  factory SteelCantileverTrussSchemeInputGlobal({
    @Default('1') String id,
    @Default(20.0) double span,
    @Default(10.0) double loadWidth,
    @Default(1200.0) double strZone,
    @Default(355.0) double fsy,
    @Default(5.0) double unbracedLength,
    @Default('') String usage,
    @Default(130.0) double slabThickness,
  }) = _SteelCantileverTrussSchemeInputGlobal;
  

  factory SteelCantileverTrussSchemeInputGlobal.fromJson(Map<String, Object?> json) =>
      _$SteelCantileverTrussSchemeInputGlobalFromJson(json);
}
