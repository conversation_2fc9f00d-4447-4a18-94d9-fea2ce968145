import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:nanoid2/nanoid2.dart';
import 'dart:math';
import 'package:intl/intl.dart';

//presentation layer
import '../../domain_layer/mixin/mixin_steel_str.dart';
import '../../domain_layer/pile_driven_data.dart';
import '../../domain_layer/pile_driven_input.dart';
import '../screen/homescreen.dart';

// domain layer
import '../../domain_layer/global_data.dart';
import '../../domain_layer/preferences.dart';

part 'pile_driven_data_controller.g.dart';

@riverpod
class PileDrivenDataController extends _$PileDrivenDataController
    with SteelStrHK {
  @override
  FutureOr<List<PileDrivenData>> build() async {
    // print('Build: Column Scheme Data');
    final pileDrivenDataList =
        await ref
            .read(appDatabaseControllerProvider.notifier)
            .queryPileDrivenData();
    final data1 = ref.watch(pileDrivenInputControllerProvider);
    return data1.when(
      data: (pileDrivenInput) async {
        return await pileDrivenScheming(
          existingSchemes: pileDrivenDataList,
          calledInBuild: true,
        );
      },
      error: (error, stackTrace) => <PileDrivenData>[],
      loading: () => <PileDrivenData>[],
    );
  }

  Future<void> addSchemeData(List<PileDrivenData> pileData) async {
    final x = await future;
    state = AsyncData([...x, ...pileData]);
  }

  Future<void> deleteTable(String id) async {
    final x = await future;
    x.removeWhere((item) => item.pileDrivenSchemeId == id);
    state = AsyncData(x);
  }

  Future<void> deleteAllTable() async {
    state = AsyncData([]);
  }

  Future<List<PileDrivenData>> deleteTablesNotSelected({
    List<PileDrivenData>? existingScheme,
    bool? calledInBuild,
  }) async {
    late final List<PileDrivenData> schemes;
    if (existingScheme != null && existingScheme.isNotEmpty) {
      schemes = existingScheme;
    } else {
      if (calledInBuild != null && calledInBuild) {
        schemes = [];
      } else {
        schemes = await future;
      }
    }
    schemes.removeWhere((item) => !item.isSelected);
    state = AsyncData(schemes);
    return schemes;
  }

  Future<void> toggleSelectScheme(String pileDrivenSchemeId) async {
    final x = await future;
    final newState =
        x.map((item) {
          if (item.pileDrivenSchemeId == pileDrivenSchemeId) {
            return item.copyWith(isSelected: !item.isSelected);
          } else {
            return item.copyWith(isSelected: false);
          }
        }).toList();
    state = AsyncData(newState);
  }

  Future<String> _generateUniqueId({Set<String>? existingID}) async {
    late final Set<String> pileDrivenSchemeId;
    if (existingID == null) {
      final x = await future;
      pileDrivenSchemeId = x.map((item) => item.pileDrivenSchemeId).toSet();
    } else {
      pileDrivenSchemeId = existingID;
    }
    String newID = nanoid(alphabet: Alphabet.noDoppelgangerSafe);
    while (pileDrivenSchemeId.contains(newID)) {
      newID = nanoid(alphabet: Alphabet.noDoppelgangerSafe);
    }
    return newID;
  }

  Future<List<PileDrivenData>> pileDrivenScheming({
    List<PileDrivenData>? existingSchemes,
    bool? calledInBuild,
  }) async {
    //* keep selected schemes
    late final List<PileDrivenData> schemesLeft;
    List<PileDrivenData> finalList = [];

    schemesLeft = await deleteTablesNotSelected(
      existingScheme: existingSchemes,
      calledInBuild: calledInBuild,
    );
    finalList.addAll(schemesLeft);
    final existingID =
        existingSchemes?.map((e) => e.pileDrivenSchemeId).toSet();

    final loadingTables = ref.watch(loadingTablesControllerProvider);
    final globalData = ref.watch(globalDataControllerProvider);
    final pileDrivenInput = ref.watch(pileDrivenInputControllerProvider);

    //* Initialization
    // record params
    StringBuffer buffer = StringBuffer(); // record the cals

    // capacity param
    double shaftCapacity = 0,
        strSLSCapacity = 0,
        totalGroundResistance = 0,
        strULSCapacity = 0;

    // material params
    final List<Map<String, dynamic>> steelSections = await getAllSteelISection(
      sectionShape: 'UBP',
    );
    late final Map<String, dynamic> section;
    late double fsyGrade;

    // initial iteration param
    double lengthIntl = 5;

    // iteration param
    double lengthDelta = 1;

    return loadingTables.when(
      data: (tables) {
        //start reutnr real stuffs when loading tables available
        return globalData.when(
          //also, start reutnr real stuffs when global data tables available
          data: (data) {
            return pileDrivenInput.when(
              data: (input) async {
                //* get section then update the design strength
                section = steelSections.firstWhere(
                  (item) => item['name'] == ('UBP305X305X223'),
                );
                fsyGrade = 460;
                //* get column capacity thru looping

                for (
                  double length = lengthIntl;
                  length <= input.maxPileLength;
                  length += lengthDelta
                ) {
                  shaftCapacity = getDrivenPileShaftCap(
                    section,
                    length,
                    input.sptNValue,
                    fos: input.fos,
                  );
                  strSLSCapacity =
                      0.3 *
                      await getDesignStrength(
                        section['name'],
                        fsyGrade,
                        steelSections,
                      ) *
                      (double.parse(section['A'])) *
                      100 /
                      1000;
                  totalGroundResistance = min(shaftCapacity, strSLSCapacity);
                  strULSCapacity = await getAxialCapacitySteelUC(
                    fsyGrade: fsyGrade,
                    unbracedLength: 0.01,
                    sectionName: section['name'],
                    steelSections: steelSections,
                  );
                  //* save scheme ONLY when criteria satisfied
                  if (totalGroundResistance >= input.slsLoad &&
                      strULSCapacity >= input.ulsLoad) {
                    final id = await _generateUniqueId(existingID: existingID);

                    _recordCalsResult(
                      buffer,
                      input: input,
                      globalData: data,
                      section: section,
                      length: length,
                      shaftCapacity: shaftCapacity,
                      strSLSCapacity: strSLSCapacity,
                      totalGroundResistance: totalGroundResistance,
                      strULSCapacity: strULSCapacity,
                    );

                    finalList.add(
                      PileDrivenData(
                        sptNValue: input.sptNValue,
                        fos: input.fos,
                        maxPileLength: input.maxPileLength,
                        slsLoad: input.slsLoad,
                        ulsLoad: input.ulsLoad,
                        section: section['name'],
                        length: length,
                        shaftCapacity: shaftCapacity,
                        strSLSCapacuity: strSLSCapacity,
                        totalGroundResistance: totalGroundResistance,
                        strULSCapacity: strULSCapacity,
                        isSelected: false,
                        calsLog: buffer.toString(),
                        pileDrivenSchemeId: id,
                      ),
                    );
                    break;
                  }
                }
                state = AsyncData(finalList);
                return finalList;
              },

              error: (error, stackTrace) => [PileDrivenData(calsLog: 'error')],
              loading: () => [PileDrivenData(calsLog: 'loading')],
            );
          },
          error: (error, stackTrace) => [PileDrivenData(calsLog: 'error')],
          loading: () => [PileDrivenData(calsLog: 'loading')],
        );
      },
      error: (error, stackTrace) => [PileDrivenData(calsLog: 'error')],
      loading: () => [PileDrivenData(calsLog: 'loading')],
    );
  }

  void _recordCalsResult(
    StringBuffer buffer, {
    required PileDrivenInput input,
    required GlobalData globalData,
    required Map<String, dynamic> section,
    required double length,
    required double shaftCapacity,
    required double strSLSCapacity,
    required double totalGroundResistance,
    required double strULSCapacity,
  }) {
    late final List<String> unit;
    switch (globalData.unit) {
      case 'metrics':
        unit = PreferredUnit.metrics;
        break;
      case 'imperial':
        unit = PreferredUnit.imperial;
        break;
      default:
        unit = PreferredUnit.metrics;
        break;
    }
    buffer.clear();
    buffer.write(
      'SLS Total: ${NumberFormat('0').format(input.slsLoad)} [${unit[0]}] | ',
    );
    buffer.write(
      'ULS Total: ${NumberFormat('0').format(input.ulsLoad)} [${unit[0]}]\n',
    );
    buffer.write('Section: ${section['name']} | ');
    buffer.write('Length: ${NumberFormat('0').format(length)} [${unit[4]}] | ');

    buffer.write(
      'SPT N-value: ${NumberFormat('0').format(input.sptNValue)} | ',
    );
    buffer.write(
      ' Surface Area per meter: ${NumberFormat('0').format(((double.tryParse(section['d']) ?? 0) + (double.tryParse(section['b']) ?? 0)) * 2 * 10)} [${unit[7]}/${unit[3]}]',
    );
    buffer.write('FOS: ${NumberFormat('0.00').format(input.fos)}\n');
    buffer.write(
      'Shaft Capacity: ${NumberFormat('0').format(shaftCapacity)} [${unit[0]}] | ',
    );
    buffer.write(
      'Structural SLS Capacity: ${NumberFormat('0').format(strSLSCapacity)} [${unit[0]}] | ',
    );
    buffer.write(
      'Total Ground Resistance: ${NumberFormat('0').format(totalGroundResistance)} [${unit[0]}]\n',
    );
    buffer.write(
      'Structural ULS Capacity: ${NumberFormat('0').format(strULSCapacity)} [${unit[0]}]\n',
    );
    if (totalGroundResistance > input.slsLoad) {
      buffer.write('Ground Capacity: pass\n');
    } else {
      buffer.write('Ground Capacity: fail\n');
    }
    if (strULSCapacity > input.ulsLoad) {
      buffer.write('Structural Capacity: pass\n');
    } else {
      buffer.write('Structural Capacity: fail\n');
    }
  }
}
