// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'beam_scheme_input.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$BeamSchemeInput {

 String get id; double get shortSpan; double get longSpan; int get bays; double get mainStrZone; double get secStrZone; double get fcu; double get cover; double get mainKValue; double get mainSteelRatio; double get secKValue; double get secSteelRatio; int get minS; int get maxS; double get maxWidth; int get maxLayers; double get shortSpanIncreament; double get longSpanIncreament; int get baysIncreament; int get iterationSteps; String get usage; double get slabThickness; bool get useSlabSelected;
/// Create a copy of BeamSchemeInput
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$BeamSchemeInputCopyWith<BeamSchemeInput> get copyWith => _$BeamSchemeInputCopyWithImpl<BeamSchemeInput>(this as BeamSchemeInput, _$identity);

  /// Serializes this BeamSchemeInput to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is BeamSchemeInput&&(identical(other.id, id) || other.id == id)&&(identical(other.shortSpan, shortSpan) || other.shortSpan == shortSpan)&&(identical(other.longSpan, longSpan) || other.longSpan == longSpan)&&(identical(other.bays, bays) || other.bays == bays)&&(identical(other.mainStrZone, mainStrZone) || other.mainStrZone == mainStrZone)&&(identical(other.secStrZone, secStrZone) || other.secStrZone == secStrZone)&&(identical(other.fcu, fcu) || other.fcu == fcu)&&(identical(other.cover, cover) || other.cover == cover)&&(identical(other.mainKValue, mainKValue) || other.mainKValue == mainKValue)&&(identical(other.mainSteelRatio, mainSteelRatio) || other.mainSteelRatio == mainSteelRatio)&&(identical(other.secKValue, secKValue) || other.secKValue == secKValue)&&(identical(other.secSteelRatio, secSteelRatio) || other.secSteelRatio == secSteelRatio)&&(identical(other.minS, minS) || other.minS == minS)&&(identical(other.maxS, maxS) || other.maxS == maxS)&&(identical(other.maxWidth, maxWidth) || other.maxWidth == maxWidth)&&(identical(other.maxLayers, maxLayers) || other.maxLayers == maxLayers)&&(identical(other.shortSpanIncreament, shortSpanIncreament) || other.shortSpanIncreament == shortSpanIncreament)&&(identical(other.longSpanIncreament, longSpanIncreament) || other.longSpanIncreament == longSpanIncreament)&&(identical(other.baysIncreament, baysIncreament) || other.baysIncreament == baysIncreament)&&(identical(other.iterationSteps, iterationSteps) || other.iterationSteps == iterationSteps)&&(identical(other.usage, usage) || other.usage == usage)&&(identical(other.slabThickness, slabThickness) || other.slabThickness == slabThickness)&&(identical(other.useSlabSelected, useSlabSelected) || other.useSlabSelected == useSlabSelected));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hashAll([runtimeType,id,shortSpan,longSpan,bays,mainStrZone,secStrZone,fcu,cover,mainKValue,mainSteelRatio,secKValue,secSteelRatio,minS,maxS,maxWidth,maxLayers,shortSpanIncreament,longSpanIncreament,baysIncreament,iterationSteps,usage,slabThickness,useSlabSelected]);

@override
String toString() {
  return 'BeamSchemeInput(id: $id, shortSpan: $shortSpan, longSpan: $longSpan, bays: $bays, mainStrZone: $mainStrZone, secStrZone: $secStrZone, fcu: $fcu, cover: $cover, mainKValue: $mainKValue, mainSteelRatio: $mainSteelRatio, secKValue: $secKValue, secSteelRatio: $secSteelRatio, minS: $minS, maxS: $maxS, maxWidth: $maxWidth, maxLayers: $maxLayers, shortSpanIncreament: $shortSpanIncreament, longSpanIncreament: $longSpanIncreament, baysIncreament: $baysIncreament, iterationSteps: $iterationSteps, usage: $usage, slabThickness: $slabThickness, useSlabSelected: $useSlabSelected)';
}


}

/// @nodoc
abstract mixin class $BeamSchemeInputCopyWith<$Res>  {
  factory $BeamSchemeInputCopyWith(BeamSchemeInput value, $Res Function(BeamSchemeInput) _then) = _$BeamSchemeInputCopyWithImpl;
@useResult
$Res call({
 String id, double shortSpan, double longSpan, int bays, double mainStrZone, double secStrZone, double fcu, double cover, double mainKValue, double mainSteelRatio, double secKValue, double secSteelRatio, int minS, int maxS, double maxWidth, int maxLayers, double shortSpanIncreament, double longSpanIncreament, int baysIncreament, int iterationSteps, String usage, double slabThickness, bool useSlabSelected
});




}
/// @nodoc
class _$BeamSchemeInputCopyWithImpl<$Res>
    implements $BeamSchemeInputCopyWith<$Res> {
  _$BeamSchemeInputCopyWithImpl(this._self, this._then);

  final BeamSchemeInput _self;
  final $Res Function(BeamSchemeInput) _then;

/// Create a copy of BeamSchemeInput
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? shortSpan = null,Object? longSpan = null,Object? bays = null,Object? mainStrZone = null,Object? secStrZone = null,Object? fcu = null,Object? cover = null,Object? mainKValue = null,Object? mainSteelRatio = null,Object? secKValue = null,Object? secSteelRatio = null,Object? minS = null,Object? maxS = null,Object? maxWidth = null,Object? maxLayers = null,Object? shortSpanIncreament = null,Object? longSpanIncreament = null,Object? baysIncreament = null,Object? iterationSteps = null,Object? usage = null,Object? slabThickness = null,Object? useSlabSelected = null,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,shortSpan: null == shortSpan ? _self.shortSpan : shortSpan // ignore: cast_nullable_to_non_nullable
as double,longSpan: null == longSpan ? _self.longSpan : longSpan // ignore: cast_nullable_to_non_nullable
as double,bays: null == bays ? _self.bays : bays // ignore: cast_nullable_to_non_nullable
as int,mainStrZone: null == mainStrZone ? _self.mainStrZone : mainStrZone // ignore: cast_nullable_to_non_nullable
as double,secStrZone: null == secStrZone ? _self.secStrZone : secStrZone // ignore: cast_nullable_to_non_nullable
as double,fcu: null == fcu ? _self.fcu : fcu // ignore: cast_nullable_to_non_nullable
as double,cover: null == cover ? _self.cover : cover // ignore: cast_nullable_to_non_nullable
as double,mainKValue: null == mainKValue ? _self.mainKValue : mainKValue // ignore: cast_nullable_to_non_nullable
as double,mainSteelRatio: null == mainSteelRatio ? _self.mainSteelRatio : mainSteelRatio // ignore: cast_nullable_to_non_nullable
as double,secKValue: null == secKValue ? _self.secKValue : secKValue // ignore: cast_nullable_to_non_nullable
as double,secSteelRatio: null == secSteelRatio ? _self.secSteelRatio : secSteelRatio // ignore: cast_nullable_to_non_nullable
as double,minS: null == minS ? _self.minS : minS // ignore: cast_nullable_to_non_nullable
as int,maxS: null == maxS ? _self.maxS : maxS // ignore: cast_nullable_to_non_nullable
as int,maxWidth: null == maxWidth ? _self.maxWidth : maxWidth // ignore: cast_nullable_to_non_nullable
as double,maxLayers: null == maxLayers ? _self.maxLayers : maxLayers // ignore: cast_nullable_to_non_nullable
as int,shortSpanIncreament: null == shortSpanIncreament ? _self.shortSpanIncreament : shortSpanIncreament // ignore: cast_nullable_to_non_nullable
as double,longSpanIncreament: null == longSpanIncreament ? _self.longSpanIncreament : longSpanIncreament // ignore: cast_nullable_to_non_nullable
as double,baysIncreament: null == baysIncreament ? _self.baysIncreament : baysIncreament // ignore: cast_nullable_to_non_nullable
as int,iterationSteps: null == iterationSteps ? _self.iterationSteps : iterationSteps // ignore: cast_nullable_to_non_nullable
as int,usage: null == usage ? _self.usage : usage // ignore: cast_nullable_to_non_nullable
as String,slabThickness: null == slabThickness ? _self.slabThickness : slabThickness // ignore: cast_nullable_to_non_nullable
as double,useSlabSelected: null == useSlabSelected ? _self.useSlabSelected : useSlabSelected // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}

}


/// Adds pattern-matching-related methods to [BeamSchemeInput].
extension BeamSchemeInputPatterns on BeamSchemeInput {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _BeamSchemeInput value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _BeamSchemeInput() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _BeamSchemeInput value)  $default,){
final _that = this;
switch (_that) {
case _BeamSchemeInput():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _BeamSchemeInput value)?  $default,){
final _that = this;
switch (_that) {
case _BeamSchemeInput() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  double shortSpan,  double longSpan,  int bays,  double mainStrZone,  double secStrZone,  double fcu,  double cover,  double mainKValue,  double mainSteelRatio,  double secKValue,  double secSteelRatio,  int minS,  int maxS,  double maxWidth,  int maxLayers,  double shortSpanIncreament,  double longSpanIncreament,  int baysIncreament,  int iterationSteps,  String usage,  double slabThickness,  bool useSlabSelected)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _BeamSchemeInput() when $default != null:
return $default(_that.id,_that.shortSpan,_that.longSpan,_that.bays,_that.mainStrZone,_that.secStrZone,_that.fcu,_that.cover,_that.mainKValue,_that.mainSteelRatio,_that.secKValue,_that.secSteelRatio,_that.minS,_that.maxS,_that.maxWidth,_that.maxLayers,_that.shortSpanIncreament,_that.longSpanIncreament,_that.baysIncreament,_that.iterationSteps,_that.usage,_that.slabThickness,_that.useSlabSelected);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  double shortSpan,  double longSpan,  int bays,  double mainStrZone,  double secStrZone,  double fcu,  double cover,  double mainKValue,  double mainSteelRatio,  double secKValue,  double secSteelRatio,  int minS,  int maxS,  double maxWidth,  int maxLayers,  double shortSpanIncreament,  double longSpanIncreament,  int baysIncreament,  int iterationSteps,  String usage,  double slabThickness,  bool useSlabSelected)  $default,) {final _that = this;
switch (_that) {
case _BeamSchemeInput():
return $default(_that.id,_that.shortSpan,_that.longSpan,_that.bays,_that.mainStrZone,_that.secStrZone,_that.fcu,_that.cover,_that.mainKValue,_that.mainSteelRatio,_that.secKValue,_that.secSteelRatio,_that.minS,_that.maxS,_that.maxWidth,_that.maxLayers,_that.shortSpanIncreament,_that.longSpanIncreament,_that.baysIncreament,_that.iterationSteps,_that.usage,_that.slabThickness,_that.useSlabSelected);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  double shortSpan,  double longSpan,  int bays,  double mainStrZone,  double secStrZone,  double fcu,  double cover,  double mainKValue,  double mainSteelRatio,  double secKValue,  double secSteelRatio,  int minS,  int maxS,  double maxWidth,  int maxLayers,  double shortSpanIncreament,  double longSpanIncreament,  int baysIncreament,  int iterationSteps,  String usage,  double slabThickness,  bool useSlabSelected)?  $default,) {final _that = this;
switch (_that) {
case _BeamSchemeInput() when $default != null:
return $default(_that.id,_that.shortSpan,_that.longSpan,_that.bays,_that.mainStrZone,_that.secStrZone,_that.fcu,_that.cover,_that.mainKValue,_that.mainSteelRatio,_that.secKValue,_that.secSteelRatio,_that.minS,_that.maxS,_that.maxWidth,_that.maxLayers,_that.shortSpanIncreament,_that.longSpanIncreament,_that.baysIncreament,_that.iterationSteps,_that.usage,_that.slabThickness,_that.useSlabSelected);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _BeamSchemeInput extends BeamSchemeInput {
   _BeamSchemeInput({this.id = '1', this.shortSpan = 5.0, this.longSpan = 12.0, this.bays = 2, this.mainStrZone = 500.0, this.secStrZone = 500.0, this.fcu = 45.0, this.cover = 40.0, this.mainKValue = 0.156, this.mainSteelRatio = 0.025, this.secKValue = 0.156, this.secSteelRatio = 0.040, this.minS = 100, this.maxS = 300, this.maxWidth = 1000.0, this.maxLayers = 2, this.shortSpanIncreament = 0.0, this.longSpanIncreament = 0.0, this.baysIncreament = 1, this.iterationSteps = 4, this.usage = '', this.slabThickness = 150.0, this.useSlabSelected = false}): super._();
  factory _BeamSchemeInput.fromJson(Map<String, dynamic> json) => _$BeamSchemeInputFromJson(json);

@override@JsonKey() final  String id;
@override@JsonKey() final  double shortSpan;
@override@JsonKey() final  double longSpan;
@override@JsonKey() final  int bays;
@override@JsonKey() final  double mainStrZone;
@override@JsonKey() final  double secStrZone;
@override@JsonKey() final  double fcu;
@override@JsonKey() final  double cover;
@override@JsonKey() final  double mainKValue;
@override@JsonKey() final  double mainSteelRatio;
@override@JsonKey() final  double secKValue;
@override@JsonKey() final  double secSteelRatio;
@override@JsonKey() final  int minS;
@override@JsonKey() final  int maxS;
@override@JsonKey() final  double maxWidth;
@override@JsonKey() final  int maxLayers;
@override@JsonKey() final  double shortSpanIncreament;
@override@JsonKey() final  double longSpanIncreament;
@override@JsonKey() final  int baysIncreament;
@override@JsonKey() final  int iterationSteps;
@override@JsonKey() final  String usage;
@override@JsonKey() final  double slabThickness;
@override@JsonKey() final  bool useSlabSelected;

/// Create a copy of BeamSchemeInput
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$BeamSchemeInputCopyWith<_BeamSchemeInput> get copyWith => __$BeamSchemeInputCopyWithImpl<_BeamSchemeInput>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$BeamSchemeInputToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _BeamSchemeInput&&(identical(other.id, id) || other.id == id)&&(identical(other.shortSpan, shortSpan) || other.shortSpan == shortSpan)&&(identical(other.longSpan, longSpan) || other.longSpan == longSpan)&&(identical(other.bays, bays) || other.bays == bays)&&(identical(other.mainStrZone, mainStrZone) || other.mainStrZone == mainStrZone)&&(identical(other.secStrZone, secStrZone) || other.secStrZone == secStrZone)&&(identical(other.fcu, fcu) || other.fcu == fcu)&&(identical(other.cover, cover) || other.cover == cover)&&(identical(other.mainKValue, mainKValue) || other.mainKValue == mainKValue)&&(identical(other.mainSteelRatio, mainSteelRatio) || other.mainSteelRatio == mainSteelRatio)&&(identical(other.secKValue, secKValue) || other.secKValue == secKValue)&&(identical(other.secSteelRatio, secSteelRatio) || other.secSteelRatio == secSteelRatio)&&(identical(other.minS, minS) || other.minS == minS)&&(identical(other.maxS, maxS) || other.maxS == maxS)&&(identical(other.maxWidth, maxWidth) || other.maxWidth == maxWidth)&&(identical(other.maxLayers, maxLayers) || other.maxLayers == maxLayers)&&(identical(other.shortSpanIncreament, shortSpanIncreament) || other.shortSpanIncreament == shortSpanIncreament)&&(identical(other.longSpanIncreament, longSpanIncreament) || other.longSpanIncreament == longSpanIncreament)&&(identical(other.baysIncreament, baysIncreament) || other.baysIncreament == baysIncreament)&&(identical(other.iterationSteps, iterationSteps) || other.iterationSteps == iterationSteps)&&(identical(other.usage, usage) || other.usage == usage)&&(identical(other.slabThickness, slabThickness) || other.slabThickness == slabThickness)&&(identical(other.useSlabSelected, useSlabSelected) || other.useSlabSelected == useSlabSelected));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hashAll([runtimeType,id,shortSpan,longSpan,bays,mainStrZone,secStrZone,fcu,cover,mainKValue,mainSteelRatio,secKValue,secSteelRatio,minS,maxS,maxWidth,maxLayers,shortSpanIncreament,longSpanIncreament,baysIncreament,iterationSteps,usage,slabThickness,useSlabSelected]);

@override
String toString() {
  return 'BeamSchemeInput(id: $id, shortSpan: $shortSpan, longSpan: $longSpan, bays: $bays, mainStrZone: $mainStrZone, secStrZone: $secStrZone, fcu: $fcu, cover: $cover, mainKValue: $mainKValue, mainSteelRatio: $mainSteelRatio, secKValue: $secKValue, secSteelRatio: $secSteelRatio, minS: $minS, maxS: $maxS, maxWidth: $maxWidth, maxLayers: $maxLayers, shortSpanIncreament: $shortSpanIncreament, longSpanIncreament: $longSpanIncreament, baysIncreament: $baysIncreament, iterationSteps: $iterationSteps, usage: $usage, slabThickness: $slabThickness, useSlabSelected: $useSlabSelected)';
}


}

/// @nodoc
abstract mixin class _$BeamSchemeInputCopyWith<$Res> implements $BeamSchemeInputCopyWith<$Res> {
  factory _$BeamSchemeInputCopyWith(_BeamSchemeInput value, $Res Function(_BeamSchemeInput) _then) = __$BeamSchemeInputCopyWithImpl;
@override @useResult
$Res call({
 String id, double shortSpan, double longSpan, int bays, double mainStrZone, double secStrZone, double fcu, double cover, double mainKValue, double mainSteelRatio, double secKValue, double secSteelRatio, int minS, int maxS, double maxWidth, int maxLayers, double shortSpanIncreament, double longSpanIncreament, int baysIncreament, int iterationSteps, String usage, double slabThickness, bool useSlabSelected
});




}
/// @nodoc
class __$BeamSchemeInputCopyWithImpl<$Res>
    implements _$BeamSchemeInputCopyWith<$Res> {
  __$BeamSchemeInputCopyWithImpl(this._self, this._then);

  final _BeamSchemeInput _self;
  final $Res Function(_BeamSchemeInput) _then;

/// Create a copy of BeamSchemeInput
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? shortSpan = null,Object? longSpan = null,Object? bays = null,Object? mainStrZone = null,Object? secStrZone = null,Object? fcu = null,Object? cover = null,Object? mainKValue = null,Object? mainSteelRatio = null,Object? secKValue = null,Object? secSteelRatio = null,Object? minS = null,Object? maxS = null,Object? maxWidth = null,Object? maxLayers = null,Object? shortSpanIncreament = null,Object? longSpanIncreament = null,Object? baysIncreament = null,Object? iterationSteps = null,Object? usage = null,Object? slabThickness = null,Object? useSlabSelected = null,}) {
  return _then(_BeamSchemeInput(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,shortSpan: null == shortSpan ? _self.shortSpan : shortSpan // ignore: cast_nullable_to_non_nullable
as double,longSpan: null == longSpan ? _self.longSpan : longSpan // ignore: cast_nullable_to_non_nullable
as double,bays: null == bays ? _self.bays : bays // ignore: cast_nullable_to_non_nullable
as int,mainStrZone: null == mainStrZone ? _self.mainStrZone : mainStrZone // ignore: cast_nullable_to_non_nullable
as double,secStrZone: null == secStrZone ? _self.secStrZone : secStrZone // ignore: cast_nullable_to_non_nullable
as double,fcu: null == fcu ? _self.fcu : fcu // ignore: cast_nullable_to_non_nullable
as double,cover: null == cover ? _self.cover : cover // ignore: cast_nullable_to_non_nullable
as double,mainKValue: null == mainKValue ? _self.mainKValue : mainKValue // ignore: cast_nullable_to_non_nullable
as double,mainSteelRatio: null == mainSteelRatio ? _self.mainSteelRatio : mainSteelRatio // ignore: cast_nullable_to_non_nullable
as double,secKValue: null == secKValue ? _self.secKValue : secKValue // ignore: cast_nullable_to_non_nullable
as double,secSteelRatio: null == secSteelRatio ? _self.secSteelRatio : secSteelRatio // ignore: cast_nullable_to_non_nullable
as double,minS: null == minS ? _self.minS : minS // ignore: cast_nullable_to_non_nullable
as int,maxS: null == maxS ? _self.maxS : maxS // ignore: cast_nullable_to_non_nullable
as int,maxWidth: null == maxWidth ? _self.maxWidth : maxWidth // ignore: cast_nullable_to_non_nullable
as double,maxLayers: null == maxLayers ? _self.maxLayers : maxLayers // ignore: cast_nullable_to_non_nullable
as int,shortSpanIncreament: null == shortSpanIncreament ? _self.shortSpanIncreament : shortSpanIncreament // ignore: cast_nullable_to_non_nullable
as double,longSpanIncreament: null == longSpanIncreament ? _self.longSpanIncreament : longSpanIncreament // ignore: cast_nullable_to_non_nullable
as double,baysIncreament: null == baysIncreament ? _self.baysIncreament : baysIncreament // ignore: cast_nullable_to_non_nullable
as int,iterationSteps: null == iterationSteps ? _self.iterationSteps : iterationSteps // ignore: cast_nullable_to_non_nullable
as int,usage: null == usage ? _self.usage : usage // ignore: cast_nullable_to_non_nullable
as String,slabThickness: null == slabThickness ? _self.slabThickness : slabThickness // ignore: cast_nullable_to_non_nullable
as double,useSlabSelected: null == useSlabSelected ? _self.useSlabSelected : useSlabSelected // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}


}

// dart format on
