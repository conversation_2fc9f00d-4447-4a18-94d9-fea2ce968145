// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'steel_cantilever_truss_scheme_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_SteelCantileverTrussSchemeData _$SteelCantileverTrussSchemeDataFromJson(
  Map<String, dynamic> json,
) => _SteelCantileverTrussSchemeData(
  usage: json['usage'] as String? ?? '',
  slabThickness: (json['slabThickness'] as num?)?.toDouble() ?? 150.0,
  span: (json['span'] as num?)?.toDouble() ?? 1.0,
  loadWidth: (json['loadWidth'] as num?)?.toDouble() ?? 1.0,
  strZone: (json['strZone'] as num?)?.toDouble() ?? 500.0,
  fsy: (json['fsy'] as num?)?.toDouble() ?? 355.0,
  unbracedLength: (json['unbracedLength'] as num?)?.toDouble() ?? 5.0,
  steelSection: json['steelSection'] as String? ?? '',
  chordAxialCapacity: (json['chordAxialCapacity'] as num?)?.toDouble() ?? 0.0,
  leverArm: (json['leverArm'] as num?)?.toDouble() ?? 0.0,
  momentCapacity: (json['momentCapacity'] as num?)?.toDouble() ?? 0.0,
  liveLoadDeflection: json['liveLoadDeflection'] as String? ?? '',
  id: json['id'] as String? ?? '1',
  calsLog: json['calsLog'] as String? ?? '',
  beamForce: json['beamForce'] as String? ?? '',
);

Map<String, dynamic> _$SteelCantileverTrussSchemeDataToJson(
  _SteelCantileverTrussSchemeData instance,
) => <String, dynamic>{
  'usage': instance.usage,
  'slabThickness': instance.slabThickness,
  'span': instance.span,
  'loadWidth': instance.loadWidth,
  'strZone': instance.strZone,
  'fsy': instance.fsy,
  'unbracedLength': instance.unbracedLength,
  'steelSection': instance.steelSection,
  'chordAxialCapacity': instance.chordAxialCapacity,
  'leverArm': instance.leverArm,
  'momentCapacity': instance.momentCapacity,
  'liveLoadDeflection': instance.liveLoadDeflection,
  'id': instance.id,
  'calsLog': instance.calsLog,
  'beamForce': instance.beamForce,
};
