// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'steel_cantilever_truss_scheme_input_global.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$SteelCantileverTrussSchemeInputGlobal {

 String get id; double get span; double get loadWidth; double get strZone; double get fsy; double get unbracedLength; String get usage; double get slabThickness;
/// Create a copy of SteelCantileverTrussSchemeInputGlobal
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$SteelCantileverTrussSchemeInputGlobalCopyWith<SteelCantileverTrussSchemeInputGlobal> get copyWith => _$SteelCantileverTrussSchemeInputGlobalCopyWithImpl<SteelCantileverTrussSchemeInputGlobal>(this as SteelCantileverTrussSchemeInputGlobal, _$identity);

  /// Serializes this SteelCantileverTrussSchemeInputGlobal to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is SteelCantileverTrussSchemeInputGlobal&&(identical(other.id, id) || other.id == id)&&(identical(other.span, span) || other.span == span)&&(identical(other.loadWidth, loadWidth) || other.loadWidth == loadWidth)&&(identical(other.strZone, strZone) || other.strZone == strZone)&&(identical(other.fsy, fsy) || other.fsy == fsy)&&(identical(other.unbracedLength, unbracedLength) || other.unbracedLength == unbracedLength)&&(identical(other.usage, usage) || other.usage == usage)&&(identical(other.slabThickness, slabThickness) || other.slabThickness == slabThickness));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,span,loadWidth,strZone,fsy,unbracedLength,usage,slabThickness);

@override
String toString() {
  return 'SteelCantileverTrussSchemeInputGlobal(id: $id, span: $span, loadWidth: $loadWidth, strZone: $strZone, fsy: $fsy, unbracedLength: $unbracedLength, usage: $usage, slabThickness: $slabThickness)';
}


}

/// @nodoc
abstract mixin class $SteelCantileverTrussSchemeInputGlobalCopyWith<$Res>  {
  factory $SteelCantileverTrussSchemeInputGlobalCopyWith(SteelCantileverTrussSchemeInputGlobal value, $Res Function(SteelCantileverTrussSchemeInputGlobal) _then) = _$SteelCantileverTrussSchemeInputGlobalCopyWithImpl;
@useResult
$Res call({
 String id, double span, double loadWidth, double strZone, double fsy, double unbracedLength, String usage, double slabThickness
});




}
/// @nodoc
class _$SteelCantileverTrussSchemeInputGlobalCopyWithImpl<$Res>
    implements $SteelCantileverTrussSchemeInputGlobalCopyWith<$Res> {
  _$SteelCantileverTrussSchemeInputGlobalCopyWithImpl(this._self, this._then);

  final SteelCantileverTrussSchemeInputGlobal _self;
  final $Res Function(SteelCantileverTrussSchemeInputGlobal) _then;

/// Create a copy of SteelCantileverTrussSchemeInputGlobal
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? span = null,Object? loadWidth = null,Object? strZone = null,Object? fsy = null,Object? unbracedLength = null,Object? usage = null,Object? slabThickness = null,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,span: null == span ? _self.span : span // ignore: cast_nullable_to_non_nullable
as double,loadWidth: null == loadWidth ? _self.loadWidth : loadWidth // ignore: cast_nullable_to_non_nullable
as double,strZone: null == strZone ? _self.strZone : strZone // ignore: cast_nullable_to_non_nullable
as double,fsy: null == fsy ? _self.fsy : fsy // ignore: cast_nullable_to_non_nullable
as double,unbracedLength: null == unbracedLength ? _self.unbracedLength : unbracedLength // ignore: cast_nullable_to_non_nullable
as double,usage: null == usage ? _self.usage : usage // ignore: cast_nullable_to_non_nullable
as String,slabThickness: null == slabThickness ? _self.slabThickness : slabThickness // ignore: cast_nullable_to_non_nullable
as double,
  ));
}

}


/// Adds pattern-matching-related methods to [SteelCantileverTrussSchemeInputGlobal].
extension SteelCantileverTrussSchemeInputGlobalPatterns on SteelCantileverTrussSchemeInputGlobal {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _SteelCantileverTrussSchemeInputGlobal value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _SteelCantileverTrussSchemeInputGlobal() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _SteelCantileverTrussSchemeInputGlobal value)  $default,){
final _that = this;
switch (_that) {
case _SteelCantileverTrussSchemeInputGlobal():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _SteelCantileverTrussSchemeInputGlobal value)?  $default,){
final _that = this;
switch (_that) {
case _SteelCantileverTrussSchemeInputGlobal() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  double span,  double loadWidth,  double strZone,  double fsy,  double unbracedLength,  String usage,  double slabThickness)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _SteelCantileverTrussSchemeInputGlobal() when $default != null:
return $default(_that.id,_that.span,_that.loadWidth,_that.strZone,_that.fsy,_that.unbracedLength,_that.usage,_that.slabThickness);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  double span,  double loadWidth,  double strZone,  double fsy,  double unbracedLength,  String usage,  double slabThickness)  $default,) {final _that = this;
switch (_that) {
case _SteelCantileverTrussSchemeInputGlobal():
return $default(_that.id,_that.span,_that.loadWidth,_that.strZone,_that.fsy,_that.unbracedLength,_that.usage,_that.slabThickness);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  double span,  double loadWidth,  double strZone,  double fsy,  double unbracedLength,  String usage,  double slabThickness)?  $default,) {final _that = this;
switch (_that) {
case _SteelCantileverTrussSchemeInputGlobal() when $default != null:
return $default(_that.id,_that.span,_that.loadWidth,_that.strZone,_that.fsy,_that.unbracedLength,_that.usage,_that.slabThickness);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _SteelCantileverTrussSchemeInputGlobal extends SteelCantileverTrussSchemeInputGlobal {
   _SteelCantileverTrussSchemeInputGlobal({this.id = '1', this.span = 20.0, this.loadWidth = 10.0, this.strZone = 1200.0, this.fsy = 355.0, this.unbracedLength = 5.0, this.usage = '', this.slabThickness = 130.0}): super._();
  factory _SteelCantileverTrussSchemeInputGlobal.fromJson(Map<String, dynamic> json) => _$SteelCantileverTrussSchemeInputGlobalFromJson(json);

@override@JsonKey() final  String id;
@override@JsonKey() final  double span;
@override@JsonKey() final  double loadWidth;
@override@JsonKey() final  double strZone;
@override@JsonKey() final  double fsy;
@override@JsonKey() final  double unbracedLength;
@override@JsonKey() final  String usage;
@override@JsonKey() final  double slabThickness;

/// Create a copy of SteelCantileverTrussSchemeInputGlobal
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$SteelCantileverTrussSchemeInputGlobalCopyWith<_SteelCantileverTrussSchemeInputGlobal> get copyWith => __$SteelCantileverTrussSchemeInputGlobalCopyWithImpl<_SteelCantileverTrussSchemeInputGlobal>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$SteelCantileverTrussSchemeInputGlobalToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _SteelCantileverTrussSchemeInputGlobal&&(identical(other.id, id) || other.id == id)&&(identical(other.span, span) || other.span == span)&&(identical(other.loadWidth, loadWidth) || other.loadWidth == loadWidth)&&(identical(other.strZone, strZone) || other.strZone == strZone)&&(identical(other.fsy, fsy) || other.fsy == fsy)&&(identical(other.unbracedLength, unbracedLength) || other.unbracedLength == unbracedLength)&&(identical(other.usage, usage) || other.usage == usage)&&(identical(other.slabThickness, slabThickness) || other.slabThickness == slabThickness));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,span,loadWidth,strZone,fsy,unbracedLength,usage,slabThickness);

@override
String toString() {
  return 'SteelCantileverTrussSchemeInputGlobal(id: $id, span: $span, loadWidth: $loadWidth, strZone: $strZone, fsy: $fsy, unbracedLength: $unbracedLength, usage: $usage, slabThickness: $slabThickness)';
}


}

/// @nodoc
abstract mixin class _$SteelCantileverTrussSchemeInputGlobalCopyWith<$Res> implements $SteelCantileverTrussSchemeInputGlobalCopyWith<$Res> {
  factory _$SteelCantileverTrussSchemeInputGlobalCopyWith(_SteelCantileverTrussSchemeInputGlobal value, $Res Function(_SteelCantileverTrussSchemeInputGlobal) _then) = __$SteelCantileverTrussSchemeInputGlobalCopyWithImpl;
@override @useResult
$Res call({
 String id, double span, double loadWidth, double strZone, double fsy, double unbracedLength, String usage, double slabThickness
});




}
/// @nodoc
class __$SteelCantileverTrussSchemeInputGlobalCopyWithImpl<$Res>
    implements _$SteelCantileverTrussSchemeInputGlobalCopyWith<$Res> {
  __$SteelCantileverTrussSchemeInputGlobalCopyWithImpl(this._self, this._then);

  final _SteelCantileverTrussSchemeInputGlobal _self;
  final $Res Function(_SteelCantileverTrussSchemeInputGlobal) _then;

/// Create a copy of SteelCantileverTrussSchemeInputGlobal
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? span = null,Object? loadWidth = null,Object? strZone = null,Object? fsy = null,Object? unbracedLength = null,Object? usage = null,Object? slabThickness = null,}) {
  return _then(_SteelCantileverTrussSchemeInputGlobal(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,span: null == span ? _self.span : span // ignore: cast_nullable_to_non_nullable
as double,loadWidth: null == loadWidth ? _self.loadWidth : loadWidth // ignore: cast_nullable_to_non_nullable
as double,strZone: null == strZone ? _self.strZone : strZone // ignore: cast_nullable_to_non_nullable
as double,fsy: null == fsy ? _self.fsy : fsy // ignore: cast_nullable_to_non_nullable
as double,unbracedLength: null == unbracedLength ? _self.unbracedLength : unbracedLength // ignore: cast_nullable_to_non_nullable
as double,usage: null == usage ? _self.usage : usage // ignore: cast_nullable_to_non_nullable
as String,slabThickness: null == slabThickness ? _self.slabThickness : slabThickness // ignore: cast_nullable_to_non_nullable
as double,
  ));
}


}

// dart format on
