import 'dart:math' as math;
import 'dart:ui';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:structify/domain_layer/beam_scheme_data.dart';
import 'package:structify/presentation_layer/screen/homescreen.dart';
import 'package:vector_math/vector_math.dart' as vec;

import '../../../domain_layer/global_data.dart';
import '../../../domain_layer/preferences.dart';

class DrawBeamSchemeInfo extends ConsumerWidget {
  DrawBeamSchemeInfo({
    required this.sketchWidth,
    required this.sketchHeight,
    this.fontSize,
    super.key,
  });

  final double sketchWidth;
  final double sketchHeight;
  double? fontSize;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final data1 = ref.watch(globalDataControllerProvider);
    final ColorScheme colorScheme = Theme.of(context).colorScheme;
    final TextTheme textTheme = Theme.of(context).textTheme;

    fontSize = fontSize ?? math.min(sketchWidth, sketchHeight) / 40;
    return data1.when(
      data: (globalData) {
        final constraints = BoxConstraints(
          maxWidth: sketchWidth,
          maxHeight: sketchHeight,
          minHeight: 100,
          minWidth: 100,
        );
        return Center(
          child: Container(
            decoration: BoxDecoration(
              color: colorScheme.surfaceContainer,
              borderRadius: BorderRadius.circular(10.0),
              border: Border.all(color: Colors.black.withAlpha(100)),
            ),
            width:
                constraints.maxWidth == double.infinity
                    ? 100
                    : constraints.maxWidth,
            height:
                constraints.maxHeight == double.infinity
                    ? 100
                    : constraints.maxHeight,
            child: CustomPaint(
              painter: DrawBeamSchemeSketchPainter(
                globalData: globalData,
                boxConstraints: constraints,
                fontSize: fontSize,
                context: context,
              ),
            ),
          ),
        );
      },

      error: (error, stackTrace) {
        return Text(error.toString());
      },
      loading: () {
        return const CircularProgressIndicator();
      },
    );
  }
}

class DrawBeamSchemeSketchPainter extends CustomPainter {
  final GlobalData globalData;
  final BoxConstraints boxConstraints;
  double? fontSize;
  final BuildContext context;

  DrawBeamSchemeSketchPainter({
    required this.globalData,
    required this.boxConstraints,
    this.fontSize,
    required this.context,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final ColorScheme colorScheme = Theme.of(context).colorScheme;
    final TextTheme textTheme = Theme.of(context).textTheme;
    late final double secBeamSpacing;

    fontSize = fontSize ?? math.min(size.width, size.height) / 40;
    Offset startP1 = Offset(size.width / 2, size.height / 2);
    Paint beamPaint =
        Paint()
          ..color = colorScheme.onSurface
          ..strokeCap = StrokeCap.round
          ..style = PaintingStyle.stroke
          ..strokeWidth = 1.0;
    Paint ColPaint =
        Paint()
          ..color = colorScheme.onSurface
          ..strokeCap = StrokeCap.round
          ..style = PaintingStyle.stroke
          ..strokeWidth = 2.0;

    // final startPoint = Offset(0, 0);
    // final endPoint = Offset(size.width, 0);
    // canvas.drawLine(startPoint, endPoint, paint);
    final beamSchemeData = BeamSchemeData(
      shortSpan: 8.0,
      longSpan: 12.0,
      bays: 3,
    );
    final double maxSpan = math.max(
      beamSchemeData.shortSpan,
      beamSchemeData.longSpan,
    );
    final double minDim = math.min(
      boxConstraints.maxWidth,
      boxConstraints.maxHeight,
    );
    final double ratio = minDim / maxSpan * 4 / 5;
    final double endOffset = size.width / 20;

    Path path = Path();

    // ******************
    // draw columns
    // ******************
    final Offset startOffset = Offset(
      0.5 * (boxConstraints.maxWidth - beamSchemeData.shortSpan * ratio),
      0.5 * (boxConstraints.maxHeight - beamSchemeData.longSpan * ratio),
    );

    // upper left
    final Offset upLeftCol = Offset(startOffset.dx, startOffset.dy);
    canvas.drawCircle(upLeftCol, size.width / 50, ColPaint);

    // upper right
    final Offset upRightCol = Offset(
      startOffset.dx + beamSchemeData.shortSpan * ratio,
      startOffset.dy,
    );
    canvas.drawCircle(upRightCol, size.width / 50, ColPaint);

    // lower left
    final Offset lowLeftCol = Offset(
      startOffset.dx,
      beamSchemeData.longSpan * ratio + startOffset.dy,
    );
    canvas.drawCircle(lowLeftCol, size.width / 50, ColPaint);

    // upper right
    final Offset lowRightCol = Offset(
      startOffset.dx + beamSchemeData.shortSpan * ratio,
      beamSchemeData.longSpan * ratio + startOffset.dy,
    );
    canvas.drawCircle(lowRightCol, size.width / 50, ColPaint);

    // ******************
    // draw main beam
    // ******************
    // upper
    path.moveTo(upLeftCol.dx + endOffset, upLeftCol.dy);
    path.lineTo(upRightCol.dx - endOffset, upRightCol.dy);

    // lower
    path.moveTo(lowLeftCol.dx + endOffset, lowLeftCol.dy);
    path.lineTo(lowRightCol.dx - endOffset, lowRightCol.dy);

    // ******************
    // draw 2nd beam
    // ******************
    // left to Right
    secBeamSpacing = (upRightCol.dx - upLeftCol.dx) / beamSchemeData.bays;
    for (int i = 0; i <= beamSchemeData.bays; i++) {
      path.moveTo(upLeftCol.dx + secBeamSpacing * i, upLeftCol.dy + endOffset);
      path.lineTo(
        lowLeftCol.dx + secBeamSpacing * i,
        lowLeftCol.dy - endOffset,
      );
      canvas.drawPath(path, beamPaint);
    }

    // // left
    // path.moveTo(upLeftCol.dx, upLeftCol.dy + endOffset);
    // path.lineTo(lowLeftCol.dx, lowLeftCol.dy - endOffset);
    // canvas.drawPath(path, beamPaint);

    // //right
    // path.moveTo(upRightCol.dx, upRightCol.dy + endOffset);
    // path.lineTo(lowRightCol.dx, lowRightCol.dy - endOffset);
    // canvas.drawPath(path, beamPaint);
    // ******************
    // draw Slab Span
    // ******************
    final dx = size.width / 60, dy = size.height / 60;
    final points = Float32List.fromList(
      _getPointsFromOffset(startP1 + Offset(-dx, -dy), [
        Offset(-dx, dy),
        Offset(dx * 6, 0),
        Offset(-dx, dy),
      ]),
    );
    canvas.drawRawPoints(PointMode.polygon, points, beamPaint);
    // ******************
    // draw dimension lines
    // ******************
    final offsetDistance = math.min(size.width / 25, size.height / 25);
    final dimExtension = math.min(size.width / 50, size.height / 50);

    //* mark short span
    _drawDimLine(
      canvas,
      fontSize!,
      upLeftCol,
      upRightCol,
      offsetDistance,
      dimExtension,
      beamSchemeData.shortSpan,
      'Short Span\n(parallel to slab span)',
      globalData,
      context,
    );
    //*mark long span
    _drawDimLine(
      canvas,
      fontSize!,
      lowLeftCol,
      upLeftCol,
      -offsetDistance,
      dimExtension,
      beamSchemeData.longSpan,
      'Long Span',
      globalData,
      context,
    );

    //* Bays
    _drawDimLine(
      canvas,
      fontSize!,
      upLeftCol +
          Offset(0, -(upLeftCol.dy - lowLeftCol.dy) / 2) +
          Offset(0, -size.height / 20),
      upRightCol +
          Offset(0, -(upRightCol.dy - lowRightCol.dy) / 2) +
          Offset(0, -size.height / 20),
      offsetDistance,
      dimExtension,
      beamSchemeData.shortSpan,
      'Bays\n(Nos along short span)',
      globalData,
      context,
    );
  }

  void _drawDimLine(
    Canvas canvas,
    double fontSize,
    Offset start,
    Offset end,
    double offsetDistance,
    double dimExtension,
    double spanLength,
    String textToShow,
    GlobalData globalData,
    BuildContext context,
  ) {
    double tempOffset = 10; // forthe dim line end
    late Offset tempEnd;
    late Offset tempStart;
    late Offset textCenter;

    late double angle;

    late List<String> unit;

    Color fontColor = Colors.red.withAlpha(200);

    late final vec.Vector2 u;
    late final vec.Vector2 uUnit;
    late final vec.Vector2 vUnit;
    late final vec.Vector2 vUnit45;
    late final vec.Matrix2 m;

    final ColorScheme colorScheme = Theme.of(context).colorScheme;
    final TextTheme textTheme = Theme.of(context).textTheme;

    // get the unit
    switch (globalData.unit) {
      case 'metrics':
        unit = PreferredUnit.metrics;
        break;
      case 'imperial':
        unit = PreferredUnit.imperial;
        break;
      default:
        unit = PreferredUnit.metrics;
    }

    // double dimExtension = offsetDistance.abs() / 2;

    final Paint paint =
        Paint()
          ..color = fontColor
          ..strokeWidth = 1.0
          ..style = PaintingStyle.stroke
          ..strokeJoin = StrokeJoin.round;

    // line vector
    u = vec.Vector2(end.dx - start.dx, end.dy - start.dy);
    uUnit = u / u.length;

    // orthogonal vector
    vUnit = vec.Vector2(uUnit.y, uUnit.x); //rotate aniti-clockwise

    // Vector of parallel line with Offset
    Offset newStart = Offset(
      start.dx - vUnit.x * offsetDistance - uUnit.x * dimExtension,
      start.dy - vUnit.y * offsetDistance - uUnit.y * dimExtension,
    );
    Offset newEnd = Offset(
      end.dx - vUnit.x * offsetDistance + uUnit.x * dimExtension,
      end.dy - vUnit.y * offsetDistance + uUnit.y * dimExtension,
    );
    canvas.drawLine(newStart, newEnd, paint);

    // draw start crossing
    tempStart = Offset(
      (start.dx - vUnit.x * offsetDistance) - vUnit.x * dimExtension,
      (start.dy - vUnit.y * offsetDistance) - vUnit.y * dimExtension,
    );
    tempEnd = Offset(
      (start.dx - vUnit.x * offsetDistance) + vUnit.x * dimExtension,
      (start.dy - vUnit.y * offsetDistance) + vUnit.y * dimExtension,
    );
    canvas.drawLine(tempStart, tempEnd, paint);

    m = vec.Matrix2.rotation(-45 * math.pi / 180);
    vUnit45 = m * vUnit;
    tempStart = Offset(
      (start.dx - vUnit.x * offsetDistance) - vUnit45.x * dimExtension,
      (start.dy - vUnit.y * offsetDistance) - vUnit45.y * dimExtension,
    );
    tempEnd = Offset(
      (start.dx - vUnit.x * offsetDistance) + vUnit45.x * dimExtension,
      (start.dy - vUnit.y * offsetDistance) + vUnit45.y * dimExtension,
    );
    canvas.drawLine(tempStart, tempEnd, paint);

    // draw end crossing
    tempStart = Offset(
      (end.dx - vUnit.x * offsetDistance) - vUnit.x * dimExtension,
      (end.dy - vUnit.y * offsetDistance) - vUnit.y * dimExtension,
    );
    tempEnd = Offset(
      (end.dx - vUnit.x * offsetDistance) + vUnit.x * dimExtension,
      (end.dy - vUnit.y * offsetDistance) + vUnit.y * dimExtension,
    );
    canvas.drawLine(tempStart, tempEnd, paint);

    tempStart = Offset(
      (end.dx - vUnit.x * offsetDistance) - vUnit45.x * dimExtension,
      (end.dy - vUnit.y * offsetDistance) - vUnit45.y * dimExtension,
    );
    tempEnd = Offset(
      (end.dx - vUnit.x * offsetDistance) + vUnit45.x * dimExtension,
      (end.dy - vUnit.y * offsetDistance) + vUnit45.y * dimExtension,
    );
    canvas.drawLine(tempStart, tempEnd, paint);

    // indicate span length and beam size

    TextSpan textSpan = TextSpan(
      text: '${textToShow}',
      style: TextStyle(
        fontSize: fontSize,
        color: fontColor,
        fontWeight: FontWeight.bold,
      ),
    );

    TextPainter textPainter = TextPainter(
      text: textSpan,
      textAlign: TextAlign.center,
      textDirection: TextDirection.ltr,
    )..layout();

    textCenter = Offset(
      (start.dx + end.dx) / 2 -
          vUnit.x * offsetDistance * 2 -
          uUnit.x * textPainter.width / 2,
      (start.dy + end.dy) / 2 -
          vUnit.y * offsetDistance * 2 -
          uUnit.y * textPainter.width / 2,
    );

    angle = math.atan2(end.dy - start.dy, end.dx - start.dx) * 180 / math.pi;

    canvas.save();
    canvas.translate(textCenter.dx, textCenter.dy);
    canvas.rotate(angle * math.pi / 180);
    textPainter.paint(canvas, Offset.zero);
    canvas.restore();
  }

  Float32List _getPointsFromOffset(Offset start, List<Offset> offsets) {
    List<Offset> points = [start];
    for (Offset offset in offsets) {
      start = start + offset;
      points.add(start);
    }
    return Float32List.fromList(points.expand((i) => [i.dx, i.dy]).toList());
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    // TODO: implement shouldRepaint
    return false;
  }
}
