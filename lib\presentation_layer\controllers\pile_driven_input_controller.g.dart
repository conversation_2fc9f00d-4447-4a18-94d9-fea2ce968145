// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'pile_driven_input_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$pileDrivenInputControllerHash() =>
    r'4c8814beb60846d8de2d5cab9d06b8b1c5362626';

/// See also [PileDrivenInputController].
@ProviderFor(PileDrivenInputController)
final pileDrivenInputControllerProvider = AutoDisposeAsyncNotifierProvider<
  PileDrivenInputController,
  PileDrivenInput
>.internal(
  PileDrivenInputController.new,
  name: r'pileDrivenInputControllerProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$pileDrivenInputControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$PileDrivenInputController = AutoDisposeAsyncNotifier<PileDrivenInput>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
