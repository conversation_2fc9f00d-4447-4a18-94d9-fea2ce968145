// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'steel_cantilever_truss_scheme_input_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$steelCantileverTrussSchemeInputControllerHash() =>
    r'ae11803af750d083ad671cb44e31232b790af628';

/// See also [SteelCantileverTrussSchemeInputController].
@ProviderFor(SteelCantileverTrussSchemeInputController)
final steelCantileverTrussSchemeInputControllerProvider =
    AutoDisposeAsyncNotifierProvider<
      SteelCantileverTrussSchemeInputController,
      List<SteelCantileverTrussSchemeInput>
    >.internal(
      SteelCantileverTrussSchemeInputController.new,
      name: r'steelCantileverTrussSchemeInputControllerProvider',
      debugGetCreateSourceHash:
          const bool.fromEnvironment('dart.vm.product')
              ? null
              : _$steelCantileverTrussSchemeInputControllerHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$SteelCantileverTrussSchemeInputController =
    AutoDisposeAsyncNotifier<List<SteelCantileverTrussSchemeInput>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
